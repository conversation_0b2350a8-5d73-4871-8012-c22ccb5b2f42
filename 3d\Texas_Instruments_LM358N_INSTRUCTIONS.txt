ULTRALIBRARIAN MANUAL DOWNLOAD INSTRUCTIONS
==================================================

TARGET PART:
Manufacturer: Texas Instruments
Part Number: LM358N
Date: 2025-08-26 19:10:16

SEARCH STRATEGIES:
==================
1. Open UltraLibrarian app: https://app.ultralibrarian.com
2. Use the search box in the app to search for:
   - 'Texas Instruments LM358N'
   - 'LM358N'
   - 'LM358N Texas Instruments'
   - 'Texas Instruments LM358N'

DOWNLOAD STEPS:
===============
1. <PERSON><PERSON><PERSON> opened to UltraLibrarian app: https://app.ultralibrarian.com
2. Wait for the page to fully load (it's a JavaScript application)
3. Use the search box at the top to search for your part number
4. Try these search terms in order:
   1. 'Texas Instruments LM358N'
   2. 'LM358N'
   3. 'LM358N Texas Instruments'
   4. 'Texas Instruments LM358N'
5. Click on the matching part from the search results
6. On the part details page, look for download options:
   - 'Download Now' button
   - '3D CAD Model' section
   - 'STEP' file format option
7. You may need to:
   - Create a free UltraLibrarian account
   - Login with your credentials
   - Select STEP file format
   - Choose the correct package type if multiple options
8. Download the ZIP file containing the STEP model
9. Extract the STEP file from the ZIP
10. Save it to this folder: 3D/
11. Rename it to: Texas_Instruments_LM358N_UL.step

ALTERNATIVE SEARCHES:
====================
If no results found, try:
- Different manufacturer names (TI vs Texas Instruments)
- Part number without suffixes (-N, -SA, etc.)
- Generic part number (LM358 instead of LM358N)
- Package type + part number (DIP LM358)

TROUBLESHOOTING:
================
- No results? Try the alternative search URLs above
- Need account? Registration is usually free
- No STEP file? Look for other 3D formats (.stp, .3d)
- Multiple packages? Choose the one you need (DIP-8, SOIC-8)

WHEN COMPLETE:
==============
[✓] File downloaded and renamed
[✓] Saved in 3D/ folder
[✓] Ready for CAD import

OTHER 3D MODEL SOURCES:
=======================
If UltraLibrarian doesn't have it, try:
- SnapEDA: https://www.snapeda.com/
- SamacSys: https://www.samacsys.com/
- Component Search Plus: https://componentsearchengine.com/
- Manufacturer website directly
