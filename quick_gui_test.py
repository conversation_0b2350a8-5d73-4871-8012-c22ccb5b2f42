#!/usr/bin/env python3
"""
Quick test of the Component Finder GUI initialization
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_component_finder_gui():
    print("🧪 Testing Component Finder GUI initialization...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        print("📦 Importing ComponentFinderGUI...")
        from component_finder_gui import ComponentFinderGUI
        print("✅ Import successful")
        
        print("🖥️ Creating root window...")
        root = tk.Tk()
        root.title("Component Finder Test")
        root.geometry("800x700")
        root.configure(bg='white')
        
        # Make sure window is visible
        root.lift()
        root.attributes('-topmost', True)
        root.after(100, lambda: root.attributes('-topmost', False))
        
        print("✅ Root window created")
        
        print("🔧 Initializing ComponentFinderGUI...")
        print("   This might take a few seconds...")
        
        # Try to create the GUI
        app = ComponentFinderGUI(root)
        print("✅ ComponentFinderGUI initialized successfully!")
        
        print("🎯 Starting GUI...")
        print("📋 Look for: 'Component Finder - Interactive Learning System'")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Error during GUI test: {e}")
        import traceback
        traceback.print_exc()
        
        # Show a simple error window
        try:
            error_root = tk.Tk()
            error_root.title("GUI Error")
            error_root.geometry("500x300")
            
            error_label = tk.Label(error_root, 
                                 text=f"Error initializing GUI:\n\n{str(e)}\n\nCheck console for details",
                                 wraplength=450,
                                 justify=tk.LEFT)
            error_label.pack(padx=20, pady=20)
            
            close_button = tk.Button(error_root, text="Close", command=error_root.destroy)
            close_button.pack(pady=10)
            
            error_root.mainloop()
        except:
            pass
        
        input("Press Enter to exit...")

if __name__ == "__main__":
    test_component_finder_gui()
