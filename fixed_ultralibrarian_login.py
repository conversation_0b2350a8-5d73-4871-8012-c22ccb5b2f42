#!/usr/bin/env python3
"""
Fixed UltraLibrarian login with all required form fields
"""

import requests
from bs4 import BeautifulSoup

def test_ultralibrarian_login():
    print("🔧 FIXED ULTRALIBRARIAN LOGIN TEST")
    print("=" * 50)
    
    # Create session with proper headers
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    # Step 1: Get login page to extract all form fields
    print("\n1. Getting login page to extract form fields...")
    login_url = 'https://www.ultralibrarian.com/wp-login.php'
    
    try:
        login_page = session.get(login_url, timeout=30)
        print(f"   Status: {login_page.status_code}")
        
        if login_page.status_code != 200:
            print(f"   ❌ Failed to get login page: {login_page.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error getting login page: {e}")
        return False
    
    # Step 2: Parse form and extract ALL fields including hidden ones
    print("\n2. Parsing login form and extracting all fields...")
    try:
        soup = BeautifulSoup(login_page.text, 'html.parser')
        form = soup.find('form', {'id': 'loginform'})
        
        if not form:
            print("   ❌ Could not find login form with id 'loginform'")
            return False
        
        print(f"   ✅ Found login form: action='{form.get('action')}', method='{form.get('method')}'")
        
        # Extract ALL form fields (including hidden ones)
        form_data = {}
        inputs = form.find_all('input')
        
        print(f"   Found {len(inputs)} input fields:")
        for inp in inputs:
            name = inp.get('name')
            value = inp.get('value', '')
            input_type = inp.get('type', 'text')
            
            if name:
                form_data[name] = value
                print(f"     {name}: '{value}' (type: {input_type})")
        
        # Now set our credentials (this will override the empty values)
        form_data['log'] = '<EMAIL>'
        form_data['pwd'] = 'Lennyai123#'
        
        print(f"\n   Complete form data with credentials:")
        for key, value in form_data.items():
            if key == 'pwd':
                print(f"     {key}: {'*' * len(value)}")
            else:
                print(f"     {key}: '{value}'")
        
    except Exception as e:
        print(f"   ❌ Error parsing form: {e}")
        return False
    
    # Step 3: Submit login with all form fields
    print("\n3. Submitting login with complete form data...")
    try:
        # Add form submission headers
        session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://www.ultralibrarian.com',
            'Referer': login_url,
        })
        
        login_response = session.post(
            login_url, 
            data=form_data, 
            timeout=30, 
            allow_redirects=True
        )
        
        print(f"   Status: {login_response.status_code}")
        print(f"   Final URL: {login_response.url}")
        
        # Save response for analysis
        with open('fixed_ultralibrarian_login_response.html', 'w', encoding='utf-8') as f:
            f.write(login_response.text)
        print("   📄 Saved response to fixed_ultralibrarian_login_response.html")
        
    except Exception as e:
        print(f"   ❌ Error submitting login: {e}")
        return False
    
    # Step 4: Analyze response for success/failure
    print("\n4. Analyzing login response...")
    
    # Check for error messages
    if 'login_error' in login_response.text:
        print("   ❌ Found login error in response")
        soup = BeautifulSoup(login_response.text, 'html.parser')
        error_div = soup.find('div', {'id': 'login_error'})
        if error_div:
            error_text = error_div.get_text(strip=True)
            print(f"   Error message: {error_text}")
        return False
    
    # Check for success indicators
    success_indicators = [
        ('wp-admin' in login_response.url, 'Redirected to wp-admin'),
        ('dashboard' in login_response.url, 'Redirected to dashboard'),
        ('logout' in login_response.text.lower(), 'Logout link found'),
        ('welcome' in login_response.text.lower(), 'Welcome message found'),
        ('profile' in login_response.text.lower(), 'Profile link found'),
    ]
    
    success_count = 0
    for indicator, description in success_indicators:
        if indicator:
            print(f"   ✅ {description}")
            success_count += 1
        else:
            print(f"   ❌ {description}")
    
    print(f"\n   Success indicators: {success_count}/{len(success_indicators)}")
    
    if success_count > 0:
        print("   🎉 Login appears to be successful!")
        return True
    else:
        print("   😞 Login appears to have failed")
        return False

if __name__ == "__main__":
    success = test_ultralibrarian_login()
    if success:
        print("\n✅ UltraLibrarian login test PASSED!")
    else:
        print("\n❌ UltraLibrarian login test FAILED!")
