#!/usr/bin/env python3
"""
SnapEDA API test script - ready for when you get your API key
"""

import requests
import json
import os
import time

def test_snapeda_api():
    print("🔑 SNAPEDA API TEST")
    print("=" * 50)
    
    # Get API key from user
    print("Enter your SnapEDA API key from the email:")
    api_key = input("API Key: ").strip()
    
    if not api_key:
        print("❌ API key is required")
        return False
    
    print(f"🔐 Testing API key: {api_key[:20]}...")
    
    # Test with APX803L20-30SA-7
    part_number = "APX803L20-30SA-7"
    manufacturer = "Diodes Inc"
    
    # SnapEDA API endpoints to try
    endpoints_to_try = [
        {
            'name': 'Search API v1',
            'url': f'https://api.snapeda.com/v1/parts/search',
            'method': 'GET',
            'params': {
                'apikey': api_key,
                'q': part_number,
                'limit': 5
            }
        },
        {
            'name': 'Part Details API',
            'url': f'https://api.snapeda.com/v1/parts/{part_number}',
            'method': 'GET',
            'params': {
                'apikey': api_key
            }
        },
        {
            'name': 'Manufacturer Search',
            'url': f'https://api.snapeda.com/v1/parts/search',
            'method': 'GET',
            'params': {
                'apikey': api_key,
                'q': part_number,
                'manufacturer': manufacturer,
                'limit': 5
            }
        },
        {
            'name': 'SnapMagic Search API',
            'url': f'https://api.snapmagic.com/v1/search',
            'method': 'GET',
            'params': {
                'apikey': api_key,
                'part': part_number,
                'format': 'json'
            }
        }
    ]
    
    headers = {
        'Accept': 'application/json',
        'User-Agent': 'ComponentFinder/1.0'
    }
    
    for i, endpoint in enumerate(endpoints_to_try, 1):
        try:
            print(f"\n🧪 Testing endpoint {i}: {endpoint['name']}")
            print(f"   URL: {endpoint['url']}")
            
            if endpoint['method'] == 'POST':
                response = requests.post(
                    endpoint['url'], 
                    headers=headers, 
                    json=endpoint.get('data', {}), 
                    timeout=30
                )
            else:
                response = requests.get(
                    endpoint['url'], 
                    headers=headers, 
                    params=endpoint.get('params', {}), 
                    timeout=30
                )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS! {endpoint['name']} works!")
                
                try:
                    results = response.json()
                    
                    # Save successful results
                    with open(f'snapeda_success_{i}.json', 'w') as f:
                        json.dump(results, f, indent=2)
                    print(f"   💾 Results saved to snapeda_success_{i}.json")
                    
                    # Try to extract component info
                    components = extract_snapeda_components(results)
                    if components:
                        print(f"   📦 Found {len(components)} components!")
                        show_snapeda_component(components[0])
                        
                        # Try to download models
                        download_snapeda_models(components[0], part_number)
                        
                        # Save API key for future use
                        save_snapeda_credentials(api_key)
                        return True
                    else:
                        print(f"   ⚠️ API works but no components found")
                        
                except json.JSONDecodeError:
                    print(f"   ⚠️ Response is not JSON")
                    print(f"   Content: {response.text[:100]}...")
                    
            elif response.status_code == 401:
                print(f"   ❌ Authentication failed - check API key")
            elif response.status_code == 403:
                print(f"   ❌ Access forbidden - API key may not have permissions")
            elif response.status_code == 404:
                print(f"   ❌ Endpoint not found")
            elif response.status_code == 429:
                print(f"   ❌ Rate limit exceeded")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Response: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)[:50]}")
            continue
    
    return False

def extract_snapeda_components(results):
    """Extract components from SnapEDA API response"""
    # SnapEDA API typically uses different structure
    possible_paths = [
        ['results'],
        ['parts'],
        ['components'],
        ['data', 'results'],
        ['data', 'parts'],
        ['response', 'parts']
    ]
    
    for path in possible_paths:
        current = results
        try:
            for key in path:
                current = current[key]
            
            if isinstance(current, list) and len(current) > 0:
                return current
        except (KeyError, TypeError):
            continue
    
    # If it's a single component, wrap in list
    if isinstance(results, dict) and ('part_number' in results or 'mpn' in results):
        return [results]
    
    return []

def show_snapeda_component(component):
    """Display SnapEDA component information"""
    print(f"\n   📦 COMPONENT FOUND:")
    
    # SnapEDA API field names (may vary)
    part_num = (component.get('part_number') or 
                component.get('mpn') or 
                component.get('manufacturer_part_number') or 'N/A')
    
    manufacturer = (component.get('manufacturer') or 
                   component.get('manufacturer_name') or 'N/A')
    
    description = (component.get('description') or 
                  component.get('short_description') or 'N/A')
    
    package = (component.get('package') or 
              component.get('package_name') or 'N/A')
    
    datasheet = (component.get('datasheet_url') or 
                component.get('datasheet') or '')
    
    # Model URLs
    symbol_url = component.get('symbol_url', '')
    footprint_url = component.get('footprint_url', '')
    model_3d_url = component.get('model_3d_url', '')
    
    print(f"      Part: {part_num}")
    print(f"      Manufacturer: {manufacturer}")
    print(f"      Description: {description[:60]}...")
    print(f"      Package: {package}")
    
    if datasheet:
        print(f"      📄 Datasheet: {datasheet}")
    
    if symbol_url:
        print(f"      🔣 Symbol: Available")
    
    if footprint_url:
        print(f"      👣 Footprint: Available")
    
    if model_3d_url:
        print(f"      🧊 3D Model: Available")

def download_snapeda_models(component, part_number):
    """Download SnapEDA models (symbols, footprints, 3D)"""
    try:
        os.makedirs('snapeda_models', exist_ok=True)
        
        # URLs for different model types
        model_urls = {
            'symbol': component.get('symbol_url', ''),
            'footprint': component.get('footprint_url', ''),
            '3d_model': component.get('model_3d_url', ''),
            'datasheet': component.get('datasheet_url', '')
        }
        
        downloaded = []
        
        for model_type, url in model_urls.items():
            if url:
                try:
                    print(f"      📥 Downloading {model_type}...")
                    
                    response = requests.get(url, timeout=60)
                    
                    if response.status_code == 200:
                        # Determine file extension
                        if model_type == 'datasheet':
                            ext = '.pdf'
                        elif model_type == '3d_model':
                            ext = '.step'
                        elif model_type == 'symbol':
                            ext = '.lib'
                        elif model_type == 'footprint':
                            ext = '.mod'
                        else:
                            ext = '.dat'
                        
                        filename = f"SnapEDA_{part_number}_{model_type}{ext}"
                        filepath = os.path.join('snapeda_models', filename)
                        
                        with open(filepath, 'wb') as f:
                            f.write(response.content)
                        
                        print(f"      ✅ Downloaded: {filename}")
                        downloaded.append(filename)
                    else:
                        print(f"      ❌ {model_type} download failed: {response.status_code}")
                        
                except Exception as e:
                    print(f"      ❌ {model_type} error: {str(e)[:30]}")
        
        return downloaded
        
    except Exception as e:
        print(f"      ❌ Download setup error: {str(e)[:30]}")
        return []

def save_snapeda_credentials(api_key):
    """Save SnapEDA API credentials"""
    try:
        creds = {'api_key': api_key}
        with open('snapeda_api_credentials.json', 'w') as f:
            json.dump(creds, f, indent=2)
        print(f"   💾 SnapEDA credentials saved!")
    except Exception as e:
        print(f"   ⚠️ Could not save credentials: {e}")

def main():
    print("🚀 SNAPEDA API TEST - READY FOR YOUR API KEY")
    print("Run this script when you receive your SnapEDA API key")
    print("=" * 70)
    
    success = test_snapeda_api()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 SUCCESS! Your SnapEDA API is working!")
        print("✅ You now have access to:")
        print("   • Symbols for schematic design")
        print("   • Footprints for PCB layout")
        print("   • 3D models for visualization")
        print("   • Datasheet links")
        print("   • Compatible with 20+ CAD tools")
        
        print("\n🔧 Integration ready:")
        print("   • SnapEDA API: Symbols, footprints, 3D models")
        print("   • Digikey API: Part data, pricing, availability")
        print("   • Mouser API: Alternative part data")
        print("   • Direct manufacturers: Additional 3D models")
        
        print("\n📁 Files created:")
        print("   • snapeda_api_credentials.json")
        print("   • snapeda_models/ (downloaded models)")
        
    else:
        print("⚠️ SnapEDA API test failed")
        print("💡 Check your API key and try again")
        print("📧 Make sure you received API approval from SnapEDA")

if __name__ == "__main__":
    main()
