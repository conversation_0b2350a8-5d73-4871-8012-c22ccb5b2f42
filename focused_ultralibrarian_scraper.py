#!/usr/bin/env python3
"""
FOCUSED ULTRALIBRARIAN SCRAPER
==============================
Simplified, focused approach to navigate UltraLibrarian's multi-screen process.
Uses fresh Chrome sessions and targeted element finding.
"""

import os
import time
from urllib.parse import quote
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException

class FocusedUltraLibrarianScraper:
    def __init__(self):
        self.base_url = 'https://www.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("Focused UltraLibrarian Scraper Ready!")

    def setup_fresh_driver(self):
        """Setup a fresh Chrome driver"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3D'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            return driver
        except Exception as e:
            print(f"Chrome driver setup failed: {e}")
            return None

    def wait_and_find_elements(self, driver, xpath, timeout=10):
        """Wait for and find elements by XPath"""
        try:
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            return driver.find_elements(By.XPATH, xpath)
        except TimeoutException:
            return []
        except Exception as e:
            print(f"   Error finding elements: {e}")
            return []

    def safe_click_by_xpath(self, driver, xpath, description="element"):
        """Safely click element by XPath"""
        try:
            element = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, xpath))
            )
            driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(1)
            element.click()
            print(f"   Clicked: {description}")
            return True
        except Exception as e:
            print(f"   Failed to click {description}: {e}")
            return False

    def get_step_file(self, manufacturer, part_number):
        """Main method to get STEP file through multi-screen navigation"""
        print(f"\nFOCUSED ULTRALIBRARIAN SCRAPING")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("-" * 50)
        
        driver = self.setup_fresh_driver()
        if not driver:
            return None
        
        try:
            # Step 1: Search
            search_term = f"{manufacturer} {part_number}"
            search_url = f"{self.base_url}/search?q={quote(search_term)}"
            print(f"Loading: {search_url}")
            
            driver.get(search_url)
            time.sleep(8)  # Wait for page load
            
            # Step 2: Find and click first relevant result
            print("Looking for search results...")
            
            # Try multiple XPath strategies for finding results
            result_xpaths = [
                "//a[contains(@href, '/part/') and contains(text(), 'LM358')]",
                "//a[contains(@href, '/component/') and contains(text(), 'LM358')]",
                "//tr//a[contains(@href, 'ultralibrarian') and contains(text(), 'LM358')]",
                "//div[contains(@class, 'result')]//a[contains(text(), 'LM358')]",
                "//a[contains(text(), 'LM358')]"
            ]
            
            clicked_result = False
            for xpath in result_xpaths:
                elements = self.wait_and_find_elements(driver, xpath, 5)
                if elements:
                    print(f"Found {len(elements)} results with xpath: {xpath}")
                    for i, element in enumerate(elements[:3]):  # Try first 3
                        try:
                            text = element.text.strip()
                            href = element.get_attribute('href')
                            print(f"   Result {i+1}: {text[:50]}... -> {href}")
                            
                            # Click the result
                            driver.execute_script("arguments[0].click();", element)
                            time.sleep(5)
                            clicked_result = True
                            break
                        except Exception as e:
                            print(f"   Failed to click result {i+1}: {e}")
                            continue
                    
                    if clicked_result:
                        break
            
            if not clicked_result:
                print("No clickable results found")
                return None
            
            # Step 3: Look for download options on part page
            print("Looking for download options on part page...")
            
            download_xpaths = [
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download')]",
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download')]",
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')]",
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'step')]",
                "//a[contains(@href, '.step')]",
                "//a[contains(@href, '.stp')]"
            ]
            
            downloaded = False
            for xpath in download_xpaths:
                elements = self.wait_and_find_elements(driver, xpath, 3)
                if elements:
                    print(f"Found {len(elements)} download options with xpath: {xpath}")
                    for i, element in enumerate(elements[:2]):  # Try first 2
                        try:
                            text = element.text.strip()
                            print(f"   Trying download {i+1}: {text[:40]}...")
                            
                            # Get initial file count
                            initial_files = set(os.listdir('3D'))
                            
                            # Click download
                            driver.execute_script("arguments[0].click();", element)
                            time.sleep(10)  # Wait for download
                            
                            # Check for new files
                            current_files = set(os.listdir('3D'))
                            new_files = current_files - initial_files
                            
                            if new_files:
                                for new_file in new_files:
                                    if any(ext in new_file.lower() for ext in ['.step', '.stp', '.zip']):
                                        print(f"   SUCCESS: Downloaded {new_file}")
                                        result = self.process_file(new_file, manufacturer, part_number)
                                        if result:
                                            return result
                                        downloaded = True
                                        break
                            
                            if downloaded:
                                break
                                
                        except Exception as e:
                            print(f"   Download attempt {i+1} failed: {e}")
                            continue
                
                if downloaded:
                    break
            
            # Step 4: If no direct downloads, look for detail/model links
            if not downloaded:
                print("Looking for detail/model links...")
                
                detail_xpaths = [
                    "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'model')]",
                    "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'detail')]",
                    "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'more')]",
                    "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'cad')]"
                ]
                
                for xpath in detail_xpaths:
                    elements = self.wait_and_find_elements(driver, xpath, 3)
                    if elements:
                        print(f"Found {len(elements)} detail links with xpath: {xpath}")
                        for i, element in enumerate(elements[:2]):  # Try first 2
                            try:
                                text = element.text.strip()
                                print(f"   Following detail link {i+1}: {text[:40]}...")
                                
                                # Click detail link
                                driver.execute_script("arguments[0].click();", element)
                                time.sleep(5)
                                
                                # Look for downloads on detail page
                                for dl_xpath in download_xpaths:
                                    dl_elements = self.wait_and_find_elements(driver, dl_xpath, 3)
                                    if dl_elements:
                                        for j, dl_element in enumerate(dl_elements[:2]):
                                            try:
                                                dl_text = dl_element.text.strip()
                                                print(f"      Trying detail download {j+1}: {dl_text[:30]}...")
                                                
                                                # Get initial file count
                                                initial_files = set(os.listdir('3D'))
                                                
                                                # Click download
                                                driver.execute_script("arguments[0].click();", dl_element)
                                                time.sleep(10)
                                                
                                                # Check for new files
                                                current_files = set(os.listdir('3D'))
                                                new_files = current_files - initial_files
                                                
                                                if new_files:
                                                    for new_file in new_files:
                                                        if any(ext in new_file.lower() for ext in ['.step', '.stp', '.zip']):
                                                            print(f"      SUCCESS: Downloaded {new_file}")
                                                            result = self.process_file(new_file, manufacturer, part_number)
                                                            if result:
                                                                return result
                                                            downloaded = True
                                                            break
                                                
                                                if downloaded:
                                                    break
                                                    
                                            except Exception as e:
                                                print(f"      Detail download {j+1} failed: {e}")
                                                continue
                                    
                                    if downloaded:
                                        break
                                
                                if downloaded:
                                    break
                                
                                # Go back
                                driver.back()
                                time.sleep(3)
                                
                            except Exception as e:
                                print(f"   Detail link {i+1} failed: {e}")
                                continue
                    
                    if downloaded:
                        break
            
            if not downloaded:
                print("No downloads found through multi-screen navigation")
                return None
            
        except Exception as e:
            print(f"Scraping error: {e}")
            return None
        finally:
            driver.quit()

    def process_file(self, filename, manufacturer, part_number):
        """Process downloaded file"""
        try:
            old_path = os.path.join('3D', filename)
            
            if filename.lower().endswith('.zip'):
                # Extract STEP from ZIP
                extracted = self.extract_step_from_zip(old_path, manufacturer, part_number)
                if extracted:
                    os.remove(old_path)
                    self.create_log_file(extracted, manufacturer, part_number)
                    return extracted
            else:
                # Rename STEP file
                new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                new_path = os.path.join('3D', new_name)
                
                try:
                    os.rename(old_path, new_path)
                    self.create_log_file(new_name, manufacturer, part_number)
                    return new_name
                except:
                    self.create_log_file(filename, manufacturer, part_number)
                    return filename
        
        except Exception as e:
            print(f"File processing failed: {e}")
        
        return None

    def extract_step_from_zip(self, zip_path, manufacturer, part_number):
        """Extract STEP files from ZIP"""
        try:
            import zipfile
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                step_files = [f for f in zip_ref.namelist() if f.lower().endswith(('.step', '.stp'))]
                
                if step_files:
                    step_file = step_files[0]
                    zip_ref.extract(step_file, '3D')
                    
                    extracted_path = os.path.join('3D', step_file)
                    new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                    new_path = os.path.join('3D', new_name)
                    
                    os.rename(extracted_path, new_path)
                    return new_name
            
        except Exception as e:
            print(f"ZIP extraction failed: {e}")
        
        return None

    def create_log_file(self, filename, manufacturer, part_number):
        """Create log file"""
        log_name = filename.replace('.step', '.txt')
        log_path = os.path.join('3D', log_name)
        
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(f"{manufacturer.upper()} {part_number} STEP FILE DOWNLOAD LOG\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Manufacturer: {manufacturer}\n")
            f.write(f"Part Number: {part_number}\n")
            f.write(f"Source: UltraLibrarian (Focused Multi-Screen)\n")
            f.write(f"Downloaded File: {filename}\n")
            f.write(f"Location: 3D/{filename}\n")
            f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("SUCCESS: Automated multi-screen navigation worked!\n")

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python focused_ultralibrarian_scraper.py \"Manufacturer\" \"Part Number\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    scraper = FocusedUltraLibrarianScraper()
    result = scraper.get_step_file(manufacturer, part_number)
    
    if result:
        print(f"\nSUCCESS: Downloaded {result}")
        print(f"Location: 3D/{result}")
    else:
        print(f"\nFAILED: Could not download STEP file for {part_number}")

if __name__ == "__main__":
    main()
