#!/usr/bin/env python3
"""
Debug UltraLibrarian login by exactly replicating manual login
"""

import requests
from bs4 import BeautifulSoup
import json
from datetime import datetime

def log_message(message):
    print(message)

def debug_ultralibrarian_login():
    log_message("🔍 DEBUGGING ULTRALIBRARIAN LOGIN - EXACT REPLICATION")
    log_message("=" * 60)
    
    session = requests.Session()
    
    # Use exact browser headers
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
    })
    
    # Step 1: Get login page and extract ALL details
    log_message("\n1. Getting login page with exact browser behavior...")
    
    login_url = 'https://www.ultralibrarian.com/wp-login.php'
    
    try:
        login_response = session.get(login_url, timeout=30)
        log_message(f"   Status: {login_response.status_code}")
        log_message(f"   Cookies received: {len(session.cookies)}")
        
        for cookie in session.cookies:
            log_message(f"     {cookie.name}: {cookie.value}")
        
        if login_response.status_code != 200:
            log_message(f"   ❌ Failed to get login page")
            return False
        
        # Parse the form with extreme detail
        soup = BeautifulSoup(login_response.text, 'html.parser')
        form = soup.find('form', {'id': 'loginform'})
        
        if not form:
            log_message(f"   ❌ No login form found")
            return False
        
        log_message(f"   ✅ Found login form")
        log_message(f"   Form action: {form.get('action')}")
        log_message(f"   Form method: {form.get('method')}")
        
        # Extract EVERY input field with full details
        form_data = {}
        inputs = form.find_all('input')

        log_message(f"\n   Found {len(inputs)} input fields:")
        for i, inp in enumerate(inputs, 1):
            name = inp.get('name')
            value = inp.get('value', '')
            input_type = inp.get('type', 'text')
            required = inp.get('required')

            log_message(f"   {i}. name='{name}' type='{input_type}' value='{value}' required={required}")

            if name:
                # Only include checkbox if it should be checked (skip rememberme)
                if input_type == 'checkbox' and name == 'rememberme':
                    log_message(f"      → Skipping unchecked checkbox: {name}")
                    continue
                form_data[name] = value
        
        # Set credentials - EXACTLY as browser would
        form_data['log'] = '<EMAIL>'
        form_data['pwd'] = 'Lennyai123#'

        # Ensure we have the exact form action URL
        form_action = form.get('action') or login_url
        if not form_action.startswith('http'):
            form_action = 'https://www.ultralibrarian.com' + form_action

        log_message(f"   Form action: {form_action}")
        log_message(f"\n   Final form data:")
        for key, value in form_data.items():
            if key == 'pwd':
                log_message(f"     {key}: [HIDDEN]")
            else:
                log_message(f"     {key}: '{value}'")
        
    except Exception as e:
        log_message(f"   ❌ Error getting login page: {e}")
        return False
    
    # Step 2: Submit with exact browser headers
    log_message(f"\n2. Submitting login with exact browser headers...")
    
    try:
        # Update headers for form submission (exactly like browser)
        session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://www.ultralibrarian.com',
            'Referer': 'https://www.ultralibrarian.com/wp-login.php',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        })
        
        # Submit form to exact action URL
        submit_response = session.post(
            form_action,
            data=form_data,
            timeout=30,
            allow_redirects=True
        )
        
        log_message(f"   Submit status: {submit_response.status_code}")
        log_message(f"   Final URL: {submit_response.url}")
        log_message(f"   Response length: {len(submit_response.text)}")
        
        # Check cookies after login
        log_message(f"   Cookies after login: {len(session.cookies)}")
        for cookie in session.cookies:
            log_message(f"     {cookie.name}: {cookie.value[:50]}...")
        
        # Save response for analysis
        with open('debug_ultralibrarian_exact_response.html', 'w', encoding='utf-8') as f:
            f.write(submit_response.text)
        log_message(f"   📄 Saved response")
        
    except Exception as e:
        log_message(f"   ❌ Error submitting login: {e}")
        return False
    
    # Step 3: Analyze response in detail
    log_message(f"\n3. Analyzing response...")
    
    # Check for error messages
    if 'login_error' in submit_response.text:
        log_message(f"   ❌ Login error found")
        soup = BeautifulSoup(submit_response.text, 'html.parser')
        error_div = soup.find('div', {'id': 'login_error'})
        if error_div:
            error_text = error_div.get_text(strip=True)
            log_message(f"   Error message: {error_text}")
    
    # Check for success indicators
    success_indicators = [
        ('wp-admin' in submit_response.url, 'wp-admin in URL'),
        ('dashboard' in submit_response.url, 'dashboard in URL'),
        ('logout' in submit_response.text.lower(), 'logout in content'),
        ('welcome' in submit_response.text.lower(), 'welcome in content'),
        (submit_response.url != login_url, 'redirected away from login'),
    ]
    
    success_count = 0
    for indicator, description in success_indicators:
        if indicator:
            log_message(f"   ✅ {description}")
            success_count += 1
        else:
            log_message(f"   ❌ {description}")
    
    if success_count > 0:
        log_message(f"   🎉 Login appears successful! ({success_count}/{len(success_indicators)} indicators)")
        return True
    else:
        log_message(f"   ❌ Login failed - no success indicators")
        return False

def main():
    log_message("🚀 ULTRALIBRARIAN DEBUG - EXACT BROWSER REPLICATION")
    log_message("=" * 60)
    
    success = debug_ultralibrarian_login()
    
    log_message("\n" + "=" * 60)
    if success:
        log_message("🎉 SUCCESS: Login worked!")
    else:
        log_message("❌ FAILED: Login still failing")
        log_message("   Check debug_ultralibrarian_exact_response.html for details")

if __name__ == "__main__":
    main()
