#!/usr/bin/env python3
"""
UltraLibrarian 3D model searcher
Can be used by main program or run standalone
"""

import requests
from bs4 import BeautifulSoup
import json
import os
import sys

class UltraLibrarian3DSearcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        self.logged_in = False
    
    def search_part_3d(self, part_number):
        """Search UltraLibrarian for 3D models"""
        print(f"🔍 SEARCHING ULTRALIBRARIAN FOR: {part_number}")
        
        try:
            # First try to access the search without login
            search_url = f"https://www.ultralibrarian.com/search?query={part_number}"
            
            response = self.session.get(search_url, timeout=30)
            print(f"   Search Status: {response.status_code}")
            
            if response.status_code == 200:
                # Save search results
                with open('ultralibrarian_search.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                # Check if login is required
                if 'login' in response.text.lower() or 'sign in' in response.text.lower():
                    print(f"   ⚠️  Login required - UltraLibrarian blocks automated access")
                    print(f"   💡 Manual login needed at: https://www.ultralibrarian.com/login")
                    return None
                
                # Look for parts
                if part_number in response.text:
                    print(f"   ✅ Found part in search results")
                    return self.extract_3d_links(response.text, part_number)
                else:
                    print(f"   ❌ Part not found in search results")
                    return None
            else:
                print(f"   ❌ Search failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ SEARCH ERROR: {e}")
            return None
    
    def extract_3d_links(self, html_content, part_number):
        """Extract 3D model download links"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for download links
            download_links = []
            
            for link in soup.find_all('a', href=True):
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                
                if any(keyword in href or keyword in text 
                       for keyword in ['download', 'step', '3d', 'model', 'cad']):
                    download_links.append({
                        'url': link.get('href'),
                        'text': link.get_text(strip=True)
                    })
            
            # Also look for part detail links
            part_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if '/part/' in href or part_number.lower() in href.lower():
                    part_links.append(href)
            
            if part_links:
                print(f"   🎯 Found {len(part_links)} part detail links")
                # Try to access the first part detail page
                return self.get_part_detail_3d(part_links[0], part_number)
            elif download_links:
                print(f"   🎯 Found {len(download_links)} download links")
                return self.download_3d_models(download_links, part_number)
            else:
                print(f"   ❌ No relevant links found")
                return None
                
        except Exception as e:
            print(f"   ❌ EXTRACTION ERROR: {e}")
            return None
    
    def get_part_detail_3d(self, part_url, part_number):
        """Access part detail page for 3D models"""
        try:
            # Make URL absolute
            if not part_url.startswith('http'):
                part_url = f"https://www.ultralibrarian.com{part_url}"
            
            print(f"   Accessing part detail: {part_url}")
            
            response = self.session.get(part_url, timeout=30)
            print(f"   Part detail status: {response.status_code}")
            
            if response.status_code == 200:
                # Save part page
                with open('ultralibrarian_part_detail.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                # Look for 3D model downloads
                soup = BeautifulSoup(response.text, 'html.parser')
                
                download_links = []
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '').lower()
                    text = link.get_text(strip=True).lower()
                    
                    if any(keyword in href or keyword in text 
                           for keyword in ['step', '3d', 'model', 'download']):
                        download_links.append({
                            'url': link.get('href'),
                            'text': link.get_text(strip=True)
                        })
                
                if download_links:
                    print(f"   🎯 Found {len(download_links)} download links on part page")
                    return self.download_3d_models(download_links, part_number)
                else:
                    print(f"   ❌ No download links found on part page")
                    return None
            else:
                print(f"   ❌ Failed to access part detail")
                return None
                
        except Exception as e:
            print(f"   ❌ PART DETAIL ERROR: {e}")
            return None
    
    def download_3d_models(self, download_links, part_number):
        """Download 3D models from UltraLibrarian"""
        downloaded_files = []
        
        for i, link in enumerate(download_links[:3], 1):  # Try first 3
            try:
                url = link['url']
                
                # Make URL absolute
                if not url.startswith('http'):
                    url = f"https://www.ultralibrarian.com{url}"
                
                print(f"📥 Trying download {i}: {link['text']}")
                print(f"   URL: {url}")
                
                response = self.session.get(url, timeout=60, stream=True)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    
                    # Check if it's a STEP file
                    if any(ct in content_type for ct in ['application/octet-stream', 'application/step', 'model/step']):
                        filename = f"UltraLibrarian-{part_number}.step"
                        if self.save_step_file(response, filename):
                            downloaded_files.append(filename)
                    
                    # Check if it's a ZIP file (common for UltraLibrarian)
                    elif 'application/zip' in content_type:
                        filename = f"UltraLibrarian-{part_number}.zip"
                        if self.save_zip_file(response, filename, part_number):
                            downloaded_files.append(filename)
                    
                    else:
                        print(f"   ⚠️  Unexpected content type: {content_type}")
                
            except Exception as e:
                print(f"   ❌ Download {i} error: {e}")
        
        return downloaded_files
    
    def save_step_file(self, response, filename):
        """Save STEP file and verify"""
        try:
            os.makedirs('3d', exist_ok=True)
            filepath = os.path.join('3d', filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    first_lines = f.read(200)
                
                if 'ISO-10303' in first_lines or 'STEP' in first_lines:
                    print(f"   🎉 SUCCESS: Valid STEP file!")
                    return True
                else:
                    print(f"   ⚠️  File doesn't appear to be STEP format")
                    return False
            else:
                print(f"   ⚠️  File too small")
                return False
                
        except Exception as e:
            print(f"   ❌ Save error: {e}")
            return False
    
    def save_zip_file(self, response, filename, part_number):
        """Save ZIP file (UltraLibrarian often provides ZIP packages)"""
        try:
            os.makedirs('3d', exist_ok=True)
            filepath = os.path.join('3d', filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"   ✅ Downloaded ZIP: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                print(f"   📦 ZIP package downloaded (may contain STEP files)")
                return True
            else:
                print(f"   ⚠️  ZIP file too small")
                return False
                
        except Exception as e:
            print(f"   ❌ ZIP save error: {e}")
            return False

def main():
    """Standalone usage"""
    if len(sys.argv) < 2:
        print("Usage: python ultralibrarian_3d_searcher.py <part_number>")
        print("Example: python ultralibrarian_3d_searcher.py APX803L20-30SA-7")
        return
    
    part_number = sys.argv[1]
    
    print("🚀 ULTRALIBRARIAN 3D MODEL SEARCHER")
    print("=" * 45)
    
    searcher = UltraLibrarian3DSearcher()
    
    # Search for 3D models
    downloaded = searcher.search_part_3d(part_number)
    
    print("\n" + "=" * 45)
    if downloaded:
        print(f"✅ SUCCESS: Downloaded {len(downloaded)} files from UltraLibrarian!")
        for file in downloaded:
            print(f"   🎯 {file}")
    else:
        print("❌ FAILED: Could not find or download 3D models from UltraLibrarian")
        print("💡 Note: UltraLibrarian often requires manual login")

if __name__ == "__main__":
    main()
