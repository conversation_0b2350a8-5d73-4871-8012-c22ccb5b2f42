#!/usr/bin/env python3
"""
Direct access to SamacSys part page to download STEP file
We found the exact URL: /part-view/APX803L20-30SA-7/Diodes%20Incorporated
"""

import requests
from bs4 import BeautifulSoup
import os
import time
from datetime import datetime

def log_message(message):
    """Write message to log file"""
    print(message)
    with open('samacsys_direct_log.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {message}\n")

def access_samacsys_part_page():
    log_message("🎯 DIRECT ACCESS TO SAMACSYS PART PAGE")
    log_message("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    # Direct URL to the part page we found
    part_url = "https://componentsearchengine.com/part-view/APX803L20-30SA-7/Diodes%20Incorporated"
    
    log_message(f"🔍 Accessing part page directly...")
    log_message(f"   URL: {part_url}")
    
    try:
        response = session.get(part_url, timeout=30)
        log_message(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the part page
            with open('samacsys_part_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            log_message(f"   📄 Saved: samacsys_part_page.html")
            
            # Parse the page
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for STEP download links
            step_links = find_step_download_links(soup, part_url)
            
            if step_links:
                log_message(f"   ✅ Found {len(step_links)} STEP download links:")
                for i, link in enumerate(step_links, 1):
                    log_message(f"   {i}. {link['text']}")
                    log_message(f"      URL: {link['url']}")
                
                # Try to download the first STEP file
                for link in step_links:
                    success = download_step_file(session, link)
                    if success:
                        return True
                
                log_message(f"   ⚠️  Found STEP links but download failed")
                return False
            else:
                log_message(f"   ❌ No STEP download links found")
                
                # Look for any download buttons or forms
                download_elements = find_download_elements(soup)
                if download_elements:
                    log_message(f"   Found {len(download_elements)} download elements:")
                    for i, elem in enumerate(download_elements, 1):
                        log_message(f"   {i}. {elem['type']}: {elem['text']}")
                        log_message(f"      {elem['info']}")
                
                return False
        else:
            log_message(f"   ❌ Failed to access part page: {response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Error accessing part page: {e}")
        return False

def find_step_download_links(soup, base_url):
    """Find direct STEP file download links"""
    step_links = []
    
    # Look for direct STEP file links
    for link in soup.find_all('a', href=True):
        href = link.get('href')
        text = link.get_text(strip=True)
        
        if href and any(ext in href.lower() for ext in ['.step', '.stp']):
            # Make URL absolute
            if not href.startswith('http'):
                if href.startswith('/'):
                    href = f"https://componentsearchengine.com{href}"
                else:
                    href = f"{base_url}/{href}"
            
            step_links.append({
                'url': href,
                'text': text,
                'type': 'direct_step'
            })
    
    # Look for download buttons that might lead to STEP files
    download_selectors = [
        'a[href*="download"]',
        '.download-btn',
        '.download-button',
        'button[onclick*="download"]',
        'a[onclick*="download"]'
    ]
    
    for selector in download_selectors:
        elements = soup.select(selector)
        for element in elements:
            href = element.get('href')
            onclick = element.get('onclick', '')
            text = element.get_text(strip=True)
            
            if 'step' in text.lower() or 'step' in onclick.lower():
                if href:
                    if not href.startswith('http'):
                        if href.startswith('/'):
                            href = f"https://componentsearchengine.com{href}"
                        else:
                            href = f"{base_url}/{href}"
                    
                    step_links.append({
                        'url': href,
                        'text': text,
                        'type': 'download_button'
                    })
    
    return step_links

def find_download_elements(soup):
    """Find any download-related elements for analysis"""
    download_elements = []
    
    # Look for forms
    for form in soup.find_all('form'):
        action = form.get('action', '')
        method = form.get('method', 'GET')
        form_text = form.get_text(strip=True)[:100]
        
        if any(keyword in form_text.lower() for keyword in ['download', 'step', 'model']):
            download_elements.append({
                'type': 'form',
                'text': form_text,
                'info': f"Action: {action}, Method: {method}"
            })
    
    # Look for buttons
    for button in soup.find_all(['button', 'input']):
        button_type = button.get('type', '')
        value = button.get('value', '')
        text = button.get_text(strip=True)
        onclick = button.get('onclick', '')
        
        if any(keyword in (text + value + onclick).lower() for keyword in ['download', 'step', 'model']):
            download_elements.append({
                'type': f'button ({button_type})',
                'text': text or value,
                'info': f"onclick: {onclick[:50]}..."
            })
    
    # Look for JavaScript download functions
    scripts = soup.find_all('script')
    for script in scripts:
        script_content = script.string or ''
        if 'download' in script_content.lower() and 'step' in script_content.lower():
            # Extract relevant function names
            lines = [line.strip() for line in script_content.split('\n') if 'download' in line.lower()]
            for line in lines[:3]:  # First 3 relevant lines
                download_elements.append({
                    'type': 'javascript',
                    'text': line[:80] + '...' if len(line) > 80 else line,
                    'info': 'JavaScript download function'
                })
    
    return download_elements

def download_step_file(session, link):
    """Try to download a STEP file"""
    log_message(f"   🔽 Attempting to download STEP file...")
    log_message(f"   URL: {link['url']}")
    
    try:
        download_response = session.get(link['url'], timeout=60, stream=True)
        log_message(f"   Download status: {download_response.status_code}")
        
        if download_response.status_code == 200:
            # Check content type
            content_type = download_response.headers.get('content-type', '').lower()
            log_message(f"   Content-Type: {content_type}")
            
            # Create 3d directory
            os.makedirs('3d', exist_ok=True)
            
            # Determine filename
            filename = "APX803L20-30SA-7_SamacSys.step"
            filepath = os.path.join('3d', filename)
            
            # Save file
            with open(filepath, 'wb') as f:
                for chunk in download_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            log_message(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            # Check if it's actually a STEP file
            if file_size > 1000:
                # Read first few lines to check STEP format
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    first_lines = f.read(200)
                
                if 'ISO-10303' in first_lines or 'STEP' in first_lines:
                    log_message(f"   🎉 SUCCESS: Valid STEP file downloaded!")
                    return True
                else:
                    log_message(f"   ⚠️  File doesn't appear to be STEP format")
                    log_message(f"   First 200 chars: {first_lines[:100]}...")
                    return False
            else:
                log_message(f"   ⚠️  File too small, might be error page")
                return False
        else:
            log_message(f"   ❌ Download failed: {download_response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Download error: {e}")
        return False

def main():
    # Clear log
    with open('samacsys_direct_log.txt', 'w') as f:
        f.write(f"SAMACSYS DIRECT ACCESS LOG - {datetime.now()}\n")
        f.write("=" * 60 + "\n")
    
    log_message("🚀 DIRECT SAMACSYS STEP FILE DOWNLOAD")
    log_message("=" * 60)
    
    success = access_samacsys_part_page()
    
    # Summary
    log_message("\n" + "=" * 60)
    if success:
        log_message("🎉 SUCCESS: Downloaded STEP file from SamacSys!")
    else:
        log_message("❌ Failed to download STEP file from SamacSys")
    
    # Check for actual STEP files
    step_files = []
    if os.path.exists('3d'):
        for file in os.listdir('3d'):
            if file.endswith('.step') and not file.startswith('KiCad_'):
                step_files.append(file)
    
    if step_files:
        log_message(f"\n🎯 ACTUAL STEP FILES FOUND:")
        for file in step_files:
            filepath = os.path.join('3d', file)
            size = os.path.getsize(filepath)
            log_message(f"   ✅ {file} ({size:,} bytes)")
        log_message(f"\n🎉 SUCCESS: We now have real manufacturer STEP files!")
    else:
        log_message(f"\n❌ Still no STEP files downloaded")
        log_message(f"   Check samacsys_part_page.html for manual inspection")
    
    log_message(f"\n📄 Full log saved to samacsys_direct_log.txt")

if __name__ == "__main__":
    main()
