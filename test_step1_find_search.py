#!/usr/bin/env python3
"""
STEP 1 TEST: Just find the search box on TI website
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

def test_find_search_box():
    print("STEP 1: Finding search box on TI website")
    print("=" * 50)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Load TI website
        print("Loading https://www.ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(15)  # Wait for page to fully load
        
        print(f"Page title: {driver.title}")
        
        # Try to find search box
        search_selectors = [
            "#searchboxheader",
            "#searchboxheader input",
            ".coveo-search-section input",
            "input[type='search']",
            "input[placeholder*='search' i]"
        ]
        
        found_search = False
        for selector in search_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    element = elements[0]
                    print(f"✅ FOUND search box with selector: {selector}")
                    print(f"   Tag: {element.tag_name}")
                    print(f"   Displayed: {element.is_displayed()}")
                    print(f"   Enabled: {element.is_enabled()}")
                    print(f"   Text: '{element.text}'")
                    
                    # Try to get attributes
                    try:
                        attrs = ['id', 'class', 'name', 'placeholder', 'type']
                        for attr in attrs:
                            value = element.get_attribute(attr)
                            if value:
                                print(f"   {attr}: '{value}'")
                    except:
                        pass
                    
                    found_search = True
                    break
            except Exception as e:
                print(f"❌ Error with selector '{selector}': {e}")
        
        if not found_search:
            print("❌ No search box found")
            
            # Debug: show all input elements
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"\nDEBUG: Found {len(all_inputs)} total input elements:")
            for i, inp in enumerate(all_inputs[:5]):
                try:
                    print(f"  {i+1}: {inp.get_attribute('outerHTML')[:100]}...")
                except:
                    print(f"  {i+1}: Error reading element")
        
        print("Test completed.")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_find_search_box()
