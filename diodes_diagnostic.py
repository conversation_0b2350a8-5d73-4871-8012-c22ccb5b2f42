#!/usr/bin/env python3
"""
Diodes Diagnostic - Check what's happening on the Diodes website
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def diagnose_diodes():
    print("🔍 DIODES WEBSITE DIAGNOSTIC")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("📱 Step 1: Loading Diodes website...")
        driver.get('https://www.diodes.com')
        time.sleep(10)
        
        print(f"✅ Page loaded")
        print(f"📄 Title: {driver.title}")
        print(f"🌐 URL: {driver.current_url}")
        
        # Check for cookie popups or overlays
        print(f"\n🍪 Checking for popups/overlays...")
        
        # Look for cookie popups
        cookie_elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Accept')] | //button[contains(text(), 'OK')] | //button[contains(text(), 'Continue')]")
        
        if cookie_elements:
            print(f"Found {len(cookie_elements)} potential cookie buttons:")
            for i, elem in enumerate(cookie_elements):
                try:
                    if elem.is_displayed():
                        text = elem.text.strip()
                        print(f"  {i+1}. '{text}'")
                        
                        if i == 0:  # Click first one
                            print(f"🎯 Clicking: '{text}'")
                            elem.click()
                            time.sleep(5)
                            break
                except:
                    continue
        else:
            print("No cookie popups found")
        
        # Look for overlays or modals
        overlays = driver.find_elements(By.CSS_SELECTOR, ".modal, .overlay, .popup, [class*='modal'], [class*='overlay']")
        
        if overlays:
            print(f"Found {len(overlays)} potential overlays")
            for overlay in overlays:
                try:
                    if overlay.is_displayed():
                        print(f"  Overlay found - trying to close")
                        
                        # Look for close buttons in overlay
                        close_buttons = overlay.find_elements(By.CSS_SELECTOR, "button, .close, [class*='close']")
                        for btn in close_buttons:
                            if btn.is_displayed():
                                btn.click()
                                time.sleep(3)
                                break
                except:
                    continue
        
        # Now look for search functionality
        print(f"\n🔍 Looking for search functionality...")
        
        # Check all input elements
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(all_inputs)} input elements:")
        
        search_candidates = []
        
        for i, inp in enumerate(all_inputs):
            try:
                if inp.is_displayed():
                    input_type = inp.get_attribute('type') or 'text'
                    placeholder = inp.get_attribute('placeholder') or ''
                    name = inp.get_attribute('name') or ''
                    id_attr = inp.get_attribute('id') or ''
                    
                    print(f"  {i+1}. Type: {input_type}, Placeholder: '{placeholder}', Name: '{name}', ID: '{id_attr}'")
                    
                    # Check if this looks like a search box
                    if any(word in placeholder.lower() for word in ['search', 'find', 'part']) or \
                       any(word in name.lower() for word in ['search', 'find', 'part']) or \
                       any(word in id_attr.lower() for word in ['search', 'find', 'part']):
                        search_candidates.append((i+1, inp, placeholder or name or id_attr))
            except:
                continue
        
        if search_candidates:
            print(f"\n🎯 Found {len(search_candidates)} search candidates:")
            for idx, elem, desc in search_candidates:
                print(f"  {idx}. {desc}")
            
            # Try the first search candidate
            print(f"\n🔍 Testing first search candidate...")
            try:
                first_search = search_candidates[0][1]
                first_search.clear()
                first_search.send_keys("APX803L20-30SA-7")
                print(f"✅ Entered part number")
                
                # Look for search button or press Enter
                from selenium.webdriver.common.keys import Keys
                first_search.send_keys(Keys.RETURN)
                time.sleep(8)
                
                print(f"📍 After search: {driver.current_url}")
                
                # Check if we got results
                if "APX803L20-30SA-7" in driver.page_source.upper():
                    print(f"✅ Part found in search results!")
                else:
                    print(f"❌ Part not found in results")
                
            except Exception as e:
                print(f"❌ Error testing search: {e}")
        else:
            print(f"❌ No search candidates found")
        
        # Check page source for any obvious issues
        print(f"\n🔍 Page analysis...")
        page_source = driver.page_source.lower()
        
        if "javascript" in page_source and "loading" in page_source:
            print(f"⚠️ Page may still be loading JavaScript content")
        
        if "error" in page_source:
            print(f"⚠️ Page contains error messages")
        
        if len(page_source) < 1000:
            print(f"⚠️ Page content seems very small ({len(page_source)} chars)")
        
        print(f"\n🔸 Keeping browser open for 2 minutes for manual inspection...")
        time.sleep(120)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    diagnose_diodes()
