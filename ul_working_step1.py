#!/usr/bin/env python3
"""
UltraLibrarian Step 1 - Open and type search
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options

def main():
    print("🔸 SCREEN 1: Opening UltraLibrarian and typing LM358N")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Load homepage
        print("1. Loading UltraLibrarian homepage...")
        driver.get("https://app.ultralibrarian.com")
        time.sleep(10)
        
        print(f"✅ Page loaded: {driver.title}")
        
        # Step 2: Find search box
        print("2. Finding search box...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        
        search_input = None
        for inp in inputs:
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower() or 'part' in placeholder.lower():
                        search_input = inp
                        print(f"✅ Found search box with placeholder: {placeholder}")
                        break
            except:
                continue
        
        if not search_input:
            print("❌ No search box found!")
            return
        
        # Step 3: Type LM358N
        print("3. Typing 'LM358N' in search box...")
        search_input.clear()
        search_input.send_keys("LM358N")
        print("✅ Typed 'LM358N'")
        
        # Step 4: Submit search
        print("4. Submitting search...")
        search_input.send_keys(Keys.RETURN)
        time.sleep(8)
        
        print("✅ Search submitted")
        print(f"✅ Current URL: {driver.current_url}")
        
        # Check what's on the page
        page_text = driver.page_source.lower()
        if 'lm358n' in page_text:
            print("✅ LM358N found in search results")
        else:
            print("❌ LM358N not found in results")
        
        print("\n🔸 SCREEN 1 COMPLETE")
        print("You should now see search results with LM358N parts")
        
        input("Press Enter to continue to Screen 2...")
        
        return driver
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    driver = main()
    if driver:
        input("Press Enter to close browser...")
        driver.quit()
