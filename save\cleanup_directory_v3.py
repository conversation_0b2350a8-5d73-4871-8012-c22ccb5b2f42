#!/usr/bin/env python3
"""
Directory cleanup script - removes unnecessary test and debug files
Keeps only essential working scripts
"""

import os
import glob

def cleanup_directory():
    print("🧹 DIRECTORY CLEANUP")
    print("=" * 50)
    print("Removing unnecessary test, debug, and experimental files...")
    print("Keeping only essential working scripts")
    
    # Files to DELETE (patterns)
    files_to_delete = [
        # Test files
        "test_*.py",
        "debug_*.py", 
        "try_*.py",
        
        # HTML response files
        "*.html",
        "digikey_search_response.html",
        "rs_components_*.html",
        "ti_test_*.html",
        "ultralibrarian_search_*.html",
        "samacsys_search_*.html",
        
        # JSON response files
        "*.json",
        "!digikey_api_credentials.json",  # Keep this one
        "!manufacturer_knowledge.json",   # Keep this one
        "!component_site_credentials.json", # Keep this one
        
        # Experimental/alternative approaches
        "advanced_*.py",
        "alternative_*.py",
        "bypass_*.py",
        "capture_*.py",
        "check_*.py",
        "comprehensive_*.py",
        "create_accounts.py",
        "demo_*.py",
        "dialog_*.py",
        "direct_*.py",
        "download_*.py",
        "exact_*.py",
        "extract_*.py",
        "final_*.py",
        "fix_*.py",
        "fixed_*.py",
        "focused_*.py",
        "get_*.py",
        "gui_layout_demo.py",
        "launch_gui.py",
        "learning_*.py",
        "open_*.py",
        "parse_*.py",
        "proper_*.py",
        "quick_*.py",
        "real_*.py",
        "search_*.py",
        "selenium_*.py",
        "simple_gui_test.py",
        "simple_ultralibrarian_test.py",
        "smart_*.py",
        "step1_*.py",
        "universal_*.py",
        "working_*.py",
        
        # Specific obsolete files
        "component_finder.py",
        "component_finder_enhanced.py", 
        "component_finder_gui1.py",
        "digikey_api_solution.py",
        "digikey_api_test.py",
        "digikey_proper_search.py",
        "digikey_searcher.py",
        "digikey_simple.py",
        "enhanced_digikey_search.py",  # We have simple version now
        "manufacturer_3d_searcher.py",
        "mouser_searcher.py",
        "ultralibrarian_complete_login.py",
        "ultralibrarian_fixed_login.py",
        "ultralibrarian_manual_login.py",
        "ultralibrarian_raw_test.py",
        
        # Specific 3D searchers (keep main ones)
        "samacsys_3d_searcher.py",
        "samacsys_focused_search.py",
        "snapeda_3d_searcher.py",
        "snapeda_direct_download.py",
        "snapeda_focused_search.py",
        "snapeda_step_download.py",
        "ultralibrarian_3d_searcher.py",
        
        # CSV and other data files
        "actual-web-site-xref.csv",
        "found-files-log.csv",
        "working_mouser_response.html",
        
        # Temp files
        "test_download.pdf",
        "~$*.xlsx"
    ]
    
    # Essential files to KEEP (override delete patterns)
    essential_files = [
        "simple_digikey_with_csv.py",
        "refresh_digikey_token.py", 
        "digikey_api_credentials.json",
        "ultralibrarian_scraper.py",
        "rs_components_scraper.py",
        "snapeda_api_test_ready.py",
        "mouser_api_test_ready.py",
        "component_finder_gui.py",
        "samacsys_scraper.py",
        "manufacturer_knowledge.json",
        "component_site_credentials.json",
        "README.md",
        "Teledyne_Flir_master-footprint_list.xlsx",
        "install.bat",
        "run_component_finder.bat",
        "test_ultralibrarian.bat"
    ]
    
    deleted_count = 0
    kept_count = 0
    
    # Get all Python files
    all_files = []
    for pattern in ["*.py", "*.html", "*.json", "*.csv", "*.pdf"]:
        all_files.extend(glob.glob(pattern))
    
    print(f"\nFound {len(all_files)} files to evaluate...")
    
    for file in all_files:
        # Skip if it's an essential file
        if file in essential_files:
            print(f"✅ KEEP: {file}")
            kept_count += 1
            continue
            
        # Check if file matches delete patterns
        should_delete = False
        for pattern in files_to_delete:
            if pattern.startswith("!"):
                # Skip exclusion patterns for now
                continue
            if "*" in pattern:
                import fnmatch
                if fnmatch.fnmatch(file, pattern):
                    should_delete = True
                    break
            else:
                if file == pattern:
                    should_delete = True
                    break
        
        if should_delete:
            try:
                os.remove(file)
                print(f"🗑️  DELETED: {file}")
                deleted_count += 1
            except Exception as e:
                print(f"❌ Could not delete {file}: {str(e)}")
        else:
            print(f"✅ KEEP: {file}")
            kept_count += 1
    
    # Clean up __pycache__ directory
    try:
        import shutil
        if os.path.exists("__pycache__"):
            shutil.rmtree("__pycache__")
            print(f"🗑️  DELETED: __pycache__ directory")
            deleted_count += 1
    except:
        pass
    
    # Clean up old search results
    try:
        if os.path.exists("search_results"):
            for file in os.listdir("search_results"):
                if file.endswith(".txt"):
                    os.remove(os.path.join("search_results", file))
                    print(f"🗑️  DELETED: search_results/{file}")
                    deleted_count += 1
    except:
        pass
    
    print(f"\n" + "=" * 50)
    print(f"📊 CLEANUP SUMMARY")
    print(f"=" * 50)
    print(f"Files deleted: {deleted_count}")
    print(f"Files kept: {kept_count}")
    print(f"\n✅ Directory cleanup complete!")
    print(f"Your directory now contains only essential working files.")

def show_remaining_files():
    """Show what files remain after cleanup"""
    print(f"\n📁 REMAINING FILES:")
    print("-" * 30)
    
    # Show Python files
    py_files = glob.glob("*.py")
    if py_files:
        print("🐍 Python Scripts:")
        for file in sorted(py_files):
            print(f"   • {file}")
    
    # Show config files
    config_files = glob.glob("*.json") + glob.glob("*.bat")
    if config_files:
        print("\n⚙️  Configuration Files:")
        for file in sorted(config_files):
            print(f"   • {file}")
    
    # Show data files
    data_files = glob.glob("*.xlsx") + glob.glob("*.md")
    if data_files:
        print("\n📊 Data Files:")
        for file in sorted(data_files):
            print(f"   • {file}")
    
    # Show directories
    dirs = [d for d in os.listdir(".") if os.path.isdir(d) and not d.startswith(".")]
    if dirs:
        print("\n📁 Directories:")
        for dir in sorted(dirs):
            print(f"   • {dir}/")

if __name__ == "__main__":
    print("🚀 DIRECTORY CLEANUP UTILITY")
    print("This will remove unnecessary test and debug files")
    print("=" * 60)
    
    response = input("Do you want to proceed with cleanup? (y/n): ")
    
    if response.lower() in ['y', 'yes']:
        cleanup_directory()
        show_remaining_files()
    else:
        print("Cleanup cancelled.")
