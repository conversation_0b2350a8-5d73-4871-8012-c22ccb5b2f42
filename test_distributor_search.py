#!/usr/bin/env python3
"""
Test script to verify distributor search functionality
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import quote
import re

def test_digikey_search(part_number):
    """Test Digi-Key search for a specific part"""
    print(f"🔍 Testing Digi-Key search for: {part_number}")
    
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # Search for the specific part number on Digi-Key
        search_url = f"https://www.digikey.com/en/products/filter?keywords={quote(part_number)}&ColumnSort=0&page=1&pageSize=25"
        print(f"   URL: {search_url}")
        
        response = session.get(search_url, timeout=15)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for the part in search results
            found_results = False
            for result_row in soup.find_all(['tr', 'div'], class_=re.compile(r'product|part|result', re.I)):
                row_text = result_row.get_text()
                if part_number.lower() in row_text.lower():
                    found_results = True
                    print(f"   ✅ Found result containing: {part_number}")
                    
                    # Look for datasheet links
                    for link in result_row.find_all('a', href=True):
                        href = link['href']
                        link_text = link.get_text().strip().lower()
                        
                        if any(keyword in link_text for keyword in ['datasheet', 'data sheet', 'pdf']):
                            print(f"   📄 Found datasheet link: {href}")
                        
                        # Look for manufacturer info
                        if href.startswith('http') and 'digikey.com' not in href:
                            print(f"   🏭 Found external link: {href}")
                    
                    # Look for manufacturer name in text
                    mfg_patterns = [
                        r'Manufacturer[:\s]+([^,\n\|]+)',
                        r'Mfg[:\s]+([^,\n\|]+)',
                        r'Brand[:\s]+([^,\n\|]+)',
                    ]
                    
                    for pattern in mfg_patterns:
                        match = re.search(pattern, row_text, re.I)
                        if match:
                            found_mfg = match.group(1).strip()
                            print(f"   🏭 Found manufacturer: {found_mfg}")
                            break
                    
                    break
            
            if not found_results:
                print(f"   ❌ No results found for {part_number}")
                # Print first few results to see what's available
                print("   Available results:")
                for i, result_row in enumerate(soup.find_all(['tr', 'div'], class_=re.compile(r'product|part|result', re.I))[:3]):
                    row_text = result_row.get_text()[:100]
                    print(f"   {i+1}. {row_text}...")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def test_mouser_search(part_number):
    """Test Mouser search for a specific part"""
    print(f"\n🔍 Testing Mouser search for: {part_number}")
    
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # Search for the specific part number on Mouser
        search_url = f"https://www.mouser.com/ProductDetail/?q={quote(part_number)}"
        print(f"   URL: {search_url}")
        
        response = session.get(search_url, timeout=15)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for datasheet links
            datasheet_found = False
            for link in soup.find_all('a', href=True):
                href = link['href']
                link_text = link.get_text().strip().lower()
                
                if any(keyword in link_text for keyword in ['datasheet', 'data sheet', 'pdf']):
                    print(f"   📄 Found datasheet link: {href}")
                    datasheet_found = True
                
                # Look for manufacturer website links
                if href.startswith('http') and 'mouser.com' not in href:
                    if any(word in href.lower() for word in ['wurth', 'we-online']):
                        print(f"   🏭 Found manufacturer link: {href}")
            
            if not datasheet_found:
                print(f"   ❌ No datasheet links found")
                
            # Look for manufacturer information
            page_text = soup.get_text()
            mfg_patterns = [
                r'Manufacturer[:\s]+([^,\n\|]+)',
                r'Brand[:\s]+([^,\n\|]+)',
                r'Mfr[:\s]+([^,\n\|]+)',
            ]
            
            for pattern in mfg_patterns:
                match = re.search(pattern, page_text, re.I)
                if match:
                    found_mfg = match.group(1).strip()
                    print(f"   🏭 Found manufacturer: {found_mfg}")
                    break
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🧪 DISTRIBUTOR SEARCH TEST")
    print("=" * 50)
    
    # Test with WURTH part number
    part_number = "435151014845"
    
    test_digikey_search(part_number)
    test_mouser_search(part_number)
    
    print("\n" + "=" * 50)
    print("🏁 Test complete!")
