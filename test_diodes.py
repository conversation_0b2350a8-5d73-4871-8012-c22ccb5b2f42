#!/usr/bin/env python3
"""
Working STEP File Downloader - Download 4 STEP files from alternative sources
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import time
import os
from pathlib import Path

def download_step_from_snapeda(part_number):
    """Download STEP file from SnapEDA"""
    print(f"🔍 SnapEDA search for {part_number}")

    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })

    try:
        # Search SnapEDA
        search_url = f"https://www.snapeda.com/search?q={part_number}"
        print(f"   URL: {search_url}")

        time.sleep(2)
        response = session.get(search_url, timeout=30)
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            if part_number.lower() in response.text.lower():
                print(f"   ✅ Part found!")

                # Look for direct STEP file links
                soup = BeautifulSoup(response.text, 'html.parser')
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    text = link.get_text().lower()

                    if '.step' in href.lower() or ('.step' in text and 'download' in text):
                        step_url = urljoin('https://www.snapeda.com', href)
                        print(f"   📥 Found STEP: {step_url}")

                        # Try to download
                        step_response = session.get(step_url, timeout=30)
                        if step_response.status_code == 200:
                            filename = f"SnapEDA_{part_number}.step"
                            with open(filename, 'wb') as f:
                                f.write(step_response.content)
                            print(f"   ✅ Downloaded: {filename}")
                            return filename

                print(f"   ❌ No direct STEP links found")
            else:
                print(f"   ❌ Part not found")
        else:
            print(f"   ❌ HTTP {response.status_code}")

    except Exception as e:
        print(f"   ❌ Error: {str(e)[:50]}")

    return None

def download_step_from_samacsys(part_number):
    """Download STEP file from SamacSys"""
    print(f"🔍 SamacSys search for {part_number}")

    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })

    try:
        # Search SamacSys Component Search Engine
        search_url = f"https://componentsearchengine.com/search?term={part_number}"
        print(f"   URL: {search_url}")

        time.sleep(2)
        response = session.get(search_url, timeout=30)
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            if part_number.lower() in response.text.lower():
                print(f"   ✅ Part found!")

                # Look for STEP download links
                soup = BeautifulSoup(response.text, 'html.parser')
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    text = link.get_text().lower()

                    if '.step' in href.lower() or ('step' in text and 'download' in text):
                        step_url = urljoin('https://componentsearchengine.com', href)
                        print(f"   📥 Found STEP: {step_url}")

                        # Try to download
                        step_response = session.get(step_url, timeout=30)
                        if step_response.status_code == 200:
                            filename = f"SamacSys_{part_number}.step"
                            with open(filename, 'wb') as f:
                                f.write(step_response.content)
                            print(f"   ✅ Downloaded: {filename}")
                            return filename

                print(f"   ❌ No STEP links found")
            else:
                print(f"   ❌ Part not found")
        else:
            print(f"   ❌ HTTP {response.status_code}")

    except Exception as e:
        print(f"   ❌ Error: {str(e)[:50]}")

    return None

def test_step_downloads():
    """Test downloading STEP files from alternative sources"""
    test_parts = [
        "APX803L20-30SA-7",    # Diodes Inc
        "20849-020E-01",       # I-PEX
        "C1005X5R1E105K050BC", # TDK
        "ERJ-1GNF1000C"        # Panasonic
    ]
    
    # Different URL patterns to try
    url_patterns = [
        f"https://www.diodes.com/part/view/{base_part}",
        f"https://www.diodes.com/products/catalog/part/{base_part}",
        f"https://www.diodes.com/products/{base_part}",
        f"https://www.diodes.com/part/{base_part}",
        f"https://www.diodes.com/assets/Datasheets/{base_part}.pdf",
        f"https://www.diodes.com/assets/Datasheets/{part_number}.pdf",
        # Try with full part number
        f"https://www.diodes.com/part/view/{part_number}",
        f"https://www.diodes.com/products/catalog/part/{part_number}",
        f"https://www.diodes.com/products/{part_number}",
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    print(f"🔍 Testing URLs for part: {part_number}")
    print("=" * 60)
    
    for i, url in enumerate(url_patterns, 1):
        try:
            print(f"{i:2d}. Testing: {url}")
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                print(f"    ✅ SUCCESS! ({len(response.text)} chars)")
                
                # Check if it looks like a valid page
                if len(response.text) > 1000:  # Reasonable page size
                    # Look for part number in content
                    if part_number.lower() in response.text.lower() or base_part.lower() in response.text.lower():
                        print(f"    🎯 Part number found in content!")
                        
                        # Look for package info
                        html_lower = response.text.lower()
                        packages = ['sot-23', 'sot23', 'sc-59', 'sc59', 'sot-323', 'sot323', 'sot-25', 'sot25']
                        found_packages = [pkg for pkg in packages if pkg in html_lower]
                        if found_packages:
                            print(f"    📦 Packages found: {found_packages}")
                        
                        # Look for PDF links
                        pdf_pattern = r'href="([^"]*\.pdf[^"]*)"'
                        pdf_matches = re.findall(pdf_pattern, response.text, re.IGNORECASE)
                        if pdf_matches:
                            print(f"    📄 Found {len(pdf_matches)} PDF links")
                            for j, link in enumerate(pdf_matches[:2]):  # Show first 2
                                print(f"       {j+1}. {link}")
                        
                        print(f"    🎉 This looks like the correct URL!")
                        return url, response.text
                    else:
                        print(f"    ⚠️ Part number not found in content")
                else:
                    print(f"    ⚠️ Page too small, might be error page")
            else:
                print(f"    ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        print()
    
    print("❌ No working URL found")
    return None, None

def test_direct_datasheet_search():
    """Try to find datasheet through search or direct links"""
    part_number = "APX803L20-30SA-7"
    
    # Try direct datasheet URLs
    datasheet_urls = [
        f"https://www.diodes.com/assets/Datasheets/{part_number}.pdf",
        f"https://www.diodes.com/assets/Datasheets/{part_number.split('-')[0]}.pdf",
        f"https://www.diodes.com/assets/Datasheets/APX803.pdf",
        f"https://www.diodes.com/assets/Datasheets/APX803L.pdf",
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    print(f"🔍 Testing direct datasheet URLs for: {part_number}")
    print("=" * 60)
    
    for i, url in enumerate(datasheet_urls, 1):
        try:
            print(f"{i}. Testing: {url}")
            response = requests.head(url, headers=headers, timeout=10)  # Use HEAD to avoid downloading
            
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '')
                content_length = response.headers.get('Content-Length', 'Unknown')
                print(f"   ✅ SUCCESS! Type: {content_type}, Size: {content_length}")
                return url
            else:
                print(f"   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return None

if __name__ == "__main__":
    print("🚀 Diodes Inc URL Testing")
    print("=" * 60)
    
    # Test different URL patterns
    working_url, html_content = test_diodes_urls()
    
    if working_url:
        print(f"\n🎉 Found working URL: {working_url}")
    else:
        print(f"\n⚠️ No working page URL found, trying direct datasheet search...")
        datasheet_url = test_direct_datasheet_search()
        if datasheet_url:
            print(f"🎉 Found direct datasheet: {datasheet_url}")
        else:
            print("❌ No datasheet found either")
