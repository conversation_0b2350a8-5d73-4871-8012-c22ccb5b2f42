#!/usr/bin/env python3
"""
Advanced 3D Model Finder - Multiple Search Strategies

This handles the challenge that 3D model files can have any name by using:
1. Package-type based searching (SOT23.step, sot23.stp, etc.)
2. Part number based searching (APX803L.step, APX803L20.step, etc.)
3. Directory crawling to find all STEP/STP files
4. Web search for 3D models
5. Product page analysis for CAD download links
6. Common naming pattern attempts

Usage:
    python advanced_3d_finder.py "Diodes Inc" "APX803L20-30SA-7"
"""

import requests
from bs4 import BeautifulSoup
import re
import os
import json
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Advanced3DFinder:
    def __init__(self, download_dir="files-download"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = self._create_session()
    
    def _create_session(self):
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        return session
    
    def search_3d_models_comprehensive(self, manufacturer, part_number, package_type=None):
        """Comprehensive 3D model search using multiple strategies"""
        logger.info(f"🎯 Starting comprehensive 3D model search")
        logger.info(f"   Manufacturer: {manufacturer}")
        logger.info(f"   Part Number: {part_number}")
        logger.info(f"   Package Type: {package_type or 'Unknown'}")
        
        all_found_models = []
        
        # Strategy 1: Package-based filename search
        if package_type:
            logger.info("🔍 Strategy 1: Package-based filename search")
            models = self._search_by_package_name(manufacturer, package_type)
            all_found_models.extend(models)
        
        # Strategy 2: Part number-based filename search
        logger.info("🔍 Strategy 2: Part number-based filename search")
        models = self._search_by_part_number(manufacturer, part_number)
        all_found_models.extend(models)
        
        # Strategy 3: Directory crawling for STEP/STP files
        logger.info("🔍 Strategy 3: Directory crawling")
        models = self._crawl_common_directories(manufacturer, part_number, package_type)
        all_found_models.extend(models)
        
        # Strategy 4: Product page CAD link analysis
        logger.info("🔍 Strategy 4: Product page CAD link analysis")
        models = self._analyze_product_page_for_cad_links(manufacturer, part_number)
        all_found_models.extend(models)
        
        # Strategy 5: Web search for 3D models
        logger.info("🔍 Strategy 5: Web search")
        models = self._web_search_3d_models(manufacturer, part_number, package_type)
        all_found_models.extend(models)
        
        # Remove duplicates
        unique_models = list(dict.fromkeys(all_found_models))
        
        logger.info(f"🎉 Found {len(unique_models)} unique 3D model(s)")
        return unique_models
    
    def _search_by_package_name(self, manufacturer, package_type):
        """Search for 3D models using package type as filename"""
        models = []
        base_url = self._get_manufacturer_base_url(manufacturer)
        
        if not base_url:
            return models
        
        # Generate package name variations
        package_variations = [
            package_type,                           # SOT-23
            package_type.replace('-', ''),          # SOT23
            package_type.replace('-', '_'),         # SOT_23
            package_type.lower(),                   # sot-23
            package_type.lower().replace('-', ''), # sot23
            package_type.upper(),                   # SOT-23
            package_type.upper().replace('-', ''), # SOT23
        ]
        
        # Common directory patterns where 3D models are stored
        common_3d_dirs = [
            "assets/3d-models", "assets/3D-models", "assets/cad-models", "assets/CAD-models",
            "assets/step-models", "assets/STEP-models", "assets/packages", "assets/package-models",
            "downloads/3d", "downloads/cad", "downloads/models", "downloads/step",
            "cad", "3d", "models", "step", "packages", "library", "cad-library",
            "support/3d", "support/cad", "design/3d", "design/cad", "files/3d", "files/cad"
        ]
        
        # File extensions to try
        extensions = ['.step', '.stp', '.STEP', '.STP']
        
        for directory in common_3d_dirs:
            for pkg_var in package_variations:
                for ext in extensions:
                    try:
                        url = f"{base_url}/{directory}/{pkg_var}{ext}"
                        logger.info(f"      Testing: {url}")
                        
                        response = self.session.head(url, timeout=5)
                        if response.status_code == 200:
                            logger.info(f"✅ Found package-based 3D model: {url}")
                            models.append(url)
                            
                    except Exception as e:
                        logger.debug(f"Package-based search failed: {e}")
                        continue
        
        return models
    
    def _search_by_part_number(self, manufacturer, part_number):
        """Search for 3D models using part number variations as filename"""
        models = []
        base_url = self._get_manufacturer_base_url(manufacturer)
        
        if not base_url:
            return models
        
        # Generate part number variations
        base_part = part_number.split('-')[0]
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
        
        part_variations = [
            part_number,                    # APX803L20-30SA-7
            part_number.replace('-', ''),   # APX803L2030SA7
            part_number.replace('-', '_'),  # APX803L20_30SA_7
            base_part,                      # APX803L20
            part_family,                    # APX803L
            part_number.lower(),
            part_number.upper(),
            base_part.lower(),
            base_part.upper(),
            part_family.lower(),
            part_family.upper()
        ]
        
        # Common directories
        common_3d_dirs = [
            "assets/3d-models", "assets/cad-models", "assets/step-models", "assets/packages",
            "downloads/3d", "downloads/cad", "cad", "3d", "models", "step"
        ]
        
        extensions = ['.step', '.stp', '.STEP', '.STP']
        
        for directory in common_3d_dirs:
            for part_var in part_variations:
                for ext in extensions:
                    try:
                        url = f"{base_url}/{directory}/{part_var}{ext}"
                        logger.info(f"      Testing: {url}")
                        
                        response = self.session.head(url, timeout=5)
                        if response.status_code == 200:
                            logger.info(f"✅ Found part-based 3D model: {url}")
                            models.append(url)
                            
                    except Exception as e:
                        logger.debug(f"Part-based search failed: {e}")
                        continue
        
        return models
    
    def _crawl_common_directories(self, manufacturer, part_number, package_type):
        """Crawl common directories to find any STEP/STP files"""
        models = []
        base_url = self._get_manufacturer_base_url(manufacturer)
        
        if not base_url:
            return models
        
        # Directories that commonly contain 3D models
        directories_to_crawl = [
            "assets/3d-models", "assets/cad-models", "assets/packages",
            "downloads/3d", "downloads/cad", "cad", "3d", "models"
        ]
        
        for directory in directories_to_crawl:
            try:
                dir_url = f"{base_url}/{directory}/"
                logger.info(f"   Crawling directory: {dir_url}")
                
                response = self.session.get(dir_url, timeout=15)
                if response.status_code == 200:
                    # Look for STEP/STP file links in the directory listing
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        if any(ext in href.lower() for ext in ['.step', '.stp']):
                            full_url = urljoin(dir_url, href)
                            logger.info(f"✅ Found 3D model in directory: {full_url}")
                            models.append(full_url)
                            
            except Exception as e:
                logger.debug(f"Directory crawling failed for {directory}: {e}")
                continue
        
        return models
    
    def _analyze_product_page_for_cad_links(self, manufacturer, part_number):
        """Analyze product page for CAD download links"""
        models = []
        
        if manufacturer.lower() == "diodes inc":
            # Get Diodes product page
            base_part = part_number.split('-')[0]
            part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
            
            product_url = f"https://www.diodes.com/part/view/{part_family}"
            
            try:
                logger.info(f"   Analyzing product page: {product_url}")
                response = self.session.get(product_url, timeout=30)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Look for any links containing CAD, 3D, STEP, or STP
                    cad_keywords = ['cad', '3d', 'step', 'stp', 'model']
                    
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        text = link.get_text().lower()
                        
                        # Check if link or text contains CAD-related keywords
                        if (any(keyword in href.lower() for keyword in cad_keywords) or
                            any(keyword in text for keyword in cad_keywords)):
                            
                            if any(ext in href.lower() for ext in ['.step', '.stp']):
                                full_url = urljoin(product_url, href)
                                logger.info(f"✅ Found CAD link in product page: {full_url}")
                                models.append(full_url)
                    
                    # Also look for JavaScript-based download links
                    # Search for data attributes or onclick handlers that might contain 3D model URLs
                    for element in soup.find_all(attrs={"data-url": True}):
                        data_url = element.get('data-url', '')
                        if any(ext in data_url.lower() for ext in ['.step', '.stp']):
                            full_url = urljoin(product_url, data_url)
                            logger.info(f"✅ Found data-url 3D model: {full_url}")
                            models.append(full_url)
                            
            except Exception as e:
                logger.error(f"Product page analysis failed: {e}")
        
        return models
    
    def _web_search_3d_models(self, manufacturer, part_number, package_type):
        """Use web search to find 3D models with various search terms"""
        models = []
        
        # This would integrate with web search API
        # Search terms to try:
        search_terms = []
        
        if package_type:
            search_terms.extend([
                f'"{package_type}" 3D model STEP download',
                f'"{package_type}" CAD model {manufacturer}',
                f'site:{self._get_manufacturer_domain(manufacturer)} {package_type} STEP',
                f'site:{self._get_manufacturer_domain(manufacturer)} {package_type} 3D'
            ])
        
        search_terms.extend([
            f'"{part_number}" 3D model STEP',
            f'"{part_number}" CAD model download',
            f'site:{self._get_manufacturer_domain(manufacturer)} "{part_number}" STEP',
            f'{manufacturer} {part_number} 3D model download'
        ])
        
        logger.info(f"   Would search with {len(search_terms)} different terms")
        # In a full implementation, you'd use web search API here
        
        return models
    
    def _get_manufacturer_base_url(self, manufacturer):
        """Get base URL for manufacturer"""
        urls = {
            "diodes inc": "https://www.diodes.com",
            "texas instruments": "https://www.ti.com",
            "analog devices": "https://www.analog.com",
            "microchip": "https://www.microchip.com",
            "infineon": "https://www.infineon.com",
            "stmicroelectronics": "https://www.st.com",
            "nxp": "https://www.nxp.com"
        }
        return urls.get(manufacturer.lower())
    
    def _get_manufacturer_domain(self, manufacturer):
        """Get domain for manufacturer for web search"""
        base_url = self._get_manufacturer_base_url(manufacturer)
        if base_url:
            return urlparse(base_url).netloc
        return ""
    
    def download_file(self, url, filename=None):
        """Download file with proper handling"""
        try:
            logger.info(f"📥 Downloading: {url}")
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            if not filename:
                filename = os.path.basename(urlparse(url).path)
                if not filename or '.' not in filename:
                    if any(ext in url.lower() for ext in ['.step', '.stp']):
                        filename = f"3d_model_{int(time.time())}.step"
                    else:
                        filename = f"download_{int(time.time())}"
            
            filepath = self.download_dir / filename
            
            # Handle existing files
            counter = 1
            original_filepath = filepath
            while filepath.exists():
                name, ext = original_filepath.stem, original_filepath.suffix
                filepath = self.download_dir / f"{name}_{counter}{ext}"
                counter += 1
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            logger.info(f"✅ Downloaded: {filepath} ({file_size:,} bytes)")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            return None
    
    def find_all_3d_models(self, manufacturer, part_number, package_type=None):
        """Find all possible 3D models using comprehensive search"""
        
        # First, let's try to get the package type if not provided
        if not package_type:
            package_type = self._extract_package_type_smart(manufacturer, part_number)
        
        # Search for 3D models
        model_urls = self.search_3d_models_comprehensive(manufacturer, part_number, package_type)
        
        # Download all found models
        downloaded_models = []
        for i, url in enumerate(model_urls):
            filename = f"{package_type or 'unknown'}_{i+1}_3d_model.step"
            model_file = self.download_file(url, filename)
            if model_file:
                downloaded_models.append(model_file)
        
        return downloaded_models, package_type
    
    def _extract_package_type_smart(self, manufacturer, part_number):
        """Smart package type extraction"""
        
        # Method 1: Known mappings for common parts
        known_packages = {
            "diodes inc": {
                "APX803L": "SOT23",  # Family mapping
                "APX803L20-30SA-7": "SOT23"  # Specific part mapping
            }
        }
        
        manufacturer_lower = manufacturer.lower()
        if manufacturer_lower in known_packages:
            mapping = known_packages[manufacturer_lower]
            
            # Try exact match
            if part_number in mapping:
                logger.info(f"📦 Package from known mapping: {mapping[part_number]}")
                return mapping[part_number]
            
            # Try family match
            base_part = part_number.split('-')[0]
            part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
            if part_family in mapping:
                logger.info(f"📦 Package from family mapping: {mapping[part_family]}")
                return mapping[part_family]
        
        # Method 2: Try to get from product page
        if manufacturer_lower == "diodes inc":
            try:
                base_part = part_number.split('-')[0]
                part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
                
                url = f"https://www.diodes.com/part/view/{part_family}"
                logger.info(f"📦 Getting package from product page: {url}")
                
                response = self.session.get(url, timeout=20)
                if response.status_code == 200:
                    html_lower = response.text.lower()
                    
                    # Look for package types in the HTML
                    packages = {
                        'SOT23': ['sot-23', 'sot23'],
                        'SOT323': ['sot-323', 'sot323'],
                        'SOT25': ['sot-25', 'sot25'],
                        'SC59': ['sc-59', 'sc59'],
                        'SC70': ['sc-70', 'sc70']
                    }
                    
                    for pkg_name, patterns in packages.items():
                        for pattern in patterns:
                            if pattern in html_lower:
                                logger.info(f"📦 Package from product page: {pkg_name}")
                                return pkg_name
                                
            except Exception as e:
                logger.error(f"Error extracting package from product page: {e}")
        
        return None

def test_comprehensive_search():
    """Test the comprehensive 3D model search"""
    finder = Advanced3DFinder()
    
    # Test with known part
    manufacturer = "Diodes Inc"
    part_number = "APX803L20-30SA-7"
    
    print(f"🚀 Testing comprehensive 3D model search")
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print("=" * 60)
    
    models, package_type = finder.find_all_3d_models(manufacturer, part_number)
    
    print("\n" + "="*60)
    print("COMPREHENSIVE SEARCH RESULTS")
    print("="*60)
    print(f"Package Type: {package_type or 'Unknown'}")
    print(f"3D Models Found: {len(models)}")
    
    for i, model in enumerate(models, 1):
        print(f"  {i}. {model}")
    
    if models:
        print("\n🎉 3D model search successful!")
    else:
        print("\n⚠️ No 3D models found")
    
    return models, package_type

def main():
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description='Advanced 3D Model Finder')
    parser.add_argument('manufacturer', help='Manufacturer name')
    parser.add_argument('part_number', help='Part number')
    parser.add_argument('--package-type', help='Package type (if known)')
    
    args = parser.parse_args()
    
    finder = Advanced3DFinder()
    models, package_type = finder.find_all_3d_models(
        args.manufacturer, 
        args.part_number, 
        args.package_type
    )
    
    print("\n" + "="*60)
    print("ADVANCED 3D FINDER RESULTS")
    print("="*60)
    print(f"Manufacturer: {args.manufacturer}")
    print(f"Part Number: {args.part_number}")
    print(f"Package Type: {package_type or 'Unknown'}")
    print(f"3D Models: {len(models)} found")
    
    for model in models:
        print(f"  - {model}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        test_comprehensive_search()
    else:
        main()
