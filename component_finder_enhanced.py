#!/usr/bin/env python3
"""
Enhanced Component Finder with Learning System
Searches Digi-Key, Mouser, and manufacturer websites for datasheets and 3D models
Learns and remembers successful search patterns for faster future searches
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
from bs4 import BeautifulSoup
import json
import csv
import os
import re
import time
import threading
from pathlib import Path
from urllib.parse import urljoin, urlparse
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComponentFinderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced Component Finder - Learning System")
        self.root.geometry("1000x700")
        
        # Create directories
        self.datasheet_dir = Path("datasheets")
        self.model_3d_dir = Path("3d")
        self.datasheet_dir.mkdir(exist_ok=True)
        self.model_3d_dir.mkdir(exist_ok=True)
        
        # Initialize session and knowledge base
        self.session = self._create_session()
        self.knowledge_base_file = Path("manufacturer_knowledge.json")
        self.knowledge_base = self._load_knowledge_base()
        
        # Initialize CSV files
        self.csv_file = Path("actual-web-site-xref.csv")
        self.manufacturer_websites = self._load_manufacturer_csv()
        
        # Initialize CSV file for tracking found files
        self.found_files_csv = Path("found-files-log.csv")
        self._initialize_found_files_csv()
        
        self.setup_gui()
        
        # Add WURTH patterns after GUI is ready
        self._add_wurth_patterns()
        
        # Add initial message
        self.add_comment("🚀 Enhanced Component Finder Started")
        self.add_comment(f"📁 Working directory: {os.getcwd()}")
        self.add_comment("📁 Created directories: datasheets/ and 3d/")
        self.add_comment("📚 Loaded knowledge base with learning system")
        self.add_comment(f"📋 Loaded {len(self.manufacturer_websites)} manufacturer websites")
        self.add_comment("Ready to search and learn!")

    def _create_session(self):
        """Create requests session with proper headers"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        return session

    def _load_knowledge_base(self):
        """Load manufacturer knowledge base"""
        try:
            if self.knowledge_base_file.exists():
                with open(self.knowledge_base_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading knowledge base: {e}")
        return {}

    def _save_knowledge_base(self):
        """Save knowledge base to file"""
        try:
            with open(self.knowledge_base_file, 'w') as f:
                json.dump(self.knowledge_base, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving knowledge base: {e}")

    def _add_wurth_patterns(self):
        """Add WURTH/Würth Elektronik patterns to knowledge base"""
        wurth_key = "wurth"
        
        # Always update with the correct search URL we discovered
        self.knowledge_base[wurth_key] = {
            "name": "Würth Elektronik",
            "base_url": "https://www.we-online.com",
            "search_url_format": "https://www.we-online.com/en/components/products?sq={part_number}",
            "search_patterns": {
                "successful_urls": [
                    "/en/components/products?sq={part_number}",
                    "/catalog/en/products/{part_number}",
                    "/catalog/products/{part_number}",
                    "/en/products/{part_number}",
                    "/products/{part_number}"
                ],
                "search_methods": ["simple_search", "direct_url", "catalog_search"],
                "datasheet_patterns": [
                    r'href="([^"]*\.pdf[^"]*)"[^>]*>.*?datasheet',
                    r'href="([^"]*datasheet[^"]*\.pdf)"',
                    r'href="([^"]*catalog[^"]*\.pdf)"',
                    r'href="([^"]*media[^"]*\.pdf)"'
                ],
                "model_3d_patterns": [
                    r'href="([^"]*\.step)"',
                    r'href="([^"]*\.stp)"',
                    r'href="([^"]*3d[^"]*\.step)"',
                    r'href="([^"]*3d[^"]*\.stp)"'
                ]
            },
            "package_detection": {
                "patterns": [
                    r'package[:\s]*([a-z0-9\-]+)',
                    r'footprint[:\s]*([a-z0-9\-]+)',
                    r'housing[:\s]*([a-z0-9\-]+)'
                ]
            },
            "known_parts": {
                # Will be populated as we find parts
            },
            "last_successful_search": "https://www.we-online.com/en/components/products?sq=435151014845",
            "last_updated": "2025-01-23"
        }
        self._save_knowledge_base()
        self.add_comment("📚 Updated WURTH search patterns in knowledge base")

    def _load_manufacturer_csv(self):
        """Load manufacturer websites from CSV"""
        websites = {}
        try:
            if self.csv_file.exists():
                with open(self.csv_file, 'r', newline='', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        if row.get('Manufacturer Name') and row.get('Website'):
                            websites[row['Manufacturer Name'].lower().strip()] = row['Website'].strip()
        except Exception as e:
            logger.error(f"Error loading manufacturer CSV: {e}")
        return websites

    def _save_manufacturer_to_csv(self, manufacturer, website):
        """Save manufacturer website to CSV"""
        try:
            # Update in memory
            self.manufacturer_websites[manufacturer.lower().strip()] = website
            
            # Write to file
            with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['Manufacturer Name', 'Website'])
                for name, url in self.manufacturer_websites.items():
                    writer.writerow([name.title(), url])
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")

    def _initialize_found_files_csv(self):
        """Initialize CSV file for tracking found files"""
        try:
            if not self.found_files_csv.exists():
                with open(self.found_files_csv, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow([
                        'Manufacturer Name', 'Part Number', 'Datasheet URL', 'Datasheet Filename',
                        '3D Model URL', '3D Model Filename', 'Package Type', 'Date Found', 'Search Success'
                    ])
        except Exception as e:
            logger.error(f"Error initializing found files CSV: {e}")

    def setup_gui(self):
        """Setup the GUI layout"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # Input section
        ttk.Label(main_frame, text="Manufacturer:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.manufacturer_var = tk.StringVar(value="WURTH")
        manufacturer_entry = ttk.Entry(main_frame, textvariable=self.manufacturer_var, width=30)
        manufacturer_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        
        ttk.Label(main_frame, text="Part Number:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.part_number_var = tk.StringVar(value="435151014845")
        part_entry = ttk.Entry(main_frame, textvariable=self.part_number_var, width=30)
        part_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        
        # Search button
        self.search_button = ttk.Button(main_frame, text="🔍 Search Component", command=self.on_search_click)
        self.search_button.grid(row=2, column=0, columnspan=2, pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Results area
        results_frame = ttk.LabelFrame(main_frame, text="Search Results", padding="5")
        results_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=20, wrap=tk.WORD)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Status section
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="Status:").grid(row=0, column=0, sticky=tk.W)
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.grid(row=0, column=1, sticky=tk.W, padx=(5, 0))

    def add_comment(self, message):
        """Add timestamped comment to results"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.results_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def on_search_click(self):
        """Handle search button click"""
        manufacturer = self.manufacturer_var.get().strip()
        part_number = self.part_number_var.get().strip()
        
        if not manufacturer or not part_number:
            messagebox.showerror("Error", "Please enter both manufacturer and part number")
            return
        
        # Disable search button and start progress
        self.search_button.config(state='disabled')
        self.progress.start()
        self.status_var.set("Searching...")
        
        # Start search in separate thread
        search_thread = threading.Thread(target=self.search_component, args=(manufacturer, part_number))
        search_thread.daemon = True
        search_thread.start()

    def search_component(self, manufacturer, part_number):
        """Main search method - searches distributors first, then manufacturer website"""
        try:
            self.add_comment(f"🔍 Starting search for {manufacturer} {part_number}")

            # Step 1: Search distributors first (Digi-Key, Mouser)
            self.add_comment(f"📊 Checking distributors first...")
            distributor_info = self.search_distributors_for_part_info(manufacturer, part_number)

            if distributor_info:
                verified_manufacturer, website, datasheet_url = distributor_info
                self.add_comment(f"✅ Distributor verified: {verified_manufacturer} → {website}")

                # Download datasheet if found
                if datasheet_url:
                    self.add_comment(f"📄 Found direct datasheet link: {datasheet_url}")
                    self.download_file_from_url(datasheet_url, verified_manufacturer, part_number, "datasheet")

                # Update manufacturer info
                manufacturer = verified_manufacturer
                self._save_manufacturer_to_csv(verified_manufacturer, website)

                # Step 2: Search manufacturer website for STEP files
                self.add_comment(f"🌐 Using verified website: {website}")

                # For WURTH, use simple search method
                if 'wurth' in manufacturer.lower() or 'würth' in manufacturer.lower():
                    self.add_comment(f"🔍 Trying simple WURTH website search for {manufacturer}...")
                    self.search_wurth_simple(manufacturer, part_number)
                else:
                    self.add_comment(f"🔍 Using general search for {manufacturer}...")
                    # Add other manufacturer search methods here

            else:
                self.add_comment(f"❌ Part not found on distributors")

        except Exception as e:
            self.add_comment(f"❌ Search error: {str(e)}")

        finally:
            # Re-enable search button and stop progress
            self.search_button.config(state='normal')
            self.progress.stop()
            self.status_var.set("Search completed")

    def search_distributors_for_part_info(self, manufacturer, part_number):
        """Search Digi-Key and Mouser for part information"""
        # Try Digi-Key first
        digikey_result = self.search_digikey_simple(manufacturer, part_number)
        if digikey_result:
            return digikey_result

        # Try Mouser if Digi-Key fails
        mouser_result = self.search_mouser_simple(manufacturer, part_number)
        if mouser_result:
            return mouser_result

        return None

    def search_digikey_simple(self, manufacturer, part_number):
        """Search Digi-Key for part information"""
        try:
            self.add_comment(f"🔍 Searching Digi-Key for {part_number}...")

            search_url = f"https://www.digikey.com/en/products/result?keywords={part_number}"

            time.sleep(1)  # Be respectful
            response = self.session.get(search_url, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()

                if part_number.lower() in page_text:
                    self.add_comment(f"✅ Found {part_number} on Digi-Key")

                    # Look for manufacturer name
                    found_manufacturer = None
                    if manufacturer.lower() in page_text or 'wurth' in page_text or 'würth' in page_text:
                        found_manufacturer = "Würth Elektronik"
                        self.add_comment(f"✅ Found WURTH manufacturer")

                    # Look for datasheet links (only datasheet, not STEP files)
                    datasheet_url = None
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        link_text = link.get_text().lower()

                        # Look for datasheet links
                        if any(keyword in link_text for keyword in ['datasheet', 'pdf', 'data sheet']):
                            if href.startswith('http'):
                                datasheet_url = href
                            elif href.startswith('/'):
                                datasheet_url = urljoin('https://www.digikey.com', href)

                            if datasheet_url:
                                self.add_comment(f"📄 Found datasheet: {datasheet_url[:60]}...")
                                break

                    if found_manufacturer and datasheet_url:
                        return (found_manufacturer, "https://www.we-online.com", datasheet_url)

        except Exception as e:
            self.add_comment(f"⚠️ Digi-Key search failed: {str(e)[:50]}")

        return None

    def search_mouser_simple(self, manufacturer, part_number):
        """Search Mouser for part information"""
        try:
            self.add_comment(f"🔍 Searching Mouser for {part_number}...")

            search_url = f"https://www.mouser.com/ProductDetail/{part_number}"

            time.sleep(1)  # Be respectful
            response = self.session.get(search_url, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()

                if part_number.lower() in page_text:
                    self.add_comment(f"✅ Found {part_number} on Mouser")

                    # Look for manufacturer and datasheet
                    found_manufacturer = None
                    if manufacturer.lower() in page_text or 'wurth' in page_text or 'würth' in page_text:
                        found_manufacturer = "Würth Elektronik"
                        self.add_comment(f"✅ Found WURTH manufacturer")

                    # Look for datasheet links (only datasheet, not STEP files)
                    datasheet_url = None
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        link_text = link.get_text().lower()

                        if any(keyword in link_text for keyword in ['datasheet', 'pdf', 'data sheet']):
                            if href.startswith('http'):
                                datasheet_url = href
                            elif href.startswith('/'):
                                datasheet_url = urljoin('https://www.mouser.com', href)

                            if datasheet_url:
                                self.add_comment(f"📄 Found datasheet: {datasheet_url[:60]}...")
                                break

                    if found_manufacturer and datasheet_url:
                        return (found_manufacturer, "https://www.we-online.com", datasheet_url)

        except Exception as e:
            self.add_comment(f"⚠️ Mouser search failed: {str(e)[:50]}")

        return None

    def search_wurth_simple(self, manufacturer, part_number):
        """Search WURTH website for STEP files with learning system"""
        try:
            # First check if we already know this part
            if self.check_known_wurth_parts(part_number):
                return True

            self.add_comment(f"🔍 Searching WURTH website for {part_number}...")

            # WURTH website search URL (correct format)
            search_url = f"https://www.we-online.com/en/components/products?sq={part_number}"

            time.sleep(1)

            self.add_comment(f"   Opening: {search_url}")
            response = self.session.get(search_url, timeout=30, allow_redirects=True)

            if response.status_code == 200:
                # Check if we were redirected to a product page
                final_url = response.url
                if final_url != search_url:
                    self.add_comment(f"   Redirected to: {final_url[:60]}...")

                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()

                # Check if our part number appears on the page (or in the URL)
                if part_number.lower() in page_text or part_number.lower() in final_url.lower():
                    self.add_comment(f"✅ Found {part_number} on WURTH website")

                    # Look for STEP file on this page
                    step_file_url = None
                    datasheet_url = None

                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        link_text = link.get_text().lower()

                        # Make URL absolute
                        if href.startswith('http'):
                            full_url = href
                        elif href.startswith('/'):
                            full_url = urljoin('https://www.we-online.com', href)
                        else:
                            continue

                        # Look for STEP files
                        if (any(keyword in link_text for keyword in ['step', '3d', 'cad', 'model', 'download']) or
                            any(ext in full_url.lower() for ext in ['.step', '.stp'])):
                            step_file_url = full_url
                            self.add_comment(f"🔧 Found STEP file: {step_file_url[:60]}...")
                            break

                        # Also look for additional datasheet
                        if any(keyword in link_text for keyword in ['datasheet', 'pdf', 'data sheet']):
                            datasheet_url = full_url
                            self.add_comment(f"📄 Found additional datasheet: {datasheet_url[:60]}...")

                    # Also scan for direct STEP file links in HTML
                    if not step_file_url:
                        for element in soup.find_all(['a', 'link'], href=True):
                            href = element.get('href', '')
                            if any(ext in href.lower() for ext in ['.step', '.stp']):
                                if href.startswith('http'):
                                    step_file_url = href
                                elif href.startswith('/'):
                                    step_file_url = urljoin('https://www.we-online.com', href)

                                if step_file_url:
                                    self.add_comment(f"🔧 Found direct STEP link: {step_file_url[:60]}...")
                                    break

                    # Download files
                    files_found = False
                    if datasheet_url:
                        self.download_file_from_url(datasheet_url, "Würth Elektronik", part_number, "datasheet")
                        files_found = True

                    if step_file_url:
                        self.add_comment(f"📥 Downloading STEP file from WURTH...")
                        self.download_file_from_url(step_file_url, "Würth Elektronik", part_number, "3d_model")
                        files_found = True

                        # Save this part info for next time
                        self.save_wurth_part_info(part_number, datasheet_url, step_file_url)

                    if files_found:
                        return True
                    else:
                        self.add_comment(f"❌ No STEP file found on WURTH page")
                        return False

                else:
                    self.add_comment(f"❌ Part not found on WURTH website")
                    return False

            else:
                self.add_comment(f"❌ HTTP {response.status_code}")
                return False

        except Exception as e:
            self.add_comment(f"⚠️ WURTH search failed: {str(e)[:50]}")
            return False

    def check_known_wurth_parts(self, part_number):
        """Check if we already know where to find this WURTH part's files"""
        try:
            wurth_key = "wurth"

            if (wurth_key in self.knowledge_base and
                "known_parts" in self.knowledge_base[wurth_key] and
                part_number in self.knowledge_base[wurth_key]["known_parts"]):

                part_info = self.knowledge_base[wurth_key]["known_parts"][part_number]
                self.add_comment(f"✅ Found {part_number} in saved WURTH parts!")

                # Download known files directly
                files_downloaded = 0

                if "datasheet" in part_info:
                    datasheet_url = part_info["datasheet"]
                    self.add_comment(f"📄 Using saved datasheet: {datasheet_url[:60]}...")
                    self.download_file_from_url(datasheet_url, "Würth Elektronik", part_number, "datasheet")
                    files_downloaded += 1

                if "step_file" in part_info:
                    step_url = part_info["step_file"]
                    self.add_comment(f"🔧 Using saved STEP file: {step_url[:60]}...")
                    self.download_file_from_url(step_url, "Würth Elektronik", part_number, "3d_model")
                    files_downloaded += 1

                if files_downloaded > 0:
                    self.add_comment(f"✅ Downloaded {files_downloaded} files using saved URLs")
                    return True

            return False

        except Exception as e:
            self.add_comment(f"⚠️ Error checking known parts: {str(e)[:50]}")
            return False

    def save_wurth_part_info(self, part_number, datasheet_url, step_file_url):
        """Save WURTH part information for future quick access"""
        try:
            wurth_key = "wurth"

            # Ensure WURTH entry exists
            if wurth_key not in self.knowledge_base:
                self.knowledge_base[wurth_key] = {}

            if "known_parts" not in self.knowledge_base[wurth_key]:
                self.knowledge_base[wurth_key]["known_parts"] = {}

            # Build part info
            part_info = {}
            if datasheet_url:
                part_info["datasheet"] = datasheet_url
            if step_file_url:
                part_info["step_file"] = step_file_url

            # Add timestamp
            part_info["found_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Save the part info
            self.knowledge_base[wurth_key]["known_parts"][part_number] = part_info

            # Save to file
            self._save_knowledge_base()

            self.add_comment(f"💾 Saved {part_number} URLs for future quick access")

        except Exception as e:
            self.add_comment(f"⚠️ Error saving part info: {str(e)[:50]}")

    def download_file_from_url(self, url, manufacturer, part_number, file_type):
        """Download file from URL and save with proper naming"""
        try:
            self.add_comment(f"📥 Downloading {file_type} from: {url[:60]}...")

            response = self.session.get(url, timeout=30)

            if response.status_code == 200:
                # Determine file extension
                if file_type == "datasheet":
                    extension = ".pdf"
                    directory = self.datasheet_dir
                elif file_type == "3d_model":
                    # Check URL for extension
                    if '.step' in url.lower():
                        extension = ".step"
                    elif '.stp' in url.lower():
                        extension = ".stp"
                    else:
                        extension = ".step"  # Default
                    directory = self.model_3d_dir
                else:
                    extension = ".pdf"
                    directory = self.datasheet_dir

                # Create filename
                safe_manufacturer = re.sub(r'[^\w\s-]', '', manufacturer).strip()
                safe_part = re.sub(r'[^\w\s-]', '', part_number).strip()
                filename = f"{safe_manufacturer} {safe_part}_{file_type}{extension}"

                filepath = directory / filename

                # Save file
                with open(filepath, 'wb') as f:
                    f.write(response.content)

                self.add_comment(f"✅ Downloaded: {filename}")

                # Log to CSV
                self.log_found_file(manufacturer, part_number, url, filename, file_type)

                return True
            else:
                self.add_comment(f"❌ Download failed: HTTP {response.status_code}")
                return False

        except Exception as e:
            self.add_comment(f"❌ Download error: {str(e)[:50]}")
            return False

    def log_found_file(self, manufacturer, part_number, url, filename, file_type):
        """Log found file to CSV"""
        try:
            with open(self.found_files_csv, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # Prepare row data
                if file_type == "datasheet":
                    datasheet_url = url
                    datasheet_filename = filename
                    model_url = ""
                    model_filename = ""
                else:
                    datasheet_url = ""
                    datasheet_filename = ""
                    model_url = url
                    model_filename = filename

                writer.writerow([
                    manufacturer, part_number, datasheet_url, datasheet_filename,
                    model_url, model_filename, "", datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "Success"
                ])

        except Exception as e:
            logger.error(f"Error logging to CSV: {e}")


def main():
    """Main function to start the GUI"""
    print("🚀 Starting Enhanced Component Finder GUI...")
    print(f"📁 Working directory: {os.getcwd()}")

    try:
        print("🖥️ Creating tkinter root window...")
        root = tk.Tk()
        print("✅ Root window created successfully")

        print("🔧 Initializing ComponentFinderGUI...")
        app = ComponentFinderGUI(root)
        print("✅ GUI initialized successfully")

        print("🎯 Starting main event loop...")
        root.mainloop()

    except Exception as e:
        print(f"❌ Error starting GUI: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
