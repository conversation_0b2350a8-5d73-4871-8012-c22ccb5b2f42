#!/usr/bin/env python3
"""
NEVER CLOSE BROWSER
===================
Browser will never close automatically.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def never_close_browser():
    print("🎯 NEVER CLOSE BROWSER")
    print("=" * 30)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Chrome started - will NEVER close automatically")
        
        # Load UltraLibrarian
        print("Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print("✅ UltraLibrarian loaded")
        
        # Enter part number
        print("Entering LM358N...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                print("✅ Part number entered and submitted")
                break
        
        time.sleep(10)
        print("✅ Search results should be showing")
        
        # Keep browser open forever
        print("\n🔒 BROWSER WILL STAY OPEN")
        print("The browser will NOT close automatically.")
        print("You can continue manually or close it yourself.")
        
        # Infinite loop to prevent closing
        while True:
            time.sleep(60)
            print("Browser still open...")
            
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        try:
            driver.quit()
        except:
            pass
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Browser will stay open despite error...")
        # Don't close on error - keep running
        while True:
            time.sleep(60)
            print("Browser still open despite error...")

if __name__ == "__main__":
    never_close_browser()
