#!/usr/bin/env python3
"""
ROBUST ULTRALIBRARIAN AUTOMATION
================================
More robust version with better error handling and timing.
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class RobustUltraLibrarianAutomation:
    def __init__(self):
        self.base_url = 'https://app.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("🚀 Robust UltraLibrarian Automation Ready!")

    def setup_driver(self):
        """Setup Chrome driver with robust options"""
        chrome_options = Options()
        
        # Download preferences
        download_dir = os.path.abspath('3D')
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # Stealth options
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(60)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver

    def wait_and_find_element(self, driver, selectors, timeout=20, description="element"):
        """Robustly find an element using multiple selectors"""
        print(f"   🔍 Looking for {description}...")
        
        for i, selector in enumerate(selectors):
            try:
                if selector.startswith('//'):
                    # XPath selector
                    elements = WebDriverWait(driver, timeout).until(
                        EC.presence_of_all_elements_located((By.XPATH, selector))
                    )
                else:
                    # CSS selector
                    elements = WebDriverWait(driver, timeout).until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                    )
                
                # Find first visible and enabled element
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   ✅ Found {description} with selector {i+1}")
                        return element
                        
            except TimeoutException:
                continue
            except Exception as e:
                print(f"   ⚠️ Selector {i+1} error: {e}")
                continue
        
        print(f"   ❌ Could not find {description}")
        return None

    def screen_1_search(self, driver, part_number):
        """Screen 1: Search for part"""
        print(f"\n🔸 SCREEN 1: Search for {part_number}")
        
        try:
            # Load page
            print(f"   📍 Loading {self.base_url}...")
            driver.get(self.base_url)
            
            # Wait for page to fully load
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(5)  # Additional wait for JavaScript
            
            print(f"   ✅ Page loaded: {driver.title}")
            
            # Find search box
            search_selectors = [
                "input[placeholder*='Search']",
                "input[placeholder*='search']",
                "input[name*='search']",
                "input[type='search']",
                "input[type='text']"
            ]
            
            search_box = self.wait_and_find_element(driver, search_selectors, 20, "search box")
            if not search_box:
                return False
            
            # Enter search term
            print(f"   ⌨️ Entering '{part_number}'...")
            search_box.clear()
            time.sleep(1)
            search_box.send_keys(part_number)
            time.sleep(1)
            search_box.send_keys(Keys.RETURN)
            
            # Wait for results
            print(f"   ⏳ Waiting for search results...")
            time.sleep(8)
            
            print(f"   ✅ Screen 1 complete")
            return True
            
        except Exception as e:
            print(f"   ❌ Screen 1 error: {e}")
            return False

    def screen_2_select_part(self, driver, part_number):
        """Screen 2: Select the TI LM358N part"""
        print(f"\n🔸 SCREEN 2: Select Texas Instruments {part_number}")
        
        try:
            # Wait for results to load
            time.sleep(3)
            
            # Look for TI LM358N results
            result_selectors = [
                f"//a[contains(text(), '{part_number}')]",
                f"//a[contains(text(), '{part_number.upper()}')]",
                f"//a[contains(text(), '{part_number.lower()}')]",
                "//a[contains(@href, 'details')]",
                "//a[contains(@href, 'texas-instruments')]"
            ]
            
            result_link = self.wait_and_find_element(driver, result_selectors, 15, f"{part_number} result")
            if not result_link:
                return False
            
            # Click the result
            print(f"   🖱️ Clicking on: {result_link.text[:50]}...")
            driver.execute_script("arguments[0].scrollIntoView(true);", result_link)
            time.sleep(1)
            result_link.click()
            
            # Wait for part details page
            time.sleep(5)
            print(f"   ✅ Screen 2 complete")
            return True
            
        except Exception as e:
            print(f"   ❌ Screen 2 error: {e}")
            return False

    def screen_3_download_now(self, driver):
        """Screen 3: Click Download Now"""
        print(f"\n🔸 SCREEN 3: Click Download Now")
        
        try:
            # Verify we're on details page
            if 'details' not in driver.current_url.lower():
                print(f"   ❌ Not on details page: {driver.current_url}")
                return False
            
            print(f"   ✅ On part details page")
            
            # Look for Download Now button
            download_selectors = [
                "//button[contains(text(), 'Download Now')]",
                "//a[contains(text(), 'Download Now')]",
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download now')]",
                ".download-button",
                "button[class*='download']"
            ]
            
            download_button = self.wait_and_find_element(driver, download_selectors, 15, "Download Now button")
            if not download_button:
                return False
            
            # Click download
            print(f"   🖱️ Clicking Download Now...")
            driver.execute_script("arguments[0].scrollIntoView(true);", download_button)
            time.sleep(1)
            download_button.click()
            
            time.sleep(3)
            print(f"   ✅ Screen 3 complete")
            return True
            
        except Exception as e:
            print(f"   ❌ Screen 3 error: {e}")
            return False

    def screen_4_3d_model(self, driver):
        """Screen 4: Click 3D CAD Model"""
        print(f"\n🔸 SCREEN 4: Click 3D CAD Model")
        
        try:
            # Look for 3D CAD Model option
            model_selectors = [
                "//button[contains(text(), '3D CAD Model')]",
                "//a[contains(text(), '3D CAD Model')]",
                "//button[contains(text(), '3D Model')]",
                "//a[contains(text(), '3D Model')]",
                ".model-button",
                "button[class*='3d']"
            ]
            
            model_button = self.wait_and_find_element(driver, model_selectors, 15, "3D CAD Model button")
            if not model_button:
                return False
            
            # Click 3D model
            print(f"   🖱️ Clicking 3D CAD Model...")
            driver.execute_script("arguments[0].scrollIntoView(true);", model_button)
            time.sleep(1)
            model_button.click()
            
            time.sleep(3)
            print(f"   ✅ Screen 4 complete")
            return True
            
        except Exception as e:
            print(f"   ❌ Screen 4 error: {e}")
            return False

    def screen_5_login_and_download(self, driver, manufacturer, part_number):
        """Screen 5: Login and download"""
        print(f"\n🔸 SCREEN 5: Login and download")
        
        try:
            # Load credentials
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            print(f"   🔐 Using credentials: {email}")
            
            # Get initial file count
            initial_files = set(os.listdir('3D'))
            
            # Check if we need to login or if download started automatically
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            if current_files != initial_files:
                print(f"   🎉 Download started automatically!")
                return self.check_download_completion(initial_files, manufacturer, part_number)
            
            # Look for login form
            email_selectors = [
                "input[type='email']",
                "input[name*='email']",
                "input[placeholder*='email']",
                "input[id*='email']"
            ]
            
            email_input = self.wait_and_find_element(driver, email_selectors, 10, "email input")
            if not email_input:
                print(f"   ⚠️ No login form found, checking for download...")
                time.sleep(10)
                return self.check_download_completion(initial_files, manufacturer, part_number)
            
            # Enter email
            print(f"   ⌨️ Entering email...")
            email_input.clear()
            email_input.send_keys(email)
            
            # Find password input
            password_selectors = [
                "input[type='password']",
                "input[name*='password']",
                "input[placeholder*='password']"
            ]
            
            password_input = self.wait_and_find_element(driver, password_selectors, 10, "password input")
            if not password_input:
                return False
            
            # Enter password
            print(f"   ⌨️ Entering password...")
            password_input.clear()
            password_input.send_keys(password)
            
            # Find and click login button
            login_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "//button[contains(text(), 'Login')]",
                "//button[contains(text(), 'Sign In')]"
            ]
            
            login_button = self.wait_and_find_element(driver, login_selectors, 10, "login button")
            if not login_button:
                return False
            
            # Click login
            print(f"   🖱️ Clicking login...")
            login_button.click()
            
            # Wait for login and download
            print(f"   ⏳ Waiting for login and download...")
            time.sleep(15)
            
            return self.check_download_completion(initial_files, manufacturer, part_number)
            
        except Exception as e:
            print(f"   ❌ Screen 5 error: {e}")
            return None

    def check_download_completion(self, initial_files, manufacturer, part_number):
        """Check if download completed successfully"""
        try:
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                for new_file in new_files:
                    if any(ext in new_file.lower() for ext in ['.step', '.stp', '.zip']):
                        print(f"   🎉 Downloaded: {new_file}")
                        
                        # Rename to standard format
                        if not new_file.endswith('.step'):
                            old_path = os.path.join('3D', new_file)
                            new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                            new_path = os.path.join('3D', new_name)
                            
                            try:
                                os.rename(old_path, new_path)
                                return new_name
                            except:
                                return new_file
                        
                        return new_file
            
            print(f"   ❌ No files downloaded")
            return None
            
        except Exception as e:
            print(f"   ❌ Download check error: {e}")
            return None

    def run_automation(self, manufacturer, part_number):
        """Run the complete automation"""
        print(f"\n🚀 ROBUST ULTRALIBRARIAN AUTOMATION")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("=" * 60)
        
        driver = self.setup_driver()
        
        try:
            # Run all screens
            if not self.screen_1_search(driver, part_number):
                print(f"\n❌ Screen 1 failed!")
                return None
            
            if not self.screen_2_select_part(driver, part_number):
                print(f"\n❌ Screen 2 failed!")
                return None
            
            if not self.screen_3_download_now(driver):
                print(f"\n❌ Screen 3 failed!")
                return None
            
            if not self.screen_4_3d_model(driver):
                print(f"\n❌ Screen 4 failed!")
                return None
            
            result = self.screen_5_login_and_download(driver, manufacturer, part_number)
            
            if result:
                print(f"\n🎉 COMPLETE SUCCESS!")
                print(f"Downloaded: {result}")
                return result
            else:
                print(f"\n❌ Download failed!")
                return None
                
        except Exception as e:
            print(f"\n❌ Automation error: {e}")
            return None
        
        finally:
            input("Press Enter to close browser...")
            driver.quit()

if __name__ == "__main__":
    automation = RobustUltraLibrarianAutomation()
    result = automation.run_automation("Texas Instruments", "LM358N")
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded to 3D/ folder!")
    else:
        print(f"\n❌ FAILED: No STEP file downloaded")
