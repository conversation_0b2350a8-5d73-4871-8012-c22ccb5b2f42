#!/usr/bin/env python3
"""
UltraLibrarian Step by Step - Manual verification at each screen
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def screen_1_search():
    """SCREEN 1: Search Page"""
    print("🔸 SCREEN 1: Search Page")
    print("=" * 40)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Load UltraLibrarian
        print("Loading https://app.ultralibrarian.com...")
        driver.get("https://app.ultralibrarian.com")
        time.sleep(10)
        
        print(f"✅ Page loaded: {driver.title}")
        print(f"✅ Current URL: {driver.current_url}")
        
        # Find search box
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(inputs)} input elements")
        
        search_input = None
        for i, inp in enumerate(inputs):
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    name = inp.get_attribute('name') or ''
                    print(f"  Input {i}: placeholder='{placeholder}' name='{name}'")
                    
                    if 'search' in placeholder.lower() or 'part' in placeholder.lower():
                        search_input = inp
                        print(f"  ✅ Using this as search box")
                        break
            except:
                continue
        
        if not search_input:
            print("❌ No search box found!")
            input("Press Enter to close...")
            return None
        
        # Type search term
        print("\nTyping 'LM358N' in search box...")
        search_input.clear()
        search_input.send_keys("LM358N")
        
        input("✋ READY TO SEARCH - Press Enter to submit search...")
        
        search_input.send_keys(Keys.RETURN)
        time.sleep(8)
        
        print(f"✅ Search submitted")
        print(f"✅ New URL: {driver.current_url}")
        
        input("✋ SCREEN 1 COMPLETE - Press Enter to continue to Screen 2...")
        
        return driver
        
    except Exception as e:
        print(f"❌ Error in Screen 1: {e}")
        input("Press Enter to close...")
        driver.quit()
        return None

if __name__ == "__main__":
    driver = screen_1_search()
    if driver:
        driver.quit()
