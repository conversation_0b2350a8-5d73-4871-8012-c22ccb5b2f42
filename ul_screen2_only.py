#!/usr/bin/env python3
"""
UltraLibrarian Screen 2 Only - Select Texas Instruments LM358N part
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def screen_2_only():
    print("🔸 SCREEN 2: Select Texas Instruments LM358N part")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    try:
        # Repeat Screen 1 to get to search results
        print("   Repeating Screen 1 to get to search results...")
        driver.get("https://app.ultralibrarian.com")
        time.sleep(10)
        
        # Search for LM358N
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_input = None
        for inp in inputs:
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower() or 'part' in placeholder.lower():
                        search_input = inp
                        break
            except:
                continue
        
        if search_input:
            search_input.clear()
            search_input.send_keys("LM358N")
            search_input.send_keys(Keys.RETURN)
            time.sleep(8)
            print("   ✅ Search completed, now on results page")
        
        # Now Screen 2: Select the Texas Instruments part
        print("   Looking for Texas Instruments LM358N part...")
        
        # Look for links that contain LM358N and Texas Instruments
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"   Found {len(links)} links on page")
        
        ti_part_found = False
        for i, link in enumerate(links):
            try:
                if link.is_displayed():
                    link_text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    
                    # Look for Texas Instruments LM358N
                    if ('lm358n' in link_text.lower() and 
                        ('texas' in link_text.lower() or 'ti' in link_text.lower() or 
                         'nopb' in link_text.lower())):
                        
                        print(f"   ✅ Found TI part: '{link_text}'")
                        print(f"   ✅ Link: {href}")
                        print(f"   Clicking on Texas Instruments LM358N...")
                        
                        link.click()
                        time.sleep(10)
                        ti_part_found = True
                        break
                        
            except Exception as e:
                continue
        
        if not ti_part_found:
            print("   ❌ Texas Instruments LM358N part not found")
            # Show available parts for debugging
            print("   Available parts found:")
            for i, link in enumerate(links[:10]):  # Show first 10
                try:
                    if link.is_displayed() and 'lm358' in link.text.lower():
                        print(f"     {i}: {link.text.strip()}")
                except:
                    continue
            return False
        
        print(f"   ✅ Clicked on Texas Instruments part")
        print(f"   ✅ Current URL: {driver.current_url}")
        
        print("\n🎯 SCREEN 2 COMPLETE")
        print("Browser will stay open for 60 seconds for you to inspect the part details page")
        
        # Keep browser open
        time.sleep(60)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    screen_2_only()
