#!/usr/bin/env python3
"""
FINAL ULTRALIBRARIAN TEST
========================
Run the complete automation through Screen 5, then pause for manual login and download.
This will get us to the login screen, then you complete it manually.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_chrome():
    """Setup Chrome with download preferences"""
    chrome_options = Options()
    
    # Download preferences
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    # Basic options
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def run_automation():
    """Run automation through Screen 5, then manual Screen 6"""
    print("🚀 FINAL ULTRALIBRARIAN TEST")
    print("=" * 50)
    
    # Check initial files
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    print(f"Initial files in 3D/: {len(initial_files)}")
    
    driver = setup_chrome()
    
    try:
        # Screen 1: Search
        print("\n🔸 SCREEN 1: Search for LM358N")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(10)
        
        # Find search box
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_box = None
        for inp in inputs:
            if inp.is_displayed() and inp.is_enabled():
                placeholder = inp.get_attribute('placeholder') or ''
                if 'search' in placeholder.lower():
                    search_box = inp
                    break
        
        if not search_box:
            print("❌ Could not find search box")
            return False
        
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        time.sleep(8)
        print("✅ Screen 1 complete")
        
        # Screen 2: Select TI LM358N
        print("\n🔸 SCREEN 2: Select Texas Instruments LM358N")
        elements = driver.find_elements(By.XPATH, "//a[contains(text(), 'LM358N')]")
        if elements:
            elements[0].click()
            time.sleep(5)
            print("✅ Screen 2 complete")
        else:
            print("❌ Could not find LM358N result")
            return False
        
        # Screen 3: Already on details page
        print("\n🔸 SCREEN 3: On part details page")
        if 'details' in driver.current_url.lower():
            print("✅ Screen 3 complete")
        else:
            print("❌ Not on details page")
            return False
        
        # Screen 4: Download Now
        print("\n🔸 SCREEN 4: Click Download Now")
        download_xpath = "//button[contains(text(), 'Download Now')] | //a[contains(text(), 'Download Now')]"
        elements = driver.find_elements(By.XPATH, download_xpath)
        if elements:
            elements[0].click()
            time.sleep(3)
            print("✅ Screen 4 complete")
        else:
            print("❌ Could not find Download Now button")
            return False
        
        # Screen 5: 3D CAD Model
        print("\n🔸 SCREEN 5: Click 3D CAD Model")
        model_xpath = "//button[contains(text(), '3D CAD Model')] | //a[contains(text(), '3D CAD Model')]"
        elements = driver.find_elements(By.XPATH, model_xpath)
        if elements:
            elements[0].click()
            time.sleep(3)
            print("✅ Screen 5 complete")
        else:
            print("❌ Could not find 3D CAD Model option")
            return False
        
        # Screen 6: Manual login
        print("\n🔸 SCREEN 6: MANUAL LOGIN REQUIRED")
        print("=" * 50)
        print("🎯 AUTOMATION REACHED LOGIN SCREEN!")
        print()
        print("📋 MANUAL STEPS:")
        print("1. The browser is now at the login screen")
        print("2. Login with: <EMAIL>")
        print("3. Password: CompFinder2024!098554")
        print("4. Complete the download")
        print("5. The STEP file should download to the 3D/ folder")
        print()
        print("⏳ Waiting for you to complete login and download...")
        
        # Wait for user to complete
        input("Press Enter after you've completed the login and download: ")
        
        # Check for downloaded files
        current_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
        new_files = current_files - initial_files
        
        print(f"\n📁 File check:")
        print(f"Initial files: {len(initial_files)}")
        print(f"Current files: {len(current_files)}")
        print(f"New files: {list(new_files)}")
        
        # Look for STEP files
        step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
        
        if step_files:
            print(f"\n🎉 SUCCESS! Downloaded STEP files:")
            for step_file in step_files:
                print(f"  ✅ {step_file}")
            return True
        else:
            print(f"\n❌ No STEP files found in new downloads")
            if new_files:
                print(f"New files found (but not STEP): {list(new_files)}")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    success = run_automation()
    if success:
        print("\n🎉 FINAL TEST PASSED: LM358N STEP file downloaded!")
    else:
        print("\n❌ FINAL TEST FAILED: No STEP file downloaded")
