#!/usr/bin/env python3
"""
UltraLibrarian Screen 1 - Just open homepage
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

def open_homepage():
    print("🔸 Opening UltraLibrarian homepage...")
    
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        driver.get("https://app.ultralibrarian.com")
        time.sleep(10)
        
        print(f"✅ Page loaded: {driver.title}")
        print(f"✅ URL: {driver.current_url}")
        
        input("✋ SCREEN 1: Homepage is open. Can you see the search box? Press Enter when ready...")
        
        return driver
        
    except Exception as e:
        print(f"❌ Error: {e}")
        driver.quit()
        return None

if __name__ == "__main__":
    driver = open_homepage()
    if driver:
        input("Press Enter to close browser...")
        driver.quit()
