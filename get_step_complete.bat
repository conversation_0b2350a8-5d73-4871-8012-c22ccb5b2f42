@echo off
echo ========================================
echo    COMPLETE ULTRALIBRARIAN AUTOMATION
echo ========================================
echo.
echo This will automate all 6 screens:
echo 1. Enter part number and search
echo 2. Select specific part from results  
echo 3. Find the right variant and click
echo 4. Hit "Download Now"
echo 5. Hit "3D Model" 
echo 6. Login and download (manual step)
echo.

if "%~2"=="" (
    echo Usage: get_step_complete.bat "Manufacturer" "Part Number"
    echo.
    echo Examples:
    echo   get_step_complete.bat "TI" "LM358N"
    echo   get_step_complete.bat "Analog Devices" "AD8066"
    echo   get_step_complete.bat "ST" "STM32F103"
    echo.
    pause
    exit /b 1
)

echo Running complete automation for: %~1 %~2
echo.

python complete_ultralibrarian_automation.py "%~1" "%~2"

echo.
echo ========================================
echo Check the 3D folder for downloaded files
echo ========================================
pause
