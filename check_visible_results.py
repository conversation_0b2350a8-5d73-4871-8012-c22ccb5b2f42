#!/usr/bin/env python3
"""
Check what's actually visible on TI search results page
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def check_visible_results():
    print("CHECKING VISIBLE RESULTS ON TI SEARCH PAGE")
    print("=" * 50)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Go to TI search results
        search_url = "https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm=LM358N&nr=1280240"
        print(f"Going to: {search_url}")
        driver.get(search_url)
        time.sleep(15)
        
        print(f"Current URL: {driver.current_url}")
        print(f"Page title: {driver.title}")
        
        # Check if LM358N is in HTML source
        html_source = driver.page_source
        lm358_in_source = "LM358N" in html_source.upper()
        print(f"LM358N in HTML source: {lm358_in_source}")
        
        # Check visible text on page
        body_text = driver.find_element("tag name", "body").text
        lm358_visible = "LM358N" in body_text.upper()
        print(f"LM358N in visible text: {lm358_visible}")
        
        # Show first 500 characters of visible text
        print(f"\nFirst 500 characters of visible text:")
        print(f"'{body_text[:500]}...'")
        
        # Look for "no results" or error messages
        error_phrases = ["no results", "not found", "0 results", "no matches", "error"]
        for phrase in error_phrases:
            if phrase in body_text.lower():
                print(f"Found error phrase: '{phrase}'")
        
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    check_visible_results()
