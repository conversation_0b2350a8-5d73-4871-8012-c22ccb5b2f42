#!/usr/bin/env python3
"""
CHECK CHROME SETUP
==================
Diagnose Chrome and ChromeDriver issues.
"""

import os
import subprocess
import sys

def check_chrome_setup():
    print("🔍 CHROME SETUP DIAGNOSTIC")
    print("=" * 30)
    
    # Check if selenium is installed
    print("\n1. Checking Selenium...")
    try:
        import selenium
        print(f"   ✅ Selenium installed: {selenium.__version__}")
    except ImportError:
        print("   ❌ Selenium not installed!")
        print("   Run: pip install selenium")
        return False
    
    # Check if webdriver-manager is available
    print("\n2. Checking webdriver-manager...")
    try:
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        print("   ✅ webdriver-manager available")
        
        # Try to get ChromeDriver
        print("   Downloading/checking ChromeDriver...")
        try:
            driver_path = ChromeDriverManager().install()
            print(f"   ✅ ChromeDriver path: {driver_path}")
        except Exception as e:
            print(f"   ❌ ChromeDriver download failed: {e}")
            return False
            
    except ImportError:
        print("   ❌ webdriver-manager not installed!")
        print("   Run: pip install webdriver-manager")
        return False
    
    # Check Chrome browser
    print("\n3. Checking Chrome browser...")
    try:
        # Try to find Chrome executable
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', ''))
        ]
        
        chrome_found = False
        for path in chrome_paths:
            if os.path.exists(path):
                print(f"   ✅ Chrome found: {path}")
                chrome_found = True
                break
        
        if not chrome_found:
            print("   ❌ Chrome browser not found in standard locations")
            print("   Please install Google Chrome")
            return False
            
    except Exception as e:
        print(f"   ❌ Chrome check failed: {e}")
        return False
    
    # Test basic webdriver creation
    print("\n4. Testing WebDriver creation...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.options import Options
        
        # Setup options
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # Run headless for test
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Create service
        service = Service(ChromeDriverManager().install())
        
        # Create driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Test basic functionality
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"   ✅ WebDriver test successful! Page title: {title}")
        return True
        
    except Exception as e:
        print(f"   ❌ WebDriver test failed: {e}")
        return False

def install_requirements():
    print("\n🔧 INSTALLING REQUIREMENTS...")
    
    requirements = [
        "selenium",
        "webdriver-manager"
    ]
    
    for req in requirements:
        print(f"Installing {req}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", req])
            print(f"   ✅ {req} installed")
        except Exception as e:
            print(f"   ❌ {req} installation failed: {e}")

if __name__ == "__main__":
    print("Chrome Setup Checker")
    print("===================")
    
    choice = input("\n1. Check current setup\n2. Install requirements\n\nChoice (1 or 2): ")
    
    if choice == "2":
        install_requirements()
        print("\nNow checking setup...")
        check_chrome_setup()
    else:
        success = check_chrome_setup()
        
        if not success:
            print("\n💡 SOLUTIONS:")
            print("1. Install missing packages: pip install selenium webdriver-manager")
            print("2. Install Google Chrome browser")
            print("3. Run this script with option 2 to auto-install requirements")
        else:
            print("\n✅ Chrome setup is working correctly!")
