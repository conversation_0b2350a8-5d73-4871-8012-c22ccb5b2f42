#!/usr/bin/env python3
"""
ONE SCREEN AT A TIME
====================
Show each screen and wait for user verification before proceeding.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def one_screen_at_a_time():
    print("🎯 ONE SCREEN AT A TIME - ULTRALIBRARIAN")
    print("=" * 50)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # SCREEN 1: Load UltraLibrarian
        print("\n📺 SCREEN 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        print(f"✅ Loaded: {driver.title}")
        print(f"URL: {driver.current_url}")
        
        response = input("\n❓ SCREEN 1 - Is UltraLibrarian loaded correctly? (y/n): ")
        if response.lower() != 'y':
            print("❌ Stopping at Screen 1")
            return
        
        # SCREEN 2: Enter part number
        print("\n📺 SCREEN 2: Entering LM358N...")
        
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_box = None
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                search_box = inp
                break
        
        if not search_box:
            print("❌ No search box found!")
            return
        
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        print("✅ Entered LM358N and pressed Enter")
        time.sleep(10)
        
        print(f"New URL: {driver.current_url}")
        
        response = input("\n❓ SCREEN 2 - Do you see LM358N search results? (y/n): ")
        if response.lower() != 'y':
            print("❌ Stopping at Screen 2")
            return
        
        # SCREEN 3: Click Texas Instruments LM358N
        print("\n📺 SCREEN 3: Clicking Texas Instruments LM358N...")
        
        links = driver.find_elements(By.TAG_NAME, "a")
        ti_link = None
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed()):
                    ti_link = link
                    print(f"✅ Found: {text}")
                    break
            except:
                continue
        
        if not ti_link:
            print("❌ No Texas Instruments LM358N found!")
            return
        
        ti_link.click()
        print("✅ Clicked Texas Instruments LM358N")
        time.sleep(8)
        
        print(f"New URL: {driver.current_url}")
        
        response = input("\n❓ SCREEN 3 - Are you on the TI LM358N part details page? (y/n): ")
        if response.lower() != 'y':
            print("❌ Stopping at Screen 3")
            return
        
        # SCREEN 4: Click Download Now
        print("\n📺 SCREEN 4: Clicking 'Download Now'...")
        
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if not download_btns:
            print("❌ No 'Download Now' button found!")
            return
        
        print(f"✅ Found Download Now button: {download_btns[0].text}")
        
        # Use JavaScript click to avoid interception
        driver.execute_script("arguments[0].click();", download_btns[0])
        print("✅ Clicked 'Download Now'")
        time.sleep(5)
        
        response = input("\n❓ SCREEN 4 - After clicking Download Now, what do you see? (describe): ")
        print(f"You said: {response}")
        
        if "3d" in response.lower() or "cad" in response.lower():
            print("✅ Proceeding to 3D CAD selection...")
        else:
            print("❌ Unexpected response, stopping for investigation")
            input("Press Enter to close browser...")
            return
        
        # SCREEN 5: Click 3D CAD Model
        print("\n📺 SCREEN 5: Looking for 3D CAD Model option...")
        
        # Show what's available
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print("Available buttons:")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if text:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        response = input("\n❓ SCREEN 5 - Which button should I click for 3D CAD? (tell me the exact text): ")
        
        # Find and click the specified button
        target_button = None
        for btn in buttons:
            try:
                if response.lower() in btn.text.lower() and btn.is_displayed():
                    target_button = btn
                    break
            except:
                continue
        
        if target_button:
            driver.execute_script("arguments[0].click();", target_button)
            print(f"✅ Clicked: {target_button.text}")
            time.sleep(5)
        else:
            print(f"❌ Could not find button with text: {response}")
            input("Press Enter to close browser...")
            return
        
        response = input("\n❓ SCREEN 5 - After clicking 3D CAD, what do you see now? (describe): ")
        print(f"You said: {response}")
        
        print("\n🔍 PAUSING FOR INVESTIGATION")
        print("Browser will stay open for you to inspect the current state...")
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    one_screen_at_a_time()
