#!/usr/bin/env python3
"""
Test what's actually on UltraLibrarian page for LM358N
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time

def test_ultralibrarian_lm358n():
    print("TESTING ULTRALIBRARIAN LM358N PAGE")
    print("=" * 40)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        print("1. Going to UltraLibrarian...")
        driver.get("https://www.ultralibrarian.com")
        time.sleep(10)
        
        print("2. Searching for LM358N...")
        # Go directly to search URL
        search_url = "https://www.ultralibrarian.com/?s=LM358N"
        driver.get(search_url)
        time.sleep(15)
        
        print(f"3. Search results URL: {driver.current_url}")
        
        # Look for LM358N results
        page_text = driver.page_source.lower()
        if "lm358n" in page_text:
            print("✅ Found LM358N on search results")
        else:
            print("❌ No LM358N found")
            
        # Look for product links
        print("4. Looking for LM358N product links...")
        links = driver.find_elements(By.TAG_NAME, "a")
        lm358_links = []
        
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if 'lm358' in text.lower() or 'lm358' in href.lower():
                    lm358_links.append((text, href))
            except:
                continue
        
        print(f"Found {len(lm358_links)} LM358 links:")
        for i, (text, href) in enumerate(lm358_links[:5]):
            print(f"  {i+1}. '{text}' -> {href}")
        
        # Click on first LM358N product link
        if lm358_links:
            print("5. Clicking on first LM358N product...")
            try:
                first_link = driver.find_element(By.XPATH, "//a[contains(text(), 'LM358') or contains(@href, 'lm358')]")
                first_link.click()
                time.sleep(15)
                
                print(f"Product page URL: {driver.current_url}")
                
                # Look for download content
                print("6. Looking for download links on product page...")
                
                download_keywords = ['download', 'step', '3d', 'model', 'cad', 'altium', 'eagle', 'kicad']
                product_text = driver.page_source.lower()
                
                found_keywords = {}
                for keyword in download_keywords:
                    count = product_text.count(keyword)
                    if count > 0:
                        found_keywords[keyword] = count
                
                if found_keywords:
                    print("✅ Found download-related keywords:")
                    for keyword, count in found_keywords.items():
                        print(f"  '{keyword}': {count} times")
                else:
                    print("❌ No download keywords found")
                
                # Look for actual download links
                download_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'Download') or contains(text(), 'STEP') or contains(text(), '3D') or contains(@href, '.step') or contains(@href, 'download')]")
                
                print(f"7. Found {len(download_links)} potential download links:")
                for i, link in enumerate(download_links[:5]):
                    try:
                        text = link.text.strip()
                        href = link.get_attribute('href') or ''
                        print(f"  {i+1}. '{text}' -> {href[:60]}...")
                    except:
                        continue
                
                # Try clicking on download links
                if download_links:
                    print("8. Trying to click first download link...")
                    try:
                        download_links[0].click()
                        time.sleep(10)
                        print(f"After click URL: {driver.current_url}")
                    except Exception as e:
                        print(f"Click failed: {e}")
                        
            except Exception as e:
                print(f"Error clicking product link: {e}")
        
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_ultralibrarian_lm358n()
