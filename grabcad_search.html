<!DOCTYPE html>
<html lang="en" ng-app="communityFrontendApp" ng-strict-di state-attr>
	<head>
		<meta charset="utf-8">
<script>window.NREUM||(NREUM={});NREUM.info={"beacon":"bam.nr-data.net","errorBeacon":"bam.nr-data.net","licenseKey":"2ff0bc8e20","applicationID":"4115141","transactionName":"c1hbEkVfCgoHQhkBV11aQAheRB85BEJZDExVWVFJW1kEFANCTz1VX1NQCkQfDwgGVU4=","queueTime":1,"applicationTime":31,"agent":""}</script>
<script>(window.NREUM||(NREUM={})).init={ajax:{deny_list:["bam.nr-data.net"]}};(window.NREUM||(NREUM={})).loader_config={xpid:"VgEBWVNWGwcBVVRTAwA=",licenseKey:"2ff0bc8e20",applicationID:"4115141"};;/*! For license information please see nr-loader-full-1.295.0.min.js.LICENSE.txt */
(()=>{var e,t,r={8122:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});var n=r(944);function i(e,t){try{if(!e||"object"!=typeof e)return(0,n.R)(3);if(!t||"object"!=typeof t)return(0,n.R)(4);const r=Object.create(Object.getPrototypeOf(t),Object.getOwnPropertyDescriptors(t)),a=0===Object.keys(r).length?e:r;for(let o in a)if(void 0!==e[o])try{if(null===e[o]){r[o]=null;continue}Array.isArray(e[o])&&Array.isArray(t[o])?r[o]=Array.from(new Set([...e[o],...t[o]])):"object"==typeof e[o]&&"object"==typeof t[o]?r[o]=i(e[o],t[o]):r[o]=e[o]}catch(e){r[o]||(0,n.R)(1,e)}return r}catch(e){(0,n.R)(2,e)}}},2555:(e,t,r)=>{"use strict";r.d(t,{D:()=>s,f:()=>o});var n=r(384),i=r(8122);const a={beacon:n.NT.beacon,errorBeacon:n.NT.errorBeacon,licenseKey:void 0,applicationID:void 0,sa:void 0,queueTime:void 0,applicationTime:void 0,ttGuid:void 0,user:void 0,account:void 0,product:void 0,extra:void 0,jsAttributes:{},userAttributes:void 0,atts:void 0,transactionName:void 0,tNamePlain:void 0};function o(e){try{return!!e.licenseKey&&!!e.errorBeacon&&!!e.applicationID}catch(e){return!1}}const s=e=>(0,i.a)(e,a)},9324:(e,t,r)=>{"use strict";r.d(t,{F3:()=>i,Xs:()=>a,Yq:()=>o,xv:()=>n});const n="1.295.0",i="PROD",a="CDN",o="^2.0.0-alpha.18"},6154:(e,t,r)=>{"use strict";r.d(t,{OF:()=>u,RI:()=>i,WN:()=>f,bv:()=>a,gm:()=>o,lR:()=>l,m:()=>c,mw:()=>s,sb:()=>d});var n=r(1863);const i="undefined"!=typeof window&&!!window.document,a="undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self.navigator instanceof WorkerNavigator||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis.navigator instanceof WorkerNavigator),o=i?window:"undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis),s=Boolean("hidden"===o?.document?.visibilityState),c=""+o?.location,u=/iPad|iPhone|iPod/.test(o.navigator?.userAgent),d=u&&"undefined"==typeof SharedWorker,l=(()=>{const e=o.navigator?.userAgent?.match(/Firefox[/\s](\d+\.\d+)/);return Array.isArray(e)&&e.length>=2?+e[1]:0})(),f=Date.now()-(0,n.t)()},7295:(e,t,r)=>{"use strict";r.d(t,{Xv:()=>o,gX:()=>i,iW:()=>a});var n=[];function i(e){if(!e||a(e))return!1;if(0===n.length)return!0;for(var t=0;t<n.length;t++){var r=n[t];if("*"===r.hostname)return!1;if(s(r.hostname,e.hostname)&&c(r.pathname,e.pathname))return!1}return!0}function a(e){return void 0===e.hostname}function o(e){if(n=[],e&&e.length)for(var t=0;t<e.length;t++){let r=e[t];if(!r)continue;0===r.indexOf("http://")?r=r.substring(7):0===r.indexOf("https://")&&(r=r.substring(8));const i=r.indexOf("/");let a,o;i>0?(a=r.substring(0,i),o=r.substring(i)):(a=r,o="");let[s]=a.split(":");n.push({hostname:s,pathname:o})}}function s(e,t){return!(e.length>t.length)&&t.indexOf(e)===t.length-e.length}function c(e,t){return 0===e.indexOf("/")&&(e=e.substring(1)),0===t.indexOf("/")&&(t=t.substring(1)),""===e||e===t}},3241:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});var n=r(6154);const i="newrelic";function a(e={}){try{n.gm.dispatchEvent(new CustomEvent(i,{detail:e}))}catch(e){}}},1687:(e,t,r)=>{"use strict";r.d(t,{Ak:()=>u,Ze:()=>f,x3:()=>d});var n=r(3241),i=r(7836),a=r(3606),o=r(860),s=r(2646);const c={};function u(e,t){const r={staged:!1,priority:o.P3[t]||0};l(e),c[e].get(t)||c[e].set(t,r)}function d(e,t){e&&c[e]&&(c[e].get(t)&&c[e].delete(t),p(e,t,!1),c[e].size&&h(e))}function l(e){if(!e)throw new Error("agentIdentifier required");c[e]||(c[e]=new Map)}function f(e="",t="feature",r=!1){if(l(e),!e||!c[e].get(t)||r)return p(e,t);c[e].get(t).staged=!0,h(e)}function h(e){const t=Array.from(c[e]);t.every((([e,t])=>t.staged))&&(t.sort(((e,t)=>e[1].priority-t[1].priority)),t.forEach((([t])=>{c[e].delete(t),p(e,t)})))}function p(e,t,r=!0){const o=e?i.ee.get(e):i.ee,c=a.i.handlers;if(!o.aborted&&o.backlog&&c){if((0,n.W)({agentIdentifier:e,type:"lifecycle",name:"drain",feature:t}),r){const e=o.backlog[t],r=c[t];if(r){for(let t=0;e&&t<e.length;++t)g(e[t],r);Object.entries(r).forEach((([e,t])=>{Object.values(t||{}).forEach((t=>{t[0]?.on&&t[0]?.context()instanceof s.y&&t[0].on(e,t[1])}))}))}}o.isolatedBacklog||delete c[t],o.backlog[t]=null,o.emit("drain-"+t,[])}}function g(e,t){var r=e[1];Object.values(t[r]||{}).forEach((t=>{var r=e[0];if(t[0]===r){var n=t[1],i=e[3],a=e[2];n.apply(i,a)}}))}},7836:(e,t,r)=>{"use strict";r.d(t,{P:()=>s,ee:()=>c});var n=r(384),i=r(8990),a=r(2646),o=r(5607);const s="nr@context:".concat(o.W),c=function e(t,r){var n={},o={},d={},l=!1;try{l=16===r.length&&u.initializedAgents?.[r]?.runtime.isolatedBacklog}catch(e){}var f={on:p,addEventListener:p,removeEventListener:function(e,t){var r=n[e];if(!r)return;for(var i=0;i<r.length;i++)r[i]===t&&r.splice(i,1)},emit:function(e,r,n,i,a){!1!==a&&(a=!0);if(c.aborted&&!i)return;t&&a&&t.emit(e,r,n);var s=h(n);g(e).forEach((e=>{e.apply(s,r)}));var u=v()[o[e]];u&&u.push([f,e,r,s]);return s},get:m,listeners:g,context:h,buffer:function(e,t){const r=v();if(t=t||"feature",f.aborted)return;Object.entries(e||{}).forEach((([e,n])=>{o[n]=t,t in r||(r[t]=[])}))},abort:function(){f._aborted=!0,Object.keys(f.backlog).forEach((e=>{delete f.backlog[e]}))},isBuffering:function(e){return!!v()[o[e]]},debugId:r,backlog:l?{}:t&&"object"==typeof t.backlog?t.backlog:{},isolatedBacklog:l};return Object.defineProperty(f,"aborted",{get:()=>{let e=f._aborted||!1;return e||(t&&(e=t.aborted),e)}}),f;function h(e){return e&&e instanceof a.y?e:e?(0,i.I)(e,s,(()=>new a.y(s))):new a.y(s)}function p(e,t){n[e]=g(e).concat(t)}function g(e){return n[e]||[]}function m(t){return d[t]=d[t]||e(f,t)}function v(){return f.backlog}}(void 0,"globalEE"),u=(0,n.Zm)();u.ee||(u.ee=c)},2646:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});class n{constructor(e){this.contextId=e}}},9908:(e,t,r)=>{"use strict";r.d(t,{d:()=>n,p:()=>i});var n=r(7836).ee.get("handle");function i(e,t,r,i,a){a?(a.buffer([e],i),a.emit(e,t,r)):(n.buffer([e],i),n.emit(e,t,r))}},3606:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(9908);a.on=o;var i=a.handlers={};function a(e,t,r,a){o(a||n.d,i,e,t,r)}function o(e,t,r,i,a){a||(a="feature"),e||(e=n.d);var o=t[a]=t[a]||{};(o[r]=o[r]||[]).push([e,i])}},3878:(e,t,r)=>{"use strict";function n(e,t){return{capture:e,passive:!1,signal:t}}function i(e,t,r=!1,i){window.addEventListener(e,t,n(r,i))}function a(e,t,r=!1,i){document.addEventListener(e,t,n(r,i))}r.d(t,{DD:()=>a,jT:()=>n,sp:()=>i})},5607:(e,t,r)=>{"use strict";r.d(t,{W:()=>n});const n=(0,r(9566).bz)()},9566:(e,t,r)=>{"use strict";r.d(t,{LA:()=>s,ZF:()=>c,bz:()=>o,el:()=>u});var n=r(6154);const i="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";function a(e,t){return e?15&e[t]:16*Math.random()|0}function o(){const e=n.gm?.crypto||n.gm?.msCrypto;let t,r=0;return e&&e.getRandomValues&&(t=e.getRandomValues(new Uint8Array(30))),i.split("").map((e=>"x"===e?a(t,r++).toString(16):"y"===e?(3&a()|8).toString(16):e)).join("")}function s(e){const t=n.gm?.crypto||n.gm?.msCrypto;let r,i=0;t&&t.getRandomValues&&(r=t.getRandomValues(new Uint8Array(e)));const o=[];for(var s=0;s<e;s++)o.push(a(r,i++).toString(16));return o.join("")}function c(){return s(16)}function u(){return s(32)}},2614:(e,t,r)=>{"use strict";r.d(t,{BB:()=>o,H3:()=>n,g:()=>u,iL:()=>c,tS:()=>s,uh:()=>i,wk:()=>a});const n="NRBA",i="SESSION",a=144e5,o=18e5,s={STARTED:"session-started",PAUSE:"session-pause",RESET:"session-reset",RESUME:"session-resume",UPDATE:"session-update"},c={SAME_TAB:"same-tab",CROSS_TAB:"cross-tab"},u={OFF:0,FULL:1,ERROR:2}},1863:(e,t,r)=>{"use strict";function n(){return Math.floor(performance.now())}r.d(t,{t:()=>n})},7485:(e,t,r)=>{"use strict";r.d(t,{D:()=>i});var n=r(6154);function i(e){if(0===(e||"").indexOf("data:"))return{protocol:"data"};try{const t=new URL(e,location.href),r={port:t.port,hostname:t.hostname,pathname:t.pathname,search:t.search,protocol:t.protocol.slice(0,t.protocol.indexOf(":")),sameOrigin:t.protocol===n.gm?.location?.protocol&&t.host===n.gm?.location?.host};return r.port&&""!==r.port||("http:"===t.protocol&&(r.port="80"),"https:"===t.protocol&&(r.port="443")),r.pathname&&""!==r.pathname?r.pathname.startsWith("/")||(r.pathname="/".concat(r.pathname)):r.pathname="/",r}catch(e){return{}}}},944:(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var n=r(3241);function i(e,t){"function"==typeof console.debug&&(console.debug("New Relic Warning: https://github.com/newrelic/newrelic-browser-agent/blob/main/docs/warning-codes.md#".concat(e),t),(0,n.W)({agentIdentifier:null,drained:null,type:"data",name:"warn",feature:"warn",data:{code:e,secondary:t}}))}},5701:(e,t,r)=>{"use strict";r.d(t,{B:()=>a,t:()=>o});var n=r(3241);const i=new Set,a={};function o(e,t){const r=t.agentIdentifier;a[r]??={},e&&"object"==typeof e&&(i.has(r)||(t.ee.emit("rumresp",[e]),a[r]=e,i.add(r),(0,n.W)({agentIdentifier:r,loaded:!0,drained:!0,type:"lifecycle",name:"load",feature:void 0,data:e})))}},8990:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var n=Object.prototype.hasOwnProperty;function i(e,t,r){if(n.call(e,t))return e[t];var i=r();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(e,t,{value:i,writable:!0,enumerable:!1}),i}catch(e){}return e[t]=i,i}},6389:(e,t,r)=>{"use strict";function n(e,t=500,r={}){const n=r?.leading||!1;let i;return(...r)=>{n&&void 0===i&&(e.apply(this,r),i=setTimeout((()=>{i=clearTimeout(i)}),t)),n||(clearTimeout(i),i=setTimeout((()=>{e.apply(this,r)}),t))}}function i(e){let t=!1;return(...r)=>{t||(t=!0,e.apply(this,r))}}r.d(t,{J:()=>i,s:()=>n})},1910:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(944);const i=new Map;function a(...e){return e.every((e=>{if(i.has(e))return i.get(e);const t="function"==typeof e&&e.toString().includes("[native code]");return t||(0,n.R)(64,e?.name||e?.toString()),i.set(e,t),t}))}},3304:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(7836);const i=()=>{const e=new WeakSet;return(t,r)=>{if("object"==typeof r&&null!==r){if(e.has(r))return;e.add(r)}return r}};function a(e){try{return JSON.stringify(e,i())??""}catch(e){try{n.ee.emit("internal-error",[e])}catch(e){}return""}}},3496:(e,t,r)=>{"use strict";function n(e){return!e||!(!e.licenseKey||!e.applicationID)}function i(e,t){return!e||e.licenseKey===t.info.licenseKey&&e.applicationID===t.info.applicationID}r.d(t,{A:()=>i,I:()=>n})},5289:(e,t,r)=>{"use strict";r.d(t,{GG:()=>a,Qr:()=>s,sB:()=>o});var n=r(3878);function i(){return"undefined"==typeof document||"complete"===document.readyState}function a(e,t){if(i())return e();(0,n.sp)("load",e,t)}function o(e){if(i())return e();(0,n.DD)("DOMContentLoaded",e)}function s(e){if(i())return e();(0,n.sp)("popstate",e)}},384:(e,t,r)=>{"use strict";r.d(t,{NT:()=>o,US:()=>d,Zm:()=>s,bQ:()=>u,dV:()=>c,pV:()=>l});var n=r(6154),i=r(1863),a=r(1910);const o={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net"};function s(){return n.gm.NREUM||(n.gm.NREUM={}),void 0===n.gm.newrelic&&(n.gm.newrelic=n.gm.NREUM),n.gm.NREUM}function c(){let e=s();return e.o||(e.o={ST:n.gm.setTimeout,SI:n.gm.setImmediate||n.gm.setInterval,CT:n.gm.clearTimeout,XHR:n.gm.XMLHttpRequest,REQ:n.gm.Request,EV:n.gm.Event,PR:n.gm.Promise,MO:n.gm.MutationObserver,FETCH:n.gm.fetch,WS:n.gm.WebSocket},(0,a.i)(...Object.values(e.o))),e}function u(e,t){let r=s();r.initializedAgents??={},t.initializedAt={ms:(0,i.t)(),date:new Date},r.initializedAgents[e]=t}function d(e,t){s()[e]=t}function l(){return function(){let e=s();const t=e.info||{};e.info={beacon:o.beacon,errorBeacon:o.errorBeacon,...t}}(),function(){let e=s();const t=e.init||{};e.init={...t}}(),c(),function(){let e=s();const t=e.loader_config||{};e.loader_config={...t}}(),s()}},2843:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});var n=r(3878);function i(e,t=!1,r,i){(0,n.DD)("visibilitychange",(function(){if(t)return void("hidden"===document.visibilityState&&e());e(document.visibilityState)}),r,i)}},8139:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(7836),i=r(3434),a=r(8990),o=r(6154);const s={},c=o.gm.XMLHttpRequest,u="addEventListener",d="removeEventListener",l="nr@wrapped:".concat(n.P);function f(e){var t=function(e){return(e||n.ee).get("events")}(e);if(s[t.debugId]++)return t;s[t.debugId]=1;var r=(0,i.YM)(t,!0);function f(e){r.inPlace(e,[u,d],"-",p)}function p(e,t){return e[1]}return"getPrototypeOf"in Object&&(o.RI&&h(document,f),c&&h(c.prototype,f),h(o.gm,f)),t.on(u+"-start",(function(e,t){var n=e[1];if(null!==n&&("function"==typeof n||"object"==typeof n)){var i=(0,a.I)(n,l,(function(){var e={object:function(){if("function"!=typeof n.handleEvent)return;return n.handleEvent.apply(n,arguments)},function:n}[typeof n];return e?r(e,"fn-",null,e.name||"anonymous"):n}));this.wrapped=e[1]=i}})),t.on(d+"-start",(function(e){e[1]=this.wrapped||e[1]})),t}function h(e,t,...r){let n=e;for(;"object"==typeof n&&!Object.prototype.hasOwnProperty.call(n,u);)n=Object.getPrototypeOf(n);n&&t(n,...r)}},3434:(e,t,r)=>{"use strict";r.d(t,{Jt:()=>a,YM:()=>c});var n=r(7836),i=r(5607);const a="nr@original:".concat(i.W);var o=Object.prototype.hasOwnProperty,s=!1;function c(e,t){return e||(e=n.ee),r.inPlace=function(e,t,n,i,a){n||(n="");const o="-"===n.charAt(0);for(let s=0;s<t.length;s++){const c=t[s],u=e[c];d(u)||(e[c]=r(u,o?c+n:n,i,c,a))}},r.flag=a,r;function r(t,r,n,s,c){return d(t)?t:(r||(r=""),nrWrapper[a]=t,function(e,t,r){if(Object.defineProperty&&Object.keys)try{return Object.keys(e).forEach((function(r){Object.defineProperty(t,r,{get:function(){return e[r]},set:function(t){return e[r]=t,t}})})),t}catch(e){u([e],r)}for(var n in e)o.call(e,n)&&(t[n]=e[n])}(t,nrWrapper,e),nrWrapper);function nrWrapper(){var a,o,d,l;let f;try{o=this,a=[...arguments],d="function"==typeof n?n(a,o):n||{}}catch(t){u([t,"",[a,o,s],d],e)}i(r+"start",[a,o,s],d,c);const h=performance.now();let p=h;try{return l=t.apply(o,a),p=performance.now(),l}catch(e){throw p=performance.now(),i(r+"err",[a,o,e],d,c),f=e,f}finally{const e=p-h,t={duration:e,isLongTask:e>=50,methodName:s,thrownError:f};t.isLongTask&&i("long-task",[t],d,c),i(r+"end",[a,o,l,t],d,c)}}}function i(r,n,i,a){if(!s||t){var o=s;s=!0;try{e.emit(r,n,i,t,a)}catch(t){u([t,r,n,i],e)}s=o}}}function u(e,t){t||(t=n.ee);try{t.emit("internal-error",e)}catch(e){}}function d(e){return!(e&&"function"==typeof e&&e.apply&&!e[a])}},9300:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.ajax},3333:(e,t,r)=>{"use strict";r.d(t,{$v:()=>u,TZ:()=>n,Zp:()=>i,kd:()=>c,mq:()=>s,nf:()=>o,qN:()=>a});const n=r(860).K7.genericEvents,i=["auxclick","click","copy","keydown","paste","scrollend"],a=["focus","blur"],o=4,s=1e3,c=["PageAction","UserAction","BrowserPerformance"],u={MARKS:"experimental.marks",MEASURES:"experimental.measures",RESOURCES:"experimental.resources"}},6774:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.jserrors},993:(e,t,r)=>{"use strict";r.d(t,{A$:()=>a,ET:()=>o,TZ:()=>s,p_:()=>i});var n=r(860);const i={ERROR:"ERROR",WARN:"WARN",INFO:"INFO",DEBUG:"DEBUG",TRACE:"TRACE"},a={OFF:0,ERROR:1,WARN:2,INFO:3,DEBUG:4,TRACE:5},o="log",s=n.K7.logging},3785:(e,t,r)=>{"use strict";r.d(t,{R:()=>c,b:()=>u});var n=r(9908),i=r(1863),a=r(860),o=r(8154),s=r(993);function c(e,t,r={},c=s.p_.INFO,u,d=(0,i.t)()){(0,n.p)(o.xV,["API/logging/".concat(c.toLowerCase(),"/called")],void 0,a.K7.metrics,e),(0,n.p)(s.ET,[d,t,r,c,u],void 0,a.K7.logging,e)}function u(e){return"string"==typeof e&&Object.values(s.p_).some((t=>t===e.toUpperCase().trim()))}},8154:(e,t,r)=>{"use strict";r.d(t,{z_:()=>a,XG:()=>s,TZ:()=>n,rs:()=>i,xV:()=>o});r(6154),r(9566),r(384);const n=r(860).K7.metrics,i="sm",a="cm",o="storeSupportabilityMetrics",s="storeEventMetrics"},6630:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewEvent},782:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewTiming},6344:(e,t,r)=>{"use strict";r.d(t,{BB:()=>d,G4:()=>a,Qb:()=>l,TZ:()=>i,Ug:()=>o,_s:()=>s,bc:()=>u,yP:()=>c});var n=r(2614);const i=r(860).K7.sessionReplay,a={RECORD:"recordReplay",PAUSE:"pauseReplay",ERROR_DURING_REPLAY:"errorDuringReplay"},o=.12,s={DomContentLoaded:0,Load:1,FullSnapshot:2,IncrementalSnapshot:3,Meta:4,Custom:5},c={[n.g.ERROR]:15e3,[n.g.FULL]:3e5,[n.g.OFF]:0},u={RESET:{message:"Session was reset",sm:"Reset"},IMPORT:{message:"Recorder failed to import",sm:"Import"},TOO_MANY:{message:"429: Too Many Requests",sm:"Too-Many"},TOO_BIG:{message:"Payload was too large",sm:"Too-Big"},CROSS_TAB:{message:"Session Entity was set to OFF on another tab",sm:"Cross-Tab"},ENTITLEMENTS:{message:"Session Replay is not allowed and will not be started",sm:"Entitlement"}},d=5e3,l={API:"api"}},5270:(e,t,r)=>{"use strict";r.d(t,{Aw:()=>s,CT:()=>c,SR:()=>o,rF:()=>u});var n=r(384),i=r(7767),a=r(6154);function o(e){return!!(0,n.dV)().o.MO&&(0,i.V)(e)&&!0===e?.session_trace.enabled}function s(e){return!0===e?.session_replay.preload&&o(e)}function c(e,t){const r=t.correctAbsoluteTimestamp(e);return{originalTimestamp:e,correctedTimestamp:r,timestampDiff:e-r,originTime:a.WN,correctedOriginTime:t.correctedOriginTime,originTimeDiff:Math.floor(a.WN-t.correctedOriginTime)}}function u(e,t){try{if("string"==typeof t?.type){if("password"===t.type.toLowerCase())return"*".repeat(e?.length||0);if(void 0!==t?.dataset?.nrUnmask||t?.classList?.contains("nr-unmask"))return e}}catch(e){}return"string"==typeof e?e.replace(/[\S]/g,"*"):"*".repeat(e?.length||0)}},3738:(e,t,r)=>{"use strict";r.d(t,{He:()=>i,Kp:()=>s,Lc:()=>u,Rz:()=>d,TZ:()=>n,bD:()=>a,d3:()=>o,jx:()=>l,uP:()=>c});const n=r(860).K7.sessionTrace,i="bstResource",a="resource",o="-start",s="-end",c="fn"+o,u="fn"+s,d="pushState",l=1e3},4234:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});var n=r(7836),i=r(1687);class a{constructor(e,t){this.agentIdentifier=e,this.ee=n.ee.get(e),this.featureName=t,this.blocked=!1}deregisterDrain(){(0,i.x3)(this.agentIdentifier,this.featureName)}}},7767:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});var n=r(6154);const i=e=>n.RI&&!0===e?.privacy.cookies_enabled},1741:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});var n=r(944),i=r(4261);class a{#e(e,...t){if(this[e]!==a.prototype[e])return this[e](...t);(0,n.R)(35,e)}addPageAction(e,t){return this.#e(i.hG,e,t)}register(e){return this.#e(i.eY,e)}recordCustomEvent(e,t){return this.#e(i.fF,e,t)}setPageViewName(e,t){return this.#e(i.Fw,e,t)}setCustomAttribute(e,t,r){return this.#e(i.cD,e,t,r)}noticeError(e,t){return this.#e(i.o5,e,t)}setUserId(e){return this.#e(i.Dl,e)}setApplicationVersion(e){return this.#e(i.nb,e)}setErrorHandler(e){return this.#e(i.bt,e)}addRelease(e,t){return this.#e(i.k6,e,t)}log(e,t){return this.#e(i.$9,e,t)}start(){return this.#e(i.d3)}finished(e){return this.#e(i.BL,e)}recordReplay(){return this.#e(i.CH)}pauseReplay(){return this.#e(i.Tb)}addToTrace(e){return this.#e(i.U2,e)}setCurrentRouteName(e){return this.#e(i.PA,e)}interaction(){return this.#e(i.dT)}wrapLogger(e,t,r){return this.#e(i.Wb,e,t,r)}measure(e,t){return this.#e(i.V1,e,t)}}},4261:(e,t,r)=>{"use strict";r.d(t,{$9:()=>u,BL:()=>s,CH:()=>h,Dl:()=>R,Fw:()=>y,PA:()=>m,Pl:()=>n,Tb:()=>l,U2:()=>a,V1:()=>T,Wb:()=>w,bt:()=>b,cD:()=>v,d3:()=>x,dT:()=>c,eY:()=>p,fF:()=>f,hG:()=>i,k6:()=>o,nb:()=>g,o5:()=>d});const n="api-",i="addPageAction",a="addToTrace",o="addRelease",s="finished",c="interaction",u="log",d="noticeError",l="pauseReplay",f="recordCustomEvent",h="recordReplay",p="register",g="setApplicationVersion",m="setCurrentRouteName",v="setCustomAttribute",b="setErrorHandler",y="setPageViewName",R="setUserId",x="start",w="wrapLogger",T="measure"},5205:(e,t,r)=>{"use strict";r.d(t,{j:()=>A});var n=r(384),i=r(1741);var a=r(2555),o=r(3333);const s=e=>{if(!e||"string"!=typeof e)return!1;try{document.createDocumentFragment().querySelector(e)}catch{return!1}return!0};var c=r(2614),u=r(944),d=r(8122);const l="[data-nr-mask]",f=e=>(0,d.a)(e,(()=>{const e={feature_flags:[],experimental:{marks:!1,measures:!1,resources:!1},mask_selector:"*",block_selector:"[data-nr-block]",mask_input_options:{color:!1,date:!1,"datetime-local":!1,email:!1,month:!1,number:!1,range:!1,search:!1,tel:!1,text:!1,time:!1,url:!1,week:!1,textarea:!1,select:!1,password:!0}};return{ajax:{deny_list:void 0,block_internal:!0,enabled:!0,autoStart:!0},api:{allow_registered_children:!0,duplicate_registered_data:!1},distributed_tracing:{enabled:void 0,exclude_newrelic_header:void 0,cors_use_newrelic_header:void 0,cors_use_tracecontext_headers:void 0,allowed_origins:void 0},get feature_flags(){return e.feature_flags},set feature_flags(t){e.feature_flags=t},generic_events:{enabled:!0,autoStart:!0},harvest:{interval:30},jserrors:{enabled:!0,autoStart:!0},logging:{enabled:!0,autoStart:!0},metrics:{enabled:!0,autoStart:!0},obfuscate:void 0,page_action:{enabled:!0},page_view_event:{enabled:!0,autoStart:!0},page_view_timing:{enabled:!0,autoStart:!0},performance:{get capture_marks(){return e.feature_flags.includes(o.$v.MARKS)||e.experimental.marks},set capture_marks(t){e.experimental.marks=t},get capture_measures(){return e.feature_flags.includes(o.$v.MEASURES)||e.experimental.measures},set capture_measures(t){e.experimental.measures=t},capture_detail:!0,resources:{get enabled(){return e.feature_flags.includes(o.$v.RESOURCES)||e.experimental.resources},set enabled(t){e.experimental.resources=t},asset_types:[],first_party_domains:[],ignore_newrelic:!0}},privacy:{cookies_enabled:!0},proxy:{assets:void 0,beacon:void 0},session:{expiresMs:c.wk,inactiveMs:c.BB},session_replay:{autoStart:!0,enabled:!1,preload:!1,sampling_rate:10,error_sampling_rate:100,collect_fonts:!1,inline_images:!1,fix_stylesheets:!0,mask_all_inputs:!0,get mask_text_selector(){return e.mask_selector},set mask_text_selector(t){s(t)?e.mask_selector="".concat(t,",").concat(l):""===t||null===t?e.mask_selector=l:(0,u.R)(5,t)},get block_class(){return"nr-block"},get ignore_class(){return"nr-ignore"},get mask_text_class(){return"nr-mask"},get block_selector(){return e.block_selector},set block_selector(t){s(t)?e.block_selector+=",".concat(t):""!==t&&(0,u.R)(6,t)},get mask_input_options(){return e.mask_input_options},set mask_input_options(t){t&&"object"==typeof t?e.mask_input_options={...t,password:!0}:(0,u.R)(7,t)}},session_trace:{enabled:!0,autoStart:!0},soft_navigations:{enabled:!0,autoStart:!0},spa:{enabled:!0,autoStart:!0},ssl:void 0,user_actions:{enabled:!0,elementAttributes:["id","className","tagName","type"]}}})());var h=r(6154),p=r(9324);let g=0;const m={buildEnv:p.F3,distMethod:p.Xs,version:p.xv,originTime:h.WN},v={appMetadata:{},customTransaction:void 0,denyList:void 0,disabled:!1,entityManager:void 0,harvester:void 0,isolatedBacklog:!1,isRecording:!1,loaderType:void 0,maxBytes:3e4,obfuscator:void 0,onerror:void 0,ptid:void 0,releaseIds:{},session:void 0,timeKeeper:void 0,jsAttributesMetadata:{bytes:0},get harvestCount(){return++g}},b=e=>{const t=(0,d.a)(e,v),r=Object.keys(m).reduce(((e,t)=>(e[t]={value:m[t],writable:!1,configurable:!0,enumerable:!0},e)),{});return Object.defineProperties(t,r)};var y=r(5701);const R=e=>{const t=e.startsWith("http");e+="/",r.p=t?e:"https://"+e};var x=r(7836),w=r(3241);const T={accountID:void 0,trustKey:void 0,agentID:void 0,licenseKey:void 0,applicationID:void 0,xpid:void 0},_=e=>(0,d.a)(e,T),S=new Set;function A(e,t={},r,o){let{init:s,info:c,loader_config:u,runtime:d={},exposed:l=!0}=t;if(!c){const e=(0,n.pV)();s=e.init,c=e.info,u=e.loader_config}e.init=f(s||{}),e.loader_config=_(u||{}),c.jsAttributes??={},h.bv&&(c.jsAttributes.isWorker=!0),e.info=(0,a.D)(c);const p=e.init,g=[c.beacon,c.errorBeacon];S.has(e.agentIdentifier)||(p.proxy.assets&&(R(p.proxy.assets),g.push(p.proxy.assets)),p.proxy.beacon&&g.push(p.proxy.beacon),function(e){const t=(0,n.pV)();Object.getOwnPropertyNames(i.W.prototype).forEach((r=>{const n=i.W.prototype[r];if("function"!=typeof n||"constructor"===n)return;let a=t[r];e[r]&&!1!==e.exposed&&"micro-agent"!==e.runtime?.loaderType&&(t[r]=(...t)=>{const n=e[r](...t);return a?a(...t):n})}))}(e),(0,n.US)("activatedFeatures",y.B),e.runSoftNavOverSpa&&=!0===p.soft_navigations.enabled&&p.feature_flags.includes("soft_nav")),d.denyList=[...p.ajax.deny_list||[],...p.ajax.block_internal?g:[]],d.ptid=e.agentIdentifier,d.loaderType=r,e.runtime=b(d),S.has(e.agentIdentifier)||(e.ee=x.ee.get(e.agentIdentifier),e.exposed=l,(0,w.W)({agentIdentifier:e.agentIdentifier,drained:!!y.B?.[e.agentIdentifier],type:"lifecycle",name:"initialize",feature:void 0,data:e.config})),S.add(e.agentIdentifier)}},8374:(e,t,r)=>{r.nc=(()=>{try{return document?.currentScript?.nonce}catch(e){}return""})()},860:(e,t,r)=>{"use strict";r.d(t,{$J:()=>d,K7:()=>c,P3:()=>u,XX:()=>i,Yy:()=>s,df:()=>a,qY:()=>n,v4:()=>o});const n="events",i="jserrors",a="browser/blobs",o="rum",s="browser/logs",c={ajax:"ajax",genericEvents:"generic_events",jserrors:i,logging:"logging",metrics:"metrics",pageAction:"page_action",pageViewEvent:"page_view_event",pageViewTiming:"page_view_timing",sessionReplay:"session_replay",sessionTrace:"session_trace",softNav:"soft_navigations",spa:"spa"},u={[c.pageViewEvent]:1,[c.pageViewTiming]:2,[c.metrics]:3,[c.jserrors]:4,[c.spa]:5,[c.ajax]:6,[c.sessionTrace]:7,[c.softNav]:8,[c.sessionReplay]:9,[c.logging]:10,[c.genericEvents]:11},d={[c.pageViewEvent]:o,[c.pageViewTiming]:n,[c.ajax]:n,[c.spa]:n,[c.softNav]:n,[c.metrics]:i,[c.jserrors]:i,[c.sessionTrace]:a,[c.sessionReplay]:a,[c.logging]:s,[c.genericEvents]:"ins"}}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var a=n[e]={exports:{}};return r[e](a,a.exports,i),a.exports}i.m=r,i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,r)=>(i.f[r](e,t),t)),[])),i.u=e=>({95:"nr-full-compressor",222:"nr-full-recorder",891:"nr-full"}[e]+"-1.295.0.min.js"),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="NRBA-1.295.0.PROD:",i.l=(r,n,a,o)=>{if(e[r])e[r].push(n);else{var s,c;if(void 0!==a)for(var u=document.getElementsByTagName("script"),d=0;d<u.length;d++){var l=u[d];if(l.getAttribute("src")==r||l.getAttribute("data-webpack")==t+a){s=l;break}}if(!s){c=!0;var f={891:"sha512-jof32EeiJXPGQ6U0rxTUAvtFrW/6xm/gJJV+ubYkUl1R7cMaogYNCx3RaaPGN8NLCZA2G4qxHlw4wkM+5LeL1g==",222:"sha512-ZEb0UWPEupD3jiDU/NMpZt/k12WXRSNzacjBi/fuLQkXHSAg2PgKil6cLSVSsDQm/dZ4WQeUyydrw3M4SQA+7w==",95:"sha512-Ute+q3UP0KbnwVFBff0CvCHLoJuGW9iydP2d0rdMMBtYjJOSFKWeRSS+tnJbJCyig8awoxqbGprgRPt+kFBy9A=="};(s=document.createElement("script")).charset="utf-8",s.timeout=120,i.nc&&s.setAttribute("nonce",i.nc),s.setAttribute("data-webpack",t+a),s.src=r,0!==s.src.indexOf(window.location.origin+"/")&&(s.crossOrigin="anonymous"),f[o]&&(s.integrity=f[o])}e[r]=[n];var h=(t,n)=>{s.onerror=s.onload=null,clearTimeout(p);var i=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach((e=>e(n))),t)return t(n)},p=setTimeout(h.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=h.bind(null,s.onerror),s.onload=h.bind(null,s.onload),c&&document.head.appendChild(s)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.p="https://js-agent.newrelic.com/",(()=>{var e={85:0,959:0};i.f.j=(t,r)=>{var n=i.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var a=new Promise(((r,i)=>n=e[t]=[r,i]));r.push(n[2]=a);var o=i.p+i.u(t),s=new Error;i.l(o,(r=>{if(i.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var a=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+a+": "+o+")",s.name="ChunkLoadError",s.type=a,s.request=o,n[1](s)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,a,[o,s,c]=r,u=0;if(o.some((t=>0!==e[t]))){for(n in s)i.o(s,n)&&(i.m[n]=s[n]);if(c)c(i)}for(t&&t(r);u<o.length;u++)a=o[u],i.o(e,a)&&e[a]&&e[a][0](),e[a]=0},r=self["webpackChunk:NRBA-1.295.0.PROD"]=self["webpackChunk:NRBA-1.295.0.PROD"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";i(8374);var e=i(9566),t=i(1741);class r extends t.W{agentIdentifier=(0,e.LA)(16)}var n=i(860);const a=Object.values(n.K7);var o=i(5205);var s=i(9908),c=i(1863),u=i(4261),d=i(3241),l=i(944),f=i(5701),h=i(8154);function p(e,t,i,a){const o=a||i;!o||o[e]&&o[e]!==r.prototype[e]||(o[e]=function(){(0,s.p)(h.xV,["API/"+e+"/called"],void 0,n.K7.metrics,i.ee),(0,d.W)({agentIdentifier:i.agentIdentifier,drained:!!f.B?.[i.agentIdentifier],type:"data",name:"api",feature:u.Pl+e,data:{}});try{return t.apply(this,arguments)}catch(e){(0,l.R)(23,e)}})}function g(e,t,r,n,i){const a=e.info;null===r?delete a.jsAttributes[t]:a.jsAttributes[t]=r,(i||null===r)&&(0,s.p)(u.Pl+n,[(0,c.t)(),t,r],void 0,"session",e.ee)}var m=i(1687),v=i(4234),b=i(5289),y=i(6154),R=i(5270),x=i(7767),w=i(6389);class T extends v.W{constructor(e,t){super(e.agentIdentifier,t),this.abortHandler=void 0,this.featAggregate=void 0,this.onAggregateImported=void 0,this.deferred=Promise.resolve(),!1===e.init[this.featureName].autoStart?this.deferred=new Promise(((t,r)=>{this.ee.on("manual-start-all",(0,w.J)((()=>{(0,m.Ak)(e.agentIdentifier,this.featureName),t()})))})):(0,m.Ak)(e.agentIdentifier,t)}importAggregator(e,t,r={}){if(this.featAggregate)return;let a;this.onAggregateImported=new Promise((e=>{a=e}));const o=async()=>{let o;await this.deferred;try{if((0,x.V)(e.init)){const{setupAgentSession:t}=await i.e(891).then(i.bind(i,2955));o=t(e)}}catch(e){(0,l.R)(20,e),this.ee.emit("internal-error",[e]),this.featureName===n.K7.sessionReplay&&this.abortHandler?.()}try{if(!this.#t(this.featureName,o,e.init))return(0,m.Ze)(this.agentIdentifier,this.featureName),void a(!1);const{Aggregate:n}=await t();this.featAggregate=new n(e,r),e.runtime.harvester.initializedAggregates.push(this.featAggregate),a(!0)}catch(e){(0,l.R)(34,e),this.abortHandler?.(),(0,m.Ze)(this.agentIdentifier,this.featureName,!0),a(!1),this.ee&&this.ee.abort()}};y.RI?(0,b.GG)((()=>o()),!0):o()}#t(e,t,r){switch(e){case n.K7.sessionReplay:return(0,R.SR)(r)&&!!t;case n.K7.sessionTrace:return!!t;default:return!0}}}var _=i(6630),S=i(2614);class A extends T{static featureName=_.T;constructor(e){var t;super(e,_.T),this.setupInspectionEvents(e.agentIdentifier),t=e,p(u.Fw,(function(e,r){"string"==typeof e&&("/"!==e.charAt(0)&&(e="/"+e),t.runtime.customTransaction=(r||"http://custom.transaction")+e,(0,s.p)(u.Pl+u.Fw,[(0,c.t)()],void 0,void 0,t.ee))}),t),this.ee.on("api-send-rum",((e,t)=>(0,s.p)("send-rum",[e,t],void 0,this.featureName,this.ee))),this.importAggregator(e,(()=>i.e(891).then(i.bind(i,7550))))}setupInspectionEvents(e){const t=(t,r)=>{t&&(0,d.W)({agentIdentifier:e,timeStamp:t.timeStamp,loaded:"complete"===t.target.readyState,type:"window",name:r,data:t.target.location+""})};(0,b.sB)((e=>{t(e,"DOMContentLoaded")})),(0,b.GG)((e=>{t(e,"load")})),(0,b.Qr)((e=>{t(e,"navigate")})),this.ee.on(S.tS.UPDATE,((t,r)=>{(0,d.W)({agentIdentifier:e,type:"lifecycle",name:"session",data:r})}))}}var E=i(384);var O=i(2843),N=i(3878),I=i(782);class P extends T{static featureName=I.T;constructor(e){super(e,I.T),y.RI&&((0,O.u)((()=>(0,s.p)("docHidden",[(0,c.t)()],void 0,I.T,this.ee)),!0),(0,N.sp)("pagehide",(()=>(0,s.p)("winPagehide",[(0,c.t)()],void 0,I.T,this.ee))),this.importAggregator(e,(()=>i.e(891).then(i.bind(i,9917)))))}}class j extends T{static featureName=h.TZ;constructor(e){super(e,h.TZ),y.RI&&document.addEventListener("securitypolicyviolation",(e=>{(0,s.p)(h.xV,["Generic/CSPViolation/Detected"],void 0,this.featureName,this.ee)})),this.importAggregator(e,(()=>i.e(891).then(i.bind(i,8351))))}}var k=i(6774),C=i(3304);class H{constructor(e,t,r,n,i){this.name="UncaughtError",this.message="string"==typeof e?e:(0,C.A)(e),this.sourceURL=t,this.line=r,this.column=n,this.__newrelic=i}}function L(e){return K(e)?e:new H(void 0!==e?.message?e.message:e,e?.filename||e?.sourceURL,e?.lineno||e?.line,e?.colno||e?.col,e?.__newrelic,e?.cause)}function M(e){const t="Unhandled Promise Rejection: ";if(!e?.reason)return;if(K(e.reason)){try{e.reason.message.startsWith(t)||(e.reason.message=t+e.reason.message)}catch(e){}return L(e.reason)}const r=L(e.reason);return(r.message||"").startsWith(t)||(r.message=t+r.message),r}function D(e){if(e.error instanceof SyntaxError&&!/:\d+$/.test(e.error.stack?.trim())){const t=new H(e.message,e.filename,e.lineno,e.colno,e.error.__newrelic,e.cause);return t.name=SyntaxError.name,t}return K(e.error)?e.error:L(e)}function K(e){return e instanceof Error&&!!e.stack}function F(e,t,r,i,a=(0,c.t)()){"string"==typeof e&&(e=new Error(e)),(0,s.p)("err",[e,a,!1,t,r.runtime.isRecording,void 0,i],void 0,n.K7.jserrors,r.ee)}var W=i(3496),B=i(993),U=i(3785);function G(e,{customAttributes:t={},level:r=B.p_.INFO}={},n,i,a=(0,c.t)()){(0,U.R)(n.ee,e,t,r,i,a)}function V(e,t,r,i,a=(0,c.t)()){(0,s.p)(u.Pl+u.hG,[a,e,t,i],void 0,n.K7.genericEvents,r.ee)}function z(e){p(u.eY,(function(t){return function(e,t){const r={};let i,a;(0,l.R)(54,"newrelic.register"),e.init.api.allow_registered_children||(i=()=>(0,l.R)(55));t&&(0,W.I)(t)||(i=()=>(0,l.R)(48,t));const o={addPageAction:(n,i={})=>{u(V,[n,{...r,...i},e],t)},log:(n,i={})=>{u(G,[n,{...i,customAttributes:{...r,...i.customAttributes||{}}},e],t)},noticeError:(n,i={})=>{u(F,[n,{...r,...i},e],t)},setApplicationVersion:e=>{r["application.version"]=e},setCustomAttribute:(e,t)=>{r[e]=t},setUserId:e=>{r["enduser.id"]=e},metadata:{customAttributes:r,target:t,get connected(){return a||Promise.reject(new Error("Failed to connect"))}}};i?i():a=new Promise(((n,i)=>{try{const a=e.runtime?.entityManager;let s=!!a?.get().entityGuid,c=a?.getEntityGuidFor(t.licenseKey,t.applicationID),u=!!c;if(s&&u)t.entityGuid=c,n(o);else{const d=setTimeout((()=>i(new Error("Failed to connect - Timeout"))),15e3);function l(r){(0,W.A)(r,e)?s||=!0:t.licenseKey===r.licenseKey&&t.applicationID===r.applicationID&&(u=!0,t.entityGuid=r.entityGuid),s&&u&&(clearTimeout(d),e.ee.removeEventListener("entity-added",l),n(o))}e.ee.emit("api-send-rum",[r,t]),e.ee.on("entity-added",l)}}catch(f){i(f)}}));const u=async(t,r,o)=>{if(i)return i();const u=(0,c.t)();(0,s.p)(h.xV,["API/register/".concat(t.name,"/called")],void 0,n.K7.metrics,e.ee);try{await a;const n=e.init.api.duplicate_registered_data;(!0===n||Array.isArray(n)&&n.includes(o.entityGuid))&&t(...r,void 0,u),t(...r,o.entityGuid,u)}catch(e){(0,l.R)(50,e)}};return o}(e,t)}),e)}class Z extends T{static featureName=k.T;constructor(e){var t;super(e,k.T),t=e,p(u.o5,((e,r)=>F(e,r,t)),t),function(e){p(u.bt,(function(t){e.runtime.onerror=t}),e)}(e),function(e){let t=0;p(u.k6,(function(e,r){++t>10||(this.runtime.releaseIds[e.slice(-200)]=(""+r).slice(-200))}),e)}(e),z(e);try{this.removeOnAbort=new AbortController}catch(e){}this.ee.on("internal-error",((t,r)=>{this.abortHandler&&(0,s.p)("ierr",[L(t),(0,c.t)(),!0,{},e.runtime.isRecording,r],void 0,this.featureName,this.ee)})),y.gm.addEventListener("unhandledrejection",(t=>{this.abortHandler&&(0,s.p)("err",[M(t),(0,c.t)(),!1,{unhandledPromiseRejection:1},e.runtime.isRecording],void 0,this.featureName,this.ee)}),(0,N.jT)(!1,this.removeOnAbort?.signal)),y.gm.addEventListener("error",(t=>{this.abortHandler&&(0,s.p)("err",[D(t),(0,c.t)(),!1,{},e.runtime.isRecording],void 0,this.featureName,this.ee)}),(0,N.jT)(!1,this.removeOnAbort?.signal)),this.abortHandler=this.#r,this.importAggregator(e,(()=>i.e(891).then(i.bind(i,2176))))}#r(){this.removeOnAbort?.abort(),this.abortHandler=void 0}}var q=i(8990);let X=1;function Y(e){const t=typeof e;return!e||"object"!==t&&"function"!==t?-1:e===y.gm?0:(0,q.I)(e,"nr@id",(function(){return X++}))}function Q(e){if("string"==typeof e&&e.length)return e.length;if("object"==typeof e){if("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer&&e.byteLength)return e.byteLength;if("undefined"!=typeof Blob&&e instanceof Blob&&e.size)return e.size;if(!("undefined"!=typeof FormData&&e instanceof FormData))try{return(0,C.A)(e).length}catch(e){return}}}var J=i(8139),ee=i(7836),te=i(3434);const re={},ne=["open","send"];function ie(e){var t=e||ee.ee;const r=function(e){return(e||ee.ee).get("xhr")}(t);if(void 0===y.gm.XMLHttpRequest)return r;if(re[r.debugId]++)return r;re[r.debugId]=1,(0,J.u)(t);var n=(0,te.YM)(r),i=y.gm.XMLHttpRequest,a=y.gm.MutationObserver,o=y.gm.Promise,s=y.gm.setInterval,c="readystatechange",u=["onload","onerror","onabort","onloadstart","onloadend","onprogress","ontimeout"],d=[],f=y.gm.XMLHttpRequest=function(e){const t=new i(e),a=r.context(t);try{r.emit("new-xhr",[t],a),t.addEventListener(c,(o=a,function(){var e=this;e.readyState>3&&!o.resolved&&(o.resolved=!0,r.emit("xhr-resolved",[],e)),n.inPlace(e,u,"fn-",b)}),(0,N.jT)(!1))}catch(e){(0,l.R)(15,e);try{r.emit("internal-error",[e])}catch(e){}}var o;return t};function h(e,t){n.inPlace(t,["onreadystatechange"],"fn-",b)}if(function(e,t){for(var r in e)t[r]=e[r]}(i,f),f.prototype=i.prototype,n.inPlace(f.prototype,ne,"-xhr-",b),r.on("send-xhr-start",(function(e,t){h(e,t),function(e){d.push(e),a&&(p?p.then(v):s?s(v):(g=-g,m.data=g))}(t)})),r.on("open-xhr-start",h),a){var p=o&&o.resolve();if(!s&&!o){var g=1,m=document.createTextNode(g);new a(v).observe(m,{characterData:!0})}}else t.on("fn-end",(function(e){e[0]&&e[0].type===c||v()}));function v(){for(var e=0;e<d.length;e++)h(0,d[e]);d.length&&(d=[])}function b(e,t){return t}return r}var ae="fetch-",oe=ae+"body-",se=["arrayBuffer","blob","json","text","formData"],ce=y.gm.Request,ue=y.gm.Response,de="prototype";const le={};function fe(e){const t=function(e){return(e||ee.ee).get("fetch")}(e);if(!(ce&&ue&&y.gm.fetch))return t;if(le[t.debugId]++)return t;function r(e,r,n){var i=e[r];"function"==typeof i&&(e[r]=function(){var e,r=[...arguments],a={};t.emit(n+"before-start",[r],a),a[ee.P]&&a[ee.P].dt&&(e=a[ee.P].dt);var o=i.apply(this,r);return t.emit(n+"start",[r,e],o),o.then((function(e){return t.emit(n+"end",[null,e],o),e}),(function(e){throw t.emit(n+"end",[e],o),e}))})}return le[t.debugId]=1,se.forEach((e=>{r(ce[de],e,oe),r(ue[de],e,oe)})),r(y.gm,"fetch",ae),t.on(ae+"end",(function(e,r){var n=this;if(r){var i=r.headers.get("content-length");null!==i&&(n.rxSize=i),t.emit(ae+"done",[null,r],n)}else t.emit(ae+"done",[e],n)})),t}var he=i(7485);class pe{constructor(e){this.agentRef=e}generateTracePayload(t){const r=this.agentRef.loader_config;if(!this.shouldGenerateTrace(t)||!r)return null;var n=(r.accountID||"").toString()||null,i=(r.agentID||"").toString()||null,a=(r.trustKey||"").toString()||null;if(!n||!i)return null;var o=(0,e.ZF)(),s=(0,e.el)(),c=Date.now(),u={spanId:o,traceId:s,timestamp:c};return(t.sameOrigin||this.isAllowedOrigin(t)&&this.useTraceContextHeadersForCors())&&(u.traceContextParentHeader=this.generateTraceContextParentHeader(o,s),u.traceContextStateHeader=this.generateTraceContextStateHeader(o,c,n,i,a)),(t.sameOrigin&&!this.excludeNewrelicHeader()||!t.sameOrigin&&this.isAllowedOrigin(t)&&this.useNewrelicHeaderForCors())&&(u.newrelicHeader=this.generateTraceHeader(o,s,c,n,i,a)),u}generateTraceContextParentHeader(e,t){return"00-"+t+"-"+e+"-01"}generateTraceContextStateHeader(e,t,r,n,i){return i+"@nr=0-1-"+r+"-"+n+"-"+e+"----"+t}generateTraceHeader(e,t,r,n,i,a){if(!("function"==typeof y.gm?.btoa))return null;var o={v:[0,1],d:{ty:"Browser",ac:n,ap:i,id:e,tr:t,ti:r}};return a&&n!==a&&(o.d.tk=a),btoa((0,C.A)(o))}shouldGenerateTrace(e){return this.agentRef.init?.distributed_tracing?.enabled&&this.isAllowedOrigin(e)}isAllowedOrigin(e){var t=!1;const r=this.agentRef.init?.distributed_tracing;if(e.sameOrigin)t=!0;else if(r?.allowed_origins instanceof Array)for(var n=0;n<r.allowed_origins.length;n++){var i=(0,he.D)(r.allowed_origins[n]);if(e.hostname===i.hostname&&e.protocol===i.protocol&&e.port===i.port){t=!0;break}}return t}excludeNewrelicHeader(){var e=this.agentRef.init?.distributed_tracing;return!!e&&!!e.exclude_newrelic_header}useNewrelicHeaderForCors(){var e=this.agentRef.init?.distributed_tracing;return!!e&&!1!==e.cors_use_newrelic_header}useTraceContextHeadersForCors(){var e=this.agentRef.init?.distributed_tracing;return!!e&&!!e.cors_use_tracecontext_headers}}var ge=i(9300),me=i(7295),ve=["load","error","abort","timeout"],be=ve.length,ye=(0,E.dV)().o.REQ,Re=(0,E.dV)().o.XHR;const xe="X-NewRelic-App-Data";class we extends T{static featureName=ge.T;constructor(e){super(e,ge.T),this.dt=new pe(e),this.handler=(e,t,r,n)=>(0,s.p)(e,t,r,n,this.ee);try{const e={xmlhttprequest:"xhr",fetch:"fetch",beacon:"beacon"};y.gm?.performance?.getEntriesByType("resource").forEach((t=>{if(t.initiatorType in e&&0!==t.responseStatus){const r={status:t.responseStatus},i={rxSize:t.transferSize,duration:Math.floor(t.duration),cbTime:0};Te(r,t.name),this.handler("xhr",[r,i,t.startTime,t.responseEnd,e[t.initiatorType]],void 0,n.K7.ajax)}}))}catch(e){}fe(this.ee),ie(this.ee),function(e,t,r,i){function a(e){var t=this;t.totalCbs=0,t.called=0,t.cbTime=0,t.end=_,t.ended=!1,t.xhrGuids={},t.lastSize=null,t.loadCaptureCalled=!1,t.params=this.params||{},t.metrics=this.metrics||{},e.addEventListener("load",(function(r){S(t,e)}),(0,N.jT)(!1)),y.lR||e.addEventListener("progress",(function(e){t.lastSize=e.loaded}),(0,N.jT)(!1))}function o(e){this.params={method:e[0]},Te(this,e[1]),this.metrics={}}function u(t,r){e.loader_config.xpid&&this.sameOrigin&&r.setRequestHeader("X-NewRelic-ID",e.loader_config.xpid);var n=i.generateTracePayload(this.parsedOrigin);if(n){var a=!1;n.newrelicHeader&&(r.setRequestHeader("newrelic",n.newrelicHeader),a=!0),n.traceContextParentHeader&&(r.setRequestHeader("traceparent",n.traceContextParentHeader),n.traceContextStateHeader&&r.setRequestHeader("tracestate",n.traceContextStateHeader),a=!0),a&&(this.dt=n)}}function d(e,r){var n=this.metrics,i=e[0],a=this;if(n&&i){var o=Q(i);o&&(n.txSize=o)}this.startTime=(0,c.t)(),this.body=i,this.listener=function(e){try{"abort"!==e.type||a.loadCaptureCalled||(a.params.aborted=!0),("load"!==e.type||a.called===a.totalCbs&&(a.onloadCalled||"function"!=typeof r.onload)&&"function"==typeof a.end)&&a.end(r)}catch(e){try{t.emit("internal-error",[e])}catch(e){}}};for(var s=0;s<be;s++)r.addEventListener(ve[s],this.listener,(0,N.jT)(!1))}function l(e,t,r){this.cbTime+=e,t?this.onloadCalled=!0:this.called+=1,this.called!==this.totalCbs||!this.onloadCalled&&"function"==typeof r.onload||"function"!=typeof this.end||this.end(r)}function f(e,t){var r=""+Y(e)+!!t;this.xhrGuids&&!this.xhrGuids[r]&&(this.xhrGuids[r]=!0,this.totalCbs+=1)}function p(e,t){var r=""+Y(e)+!!t;this.xhrGuids&&this.xhrGuids[r]&&(delete this.xhrGuids[r],this.totalCbs-=1)}function g(){this.endTime=(0,c.t)()}function m(e,r){r instanceof Re&&"load"===e[0]&&t.emit("xhr-load-added",[e[1],e[2]],r)}function v(e,r){r instanceof Re&&"load"===e[0]&&t.emit("xhr-load-removed",[e[1],e[2]],r)}function b(e,t,r){t instanceof Re&&("onload"===r&&(this.onload=!0),("load"===(e[0]&&e[0].type)||this.onload)&&(this.xhrCbStart=(0,c.t)()))}function R(e,r){this.xhrCbStart&&t.emit("xhr-cb-time",[(0,c.t)()-this.xhrCbStart,this.onload,r],r)}function x(e){var t,r=e[1]||{};if("string"==typeof e[0]?0===(t=e[0]).length&&y.RI&&(t=""+y.gm.location.href):e[0]&&e[0].url?t=e[0].url:y.gm?.URL&&e[0]&&e[0]instanceof URL?t=e[0].href:"function"==typeof e[0].toString&&(t=e[0].toString()),"string"==typeof t&&0!==t.length){t&&(this.parsedOrigin=(0,he.D)(t),this.sameOrigin=this.parsedOrigin.sameOrigin);var n=i.generateTracePayload(this.parsedOrigin);if(n&&(n.newrelicHeader||n.traceContextParentHeader))if(e[0]&&e[0].headers)s(e[0].headers,n)&&(this.dt=n);else{var a={};for(var o in r)a[o]=r[o];a.headers=new Headers(r.headers||{}),s(a.headers,n)&&(this.dt=n),e.length>1?e[1]=a:e.push(a)}}function s(e,t){var r=!1;return t.newrelicHeader&&(e.set("newrelic",t.newrelicHeader),r=!0),t.traceContextParentHeader&&(e.set("traceparent",t.traceContextParentHeader),t.traceContextStateHeader&&e.set("tracestate",t.traceContextStateHeader),r=!0),r}}function w(e,t){this.params={},this.metrics={},this.startTime=(0,c.t)(),this.dt=t,e.length>=1&&(this.target=e[0]),e.length>=2&&(this.opts=e[1]);var r,n=this.opts||{},i=this.target;"string"==typeof i?r=i:"object"==typeof i&&i instanceof ye?r=i.url:y.gm?.URL&&"object"==typeof i&&i instanceof URL&&(r=i.href),Te(this,r);var a=(""+(i&&i instanceof ye&&i.method||n.method||"GET")).toUpperCase();this.params.method=a,this.body=n.body,this.txSize=Q(n.body)||0}function T(e,t){if(this.endTime=(0,c.t)(),this.params||(this.params={}),(0,me.iW)(this.params))return;let i;this.params.status=t?t.status:0,"string"==typeof this.rxSize&&this.rxSize.length>0&&(i=+this.rxSize);const a={txSize:this.txSize,rxSize:i,duration:(0,c.t)()-this.startTime};r("xhr",[this.params,a,this.startTime,this.endTime,"fetch"],this,n.K7.ajax)}function _(e){const t=this.params,i=this.metrics;if(!this.ended){this.ended=!0;for(let t=0;t<be;t++)e.removeEventListener(ve[t],this.listener,!1);t.aborted||(0,me.iW)(t)||(i.duration=(0,c.t)()-this.startTime,this.loadCaptureCalled||4!==e.readyState?null==t.status&&(t.status=0):S(this,e),i.cbTime=this.cbTime,r("xhr",[t,i,this.startTime,this.endTime,"xhr"],this,n.K7.ajax))}}function S(e,r){e.params.status=r.status;var i=function(e,t){var r=e.responseType;return"json"===r&&null!==t?t:"arraybuffer"===r||"blob"===r||"json"===r?Q(e.response):"text"===r||""===r||void 0===r?Q(e.responseText):void 0}(r,e.lastSize);if(i&&(e.metrics.rxSize=i),e.sameOrigin&&r.getAllResponseHeaders().indexOf(xe)>=0){var a=r.getResponseHeader(xe);a&&((0,s.p)(h.rs,["Ajax/CrossApplicationTracing/Header/Seen"],void 0,n.K7.metrics,t),e.params.cat=a.split(", ").pop())}e.loadCaptureCalled=!0}t.on("new-xhr",a),t.on("open-xhr-start",o),t.on("open-xhr-end",u),t.on("send-xhr-start",d),t.on("xhr-cb-time",l),t.on("xhr-load-added",f),t.on("xhr-load-removed",p),t.on("xhr-resolved",g),t.on("addEventListener-end",m),t.on("removeEventListener-end",v),t.on("fn-end",R),t.on("fetch-before-start",x),t.on("fetch-start",w),t.on("fn-start",b),t.on("fetch-done",T)}(e,this.ee,this.handler,this.dt),this.importAggregator(e,(()=>i.e(891).then(i.bind(i,3845))))}}function Te(e,t){var r=(0,he.D)(t),n=e.params||e;n.hostname=r.hostname,n.port=r.port,n.protocol=r.protocol,n.host=r.hostname+":"+r.port,n.pathname=r.pathname,e.parsedOrigin=r,e.sameOrigin=r.sameOrigin}const _e={},Se=["pushState","replaceState"];function Ae(e){const t=function(e){return(e||ee.ee).get("history")}(e);return!y.RI||_e[t.debugId]++||(_e[t.debugId]=1,(0,te.YM)(t).inPlace(window.history,Se,"-")),t}var Ee=i(3738);function Oe(e){p(u.BL,(function(t=Date.now()){const r=t-y.WN;r<0&&(0,l.R)(62,t),(0,s.p)(h.XG,[u.BL,{time:r}],void 0,n.K7.metrics,e.ee),e.addToTrace({name:u.BL,start:t,origin:"nr"}),(0,s.p)(u.Pl+u.hG,[r,u.BL],void 0,n.K7.genericEvents,e.ee)}),e)}const{He:Ne,bD:Ie,d3:Pe,Kp:je,TZ:ke,Lc:Ce,uP:He,Rz:Le}=Ee;class Me extends T{static featureName=ke;constructor(e){var t;super(e,ke),t=e,p(u.U2,(function(e){if(!(e&&"object"==typeof e&&e.name&&e.start))return;const r={n:e.name,s:e.start-y.WN,e:(e.end||e.start)-y.WN,o:e.origin||"",t:"api"};r.s<0||r.e<0||r.e<r.s?(0,l.R)(61,{start:r.s,end:r.e}):(0,s.p)("bstApi",[r],void 0,n.K7.sessionTrace,t.ee)}),t),Oe(e);if(!(0,x.V)(e.init))return void this.deregisterDrain();const r=this.ee;let a;Ae(r),this.eventsEE=(0,J.u)(r),this.eventsEE.on(He,(function(e,t){this.bstStart=(0,c.t)()})),this.eventsEE.on(Ce,(function(e,t){(0,s.p)("bst",[e[0],t,this.bstStart,(0,c.t)()],void 0,n.K7.sessionTrace,r)})),r.on(Le+Pe,(function(e){this.time=(0,c.t)(),this.startPath=location.pathname+location.hash})),r.on(Le+je,(function(e){(0,s.p)("bstHist",[location.pathname+location.hash,this.startPath,this.time],void 0,n.K7.sessionTrace,r)}));try{a=new PerformanceObserver((e=>{const t=e.getEntries();(0,s.p)(Ne,[t],void 0,n.K7.sessionTrace,r)})),a.observe({type:Ie,buffered:!0})}catch(e){}this.importAggregator(e,(()=>i.e(891).then(i.bind(i,575))),{resourceObserver:a})}}var De=i(6344);class Ke extends T{static featureName=De.TZ;#n;#i;constructor(e){var t;let r;super(e,De.TZ),t=e,p(u.CH,(function(){(0,s.p)(u.CH,[],void 0,n.K7.sessionReplay,t.ee)}),t),function(e){p(u.Tb,(function(){(0,s.p)(u.Tb,[],void 0,n.K7.sessionReplay,e.ee)}),e)}(e),this.#i=e;try{r=JSON.parse(localStorage.getItem("".concat(S.H3,"_").concat(S.uh)))}catch(e){}(0,R.SR)(e.init)&&this.ee.on(De.G4.RECORD,(()=>this.#a())),this.#o(r)?(this.#n=r?.sessionReplayMode,this.#s()):this.importAggregator(this.#i,(()=>i.e(891).then(i.bind(i,6167)))),this.ee.on("err",(e=>{this.#i.runtime.isRecording&&(this.errorNoticed=!0,(0,s.p)(De.G4.ERROR_DURING_REPLAY,[e],void 0,this.featureName,this.ee))}))}#o(e){return e&&(e.sessionReplayMode===S.g.FULL||e.sessionReplayMode===S.g.ERROR)||(0,R.Aw)(this.#i.init)}#c=!1;async#s(e){if(!this.#c){this.#c=!0;try{const{Recorder:t}=await Promise.all([i.e(891),i.e(222)]).then(i.bind(i,8589));this.recorder??=new t({mode:this.#n,agentIdentifier:this.agentIdentifier,trigger:e,ee:this.ee,agentRef:this.#i}),this.recorder.startRecording(),this.abortHandler=this.recorder.stopRecording}catch(e){this.parent.ee.emit("internal-error",[e])}this.importAggregator(this.#i,(()=>i.e(891).then(i.bind(i,6167))),{recorder:this.recorder,errorNoticed:this.errorNoticed})}}#a(){this.featAggregate?this.featAggregate.mode!==S.g.FULL&&this.featAggregate.initializeRecording(S.g.FULL,!0):(this.#n=S.g.FULL,this.#s(De.Qb.API),this.recorder&&this.recorder.parent.mode!==S.g.FULL&&(this.recorder.parent.mode=S.g.FULL,this.recorder.stopRecording(),this.recorder.startRecording(),this.abortHandler=this.recorder.stopRecording))}}var Fe=i(3333);class We extends T{static featureName=Fe.TZ;constructor(e){super(e,Fe.TZ);const t=[e.init.page_action.enabled,e.init.performance.capture_marks,e.init.performance.capture_measures,e.init.user_actions.enabled,e.init.performance.resources.enabled];var r;if(r=e,p(u.hG,((e,t)=>V(e,t,r)),r),function(e){p(u.fF,(function(){(0,s.p)(u.Pl+u.fF,[(0,c.t)(),...arguments],void 0,n.K7.genericEvents,e.ee)}),e)}(e),Oe(e),z(e),function(e){p(u.V1,(function(t,r){const i=(0,c.t)(),{start:a,end:o,customAttributes:d}=r||{},f={customAttributes:d||{}};if("object"!=typeof f.customAttributes||"string"!=typeof t||0===t.length)return void(0,l.R)(57);const h=(e,t)=>null==e?t:"number"==typeof e?e:e instanceof PerformanceMark?e.startTime:Number.NaN;if(f.start=h(a,0),f.end=h(o,i),Number.isNaN(f.start)||Number.isNaN(f.end))(0,l.R)(57);else{if(f.duration=f.end-f.start,!(f.duration<0))return(0,s.p)(u.Pl+u.V1,[f,t],void 0,n.K7.genericEvents,e.ee),f;(0,l.R)(58)}}),e)}(e),y.RI&&(e.init.user_actions.enabled&&(Fe.Zp.forEach((e=>(0,N.sp)(e,(e=>(0,s.p)("ua",[e],void 0,this.featureName,this.ee)),!0))),Fe.qN.forEach((e=>{const t=(0,w.s)((e=>{(0,s.p)("ua",[e],void 0,this.featureName,this.ee)}),500,{leading:!0});(0,N.sp)(e,t)}))),e.init.performance.resources.enabled&&y.gm.PerformanceObserver?.supportedEntryTypes.includes("resource"))){new PerformanceObserver((e=>{e.getEntries().forEach((e=>{(0,s.p)("browserPerformance.resource",[e],void 0,this.featureName,this.ee)}))})).observe({type:"resource",buffered:!0})}t.some((e=>e))?this.importAggregator(e,(()=>i.e(891).then(i.bind(i,8019)))):this.deregisterDrain()}}var Be=i(2646);const Ue=new Map;function Ge(e,t,r,n){if("object"!=typeof t||!t||"string"!=typeof r||!r||"function"!=typeof t[r])return(0,l.R)(29);const i=function(e){return(e||ee.ee).get("logger")}(e),a=(0,te.YM)(i),o=new Be.y(ee.P);o.level=n.level,o.customAttributes=n.customAttributes;const s=t[r]?.[te.Jt]||t[r];return Ue.set(s,o),a.inPlace(t,[r],"wrap-logger-",(()=>Ue.get(s))),i}var Ve=i(1910);class ze extends T{static featureName=B.TZ;constructor(e){var t;super(e,B.TZ),t=e,p(u.$9,((e,r)=>G(e,r,t)),t),function(e){p(u.Wb,((t,r,{customAttributes:n={},level:i=B.p_.INFO}={})=>{Ge(e.ee,t,r,{customAttributes:n,level:i})}),e)}(e),z(e);const r=this.ee;["log","error","warn","info","debug","trace"].forEach((e=>{(0,Ve.i)(y.gm.console[e]),Ge(r,y.gm.console,e,{level:"log"===e?"info":e})})),this.ee.on("wrap-logger-end",(function([e]){const{level:t,customAttributes:n}=this;(0,U.R)(r,e,n,t)})),this.importAggregator(e,(()=>i.e(891).then(i.bind(i,5288))))}}new class extends r{constructor(e){var t;(super(),y.gm)?(this.features={},(0,E.bQ)(this.agentIdentifier,this),this.desiredFeatures=new Set(e.features||[]),this.desiredFeatures.add(A),this.runSoftNavOverSpa=[...this.desiredFeatures].some((e=>e.featureName===n.K7.softNav)),(0,o.j)(this,e,e.loaderType||"agent"),t=this,p(u.cD,(function(e,r,n=!1){if("string"==typeof e){if(["string","number","boolean"].includes(typeof r)||null===r)return g(t,e,r,u.cD,n);(0,l.R)(40,typeof r)}else(0,l.R)(39,typeof e)}),t),function(e){p(u.Dl,(function(t){if("string"==typeof t||null===t)return g(e,"enduser.id",t,u.Dl,!0);(0,l.R)(41,typeof t)}),e)}(this),function(e){p(u.nb,(function(t){if("string"==typeof t||null===t)return g(e,"application.version",t,u.nb,!1);(0,l.R)(42,typeof t)}),e)}(this),function(e){p(u.d3,(function(){e.ee.emit("manual-start-all")}),e)}(this),this.run()):(0,l.R)(21)}get config(){return{info:this.info,init:this.init,loader_config:this.loader_config,runtime:this.runtime}}get api(){return this}run(){try{const e=function(e){const t={};return a.forEach((r=>{t[r]=!!e[r]?.enabled})),t}(this.init),t=[...this.desiredFeatures];t.sort(((e,t)=>n.P3[e.featureName]-n.P3[t.featureName])),t.forEach((t=>{if(!e[t.featureName]&&t.featureName!==n.K7.pageViewEvent)return;if(this.runSoftNavOverSpa&&t.featureName===n.K7.spa)return;if(!this.runSoftNavOverSpa&&t.featureName===n.K7.softNav)return;const r=function(e){switch(e){case n.K7.ajax:return[n.K7.jserrors];case n.K7.sessionTrace:return[n.K7.ajax,n.K7.pageViewEvent];case n.K7.sessionReplay:return[n.K7.sessionTrace];case n.K7.pageViewTiming:return[n.K7.pageViewEvent];default:return[]}}(t.featureName).filter((e=>!(e in this.features)));r.length>0&&(0,l.R)(36,{targetFeature:t.featureName,missingDependencies:r}),this.features[t.featureName]=new t(this)}))}catch(e){(0,l.R)(22,e);for(const e in this.features)this.features[e].abortHandler?.();const t=(0,E.Zm)();delete t.initializedAgents[this.agentIdentifier]?.features,delete this.sharedAggregator;return t.ee.get(this.agentIdentifier).abort(),!1}}}({features:[A,P,Me,Ke,we,j,Z,We,ze],loaderType:"pro"})})()})();</script>
<meta name="robots" content="noodp">
<meta name="MSSmartTagsPreventParsing" content="true">
<meta name="google-site-verification" content="sRcy8COOEVzfXadS3NzNsefhbSouqvlesgWngH5kKxs">
<meta name="csrf-param" content="authenticity_token" />
<meta name="csrf-token" content="Z+3SmVTvie9uKTqndR6+sXq94p1NRh5nHbVJkYwjCggCSufIdMsaxc/hdvoSVOwfFVegRsSK2FZBsny1pEK3Og==" />
<link rel="shortcut icon" type="image/x-icon" href="https://d1pspl52z5rk07.cloudfront.net/static/favicon.ico" />

		<meta name="fragment" content="!">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<meta name="description" ng-attr-content="{{meta.description}}" content="The GrabCAD Library offers millions of free CAD designs, CAD files, and 3D models. Join the GrabCAD Community today to gain access and download!">
		<base href="/">
		<meta name="prerender-status-code" content="{{meta.statusCode}}">
		<link rel="stylesheet" href="/static/gc-hoops-viewer-131/community/bundle.css">
		<script id="viewersrc" type="text/javascript" data-src="/static/gc-hoops-viewer-131/community/bundle.js"></script>
		<title ng-bind="title">Free CAD Designs, Files & 3D Models | The GrabCAD Community Library</title>
		<link rel="stylesheet" media="screen" href="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/application_community-b28f067b7e8b060f1ddfd96b589535dfb2fb6c827b4f5673a8653be400a798bc.css" />
		<link rel="stylesheet" media="screen" href="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/application_frontend/app-18925d101adeb8da258f123c51f5956b749b5f526df91ab447cbfb12d5b01fee.css" />
		
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-173TDQJFJ7"></script>
<script>
	window.dataLayer = window.dataLayer || [];
	function gtag(){dataLayer.push(arguments);}
	gtag('js', new Date());
	gtag('config', 'G-173TDQJFJ7');
</script>


		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
        crossorigin="anonymous">
</script>
		<script type='text/javascript'>
var Grabcad = Grabcad || {};

Grabcad.site = {
	community: 'https://grabcad.com',
	community_home: 'https://grabcad.com/home',
	help_center_badges: 'https://help.grabcad.com/article/52-badges-score',
	help_center_guidelines: 'https://help.grabcad.com/article/149-community-values-and-guidelines',
	shop: 'https://shop.grabcad.com',
	company_signup: 'https://login.grabcad.com/create_account'
};
Grabcad.mails = {
	info: '<EMAIL>',
	support: '<EMAIL>',
};

Grabcad.openToWork_profileIcon_white = 'https://d1pspl52z5rk07.cloudfront.net/assets/production/app/gc/shared/gc-shared-opentowork-profileicon-white-21a94fec4637037f09f77f4bb0f1dd85d46a7b8119372f7067c248da45f92396.svg';
Grabcad.asense = {
	modal_image: 'https://d1pspl52z5rk07.cloudfront.net/assets/production/app/gc/shared/asense/modalimage-f53d5e8e4f17c51f5e98011814415365534b15a4b19246de79999414e305e91f.png',
	info_icon: 'https://d1pspl52z5rk07.cloudfront.net/assets/production/app/gc/shared/asense/infoicon-21cf6b76c25d7b37bdf9e82b40ea4bc56362abd53afe3537b36cff6fd7277567.svg',
	horizontal_ad_format: 'horizontal',
};
Grabcad.defaultAvatar = 'https://d1pspl52z5rk07.cloudfront.net/assets/production/app/default/avatar-13e49413d14d7528c1dba3d70cb39957e4aa4b997dff5cf4cd6c89992da9aaa5.png';
Grabcad.Components = Grabcad.Components || {};

Grabcad.locale = 'en';
Grabcad.authenticated = false;
Grabcad.memberAvatar = '';


Grabcad.AttributeLimits = {
	Generic: {
		CONTENT_BODY: 50000,
		CONTENT_DESCRIPTION_LONG: 2500,
		ORGANISATION_NAME: 250,
		SEARCH: 2048,
		TAG: 60,
		TITLE: 250,
	},
	Legacy: {
		URL: 255,
	},
};
</script>

		  <script
    id="bizible-settings"
    type="text/javascript"
    src="https://cdn.bizible.com/scripts/bizible.js?account=stratasys.com"
    async="">
  </script>


		<link ng-if="relNextPrev.prev" rel="prev" href="{{relNextPrev.prev}}">
		<link ng-if="relNextPrev.next" rel="next" href="{{relNextPrev.next}}">
		<script type="text/javascript"> (function(c,l,a,r,i,t,y){ c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)}; t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i; y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y); })(window, document, "clarity", "script", "n55qnhg70c"); </script>
	</head>
	<body id="community_frontend-library_models"  ng-class="{'initial-load-completed': initialLoadCompleted}" class="community-frontend text-normal">
		<noscript><img src="/assets/community_frontend/track_no_js.png"><div class="noscript-warning">Please enable JavaScript to use GrabCAD</div></noscript>
		<div class="gc-shared-homepagev2modal__overlay hidden">
	<div class="gc-shared-homepagev2modal__wrapper">
		<div class="gc-shared-homepagev2modal__box">
			<img class="gc-shared-homepagev2modal__hidebutton" alt="Hide modal button" src="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/gc/shared/navicon/mobileNavButtonClose-e12499ee0504c21efa0a48408d113964d6c4f8f33e9ae44676837808f2320510.svg" />

			<div class="gc-shared-homepagev2modal__image-box">
				<img class="gc-shared-homepagev2modal__image" alt="GrabCAD Platform" width="320" height="200" src="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/gc/pages/homepage/hero/big-5e8eabd176a106bebf5f0bebf7a118d6a6f8fd60a0eef7706b9926f6adc6aaa1.svg" />
			</div>

			<div class="gc-shared-homepagev2modal__content">
				<div class="gc-shared-homepagev2modal__title">Learn about the GrabCAD Platform</div>
				<div class="gc-shared-homepagev2modal__text">Get to know GrabCAD as an open software platform for Additive Manufacturing</div>
				<a href="https://grabcad.com/home" class="gc-shared-homepagev2modal__ctabutton gc_web_el-button--primary gc_web_el-button--icon-right-white">Visit our new homepage</a>
			</div>
	  </div>
	</div>
</div>

		<header class="community" ng-non-bindable>
  <nav class="main">
    <div class="headerMainMenu">
      <ul class="app-menu">
	<li class="app-selection-menu hover-element">
		<a class="selected-app link-mobile-disabled" href="https://grabcad.com/dashboard">
			<span class="grabcad-logo grabcad-logo-community"></span>
			<span class="dropdown-icon">
				<svg width="10" height="11" viewBox="0 0 1024 1024">
					<path d="M1025 320q0 -26 -19 -45t-45 -19h-896q-26 0 -45 19t-19 45t19 45l448 448q19 19 45 19t45 -19l448 -448q19 -19 19 -45z" fill="#fff"/>
				</svg>
			</span>
		</a>
		<ul class="selection lightDropdown">
			<li>
				<a class="text-normal" target="_self" href="/home">
					Home<i class="gc-icon gc-icon-check-circle"></i>
</a>			</li>
			<li>
				<a class="text-normal " target="_self" href="/control">
					Control<i class="gc-icon gc-icon-check-circle"></i>
</a>			</li>
			<li>
				<a class="text-normal " target="_self" href="/shop">
					Shop<i class="gc-icon gc-icon-check-circle"></i>
</a>			</li>
			<li>
				<a class="text-normal" target="_self" href="/streamline-pro">
					Streamline Pro<i class="gc-icon gc-icon-check-circle"></i>
</a>			</li>
			<li>
				<a class="text-normal " target="_self" href="/featured-software-partners">
					Partner Program<i class="gc-icon gc-icon-check-circle"></i>
</a>			</li>
			<li>
				<a class="text-normal " target="_self" href="/print">
					Print<i class="gc-icon gc-icon-check-circle"></i>
</a>			</li>


			<li class="separator"></li>

			<li>
				<a class="text-normal active" href="https://grabcad.com/dashboard" target="_self">
					Community<i class="gc-icon gc-icon-check-circle"></i>
				</a>
			</li>

		</ul>
	</li>
</ul>

      <label for="showMenu" class="showMenu">
	<span class="menuIcon"><span></span></span>
</label>
<input type="checkbox" id="showMenu" role="button">
	<ul class="login-menu">
		<li>
			<a data-toggle="modal" href="https://grabcad.com/community/login" class="login" target="_self">
				Log in
			</a>
		</li>
	</ul>


      <ul class="menu">
        <li class="communityMenu">
          <ul>
            <li class="selected">
              <a target="_self" href="https://grabcad.com/library">
                <span>Library</span>
              </a>
            </li>
            <li >
              <a target="_self" href="https://grabcad.com/challenges">
                <span>Challenges</span>
              </a>
            </li>
            <li class="communityMenu__groups ">
              <a target="_self" href="https://grabcad.com/groups">
                <span>Groups</span>
              </a>
            </li>
            <li class="">
              <a target="_self" href="https://grabcad.com/questions">
                <span>Questions</span>
              </a>
            </li>
            <li >
              <a target="_self" href="https://grabcad.com/tutorials">
                <span>Tutorials</span>
              </a>
            </li>
            <li >
              <a target="_self" href="https://grabcad.com/engineers">
                <span>Engineers</span>
              </a>
            </li>
          </ul>
        </li>
        <li class="rightMenu">
          <ul>
            <li class="staticMenu">
              <ul>
                <li>
                  <a target="_self" href="https://blog.grabcad.com">
                    <span>Blog</span>
                  </a>
                </li>
              </ul>
            </li>
            <li class="personalMenu logged-out">
              <ul>


                <li class="account hover-element ">
                    <ul class="login">
                      <li>
                        <a target="_self" href="/community/login?return=true" class="login">Log in</a>
                      </li>
                    </ul>
                </li>
              </ul>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </nav>
</header>


		<div id="communityReportApp" ng-non-bindable>
			<frontend-alerts></frontend-alerts>

			
			<component :is="activeModal"
				:modal-params="modalParams"
				@cancel="deactivateModal">
			</component>
		</div>

		<div class="blank" ng-class="{'initial-load-completed': initialLoadCompleted}"></div>

		

		<div class="alerts">
			<community-alert class="community-alert" ng-repeat="alert in alerts track by $index"></community-alert>
		</div>

		<div id="communityadsApp" ng-non-bindable>
			<gc-adsense-modal></gc-adsense-modal>
		</div>

		<div class="contentWrapper" ui-view></div>
			
	  <div id="signInModal" v-cloak>
    <transition name="wrap-transition">
      <div class="sign-in-modal__wrap" :class="{'sign-in-modal__wrap--permanent': permanent}" v-if="active">
        <div class="sign-in-modal__wrap--backdrop" v-if="!permanent" @click="close"></div>
        <div class="sign-in-modal__modal">
          <div class="sign-in-modal__header">
            <h4 class="sign-in-modal__title" v-if="state !== 'forgotPassword'">
              Sign In or Create Account
            </h4>

            <h4 class="sign-in-modal__title" v-if="state === 'forgotPassword'" v-cloak>
              Forgot password
            </h4>

            <a href="#" class="sign-in-modal__close" @click.prevent="close" v-if="!permanent">&times;</a>
          </div>
          <transition name="sign-in-modal__transition" mode="out-in">
            <div v-if="state === 'login'" :class="isWorkbenchLogin ? 'sign-in-modal__body-collapsed' : 'sign-in-modal__body'" :key="0">
              <form @submit.prevent="login" method="post" id="login_form">
                <div class="sign-in__input-block">
                  <i class="fas fa-envelope sign-in__input-icon"></i>

                  <input type="email"
                    v-model="email"
                    name="member[email]"
                    placeholder="Enter email"
                    class="sign-in__input sign-in__input--email"
                    autocomplete="off">
                </div>

                <div class="sign-in__input-wrap">
                  <div class="sign-in__input-block">
                    <i class="fas fa-lock sign-in__input-icon"></i>
                    <input type="password"
                      v-model="password"
                      placeholder="Enter password"
                      name="member[password]"
                      class="sign-in__input sign-in__input--password"
                      autocomplete="off"
                      v-if="!passwordVisible">
                    <input type="text"
                      v-model="password"
                      placeholder="Enter password"
                      name="member[password]"
                      class="sign-in__input sign-in__input--password"
                      autocomplete="off"
                      v-else v-cloak>

                    <a href="#" @click.prevent="togglePasswordVisibility"
                       class="sign-in__input--password-visibility">
                      <i class="fas fa-eye" v-if="!passwordVisible"></i>
                      <i class="fas fa-eye-slash" v-else v-cloak></i>
                    </a>
                  </div>
                  <a href="#" @click.prevent="state = 'forgotPassword'" class="sign-in__forgot-password">
                    <i class="fas fa-question"></i>
                  </a>
                </div>

                <div class="sign-in__error" v-if="error && error.length" v-text="error" v-cloak></div>

                <button class="sign-in__button" disabled="true" :disabled="buttonDisabled" type="submit" :id="'signInButton'">Sign In</button>
              </form>

              <div class="sign-in__social-network-buttons">
                <form action="/auth/facebook" method="post">
                  <input type="hidden" name="authenticity_token" :value="csrfToken">

                  <button type="submit" class="sign-in__social-network-button sign-in__social-network-button--facebook">
                    <i class="fab fa-facebook-f"></i>
                  </button>
                </form>
                <form action="/auth/linkedin" method="post">
                  <input type="hidden" name="authenticity_token" :value="csrfToken">

                  <button type="submit" class="sign-in__social-network-button sign-in__social-network-button--linkedin">
                    <i class="fab fa-linkedin-in"></i>
                  </button>
                </form>
                <form action="/auth/google_oauth2" method="post">
                  <input type="hidden" name="authenticity_token" :value="csrfToken">

                  <button type="submit" class="sign-in__social-network-button sign-in__social-network-button--google">
                    <i class="fab fa-google"></i>
                  </button>
                </form>
              </div>

 							<div v-if="!isWorkbenchLogin">
									<div class="sign-in__or-wrap">
										<hr class="sign-in__or-hr">

										<div class="sign-in__or">or</div>

										<hr class="sign-in__or-hr">
									</div>
									<a href="/profile/register" target="_self" class="sign-in__button">
										Create Account
									</a>
 							</div>
 							<div v-else class="sign-in-modal__body__fixed-height">
 							</div>

            </div>
            <form class="sign-in-modal__body"
              v-if="state === 'forgotPassword'"
              :key="1"
              @submit.prevent="passwordReset"
              method="post"
              v-cloak>
              <div class="sign-in__input-block">
                <i class="fas fa-envelope sign-in__input-icon"></i>

                <input type="email"
                 v-model="forgotPasswordEmail"
                 name="member[email]"
                 placeholder="Enter email"
                 class="sign-in__input sign-in__input--email"
                 autocomplete="off">
              </div>

              <div class="sign-in__error" v-if="forgotPasswordError && forgotPasswordError.length" v-text="forgotPasswordError" v-cloak></div>

              <button class="sign-in__button" :disabled="forgotPasswordEmail.length < 1 || buttonDisabled" type="submit">Send Email</button>

              <div class="sign-in__back-to-login-wrap">
                <a href="#" class="sign-in__back-to-login" @click.prevent="state = 'login'">Back to Login Form</a>
              </div>
            </form>
            <div v-if="state === 'forgotPasswordEmailSent'" class="sign-in-modal__body" :key="2" v-cloak>
              <div class="sign-in-modal__alert">
                <img src="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/confirmation/green_tick-e2f4c3ce46722430dc7a9a0a72eba2ee91381a92e16094440d6cd893ec6f5d9d.svg" alt="">

                <div>The email with your password reset link has been sent.</div>
              </div>

              <p class="sign-in-modal__text">If you don't receive the email within an hour (and you've checked your Spam folder), email <NAME_EMAIL></p>

              <div class="sign-in__back-to-login-wrap">
                <a href="#" class="sign-in__back-to-login" @click.prevent="state = 'login'">Back to Login Form</a>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </transition>
</div>

<script src="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/styleguide/sign_in_modal_app-999b69082ce9653b1fdf331664b5ec12c3bee0c7016e62262e815ad743d85b94.js"></script>



    <div class="gc_web_layout-wrapper gc_web_comp-footer__wrapper">
        <div class="gc_web_layout-container">
            <div class="gc_web_comp-footer__toplinks">
                <div>
                    <div class="gc_web_comp-footer__logos">
                        <a href="https://www.stratasys.com/" target="_blank" rel="noopener noreferrer">
                            <img alt="White Stratasys Logo" width="112" height="25" src="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/gc/shared/logo/stratasys-white-4b77020b3f456bc74cfda0e64727c838a1670a408727365c16916105579d091f.svg" />
                        </a>
                        <img alt="White GrabCAD Logo" width="112" height="17" src="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/gc/shared/logo/grabcad-white-69ffbd57ceb362664748e93188589f101afe294023645493c7e71e4c93d27aa8.svg" />
                    </div>


                    <div class="gc_web_comp-footer__copyright">
                        © 2025 Stratasys Inc.
                    </div>
                </div>

                <div class="gc_web_comp-footer__sections">
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title">ABOUT US</span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_self" rel="noopener noreferrer" href="https://resources.grabcad.com/company/">Company</a>
                                    </div>
                                    <div>
                                        <a target="_self" rel="noopener noreferrer" href="https://blog.grabcad.com/jobs/">Jobs</a>
                                    </div>
                            </div>
                        </div>
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title">RESOURCES</span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_blank" rel="noopener noreferrer" href="https://blog.grabcad.com">Blog</a>
                                    </div>
                                    <div>
                                        <a target="_blank" rel="noopener noreferrer" href="https://resources.grabcad.com">Resource Center</a>
                                    </div>
                            </div>
                        </div>
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title"><span class="translation_missing" title="translation missing: en.footer_new.links.support">Support</span></span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_blank" rel="noopener noreferrer" href="https://help.grabcad.com">GrabCAD Software Support</a>
                                    </div>
                                    <div>
                                        <a target="_blank" rel="noopener noreferrer" href="https://support.stratasys.com/Software">GrabCAD Community Support</a>
                                    </div>
                            </div>
                        </div>
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title">SOCIAL</span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_blank" rel="noopener noreferrer" href="https://www.facebook.com/GrabCAD">Facebook</a>
                                    </div>
                                    <div>
                                        <a target="_blank" rel="noopener noreferrer" href="https://www.youtube.com/channel/UCKObKIn7qvIfd25MQOSbwjA">Youtube</a>
                                    </div>
                                    <div>
                                        <a target="_blank" rel="noopener noreferrer" href="https://www.instagram.com/grabcadcommunity/">Instagram</a>
                                    </div>
                                    <div>
                                        <a target="_blank" rel="noopener noreferrer" href="https://www.linkedin.com/company/grabcad/">Linkedin</a>
                                    </div>
                                    <div>
                                        <a target="_blank" rel="noopener noreferrer" href="https://twitter.com/grabcad">Twitter</a>
                                    </div>
                            </div>
                        </div>
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title"><span class="translation_missing" title="translation missing: en.navigation.footer.streamline_pro">Streamline Pro</span></span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_self" href="/streamline-pro">Overview</a>
                                    </div>
                            </div>
                        </div>
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title"><span class="translation_missing" title="translation missing: en.navigation.footer.control">Control</span></span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_self" href="/control">Overview</a>
                                    </div>
                            </div>
                        </div>
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title">Print</span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_self" href="https://grabcad.com/print">Overview</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/print/pro">Pro</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/print/fdm">GrabCAD Print for FDM</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/print/polyjet">GrabCAD Print for PolyJet</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/print/digital_anatomy">Digital Anatomy</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/print/saf">SAF Technology</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/print/p3">Origin</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/print/sl">GrabCAD Print for Neo</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/print/pro/fixturemate">fixturemate</a>
                                    </div>
                            </div>
                        </div>
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title">Shop</span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_self" href="https://grabcad.com/shop">Overview</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/shop/features">Features</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/shop/compare">Compare</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/shop/schools">Teachers &amp; Lab Managers</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/shop/security">IT &amp; Security</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/shop/3d-printing-management-software">Engineering Managers</a>
                                    </div>
                            </div>
                        </div>
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title">PARTNERS</span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_self" href="/featured-software-partners">Software Partners</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="/software-development-kit">GrabCAD SDKs</a>
                                    </div>
                            </div>
                        </div>
                        <div class="gc_web_comp-footer__section">
                            <span class="gc_web_comp-footer__section-title">COMMUNITY</span>
                            <div class="gc_web_comp-footer__section-links">
                                    <div>
                                        <a target="_self" href="https://grabcad.com/library">Library</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/challenges">Challenges</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/groups">Groups</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/questions">Questions</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/tutorials">Tutorials</a>
                                    </div>
                                    <div>
                                        <a target="_self" href="https://grabcad.com/engineers">Engineers</a>
                                    </div>
                            </div>
                        </div>
                </div>
            </div>

            <div class="gc_web_comp-footer__bottomlinks">
                <a target="_blank" rel="noopener" href="https://resources.grabcad.com/company/">Contact Us</a>
                <a target="_self" href="https://grabcad.com/terms">Website Terms of Use</a>
                <a target="_self" href="https://grabcad.com/software_terms">Software Terms of Use</a>
                <a target="_self" href="https://grabcad.com/privacy_policy">Privacy policy</a>
                <a target="_blank" rel="noopener" href="https://www.stratasys.com/en/legal/legal-information/">Trademarks</a>
								<a target="_blank" rel="noopener" href="https://support.stratasys.com/Software/GrabCAD-Print/About/Your-Data-on-GrabCAD">Your Data on GrabCAD</a>
            </div>

            <div class="gc_web_comp-footer__bottomtext">
                The Computer-Aided Design (&quot;CAD&quot;) files and all associated content posted to this website are created, uploaded, managed and owned by third-party users. Each CAD and any associated text, image or data is in no way sponsored by or affiliated with any company, organization or real-world item, product, or good it may purport to portray.
            </div>
        </div>
    </div>

		
		<script type="text/javascript">
			var Grabcad = Grabcad || {};
			Grabcad.configuration = Grabcad.configuration || {};
			Grabcad.configuration.evaluatorServer = {};
			Grabcad.configuration.flash = {};
			Grabcad.configuration.imagePath = 'https://d1pspl52z5rk07.cloudfront.net/images';
			Grabcad.configuration.subscribe_key = '******************************************';
			Grabcad.configuration.recaptcha_key = '6LdkkBkUAAAAAOpZhSajb_PoE1sw3mORcZhLNkKe';
			Grabcad.stats = {"files":6570000,"engineers":15610000};
			Grabcad.member = null;
			Grabcad.filters = {"softwares":[{"id":326,"name":"Snagit","cached_slug":"snagit"},{"id":10,"name":"Alibre Design","cached_slug":"alibre-design"},{"id":40,"name":"ArchiCAD","cached_slug":"archicad"},{"id":15,"name":"AutoCAD","cached_slug":"autocad"},{"id":24,"name":"AutoCAD Electrical","cached_slug":"autocad-electrical"},{"id":28,"name":"Autodesk 3ds Max","cached_slug":"autodesk-3ds-max"},{"id":23,"name":"Autodesk Alias","cached_slug":"autodesk-alias"},{"id":1,"name":"Autodesk Inventor","cached_slug":"autodesk-inventor"},{"id":46,"name":"Autodesk Maya","cached_slug":"autodesk-maya"},{"id":9,"name":"Autodesk Revit","cached_slug":"autodesk-revit"},{"id":285,"name":"Autodesk 123D","cached_slug":"autodesk-123d"},{"id":25,"name":"BricsCAD","cached_slug":"bricscad"},{"id":153,"name":"Bentley MicroStation","cached_slug":"bentley-microstation"},{"id":80,"name":"BlenderCAD","cached_slug":"blendercad"},{"id":252,"name":"BobCAD-CAM","cached_slug":"bobcad-cam"},{"id":2,"name":"CATIA","cached_slug":"catia"},{"id":287,"name":"Delmia","cached_slug":"delmia"},{"id":21,"name":"DraftSight","cached_slug":"draftsight"},{"id":117,"name":"FreeCAD","cached_slug":"freecad"},{"id":26,"name":"Femap","cached_slug":"femap"},{"id":47,"name":"Fusion 360","cached_slug":"fusion-360"},{"id":44,"name":"Geomagic Design","cached_slug":"geomagic-design"},{"id":16,"name":"IronCAD","cached_slug":"ironcad"},{"id":42,"name":"JT","cached_slug":"jt"},{"id":14,"name":"Kompas-3D","cached_slug":"kompas-3d"},{"id":12,"name":"KeyCreator","cached_slug":"keycreator"},{"id":83,"name":"KeyShot","cached_slug":"keyshot"},{"id":118,"name":"Lagoa","cached_slug":"lagoa"},{"id":32,"name":"Lightwave","cached_slug":"lightwave"},{"id":35,"name":"Luxology","cached_slug":"luxology"},{"id":33,"name":"Mastercam","cached_slug":"mastercam"},{"id":13,"name":"Moi3D","cached_slug":"moi3d"},{"id":5,"name":"NX Unigraphics","cached_slug":"nx-unigraphics"},{"id":116,"name":"OBJ","cached_slug":"obj"},{"id":219,"name":"Onshape","cached_slug":"onshape"},{"id":82,"name":"OpenSCAD","cached_slug":"openscad"},{"id":120,"name":"Parasolid","cached_slug":"parasolid"},{"id":39,"name":"Powermill","cached_slug":"powermill"},{"id":38,"name":"Powershape","cached_slug":"powershape"},{"id":4,"name":"Pro/Engineer Wildfire","cached_slug":"pro-slash-engineer-wildfire"},{"id":43,"name":"PTC Creo Parametric","cached_slug":"ptc-creo-parametric"},{"id":20,"name":"PTC Creo Elements","cached_slug":"ptc-creo-elements"},{"id":22,"name":"Rhino","cached_slug":"rhino"},{"id":7,"name":"SpaceClaim","cached_slug":"spaceclaim"},{"id":3,"name":"SOLIDWORKS","cached_slug":"solidworks"},{"id":186,"name":"solidThinking Evolve","cached_slug":"solidthinking-evolve"},{"id":45,"name":"SurfCAM","cached_slug":"surfcam"},{"id":6,"name":"Solid Edge","cached_slug":"solid-edge"},{"id":27,"name":"SolidFace","cached_slug":"solidface"},{"id":19,"name":"STEP / IGES","cached_slug":"step-slash-iges"},{"id":11,"name":"SketchUp","cached_slug":"sketchup"},{"id":17,"name":"STL","cached_slug":"stl"},{"id":31,"name":"TopSolid","cached_slug":"topsolid"},{"id":119,"name":"TinkerCAD","cached_slug":"tinkercad"},{"id":41,"name":"T-Flex CAD","cached_slug":"t-flex-cad"},{"id":34,"name":"TurboCAD","cached_slug":"turbocad"},{"id":36,"name":"VectorWorks","cached_slug":"vectorworks"},{"id":37,"name":"ViaCAD 3D","cached_slug":"viacad-3d"},{"id":286,"name":"VRML / WRL","cached_slug":"vrml-wrl"},{"id":30,"name":"ZW3D","cached_slug":"zw3d"},{"id":18,"name":"Rendering","cached_slug":"rendering"},{"id":321,"name":"GrabCAD Print","cached_slug":"grabcad-print"},{"id":320,"name":"GrabCAD Community","cached_slug":"grabcad-community"},{"id":81,"name":"GrabCAD Workbench","cached_slug":"grabcad-workbench"},{"id":327,"name":"Text file","cached_slug":"text-file"},{"id":323,"name":"3D Manufacturing Format","cached_slug":"3d-manufacturing-format"},{"id":324,"name":"Wavefront OBJ","cached_slug":"wavefront-obj"},{"id":325,"name":"Cinema 4D","cached_slug":"cinema-4d"},{"id":8,"name":"Other","cached_slug":"other"}],"categories":[{"id":119,"name":"3D printing","cached_slug":"3d-printing"},{"id":2,"name":"Aerospace","cached_slug":"aerospace"},{"id":155,"name":"Agriculture","cached_slug":"agriculture"},{"id":101,"name":"Architecture","cached_slug":"architecture"},{"id":1,"name":"Automotive","cached_slug":"automotive"},{"id":104,"name":"Aviation","cached_slug":"aviation"},{"id":122,"name":"Components","cached_slug":"components"},{"id":188,"name":"Computer","cached_slug":"computer"},{"id":108,"name":"Construction","cached_slug":"construction"},{"id":109,"name":"Educational","cached_slug":"educational"},{"id":5,"name":"Electrical","cached_slug":"electrical"},{"id":114,"name":"Energy and Power","cached_slug":"energy-and-power"},{"id":7,"name":"Fixtures","cached_slug":"fixtures"},{"id":103,"name":"Furniture","cached_slug":"furniture"},{"id":118,"name":"Hobby","cached_slug":"hobby"},{"id":6,"name":"Household","cached_slug":"household"},{"id":111,"name":"Industrial design","cached_slug":"industrial-design"},{"id":106,"name":"Interior design","cached_slug":"interior-design"},{"id":112,"name":"Jewellery","cached_slug":"jewellery"},{"id":154,"name":"Just for fun","cached_slug":"just-for-fun"},{"id":102,"name":"Machine design","cached_slug":"machine-design"},{"id":3,"name":"Marine","cached_slug":"marine"},{"id":105,"name":"Medical","cached_slug":"medical"},{"id":107,"name":"Military","cached_slug":"military"},{"id":100,"name":"Miscellaneous","cached_slug":"miscellaneous"},{"id":113,"name":"Nature","cached_slug":"nature"},{"id":117,"name":"Piping","cached_slug":"piping"},{"id":116,"name":"Robotics","cached_slug":"robotics"},{"id":120,"name":"Speedrun","cached_slug":"speedrun"},{"id":4,"name":"Sport","cached_slug":"sport"},{"id":121,"name":"Tech","cached_slug":"tech"},{"id":8,"name":"Tools","cached_slug":"tools"},{"id":115,"name":"Toys","cached_slug":"toys"}]};
		</script>
		<script src="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/application_frontend/app-64828df7885db2efe63835196fef71a5ee1a16cf06334a48a8434d1cd45c2897.js"></script>
		<script src="https://d1pspl52z5rk07.cloudfront.net/assets/production/app/community_frontend/application-d09388d0ecfff46e8f191b5afdedcbbd5a34fbd03510d1a94ffe16a43109bb4d.js"></script>
		<div class="feedbackContainer">
	<script type="text/javascript">!function(e,t,n){function a(){var e=t.getElementsByTagName("script")[0],n=t.createElement("script");n.type="text/javascript",n.async=!0,n.src="https://beacon-v2.helpscout.net",e.parentNode.insertBefore(n,e)}if(e.Beacon=n=function(t,n,a){e.Beacon.readyQueue.push({method:t,options:n,data:a})},n.readyQueue=[],"complete"===t.readyState)return a();e.attachEvent?e.attachEvent("onload",a):e.addEventListener("load",a,!1)}(window,document,window.Beacon||function(){});</script>

	<script>
		Beacon('init', '95673dc0-6024-429e-be64-5211d77631fb');

		Beacon('config', {
			labels: {
				answer: 'Search',
				ask: 'Contact',
				suggestedForYou: 'Related Articles',
				searchLabel: 'Search our Help Center'
			},
			hideAvatars: true
		});

		Beacon('on', 'ready', () => {
			Beacon('suggest', ["54f62cfce4b034c37ea93487", "54871f60e4b047e113e4f47a", "5492fd5ce4b07d03cb251434", "5488ad3ce4b0dc8d3cacde8d", "54934688e4b07d03cb25172a"])

		});

		function toggleBeacon(event) {
			event.preventDefault();
			event.stopImmediatePropagation();
			Beacon('toggle');
		}

		$('#openHelp').click(toggleBeacon)

		function closeBeacon() {
			Beacon('close');
		}

		Beacon('on', 'open', () => {
			$(document).click(closeBeacon);
		});

		Beacon('on', 'close', () => {
			$(document).unbind('click', closeBeacon);
		});

	</script>
</div>

			<script src="https://www.recaptcha.net/recaptcha/api.js?render=explicit" async defer></script>
		<script src="//cdn.optimizely.com/js/238376531.js"></script>
		

		
	</body>
</html>
