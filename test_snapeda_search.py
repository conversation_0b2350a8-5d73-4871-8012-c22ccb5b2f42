#!/usr/bin/env python3
"""
Test SnapEDA search for APX803L20-30SA-7 STEP files
"""

import requests
from bs4 import BeautifulSoup
import json
import time

def search_snapeda_for_part(part_number):
    print(f"🔍 SEARCHING SNAPEDA FOR: {part_number}")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    print("\n1. Searching SnapEDA...")
    
    try:
        # SnapEDA search URL
        search_url = f"https://www.snapeda.com/search"
        params = {
            'q': part_number,
            'ref': 'search'
        }
        
        search_response = session.get(search_url, params=params, timeout=30)
        print(f"   Search Status: {search_response.status_code}")
        print(f"   Final URL: {search_response.url}")
        
        if search_response.status_code != 200:
            print(f"   ❌ Search failed with status {search_response.status_code}")
            return False
        
        # Save search results
        with open('snapeda_search_results.html', 'w', encoding='utf-8') as f:
            f.write(search_response.text)
        print("   📄 Saved search results to snapeda_search_results.html")
        
        # Parse search results
        soup = BeautifulSoup(search_response.text, 'html.parser')
        
        # Look for part results
        part_links = []
        
        # SnapEDA typically shows results in cards or list items
        # Look for links that contain the part number or go to part pages
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            text = link.get_text(strip=True)
            
            if href and ('/parts/' in href or '/part/' in href):
                if part_number.lower() in text.lower() or part_number.lower() in href.lower():
                    full_url = href if href.startswith('http') else f"https://www.snapeda.com{href}"
                    part_links.append({
                        'url': full_url,
                        'text': text,
                        'href': href
                    })
        
        print(f"\n2. Found {len(part_links)} potential part matches:")
        for i, link in enumerate(part_links[:5], 1):  # Show first 5
            print(f"   {i}. {link['text']}")
            print(f"      URL: {link['url']}")
        
        if not part_links:
            print("   ❌ No part matches found")
            return False
        
        # Try the first match
        print(f"\n3. Checking first match: {part_links[0]['url']}")
        return check_snapeda_part_page(session, part_links[0]['url'], part_number)
        
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def check_snapeda_part_page(session, part_url, part_number):
    print(f"\n   Accessing part page: {part_url}")
    
    try:
        part_response = session.get(part_url, timeout=30)
        print(f"   Part page status: {part_response.status_code}")
        
        if part_response.status_code != 200:
            print(f"   ❌ Cannot access part page")
            return False
        
        # Save part page
        with open('snapeda_part_page.html', 'w', encoding='utf-8') as f:
            f.write(part_response.text)
        print("   📄 Saved part page to snapeda_part_page.html")
        
        # Parse part page for 3D models
        soup = BeautifulSoup(part_response.text, 'html.parser')
        
        # Look for 3D model indicators
        step_indicators = [
            'step', 'stp', '3d model', '3d file', 'download 3d',
            'mechanical', 'solidworks', 'autocad', 'fusion'
        ]
        
        found_3d_content = []
        
        # Check page text for 3D model mentions
        page_text = soup.get_text().lower()
        for indicator in step_indicators:
            if indicator in page_text:
                found_3d_content.append(indicator)
        
        # Look for download links
        download_links = []
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            text = link.get_text(strip=True).lower()
            
            if any(indicator in text for indicator in step_indicators):
                download_links.append({
                    'url': href,
                    'text': text
                })
            elif any(indicator in href.lower() for indicator in step_indicators):
                download_links.append({
                    'url': href,
                    'text': text
                })
        
        print(f"\n   3D Content Indicators Found: {found_3d_content}")
        print(f"   Potential Download Links: {len(download_links)}")
        
        for i, link in enumerate(download_links[:3], 1):
            print(f"   {i}. {link['text']}")
            print(f"      URL: {link['url']}")
        
        if found_3d_content or download_links:
            print("   ✅ 3D content appears to be available!")
            return True
        else:
            print("   ❌ No 3D content found on this page")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking part page: {e}")
        return False

def main():
    part_number = "APX803L20-30SA-7"
    
    print("🚀 SNAPEDA 3D MODEL SEARCH")
    print("=" * 50)
    
    success = search_snapeda_for_part(part_number)
    
    if success:
        print(f"\n✅ SUCCESS: Found potential 3D content for {part_number} on SnapEDA!")
        print("\nNext steps:")
        print("1. Check the saved HTML files for more details")
        print("2. Look for download buttons or registration requirements")
        print("3. Consider creating a SnapEDA account if needed")
    else:
        print(f"\n❌ No 3D content found for {part_number} on SnapEDA")
        print("\nNext steps:")
        print("1. Try other sources")
        print("2. Search for similar parts")
        print("3. Check manufacturer website directly")

if __name__ == "__main__":
    main()
