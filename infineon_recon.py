#!/usr/bin/env python3
"""
Infineon Reconnaissance - Quick analysis of infineon.com for 3D models
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def main():
    print("🎯 INFINEON RECONNAISSANCE")
    print("=" * 40)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Try direct part page access
        print("\n📱 Testing Infineon with IRF540N...")
        
        # Try common Infineon URL patterns
        possible_urls = [
            "https://www.infineon.com/cms/en/product/power/mosfet/n-channel/irf540n/",
            "https://www.infineon.com/cms/en/product/irf540n/",
            "https://www.infineon.com/dgdl/irf540n.pdf"
        ]
        
        part_found = False
        
        for url in possible_urls:
            print(f"  Trying: {url}")
            try:
                driver.get(url)
                time.sleep(5)
                
                if "IRF540N" in driver.page_source and "404" not in driver.title.lower():
                    print(f"  ✅ Found: {driver.current_url}")
                    part_found = True
                    break
                else:
                    print(f"  ❌ Not found")
            except:
                print(f"  ❌ Error")
                continue
        
        # If direct failed, try search
        if not part_found:
            print(f"\n📱 Using search...")
            driver.get("https://www.infineon.com/")
            time.sleep(5)
            
            # Look for search
            search_elements = driver.find_elements(By.CSS_SELECTOR, "input[type='search'], input[placeholder*='search' i]")
            
            if search_elements:
                search_box = search_elements[0]
                search_box.clear()
                search_box.send_keys("IRF540N")
                search_box.send_keys(Keys.RETURN)
                time.sleep(8)
                
                print(f"📍 Search results: {driver.current_url}")
                
                # Look for part links
                part_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'IRF540N')]")
                
                if part_links:
                    print(f"Found {len(part_links)} part links")
                    for link in part_links[:3]:
                        try:
                            text = link.text.strip()
                            href = link.get_attribute('href')
                            print(f"  - '{text}' -> {href}")
                            
                            if 'product' in href:
                                print(f"🎯 Clicking product link")
                                link.click()
                                time.sleep(8)
                                part_found = True
                                break
                        except:
                            continue
        
        if part_found:
            print(f"\n📱 Analyzing Infineon part page...")
            print(f"📍 URL: {driver.current_url}")
            print(f"📄 Title: {driver.title}")
            
            # Quick 3D model check
            model_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '3D') or contains(text(), 'STEP') or contains(text(), 'CAD')]")
            
            print(f"🔸 3D model indicators: {len(model_elements)} found")
            for elem in model_elements[:5]:
                try:
                    if elem.is_displayed():
                        text = elem.text.strip()[:50]
                        print(f"  - '{text}...'")
                except:
                    continue
            
            # Check for downloads
            download_elements = driver.find_elements(By.XPATH, "//a[contains(text(), 'Download')]")
            print(f"🔸 Download links: {len(download_elements)} found")
            
            # Check tabs
            tabs = driver.find_elements(By.CSS_SELECTOR, ".tab, [role='tab'], .nav-item")
            print(f"🔸 Navigation tabs: {len(tabs)} found")
            
            for tab in tabs[:10]:
                try:
                    if tab.is_displayed():
                        text = tab.text.strip()
                        if text and len(text) < 30:
                            print(f"  Tab: '{text}'")
                            if any(word in text.lower() for word in ['3d', 'cad', 'model', 'design']):
                                print(f"      🎯 POTENTIAL 3D TAB!")
                except:
                    continue
        
        print(f"\n📋 INFINEON SUMMARY:")
        print(f"🌐 Site: infineon.com")
        print(f"📄 Part: IRF540N")
        print(f"✅ Part page: {'Found' if part_found else 'Not found'}")
        
        # Keep browser open briefly
        print(f"\n🔸 Browser open for 2 minutes...")
        time.sleep(120)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
