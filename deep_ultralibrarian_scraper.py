#!/usr/bin/env python3
"""
DEEP ULTRALIBRARIAN SCRAPER
===========================
Goes deeper into UltraLibrarian's multi-screen process to find buried STEP files.
Navigates through search results -> part pages -> download screens.
"""

import requests
import os
import time
from urllib.parse import urljoin, quote
from bs4 import Beautiful<PERSON>ou<PERSON>
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class DeepUltraLibrarianScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.base_url = 'https://www.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("Deep UltraLibrarian Scraper Ready!")

    def setup_driver(self):
        """Setup Chrome driver with patience for multi-screen navigation"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3D'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"Chrome driver failed: {e}")
            return None

    def deep_search(self, manufacturer, part_number):
        """Deep search through UltraLibrarian's multi-screen process"""
        print(f"Deep searching UltraLibrarian for {manufacturer} {part_number}...")
        
        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            # Search strategies
            search_terms = [
                f"{manufacturer} {part_number}",
                part_number,
                f"{part_number} {manufacturer}"
            ]
            
            for search_term in search_terms:
                print(f"   Trying deep search: '{search_term}'")
                
                result = self.try_deep_search(driver, search_term, manufacturer, part_number)
                if result:
                    return result
            
            print("   No STEP files found in deep search")
            return None
            
        except Exception as e:
            print(f"Deep search error: {e}")
            return None
        finally:
            driver.quit()

    def try_deep_search(self, driver, search_term, manufacturer, part_number):
        """Try a deep search with one search term"""
        try:
            # Step 1: Go to search page
            search_url = f"{self.base_url}/search?q={quote(search_term)}"
            print(f"      Loading: {search_url}")
            driver.get(search_url)
            
            # Wait for page load
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(3)
            
            # Step 2: Find and click on part results
            part_links = self.find_part_links(driver)
            print(f"      Found {len(part_links)} part links to explore")
            
            for i, (link_element, link_text, link_url) in enumerate(part_links[:5]):  # Try first 5 results
                print(f"      Exploring part {i+1}: {link_text[:50]}...")
                
                result = self.explore_part_page(driver, link_element, link_url, manufacturer, part_number)
                if result:
                    return result
                
                # Go back to search results for next part
                driver.back()
                time.sleep(2)
            
            return None
            
        except Exception as e:
            print(f"      Deep search attempt failed: {e}")
            return None

    def find_part_links(self, driver):
        """Find all clickable part links on search results"""
        part_links = []
        
        try:
            # Look for various types of part links
            selectors = [
                "a[href*='/part/']",
                "a[href*='/component/']",
                ".search-result a",
                ".part-result a",
                ".component-link",
                "a[href*='ultralibrarian.com']"
            ]
            
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        text = element.text.strip()
                        
                        if href and text and len(text) > 2:
                            # Filter out navigation links
                            if any(skip in href.lower() for skip in ['login', 'register', 'about', 'contact']):
                                continue
                            part_links.append((element, text, href))
                except:
                    continue
            
            # Also try XPath for more complex searches
            try:
                xpath_elements = driver.find_elements(By.XPATH, 
                    "//a[contains(@href, 'part') or contains(@href, 'component')]"
                )
                for element in xpath_elements:
                    href = element.get_attribute('href')
                    text = element.text.strip()
                    
                    if href and text and len(text) > 2:
                        part_links.append((element, text, href))
            except:
                pass
            
        except Exception as e:
            print(f"      Error finding part links: {e}")
        
        # Remove duplicates
        seen_urls = set()
        unique_links = []
        for link in part_links:
            if link[2] not in seen_urls:
                seen_urls.add(link[2])
                unique_links.append(link)
        
        return unique_links

    def explore_part_page(self, driver, link_element, link_url, manufacturer, part_number):
        """Explore a specific part page for downloads"""
        try:
            print(f"         Clicking part link...")
            
            # Click the part link
            driver.execute_script("arguments[0].click();", link_element)
            time.sleep(5)  # Wait for page load
            
            # Look for download options on this page
            downloads = self.find_all_downloads(driver)
            
            if downloads:
                print(f"         Found {len(downloads)} download options")
                
                # Try each download
                for download_element, download_text in downloads:
                    result = self.try_download(driver, download_element, download_text, manufacturer, part_number)
                    if result:
                        return result
            
            # Look for "more info" or "details" links that might lead to downloads
            detail_links = self.find_detail_links(driver)
            
            for detail_element, detail_text in detail_links[:3]:  # Try first 3 detail links
                print(f"         Exploring detail link: {detail_text[:30]}...")
                
                try:
                    driver.execute_script("arguments[0].click();", detail_element)
                    time.sleep(3)
                    
                    # Look for downloads on detail page
                    detail_downloads = self.find_all_downloads(driver)
                    
                    for download_element, download_text in detail_downloads:
                        result = self.try_download(driver, download_element, download_text, manufacturer, part_number)
                        if result:
                            return result
                    
                    # Go back
                    driver.back()
                    time.sleep(2)
                    
                except:
                    continue
            
            return None
            
        except Exception as e:
            print(f"         Part page exploration failed: {e}")
            return None

    def find_all_downloads(self, driver):
        """Find all possible download elements on current page"""
        downloads = []
        
        try:
            # Look for download buttons, links, and forms
            selectors = [
                "button[onclick*='download']",
                "a[href*='download']",
                "input[value*='download']",
                ".download-btn",
                ".btn-download",
                "button:contains('Download')",
                "a:contains('Download')",
                "button[title*='download']",
                "a[title*='download']",
                "a[href*='.step']",
                "a[href*='.stp']",
                "a[href*='.zip']",
                "button[onclick*='3d']",
                "a[href*='3d']"
            ]
            
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if not text:
                            text = element.get_attribute('title') or element.get_attribute('value') or 'Download'
                        
                        if text and any(keyword in text.lower() for keyword in ['download', '3d', 'step', 'cad', 'model']):
                            downloads.append((element, text))
                except:
                    continue
            
            # Also look for text that might indicate downloads
            try:
                download_texts = driver.find_elements(By.XPATH, 
                    "//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download') or " +
                    "contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d') or " +
                    "contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'step')]"
                )
                
                for element in download_texts:
                    if element.tag_name in ['a', 'button', 'input']:
                        text = element.text.strip()
                        if text:
                            downloads.append((element, text))
            except:
                pass
                
        except Exception as e:
            print(f"         Error finding downloads: {e}")
        
        return downloads

    def find_detail_links(self, driver):
        """Find links that might lead to more detailed pages with downloads"""
        detail_links = []
        
        try:
            # Look for "more info", "details", "specifications" etc.
            detail_keywords = ['detail', 'more', 'info', 'spec', 'datasheet', 'model', '3d']
            
            for keyword in detail_keywords:
                try:
                    elements = driver.find_elements(By.XPATH, 
                        f"//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]"
                    )
                    
                    for element in elements:
                        text = element.text.strip()
                        if text and len(text) < 50:  # Reasonable link text length
                            detail_links.append((element, text))
                except:
                    continue
                    
        except Exception as e:
            print(f"         Error finding detail links: {e}")
        
        return detail_links

    def try_download(self, driver, element, text, manufacturer, part_number):
        """Try to download from a specific element"""
        try:
            print(f"            Trying download: {text[:40]}...")
            
            # Get initial file count
            initial_files = set(os.listdir('3D'))
            
            # Click the download element
            driver.execute_script("arguments[0].click();", element)
            time.sleep(10)  # Wait longer for download
            
            # Check for new files
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            for new_file in new_files:
                if any(ext in new_file.lower() for ext in ['.step', '.stp', '.zip']):
                    print(f"            SUCCESS: Downloaded {new_file}")
                    
                    # Rename to standard format
                    old_path = os.path.join('3D', new_file)
                    new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                    new_path = os.path.join('3D', new_name)
                    
                    try:
                        if new_file.lower().endswith('.zip'):
                            # Handle ZIP files
                            extracted = self.extract_step_from_zip(old_path, manufacturer, part_number)
                            if extracted:
                                os.remove(old_path)
                                self.create_log_file(extracted, manufacturer, part_number)
                                return extracted
                        else:
                            os.rename(old_path, new_path)
                            self.create_log_file(new_name, manufacturer, part_number)
                            return new_name
                    except:
                        # If rename fails, keep original
                        self.create_log_file(new_file, manufacturer, part_number)
                        return new_file
            
            return None
            
        except Exception as e:
            print(f"            Download failed: {e}")
            return None

    def extract_step_from_zip(self, zip_path, manufacturer, part_number):
        """Extract STEP files from ZIP"""
        try:
            import zipfile
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                step_files = [f for f in zip_ref.namelist() if f.lower().endswith(('.step', '.stp'))]
                
                if step_files:
                    step_file = step_files[0]
                    zip_ref.extract(step_file, '3D')
                    
                    extracted_path = os.path.join('3D', step_file)
                    new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                    new_path = os.path.join('3D', new_name)
                    
                    os.rename(extracted_path, new_path)
                    return new_name
            
        except Exception as e:
            print(f"            ZIP extraction failed: {e}")
        
        return None

    def create_log_file(self, filename, manufacturer, part_number):
        """Create log file for successful download"""
        log_name = filename.replace('.step', '.txt')
        log_path = os.path.join('3D', log_name)
        
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(f"{manufacturer.upper()} {part_number} STEP FILE DOWNLOAD LOG\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Manufacturer: {manufacturer}\n")
            f.write(f"Part Number: {part_number}\n")
            f.write(f"Source: UltraLibrarian (Deep Scraping)\n")
            f.write(f"Downloaded File: {filename}\n")
            f.write(f"Location: 3D/{filename}\n")
            f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("DOWNLOAD DETAILS:\n")
            f.write("================\n")
            f.write("Method: Deep multi-screen scraping\n")
            f.write("Source: UltraLibrarian website\n")
            f.write("Process: Automated navigation through multiple screens\n")
            f.write("Status: SUCCESS\n\n")
            f.write("NOTES:\n")
            f.write("======\n")
            f.write("- Downloaded via deep scraping of UltraLibrarian\n")
            f.write("- Navigated through multiple screens to find buried file\n")
            f.write("- Third-party CAD model library\n")
            f.write("- Verify dimensions against datasheet\n")

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python deep_ultralibrarian_scraper.py \"Manufacturer\" \"Part Number\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    scraper = DeepUltraLibrarianScraper()
    result = scraper.deep_search(manufacturer, part_number)
    
    if result:
        print(f"\nSUCCESS: Downloaded {result}")
        print(f"Location: 3D/{result}")
    else:
        print(f"\nFAILED: No STEP file found for {part_number} in deep search")

if __name__ == "__main__":
    main()
