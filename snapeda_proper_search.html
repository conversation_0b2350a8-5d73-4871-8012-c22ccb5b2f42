





<!DOCTYPE html>
<html lang="en">
<head>
    


<!--[if IE]><style>div { zoom: 1; /* trigger hasLayout */ }</style><![endif]-->
<!-- META -->
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta charset="UTF-8"><script type="text/javascript">(window.NREUM||(NREUM={})).init={ajax:{deny_list:["bam.nr-data.net"]}};(window.NREUM||(NREUM={})).loader_config={licenseKey:"40b3e098a7",applicationID:"29832197"};;/*! For license information please see nr-loader-rum-1.295.0.min.js.LICENSE.txt */
(()=>{var e,t,r={122:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});var n=r(944);function i(e,t){try{if(!e||"object"!=typeof e)return(0,n.R)(3);if(!t||"object"!=typeof t)return(0,n.R)(4);const r=Object.create(Object.getPrototypeOf(t),Object.getOwnPropertyDescriptors(t)),a=0===Object.keys(r).length?e:r;for(let s in a)if(void 0!==e[s])try{if(null===e[s]){r[s]=null;continue}Array.isArray(e[s])&&Array.isArray(t[s])?r[s]=Array.from(new Set([...e[s],...t[s]])):"object"==typeof e[s]&&"object"==typeof t[s]?r[s]=i(e[s],t[s]):r[s]=e[s]}catch(e){r[s]||(0,n.R)(1,e)}return r}catch(e){(0,n.R)(2,e)}}},555:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,f:()=>s});var n=r(384),i=r(122);const a={beacon:n.NT.beacon,errorBeacon:n.NT.errorBeacon,licenseKey:void 0,applicationID:void 0,sa:void 0,queueTime:void 0,applicationTime:void 0,ttGuid:void 0,user:void 0,account:void 0,product:void 0,extra:void 0,jsAttributes:{},userAttributes:void 0,atts:void 0,transactionName:void 0,tNamePlain:void 0};function s(e){try{return!!e.licenseKey&&!!e.errorBeacon&&!!e.applicationID}catch(e){return!1}}const o=e=>(0,i.a)(e,a)},324:(e,t,r)=>{"use strict";r.d(t,{F3:()=>i,Xs:()=>a,xv:()=>n});const n="1.295.0",i="PROD",a="CDN"},154:(e,t,r)=>{"use strict";r.d(t,{OF:()=>c,RI:()=>i,WN:()=>d,bv:()=>a,gm:()=>s,mw:()=>o,sb:()=>u});var n=r(863);const i="undefined"!=typeof window&&!!window.document,a="undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self.navigator instanceof WorkerNavigator||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis.navigator instanceof WorkerNavigator),s=i?window:"undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis),o=Boolean("hidden"===s?.document?.visibilityState),c=/iPad|iPhone|iPod/.test(s.navigator?.userAgent),u=c&&"undefined"==typeof SharedWorker,d=((()=>{const e=s.navigator?.userAgent?.match(/Firefox[/\s](\d+\.\d+)/);Array.isArray(e)&&e.length>=2&&e[1]})(),Date.now()-(0,n.t)())},241:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});var n=r(154);const i="newrelic";function a(e={}){try{n.gm.dispatchEvent(new CustomEvent(i,{detail:e}))}catch(e){}}},687:(e,t,r)=>{"use strict";r.d(t,{Ak:()=>u,Ze:()=>f,x3:()=>d});var n=r(241),i=r(836),a=r(606),s=r(860),o=r(646);const c={};function u(e,t){const r={staged:!1,priority:s.P3[t]||0};l(e),c[e].get(t)||c[e].set(t,r)}function d(e,t){e&&c[e]&&(c[e].get(t)&&c[e].delete(t),p(e,t,!1),c[e].size&&g(e))}function l(e){if(!e)throw new Error("agentIdentifier required");c[e]||(c[e]=new Map)}function f(e="",t="feature",r=!1){if(l(e),!e||!c[e].get(t)||r)return p(e,t);c[e].get(t).staged=!0,g(e)}function g(e){const t=Array.from(c[e]);t.every((([e,t])=>t.staged))&&(t.sort(((e,t)=>e[1].priority-t[1].priority)),t.forEach((([t])=>{c[e].delete(t),p(e,t)})))}function p(e,t,r=!0){const s=e?i.ee.get(e):i.ee,c=a.i.handlers;if(!s.aborted&&s.backlog&&c){if((0,n.W)({agentIdentifier:e,type:"lifecycle",name:"drain",feature:t}),r){const e=s.backlog[t],r=c[t];if(r){for(let t=0;e&&t<e.length;++t)m(e[t],r);Object.entries(r).forEach((([e,t])=>{Object.values(t||{}).forEach((t=>{t[0]?.on&&t[0]?.context()instanceof o.y&&t[0].on(e,t[1])}))}))}}s.isolatedBacklog||delete c[t],s.backlog[t]=null,s.emit("drain-"+t,[])}}function m(e,t){var r=e[1];Object.values(t[r]||{}).forEach((t=>{var r=e[0];if(t[0]===r){var n=t[1],i=e[3],a=e[2];n.apply(i,a)}}))}},836:(e,t,r)=>{"use strict";r.d(t,{P:()=>o,ee:()=>c});var n=r(384),i=r(990),a=r(646),s=r(607);const o="nr@context:".concat(s.W),c=function e(t,r){var n={},s={},d={},l=!1;try{l=16===r.length&&u.initializedAgents?.[r]?.runtime.isolatedBacklog}catch(e){}var f={on:p,addEventListener:p,removeEventListener:function(e,t){var r=n[e];if(!r)return;for(var i=0;i<r.length;i++)r[i]===t&&r.splice(i,1)},emit:function(e,r,n,i,a){!1!==a&&(a=!0);if(c.aborted&&!i)return;t&&a&&t.emit(e,r,n);var o=g(n);m(e).forEach((e=>{e.apply(o,r)}));var u=v()[s[e]];u&&u.push([f,e,r,o]);return o},get:h,listeners:m,context:g,buffer:function(e,t){const r=v();if(t=t||"feature",f.aborted)return;Object.entries(e||{}).forEach((([e,n])=>{s[n]=t,t in r||(r[t]=[])}))},abort:function(){f._aborted=!0,Object.keys(f.backlog).forEach((e=>{delete f.backlog[e]}))},isBuffering:function(e){return!!v()[s[e]]},debugId:r,backlog:l?{}:t&&"object"==typeof t.backlog?t.backlog:{},isolatedBacklog:l};return Object.defineProperty(f,"aborted",{get:()=>{let e=f._aborted||!1;return e||(t&&(e=t.aborted),e)}}),f;function g(e){return e&&e instanceof a.y?e:e?(0,i.I)(e,o,(()=>new a.y(o))):new a.y(o)}function p(e,t){n[e]=m(e).concat(t)}function m(e){return n[e]||[]}function h(t){return d[t]=d[t]||e(f,t)}function v(){return f.backlog}}(void 0,"globalEE"),u=(0,n.Zm)();u.ee||(u.ee=c)},646:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});class n{constructor(e){this.contextId=e}}},908:(e,t,r)=>{"use strict";r.d(t,{d:()=>n,p:()=>i});var n=r(836).ee.get("handle");function i(e,t,r,i,a){a?(a.buffer([e],i),a.emit(e,t,r)):(n.buffer([e],i),n.emit(e,t,r))}},606:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(908);a.on=s;var i=a.handlers={};function a(e,t,r,a){s(a||n.d,i,e,t,r)}function s(e,t,r,i,a){a||(a="feature"),e||(e=n.d);var s=t[a]=t[a]||{};(s[r]=s[r]||[]).push([e,i])}},878:(e,t,r)=>{"use strict";function n(e,t){return{capture:e,passive:!1,signal:t}}function i(e,t,r=!1,i){window.addEventListener(e,t,n(r,i))}function a(e,t,r=!1,i){document.addEventListener(e,t,n(r,i))}r.d(t,{DD:()=>a,jT:()=>n,sp:()=>i})},607:(e,t,r)=>{"use strict";r.d(t,{W:()=>n});const n=(0,r(566).bz)()},566:(e,t,r)=>{"use strict";r.d(t,{LA:()=>o,bz:()=>s});var n=r(154);const i="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";function a(e,t){return e?15&e[t]:16*Math.random()|0}function s(){const e=n.gm?.crypto||n.gm?.msCrypto;let t,r=0;return e&&e.getRandomValues&&(t=e.getRandomValues(new Uint8Array(30))),i.split("").map((e=>"x"===e?a(t,r++).toString(16):"y"===e?(3&a()|8).toString(16):e)).join("")}function o(e){const t=n.gm?.crypto||n.gm?.msCrypto;let r,i=0;t&&t.getRandomValues&&(r=t.getRandomValues(new Uint8Array(e)));const s=[];for(var o=0;o<e;o++)s.push(a(r,i++).toString(16));return s.join("")}},614:(e,t,r)=>{"use strict";r.d(t,{BB:()=>s,H3:()=>n,g:()=>u,iL:()=>c,tS:()=>o,uh:()=>i,wk:()=>a});const n="NRBA",i="SESSION",a=144e5,s=18e5,o={STARTED:"session-started",PAUSE:"session-pause",RESET:"session-reset",RESUME:"session-resume",UPDATE:"session-update"},c={SAME_TAB:"same-tab",CROSS_TAB:"cross-tab"},u={OFF:0,FULL:1,ERROR:2}},863:(e,t,r)=>{"use strict";function n(){return Math.floor(performance.now())}r.d(t,{t:()=>n})},944:(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var n=r(241);function i(e,t){"function"==typeof console.debug&&(console.debug("New Relic Warning: https://github.com/newrelic/newrelic-browser-agent/blob/main/docs/warning-codes.md#".concat(e),t),(0,n.W)({agentIdentifier:null,drained:null,type:"data",name:"warn",feature:"warn",data:{code:e,secondary:t}}))}},701:(e,t,r)=>{"use strict";r.d(t,{B:()=>a,t:()=>s});var n=r(241);const i=new Set,a={};function s(e,t){const r=t.agentIdentifier;a[r]??={},e&&"object"==typeof e&&(i.has(r)||(t.ee.emit("rumresp",[e]),a[r]=e,i.add(r),(0,n.W)({agentIdentifier:r,loaded:!0,drained:!0,type:"lifecycle",name:"load",feature:void 0,data:e})))}},990:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var n=Object.prototype.hasOwnProperty;function i(e,t,r){if(n.call(e,t))return e[t];var i=r();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(e,t,{value:i,writable:!0,enumerable:!1}),i}catch(e){}return e[t]=i,i}},389:(e,t,r)=>{"use strict";function n(e,t=500,r={}){const n=r?.leading||!1;let i;return(...r)=>{n&&void 0===i&&(e.apply(this,r),i=setTimeout((()=>{i=clearTimeout(i)}),t)),n||(clearTimeout(i),i=setTimeout((()=>{e.apply(this,r)}),t))}}function i(e){let t=!1;return(...r)=>{t||(t=!0,e.apply(this,r))}}r.d(t,{J:()=>i,s:()=>n})},910:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(944);const i=new Map;function a(...e){return e.every((e=>{if(i.has(e))return i.get(e);const t="function"==typeof e&&e.toString().includes("[native code]");return t||(0,n.R)(64,e?.name||e?.toString()),i.set(e,t),t}))}},289:(e,t,r)=>{"use strict";r.d(t,{GG:()=>a,Qr:()=>o,sB:()=>s});var n=r(878);function i(){return"undefined"==typeof document||"complete"===document.readyState}function a(e,t){if(i())return e();(0,n.sp)("load",e,t)}function s(e){if(i())return e();(0,n.DD)("DOMContentLoaded",e)}function o(e){if(i())return e();(0,n.sp)("popstate",e)}},384:(e,t,r)=>{"use strict";r.d(t,{NT:()=>s,US:()=>d,Zm:()=>o,bQ:()=>u,dV:()=>c,pV:()=>l});var n=r(154),i=r(863),a=r(910);const s={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net"};function o(){return n.gm.NREUM||(n.gm.NREUM={}),void 0===n.gm.newrelic&&(n.gm.newrelic=n.gm.NREUM),n.gm.NREUM}function c(){let e=o();return e.o||(e.o={ST:n.gm.setTimeout,SI:n.gm.setImmediate||n.gm.setInterval,CT:n.gm.clearTimeout,XHR:n.gm.XMLHttpRequest,REQ:n.gm.Request,EV:n.gm.Event,PR:n.gm.Promise,MO:n.gm.MutationObserver,FETCH:n.gm.fetch,WS:n.gm.WebSocket},(0,a.i)(...Object.values(e.o))),e}function u(e,t){let r=o();r.initializedAgents??={},t.initializedAt={ms:(0,i.t)(),date:new Date},r.initializedAgents[e]=t}function d(e,t){o()[e]=t}function l(){return function(){let e=o();const t=e.info||{};e.info={beacon:s.beacon,errorBeacon:s.errorBeacon,...t}}(),function(){let e=o();const t=e.init||{};e.init={...t}}(),c(),function(){let e=o();const t=e.loader_config||{};e.loader_config={...t}}(),o()}},843:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});var n=r(878);function i(e,t=!1,r,i){(0,n.DD)("visibilitychange",(function(){if(t)return void("hidden"===document.visibilityState&&e());e(document.visibilityState)}),r,i)}},773:(e,t,r)=>{"use strict";r.d(t,{z_:()=>a,XG:()=>o,TZ:()=>n,rs:()=>i,xV:()=>s});r(154),r(566),r(384);const n=r(860).K7.metrics,i="sm",a="cm",s="storeSupportabilityMetrics",o="storeEventMetrics"},630:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewEvent},782:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewTiming},234:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});var n=r(836),i=r(687);class a{constructor(e,t){this.agentIdentifier=e,this.ee=n.ee.get(e),this.featureName=t,this.blocked=!1}deregisterDrain(){(0,i.x3)(this.agentIdentifier,this.featureName)}}},741:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});var n=r(944),i=r(261);class a{#e(e,...t){if(this[e]!==a.prototype[e])return this[e](...t);(0,n.R)(35,e)}addPageAction(e,t){return this.#e(i.hG,e,t)}register(e){return this.#e(i.eY,e)}recordCustomEvent(e,t){return this.#e(i.fF,e,t)}setPageViewName(e,t){return this.#e(i.Fw,e,t)}setCustomAttribute(e,t,r){return this.#e(i.cD,e,t,r)}noticeError(e,t){return this.#e(i.o5,e,t)}setUserId(e){return this.#e(i.Dl,e)}setApplicationVersion(e){return this.#e(i.nb,e)}setErrorHandler(e){return this.#e(i.bt,e)}addRelease(e,t){return this.#e(i.k6,e,t)}log(e,t){return this.#e(i.$9,e,t)}start(){return this.#e(i.d3)}finished(e){return this.#e(i.BL,e)}recordReplay(){return this.#e(i.CH)}pauseReplay(){return this.#e(i.Tb)}addToTrace(e){return this.#e(i.U2,e)}setCurrentRouteName(e){return this.#e(i.PA,e)}interaction(){return this.#e(i.dT)}wrapLogger(e,t,r){return this.#e(i.Wb,e,t,r)}measure(e,t){return this.#e(i.V1,e,t)}}},261:(e,t,r)=>{"use strict";r.d(t,{$9:()=>u,BL:()=>o,CH:()=>g,Dl:()=>_,Fw:()=>y,PA:()=>h,Pl:()=>n,Tb:()=>l,U2:()=>a,V1:()=>k,Wb:()=>x,bt:()=>b,cD:()=>v,d3:()=>w,dT:()=>c,eY:()=>p,fF:()=>f,hG:()=>i,k6:()=>s,nb:()=>m,o5:()=>d});const n="api-",i="addPageAction",a="addToTrace",s="addRelease",o="finished",c="interaction",u="log",d="noticeError",l="pauseReplay",f="recordCustomEvent",g="recordReplay",p="register",m="setApplicationVersion",h="setCurrentRouteName",v="setCustomAttribute",b="setErrorHandler",y="setPageViewName",_="setUserId",w="start",x="wrapLogger",k="measure"},163:(e,t,r)=>{"use strict";r.d(t,{j:()=>E});var n=r(384),i=r(741);var a=r(555);r(860).K7.genericEvents;const s="experimental.marks",o="experimental.measures",c="experimental.resources",u=e=>{if(!e||"string"!=typeof e)return!1;try{document.createDocumentFragment().querySelector(e)}catch{return!1}return!0};var d=r(614),l=r(944),f=r(122);const g="[data-nr-mask]",p=e=>(0,f.a)(e,(()=>{const e={feature_flags:[],experimental:{marks:!1,measures:!1,resources:!1},mask_selector:"*",block_selector:"[data-nr-block]",mask_input_options:{color:!1,date:!1,"datetime-local":!1,email:!1,month:!1,number:!1,range:!1,search:!1,tel:!1,text:!1,time:!1,url:!1,week:!1,textarea:!1,select:!1,password:!0}};return{ajax:{deny_list:void 0,block_internal:!0,enabled:!0,autoStart:!0},api:{allow_registered_children:!0,duplicate_registered_data:!1},distributed_tracing:{enabled:void 0,exclude_newrelic_header:void 0,cors_use_newrelic_header:void 0,cors_use_tracecontext_headers:void 0,allowed_origins:void 0},get feature_flags(){return e.feature_flags},set feature_flags(t){e.feature_flags=t},generic_events:{enabled:!0,autoStart:!0},harvest:{interval:30},jserrors:{enabled:!0,autoStart:!0},logging:{enabled:!0,autoStart:!0},metrics:{enabled:!0,autoStart:!0},obfuscate:void 0,page_action:{enabled:!0},page_view_event:{enabled:!0,autoStart:!0},page_view_timing:{enabled:!0,autoStart:!0},performance:{get capture_marks(){return e.feature_flags.includes(s)||e.experimental.marks},set capture_marks(t){e.experimental.marks=t},get capture_measures(){return e.feature_flags.includes(o)||e.experimental.measures},set capture_measures(t){e.experimental.measures=t},capture_detail:!0,resources:{get enabled(){return e.feature_flags.includes(c)||e.experimental.resources},set enabled(t){e.experimental.resources=t},asset_types:[],first_party_domains:[],ignore_newrelic:!0}},privacy:{cookies_enabled:!0},proxy:{assets:void 0,beacon:void 0},session:{expiresMs:d.wk,inactiveMs:d.BB},session_replay:{autoStart:!0,enabled:!1,preload:!1,sampling_rate:10,error_sampling_rate:100,collect_fonts:!1,inline_images:!1,fix_stylesheets:!0,mask_all_inputs:!0,get mask_text_selector(){return e.mask_selector},set mask_text_selector(t){u(t)?e.mask_selector="".concat(t,",").concat(g):""===t||null===t?e.mask_selector=g:(0,l.R)(5,t)},get block_class(){return"nr-block"},get ignore_class(){return"nr-ignore"},get mask_text_class(){return"nr-mask"},get block_selector(){return e.block_selector},set block_selector(t){u(t)?e.block_selector+=",".concat(t):""!==t&&(0,l.R)(6,t)},get mask_input_options(){return e.mask_input_options},set mask_input_options(t){t&&"object"==typeof t?e.mask_input_options={...t,password:!0}:(0,l.R)(7,t)}},session_trace:{enabled:!0,autoStart:!0},soft_navigations:{enabled:!0,autoStart:!0},spa:{enabled:!0,autoStart:!0},ssl:void 0,user_actions:{enabled:!0,elementAttributes:["id","className","tagName","type"]}}})());var m=r(154),h=r(324);let v=0;const b={buildEnv:h.F3,distMethod:h.Xs,version:h.xv,originTime:m.WN},y={appMetadata:{},customTransaction:void 0,denyList:void 0,disabled:!1,entityManager:void 0,harvester:void 0,isolatedBacklog:!1,isRecording:!1,loaderType:void 0,maxBytes:3e4,obfuscator:void 0,onerror:void 0,ptid:void 0,releaseIds:{},session:void 0,timeKeeper:void 0,jsAttributesMetadata:{bytes:0},get harvestCount(){return++v}},_=e=>{const t=(0,f.a)(e,y),r=Object.keys(b).reduce(((e,t)=>(e[t]={value:b[t],writable:!1,configurable:!0,enumerable:!0},e)),{});return Object.defineProperties(t,r)};var w=r(701);const x=e=>{const t=e.startsWith("http");e+="/",r.p=t?e:"https://"+e};var k=r(836),A=r(241);const S={accountID:void 0,trustKey:void 0,agentID:void 0,licenseKey:void 0,applicationID:void 0,xpid:void 0},T=e=>(0,f.a)(e,S),R=new Set;function E(e,t={},r,s){let{init:o,info:c,loader_config:u,runtime:d={},exposed:l=!0}=t;if(!c){const e=(0,n.pV)();o=e.init,c=e.info,u=e.loader_config}e.init=p(o||{}),e.loader_config=T(u||{}),c.jsAttributes??={},m.bv&&(c.jsAttributes.isWorker=!0),e.info=(0,a.D)(c);const f=e.init,g=[c.beacon,c.errorBeacon];R.has(e.agentIdentifier)||(f.proxy.assets&&(x(f.proxy.assets),g.push(f.proxy.assets)),f.proxy.beacon&&g.push(f.proxy.beacon),function(e){const t=(0,n.pV)();Object.getOwnPropertyNames(i.W.prototype).forEach((r=>{const n=i.W.prototype[r];if("function"!=typeof n||"constructor"===n)return;let a=t[r];e[r]&&!1!==e.exposed&&"micro-agent"!==e.runtime?.loaderType&&(t[r]=(...t)=>{const n=e[r](...t);return a?a(...t):n})}))}(e),(0,n.US)("activatedFeatures",w.B),e.runSoftNavOverSpa&&=!0===f.soft_navigations.enabled&&f.feature_flags.includes("soft_nav")),d.denyList=[...f.ajax.deny_list||[],...f.ajax.block_internal?g:[]],d.ptid=e.agentIdentifier,d.loaderType=r,e.runtime=_(d),R.has(e.agentIdentifier)||(e.ee=k.ee.get(e.agentIdentifier),e.exposed=l,(0,A.W)({agentIdentifier:e.agentIdentifier,drained:!!w.B?.[e.agentIdentifier],type:"lifecycle",name:"initialize",feature:void 0,data:e.config})),R.add(e.agentIdentifier)}},374:(e,t,r)=>{r.nc=(()=>{try{return document?.currentScript?.nonce}catch(e){}return""})()},860:(e,t,r)=>{"use strict";r.d(t,{$J:()=>d,K7:()=>c,P3:()=>u,XX:()=>i,Yy:()=>o,df:()=>a,qY:()=>n,v4:()=>s});const n="events",i="jserrors",a="browser/blobs",s="rum",o="browser/logs",c={ajax:"ajax",genericEvents:"generic_events",jserrors:i,logging:"logging",metrics:"metrics",pageAction:"page_action",pageViewEvent:"page_view_event",pageViewTiming:"page_view_timing",sessionReplay:"session_replay",sessionTrace:"session_trace",softNav:"soft_navigations",spa:"spa"},u={[c.pageViewEvent]:1,[c.pageViewTiming]:2,[c.metrics]:3,[c.jserrors]:4,[c.spa]:5,[c.ajax]:6,[c.sessionTrace]:7,[c.softNav]:8,[c.sessionReplay]:9,[c.logging]:10,[c.genericEvents]:11},d={[c.pageViewEvent]:s,[c.pageViewTiming]:n,[c.ajax]:n,[c.spa]:n,[c.softNav]:n,[c.metrics]:i,[c.jserrors]:i,[c.sessionTrace]:a,[c.sessionReplay]:a,[c.logging]:o,[c.genericEvents]:"ins"}}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var a=n[e]={exports:{}};return r[e](a,a.exports,i),a.exports}i.m=r,i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,r)=>(i.f[r](e,t),t)),[])),i.u=e=>"nr-rum-1.295.0.min.js",i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="NRBA-1.295.0.PROD:",i.l=(r,n,a,s)=>{if(e[r])e[r].push(n);else{var o,c;if(void 0!==a)for(var u=document.getElementsByTagName("script"),d=0;d<u.length;d++){var l=u[d];if(l.getAttribute("src")==r||l.getAttribute("data-webpack")==t+a){o=l;break}}if(!o){c=!0;var f={296:"sha512-5mjLs4V+vqmVCMY5tMI4S9zuik2ZQGpHyNyS3JjzoMIc9MrdQiFKTHsXuapkHWyDEDo06Z3nazEbjwKKn3drdA=="};(o=document.createElement("script")).charset="utf-8",o.timeout=120,i.nc&&o.setAttribute("nonce",i.nc),o.setAttribute("data-webpack",t+a),o.src=r,0!==o.src.indexOf(window.location.origin+"/")&&(o.crossOrigin="anonymous"),f[s]&&(o.integrity=f[s])}e[r]=[n];var g=(t,n)=>{o.onerror=o.onload=null,clearTimeout(p);var i=e[r];if(delete e[r],o.parentNode&&o.parentNode.removeChild(o),i&&i.forEach((e=>e(n))),t)return t(n)},p=setTimeout(g.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=g.bind(null,o.onerror),o.onload=g.bind(null,o.onload),c&&document.head.appendChild(o)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.p="https://js-agent.newrelic.com/",(()=>{var e={374:0,840:0};i.f.j=(t,r)=>{var n=i.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var a=new Promise(((r,i)=>n=e[t]=[r,i]));r.push(n[2]=a);var s=i.p+i.u(t),o=new Error;i.l(s,(r=>{if(i.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var a=r&&("load"===r.type?"missing":r.type),s=r&&r.target&&r.target.src;o.message="Loading chunk "+t+" failed.\n("+a+": "+s+")",o.name="ChunkLoadError",o.type=a,o.request=s,n[1](o)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,a,[s,o,c]=r,u=0;if(s.some((t=>0!==e[t]))){for(n in o)i.o(o,n)&&(i.m[n]=o[n]);if(c)c(i)}for(t&&t(r);u<s.length;u++)a=s[u],i.o(e,a)&&e[a]&&e[a][0](),e[a]=0},r=self["webpackChunk:NRBA-1.295.0.PROD"]=self["webpackChunk:NRBA-1.295.0.PROD"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";i(374);var e=i(566),t=i(741);class r extends t.W{agentIdentifier=(0,e.LA)(16)}var n=i(860);const a=Object.values(n.K7);var s=i(163);var o=i(908),c=i(863),u=i(261),d=i(241),l=i(944),f=i(701),g=i(773);function p(e,t,i,a){const s=a||i;!s||s[e]&&s[e]!==r.prototype[e]||(s[e]=function(){(0,o.p)(g.xV,["API/"+e+"/called"],void 0,n.K7.metrics,i.ee),(0,d.W)({agentIdentifier:i.agentIdentifier,drained:!!f.B?.[i.agentIdentifier],type:"data",name:"api",feature:u.Pl+e,data:{}});try{return t.apply(this,arguments)}catch(e){(0,l.R)(23,e)}})}function m(e,t,r,n,i){const a=e.info;null===r?delete a.jsAttributes[t]:a.jsAttributes[t]=r,(i||null===r)&&(0,o.p)(u.Pl+n,[(0,c.t)(),t,r],void 0,"session",e.ee)}var h=i(687),v=i(234),b=i(289),y=i(154),_=i(384);const w=e=>y.RI&&!0===e?.privacy.cookies_enabled;function x(e){return!!(0,_.dV)().o.MO&&w(e)&&!0===e?.session_trace.enabled}var k=i(389);class A extends v.W{constructor(e,t){super(e.agentIdentifier,t),this.abortHandler=void 0,this.featAggregate=void 0,this.onAggregateImported=void 0,this.deferred=Promise.resolve(),!1===e.init[this.featureName].autoStart?this.deferred=new Promise(((t,r)=>{this.ee.on("manual-start-all",(0,k.J)((()=>{(0,h.Ak)(e.agentIdentifier,this.featureName),t()})))})):(0,h.Ak)(e.agentIdentifier,t)}importAggregator(e,t,r={}){if(this.featAggregate)return;let a;this.onAggregateImported=new Promise((e=>{a=e}));const s=async()=>{let s;await this.deferred;try{if(w(e.init)){const{setupAgentSession:t}=await i.e(296).then(i.bind(i,305));s=t(e)}}catch(e){(0,l.R)(20,e),this.ee.emit("internal-error",[e]),this.featureName===n.K7.sessionReplay&&this.abortHandler?.()}try{if(!this.#t(this.featureName,s,e.init))return(0,h.Ze)(this.agentIdentifier,this.featureName),void a(!1);const{Aggregate:n}=await t();this.featAggregate=new n(e,r),e.runtime.harvester.initializedAggregates.push(this.featAggregate),a(!0)}catch(e){(0,l.R)(34,e),this.abortHandler?.(),(0,h.Ze)(this.agentIdentifier,this.featureName,!0),a(!1),this.ee&&this.ee.abort()}};y.RI?(0,b.GG)((()=>s()),!0):s()}#t(e,t,r){switch(e){case n.K7.sessionReplay:return x(r)&&!!t;case n.K7.sessionTrace:return!!t;default:return!0}}}var S=i(630),T=i(614);class R extends A{static featureName=S.T;constructor(e){var t;super(e,S.T),this.setupInspectionEvents(e.agentIdentifier),t=e,p(u.Fw,(function(e,r){"string"==typeof e&&("/"!==e.charAt(0)&&(e="/"+e),t.runtime.customTransaction=(r||"http://custom.transaction")+e,(0,o.p)(u.Pl+u.Fw,[(0,c.t)()],void 0,void 0,t.ee))}),t),this.ee.on("api-send-rum",((e,t)=>(0,o.p)("send-rum",[e,t],void 0,this.featureName,this.ee))),this.importAggregator(e,(()=>i.e(296).then(i.bind(i,108))))}setupInspectionEvents(e){const t=(t,r)=>{t&&(0,d.W)({agentIdentifier:e,timeStamp:t.timeStamp,loaded:"complete"===t.target.readyState,type:"window",name:r,data:t.target.location+""})};(0,b.sB)((e=>{t(e,"DOMContentLoaded")})),(0,b.GG)((e=>{t(e,"load")})),(0,b.Qr)((e=>{t(e,"navigate")})),this.ee.on(T.tS.UPDATE,((t,r)=>{(0,d.W)({agentIdentifier:e,type:"lifecycle",name:"session",data:r})}))}}var E=i(843),N=i(878),j=i(782);class I extends A{static featureName=j.T;constructor(e){super(e,j.T),y.RI&&((0,E.u)((()=>(0,o.p)("docHidden",[(0,c.t)()],void 0,j.T,this.ee)),!0),(0,N.sp)("pagehide",(()=>(0,o.p)("winPagehide",[(0,c.t)()],void 0,j.T,this.ee))),this.importAggregator(e,(()=>i.e(296).then(i.bind(i,350)))))}}class O extends A{static featureName=g.TZ;constructor(e){super(e,g.TZ),y.RI&&document.addEventListener("securitypolicyviolation",(e=>{(0,o.p)(g.xV,["Generic/CSPViolation/Detected"],void 0,this.featureName,this.ee)})),this.importAggregator(e,(()=>i.e(296).then(i.bind(i,373))))}}new class extends r{constructor(e){var t;(super(),y.gm)?(this.features={},(0,_.bQ)(this.agentIdentifier,this),this.desiredFeatures=new Set(e.features||[]),this.desiredFeatures.add(R),this.runSoftNavOverSpa=[...this.desiredFeatures].some((e=>e.featureName===n.K7.softNav)),(0,s.j)(this,e,e.loaderType||"agent"),t=this,p(u.cD,(function(e,r,n=!1){if("string"==typeof e){if(["string","number","boolean"].includes(typeof r)||null===r)return m(t,e,r,u.cD,n);(0,l.R)(40,typeof r)}else(0,l.R)(39,typeof e)}),t),function(e){p(u.Dl,(function(t){if("string"==typeof t||null===t)return m(e,"enduser.id",t,u.Dl,!0);(0,l.R)(41,typeof t)}),e)}(this),function(e){p(u.nb,(function(t){if("string"==typeof t||null===t)return m(e,"application.version",t,u.nb,!1);(0,l.R)(42,typeof t)}),e)}(this),function(e){p(u.d3,(function(){e.ee.emit("manual-start-all")}),e)}(this),this.run()):(0,l.R)(21)}get config(){return{info:this.info,init:this.init,loader_config:this.loader_config,runtime:this.runtime}}get api(){return this}run(){try{const e=function(e){const t={};return a.forEach((r=>{t[r]=!!e[r]?.enabled})),t}(this.init),t=[...this.desiredFeatures];t.sort(((e,t)=>n.P3[e.featureName]-n.P3[t.featureName])),t.forEach((t=>{if(!e[t.featureName]&&t.featureName!==n.K7.pageViewEvent)return;if(this.runSoftNavOverSpa&&t.featureName===n.K7.spa)return;if(!this.runSoftNavOverSpa&&t.featureName===n.K7.softNav)return;const r=function(e){switch(e){case n.K7.ajax:return[n.K7.jserrors];case n.K7.sessionTrace:return[n.K7.ajax,n.K7.pageViewEvent];case n.K7.sessionReplay:return[n.K7.sessionTrace];case n.K7.pageViewTiming:return[n.K7.pageViewEvent];default:return[]}}(t.featureName).filter((e=>!(e in this.features)));r.length>0&&(0,l.R)(36,{targetFeature:t.featureName,missingDependencies:r}),this.features[t.featureName]=new t(this)}))}catch(e){(0,l.R)(22,e);for(const e in this.features)this.features[e].abortHandler?.();const t=(0,_.Zm)();delete t.initializedAgents[this.agentIdentifier]?.features,delete this.sharedAggregator;return t.ee.get(this.agentIdentifier).abort(),!1}}}({features:[R,I,O],loaderType:"lite"})})()})();</script><script type="text/javascript">window.NREUM||(NREUM={});NREUM.info={"beacon":"bam.nr-data.net","queueTime":2,"licenseKey":"40b3e098a7","agent":"","transactionName":"NVYAbUpQVhIDW0JeWwwcJExWUkwIDVYZREQLUAdKTF5KBExbWVpZDV1MT1FUTxJYS1NWRgFb","applicationID":"29832197","errorBeacon":"bam.nr-data.net","applicationTime":30}</script>
<!-- Standard Favicon -->
<link rel="icon" type="image/x-icon" href="/static/img/icons/SnapEDA.ico"/>
<!-- Google+ publisher -->
<link href="https://plus.google.com/105383266825551010805" rel="publisher">

<style>
.join-hook-container{
    background-image: url(/static/img/join-hook-bg.png);
}
.social-facebook a {
  background-image: url(/static/img/icons/face.png);
}
.social-linkedin a{
    background-image: url(/static/img/icons/linkedin.png);
}
.social-twitter a{
    background-image: url(/static/img/icons/twitter.png);
}
</style>
<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

<!-- Convertize -->
<script src="//pixel.convertize.io/3693.js" charset="UTF-8"></script>




    <title>
        
    APX803L20-30SA-7 search results
 | SnapMagic Search
    </title>
    <style>
        .async-hide { opacity: 0 !important}

        

        .d-flex-center {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .label.speciality_skill{
            cursor: pointer;
        }
    </style>
    <script>(function(a,s,y,n,c,h,i,d,e){s.className+=' '+y;h.start=1*new Date;
    h.end=i=function(){s.className=s.className.replace(RegExp(' ?'+y),'')};
    (a[n]=a[n]||[]).hide=h;setTimeout(function(){i();h.end=null},c);h.timeout=c;
    })(window,document.documentElement,'async-hide','dataLayer',4000,
    {'GTM-WPWKB7R':true});</script>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-N2DMH7J');</script>
    <!-- End Google Tag Manager -->
    <script>
      (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
      ga('create', '***********-1', 'auto');
      ga('require', 'GTM-WPWKB7R');
      ga('send', 'pageview');
    </script>
        <script>
            !function(){var analytics=window.analytics=window.analytics||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else{analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","once","off","on","addSourceMiddleware","addIntegrationMiddleware","setAnonymousId","addDestinationMiddleware"];analytics.factory=function(e){return function(){var t=Array.prototype.slice.call(arguments);t.unshift(e);analytics.push(t);return analytics}};for(var e=0;e<analytics.methods.length;e++){var key=analytics.methods[e];analytics[key]=analytics.factory(key)}analytics.load=function(key,e){var t=document.createElement("script");t.type="text/javascript";t.async=!0;t.src="https://cdn.segment.com/analytics.js/v1/" + key + "/analytics.min.js";var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n);analytics._loadOptions=e};analytics._writeKey="R33QdDxhZ5YzaUts0oFQgxD5FNZqV3TG";;analytics.SNIPPET_VERSION="4.15.3";
            analytics.load("R33QdDxhZ5YzaUts0oFQgxD5FNZqV3TG");
            analytics.page();
            }}();
            </script>
                <!-- Start VWO Async SmartCode -->
    <script type='text/javascript'>
        window._vwo_code = window._vwo_code || (function(){
        var account_id=612614,
        settings_tolerance=2000,
        library_tolerance=2500,
        use_existing_jquery=false,
        is_spa=1,
        hide_element='body',
        
        
        f=false,d=document,code={use_existing_jquery:function(){return use_existing_jquery;},library_tolerance:function(){return library_tolerance;},finish:function(){if(!f){f=true;var a=d.getElementById('_vis_opt_path_hides');if(a)a.parentNode.removeChild(a);}},finished:function(){return f;},load:function(a){var b=d.createElement('script');b.src=a;b.type='text/javascript';b.innerText;b.onerror=function(){_vwo_code.finish();};d.getElementsByTagName('head')[0].appendChild(b);},init:function(){
        window.settings_timer=setTimeout(function () {_vwo_code.finish() },settings_tolerance);var a=d.createElement('style'),b=hide_element?hide_element+'{opacity:0 !important;filter:alpha(opacity=0) !important;background:none !important;}':'',h=d.getElementsByTagName('head')[0];a.setAttribute('id','_vis_opt_path_hides');a.setAttribute('type','text/css');if(a.styleSheet)a.styleSheet.cssText=b;else a.appendChild(d.createTextNode(b));h.appendChild(a);this.load('https://dev.visualwebsiteoptimizer.com/j.php?a='+account_id+'&u='+encodeURIComponent(d.URL)+'&f='+(+is_spa)+'&r='+Math.random());return settings_timer; }};window._vwo_settings_timer = code.init(); return code; }());
        </script>
        <!-- End VWO Async SmartCode -->
    <link href='//fonts.googleapis.com/css?family=Open+Sans:300,400,600' rel='stylesheet' type='text/css'>
    <link href="/static/css/core.min.css" rel="stylesheet" type="text/css" media="all" />

    <!-- EXTRA_HEAD -->
    
        

    <meta name="keywords" content="symbols, footprints, APX803L20-30SA-7, download, spice models, free, cad library, cad part, pcb, eagle symbol, eagle footprint, altium, kicad, orcad" />
    <meta name="description" content="Download schematic symbols, PCB footprints, pinout &amp; datasheets for APX803L20-30SA-7. Exports to OrCAD, Allegro, Altium, PADS, Eagle, KiCad &amp; Pulsonix." />
    <link rel="stylesheet" href="/static/css/search.css">

    <style>
    #nav-search-part-form, #navbar-search-form {
        display: none !important;
    }
    .featured_part{
        display: none !important;
    }
    .ad_view_left{
        margin-top: 15px;
        float:left;
        display: none;
    }
    .search_title{
        margin: 20px 20px -40px 20px;
        text-align: right;
        font-size: 22px;
        color: #777;
        font-weight: 300;
    }

    .rec-sect{
        margin-bottom: 15px;
    }
    .rec-link{
        float: left;
        margin-right: 10px;
        color: #777;
        text-decoration: underline;
    }

    .standard_used_cont {
        border:1px dashed #f6bb42;
        padding:7px 10px;
        display:inline-block;
        width:auto;
        border-radius:30px;
        background-color:#fdf5e7;
        color:#ff761a;
        font-weight:600;
        margin:0px 10px 10px;
    }

    .standard_used_icon{
        float:left;
        margin-right:10px;
        font-size:14px;
        color:#ff761a;
    }

    .tooltip {
        z-index: 100000000;
    }

    .bnmSearch {
    top: 15%!important;
    left: 50%;
    display: none;
    position: fixed;
    opacity: 1;
    z-index: 11000;
    width: 680px;
    margin-left: -340px;
    }

    .bnmSearch > .tab-content {
        display: block !important;
        padding: 15px;
        max-height: calc(70vh - 104px);
    }

    .bnm-overlay {
        width: 100vw;
        height: 100vh;
        position: fixed;
        z-index: 10;
        background: rgba(0,0,0,0.4);
        top: 0;
        left: 0;
        display: none;
    }

    .bnmSearch .price_row {
        border-top: 1px solid #d3d3d3;
    }

    #one-priceperquant {
        background: #fff;
        color: #000;
        width: 128px;
        padding-left: 3px;
        margin-left: auto;
        margin-right: auto;
        padding-top: 3px;
        padding-bottom: 3px;
        text-align: left;
    }

    /* .distributor {
        width: 180px!important;
        display: block;
        padding-right: 0!important;
    } */

    .col-distributor {
        position: relative;
        /* display: flex; */
        align-items: center;
    }

    .pt-24 {
        padding-top: 24px !important;
    }

    .price_column select, .select-wrapper.digikey_price_left select {
        color: black;
        border-color: transparent;
        border-bottom: 1px solid #ccc;
        background-color: transparent;
        padding: 0;
        font-size: 16px;
        font-weight: 300;
        width: 160px;
        margin: 0;
        -webkit-border-radius: 0;
        cursor: pointer;
    }

    .buy {
        padding: 3px 20px 3px 20px;
    }

    .callout-banner {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .callout-banner .description-text {
        max-width: 50%;
    }

    .manuf-banner {
        background: #F2F2F2;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 40px;
        width: 168px;
        border-radius: 50px;
    }

    .manuf-banner-ew {
        width: 200px;
    }

    .manuf-banner a {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.25rem;
        color: #3A3A3A !important;
        text-decoration: none !important;
        font-size: 14px;
        font-weight: 600;
    }
    .part-result.ti-banner .manuf-banner a{
        color: #08c !important;
    }

    .manuf-banner img {
        width: 26px;
        margin-right: 6px;
    }
    .manuf-banner img.analog-d-logo {
        width: 20px;
    }

    .div-banner {
        width: 50%;
        display: flex;
        justify-content: center;
    }

    .price-result .manuf-banner {
        background: #F2F2F2;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 32px;
        width: 120px;
        border-radius: 50px;
    }

    .price-result .manuf-banner img {
        width: 20px;
        margin-right: 2px;
    }

    .price-result .manuf-banner a {
        color: #3A3A3A !important;
        text-decoration: none !important;
        font-size: 9px;
        font-weight: 600;
    }

    .featured-tag {
        background: #436182;
        color: #fff;
        font-size: 12px;
        height: 20px;
        display: flex;
        align-items: center;
        padding-left: 5px;
        width: 80px;
        position: absolute;
        top: 0px;
        left: 0px;
    }

    .featured-star {
        color: #ff761a;
        font-size: 16px;
        margin-right: 5px;
    }

    .col-distributor img {
        width: 80px;
        image-rendering: -webkit-optimize-contrast;
    }

    @media (max-width:767px) {
    .bnmSearch {
      width: 359px;
      margin-left: -180px;
    }
    .search_title{
        margin: 20px 20px -20px 20px;
    }
    .search-results-table{
        overflow: scroll;
        display: block;
    }
    }

    </style>

    <style>
        /** CSS for SnapVerified Badge **/
        .snapverifiedtooltip[data-show] {
            display: block;
        }
        .snapverifiedtooltip {
            background-color: #FFFFFF;
            box-shadow: 0px 0px 3px 0px #e1e1e1;
            border: 1px solid #E1E1E1;
            color: #2C2C35;
            padding: 20px;
            border-radius: 8px;
            font-size: 14px;
            display: none;
        }
        .arrow, .arrow::before {
            position: absolute;
            width: 24px;
            height: 24px;
            background-color: #FFFFFF;
            box-shadow: 0px 0px 3px 0px #e1e1e1;
            border: 1px solid #E1E1E1;
        }

        .arrow {
            visibility: hidden;
            overflow: hidden;
        }

        .arrow::before {
            visibility: visible;
            content: '';
            transform: rotate(45deg);
            position: absolute;
            top: 12px;
            left: 0px;
        }

        .snapverified {
            font-size: 14px !important;
        }

        .snapverified svg {
            width: 13px !important;
            height: 13px !important;
        }

        .snapverifiedtooltip svg {
            height: 15px !important;
        }

        .snapverifiedtooltip[data-popper-placement^='top'] > .arrow {
            bottom: -22px;
            left: -55px !important;
            transform: translate(112px, 0px) scale(-1) !important;
        }

        #snapverifiedtooltipinline.snapverifiedtooltip[data-popper-placement^='top'] > .arrow {
            left: 14px !important;
        }

        .snapverifiedtooltip[data-popper-placement^='bottom'] > .arrow {
            top: -25px;
        }

        .snapverifiedtooltip[data-popper-placement^='bottom'] > .arrow {
            top: -25px;
            left: -64px !important;
        }

        #snapverifiedtooltipsponsored0.snapverifiedtooltip[data-popper-placement^='bottom'] > .arrow,
        #snapverifiedtooltipsponsored1.snapverifiedtooltip[data-popper-placement^='bottom'] > .arrow,
        #snapverifiedtooltipsponsored2.snapverifiedtooltip[data-popper-placement^='bottom'] > .arrow,
        #snapverifiedtooltipfeatured.snapverifiedtooltip[data-popper-placement^='bottom'] > .arrow,
        #snapverifiedtooltipinline.snapverifiedtooltip[data-popper-placement^='bottom'] > .arrow,
        #snapverifiedtooltipdigikey.snapverifiedtooltip[data-popper-placement^='bottom'] > .arrow {
            left: 3px !important;
        }

        .snapverifiedtooltip[data-popper-placement^='left'] > .arrow {
            right: -12px;
        }

        .snapverifiedtooltip[data-popper-placement^='right'] > .arrow {
            left: -12px;
        }
    </style>

    
    
          

<script type="module">
  const baseUrl = "https://copilot.snapmagic.com/";
  window.intercomSettings['alignment'] = 'left';
  import(`${baseUrl}/embed/copilotweb.js`).then(({ init }) => {
    const isLoggedIn = false;
    const isStaff = false;
    const reqUser = 'AnonymousUser';
    let user = {
        id: '',
        email: '',
        username: '',
        is_anonymous: !isLoggedIn,
        token: "",
        isWidget: 'true',
    }

    if (isLoggedIn) {
        user.id = 'None';
        user.email = '';
        user.username = '';
        user.avatar_url = '';
        user.token = '';
    }

    window.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'COPILOT_LOGIN_REDIRECT') {
                window.location.href = `/account/login/?plugin=copilot_web&next=/home/<USER>
        }
        if (event.data && event.data.type === 'COPILOT_SIGNUP_REDIRECT') {
                window.location.href = `/account/signup/?plugin=copilot_web&next=/home/<USER>
        }
        if (event.data && event.data.type === 'COPILOT_SEARCH_REDIRECT') {
            const partName = event.data.partName;
            if (partName) {
                window.location.href = `/search/?q=${encodeURIComponent(partName)}&autoOpenCopilot=true`;
            }
        }
        if (event.data && (event.data.type === 'COPILOT_PART_REDIRECT' || event.data.type === 'COPILOT_DATASHEET_REDIRECT') && event.data.endpoint) {
            const endpoint = event.data.endpoint.trim();

            let cleanEndpoint = endpoint;
            if (cleanEndpoint.endsWith('?') || cleanEndpoint.endsWith('&')) {
                cleanEndpoint = cleanEndpoint.slice(0, -1);
            }

            const separator = cleanEndpoint.includes('?') ? '&' : '?';
            window.location.href = cleanEndpoint + separator + 'autoOpenCopilot=true';
        }
        if (event.data && event.data.type === 'COPILOT_COPY_TEXT' && event.data.text) {
            navigator.clipboard.writeText(event.data.text);
        }
    });
    const urlParams = new URLSearchParams(window.location.search);
    const autoOpen = urlParams.get('autoOpenCopilot') === 'true';
    const apiUrl = "https://snapmagic-ai-61a5bb40393e.herokuapp.com/";
    if (isLoggedIn) {
        init({
          appUrl: `${baseUrl}#/chat/?isWidget=true&userId=${user.id}&email=${user.email}&name=${user.username}&token=${user.token}&isLoggedIn=${isLoggedIn}&parentUrl=${encodeURIComponent(window.location.href)}`,
          apiUrl: apiUrl,
          autoOpen,
          stickyNavbarSelectors: ['#seda-navbar', '#s-navbar'],
          user: user,
        });
    } else {
      init({
        appUrl: `${baseUrl}#/login/?parentUrl=${encodeURIComponent(window.location.href)}`,
        apiUrl: apiUrl,
        stickyNavbarSelectors: ['#seda-navbar', '#s-navbar'],
      });
    }
  }).catch((error) => {
    console.error("SnapMagic Copilot: Error loading copilotweb.js", error);
  });
</script>

    

 
<!-- Facebook Pixel Code -->
<!-- <script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', '339053146617437');
  fbq('track', 'PageView');
</script>
<noscript><img height="1" width="1" style="display:none"
  src="https://www.facebook.com/tr?id=339053146617437&ev=PageView&noscript=1"
/></noscript> -->
<!-- End Facebook Pixel Code -->

    <!--load FOMO script -->
    <script src='https://load.fomo.com/api/v1/VRrmLWeQ3uCcECxLw7VDjw/load.js' async></script>
</head>

<body class="">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N2DMH7J"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <div id="sticky-header">

    







<!-- Convertize -->
<script src="//pixel.convertize.io/3693.js" charset="UTF-8"></script>
 
<!-- <script src="https://posthog-analytics.herokuapp.com/static/array.js"></script>
<script>
    posthog.init('LUXEDZEuoIJH3N6eecBJkyqiGL1oJNVDG8t3MaGeChY', {api_host: 'https://posthog-analytics.herokuapp.com'});
    var authenticated = false;
    
    posthog.identify(authenticated ? "" : "null");
    posthog.people.set({
        "email": authenticated ? "" : "null"
    });
</script> -->
 

<script async src="https://www.googletagmanager.com/gtag/js?id=AW-865545408"></script> 
<script> 
    function containsAny(str, substrings) {
        for (var i = 0; i != substrings.length; i++) {
        var substring = substrings[i];
        if (str.indexOf(substring) != - 1) {
            return substring;
        }
        }
        return false; 
    }
    if(!containsAny(window.location.href,['home','view-part','search'])){
        $('#questions').show()
    }

    window.dataLayer = window.dataLayer || []; 
    
    function gtag() {
        dataLayer.push(arguments);
    } 
    
    gtag('js', new Date()); 
    
    gtag('config', 'AW-865545408'); 
    
</script>


<script> 
    // url param used for redirecting user to the url page. redirect checks if it should redirect the user or not because in some cases we only want to track not redirect
    function gtag_report_conversion(url, redirect) { 
        if(url === undefined) {
            url = window.location.href;
        } 
        if(redirect === undefined) {
            redirect = false;
        }
        var callback = function (redirect) {
            if (redirect && typeof(url) != 'undefined') { 
                window.location = url; 
            } 
        }; 
        
        currUrl = window.location.href;
        if(currUrl.indexOf('part_buy=success') > 0 ) { 
             
            if(currUrl.indexOf('&part_buy=success') > -1 ) {
                currUrl = currUrl.replace('&part_buy=success',''); 
            } else if(currUrl.indexOf('?part_buy=success') > -1 ) {
                currUrl = currUrl.replace('?part_buy=success',''); 
            }
            window.history.replaceState({}, '' , currUrl); // remove param from URL
            gtag('impression', 'conversion', { 'send_to': 'AW-865545408/oWp-CLW2mHMQwNncnAM', 'transaction_id': '', 'event_callback': callback }); 
        }
        return false; 
    }  
         
</script>

<style>
    .form_row {
      display: grid;
      grid-template-columns: 150px min-content 200px; /* Fixed width for labels and layers, min-content for colons */
      align-items: center;
      gap: 8px; /* Adjust the gap between the elements as needed */
      margin-bottom: 0px; /* Adds space between each row */
    }

    .form_row label {
      justify-self: start; /* Aligns the label text to the start (left) */
    }

    .form_row .colon {
      justify-self: center; /* Centers the colon */
    }

    .form_row .header{
      font-size: 14px;
      font-weight: bolder;
      margin-top: 6px;
      color: #FF761A;
    }

    /* Optional: Ensure the select elements have a consistent style */
    .form_row select {
      width: 100%; /* Makes the dropdown take the full width of its grid column */
    }

    .extended-search-input{
        width: 100%!important;
    }
    .extended-search-bar{
        max-width: 100%;
        /* width: calc( 100% - 953px ); */
        margin-right: 16px;
        width: 100%;
    }
    .extended-search-container{
        display: flex!important;
    }
    #HW_badge {
        width: 45px !important;
        height: 45px !important;
        position: absolute !important;
        left: -25px !important;
        top: -5px !important;
        background: transparent !important; 
        color: transparent !important;
    }

    .diamond {
        margin-left: 30px;
        font-size: 25px;
        margin-right: -20px;
        color: rgba(150, 200, 255, 1) !important;
    }

    .flex-center {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .updates {
        margin-right: 15px;
        width: 40px;
        margin-left: -10px; 
    }


    #seda-navbar {
            font-family: 'Open Sans';
            font-size: 16px;
            background: #FFFFFF;
            border: 1px solid #E5E5E5;
            box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
            min-height: 78px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            color: #3a3a3a;
            position: fixed;
            width: calc(100% - 16px);
            padding-left: 16px;
            z-index: 1000;
        }
  
        #seda-navbar .nav-bar-items { 
            display: flex;
            align-items: center;
        }

        #seda-navbar .nav-bar-item {
            font-family: Open Sans;
            font-style: normal;
            font-weight: normal;
            font-size: 16px;
            line-height: 22px;
            display: flex;
            align-items: center;
            color: #3A3A3A; 
            margin-left: 32px;
            cursor: pointer;
            text-decoration: none !important;
        }

        #seda-navbar .nav-bar-item .symbol-down {
            transform: rotate(90deg) scale(1,1.75);
            margin-left: 10px;
        }
        .symbol-down-instabuild{
            transform: rotate(90deg) scale(.83333,1.4583333);;
            margin-left: 3px;
            display: inline-block;
        }

        #seda-navbar .nav-bar-item .nav-bar-dropdown {
            position: absolute;
            top: 50px;
            display: none;
            min-width: 200px;
            min-height: 50px;
            background: #FFFFFF;
            border: 1px solid #E5E5E5;
            box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
            border-top: 0px;
            padding-top: 8px;
            padding-bottom: 8px;
            cursor: pointer;
            font-size: 14px;
            z-index: 2;
        }

        #seda-navbar .nav-bar-item:hover .nav-bar-dropdown {
            display: block;
        }

        #seda-navbar .nav-bar-item .nav-bar-dropdown .dropdown-option {
            padding: 10px;
            text-decoration: none !important;
            display: block;
            color: #3a3a3a;
        }

        #seda-navbar .nav-bar-item .nav-bar-dropdown .dropdown-option:hover { 
            background: #ff761a; 
        }

        #seda-navbar #nav-logo-link {
            float: none;
            margin: 0px !important;
            margin-top: -6px !important;
        }

        .ls-btn {
            width: 96px;
            height: 40px;
            box-sizing: border-box;
            border-radius: 8px; 
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
            margin-right: 16px;
            text-decoration: none !important;
        }

        .login-btn {
            color: #3B5D83; 
            border: 1px solid #3B5D83;
        }

        .login-btn:hover {
            color:#fff;
            background: #3B5D83;
        }

        .signup-btn {
            color: #ffffff; 
            background: #FF761A;
            border: 1px solid #FF761A;
        }

        .signup-btn:hover {
            color: #FF761A;
            background: #FFFFFF;
        }

        .ls-section {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            position: absolute;
            right: 16px; 
        }

        
        #nav-search-part-form {
            height: 48px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0px;
            margin: 0px;
            position: absolute;
            right: 256px;
        }

        #nav-searchbar-wrapper {
            margin: 0px;
            padding: 0px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #searchbar-input {
            height: 40px !important;
            border: 1px solid #3B6285 !important;
            box-sizing: border-box;
            border-radius: 8px !important;
            border-top-right-radius: 0px !important;
            border-bottom-right-radius: 0px !important;
            font-family: Open Sans;
            font-family: Open Sans;
            font-style: normal;
            font-weight: normal;
            font-size: 14px !important;
            line-height: 19px;
            margin: 0px;
            min-width: 240px;
            padding-left: 16px;
            padding-right: 16px;
        }

        #nav-search-btn {
            height: 40px;
            width: 112px;
            background: #3B6285;
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
            font-family: Open Sans;
            font-style: normal;
            font-weight: bold;
            font-size: 14px;
            line-height: 19px; 
            color: #FFFFFF;
            border: 1px solid #3b6285;
        }

        .btn-search-icon {
            display: none  !important;
        }

        #seda-navbar .nav-logo-link {
            margin-top: -10px;
        }

        #seda-navbar .r-menu-btn {
            display: none;
            position: absolute;
            top: 12px;
            right: 22px;
            width: 32px;
            height: 32px;
            background: #3B6285;
            border: 1px solid #3B6285;
            box-sizing: border-box;
            border-radius: 8px;
            color: #fff;
            font-size: 22px; 
            justify-content: center;
            align-items: center;
            cursor: pointer;
            text-decoration: none !important;
        } 

        @media (max-width:1200px) {

            #seda-navbar{
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
            }

            #seda-navbar #nav-logo-link { 
                margin: 16px 10px 0 0 !important;
            }

            .section-search-part {
                display: flex !important;
            }
 
            #seda-navbar .nav-bar-items {
                width: 100%;
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
                padding-left: 0px;
            } 

            #seda-navbar .nav-bar-item-dd {
                padding: 0px !important;
            }

            #seda-navbar .nav-bar-item-dd {
                padding: 0px;
            }

            #seda-navbar .nav-bar-item .nav-item-text, 
            #seda-navbar .nav-bar-item .symbol-down {
                display: none !important;
            }
            #seda-navbar .nav-bar-item {
                margin-left: 0px;
                padding: 15px;
                padding-left: 0px;
            }

            #seda-navbar .nav-bar-item .symbol-down {
                display: flex;
            }

            #seda-navbar .nav-bar-item .nav-bar-dropdown {
                position: relative;
                display: flex !important;
                top: 0px;
                margin-left: 0px;
                border: none;
                box-shadow: none;
                padding: 0px;
            }

            #seda-navbar .nav-bar-item .nav-bar-dropdown .dropdown-option {
                padding-left: 0px; 
                width: 128px;
            }

            #seda-navbar .nav-bar-item .nav-bar-dropdown .dropdown-option:hover {
                background: #fff;
            }

            .ls-section {
                width: 100%;
                display: flex;
                position: initial; 
                margin-left: -10px;
                padding-bottom: 10px;
            } 

            .ls-btn {
                width: 50%;
                margin-right: 0px !important;
            }
            
            .login-btn {
                margin-right: 16px !important;
            }

            #nav-search-part-form {
                position: initial;
                display: block;
                margin-bottom: 10px;
                width: 100%;
                margin-left: -10px;
            }

            #nav-searchbar-wrapper { 
                margin-right: 0;
                width: 100%;
                max-width: 100%;
            }

            #searchbar-input {
                min-width: calc(100% - 112px) !important;
            }

            #seda-navbar .r-menu-btn {
                display: flex;
                top: 16px;
                right: 24px;
            }
        }

        @media (max-width:550px) { 

            .btn-search-text {
                display: none;
            }

            .btn-search-icon {
                display: block !important;
                color: #fff;
                margin: 0 auto;
            } 
        }
 
        @media (max-width:712px) {
            #seda-navbar .nav-bar-item .nav-bar-dropdown {
                flex-direction: column;
            } 
        }

    
    
    @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
        

        #HW_badge {
            margin-left: 5px !important;
        }

        #recent_news span:first-child {
            margin-left: 20px;
        }

        #seda-navbar {
            min-height: 56px !important;
        }

        #seda-navbar {
            padding-top: 24px;
        }

        #nav-search-part-form {
            top: 16px;
        }

        .ls-section {
            top: 18px;
        }

        .nav-bar-dropdown {
            margin-left: -120px;
            top: 36px;
        }

        .make-snap-header {
            height: 56px !important;
        }

        .snap-card img {
            width: 72px;
            height: 60px;
        }

        .feed-item img {
            width: 80px !important;
            height: 80px !important;
        } 
    }

    .alert {
        margin-bottom: 0px !important;
    }

    

	/*the container must be positioned relative:*/
	.autocomplete {
		position: relative; 
	} 

	.autocomplete-items {
		position: absolute;
		border: 1px solid #d4d4d4;
		z-index: 10000000;
		top: 100%;
		left: 0;
		right: 0; 
	}

	.autocomplete-items div {
		padding: 10px;
		cursor: pointer;
		background-color: #fff;  
	}

	/*when hovering an item:*/
	.autocomplete-items div:hover {
		background-color: #FF761A; 
	}

	/*when navigating through the items using the arrow keys:*/
	.autocomplete-active {
		background-color: #FF761A !important; 
		color: #ffffff; 
	}

    .search-part-package-section .autocomplete-items {
        top: 39px !important;
    }

	

    .flex-align-center {
        display: flex !important;
        justify-content: center;
        align-items: center;
    }

    #remaining-instaparts {
        background: #3a5c84;
        width: fit-content;
        height: 22px;
        color: #fff;
        min-width: 40px;
        display: none;
        justify-content: center;
        align-items: center;
        border-radius: 50px;
        margin-left: 8px;
    }

    .asked_panel .profile img {
        width: 30px !important;
        height: 30px !important;
    }
    
    @supports (-webkit-touch-callout: none) {
    
        #seda-navbar .r-menu-btn {
        
            padding-bottom: 6px;
        
        }
    }

</style>


    
        <nav class="navbar navbar-default" id="seda-navbar">
            <a id="nav-logo-link" href="/" >
                <img src="/static/img/snapeda_logo_260.png" width="200" alt="SnapMagic Search logo" title="SnapMagic Search">
            </a>
            <span class="r-menu-btn">☰</span>
            <div class="nav-bar-items" >
                <div>
                    <a href="/about" class="nav-bar-item" > About </a>
                </div>
                <div>
                    <div class="nav-bar-item nav-bar-item-dd">
                        <span class='nav-item-text'>For Engineers </span>
                        <span class='symbol-down'>
                            &gt;
                        </span>

                        
                        <div class='nav-bar-dropdown'>
                            <a class='dropdown-option' href='/instabuild/'>
                                Build Parts
                            </a>
                            <a class='dropdown-option' href='/instapart/'>
                                Request Parts
                            </a>
                            <a class='dropdown-option' href='/discover/'>
                                Browse Parts
                            </a>
                            <a class='dropdown-option' href='/pricing/'>
                                Pricing
                            </a>
                            <a class='dropdown-option' href='/questions/'>
                                Q &amp; A
                            </a>
                            <a class='dropdown-option' href='/pcb-manufacturing/'>
                                PCB Suppliers
                            </a>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="nav-bar-item nav-bar-item-dd">
                        <span class='nav-item-text'>For Part Vendors </span>
                        <span class='symbol-down'>
                            &gt;
                        </span>
                        
                        <div class='nav-bar-dropdown'>
                            <a class='dropdown-option' href='/instapublish/'>
                                Publish
                            </a>
                            <a class='dropdown-option' href='https://mediakit.snapeda.com/kit2023' target="_blank">
                                Media Kit
                            </a>
                            <a class='dropdown-option' href="https://insights.snapeda.com/">
                                SnapInsights
                            </a>
                            <a class='dropdown-option' href="/get-cad-models">
                                Get CAD Models
                            </a>
                            <a class='dropdown-option' href="https://insights.snapeda.com/syndication">
                                Syndication Program
                            </a>
                            <a class='dropdown-option' href="/about/#contact_us">
                                Contact Us
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <form id="nav-search-part-form" action="/search/?q=" method="GET">
                <div id="nav-searchbar-wrapper" class="search-wrapper searchbox-big">
                    <input id="searchbar-input" type="text" name="q" class="search-query search-input" placeholder="Search Parts" value="" required="">
                    <input class="search-type-top" type="hidden" name="search-type" value="parts" >
                    
                    <button id="nav-search-btn" type="submit" >
                        <i class="fa fa-search btn-search-icon"></i>
                        <span class="btn-search-text">Search Parts</span>
                    </button>
                </div>
            </form>
            <div class='ls-section'>
                <a href="/account/login?next=/search/%3Fq%3DAPX803L20-30SA-7" class="ls-btn login-btn">
                    Log In
                </a>
                <a href="/account/signup?next=/search/%3Fq%3DAPX803L20-30SA-7" class="ls-btn signup-btn">
                    Sign Up
                </a>
            </div>
        </nav>
        <div style='padding-bottom:80px'></div>
    

<script>
    function trackSignup() {
        var queryString = window.location.search;
        var campaign = null;
        var referrer = document.referrer
        var query_params = {}

        // get value of campaign code if it exist
        window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, function (m, key, value) {
            if (key == 'welcome') {
                campaign = value;
            }
            query_params[key] = value;
        });

        if (campaign != null
            & referrer.indexOf('account/signup') != -1) {
            var properties = {
                'userId': '',
                'email': '',
                'company': query_params['company'],
                'campaign': campaign,
                'plugin': query_params['plugin']
            }
            var url = window.location.href.replace('welcome=' + campaign, '')

            // in case the welcome is in the middle of qs
            url = url.replace('&&', '&')
            // in case the welcome is at the beginning of qs
            url = url.replace('?&', '?')

            var mixpanelWait = setInterval(function () {
                if (mixpanel) {
                    mixpanel.track('Signup', properties)
                    clearInterval(mixpanelWait);
                }
            }, 100);
            window.history.pushState({}, document.title, url);

        }
    }


    window.onload = function(){
        var HW_config = {
            selector: "#recent_news",  
            account: "x9ndd7",
            translations: {
                footer: "More Updates"
            }
        }; 
        Headway.init(HW_config);
        trackSignup()


        
        $.fn.isInViewport = function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();

            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();

            return elementBottom > viewportTop && elementTop < viewportBottom;
        };

        $('.r-menu-btn').click(function(){
            $('.nav-bar-items, #nav-search-part-form, .ls-section').slideToggle();
        });

        if($(window).width() > 1200) {
            $('.nav-bar-items, #nav-search-part-form, .ls-section').show();
        } else {
            $('.nav-bar-items, #nav-search-part-form, .ls-section').hide();
        }
        $(window).on('resize', function() {
            if($(window).width() > 1200) {
                $('.nav-bar-items, #nav-search-part-form, .ls-section').show();
            } else if($(window).width() > 576) {
                $('.nav-bar-items, #nav-search-part-form, .ls-section').hide();
            }
        });

        
        var ua = window.navigator.userAgent;
        var msie = ua.indexOf("MSIE "); 
        var urlList = ['home', 'instapart', 'questions', 'pricing', 'discover', 'about', 'instapublish', 'login', 'signup', 'instabuild'];
        var cl = window.location.href;
        if( cl == (window.location.origin + "/") ) {
                $('.r-menu-btn').on('click', function() {
                    const body = document.querySelector("body");
                    var x = document.getElementById("seda-navbar");
                    if (x.style.display === "flex" || x.style.display === "") {
                        x.style.display = "block";
                        x.style.paddingTop = "16px";
                        body.style.overflow = "hidden";
                        $(".ls-section").css("display", "flex");
                    } else {
                        x.style.display = "flex";
                        x.style.paddingTop = "0px";
                        body.style.overflow = "auto";
                        $(".ls-section").css("display", "");
                    }
                });
                $('#seda-navbar').css('padding-left','16px');
                if($('#seda-navbar').css('min-height') === '72px') {
                    $('#seda-navbar').css('min-height','56px'); 
                } else {
                    $('#seda-navbar').css('min-height','78px'); 
                }
            } 
        urlList.forEach( function(element) {
            if( cl.indexOf(element) > 0 ) {
                $('.r-menu-btn').on('click', function() {
                    const body = document.querySelector("body");
                    var x = document.getElementById("seda-navbar");
                    if (x.style.display === "flex" || x.style.display === "") {
                        x.style.display = "block";
                        x.style.paddingTop = "16px";
                        body.style.overflow = "hidden";
                        $(".ls-section").css("display", "flex");
                        $(".ls-section").css("marginBottom", "10px");
                        $("#nav-search-part-form,.ls-section").css("width", "96%");
                    } else {
                        x.style.display = "flex";
                        x.style.paddingTop = "0px";
                        $(".ls-section").css("display", "");
                        $(".ls-section").css("marginBottom", "");
                        $("#nav-search-part-form,.ls-section").css("width", "");
                        body.style.overflow = "auto";
                    }
                });
                $('#seda-navbar').css('width','calc(100% - 16px)');
                $('#seda-navbar').css('padding-left','16px');
                if($('#seda-navbar').css('min-height') === '72px') {
                    $('#seda-navbar').css('min-height','56px'); 
                } else {
                    $('#seda-navbar').css('min-height','78px'); 
                }
            } 
        });  
    };
</script>
<script async src="https://cdn.headwayapp.co/widget.js"></script>


<script>

    $(document).ready( function() {
        
    });

    
    
    function addAutoComplete(id) {
        document.getElementById(id).addEventListener("keyup", function(event){
            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function() {
                if (this.readyState == 4 && this.status == 200) { 
                    var response = JSON.parse(this.response);
                    autocomplete(document.getElementById(id), response);
                }
            };
            xhttp.open("GET", "/api/v1/search_autocomplete?q=" + this.value, true);
            xhttp.send();
            if (event.keyCode === 13) {
                $('.search-autocom-submit').click()
            }

        });
    }

    function autocomplete(inp, arr) { 
        var currentFocus;  
        var a, b, i, val = inp.value; 
        closeAllLists();
        if (!val) { return false;}
        currentFocus = -1; 
        a = document.createElement("DIV");
        a.setAttribute("id", inp.id + "autocomplete-list");
        a.setAttribute("class", "autocomplete-items"); 
        inp.parentNode.appendChild(a); 
        for (i = 0; i < arr.length; i++) {  
            b = document.createElement("DIV"); 
            b.innerHTML = "<strong>" + arr[i].substr(0, val.length) + "</strong>";
            b.innerHTML += arr[i].substr(val.length); 
            b.innerHTML += "<input type='hidden' value='" + arr[i] + "'>"; 
            b.addEventListener("click", function(e) { 
                inp.value = this.getElementsByTagName("input")[0].value; 
                closeAllLists();
                $('.search-autocom-submit').click()

            });
            a.appendChild(b); 
        } 
        inp.addEventListener("keydown", function(e) {
            var x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode == 40) { 
                currentFocus++; 
                addActive(x);
            } else if (e.keyCode == 38) { 
                currentFocus--; 
                addActive(x);
            } else if (e.keyCode == 13) { 
                e.preventDefault();
                if (currentFocus > -1) { 
                if (x) x[currentFocus].click();
                }
            }
        });
        function addActive(x) { 
            if (!x) return false; 
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = (x.length - 1); 
            if(x[currentFocus])
                x[currentFocus].classList.add("autocomplete-active");
        }
        function removeActive(x) { 
            for (var i = 0; i < x.length; i++) {
            x[i].classList.remove("autocomplete-active");
            }
        }
        function closeAllLists(elmnt) { 
            var x = document.getElementsByClassName("autocomplete-items");
            for (var i = 0; i < x.length; i++) {
            if (elmnt != x[i] && elmnt != inp) {
                x[i].parentNode.removeChild(x[i]);
            }
            }
        } 
        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });
        }

    
</script>

	    <div id="base">
	        
	        <div id="left_tabs">
	             
	        </div>
	        <div id="right_tabs" >
	             
	        </div>
	        
	            <div id="body">
	                <div class="row-fluid" id="subnav-outer">
	                    <div class="pull-right" id="subnav-inner">
                            
                        </div>
	                </div>

	                

<h1 class="search_title">Search Results | APX803L20-30SA-7</h1>

<div class="search-tabs">
    <div class="search-tab search-component active">All Parts</div>
    

    <div class="search-tab search-capacitor hidden">Capacitors</div>
    
    <a class="search-tab-link" href="/pcb-manufacturing/?ref=searchbartab" target="_blank">PCB Suppliers</a>
</div>
 

<form id="searchForm" class="searchForm" method="GET" action="/search/">

    <div class="hero-unit search-header" id="filters">
        <div class="row-fluid">
            <div class="span9 search-part-package-section ">

                <div class="row-fluid">
                    <!-- <div class="span9 no_ml autocomplete"> -->
                    <div class="span9 no_ml">
                        <input class="mainSearch inpt-search"
                            type="text"
                            
                            placeholder="Search by Part Number or Type"
                            
                             value='APX803L20-30SA-7' 
                            name="q" 
                            autocomplete="off"
                        >
                        <!--id="search_autocomplete" removed from input-->
                        
                        
                    </div>
                    <div class="span3 d-flex align-items-center mobile-search-right">
                        <input id="searchSubmit" type="submit" class="btn-search search-autocom-submit" tabindex="-1" value="Search" name="SEARCH">
                        <img src='/static/img/advanced-search.png' class='adv-search' >
                        <div style="margin: 0px 10px !important; display: none;" id="filter-spinner" class="spinner">
                            <div class="rect1"></div>
                            <div class="rect2"></div>
                            <div class="rect3"></div>
                            <div class="rect4"></div>
                            <div class="rect5"></div>
                        </div>
                        <img src='/static/img/advanced-search-selected.png' class='adv-search-selected' >
                    </div>
                </div>
                
        
                <div class="row-fluid">
                    <div id="filter-options" class="span12 ">
                        <!-- <span>Filter by:</span> -->
     
    
    
    
    
    
    
    

                        <!-- <input type="checkbox" name="has_symbol" value="1" id="symbol" />
                        <label for="has_symbol"> Has Symbol </label>

                        <input type="checkbox" name="has_footprint" value="1" id="footprint" />
                        <label for="has_footprint"> Has Footprint </label>

                        <input type="checkbox" name="has_3d" value="1" id="has3d" />
                        <label for="has_3d"> Has 3D Model </label>

                        <input type="checkbox" name="has_sim" value="1" id="has3d" />
                        <label for="has_sim"> Has Simulation </label>  -->

                        <input id="sortSelected" type="hidden" name="sort" value="">

                    </div>
                    <div class="package-example clr-spacial span12 hidden">
                        <p>For example: SOIC-8, DIP14, etc. (You can also search by the IPC Name)</p>
                    </div>
                </div> 
            </div> 

            <div class="span9 search-resistor search-resistor-section mleft0 hidden">

                <div class="row-fluid">
                    <div class="span9 no_ml">

                        <div class="span3">
                            <p class="input_label">Resistance</p>
                            <div>
                                <input type="text" class="resistor-input re-resistance" placeholder="10k"  name="resistance">
                            </div>

                        </div>
                        <div class="span3">
                            <p class="input_label">Tolerance</p>
                            <div>
                                <input type="text" class="resistor-input re-tolerance" placeholder="1%"  name="tolerance">
                            </div>

                        </div>
                        <!-- <div class="span3">
                            <p class="input_label">Manufacturer</p>
                            <div>
                                <input type="text" class="resistor-input re-manufacturer" placeholder="i.e. Panasonic"  name="manufacturer">
                            </div>
                        </div> -->
                        <div class="span3">
                            <p class="input_label">Case/Package/Footprint</p>








































                        </div>


                    </div>
                    <div class="span3">
                        <input id="searchSubmit" type="submit" class="btn-search" tabindex="-1" value="SEARCH" name="SEARCH"> 
                    </div>
                </div>

            </div>
            

            <input class="search-type" type="hidden" name="search-type" value="parts"/>

            <div class="span3">
                <div id="matchingproducts" class="" style="visibility:collapse;">
                    <p id='search-product'>APX803L20-30SA-7 Search Results</p>
                    <p id="matchingproductsvalue"></p>
                    <p>Products Found</p>
                </div>
            </div>

                    



        </div>


            <!--  -->

            <!-- <form class="searchForm" method="GET" action="">

            <div class="row-fluid">

                <div class="span6">


                        <input type='hidden' name='csrfmiddlewaretoken' value='E4b43xMHQNxotThXnMMpXjL4ydAJeLd49EFySOeyFRBZTy6cQonvNz1wLBMzBDyx' />
                        <input class="mainSearch inpt-search"
                                type="text"
                                placeholder="Search by Part Number or Type"
                                 value='APX803L20-30SA-7' 
                                name="q">
                        <input id="searchSubmit" type="submit" class="btn-search" tabindex="-1" value="SEARCH" name="SEARCH">



                </div>

                <div class="span3">
                    <div class="result-col" style="visibility:collapse;" id="sorter">
    <ul class="sort-by">
        <li id="sort-by-label">
            Sort by:
        </li>
        
            <li class="active">
                <a class="btn-sort" href="#" onclick="updateSortBy('');">Relevance</a>
            </li>
            <li>
                <a class="btn-sort" href="#" onclick="updateSortBy('mpn');">Part Number</a>
            </li>
        
    </ul>
</div>
                </div>

                <div class="span3">
                    <div id="matchingproducts" class="result-col" style="visibility:collapse;">
                        <h3>Matching Products <span id="matchingproductsvalue" class="badge"></span></h3>
                    </div>
                </div>

            </div>



            <div class="span12">
                <div id="filter-options">
                    <span>Filter by:</span>
                    <input type="checkbox" name="stockFilter" value="1" id="Stock" />
                    <label for="Stock"> In stock </label>
                    <input type="checkbox" name="leadFilter" value="1" id="Lead" />
                    <label for="Lead"> Lead free </label>
                    <input type="checkbox" name="RoHSFilter" value="1" id="RoHS" />
                    <label for="RoHS"> RoHS Compliant </label>
                    <input id="sortSelected" type="hidden" name="sort" value="">

                    <input type="checkbox" name="SymbolFilter" value="1" id="Symbol" />
                    <label for="Lead"> Has Symbol </label>
                    <input type="checkbox" name="FootprintFilter" value="1" id="Footprint" />
                    <label for="Lead"> Has Footprint </label>
                </div>
            </div> --> 
    </div>

    <div class='search-filters'>
        <div class="col-md-12 d-flex align-items-stretch">
            <div class="col-md w25 search-filter-section">
                <h2 class='section-heading'>CAD Models and Data</h2>
                <div class='cad-filters'> 
                    <div class="d-flex align-items-center" >
                        <input type="checkbox" name="has_datasheet" value="1" id="datasheet"  >
                        <img src="/static/img/search_icons/datasheet_orange.png" 
                            style="width:20px;"
                            data-toggle="tooltip" data-original-title="Datasheet">
                    </div>
                    <div class="d-flex align-items-center" >
                        <input type="checkbox" name="has_symbol" value="1" id="symbol" >
                        <img src="/static/img/search_icons/symbol_orange.png"
                            data-toggle="tooltip" data-original-title="Symbol">
                    </div>
                    <div class="d-flex align-items-center" >
                        <input type="checkbox" name="has_footprint" value="1" id="footprint" >
                        <img src="/static/img/search_icons/footprint_orange.png"    
                            data-toggle="tooltip" data-original-title="Footprint">
                    </div>
                    <div class="d-flex align-items-center" >
                        <input type="checkbox" name="has_3d" value="1" id="has3d" >
                        <img src="/static/img/search_icons/3d_model_orange.png"
                            data-toggle="tooltip" data-original-title="3D Model">
                    </div>
                    <div class="d-flex align-items-center" >
                        <input type="checkbox"  name="has_sim" value="1" id="has_sim" >
                        <img src="/static/img/search_icons/sim_orange.png"
                            data-toggle="tooltip" data-original-title="Simulation">
                    </div>
                </div>
            </div>
            <div class="col-md w25 search-filter-section">
                <h2 class='section-heading'>Manufacturer</h2>
                <div class='manufacturers-filter filter-list'> 
                </div>
            </div>
            <div class="col-md w25 search-filter-section">
                <h2 class='section-heading'>Mounting Type</h2>
                <div class='mounting-types-filter filter-list'>
                </div>
            </div>
            <div class="col-md w25 search-filter-section">
                <h2 class='section-heading'>Package</h2>
                <div class='packages-filter filter-list'>
                </div>
            </div>
            <div class="col-md w25 search-filter-section">
                <h2 class='section-heading'>Dimension</h2>
                <div class='dimensions-filter filter-list'>
                </div>
            </div>
            <div class="col-md w25 search-filter-section">
                <h2 class='section-heading'>Compliance</h2>
                <div class='compliance-filter filter-list'> 
                    <div class="d-flex align-items-center">
                        <input type="checkbox" id="Lead" name="leadFilter" value="1" >
                        <label class="list-item-label" for="Lead"> Lead free </label>
                    </div>
                    <div class="d-flex align-items-center">
                        <input type="checkbox" id="RoHS" name="RoHSFilter" value="1" >
                        <label class="list-item-label" for=""> RoHS Compliant </label>
                    </div>
                </div>
            </div>
            <div class="col-md w25 search-filter-section">
                <h2 class='section-heading'>Availability</h2>
                <div class='d-flex align-items-center'>
                All 
                <label class="switch mr-10 ml-10">
                    <input type="checkbox" id='avialabilityToggle' name="stockFilter" value="1" id="Stock" >
                    <span class="slider round"></span>
                </label>
                In Stock
                </div> 
            </div>
        </div>
        <div class='d-flex justify-content-end align-items-center'>
            <a class="btn-clear" onclick='clearAll()'>Clear All</a> 
            <input id="searchSubmit" type="submit" class="btn-see-parts" tabindex="-1" value="See Parts" name="SEARCH">
        </div>
    </div>
</form>   
 
<!-- <div class="result-col" style="visibility:collapse;" id="sorter">
    <ul class="sort-by">
        <li id="sort-by-label">
            Sort by:
        </li>
        
            <li class="active">
                <a class="btn-sort" href="#" onclick="updateSortBy('');">Relevance</a>
            </li>
            <li>
                <a class="btn-sort" href="#" onclick="updateSortBy('mpn');">Part Number</a>
            </li>
        
    </ul>
</div> -->



<div id="searching" class="spinner">
  <div class="bar rect1"></div>
  <div class="bar rect2"></div>
  <div class="bar rect3"></div>
  <div class="bar rect4"></div>
  <div class="bar rect5"></div>
</div>

<a href="" rel='leanModal'></a>

<div id="results">

<style>

    .featured_buttons .s-btn-small.top_line_view.ver2.mouser_color{
        background: #DC730A;
        border-color: #DC730A;
    }
    .featured_buttons .s-btn-small.top_line_buy.ver2.mouser_color, .sponsored_indicator.mouser_color{
        background: #004A85;
        border-color: #004A85;
    }
    .float-right {
        float: right;
    }
    .float-left {
        float: left;
    }
    

</style>


<div class="featured_part top_line_ad ad_buy" style="display: none !important">
    <div class="row-fluid">

        <div id="left_ad_view" class="span6 border-right ad_view">
            <div class="span4">
                <div class="manuf_img">
                    <img src="" alt="">
                </div>
                <div class="featured_img">
                    <a class='top_line_view' data-type='' href="">
                        <img src="" alt="">
                    </a>
                </div>
            </div>

            <div class="span8">
                <div class="sponsored_indicator_wrapper">
                    <div class="sponsored_indicator"><i class="fa fa-info" aria-hidden="true"></i> Sponsored</div>
                </div>
                <div class="featured_content">
                    <div class="featured_title_ver3">
                        <div class="distributor_title_container">
                            <a class="featured_title featured_title_alt top_line_view ver3" data-type='' href="#"></a>
                            <span id="snapverifiedlefttopline" class="snapverified" style="display: inline-block; color: #666460; font-weight: 400; line-height: inherit; font-size: 16px; cursor: pointer; transform: translateY(2px); margin-right: 5px" aria-describedby="tooltip">
                                <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_distributor)"/>
                                    <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                                    <defs>
                                    <radialGradient id="paint0_radial_304_117_distributor" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                                    <stop stop-color="#46B0ED"/>
                                    <stop offset="1" stop-color="#1A81BC"/>
                                    </radialGradient>
                                    </defs>
                                </svg>
                            </span>
                            <div id="snapverifiedtooltiplefttopline" class="snapverifiedtooltip" role="tooltip">
                                <div class="arrow" data-popper-arrow></div>
                                <div style="display: flex; flex-direction: column; font-color: #2C2C35; font-weight: 400; gap: 8px;">
                                    <span style="font-weight: 600; color: #304e70; display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="22" height="22" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_distributor)"/>
                                        <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                                        <defs>
                                        <radialGradient id="paint0_radial_304_117_distributor" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                                        <stop stop-color="#46B0ED"/>
                                        <stop offset="1" stop-color="#1A81BC"/>
                                        </radialGradient>
                                        </defs>
                                        </svg>
                                        This product is verified
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="22" height="22" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6.25 1V2.5M10.75 1V2.5M6.25 14.5V16M10.75 14.5V16M14.5 6.25H16M14.5 10H16M1 6.25H2.5M1 10H2.5M6.1 14.5H10.9C12.1601 14.5 12.7902 14.5 13.2715 14.2548C13.6948 14.039 14.039 13.6948 14.2548 13.2715C14.5 12.7902 14.5 12.1601 14.5 10.9V6.1C14.5 4.83988 14.5 4.20982 14.2548 3.72852C14.039 3.30516 13.6948 2.96095 13.2715 2.74524C12.7902 2.5 12.1601 2.5 10.9 2.5H6.1C4.83988 2.5 4.20982 2.5 3.72852 2.74524C3.30516 2.96095 2.96095 3.30516 2.74524 3.72852C2.5 4.20982 2.5 4.83988 2.5 6.1V10.9C2.5 12.1601 2.5 12.7902 2.74524 13.2715C2.96095 13.6948 3.30516 14.039 3.72852 14.2548C4.20982 14.5 4.83988 14.5 6.1 14.5ZM7.45 10.75H9.55C9.97004 10.75 10.1801 10.75 10.3405 10.6683C10.4816 10.5963 10.5963 10.4816 10.6683 10.3405C10.75 10.1801 10.75 9.97004 10.75 9.55V7.45C10.75 7.02996 10.75 6.81994 10.6683 6.65951C10.5963 6.51839 10.4816 6.40365 10.3405 6.33175C10.1801 6.25 9.97004 6.25 9.55 6.25H7.45C7.02996 6.25 6.81994 6.25 6.65951 6.33175C6.51839 6.40365 6.40365 6.51839 6.33175 6.65951C6.25 6.81994 6.25 7.02996 6.25 7.45V9.55C6.25 9.97004 6.25 10.1801 6.33175 10.3405C6.40365 10.4816 6.51839 10.5963 6.65951 10.6683C6.81994 10.75 7.02996 10.75 7.45 10.75Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        SnapMagic crafted CAD
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="19" height="22" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8.5 7.75H4M5.5 10.75H4M10 4.75H4M13 4.6V12.4C13 13.6601 13 14.2902 12.7548 14.7715C12.539 15.1948 12.1948 15.539 11.7715 15.7548C11.2902 16 10.6601 16 9.4 16H4.6C3.33988 16 2.70982 16 2.22852 15.7548C1.80516 15.539 1.46095 15.1948 1.24524 14.7715C1 14.2902 1 13.6601 1 12.4V4.6C1 3.33988 1 2.70982 1.24524 2.22852C1.46095 1.80516 1.80516 1.46095 2.22852 1.24524C2.70982 1 3.33988 1 4.6 1H9.4C10.6601 1 11.2902 1 11.7715 1.24524C12.1948 1.46095 12.539 1.80516 12.7548 2.22852C13 2.70982 13 3.33988 13 4.6Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Datasheet refreshed this week
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="21" height="22" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14.9725 3.25C14.9725 4.49264 11.8447 5.5 7.98627 5.5C4.12786 5.5 1 4.49264 1 3.25M14.9725 3.25C14.9725 2.00736 11.8447 1 7.98627 1C4.12786 1 1 2.00736 1 3.25M14.9725 3.25V13.75C14.9725 14.995 11.8675 16 7.98627 16C4.10501 16 1 14.995 1 13.75V3.25M14.9725 8.5C14.9725 9.745 11.8675 10.75 7.98627 10.75C4.10501 10.75 1 9.745 1 8.5" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Images & specs from supplier
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="20" height="22" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.88864 13.75C9.88864 14.9926 8.84719 16 7.5625 16C6.27781 16 5.23636 14.9926 5.23636 13.75M8.95547 4.17892C9.29315 3.84148 9.50095 3.38171 9.50095 2.875C9.50095 1.83947 8.63308 1 7.5625 1C6.49192 1 5.62405 1.83947 5.62405 2.875C5.62405 3.38171 5.83185 3.84148 6.16953 4.17892M12.2148 7.9C12.2148 6.86566 11.7246 5.87368 10.8522 5.14228C9.97969 4.41089 8.79636 4 7.5625 4C6.32864 4 5.14531 4.41089 4.27284 5.14228C3.40037 5.87368 2.91022 6.86566 2.91022 7.9C2.91022 9.61135 2.47145 10.8629 1.92398 11.7585C1.30002 12.7792 0.988031 13.2896 1.00035 13.4115C1.01445 13.551 1.0404 13.595 1.15735 13.6777C1.25957 13.75 1.77313 13.75 2.80026 13.75H12.3247C13.3519 13.75 13.8654 13.75 13.9677 13.6777C14.0846 13.595 14.1106 13.551 14.1246 13.4115C14.137 13.2896 13.825 12.7792 13.201 11.7585C12.6535 10.8629 12.2148 9.61135 12.2148 7.9Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Lifecycle & change notifications
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="featured_manuf ver3"></div>
                    </div>

                    <div class="featured_desc featured_desc_alt ver3"></div>

                    <div class="featured_data_icons" id="featured_data_icons">
                        <span>Data available: </span>
                        <img class="datasheet ver3" src="/static/img/search_icons/datasheet_outline.png" data-toggle='tooltip' alt='Datasheet not available' data-original-title='Datasheet not available'>
                        <img class="symbol ver3" src="/static/img/search_icons/symbol_outline.png" data-toggle='tooltip' alt='Symbol not available' data-original-title='Symbol not available'>
                        <img class="footprint ver3" src="/static/img/search_icons/footprint_outline.png" data-toggle='tooltip' alt='Footprint not available' data-original-title='Footprint not available'>
                        <img class="3d ver3" src="/static/img/search_icons/3d_outline.png" data-toggle='tooltip' alt='3D not available' data-original-title='3D not available'>
                        <img class="sim ver3" src="/static/img/search_icons/sim_dotted_orange.png" data-toggle='tooltip' alt='Sim not available' data-original-title='Sim not available'>
                        <img class="sample sponsor-3d ver3" data-toggle="tooltip">
                    </div>
                    <div>
                        <div class="featured_buttons featured_buttons_alt ver3 float-left">
                            <a class="s-btn s-btn-small ghost outl-orange download top_line_view ver2 shadow_button shadow_button_alt" data-type='' href=''>Download Models</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div id="right_ad_view" class="span6 border-right ad_view">
            <div class="span4">
                <div class="manuf_img">
                    <img src="" alt="">
                </div>
                <div class="featured_img">
                    <a class='top_line_view' data-type='' href="">
                        <img src="" alt="">
                    </a>
                </div>
            </div>

            <div class="span8">
                <div class="sponsored_indicator_wrapper">
                    <div class="sponsored_indicator"><i class="fa fa-info" aria-hidden="true"></i> Sponsored</div>
                </div>
                <div class="featured_content">
                    <div class="featured_title_ver3">
                        <div class="distributor_title_container">
                            <a class="featured_title featured_title_alt top_line_view ver3" data-type='' href="#"></a>
                            <span id="snapverifiedrighttopline" class="snapverified" style="display: inline-block; color: #666460; font-weight: 400; line-height: inherit; font-size: 16px; cursor: pointer; transform: translateY(2px); margin-right: 5px" aria-describedby="tooltip">
                                <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_distributor)"/>
                                    <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                                    <defs>
                                    <radialGradient id="paint0_radial_304_117_distributor" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                                    <stop stop-color="#46B0ED"/>
                                    <stop offset="1" stop-color="#1A81BC"/>
                                    </radialGradient>
                                    </defs>
                                </svg>
                            </span>
                            <div id="snapverifiedtooltiprighttopline" class="snapverifiedtooltip" role="tooltip">
                                <div class="arrow" data-popper-arrow></div>
                                <div style="display: flex; flex-direction: column; font-color: #2C2C35; font-weight: 400; gap: 8px;">
                                    <span style="font-weight: 600; color: #304e70; display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="22" height="22" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_distributor)"/>
                                        <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                                        <defs>
                                        <radialGradient id="paint0_radial_304_117_distributor" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                                        <stop stop-color="#46B0ED"/>
                                        <stop offset="1" stop-color="#1A81BC"/>
                                        </radialGradient>
                                        </defs>
                                        </svg>
                                        This product is verified
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="22" height="22" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6.25 1V2.5M10.75 1V2.5M6.25 14.5V16M10.75 14.5V16M14.5 6.25H16M14.5 10H16M1 6.25H2.5M1 10H2.5M6.1 14.5H10.9C12.1601 14.5 12.7902 14.5 13.2715 14.2548C13.6948 14.039 14.039 13.6948 14.2548 13.2715C14.5 12.7902 14.5 12.1601 14.5 10.9V6.1C14.5 4.83988 14.5 4.20982 14.2548 3.72852C14.039 3.30516 13.6948 2.96095 13.2715 2.74524C12.7902 2.5 12.1601 2.5 10.9 2.5H6.1C4.83988 2.5 4.20982 2.5 3.72852 2.74524C3.30516 2.96095 2.96095 3.30516 2.74524 3.72852C2.5 4.20982 2.5 4.83988 2.5 6.1V10.9C2.5 12.1601 2.5 12.7902 2.74524 13.2715C2.96095 13.6948 3.30516 14.039 3.72852 14.2548C4.20982 14.5 4.83988 14.5 6.1 14.5ZM7.45 10.75H9.55C9.97004 10.75 10.1801 10.75 10.3405 10.6683C10.4816 10.5963 10.5963 10.4816 10.6683 10.3405C10.75 10.1801 10.75 9.97004 10.75 9.55V7.45C10.75 7.02996 10.75 6.81994 10.6683 6.65951C10.5963 6.51839 10.4816 6.40365 10.3405 6.33175C10.1801 6.25 9.97004 6.25 9.55 6.25H7.45C7.02996 6.25 6.81994 6.25 6.65951 6.33175C6.51839 6.40365 6.40365 6.51839 6.33175 6.65951C6.25 6.81994 6.25 7.02996 6.25 7.45V9.55C6.25 9.97004 6.25 10.1801 6.33175 10.3405C6.40365 10.4816 6.51839 10.5963 6.65951 10.6683C6.81994 10.75 7.02996 10.75 7.45 10.75Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        SnapMagic crafted CAD
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="19" height="22" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8.5 7.75H4M5.5 10.75H4M10 4.75H4M13 4.6V12.4C13 13.6601 13 14.2902 12.7548 14.7715C12.539 15.1948 12.1948 15.539 11.7715 15.7548C11.2902 16 10.6601 16 9.4 16H4.6C3.33988 16 2.70982 16 2.22852 15.7548C1.80516 15.539 1.46095 15.1948 1.24524 14.7715C1 14.2902 1 13.6601 1 12.4V4.6C1 3.33988 1 2.70982 1.24524 2.22852C1.46095 1.80516 1.80516 1.46095 2.22852 1.24524C2.70982 1 3.33988 1 4.6 1H9.4C10.6601 1 11.2902 1 11.7715 1.24524C12.1948 1.46095 12.539 1.80516 12.7548 2.22852C13 2.70982 13 3.33988 13 4.6Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Datasheet refreshed this week
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="21" height="22" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14.9725 3.25C14.9725 4.49264 11.8447 5.5 7.98627 5.5C4.12786 5.5 1 4.49264 1 3.25M14.9725 3.25C14.9725 2.00736 11.8447 1 7.98627 1C4.12786 1 1 2.00736 1 3.25M14.9725 3.25V13.75C14.9725 14.995 11.8675 16 7.98627 16C4.10501 16 1 14.995 1 13.75V3.25M14.9725 8.5C14.9725 9.745 11.8675 10.75 7.98627 10.75C4.10501 10.75 1 9.745 1 8.5" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Images & specs from supplier
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="20" height="22" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.88864 13.75C9.88864 14.9926 8.84719 16 7.5625 16C6.27781 16 5.23636 14.9926 5.23636 13.75M8.95547 4.17892C9.29315 3.84148 9.50095 3.38171 9.50095 2.875C9.50095 1.83947 8.63308 1 7.5625 1C6.49192 1 5.62405 1.83947 5.62405 2.875C5.62405 3.38171 5.83185 3.84148 6.16953 4.17892M12.2148 7.9C12.2148 6.86566 11.7246 5.87368 10.8522 5.14228C9.97969 4.41089 8.79636 4 7.5625 4C6.32864 4 5.14531 4.41089 4.27284 5.14228C3.40037 5.87368 2.91022 6.86566 2.91022 7.9C2.91022 9.61135 2.47145 10.8629 1.92398 11.7585C1.30002 12.7792 0.988031 13.2896 1.00035 13.4115C1.01445 13.551 1.0404 13.595 1.15735 13.6777C1.25957 13.75 1.77313 13.75 2.80026 13.75H12.3247C13.3519 13.75 13.8654 13.75 13.9677 13.6777C14.0846 13.595 14.1106 13.551 14.1246 13.4115C14.137 13.2896 13.825 12.7792 13.201 11.7585C12.6535 10.8629 12.2148 9.61135 12.2148 7.9Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Lifecycle & change notifications
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="featured_manuf ver3"></div>
                    </div>

                    <div class="featured_desc featured_desc_alt ver3"></div>

                    <div class="featured_data_icons" id="featured_data_icons">
                        <span>Data available: </span>
                        <img class="datasheet ver3" src="/static/img/search_icons/datasheet_outline.png" data-toggle='tooltip' alt='Datasheet not available' data-original-title='Datasheet not available'>
                        <img class="symbol ver3" src="/static/img/search_icons/symbol_outline.png" data-toggle='tooltip' alt='Symbol not available' data-original-title='Symbol not available'>
                        <img class="footprint ver3" src="/static/img/search_icons/footprint_outline.png" data-toggle='tooltip' alt='Footprint not available' data-original-title='Footprint not available'>
                        <img class="3d ver3" src="/static/img/search_icons/3d_outline.png" data-toggle='tooltip' alt='3D not available' data-original-title='3D not available'>
                        <img class="sim ver3" src="/static/img/search_icons/sim_dotted_orange.png" data-toggle='tooltip' alt='Sim not available' data-original-title='Sim not available'>
                        <img class="sample sponsor-3d ver3" data-toggle="tooltip">
                    </div>
                    <div>
                        <div class="featured_buttons featured_buttons_alt ver3 float-left">
                            <a class="s-btn s-btn-small ghost outl-orange download top_line_view ver2 shadow_button shadow_button_alt" data-type='' href=''>Download Models</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        
    </div>
</div>


<div class="featured_part te_ad ad_view" style="display: none">
    <div class="row-fluid">

        <div class="span2">
            <div class="manuf_img">
                <img src="" alt="">
            </div>
            <div class="featured_img">
                <a class='digikey_view' href="">
                    <img src="" alt="">
                </a>
            </div>
        </div>
        <div class="span5">
            <div class="featured_content">
                <div class="featured_title_container" >
                    <a class="featured_title featured_title_alt digikey_view" href="#"></a>
                    <span id="snapverifiedfeatured" class="snapverified" style="display: flex; flex-direction: row; align-items: center; gap: 5px; color: #666460; font-weight: 400; line-height: inherit; font-size: 16px; cursor: pointer; margin-top: 4px;" aria-describedby="tooltip">
                        <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_featured)"/>
                            <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                            <defs>
                            <radialGradient id="paint0_radial_304_117_featured" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                            <stop stop-color="#46B0ED"/>
                            <stop offset="1" stop-color="#1A81BC"/>
                            </radialGradient>
                            </defs>
                        </svg>
                    </span>
                    <div id="snapverifiedtooltipfeatured" class="snapverifiedtooltip" role="tooltip">
                        <div class="arrow" data-popper-arrow></div>
                        <div style="display: flex; flex-direction: column; font-color: #2C2C35; font-weight: 400; gap: 8px;">
                            <span style="font-weight: 600; color: #304e70; display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                <svg width="22" height="22" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_featuredtip)"/>
                                <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                                <defs>
                                <radialGradient id="paint0_radial_304_117_featuredtip" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                                <stop stop-color="#46B0ED"/>
                                <stop offset="1" stop-color="#1A81BC"/>
                                </radialGradient>
                                </defs>
                                </svg>
                                This product is verified
                            </span>
                            <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                <svg width="22" height="22" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.25 1V2.5M10.75 1V2.5M6.25 14.5V16M10.75 14.5V16M14.5 6.25H16M14.5 10H16M1 6.25H2.5M1 10H2.5M6.1 14.5H10.9C12.1601 14.5 12.7902 14.5 13.2715 14.2548C13.6948 14.039 14.039 13.6948 14.2548 13.2715C14.5 12.7902 14.5 12.1601 14.5 10.9V6.1C14.5 4.83988 14.5 4.20982 14.2548 3.72852C14.039 3.30516 13.6948 2.96095 13.2715 2.74524C12.7902 2.5 12.1601 2.5 10.9 2.5H6.1C4.83988 2.5 4.20982 2.5 3.72852 2.74524C3.30516 2.96095 2.96095 3.30516 2.74524 3.72852C2.5 4.20982 2.5 4.83988 2.5 6.1V10.9C2.5 12.1601 2.5 12.7902 2.74524 13.2715C2.96095 13.6948 3.30516 14.039 3.72852 14.2548C4.20982 14.5 4.83988 14.5 6.1 14.5ZM7.45 10.75H9.55C9.97004 10.75 10.1801 10.75 10.3405 10.6683C10.4816 10.5963 10.5963 10.4816 10.6683 10.3405C10.75 10.1801 10.75 9.97004 10.75 9.55V7.45C10.75 7.02996 10.75 6.81994 10.6683 6.65951C10.5963 6.51839 10.4816 6.40365 10.3405 6.33175C10.1801 6.25 9.97004 6.25 9.55 6.25H7.45C7.02996 6.25 6.81994 6.25 6.65951 6.33175C6.51839 6.40365 6.40365 6.51839 6.33175 6.65951C6.25 6.81994 6.25 7.02996 6.25 7.45V9.55C6.25 9.97004 6.25 10.1801 6.33175 10.3405C6.40365 10.4816 6.51839 10.5963 6.65951 10.6683C6.81994 10.75 7.02996 10.75 7.45 10.75Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                SnapMagic crafted CAD
                            </span>
                            <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                <svg width="19" height="22" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8.5 7.75H4M5.5 10.75H4M10 4.75H4M13 4.6V12.4C13 13.6601 13 14.2902 12.7548 14.7715C12.539 15.1948 12.1948 15.539 11.7715 15.7548C11.2902 16 10.6601 16 9.4 16H4.6C3.33988 16 2.70982 16 2.22852 15.7548C1.80516 15.539 1.46095 15.1948 1.24524 14.7715C1 14.2902 1 13.6601 1 12.4V4.6C1 3.33988 1 2.70982 1.24524 2.22852C1.46095 1.80516 1.80516 1.46095 2.22852 1.24524C2.70982 1 3.33988 1 4.6 1H9.4C10.6601 1 11.2902 1 11.7715 1.24524C12.1948 1.46095 12.539 1.80516 12.7548 2.22852C13 2.70982 13 3.33988 13 4.6Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                Datasheet refreshed this week
                            </span>
                            <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                <svg width="21" height="22" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M14.9725 3.25C14.9725 4.49264 11.8447 5.5 7.98627 5.5C4.12786 5.5 1 4.49264 1 3.25M14.9725 3.25C14.9725 2.00736 11.8447 1 7.98627 1C4.12786 1 1 2.00736 1 3.25M14.9725 3.25V13.75C14.9725 14.995 11.8675 16 7.98627 16C4.10501 16 1 14.995 1 13.75V3.25M14.9725 8.5C14.9725 9.745 11.8675 10.75 7.98627 10.75C4.10501 10.75 1 9.745 1 8.5" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                Images & specs from supplier
                            </span>
                            <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                <svg width="20" height="22" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9.88864 13.75C9.88864 14.9926 8.84719 16 7.5625 16C6.27781 16 5.23636 14.9926 5.23636 13.75M8.95547 4.17892C9.29315 3.84148 9.50095 3.38171 9.50095 2.875C9.50095 1.83947 8.63308 1 7.5625 1C6.49192 1 5.62405 1.83947 5.62405 2.875C5.62405 3.38171 5.83185 3.84148 6.16953 4.17892M12.2148 7.9C12.2148 6.86566 11.7246 5.87368 10.8522 5.14228C9.97969 4.41089 8.79636 4 7.5625 4C6.32864 4 5.14531 4.41089 4.27284 5.14228C3.40037 5.87368 2.91022 6.86566 2.91022 7.9C2.91022 9.61135 2.47145 10.8629 1.92398 11.7585C1.30002 12.7792 0.988031 13.2896 1.00035 13.4115C1.01445 13.551 1.0404 13.595 1.15735 13.6777C1.25957 13.75 1.77313 13.75 2.80026 13.75H12.3247C13.3519 13.75 13.8654 13.75 13.9677 13.6777C14.0846 13.595 14.1106 13.551 14.1246 13.4115C14.137 13.2896 13.825 12.7792 13.201 11.7585C12.6535 10.8629 12.2148 9.61135 12.2148 7.9Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                Lifecycle & change notifications
                            </span>
                        </div>
                    </div>
                </div>
                <div class="featured_manuf"></div>
                <div class="featured_desc featured_desc_alt"></div>
                <div class="featured_price featured_price_alt">
                    Starting from: <span class="price"></span> <span class="price_currency">USD</span><br>
                    Availability: <span class="quantity"></span>
                </div>
                <div class="featured_buttons">
                    <a class="s-btn s-btn-small ghost outl-orange download digikey_view ad_view_right" href=''>Download Models</a>
                    <a class="s-btn s-btn-small ghost outl-blue buy digikey_buy ad_buy" href='' target="_blank">Buy Now From DigiKey</a>
                </div>
            </div>
        </div>
        <div class="span5">
            <div class="sponsored_indicator_wrapper">
                <div class="sponsored_indicator"><i class="fa fa-info" aria-hidden="true"></i> Sponsored</div>
            </div>

            <div class="featured_buttons featured_buttons_alt">
                <div class="featured_data_icons" id="featured_data_icons">
                    <span>Data available: </span>
                    <img class="datasheet" src="/static/img/search_icons/datasheet_outline.png" data-toggle='tooltip' alt='Datasheet not available' data-original-title='Datasheet not available'>
                    <img class="symbol" src="/static/img/search_icons/symbol_outline.png" data-toggle='tooltip' alt='Symbol not available' data-original-title='Symbol not available'>
                    <img class="footprint" src="/static/img/search_icons/footprint_outline.png" data-toggle='tooltip' alt='Footprint not available' data-original-title='Footprint not available'>
                    <img class="3d" src="/static/img/search_icons/3d_outline.png" data-toggle='tooltip' alt='3D not available' data-original-title='3D not available'>
                    <img class="sim" src="/static/img/search_icons/sim_dotted_orange.png" data-toggle='tooltip' alt='Sim not available' data-original-title='Sim not available'>
                    <img class="sample sponsor-3d" data-toggle="tooltip">
                </div>
                <div class="standard_used_cont" id='free-sample' style='display:none'>
                    <div class="standard_used_icon">
                    <i class="fa fa-star" aria-hidden="true"></i>
                    </div>
                    <span class="standard_used_text">Free Samples Available</span>
                </div>
            </div>
            <div id="stock_available_btn" class="featured_buttons" style='display:none'>
                <button class="stock_available_button" href=''>
                    <i class="fa fa-microchip ad_carrier_icon" aria-hidden="true"></i>
                    <p class="ad_carrier_text">High stock available</p>
                </button>
            </div>
            <div id="free_sample_btn" class="featured_buttons" style='display:none'>
                <button class="free_sample_button" href=''>
                    <i class="fa fa-microchip ad_carrier_icon" aria-hidden="true"></i>
                    <p class="ad_carrier_text">Free Samples available!</p>
                </button>
            </div>
        </div>
    </div>
</div>

<style>

    .featured_buttons .s-btn-small.digikey_view.ver2.mouser_color{
        background: #DC730A;
        border-color: #DC730A;
    }
    .featured_buttons .s-btn-small.digikey_buy.ver2.mouser_color, .sponsored_indicator.mouser_color{
        background: #004A85;
        border-color: #004A85;
    }
    .float-right {
        float: right;
    }
    .float-left {
        float: left;
    }
    

</style>


<div class="featured_part dk_ad ad_buy" style="display: none !important">
    <div class="row-fluid">

        <div class="span6 border-right ad_view">
            <div class="span4">
                <div class="manuf_img">
                    <img src="" alt="">
                </div>
                <div class="featured_img">
                    <a class='digikey_view' data-type='' href="">
                        <img src="" alt="">
                    </a>
                </div>
            </div>

            <div class="span8">
                <div class="featured_content">
                    <div class="featured_title_ver3">
                        <div class="distributor_title_container">
                            <a class="featured_title featured_title_alt digikey_view ver3" data-type='' href="#"></a>
                            <span id="snapverifieddigikey" class="snapverified" style="display: inline-block; color: #666460; font-weight: 400; line-height: inherit; font-size: 16px; cursor: pointer; transform: translateY(2px); margin-right: 5px" aria-describedby="tooltip">
                                <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_distributor)"/>
                                    <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                                    <defs>
                                    <radialGradient id="paint0_radial_304_117_distributor" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                                    <stop stop-color="#46B0ED"/>
                                    <stop offset="1" stop-color="#1A81BC"/>
                                    </radialGradient>
                                    </defs>
                                </svg>
                            </span>
                            <div id="snapverifiedtooltipdigikey" class="snapverifiedtooltip" role="tooltip">
                                <div class="arrow" data-popper-arrow></div>
                                <div style="display: flex; flex-direction: column; font-color: #2C2C35; font-weight: 400; gap: 8px;">
                                    <span style="font-weight: 600; color: #304e70; display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="22" height="22" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_distributor)"/>
                                        <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                                        <defs>
                                        <radialGradient id="paint0_radial_304_117_distributor" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                                        <stop stop-color="#46B0ED"/>
                                        <stop offset="1" stop-color="#1A81BC"/>
                                        </radialGradient>
                                        </defs>
                                        </svg>
                                        This product is verified
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="22" height="22" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6.25 1V2.5M10.75 1V2.5M6.25 14.5V16M10.75 14.5V16M14.5 6.25H16M14.5 10H16M1 6.25H2.5M1 10H2.5M6.1 14.5H10.9C12.1601 14.5 12.7902 14.5 13.2715 14.2548C13.6948 14.039 14.039 13.6948 14.2548 13.2715C14.5 12.7902 14.5 12.1601 14.5 10.9V6.1C14.5 4.83988 14.5 4.20982 14.2548 3.72852C14.039 3.30516 13.6948 2.96095 13.2715 2.74524C12.7902 2.5 12.1601 2.5 10.9 2.5H6.1C4.83988 2.5 4.20982 2.5 3.72852 2.74524C3.30516 2.96095 2.96095 3.30516 2.74524 3.72852C2.5 4.20982 2.5 4.83988 2.5 6.1V10.9C2.5 12.1601 2.5 12.7902 2.74524 13.2715C2.96095 13.6948 3.30516 14.039 3.72852 14.2548C4.20982 14.5 4.83988 14.5 6.1 14.5ZM7.45 10.75H9.55C9.97004 10.75 10.1801 10.75 10.3405 10.6683C10.4816 10.5963 10.5963 10.4816 10.6683 10.3405C10.75 10.1801 10.75 9.97004 10.75 9.55V7.45C10.75 7.02996 10.75 6.81994 10.6683 6.65951C10.5963 6.51839 10.4816 6.40365 10.3405 6.33175C10.1801 6.25 9.97004 6.25 9.55 6.25H7.45C7.02996 6.25 6.81994 6.25 6.65951 6.33175C6.51839 6.40365 6.40365 6.51839 6.33175 6.65951C6.25 6.81994 6.25 7.02996 6.25 7.45V9.55C6.25 9.97004 6.25 10.1801 6.33175 10.3405C6.40365 10.4816 6.51839 10.5963 6.65951 10.6683C6.81994 10.75 7.02996 10.75 7.45 10.75Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        SnapMagic crafted CAD
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="19" height="22" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8.5 7.75H4M5.5 10.75H4M10 4.75H4M13 4.6V12.4C13 13.6601 13 14.2902 12.7548 14.7715C12.539 15.1948 12.1948 15.539 11.7715 15.7548C11.2902 16 10.6601 16 9.4 16H4.6C3.33988 16 2.70982 16 2.22852 15.7548C1.80516 15.539 1.46095 15.1948 1.24524 14.7715C1 14.2902 1 13.6601 1 12.4V4.6C1 3.33988 1 2.70982 1.24524 2.22852C1.46095 1.80516 1.80516 1.46095 2.22852 1.24524C2.70982 1 3.33988 1 4.6 1H9.4C10.6601 1 11.2902 1 11.7715 1.24524C12.1948 1.46095 12.539 1.80516 12.7548 2.22852C13 2.70982 13 3.33988 13 4.6Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Datasheet refreshed this week
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="21" height="22" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14.9725 3.25C14.9725 4.49264 11.8447 5.5 7.98627 5.5C4.12786 5.5 1 4.49264 1 3.25M14.9725 3.25C14.9725 2.00736 11.8447 1 7.98627 1C4.12786 1 1 2.00736 1 3.25M14.9725 3.25V13.75C14.9725 14.995 11.8675 16 7.98627 16C4.10501 16 1 14.995 1 13.75V3.25M14.9725 8.5C14.9725 9.745 11.8675 10.75 7.98627 10.75C4.10501 10.75 1 9.745 1 8.5" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Images & specs from supplier
                                    </span>
                                    <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                        <svg width="20" height="22" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.88864 13.75C9.88864 14.9926 8.84719 16 7.5625 16C6.27781 16 5.23636 14.9926 5.23636 13.75M8.95547 4.17892C9.29315 3.84148 9.50095 3.38171 9.50095 2.875C9.50095 1.83947 8.63308 1 7.5625 1C6.49192 1 5.62405 1.83947 5.62405 2.875C5.62405 3.38171 5.83185 3.84148 6.16953 4.17892M12.2148 7.9C12.2148 6.86566 11.7246 5.87368 10.8522 5.14228C9.97969 4.41089 8.79636 4 7.5625 4C6.32864 4 5.14531 4.41089 4.27284 5.14228C3.40037 5.87368 2.91022 6.86566 2.91022 7.9C2.91022 9.61135 2.47145 10.8629 1.92398 11.7585C1.30002 12.7792 0.988031 13.2896 1.00035 13.4115C1.01445 13.551 1.0404 13.595 1.15735 13.6777C1.25957 13.75 1.77313 13.75 2.80026 13.75H12.3247C13.3519 13.75 13.8654 13.75 13.9677 13.6777C14.0846 13.595 14.1106 13.551 14.1246 13.4115C14.137 13.2896 13.825 12.7792 13.201 11.7585C12.6535 10.8629 12.2148 9.61135 12.2148 7.9Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Lifecycle & change notifications
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="featured_manuf ver3"></div>
                    </div>

                    <div class="featured_desc featured_desc_alt ver3"></div>

                    <div class="featured_data_icons" id="featured_data_icons">
                        <span>Data available: </span>
                        <img class="datasheet ver3" src="/static/img/search_icons/datasheet_outline.png" data-toggle='tooltip' alt='Datasheet not available' data-original-title='Datasheet not available'>
                        <img class="symbol ver3" src="/static/img/search_icons/symbol_outline.png" data-toggle='tooltip' alt='Symbol not available' data-original-title='Symbol not available'>
                        <img class="footprint ver3" src="/static/img/search_icons/footprint_outline.png" data-toggle='tooltip' alt='Footprint not available' data-original-title='Footprint not available'>
                        <img class="3d ver3" src="/static/img/search_icons/3d_outline.png" data-toggle='tooltip' alt='3D not available' data-original-title='3D not available'>
                        <img class="sim ver3" src="/static/img/search_icons/sim_dotted_orange.png" data-toggle='tooltip' alt='Sim not available' data-original-title='Sim not available'>
                        <img class="sample sponsor-3d ver3" data-toggle="tooltip">
                    </div>
                    <div>
                        <div class="featured_buttons featured_buttons_alt ver3 float-left">
                            <a class="s-btn s-btn-small ghost outl-orange download digikey_view ver2 shadow_button shadow_button_alt" data-type='' href=''>Download Models</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="span6">
            <div class="sponsored_indicator_wrapper">
                <div class="sponsored_indicator"><i class="fa fa-info" aria-hidden="true"></i> Sponsored</div>
            </div>

            <div class="featured_content">

                <div class="span4">
                    <img src="" class="ver3_img" alt="">
                </div>
                <div class="span8">
                    <div class="featured_title_ver3 buy_title"></div>

                    <div class="featured_price featured_price_alt">

                        Starting from: $<span class="price"></span> <span class="price_currency">USD</span><br>
                        Quantity <span class="quantity_text">available</span>: <span class="quantity"></span>
                    </div>

                    <div class="featured_buttons featured_buttons_alt ver3 float-left" >
                        <a class="s-btn s-btn-small ghost outl-blue buy digikey_buy ver2 shadow_button shadow_button_alt" href='' data-type=''></a>
                    </div>


                </div>

            </div>
        </div>

    </div>
</div>


<style>

    .featured_buttons .s-btn-small.digikey_view.ver2.mouser_color{
        background: #DC730A;
        border-color: #DC730A;
    }
    .featured_buttons .s-btn-small.digikey_buy.ver2.mouser_color, .sponsored_indicator.mouser_color{
        background: #004A85;
        border-color: #004A85;
    }

    .conn_title_sect{
        width:270px;
        float:left;
    }

    .conn_title{
        margin: 20px;
        font-weight: 400;
        color: #555;
    }
    .conn_title span{
        color: #fd772f;
    }

    .conn_box{
        width: 180px;
        margin-top: 2px;
        float: left;
        margin-right: 30px;
        box-shadow: 0 0 1px #ddd;
        border-radius: 5px;

    }
    .conn_box .img_div{
        width: 180px;
        height: 60px;
        text-align: center;
        background: #fff;
        border-bottom: none;
        border-radius: 5px 5px 0px 0px;
    }

    .conn_box .img_div img{
        height:60px;
    }
    .conn_box .con_box_label{
        width: 180px;
        background: #3a5b83;
        color: #ffffff;
        text-align: center;
        padding: 7px 0px;
        border-radius: 0px 0px 5px 5px;
    }

    .connector_ad{
        margin-bottom: 20px;
        background: #f7f7f7;
        border:1px solid #dedede;
    }


</style>


<div class="connector_ad" style="display:none;">
    <div class="row-fluid">

        <div class="conn_title_sect">
            <h2 class="conn_title">Popular <span>Connectors</span><br/> on SnapMagic Search</h2>

        </div>

        <div class="span8">

            <div class="featured_content">

                <a href="/search/?q=usb+connector&connector_ref=rec_banner" class="conn_link" data-type="usb">
                <div class="conn_box">
                    <div class="img_div">
                        <img src="https://snapeda.s3.amazonaws.com/partpics/Samtec_USB-B-S-F-W-VT-R-.HZ_69488.png" alt="">
                    </div>
                    <div class="con_box_label">USB Connectors</div>
                </div>
                </a>

                <a href="/search/?q=sma+connector&connector_ref=rec_banner" class="conn_link" data-type="sma">
                <div class="conn_box">
                    <div class="img_div">
                        <img src="https://snapeda.s3.amazonaws.com/partpics/TE%20Connectivity_221789-1_4377.jpg" alt="" style="height: 50px;margin-top: 5px;">
                    </div>
                    <div class="con_box_label">SMA Connectors</div>
                </div>
                </a>

                <a href="/search/?q=pin+header&connector_ref=rec_banner" class="conn_link" data-type="header">
                <div class="conn_box">
                    <div class="img_div">
                        <img src="https://snapeda.s3.amazonaws.com/partpics/Samtec_MMT-10201FSHA_66906.png" alt="">
                    </div>
                    <div class="con_box_label">Pin Headers</div>
                </div>
                </a>

            </div>
        </div>

    </div>
</div>

<style>
    .inline_title{
        display: block;
        font-weight: 600;
        font-size: 16px;
        line-height: 25px;
        color: #0088cc;
    }
    .inline_manuf{
        font-size: 12px;
        letter-spacing: 0.5px;
        margin-bottom: 10px;
    }
    .inline_price{
        line-height: 30px;
        font-weight: 600;
        font-size: 12px;
    }
    .inline_buttons{
        margin-bottom: 10px;
    }
    .inline_data_icons{
        display: block;
        margin-bottom: 10px;
        padding-right: 5px;
    }

    .inline_data_icons span{
        margin-right: 5px;
    }

    .inline_data_icons img{
        height:23px;
        margin-right: 3px;
        margin-left: 3px;
    }

    .inline_data_icons img.datasheet, .inline_data_icons img.footprint{
        height:23px;
        margin-right: 6px;
    }
    
    #designInlineDistributor .inline_data_icons img{
        height:18px;
        margin-right: 3px;
        margin-left: 3px;
    }

    #designInlineDistributor .inline_data_icons img.datasheet, .inline_data_icons img.footprint{
        height:18px;
        margin-right: 6px;
    }

    .inline_data_icons img.sponsor-3d{
        margin-right: 10px;
    }

    .inline_buttons .s-btn-small{
        margin-top: 10px;
        margin-right: 5px;
    }

    #designInlineDistributor .inline_content {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .inline_content .ver3_img {
        margin-top: 0px;
    }

    #designInlineDistributor .inline_buttons .orange{
        background: #DC730A;
        border-color: #DC730A; 
        background: #ff761a; 
    }
    #designInlineDistributor .inline_buttons .mouser_color, .sponsored_indicator.mouser_color{
        background: #004A85;
        border-color: #004A85; 
        background: #004A85; 
    }

    #designInlineDistributor .inline_buttons .s-btn-small {
        padding: 8px 20px;
        border-radius: 30px;
        margin-right: 10px;
        color: #fff;
        margin-top: 5px;
    }

    .inline_ad_part {
        display: none;
    }

    .inline_ad_template_container{
        margin-bottom: 20px;
        font-size: 12px;
    }
    .inline_ad_template_container .row-fluid{
        display: flex;
    }
    .inline_ad_template_container .row-fluid > .span6{
        margin-left: 0;
        width: 50%;
    }
    .inline_ad_template_container .row-fluid > [class*="span"] {
        border: 1px solid #eee;
        min-height: 175px;
        position: relative;
    }
    .inline_ad_template_container.in-line_ad .row-fluid > [class*="span"] {
        min-height: 190px;
    }
    .inline_ad_template_container .row-fluid > [class*="span"]:last-child{
        border-left: none;
    }
    .inline_ad_template_container.in-line_ad{
        padding: 0;
    }
    .inline_ad_template_container.in-line_ad .row-fluid > [class*="span"] {
        border: none !important;
        border-right: 1px solid #eee !important;
    }
    .inline_ad_template_col{
        cursor: pointer;
    }
    .inline_ad_template_col > .span8{
        padding: 10px;
    }
    .inline_ad_template_col .text-center{
        text-align: center;
    }
    .inline_ad_template_col .ad_part_link{
        display: block;
    }
    .inline_ad_template_col .ad_part_image{
        height: 130px;
    }
    .inline_ad_template_col .ad_manuf_image{
        max-width: 90px;
        position: absolute;
        bottom: 10px;
        -webkit-transform: translateX(-50%);
        -moz-transform:  translateX(-50%);
        -o-transform:  translateX(-50%);
        transform: translateX(-50%);
        -ms-transform: translateX(100%);
    }
    .inline_ad_template_col .center-span-content{
        display: flex;
        height: 100%;
        align-items: center;
        justify-content: center;
    }
    .inline_ad_template_col .center-span-content .ad_part_link{
        max-width: 90%;
    }
    .inline_ad_template_col .ad_manuf_image_top {
        position: initial;
        margin-top: 10px;
        margin-left: 10px;
    }
    .inline_ad_template_col .ad_title_container{
        margin-top: 10px;
        margin-bottom: 5px;
    }
    .inline_ad_template_col .ad_title_container .ad_title_link{
        font-weight: 600;
        font-size: 16px;
        color: #08c;
        margin-right: 5px;
    }
    .inline_ad_template_col .ad_title_container .ad_title_description{
        font-size: 12px;
        letter-spacing: 0.5px;
        font-weight: 600;
    }
    .inline_ad_template_col .ad_description_container{
        margin-bottom: 10px;
        white-space: pre-wrap;
        max-height: 40px;
        overflow: hidden;
    }
    .inline_ad_template_col .ad_data_container {
        margin-bottom: 15px;
        font-size: 13px;
    }
    .inline_ad_template_col .ad_data_container .ad_icon {
        height: 20px;
        margin: 0 3px;
    }
    .inline_ad_template_col .ad_sponsored_icon {
        padding: 5px 10px;
        background: #3a5c84;
        color: #fff;
        font-size: 12px;
        float: right;
        margin-right: -10px;
        margin-top: -10px;
    }
    .inline_ad_template_col .ad_link_button{
        padding: 8px 20px;
        border-radius: 30px;
        margin-right: 10px;
        background: #ff761a;
        color: #fff;
        font-weight: 600;
        font-size: 12px;
        position: absolute !important;
        bottom: 10px;
    }
    .deep-koamaru{
        color : #3a5c84 !important;
    }
    .bg-deep-koamaru{
        background : #3a5c84 !important;
    }
    .inline_ad_template_col.custom_featured .ad_title_container{
        margin-bottom: 10px;
    }
    .inline_ad_template_col.custom_featured .ad_description_container{
        line-height: 22px;
    }
    .inline_ad_template_col.custom_featured .ad_part_image {
        width: 100%;
        height: auto;
    }
    .inline_ad_template_col.custom_featured .ad_link_button {
        height: 20px;
    }
</style>

<div class="inline_ad_part" id='designInlineManufacturer' >
    <div class="mobile_sponsor"><i class="fa fa-info"></i> Sponsored </div>
    <div class="row-fluid">

        <div class="span2">
            <div class="manuf_img">
                <img src="" alt="">
            </div>
            <div class="featured_img">
                <a class='digikey_view' href="">
                    <img src="" alt="">
                </a>
            </div>
        </div>
        <div class="span5">
            <div class="inline_content">
                <div class="featured_title_ver3">
                    <div class="inline_title_container">
                        <a class="inline_title featured_title_alt digikey_view ver3" href="#"></a>
                        <span id="snapverifiedinline" class="snapverified" style="display: flex; flex-direction: row; align-items: center; gap: 5px; color: #666460; font-weight: 400; line-height: inherit; font-size: 16px; cursor: pointer; margin-top: 4px;" aria-describedby="tooltip">
                            <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_inline)"/>
                                <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                                <defs>
                                <radialGradient id="paint0_radial_304_117_inline" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                                <stop stop-color="#46B0ED"/>
                                <stop offset="1" stop-color="#1A81BC"/>
                                </radialGradient>
                                </defs>
                            </svg>
                        </span>
                        <div id="snapverifiedtooltipinline" class="snapverifiedtooltip" role="tooltip">
                            <div class="arrow" data-popper-arrow></div>
                            <div style="display: flex; flex-direction: column; font-color: #2C2C35; font-weight: 400; gap: 8px;">
                                <span style="font-weight: 600; color: #304e70; display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                    <svg width="22" height="22" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117_inlinetip)"/>
                                    <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
                                    <defs>
                                    <radialGradient id="paint0_radial_304_117_inlinetip" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
                                    <stop stop-color="#46B0ED"/>
                                    <stop offset="1" stop-color="#1A81BC"/>
                                    </radialGradient>
                                    </defs>
                                    </svg>
                                    This product is verified
                                </span>
                                <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                    <svg width="22" height="22" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6.25 1V2.5M10.75 1V2.5M6.25 14.5V16M10.75 14.5V16M14.5 6.25H16M14.5 10H16M1 6.25H2.5M1 10H2.5M6.1 14.5H10.9C12.1601 14.5 12.7902 14.5 13.2715 14.2548C13.6948 14.039 14.039 13.6948 14.2548 13.2715C14.5 12.7902 14.5 12.1601 14.5 10.9V6.1C14.5 4.83988 14.5 4.20982 14.2548 3.72852C14.039 3.30516 13.6948 2.96095 13.2715 2.74524C12.7902 2.5 12.1601 2.5 10.9 2.5H6.1C4.83988 2.5 4.20982 2.5 3.72852 2.74524C3.30516 2.96095 2.96095 3.30516 2.74524 3.72852C2.5 4.20982 2.5 4.83988 2.5 6.1V10.9C2.5 12.1601 2.5 12.7902 2.74524 13.2715C2.96095 13.6948 3.30516 14.039 3.72852 14.2548C4.20982 14.5 4.83988 14.5 6.1 14.5ZM7.45 10.75H9.55C9.97004 10.75 10.1801 10.75 10.3405 10.6683C10.4816 10.5963 10.5963 10.4816 10.6683 10.3405C10.75 10.1801 10.75 9.97004 10.75 9.55V7.45C10.75 7.02996 10.75 6.81994 10.6683 6.65951C10.5963 6.51839 10.4816 6.40365 10.3405 6.33175C10.1801 6.25 9.97004 6.25 9.55 6.25H7.45C7.02996 6.25 6.81994 6.25 6.65951 6.33175C6.51839 6.40365 6.40365 6.51839 6.33175 6.65951C6.25 6.81994 6.25 7.02996 6.25 7.45V9.55C6.25 9.97004 6.25 10.1801 6.33175 10.3405C6.40365 10.4816 6.51839 10.5963 6.65951 10.6683C6.81994 10.75 7.02996 10.75 7.45 10.75Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    SnapMagic crafted CAD
                                </span>
                                <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                    <svg width="19" height="22" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M8.5 7.75H4M5.5 10.75H4M10 4.75H4M13 4.6V12.4C13 13.6601 13 14.2902 12.7548 14.7715C12.539 15.1948 12.1948 15.539 11.7715 15.7548C11.2902 16 10.6601 16 9.4 16H4.6C3.33988 16 2.70982 16 2.22852 15.7548C1.80516 15.539 1.46095 15.1948 1.24524 14.7715C1 14.2902 1 13.6601 1 12.4V4.6C1 3.33988 1 2.70982 1.24524 2.22852C1.46095 1.80516 1.80516 1.46095 2.22852 1.24524C2.70982 1 3.33988 1 4.6 1H9.4C10.6601 1 11.2902 1 11.7715 1.24524C12.1948 1.46095 12.539 1.80516 12.7548 2.22852C13 2.70982 13 3.33988 13 4.6Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    Datasheet refreshed this week
                                </span>
                                <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                    <svg width="21" height="22" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14.9725 3.25C14.9725 4.49264 11.8447 5.5 7.98627 5.5C4.12786 5.5 1 4.49264 1 3.25M14.9725 3.25C14.9725 2.00736 11.8447 1 7.98627 1C4.12786 1 1 2.00736 1 3.25M14.9725 3.25V13.75C14.9725 14.995 11.8675 16 7.98627 16C4.10501 16 1 14.995 1 13.75V3.25M14.9725 8.5C14.9725 9.745 11.8675 10.75 7.98627 10.75C4.10501 10.75 1 9.745 1 8.5" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    Images & specs from supplier
                                </span>
                                <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
                                    <svg width="20" height="22" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9.88864 13.75C9.88864 14.9926 8.84719 16 7.5625 16C6.27781 16 5.23636 14.9926 5.23636 13.75M8.95547 4.17892C9.29315 3.84148 9.50095 3.38171 9.50095 2.875C9.50095 1.83947 8.63308 1 7.5625 1C6.49192 1 5.62405 1.83947 5.62405 2.875C5.62405 3.38171 5.83185 3.84148 6.16953 4.17892M12.2148 7.9C12.2148 6.86566 11.7246 5.87368 10.8522 5.14228C9.97969 4.41089 8.79636 4 7.5625 4C6.32864 4 5.14531 4.41089 4.27284 5.14228C3.40037 5.87368 2.91022 6.86566 2.91022 7.9C2.91022 9.61135 2.47145 10.8629 1.92398 11.7585C1.30002 12.7792 0.988031 13.2896 1.00035 13.4115C1.01445 13.551 1.0404 13.595 1.15735 13.6777C1.25957 13.75 1.77313 13.75 2.80026 13.75H12.3247C13.3519 13.75 13.8654 13.75 13.9677 13.6777C14.0846 13.595 14.1106 13.551 14.1246 13.4115C14.137 13.2896 13.825 12.7792 13.201 11.7585C12.6535 10.8629 12.2148 9.61135 12.2148 7.9Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    Lifecycle & change notifications
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="inline_manuf ver3"></div>
                </div>

                <div class="inline_desc featured_desc_alt"></div>

                <div class="inline_price featured_price_alt">
                    Starting from: <span class="price"></span> <span class="price_currency">USD</span><br>
                    <span class="avail_sect">Availability: <span class="quantity"></span></span>

                </div>

                <div class="inline_buttons">
                    <a class="s-btn s-btn-small ghost outl-orange download digikey_view ver2" href=''>Download Models</a>

                    <a class="s-btn s-btn-small ghost outl-blue buy digikey_buy ad_buy" href='' target="_blank">Buy Now From DigiKey</a>
                </div>
            </div>
        </div>
        <div class="span5">
            <div class="sponsored_indicator_wrapper">
                <div class="sponsored_indicator"><i class="fa fa-info" aria-hidden="true"></i> Sponsored</div>
            </div>

            <div class="inline_buttons featured_buttons_alt">
                <div class="inline_data_icons" id="inline_data_icons">
                    <span>Data available: </span>
                    <img class="datasheet" src="/static/img/search_icons/datasheet_outline.png" data-toggle='tooltip' alt='Datasheet not available' data-original-title='Datasheet not available'>
                    <img class="symbol" src="/static/img/search_icons/symbol_outline.png" data-toggle='tooltip' alt='Symbol not available' data-original-title='Symbol not available'>
                    <img class="footprint" src="/static/img/search_icons/footprint_outline.png" data-toggle='tooltip' alt='Footprint not available' data-original-title='Footprint not available'>
                    <img class="3d" src="/static/img/search_icons/3d_outline.png" data-toggle='tooltip' alt='3D not available' data-original-title='3D not available'>
                    <img class="sim" src="/static/img/search_icons/sim_dotted_orange.png" data-toggle='tooltip' alt='Sim not available' data-original-title='Sim not available'>
                    <img class="sample sponsor-3d" data-toggle="tooltip">
                </div>
                <div class="standard_used_cont" id='free-sample' style='display:none'>
                    <div class="standard_used_icon">
                    <i class="fa fa-star" aria-hidden="true"></i>
                    </div>
                    <span class="standard_used_text">Free Samples Available</span>
                </div>
            </div>
        </div>

    </div>
</div>

<div class="inline_ad_part" id='designInlineDistributor' >
    <div class="mobile_sponsor"><i class="fa fa-info"></i> Sponsored </div>
    <div class="row-fluid">
        <!-------------------------------------------------------------------------------------------------->
        <div class="span6 border-right ad_view">
            <div class="span4">
                <div class="manuf_img">
                    <img src="" alt="">
                </div>
                <div class="featured_img">
                    <a class='digikey_view' data-type='' href="">
                        <img src="" alt="">
                    </a>
                </div>
            </div>

            <div class="span8">
                <div class="inline_content" style='display: block'>
                    <div class="featured_title_ver3">
                        <a class="inline_title featured_title_alt digikey_view ver3" data-type='' href="#"></a> <div class="inline_manuf ver3"></div>
                    </div>

                    <div class="inline_desc featured_desc_alt ver3"></div>

                    <div class="inline_data_icons" id="inline_data_icons">
                        <span>Data available: </span>
                        <img class="datasheet ver3" src="/static/img/search_icons/datasheet_outline.png" data-toggle='tooltip' alt='Datasheet not available' data-original-title='Datasheet not available'>
                        <img class="symbol ver3" src="/static/img/search_icons/symbol_outline.png" data-toggle='tooltip' alt='Symbol not available' data-original-title='Symbol not available'>
                        <img class="footprint ver3" src="/static/img/search_icons/footprint_outline.png" data-toggle='tooltip' alt='Footprint not available' data-original-title='Footprint not available'>
                        <img class="3d ver3" src="/static/img/search_icons/3d_outline.png" data-toggle='tooltip' alt='3D not available' data-original-title='3D not available'>
                        <img class="sim ver3" src="/static/img/search_icons/sim_dotted_orange.png" data-toggle='tooltip' alt='Sim not available' data-original-title='Sim not available'>
                        <img class="sample sponsor-3d ver3" data-toggle="tooltip">
                    </div>
                    <div>
                        <div class="inline_buttons featured_buttons_alt ver3 float-left">
                            <a class="s-btn s-btn-small orange ghost outl-orange download digikey_view ver2 shadow_button shadow_button_alt" data-type='' href=''>Download Models</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-------------------------------------------------------------------------------------------------->
        <div class="span6">
            <div class="sponsored_indicator_wrapper">
                <div class="sponsored_indicator"><i class="fa fa-info" aria-hidden="true"></i> Sponsored</div>
            </div>

            <div class="inline_content">

                <div class="span4">
                    <img src="" class="ver3_img" alt="">
                </div>
                <div class="span8">
                    <div class="featured_title_ver3 buy_title"></div>

                    <div class="inline_price featured_price_alt">

                        Starting from: $<span class="price"></span> <span class="price_currency">USD</span><br>
                        Quantity <span class="quantity_text">available</span>: <span class="quantity"></span>
                    </div>

                    <div class="inline_buttons featured_buttons_alt ver3 float-left" >
                        <a class="s-btn s-btn-small ghost outl-blue buy digikey_buy mouser_color ver2 shadow_button shadow_button_alt" href='' data-type=''></a>
                    </div>


                </div>

            </div>
        </div>
        
    </div>
</div>
 
 <div class="span6 hidden inline_ad_template_col" id="inline_ad_template_col">
    <div class="span4 text-center">
        <img src="" alt="" class="inline_ad_template_image ad_manuf_image ad_manuf_image_top">
        <a class='ad_part_link' data-type='' href="javascript: void(0);">
            <img src="" alt="" class="inline_ad_template_image ad_part_image">
        </a>
        <img src="" alt="" class="inline_ad_template_image ad_manuf_image ad_manuf_image_bottom">
    </div>
    <div class="span8">
        <div class="ad_sponsored_icon">
            <i class="fa fa-info" aria-hidden="true"></i> Sponsored
        </div>
        <div class="ad_title_container">
            <a class="ad_title_link" data-type='' href="javascript: void(0);"></a><span class="ad_title_description"></span>
        </div>
        <div class="ad_description_container"></div>
        <div class="ad_data_container">
            <span>Data available: </span>
            <img class="ad_icon datasheet_icon" src="/static/img/search_icons/datasheet_outline.png" 
                data-toggle='tooltip' 
                data-key="Datasheet"
                data-no="datasheet_outline.png"
                data-yes="datasheet_orange.png">
            <img class="ad_icon symbol_icon" src="/static/img/search_icons/symbol_outline.png" 
                data-toggle='tooltip' 
                data-key="Symbol"
                data-no="symbol_outline.png"
                data-yes="symbol_orange.png">
            <img class="ad_icon footprint_icon" src="/static/img/search_icons/footprint_outline.png" 
                data-toggle='tooltip' 
                data-key="Footprint"
                data-no="footprint_outline.png"
                data-yes="footprint_orange.png">
            <img class="ad_icon 3d_icon" src="/static/img/search_icons/3d_outline.png" 
                data-toggle='tooltip' 
                data-key="3D" 
                data-no="3d_outline.png"
                data-yes="3d_model_orange.png">
            <img class="ad_icon sim_icon" src="/static/img/search_icons/sim_dotted_orange.png" 
                data-toggle='tooltip' 
                data-key="Sim" 
                data-no="sim_dotted_orange.png"
                data-yes="sim_orange.png">
        </div>
        <a class="s-btn s-btn-small orange ad_link_button" data-type='' href='javascript: void(0);'>Download Models</a>
    </div>
</div>
<div id='inline_ad_template' class="inline_ad_template_container">
    <div class="row-fluid">
        
    </div>
</div>

<script src="/static/js/countrystatecity.js"></script>
<script src="/static/js/promise.polyfill.min.js"></script>
<script src="/static/js/freemail/freemail.js"></script>
<script src="/static/js/leadcollection.js"></script>
<script src="/static/js/address-validation.js"></script>
<style>
    #address-input-container {
        position: relative;
    }
    .pac-container {
        z-index: 120004; /* High z-index to appear over modals */
    }
    #common-lead-form.in{
    }
    #common-lead-form .ml8{
        margin-left: 8px;
    }
    #common-lead-form .uflex{
        display: flex;
    }
    #common-lead-form .modal-body{
        max-height: 100%;
    }
    #common-lead-form .modal-body > .row-fluid{
        min-height: 550px;
        display: flex;
    }
    #common-lead-form .vcenter {
        display: flex;
        vertical-align: middle;
        align-items: center;
    }
    #common-lead-form .img-fluid{
        max-width: 100%;
        vertical-align: middle;
    }
    #common-lead-form .lead-bg{
        padding: 8px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        background: #fafafa;
        margin: -15px 0 -15px -15px;
    }
    #common-lead-form .lead-bg img{
        width: 75%;
        height: 100%;
    }
    #common-lead-form h3{
        font-family: 'Open Sans', sans-serif !important;
        font-style: normal;
        font-weight: bold;
        font-size: 24px;
        letter-spacing: 0.05em;
        line-height: 52px;
        color: black;
    }
    #common-lead-form .control-group .control-label{
        font-family: 'Open Sans', sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 24px;
        color: #000000;
        margin: 12px 0px 6px 0px;
    }
    #common-lead-form .control-group input:not([type="checkbox"]){
        border: 1px solid #BDBDBD;
        box-sizing: border-box;
        border-radius: 6px;
        width: 100%;
        height: 40px;
        padding: 14px 16px;
    }


    #common-lead-form #common-lead-form-submit{
        border-radius: 8px;
        border: none;
        margin-top: 16px;
        width: 100%;
        background: #FF761A;
        box-sizing: border-box;
        box-shadow: 10px 0px 20px rgba(244, 127, 32, 0.3), -10px 4px 20px rgba(255, 118, 26, 0.2);
        color: #fff;
        height: 45px !important;
    }
    #common-lead-form .common-lead-info-heading{
        font-family: Open Sans;
        font-style: normal;
        font-weight: bold;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: 0.04em;
        color: #000000;
        text-align: left;
    }
    #common-lead-form .common-lead-info-desc{
        font-family: Open Sans;
        font-style: normal;
        font-size: 12px;
        line-height: 24px;
        text-align: justify;
    }
    #common-lead-form .common-lead-info-section{
        border: 1px solid #D3D2D3;
        margin-top: 4px;
        padding: 15px;
        border-radius: 10px;
    }
    #common-lead-form .common-lead-close{
        position: absolute;
        right: 15px;
        font-size: 40px;
        color: #000;
        opacity: 1;
        font-weight: 400;
    }
    #common-lead-form .err-msg {
        color: #FF761A;
        display: none;
        margin-bottom: -10px;
        margin-top: -10px;
    }
    @media (min-width: 900px){
        #common-lead-form.in{
            width: 800px;
            min-height: 580px;
            top: calc(100% / 2.5);
            left: calc(100% / 2.5);
        }
        #dialog-success.in {
            top: calc(100% / 2.5);
        }
    }

    .mrp5rem { margin-right: 0.5rem !important;}

    .w-33 {
        width: 33% !important;
    }

    .p0-w100 {
        padding: 0px 0px 0px 10px !important;
        width: 100% !important;
    }

    .order-sample-heading {
        font-family: Open Sans;
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        padding-top: 15px;
        padding-bottom: 15px;
    }

    .form-field .text-field {
        border: 1px solid #BDBDBD;
        box-sizing: border-box;
        border-radius: 6px;
        width: 425px;
        height: 40px;
        padding: 14px 16px;
    }

    #dialog-success {
        width: 650px;
        background: #fff;
        min-height: 550px;
        border-radius: 10px;
        display: none;
        border-top: 8px solid #4AB937;
        flex-direction: column;
        align-items: center;
    }
    .w-100 {
        width: 100% !important;
    }
    .close-div {
        text-align: right;
        padding-top: 20px;
        padding-right: 10px;
    }
    .close-div span {
        font-size: 28px;
        cursor: pointer;
    }
    .message-joy {
        font-family: Open Sans;
        font-style: normal;
        font-weight: 800;
        font-size: 24px;
        line-height: 79px;
        display: flex;
        align-items: center;
        letter-spacing: 0.05em;
    }
    .message-delivery {
        border: 1px solid;
        padding: 20px;
        border-radius: 10px;
        font-family: Open Sans;
        font-style: normal;
        font-weight: normal;
        font-size: 24px;
        line-height: 46px;
        align-items: center;
        text-align: center;
        letter-spacing: 0.05em;
    }

    #address-input-container .autocomplete-suggestions {
        border: 1px solid #ddd;
        border-top: none;
        max-height: 150px;
        overflow-y: auto;
        position: absolute;
        background-color: #fff;
        z-index: 1051; /* Higher than modal z-index */
        left: 0;
        right: 0;
        box-sizing: border-box;
    }
    .suggestion-item {
        padding: 8px;
        cursor: pointer;
    }
    .suggestion-item:hover {
        background-color: #f0f0f0;
    }

</style>
<div class="modal hide" id="common-lead-form" role="dialog" data-resource-type="Reference Design">
    <div class="modal-body">
        <div class="row-fluid">
            <div id="ref-design-sidebar" class="span5 hidden-phone vcenter lead-bg">
                <img src="/static/img/leads-form.svg">
            </div>
            <div id="order-sample-sidebar" class="span5 hidden-phone vcenter lead-bg" style="display: none">
                <img src="/static/img/leads-form.svg">
            </div>
            <div class="span7">
                <div class="order-sample-heading" style="" data-resource-type="Samples">Order free samples</div>
                <h3 class="modal-heading">Get the reference design</h3>
                <div class="row-fluid">
                    <div class="control-group span12">
                        <label class="control-label">Work Email </label>
                        <input type="email" placeholder="Email*" name="email" id="email">
                    </div>
                    <div class="control-group hidden">
                        <i id="lead-form-spinner" class="fa fa-spinner fa-pulse" style="
                            font-size: 30px;
                            margin-top: 45px;
                            color: #FF761A;
                        "></i>
                    </div>
                </div>
                <div class="row-fluid">
                    <div class="control-group span6">
                        <label class="control-label">First Name</label>
                        <input type="text" placeholder="First Name*" name="firstname" id="firstname">
                    </div>
                    <div class="control-group span6">
                        <label class="control-label">Last Name </label>        
                        <input type="text" placeholder="Last Name*" name="lastname" id="lastname">
                    </div>
                </div>
                <div class="row-fluid">
                    <div class="control-group form-field span6">
                        <label class="control-label">Country</label>
                        <select name="country" class="countries p0-w100 text-field" name='country' id="country">
                            <option value="">Select Country*</option>
                            <option value="Afghanistan" countryid="AF">Afghanistan</option>
                            <option value="Albania" countryid="AL">Albania</option>
                            <option value="Algeria" countryid="DZ">Algeria</option>
                            <option value="Andorra" countryid="AD">Andorra</option>
                            <option value="Angola" countryid="AO">Angola</option>
                            <option value="Anguilla" countryid="AI">Anguilla</option>
                            <option value="Antigua and Barbuda" countryid="AG">Antigua and Barbuda</option>
                            <option value="Argentina" countryid="AR">Argentina</option>
                            <option value="Armenia" countryid="AM">Armenia</option>
                            <option value="Australia" countryid="AU">Australia</option>
                            <option value="Austria" countryid="AT">Austria</option>
                            <option value="Azerbaijan" countryid="AZ">Azerbaijan</option>
                            <option value="Bahamas" countryid="BS">Bahamas</option>
                            <option value="Bahrain" countryid="BH">Bahrain</option>
                            <option value="Bangladesh" countryid="BD">Bangladesh</option>
                            <option value="Barbados" countryid="BB">Barbados</option>
                            <option value="Belarus" countryid="BY">Belarus</option>
                            <option value="Belgium" countryid="BE">Belgium</option>
                            <option value="Belize" countryid="BZ">Belize</option>
                            <option value="Benin" countryid="BJ">Benin</option>
                            <option value="Bermuda" countryid="BM">Bermuda</option>
                            <option value="Bhutan" countryid="BT">Bhutan</option>
                            <option value="Bolivia" countryid="BO">Bolivia</option>
                            <option value="Bosnia and Herzegovina" countryid="BA">Bosnia and Herzegovina</option>
                            <option value="Botswana" countryid="BW">Botswana</option>
                            <option value="Brazil" countryid="BR">Brazil</option>
                            <option value="Brunei Darussalam" countryid="BN">Brunei Darussalam</option>
                            <option value="Bulgaria" countryid="BG">Bulgaria</option>
                            <option value="Burkina Faso" countryid="BF">Burkina Faso</option>
                            <option value="Burundi" countryid="BI">Burundi</option>
                            <option value="Cambodia" countryid="KH">Cambodia</option>
                            <option value="Cameroon" countryid="CM">Cameroon</option>
                            <option value="Canada" countryid="CA">Canada</option>
                            <option value="Cape Verde" countryid="CV">Cape Verde</option>
                            <option value="Cayman Islands" countryid="KY">Cayman Islands</option>
                            <option value="Central African Republic" countryid="CF">Central African Republic</option>
                            <option value="Chad" countryid="TD">Chad</option>
                            <option value="Chile" countryid="CL">Chile</option>
                            <option value="China" countryid="CN">China</option>
                            <option value="Colombia" countryid="CO">Colombia</option>
                            <option value="Comoros" countryid="KM">Comoros</option>
                            <option value="Congo" countryid="CG">Congo</option>
                            <option value="Costa Rica" countryid="CR">Costa Rica</option>
                            <option value="Croatia (Hrvatska)" countryid="HR">Croatia (Hrvatska)</option>
                            <option value="Cuba" countryid="CU">Cuba</option>
                            <option value="Cyprus" countryid="CY">Cyprus</option>
                            <option value="Czech Republic" countryid="CZ">Czech Republic</option>
                            <option value="Denmark" countryid="DK">Denmark</option>
                            <option value="Djibouti" countryid="DJ">Djibouti</option>
                            <option value="Dominica" countryid="DM">Dominica</option>
                            <option value="Dominican Republic" countryid="DO">Dominican Republic</option>
                            <option value="Ecuador" countryid="EC">Ecuador</option>
                            <option value="Egypt" countryid="EG">Egypt</option>
                            <option value="El Salvador" countryid="SV">El Salvador</option>
                            <option value="Equatorial Guinea" countryid="GQ">Equatorial Guinea</option>
                            <option value="Eritrea" countryid="ER">Eritrea</option>
                            <option value="Estonia" countryid="EE">Estonia</option>
                            <option value="Ethiopia" countryid="ET">Ethiopia</option>
                            <option value="Faroe Islands" countryid="FO">Faroe Islands</option>
                            <option value="Fiji" countryid="FJ">Fiji</option>
                            <option value="Finland" countryid="FI">Finland</option>
                            <option value="France" countryid="FR">France</option>
                            <option value="French Guiana" countryid="GF">French Guiana</option>
                            <option value="French Polynesia" countryid="PF">French Polynesia</option>
                            <option value="French Southern Territories" countryid="TF">French Southern Territories</option>
                            <option value="Gabon" countryid="GA">Gabon</option>
                            <option value="Gambia" countryid="GM">Gambia</option>
                            <option value="Georgia" countryid="GE">Georgia</option>
                            <option value="Germany" countryid="DE">Germany</option>
                            <option value="Ghana" countryid="GH">Ghana</option>
                            <option value="Greece" countryid="GR">Greece</option>
                            <option value="Greenland" countryid="GL">Greenland</option>
                            <option value="Grenada" countryid="GD">Grenada</option>
                            <option value="Guadeloupe" countryid="GP">Guadeloupe</option>
                            <option value="Guam" countryid="GU">Guam</option>
                            <option value="Guatemala" countryid="GT">Guatemala</option>
                            <option value="Guinea" countryid="GN">Guinea</option>
                            <option value="Guinea-Bissau" countryid="GW">Guinea-Bissau</option>
                            <option value="Guyana" countryid="GY">Guyana</option>
                            <option value="Haiti" countryid="HT">Haiti</option>
                            <option value="Honduras" countryid="HN">Honduras</option>
                            <option value="Hong Kong" countryid="HK">Hong Kong</option>
                            <option value="Hungary" countryid="HU">Hungary</option>
                            <option value="Iceland" countryid="IS">Iceland</option>
                            <option value="India" countryid="IN">India</option>
                            <option value="Isle of Man" countryid="IM">Isle of Man</option>
                            <option value="Indonesia" countryid="ID">Indonesia</option>
                            <option value="Iran" countryid="IR">Iran</option>
                            <option value="Iraq" countryid="IQ">Iraq</option>
                            <option value="Ireland" countryid="IE">Ireland</option>
                            <option value="Israel" countryid="IL">Israel</option>
                            <option value="Italy" countryid="IT">Italy</option>
                            <option value="Ivory Coast" countryid="CI">Ivory Coast</option>
                            <option value="Jersey" countryid="JE">Jersey</option>
                            <option value="Jamaica" countryid="JM">Jamaica</option>
                            <option value="Japan" countryid="JP">Japan</option>
                            <option value="Jordan" countryid="JO">Jordan</option>
                            <option value="Kazakhstan" countryid="KZ">Kazakhstan</option>
                            <option value="Kenya" countryid="KE">Kenya</option>
                            <option value="Kiribati" countryid="KI">Kiribati</option>
                            <option value="North Korea" countryid="KP">North Korea</option>
                            <option value="South Korea" countryid="KR">South Korea</option>
                            <option value="Kosovo" countryid="XK">Kosovo</option>
                            <option value="Kuwait" countryid="KW">Kuwait</option>
                            <option value="Kyrgyzstan" countryid="KG">Kyrgyzstan</option>
                            <option value="Lao" countryid="LA">Lao</option>
                            <option value="Latvia" countryid="LV">Latvia</option>
                            <option value="Lebanon" countryid="LB">Lebanon</option>
                            <option value="Lesotho" countryid="LS">Lesotho</option>
                            <option value="Liberia" countryid="LR">Liberia</option>
                            <option value="Libyan Arab Jamahiriya" countryid="LY">Libyan Arab Jamahiriya</option>
                            <option value="Liechtenstein" countryid="LI">Liechtenstein</option>
                            <option value="Lithuania" countryid="LT">Lithuania</option>
                            <option value="Luxembourg" countryid="LU">Luxembourg</option>
                            <option value="Macedonia" countryid="MK">Macedonia</option>
                            <option value="Madagascar" countryid="MG">Madagascar</option>
                            <option value="Malawi" countryid="MW">Malawi</option>
                            <option value="Malaysia" countryid="MY">Malaysia</option>
                            <option value="Maldives" countryid="MV">Maldives</option>
                            <option value="Mali" countryid="ML">Mali</option>
                            <option value="Malta" countryid="MT">Malta</option>
                            <option value="Marshall Islands" countryid="MH">Marshall Islands</option>
                            <option value="Martinique" countryid="MQ">Martinique</option>
                            <option value="Mauritania" countryid="MR">Mauritania</option>
                            <option value="Mauritius" countryid="MU">Mauritius</option>
                            <option value="Mexico" countryid="MX">Mexico</option>
                            <option value="Micronesia, Federated States of" countryid="FM">Micronesia, Federated States of</option>
                            <option value="Moldova, Republic of" countryid="MD">Moldova, Republic of</option>
                            <option value="Monaco" countryid="MC">Monaco</option>
                            <option value="Mongolia" countryid="MN">Mongolia</option>
                            <option value="Montenegro" countryid="ME">Montenegro</option>
                            <option value="Montserrat" countryid="MS">Montserrat</option>
                            <option value="Morocco" countryid="MA">Morocco</option>
                            <option value="Mozambique" countryid="MZ">Mozambique</option>
                            <option value="Myanmar" countryid="MM">Myanmar</option>
                            <option value="Namibia" countryid="NA">Namibia</option>
                            <option value="Nauru" countryid="NR">Nauru</option>
                            <option value="Nepal" countryid="NP">Nepal</option>
                            <option value="Netherlands" countryid="NL">Netherlands</option>
                            <option value="Netherlands Antilles" countryid="AN">Netherlands Antilles</option>
                            <option value="New Caledonia" countryid="NC">New Caledonia</option>
                            <option value="New Zealand" countryid="NZ">New Zealand</option>
                            <option value="Nicaragua" countryid="NI">Nicaragua</option>
                            <option value="Niger" countryid="NE">Niger</option>
                            <option value="Nigeria" countryid="NG">Nigeria</option>
                            <option value="Northern Mariana Islands" countryid="MP">Northern Mariana Islands</option>
                            <option value="Norway" countryid="NO">Norway</option>
                            <option value="Oman" countryid="OM">Oman</option>
                            <option value="Pakistan" countryid="PK">Pakistan</option>
                            <option value="Palau" countryid="PW">Palau</option>
                            <option value="Palestine" countryid="PS">Palestine</option>
                            <option value="Panama" countryid="PA">Panama</option>
                            <option value="Papua New Guinea" countryid="PG">Papua New Guinea</option>
                            <option value="Paraguay" countryid="PY">Paraguay</option>
                            <option value="Peru" countryid="PE">Peru</option>
                            <option value="Philippines" countryid="PH">Philippines</option>
                            <option value="Poland" countryid="PL">Poland</option>
                            <option value="Portugal" countryid="PT">Portugal</option>
                            <option value="Puerto Rico" countryid="PR">Puerto Rico</option>
                            <option value="Qatar" countryid="QA">Qatar</option>
                            <option value="Reunion" countryid="RE">Reunion</option>
                            <option value="Romania" countryid="RO">Romania</option>
                            <option value="Russian Federation" countryid="RU">Russian Federation</option>
                            <option value="Rwanda" countryid="RW">Rwanda</option>
                            <option value="Saint Kitts and Nevis" countryid="KN">Saint Kitts and Nevis</option>
                            <option value="Saint Lucia" countryid="LC">Saint Lucia</option>
                            <option value="Saint Vincent and the Grenadines" countryid="VC">Saint Vincent and the Grenadines</option>
                            <option value="Samoa" countryid="WS">Samoa</option>
                            <option value="San Marino" countryid="SM">San Marino</option>
                            <option value="Sao Tome and Principe" countryid="ST">Sao Tome and Principe</option>
                            <option value="Saudi Arabia" countryid="SA">Saudi Arabia</option>
                            <option value="Senegal" countryid="SN">Senegal</option>
                            <option value="Serbia" countryid="RS">Serbia</option>
                            <option value="Seychelles" countryid="SC">Seychelles</option>
                            <option value="Sierra Leone" countryid="SL">Sierra Leone</option>
                            <option value="Singapore" countryid="SG">Singapore</option>
                            <option value="Slovakia" countryid="SK">Slovakia</option>
                            <option value="Slovenia" countryid="SI">Slovenia</option>
                            <option value="Solomon Islands" countryid="SB">Solomon Islands</option>
                            <option value="Somalia" countryid="SO">Somalia</option>
                            <option value="South Africa" countryid="ZA">South Africa</option>
                            <option value="Spain" countryid="ES">Spain</option>
                            <option value="Sri Lanka" countryid="LK">Sri Lanka</option>
                            <option value="St. Helena" countryid="SH">St. Helena</option>
                            <option value="St. Pierre and Miquelon" countryid="PM">St. Pierre and Miquelon</option>
                            <option value="Sudan" countryid="SD">Sudan</option>
                            <option value="Suriname" countryid="SR">Suriname</option>
                            <option value="Svalbard and Jan Mayen Islands" countryid="SJ">Svalbard and Jan Mayen Islands</option>
                            <option value="Swaziland" countryid="SZ">Swaziland</option>
                            <option value="Sweden" countryid="SE">Sweden</option>
                            <option value="Switzerland" countryid="CH">Switzerland</option>
                            <option value="Syrian Arab Republic" countryid="SY">Syrian Arab Republic</option>
                            <option value="Taiwan" countryid="TW">Taiwan</option>
                            <option value="Tajikistan" countryid="TJ">Tajikistan</option>
                            <option value="Tanzania" countryid="TZ">Tanzania</option>
                            <option value="Thailand" countryid="TH">Thailand</option>
                            <option value="Togo" countryid="TG">Togo</option>
                            <option value="Tokelau" countryid="TK">Tokelau</option>
                            <option value="Tonga" countryid="TO">Tonga</option>
                            <option value="Trinidad and Tobago" countryid="TT">Trinidad and Tobago</option>
                            <option value="Tunisia" countryid="TN">Tunisia</option>
                            <option value="Turkey" countryid="TR">Turkey</option>
                            <option value="Turkmenistan" countryid="TM">Turkmenistan</option>
                            <option value="Tuvalu" countryid="TV">Tuvalu</option>
                            <option value="Uganda" countryid="UG">Uganda</option>
                            <option value="Ukraine" countryid="UA">Ukraine</option>
                            <option value="United Arab Emirates" countryid="AE">United Arab Emirates</option>
                            <option value="United Kingdom" countryid="GB">United Kingdom</option>
                            <option value="United States" countryid="US">United States</option>
                            <option value="United States minor outlying islands" countryid="UM">United States minor outlying islands</option>
                            <option value="Uruguay" countryid="UY">Uruguay</option>
                            <option value="Uzbekistan" countryid="UZ">Uzbekistan</option>
                            <option value="Vanuatu" countryid="VU">Vanuatu</option>
                            <option value="Venezuela" countryid="VE">Venezuela</option>
                            <option value="Vietnam" countryid="VN">Vietnam</option>
                            <option value="Virgin Islands (U.S.)" countryid="VI">Virgin Islands (U.S.)</option>
                            <option value="Wallis and Futuna Islands" countryid="WF">Wallis and Futuna Islands</option>
                            <option value="Yemen" countryid="YE">Yemen</option>
                            <option value="Zambia" countryid="ZM">Zambia</option>
                            <option value="Zimbabwe" countryid="ZW">Zimbabwe</option>
                        </select>
                    </div>
                    <div class="control-group form-field span6">
                        <label class="control-label">Industry</label>
                        <select name="industry" class="p0-w100 text-field" id="industry">
                            <option value="">Select Industry*</option>
                            <option value="Automotive">Automotive</option>
                            <option value="Contract Manufacturer">Contract Manufacturer</option>
                            <option value="Desktop Phones">Desktop Phones</option>
                            <option value="eHealth">eHealth</option>
                            <option value="Entertainment">Entertainment</option>
                            <option value="Industrial Modem">Industrial Modem</option>
                            <option value="Metering - AMR">Metering - AMR</option>
                            <option value="Mobile Computing">Mobile Computing</option>
                            <option value="Navigation">Navigation</option>
                            <option value="Payment Systems">Payment Systems</option>
                            <option value="Security">Security</option>
                            <option value="Tracking and Tracing">Tracking and Tracing</option>
                            <option value="Not applicable">Not applicable</option>
                            <option value="Remote Maintenance and Control">Remote Maintenance and Control</option>
                        </select>
                    </div>
                </div>
                <div class="control-group">
                        <label class="control-label">Company </label>        
                        <input type="text" placeholder="Company*" name="company" id="company">
                </div>
                <div class="control-group" id="project-input-container" style="display: none;">
                    <label class="control-label">Project Name </label>
                    <input type="text" placeholder="Project Name*" name="project_name" id="project_name">
                </div>
                <div class="control-group" id="address-input-container" style="display: none;">
                    <label class="control-label">Address </label>
                    <input type="text" placeholder="Address*" name="address" id="address">
                </div>
                <div class='control-group uflex'>
                    <input type="checkbox" id="terms" name="terms" value="terms" checked>
                    <label id="termsLabel" for="terms" class='ml8 regular'> I agree to share this information with Quectel.</label>
                </div>
                <div class='control-group'>
                    <div class='err-msg'>* Please fill required fields</div>
                    <input type='Submit' class="btn-quectel-reference-design" id='common-lead-form-submit' value="Get Reference Design" data-location="toplink">
                </div>  
                <div class="row-fluid">
                    <div class="span12 common-lead-info-section">
                        <h3 class='common-lead-info-heading'>Why we ask for this information</h3>
                        <div class='common-lead-info-desc'>
                            The supplier asked us to collect this information for this resource. At SnapMagic Search, we care about our users and only share information with your permission.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal hide" id="dialog-success" role="dialog">
    <div class="w-100">
        <div class="close-div">
            <span type="button" class="dialog-success" data-dismiss="modal" aria-hidden="true"> ⨉ </span>
        </div>
    </div>
    <div>
        <img src="/static/img/free-sampes-success.svg" style=" width: 300px; max-width: 100%;vertical-align: middle;border: 0;">
    </div>
    <h2 class="message-joy">
    We hope you enjoy your free samples!
    </h2>
    <div class="message-delivery">
        <div> Your free samples will be sent to </div>
        <div style="font-weight: bold;" class="text-email"></div>
    </div>
</div>

<script>
    function addEvents() {
        $(document).ready(function() {
            $('.modal-close, .close-div span').unbind();

            $('.modal-close, .close-div span').on('click', function() {
                $('.modal-backdrop').hide();
                $('#dialog-success').modal('display', 'none')
            });

            $(document).off('click', '.modal-backdrop').on('click', '.modal-backdrop', function(e){
                if ($(e.target).hasClass('modal-backdrop')) {
                    $('#dialog-success').modal('display', 'none')
                    $('.lead-modal-overlay').hide();
                }
            })
        })
    }

    function track_mixpanel_click(tracking_data){
        var useAsync = true;
        useAsync = navigator.userAgent.toLowerCase().indexOf('firefox') < 0;
        $.ajax({
            type: "POST",
            async: useAsync,
            url: "/api/mixpanel_click_track/",
            data: tracking_data
        });
    }

    function open_quectel_ad_lead_form(element){
        useEnrichment = false;
        $('body').append('<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@800&display=swap" rel="stylesheet">');
        $('#common-lead-form .err-msg').html('').hide();
        var unipart_id = element.data('unipart-id'),
        lead_url = element.data('lead-url'),
        location = element.data('tracking-location'),
        manufacturer = element.data('type');

        const resourcekey = element.data('resourcekey');

        addEvents();

        $('#industry').parent().hide();

        if(manufacturer && manufacturer.toUpperCase().indexOf('QUECTEL') !== -1) {
            manufacturer = 'QUECTEL'

            // Inser a new input
            if ($('#jobtitle').length == 0) {
                const JOBTITLETEMPLATE = '<div class="control-group"><label class="control-label">Job Title </label><input type="text" placeholder="Job Title*" name="jobtitle" id="jobtitle"></div>'
                $(JOBTITLETEMPLATE).insertAfter($('#company').parent())
            }

            $('#jobtitle').parent().hide();
            $('#company').parent().hide();
            $('#company').parent().hide();
            $('#firstname').parent().hide();
            $('#lastname').parent().hide();
            $('#cityId').parent().hide();
            $('#stateId').parent().hide();
            $('#country').parent().hide();
            // Change resource type
            $('#common-lead-form').attr('data-resource-type', resourcekey)
            $('.order-sample-heading').html('');
            $('.modal-heading').text('Get ' + resourcekey);
            $('#common-lead-form-submit').val('Get ' + resourcekey);
            useEnrichment = true;
        } else if(manufacturer && manufacturer.toUpperCase().indexOf('THALES') !== -1) {
            manufacturer = 'THALES';
            // Add the Thales X SnapMagic Search
            $('.order-sample-heading').html('<div style="display: flex; justify-content: center; align-items: center; gap: 10px;"><img src="/static/img/part_embed/gemalto-logo.png" style="max-width: 30% !important;"><span style="font-size: 14px; font-weight: normal;">&amp;</span><img src="/static/img/snapeda_logo_260.png" style="width:120px"></div>');
            $('.order-sample-heading').css('padding-bottom', '0px');
            // Change the form heading
            $('.modal-heading').text('Get ' + resourcekey);
            $('.modal-heading').css('margin-bottom', '5px');
            // Change disclaimer
            $('.common-lead-info-heading').html('');
            $('.common-lead-info-desc').text("The supplier has asked us for this information for this resource. At SnapMagic Search, we care about our users and only share information with your permission.");
            // Change the button text
            $('#common-lead-form-submit').val('Download');
            // Change resource type of the lead form
            $('#common-lead-form').attr('data-resource-type', resourcekey)
            // Set forms
            $('#industry').parent().show();
            $('#email').parent().parent().append($('#company').parent());
            $('#email').parent().removeClass('span12').addClass('span6');
            $('#company').parent().removeClass('span12').addClass('span6');
            // Hide unwanted forms
            $('#termsLabel').parent().hide();
            $('#lead-form-spinner').parent().remove();
            $('#ref-design-sidebar').css('display', 'none');
            $('#order-sample-sidebar').css('display', 'flex');
        } else {
            $('.modal-heading').text('');
            $('.modal-heading').css('margin-bottom', '5px');
            $('#common-lead-form-submit').val('Request free samples');
            $('#ref-design-sidebar').css('display', 'none');
            $('#order-sample-sidebar').css('display', 'flex');
            $('.common-lead-info-desc').text("The supplier will be sending you these samples directly and they'll need to follow up with you to facilitate the shipment.");
            $('#country').parent().hide();
            // Change resource type
            $('#common-lead-form').attr('data-resource-type', 'Samples')
        }

        if(manufacturer && manufacturer.toUpperCase().indexOf('ABRACON') !== -1) {
            manufacturer = 'ABRACON'

            // Set modal titles, headers and texts.
            $('#common-lead-form-submit').val('Request samples');
            $('#termsLabel').text('I agree to share this information with Abracon');

            // Show additonal fields.
            $('#project-input-container').show();
        }

        if(manufacturer && manufacturer.toUpperCase().indexOf('SUPERIOR SENSOR') !== -1) {
            manufacturer = 'SUPERIOR SENSOR'
            $('#termsLabel').text('I agree to share this information with Superior Sensor')
        }

        if(manufacturer && manufacturer.toUpperCase().indexOf('MPD') !== -1) {
            manufacturer = 'MPD'
            $('#termsLabel').text('I agree to share this information with MPD')
            $('#common-lead-form-submit').val('Request samples');
            $('.order-sample-heading').remove();
            $('.modal-heading').text('Request free samples');

            // Show additonal fields.
            $('#address-input-container').show();
        }

        common_saveImpression(location, manufacturer);

        if(unipart_id){
            $('#common-lead-form').data('unipart-id', unipart_id);
        }
        if(lead_url){
            $('#common-lead-form').data('lead-url', lead_url);
        }
        if(location){
            $('#common-lead-form').data('location', location);
        }
        if(manufacturer){
            $('#common-lead-form').data('manufacturer', manufacturer);
        }
        $('#common-lead-form').modal('show');
        $(document).off('hide', '#common-lead-form').on('hide', '#common-lead-form', function () {
            $('link[rel=stylesheet][href~="https://fonts.googleapis.com/css2?family=Open+Sans:wght@800&display=swap"]').remove();
        });
        if(useEnrichment) {
            addEnrichmentEvent();
        }
    }

    function common_saveImpression(location, manufacturer){
        var type = 'Lead Impression';
        const properties = {
            'type': type,
            'location': location,
            'manufacturer': manufacturer,
            'event_name': 'Referral'
        }
        track_mixpanel_click(properties);
    }

    function common_saveLead(payload, location) {
        var type = 'Lead Collected';
        var save_lead_url = "/analytics/api/save_lead/"
        const properties = {
            'type': type,
            'location': location,
            'manufacturer': payload.ref_manufacturer,
            'event_name': 'Referral',
            'lead_source': 'search'
        }
        track_mixpanel_click(properties);

        $.ajax({
            type: "POST",
            url: save_lead_url,
            data: payload,
            success: function ( response ) {
                if(payload.ref_manufacturer.toUpperCase().indexOf('QUECTEL') !== -1) {
                    $('#common-lead-form').modal('hide');
                    const redirectLead = $('#common-lead-form').data('lead-url')
                    window.open(redirectLead, '_blank');
                } else {
                    $('#common-lead-form').modal('hide');
                    $('.text-email').text(payload.email);
                    $('#dialog-success').css('display', 'flex');
                    $('#dialog-success').modal('show');
                }
            }
        });
    }

    function isRoleBasedEmail(email) {
        let isRoleBased = false;
        const roleBasedEmailList = ['admin@', 'default@', 'email@', 'feedback@', 'info@', 'mail@', 'marketing@',
        'news@', 'newsletter@', 'ops@', 'orders@', 'press@', 'pressrelease@', 'sales@', 'security@', 'support@']
        if (roleBasedEmailList.some((item) => email.includes(item))) {
            isRoleBased = true;
        }
        return isRoleBased;
    }

    function validateRequiredInputs(inputs) {
        for (let i = 0; i < inputs.length; i++) {
            if (inputs[i] === '') {
            return false;
            }
        }
        return true;
    }

    $('#common-lead-form #common-lead-form-submit').on('click',function(event){
        $('#common-lead-form .err-msg').hide();
        if ($('#address').length > 0) {
            const currentAddress = $('#address').val();
            const validatedAddress = sessionStorage.getItem('validatedAddress');

            // If an address is typed but it doesn't match a validated one from the dropdown
            if (currentAddress && currentAddress !== validatedAddress) {
                $('#common-lead-form .err-msg').html('Please select a valid address from the dropdown suggestions.').show();
                event.preventDefault(); // Stop the form submission
                return false;
            }
        }
        var location = $('#common-lead-form').data('location');
        var resource_type = $('#common-lead-form').attr('data-resource-type');
        unipart_id = $('#common-lead-form').data('unipart-id');
        if(window.iti &&
            window.iti.getSelectedCountryData() &&
            window.iti.getSelectedCountryData().dialCode &&
            $('#common-lead-form #phone_number').val() !== '') {
            var phoneNumber = '+' + window.iti.getSelectedCountryData().dialCode + $('#common-lead-form #phone_number').val();
        } else {
            phoneNumber = '';
        }

        var tmp_email = $('#common-lead-form #email').val();
        if(tmp_email !== ''){
            const country =  $('#countryId').val() ? $('#countryId').val() : ''
            const state =  $('#stateId').val() ? $('#stateId').val() : ''
            const city =  $('#cityId').val() ? $('#cityId').val() : ''

            // By default, we IGNORE the validation.
            // If resource type is "samples", do not ignore validation unless it's Superior Sensor or Recom.
            // Regardless of resource type, do not ignore validation if manufacturer is NXP or Quectel.
            var ignoreValidation = true;

            var r_manufacturer = $('#common-lead-form').data('manufacturer');
            if(resource_type != undefined){
               if(resource_type.toLowerCase().trim() === 'samples'){
                   if(r_manufacturer.toUpperCase().indexOf('SUPERIOR SENSOR') === -1 && r_manufacturer.toUpperCase().indexOf('RECOM') === -1) {
                       ignoreValidation = false;
                   }
               }
            }

            // validate if manufacturer is not traco power
            if (r_manufacturer.toUpperCase().indexOf('TRACO POWER') !== -1) {
                ignoreValidation = false;
            }

            //test email
            const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            let isValidEmail = re.test(String(tmp_email).toLowerCase());
            if (($('#common-lead-form').data('manufacturer') || '').toUpperCase().indexOf('QUECTEL') !== -1) {
                isValidEmail = isValidEmail && !isRoleBasedEmail(tmp_email);
                ignoreValidation = false;
            }
            if(!isValidEmail) {
                $('#common-lead-form .err-msg').html('* Please enter a valid email address').show();
            } else {
                //use freemail js to verify valid work mail
                freemail.check(tmp_email, ignoreValidation).then(function(valid_work_email){

                    const emailInput = $('#common-lead-form #email').val();
                    const firstnameInput = $('#common-lead-form #firstname').val();
                    const lastnameInput = $('#common-lead-form #lastname').val();
                    const companyInput = $('#common-lead-form #company').val();
                    const addressInput = $('#common-lead-form #address').val();
                    const termsIsChecked = $('#common-lead-form #terms').is(":checked");
                    const countrySelected = $('#common-lead-form #country').val();
                    const countryCodeSelection = $('#common-lead-form #country option:selected').attr('countryid')
                    const industrySelected = $('#common-lead-form #industry').val();

                    const ref_manufacturer = $('#common-lead-form').data('manufacturer') || '';
                    let hasValidProject = true;
                    if(ref_manufacturer.toUpperCase().indexOf('ABRACON') !== -1) {
                        hasValidProject = $('#project_name').val() !== '' ? true : false;
                    }

                    let isRequiredInputValidated = emailInput !== '' && firstnameInput !== '' && lastnameInput !== '' && companyInput !== '' && termsIsChecked === true && hasValidProject;

                    if(ref_manufacturer.toUpperCase().indexOf('THALES') !== -1) {
                        isRequiredInputValidated = emailInput !== '' && companyInput !== '' && firstnameInput !== '' && lastnameInput !== '' && countrySelected !== '' && industrySelected !== '';
                    }

                    if(ref_manufacturer.toUpperCase().indexOf('QUECTEL') !== -1) {
                        // Validates that job title is filled for QUECTEL manufacturer
                        isRequiredInputValidated = isRequiredInputValidated && $('#common-lead-form #jobtitle').val() !== '';
                    }

                    if(ref_manufacturer.toUpperCase().indexOf('MPD') !== -1) {
                        const inputs = [emailInput, firstnameInput, lastnameInput, companyInput, addressInput];
                        isRequiredInputValidated = validateRequiredInputs(inputs);
                    }

                    if(valid_work_email){
                        if(isRequiredInputValidated) {
                            if(ref_manufacturer.toUpperCase().indexOf('QUECTEL') !== -1) {
                                var payload = {
                                    firstName: firstnameInput,
                                    lastName: lastnameInput,
                                    email: emailInput,
                                    company: companyInput,
                                    jobtitle: $('#common-lead-form #jobtitle').val(),
                                    country: country,
                                    state: state,
                                    city: city,
                                    address: addressInput,
                                    addressLine1: '',
                                    addressLine2: '',
                                    phone_number: phoneNumber,
                                    csrfmiddlewaretoken: "E4b43xMHQNxotThXnMMpXjL4ydAJeLd49EFySOeyFRBZTy6cQonvNz1wLBMzBDyx",
                                    ad_manager_id: $('#adManagerId').val(),
                                    adtext_link: resource_type,
                                    resource_type: resource_type,
                                    ref_manufacturer: ref_manufacturer,
                                    unipart_id: unipart_id,
                                }
                            } else if(ref_manufacturer.toUpperCase().indexOf('ABRACON') !== -1) {
                                var payload = {
                                    firstName: firstnameInput,
                                    lastName: lastnameInput,
                                    email: emailInput,
                                    company: companyInput,
                                    project_name: $('#project_name').val(),
                                    csrfmiddlewaretoken: "E4b43xMHQNxotThXnMMpXjL4ydAJeLd49EFySOeyFRBZTy6cQonvNz1wLBMzBDyx",
                                    ad_manager_id: $('#adManagerId').val(),
                                    adtext_link: "Free Samples",
                                    resource_type: resource_type,
                                    ref_manufacturer: ref_manufacturer,
                                    unipart_id: unipart_id,
                                }
                            } else if(ref_manufacturer.toUpperCase().indexOf('THALES') !== -1) {
                                var payload = {
                                    firstName: firstnameInput,
                                    lastName: lastnameInput,
                                    email: emailInput,
                                    company: companyInput,
                                    country: countrySelected,
                                    user_country_code_location: countryCodeSelection,
                                    csrfmiddlewaretoken: "E4b43xMHQNxotThXnMMpXjL4ydAJeLd49EFySOeyFRBZTy6cQonvNz1wLBMzBDyx",
                                    ad_manager_id: $('#adManagerId').val(),
                                    adtext_link: resource_type,
                                    resource_type: resource_type,
                                    ref_manufacturer: ref_manufacturer,
                                    unipart_id: unipart_id,
                                    industy: industrySelected
                                }
                            } else {
                                var payload = {
                                    firstName: firstnameInput,
                                    lastName: lastnameInput,
                                    email: emailInput,
                                    company: companyInput,
                                    address: addressInput,
                                    csrfmiddlewaretoken: "E4b43xMHQNxotThXnMMpXjL4ydAJeLd49EFySOeyFRBZTy6cQonvNz1wLBMzBDyx",
                                    ad_manager_id: $('#adManagerId').val(),
                                    adtext_link: "Free Samples",
                                    resource_type: resource_type,
                                    ref_manufacturer: ref_manufacturer,
                                    unipart_id: unipart_id,
                                }
                            }

                            common_saveLead(payload, location);
                            dataLayer.push({ "event": "gtm-lead-form-submit"});
                        }else {
                            // try to run clearbit api to populate missing values
                            clearBitApi();
                            if (!isRequiredInputValidated && $('#common-lead-form #firstname').is(":visible")) {
                                $('#common-lead-form .err-msg').html('* Please fill required fields').show();
                            }
                        }
                    }else{
                        $('#common-lead-form .err-msg').html('Please enter a valid work email (not personal email)').show();
                    }
                });
            }
        }else{
            $('#common-lead-form .err-msg').html('* Please fill required fields').show();
        }

        event.preventDefault();
        event.stopPropagation();
    });

    $(document).ready(function() {
        if ($('#address').length) {
            initAddressAutocomplete('address');
        }
    });
</script>
<script>
$(document).off('mouseup', '.inline_ad_template_col').on('mouseup', '.inline_ad_template_col', function(e){
    if (e.which == 3){
        return;
    }
    var clickable = true;
    if(!$(this).hasClass('inline_ad_template_col')) {
        clickable = false;
    }
    if (clickable) {

        var ad_version = 'Manufacturer style',
        hide_stock = 'N/A',
        ad_type = $(this).data('type'),
        type_tracking = 'Search - View Part',
        is_custom = $(this).data('is-custom'),
        tracking_type = $(this).data('tracking-type'),
        tracking_location = $(this).data('tracking-location'),
        url = $(this).data('url'),
        part = $(this).data('part'),
        relevancy = 'N/A',
        callback = $(this).data('callback');
        
        if(is_custom){
            type_tracking = 'Custom cable click';
        }
        if(tracking_type){
            type_tracking = tracking_type;
        }

        let ad_ab_version = 'a';
        let auction_id = 'NA';
        let search_type = '';
        let ad_keywords = '';
        if (window.searchFeatured !== undefined) {
            ad_ab_version = window.searchFeatured['ad_ab_version'] || 'a';
            auction_id = window.searchFeatured['auction_id'] || 'NA';
            search_type = window.searchFeatured['search_type'] || '';
            ad_keywords = window.searchFeatured['ad_keywords'] || '';
        }
        var Properties = {
            'Type': type_tracking,
            'hide_stock': hide_stock,
            'ad_version': ad_version,
            'Ad_type': 'snapeda',
            'Search Term': 'APX803L20\u002D30SA\u002D7',
            'userid': '',
            'description_shown': 'default description',
            'download_button_position': 'left',
            'Part': part,
            'relevancy': relevancy,
            'a_b_version': 'b',
            'sample_icon': 'N/A',
            'is_xref': false,
            'ad_ab_version': ad_ab_version,
            'auction_id': auction_id + '',
            'search_type': search_type,
            'ad_keywords': ad_keywords
        };

        Properties['Part Image'] = 'component';

         // Brist ad click tracking | untracked digikey, mouser, arrow
        if(['digikey', 'mouser', 'arrow'].indexOf(ad_type) === -1 && window.searchFeatured !== undefined) {
          if (window.searchFeatured['unipart_id'] !== undefined && window.searchFeatured['ad_ab_source'] === 'brist') {
            recordAdEvent('CLICK_DATA_TYPE', window.searchFeatured['unipart_id'] + '', window.searchFeatured['auction_id']);
          }
        }

        mixpanel.track(ad_type, Properties);
        track_db_and_mixpanel(ad_type, type_tracking, 'APX803L20\u002D30SA\u002D7', relevancy, part, true, ad_ab_version, auction_id);
        var tab_target = '_self';
        if(is_custom){
            tab_target = '_blank';
        }else{
            var ref = research_feature_ref(ad_type.toUpperCase(), '', false);
            var queryparams = getQueryParams(url.substring(url.indexOf("?")))
            if (!('ref' in queryparams) && !('t' in queryparams)
                && !('con_ref' in queryparams)) {
                url = url + '?ref=' + ref + '&t=APX803L20\u002D30SA\u002D7' + '&con_ref=None';
            }
        }
        if(callback){
            if (typeof window[callback] === 'function'){
                window[callback]($(this));
            }
        }else{
            window.open(url, tab_target);
        }
    }
});
function generate_ad_by_template(data, url, type, is_custom, show_manuf_logo, manuf_logo_top){
    if(show_manuf_logo == undefined){
        show_manuf_logo = true;
    }
    if(manuf_logo_top == undefined){
        manuf_logo_top = false;
    }
    var ad_template = $('#inline_ad_template_col');
    //inline_ad_template_col

    var clone_col_1 = ad_template.clone();

    clone_col_1.attr('id','').data('url', url).data('type', type).data('part', data.part).data('is-custom', is_custom);
    clone_col_1.removeClass('hidden');

    var image_element = clone_col_1.find('.ad_part_image');
    image_element.attr('src', data.coverart);
    if(show_manuf_logo){
        clone_col_1.find('.ad_manuf_image').attr('src', data.manuf__image_100_20);
        if(manuf_logo_top){
            clone_col_1.find('.ad_manuf_image_bottom').remove();
        }else{
            clone_col_1.find('.ad_manuf_image_top').remove();
        }
    }else{
        image_element.parent().parent().addClass('center-span-content');
        clone_col_1.find('.ad_manuf_image').hide();
    }

    image_element.on('error', function (){
        image_element.unbind('error');
        var obj = $(this);
        if(data.fallback_image){
            obj.attr('src', data.fallback_image);
        }else{
            obj.hide();
            obj.parent().append('<i class="fa fa-microchip microchip" aria-hidden="true"></i>');
        }
    });

    clone_col_1.find('.ad_title_link').text(data.part);
    clone_col_1.find('.ad_description_container').html(data.description ? data.description : data.shortdesc);

    if(!is_custom){
        clone_col_1.find('.ad_sponsored_icon').addClass('hidden');
        clone_col_1.find('.ad_title_description').text("by "+data.manuf__name);
        clone_col_1.find('.ad_icon').each(function(i, obj) {
            var k = $(obj).data('key'),
            v = data['has_'+k.toLowerCase()],
            b = '/static/img/search_icons/',
            has_v = false;
            if(k.toLowerCase() == 'datasheet'){
                if(data.datasheeturl){
                    has_v = true;
                    $(obj).attr('src', b+$(obj).data('yes'));
                }else{                                      
                    $(obj).attr('src', b+$(obj).data('no'));
                }
            }else{
            if(v != undefined && v > 0){
                has_v = true;
                $(obj).attr('src', b+$(obj).data('yes'));
            }else{
                $(obj).attr('src', b+$(obj).data('no'));
            }
            }
            var t = k + ' available';
            if(!has_v){
            t = k + ' not available';
            }
            $(obj).attr('data-original-title', t);
            $(obj).attr('alt', t);
        });
    }else{
        var button_text = 'Design your cable';
        if(data.override_button_text){
            button_text = data.override_button_text;
        }
        clone_col_1.addClass('custom_featured');
        clone_col_1.find('.ad_data_container').addClass('hidden');
        clone_col_1.find('.ad_title_link').addClass('deep-koamaru');
        clone_col_1.find('.ad_link_button').removeClass('orange').addClass('bg-deep-koamaru').text(button_text);
    }

    if(data.tracking_type){
        clone_col_1.data('tracking-type', data.tracking_type);
    }
    if(data.tracking_location){
        clone_col_1.data('tracking-location', data.tracking_location);
    }
    if(data.callback){
        clone_col_1.data('callback', data.callback);
    }
    if(data.unipart_id){
        clone_col_1.data('unipart-id', data.unipart_id);
    }
    if(data.reflink){
        if(data.reflink.linkurl){
            clone_col_1.data('lead-url', data.reflink.linkurl);
        }
    }

    return clone_col_1;
}
function getQueryParams(query) {
    return query
        ? (/^[?#]/.test(query) ? query.slice(1) : query)
            .split('&')
            .reduce(function(params, param) {
                    let key = param.split('=')[0];
                    let value = param.split('=')[1];
                    params[key] = value ? decodeURIComponent(value.replace(/\+/g, ' ')) : '';
                    return params;
                }, {}
            )
        : {}
}
</script>

</div>
<div id="message" class="search-message"></div>
<div id="paginator" class="pagination"></div>


    




<script src="https://unpkg.com/@popperjs/core@2"></script>
<script type="text/javascript">
    function search_page_sort_offers(offers){
      return offers.sort(function(a, b) {
        var a_seller_name = (a.seller_name ? a.seller_name : a.seller.name),
        b_seller_name = (b.seller_name ? b.seller_name : b.seller.name);
        if(a.seller_name){

        }
        return a_seller_name > b_seller_name ? 1 : -1;
      });
    }

    qs = window.location.search.substring(1);
    qs_pagination = qs.replace(/\&page=.*(?=\&)|\&page=.*(?=$)/g, '');

    var sample_icon = 'N/A';
    var sample_inline_icon = 'N/A';
    var resultParts = [];
    var tiBanner = false;
    var ADBanner = false;
    var adSearchResult = null;
    var doneWaiting = false;
    var doc = document;
    var a_b_version = 'b'
    var description_shown = 'default description';
    var download_button_position = 'right';
    var buy_buttom = 'False';
    const ASYNC_ADS = false;
    let searchEndpoint = 'search_basic';
    let searchLen = 0;
    if (!ASYNC_ADS){
        searchEndpoint = 'search_local';
    }

    function formatDataManufacturer(manufacturer) {
      let formattedName = manufacturer.replace(/\s+/g, '_').toLowerCase();
      formattedName = formattedName.replace(/[.,]/g, '');
      return formattedName;
    }

    function setAds(searchresult, resultCheckInterval, request_id) {
        if (doneWaiting){
            clearInterval(resultCheckInterval);
        }
        else {
            return
        }

        const enable_dual_topline = searchresult.ads && Array.isArray(searchresult.ads) && searchresult.ads.length == 3;

        if (searchresult.featured != null ) {
            clearInterval(resultCheckInterval);
          if (typeof searchresult.featured['buyURL'] !== 'undefined' ) {
              if (searchresult.featured['buyURL'] != 'N/A'){
                var part = searchresult.featured;
                var part_data = {
                  part_number: part['name'],
                  manufacturer: part['manufacturer'],
                  unique_id: part['uniqueid'],
                  unipart_id: part['unipart_id'],
                  has_datasheet: part['has_datasheet'],
                  has_unipart: true,
                  row_counter: i,
                  sample_current_status: part['has_sample'],
                };

                var availability, averagePrice;

                $.ajax({
                    url: '/api/get_more_resources/',
                    dataType: 'json',
                    data: part_data,
                    headers: { 'X-SNAP-REQUEST-ID': request_id },
                    success: function(json) {
                        availability = String(json.availability);
                        averagePrice = String(json.average_price);

                        var has_datasheet = String(json.has_datasheet);
                        var total = 0;
                        var num_items = 0;
                        var contact_us_available = false;
                        if(json.sample_status === 'available' && searchresult.featured['has_sample'] == 'AJAX'){
                              $('.featured_data_icons img.sample').attr('src', "/static/img/search_icons/sample_orange.png");
                              $('.featured_data_icons img.sample').attr('title', 'Sample available');
                              $('.featured_data_icons img.sample').attr('alt', 'Sample available');
                              $('.ad_view #free-sample').show();
                              if (searchresult.featured && searchresult.featured['high_runner'] === 'TRUE') {
                                $('.ad_view #free-sample').hide();
                              }

                              if ('component_carrier' in searchresult.featured && searchresult.featured['component_carrier']) {
                                $('.ad_view #free-sample').hide();
                              }
                        }
                        if(json.offers && json.offers.length > 0) {
                          searchresult.featured.offers = json.offers
                          json.offers.forEach(function(offer){
                            if (averagePrice == 'undefined' || averagePrice == 'N/A' || !averagePrice) {
                              if (offer['price']) {
                                if(offer['price']['USD'].length){
                                    for (price in offer['price']['USD']){
                                      total += Number(offer['price']['USD'][price][1])
                                      num_items += 1
                                    }
                                }
                              }else{
                                  if(offer['prices'].length){
                                      for (price in offer['prices']){
                                        total += Number(offer['prices'][price][1])
                                        num_items += 1
                                      }
                                    }
                                  }
                            }
                            if(offer && offer.quantity_available && offer.quantity_available.toLowerCase() == "contact us"){
                              contact_us_available=true;
                            }
                          });
                          if (num_items != 0) {
                            var avg = total / num_items
                            availability = 'Available'
                            averagePrice = avg;
                          }
                        } else {
                          availability = 'Unavailable'
                          averagePrice = 'N/A';
                        }

                        if(contact_us_available){
                            availability = 'Available'
                          }

                        if (averagePrice == 'undefined' && !offers.length) {
                          averagePrice = 'N/A';
                        }
                        var price = searchresult.featured['price'];
                        if (typeof price !== 'undefined' && typeof price != 'string'){
                          price = price;
                        }

                        if (price == 'N/A' || !price){
                          averagePrice = '' + averagePrice;
                          if(averagePrice !== 'N/A' && averagePrice){
                            let currency = '$';
                            let currency_info = 'USD';
                            if (averagePrice.indexOf('€') !== -1) {
                              currency_info = 'EUR';
                            } else if (averagePrice.indexOf('$') !== -1) {
                              currency_info = 'USD';
                            } else {
                              averagePrice = '$' + averagePrice;
                              currency_info = 'USD';
                            }
                            $('.featured_price .price').text(averagePrice);
                            $('.featured_price .price_currency').text(currency_info);
                          }else{
                          $('.featured_price').hide();
                          }
                        }else{
                          $('.featured_price .price').text(price);
                        }
                        if (searchresult.featured['quantity'] == 'Not in stock' && availability == 'Available'){
                          searchresult.featured['quantity'] = 'In Stock'
                        }
                        if (parseInt(searchresult.featured['quantity']) == 0){
                          $('.featured_price .quantity_text').text("on order");
                          $('.featured_price .quantity').text(numberWithCommas(searchresult.featured['quantity_on_order']));
                        }else{
                          $('.featured_price .quantity').text(numberWithCommas(searchresult.featured['quantity']));
                        }

                        if (searchresult.featured['type'] == 'SAMTEC'){
                          if (searchresult.featured['quantity'] == 'Not in stock'){
                            $('.avail_sect').hide();
                          }
                        }
                    }
                });

              var ref = research_feature_ref(searchresult.featured['type'], searchresult.featured['ref'], false);
              var query_encoded = encodeURIComponent('APX803L20\u002D30SA\u002D7');
              var part_url = '/parts/' + searchresult.featured['urlname'] + '/' + searchresult.featured['manufacturer'] + '/view-part/?ref=' + ref + '&t=APX803L20-30SA-7' + '&con_ref=None';

              if (searchresult.featured['auction_id'] !== undefined) {
                 part_url = part_url + '&auction_id=' +  searchresult.featured['auction_id'] + '&ad_ab_version=' + searchresult.featured['ad_ab_version'];
              }
              // Add ab_test_case to the URL
              if (typeof searchresult !== 'undefined' && searchresult &&
                  typeof searchresult.search_ad_props !== 'undefined' && searchresult.search_ad_props &&
                  typeof searchresult.search_ad_props.ab_test_case !== 'undefined' && searchresult.search_ad_props.ab_test_case) {
                  part_url = part_url + '&ab_test_case=' + searchresult.search_ad_props.ab_test_case;
              }

              $('.featured_title').text(searchresult.featured['name']).attr('href', part_url);
              $('.featured_desc').html(searchresult.featured['description']);

              //hide the samtec availability when TE part is shown
              if (searchresult.featured['type'] == 'TE'){
                $('.featured_buttons .featured_data_icons p').hide();
              }
              var mobilesponsor = document.createElement("div");
              var mobilesponsoricon = document.createElement("i");
              mobilesponsoricon.className='fa fa-info';
              mobilesponsor.className='mobile_sponsor';
              mobilesponsor.appendChild(mobilesponsoricon);
              mobilesponsor.appendChild(doc.createTextNode(' Sponsored '));
              $('.featured_part').prepend(mobilesponsor);

              $('.featured_part .featured_img a img').attr('src',searchresult.featured['image']);
              $('.featured_part .featured_img a img').on("error", function () {
                    $('.featured_part .featured_img a img').hide();
                    getFallbackImage(searchresult.featured['name'], searchresult.featured['manufacturer'], function(imgUrl){
                        $('.featured_part .featured_img a').html('<img src="' +imgUrl +'" alt="featured"/>');
                    })
              });

              $('.featured_manuf').html("by " + searchresult.featured['manufacturer']);


              $('.featured_part .manuf_img img').attr('src',searchresult.featured['organization_image_100_20']);
              $('.featured_part .manuf_img img').on("error", function () {
                  console.log("img error");
                  $('.featured_part .manuf_img img').hide();
              });

              // is this ad for mouser or digikey
              if (enable_dual_topline) {

                var left_ad = searchresult.ads[0];
                var right_ad = searchresult.ads[1];

                $('.featured_part').each(function () {
                    this.style.setProperty( 'display', 'block', 'important' );
                });

                $('.featured_part.dk_ad').remove();
                $('.featured_part.mouser_ad2').remove();
                $('.featured_part.te_ad').remove();

                function generatePartURL(part) {
                  let generated_url = '/parts/' + part['urlname'] + '/' + part['manufacturer'] + '/view-part/?ref=' + ref + '&t=APX803L20-30SA-7' + '&con_ref=None';
                  if (part['auction_id'] !== undefined) {
                    generated_url = generated_url + '&auction_id=' +  part['auction_id'] + '&ad_ab_version=' + part['ad_ab_version'];
                  }
                  // Add ab_test_case to the URL
                  if (typeof searchresult !== 'undefined' && searchresult &&
                      typeof searchresult.search_ad_props !== 'undefined' && searchresult.search_ad_props &&
                      typeof searchresult.search_ad_props.ab_test_case !== 'undefined' && searchresult.search_ad_props.ab_test_case) {
                      generated_url = generated_url + '&ab_test_case=' + searchresult.search_ad_props.ab_test_case;
                  }
                  return generated_url;
                }

                $('#left_ad_view .manuf_img img').attr('src', left_ad['organization_image_100_20']);
                $('#left_ad_view .featured_img a').attr('href', generatePartURL({urlname: left_ad['urlname'], manufacturer: left_ad['manufacturer'], auction_id: left_ad['auction_id'], ad_ab_version: left_ad['ad_ab_version']}));
                $('#left_ad_view .featured_img a img').attr('src', left_ad['image']);
                $('#left_ad_view .featured_title').text(left_ad['name']).attr('href', generatePartURL({urlname: left_ad['urlname'], manufacturer: left_ad['manufacturer'], auction_id: left_ad['auction_id'], ad_ab_version: left_ad['ad_ab_version']}));
                $('#left_ad_view .featured_manuf').html("by " + left_ad['manufacturer']);
                $('#left_ad_view .featured_desc').html(left_ad['description']);
                $('#left_ad_view .featured_buttons .download').attr('href', generatePartURL({urlname: left_ad['urlname'], manufacturer: left_ad['manufacturer'], auction_id: left_ad['auction_id'], ad_ab_version: left_ad['ad_ab_version']}));

                $('#right_ad_view .manuf_img img').attr('src', right_ad['organization_image_100_20']);
                $('#right_ad_view .featured_img a').attr('href', generatePartURL({urlname: right_ad['urlname'], manufacturer: right_ad['manufacturer'], auction_id: right_ad['auction_id'], ad_ab_version: right_ad['ad_ab_version']}));
                $('#right_ad_view .featured_img a img').attr('src', right_ad['image']);
                $('#right_ad_view .featured_title').text(right_ad['name']).attr('href', generatePartURL({urlname: right_ad['urlname'], manufacturer: right_ad['manufacturer'], auction_id: right_ad['auction_id'], ad_ab_version: right_ad['ad_ab_version']}));
                $('#right_ad_view .featured_manuf').html("by " + right_ad['manufacturer']);
                $('#right_ad_view .featured_desc').html(right_ad['description']);
                $('#right_ad_view .featured_buttons .download').attr('href', generatePartURL({urlname: right_ad['urlname'], manufacturer: right_ad['manufacturer'], auction_id: right_ad['auction_id'], ad_ab_version: right_ad['ad_ab_version']}));

                // left_ad and right_ad has the same type and manufacturer
                $('.ad_view').attr('data-type', left_ad['type']);
                $('div.ad_buy').attr('data-type', left_ad['type']);
                $('div.ad_buy').attr('data-manufacturer', formatDataManufacturer(left_ad['manufacturer']));
                $('div.ad_buy').addClass('manuf_' + formatDataManufacturer(left_ad['manufacturer']));
              } else if (searchresult.featured['type'] == 'digikey') {
                $('.featured_part').each(function () {
                    this.style.setProperty( 'display', 'block', 'important' );
                });
                $('.featured_part.mouser_ad2').remove();
                $('.featured_part.te_ad').remove();
                $('.featured_part.top_line_ad').remove();

                $('.featured_content .ver3_img').attr('src', "/static/img/digikey_logo_80.svg");
                $('.featured_content .ver3_img').attr('alt', 'Buy now from DigiKey');
                $('.featured_content .buy_title').html(getWording('DigiKey'));
                $('.featured_content a.buy').html("Check availability");
                $('.ad_view').attr('data-type','DigiKey');
                $('div.ad_buy').attr('data-type','DigiKey');
                $('div.ad_buy').attr('data-manufacturer', formatDataManufacturer(searchresult.featured['manufacturer']));
                $('div.ad_buy').addClass('manuf_' + formatDataManufacturer(searchresult.featured['manufacturer']));

                // DIGIKEY TRACKING 1x1 PIXEL setup, when URLs were updated the actual implementation of this, did not
                // have any changes.
                var link_href = "https://ad.doubleclick.net/ddm/trackclk/N4481.2579422SNAPEDA/B4652602.307340238;dc_trk_aid=500214202;dc_trk_cid=102628001;dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;tfua=;ltd=";
                var img_src = "https://ad.doubleclick.net/ddm/trackimp/N4481.2579422SNAPEDA/B4652602.307340238;dc_trk_aid=500214202;dc_trk_cid=102628001;ord=1756068672;dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;tfua=;ltd=?"

                $('.digikey_tracking').attr('href',link_href);
                $('.digikey_tracking_img').attr('src', img_src);
              } else if (searchresult.featured['type'] == 'rs components') {
                $('.featured_part').each(function () {
                    this.style.setProperty( 'display', 'block', 'important' );
                });
                $('.featured_part.mouser_ad2').remove();
                $('.featured_part.te_ad').remove();
                $('.featured_part.top_line_ad').remove();
                $('.featured_content .ver3_img').attr('src', "https://snapeda.s3.amazonaws.com/static/img/rs_components_logo.png");
                $('.featured_content .ver3_img').attr('alt', 'Buy now from RS Components');
                $('.featured_content .buy_title').html(getWording('RS Components'));
                $('.featured_content a.buy').html("Check Prices Now!");
                $('.ad_view').attr('data-type','RS Components');
                $('div.ad_buy').attr('data-type','RS Components');
                $('div.ad_buy').attr('data-manufacturer', formatDataManufacturer(searchresult.featured['manufacturer']));
                $('div.ad_buy').addClass('manuf_' + formatDataManufacturer(searchresult.featured['manufacturer']));

                $('.featured_part .ver3_img').css('height', '70px');
                $('.featured_part .ver3_img').css('margin-top', '35px');

                try {
                  if(searchresult.featured['buyURL'].indexOf('us.rs-online.com') === -1) {
                    $('.price_currency').html('GBP');
                    $('.featured_price.featured_price_alt')[0].childNodes[0].nodeValue = $('.featured_price.featured_price_alt')[0].childNodes[0].nodeValue.replace('$', '€');
                  } else {
                    $('.price_currency').html('USD');
                    $('.featured_price.featured_price_alt')[0].childNodes[0].nodeValue = $('.featured_price.featured_price_alt')[0].childNodes[0].nodeValue.replace('$', '$');
                  }
                } catch (e) {}

                $('.digikey_tracking').hide();
              } else if (searchresult.featured['type'] == 'winsource electronics') {
                $('.featured_part').each(function () {
                    this.style.setProperty( 'display', 'block', 'important' );
                });
                $('.featured_part.mouser_ad2').remove();
                $('.featured_part.te_ad').remove();
                $('.featured_part.top_line_ad').remove();
                $('.featured_content .ver3_img').attr('src', "https://snapeda.s3.amazonaws.com/static/img/winsource_logo.webp");
                $('.featured_content .ver3_img').attr('alt', 'Buy now from Win Source');
                $('.featured_content .buy_title').html(getWording('Win Source'));
                $('.featured_content a.buy').html("Check availability");
                $('.ad_view').attr('data-type','WinSource Electronics');
                $('div.ad_buy').attr('data-type','WinSource Electronics');
                $('div.ad_buy').attr('data-manufacturer', formatDataManufacturer(searchresult.featured['manufacturer']));
                $('div.ad_buy').addClass('manuf_' + formatDataManufacturer(searchresult.featured['manufacturer']));

                $('.digikey_tracking').hide();
              } else if (searchresult.featured['type'] == 'mouser') {
                $('.featured_part').each(function () {
                    this.style.setProperty( 'display', 'block', 'important' );
                });

                $('.featured_part.te_ad').remove();
                $('.featured_part.top_line_ad').remove();

                $('.featured_content .ver3_img').attr('src', 'https://s3-us-west-2.amazonaws.com/snapeda-static/images/mouser_logo.png');
                $('.featured_content .ver3_img').attr('alt', 'Buy now from Mouser');
                $('.featured_content .buy_title').html(getWording('Mouser'));
                $('.featured_content a.buy').html("Check availability");
                $('.ad_view').attr('data-type','Mouser');
                $('div.ad_buy').attr('data-type','Mouser');
                $('div.ad_buy').attr('data-manufacturer', formatDataManufacturer(searchresult.featured['manufacturer']));
                $('div.ad_buy').addClass('manuf_' + formatDataManufacturer(searchresult.featured['manufacturer']));

                // Mouser colors
                $(".ad_buy").addClass('mouser_color');
                $("a.digikey_buy").addClass('mouser_color');
                $('.sponsored_indicator').addClass('mouser_color');

                $('.digikey_tracking').hide();

              } else if (searchresult.featured['type'] == 'arrow') {
                $('.featured_part').each(function () {
                    this.style.setProperty( 'display', 'block', 'important' );
                });

                $('.featured_part.te_ad').remove();
                $('.featured_part.mouser_ad2').remove();
                $('.featured_part.top_line_ad').remove();
                $('.featured_content .ver3_img').attr('src', 'https://s3-us-west-2.amazonaws.com/snapeda-static/pinax/images/company_logos/arrow_logo.gif');
                $('.featured_content .ver3_img').attr('alt', 'Buy now from Arrow');
                $('.featured_content .buy_title').html('Shop this part on Arrow');
                $('.featured_content a.buy').html("Check availability");
                $('.ad_view').attr('data-type','Arrow');
                $('div.ad_buy').attr('data-type','Arrow');
                $('div.ad_buy').attr('data-manufacturer', formatDataManufacturer(searchresult.featured['manufacturer']));
                $('div.ad_buy').addClass('manuf_' + formatDataManufacturer(searchresult.featured['manufacturer']));

                $('.digikey_tracking').hide();

              }

              if (!enable_dual_topline && ['digikey', 'mouser', 'arrow', 'rs components', 'winsource electronics'].indexOf(searchresult.featured['type']) === -1){
                $('.featured_part').each(function () {
                    this.style.setProperty( 'display', 'block', 'important' );
                });
                $('.featured_part.mouser_ad2').remove();
                $('.featured_part.dk_ad').remove();
                $('.featured_part.top_line_ad').remove();

                let buy_from_text = "Buy From " + searchresult.featured['type'];
                if (searchresult.featured['type'] === 'TEXAS'){
                    buy_from_text = "Buy on TI.com"
                    $('.featured_buttons .ad_buy').css('padding','5px 15px');
                }
                $('.ad_buy').html(buy_from_text);
                if (searchresult.featured['type'] === 'HARTING'){
                  $('.inline_buttons .ad_buy').hide()
                  $('.featured_buttons .ad_buy').hide()
                }
                $('.ad_view').attr('data-type', searchresult.featured['type']);
                $('.ad_view').attr('data-manufacturer', formatDataManufacturer(searchresult.featured['manufacturer']));
                $('.ad_view').addClass('manuf_' + formatDataManufacturer(searchresult.featured['manufacturer']));
                $('.featured_part a.ad_buy').attr('data-type', searchresult.featured['type']);

                $('.digikey_tracking').hide();
              }

              if(searchresult.featured['has_symbol'] == 'AJAX' && !enable_dual_topline) {
                var part_data = {
                    part_number: searchresult.featured['name'],
                    manufacturer: searchresult.featured['manufacturer'],
                    unique_id : searchresult.featured['uniqueid'],
                    unipart_id: searchresult.featured['unipart_id'],
                    pin_count: searchresult.featured['pin_count'],
                    has_datasheet: searchresult.featured['has_datasheet'],
                    package_type: searchresult.featured['package_type'],
                    row_counter: i,
                };

                $.ajax({
                    url: "/api/get_has_sym_foot_3d/",
                    data: part_data,
                    headers: { 'X-SNAP-REQUEST-ID': request_id },
                    success: function(json) {
                      var has_datasheet = Number(json.has_datasheet);
                      var has_symbol = Number(json.has_symbol);
                      var has_footprint = Number(json.has_footprint);
                      var has_3d = Number(json.has_3d);
                      var has_sim = Number(json.has_sim);
                      var has_sample = Number(json.has_sample);

                      if (has_datasheet) {
                        var datasheet_icon = document.getobjectbyid;
                        $('.featured_data_icons img.datasheet').attr('src', "/static/img/search_icons/datasheet_orange.png");
                        $('.featured_data_icons img.datasheet').attr('title', 'Datasheet available');
                        $('.featured_data_icons img.datasheet').attr('alt', 'Datasheet available');
                      }
                      if (has_symbol) {
                        $('.featured_data_icons img.symbol').attr('src', "/static/img/search_icons/symbol_orange.png");
                        $('.featured_data_icons img.symbol').attr('title', 'Symbol available');
                        $('.featured_data_icons img.symbol').attr('alt', 'Symbol available');
                      }
                      if (has_footprint) {
                        $('.featured_data_icons img.footprint').attr('src', "/static/img/search_icons/footprint_orange.png");
                        $('.featured_data_icons img.footprint').attr('title', 'Footprint available');
                        $('.featured_data_icons img.footprint').attr('alt', 'Footprint available');
                      }
                      if (has_3d) {
                        $('.featured_data_icons img.3d').attr('src', "/static/img/search_icons/3d_model_orange.png");
                        $('.featured_data_icons img.3d').attr('title', '3D model available');
                        $('.featured_data_icons img.3d').attr('alt', '3D model available');
                      }if (has_sim){
                        $('.featured_data_icons img.sim').attr('src', "/static/img/search_icons/sim_orange.png");
                        $('.featured_data_icons img.sim').attr('title', 'Sim model available');
                        $('.featured_data_icons img.sim').attr('alt', 'Sim model available');
                      }if (has_sample){
                          $('.featured_data_icons img.sample').attr('src', "/static/img/search_icons/sample_orange.png");
                          $('.featured_data_icons img.sample').attr('title', 'Sample available');
                          $('.featured_data_icons img.sample').attr('alt', 'Sample available');
                          // if(a_b_version == 1 || a_b_version == 2) {
                            sample_icon = "/static/img/search_icons/sample_orange.png";
                          // }
                        }
                    }
                })
              } else if (enable_dual_topline) {
                let left_ad = searchresult.ads[0];
                let right_ad = searchresult.ads[2];

                if ((left_ad['has_datasheet']) == '1') {
                  $('#left_ad_view .featured_data_icons img.datasheet').attr('src', "/static/img/search_icons/datasheet_orange.png");
                  $('#left_ad_view .featured_data_icons img.datasheet').attr('title', 'Datasheet available');
                  $('#left_ad_view .featured_data_icons img.datasheet').attr('alt', 'Datasheet available');
                }
                if ((left_ad['has_symbol']) == '1') {
                  $('#left_ad_view .featured_data_icons img.symbol').attr('src', "/static/img/search_icons/symbol_orange.png");
                  $('#left_ad_view .featured_data_icons img.symbol').attr('title', 'Symbol available');
                  $('#left_ad_view .featured_data_icons img.symbol').attr('alt', 'Symbol available');
                }
                if ((left_ad['has_footprint']) == '1') {
                  $('#left_ad_view .featured_data_icons img.footprint').attr('src', "/static/img/search_icons/footprint_orange.png");
                  $('#left_ad_view .featured_data_icons img.footprint').attr('title', 'Footprint available');
                  $('#left_ad_view .featured_data_icons img.footprint').attr('alt', 'Footprint available');
                }
                if ((left_ad['has_3d']) == '1') {
                  $('#left_ad_view .featured_data_icons img.3d').attr('src', "/static/img/search_icons/3d_model_orange.png");
                  $('#left_ad_view .featured_data_icons img.3d').attr('title', '3D model available');
                  $('#left_ad_view .featured_data_icons img.3d').attr('alt', '3D model available');
                }
                if ((left_ad['has_sim']) == '1'){
                    $('#left_ad_view .featured_data_icons img.sim').attr('src', "/static/img/search_icons/sim_orange.png");
                    $('#left_ad_view .featured_data_icons img.sim').attr('title', 'Sim model available');
                    $('#left_ad_view .featured_data_icons img.sim').attr('alt', 'Sim model available');
                  }

                if(left_ad['has_sample'] && left_ad['has_sample'] != 'AJAX'){
                      $('#left_ad_view .featured_data_icons img.sample').attr('src', "/static/img/search_icons/sample_orange.png");
                      $('#left_ad_view .featured_data_icons img.sample').attr('title', 'Sample available');
                      $('#left_ad_view .featured_data_icons img.sample').attr('alt', 'Sample available');
                      // if(a_b_version == 1 || a_b_version == 2) {
                        sample_icon = "/static/img/search_icons/sample_orange.png";
                      // }
                }

                if ((right_ad['has_datasheet']) == '1') {
                  $('#right_ad_view .featured_data_icons img.datasheet').attr('src', "/static/img/search_icons/datasheet_orange.png");
                  $('#right_ad_view .featured_data_icons img.datasheet').attr('title', 'Datasheet available');
                  $('#right_ad_view .featured_data_icons img.datasheet').attr('alt', 'Datasheet available');
                }
                if ((right_ad['has_symbol']) == '1') {
                  $('#right_ad_view .featured_data_icons img.symbol').attr('src', "/static/img/search_icons/symbol_orange.png");
                  $('#right_ad_view .featured_data_icons img.symbol').attr('title', 'Symbol available');
                  $('#right_ad_view .featured_data_icons img.symbol').attr('alt', 'Symbol available');
                }
                if ((right_ad['has_footprint']) == '1') {
                  $('#right_ad_view .featured_data_icons img.footprint').attr('src', "/static/img/search_icons/footprint_orange.png");
                  $('#right_ad_view .featured_data_icons img.footprint').attr('title', 'Footprint available');
                  $('#right_ad_view .featured_data_icons img.footprint').attr('alt', 'Footprint available');
                }
                if ((right_ad['has_3d']) == '1') {
                  $('#right_ad_view .featured_data_icons img.3d').attr('src', "/static/img/search_icons/3d_model_orange.png");
                  $('#right_ad_view .featured_data_icons img.3d').attr('title', '3D model available');
                  $('#right_ad_view .featured_data_icons img.3d').attr('alt', '3D model available');
                }
                if ((right_ad['has_sim']) == '1'){
                    $('#right_ad_view .featured_data_icons img.sim').attr('src', "/static/img/search_icons/sim_orange.png");
                    $('#right_ad_view .featured_data_icons img.sim').attr('title', 'Sim model available');
                    $('#right_ad_view .featured_data_icons img.sim').attr('alt', 'Sim model available');
                  }

                if(right_ad['has_sample'] && right_ad['has_sample'] != 'AJAX'){
                      $('#right_ad_view .featured_data_icons img.sample').attr('src', "/static/img/search_icons/sample_orange.png");
                      $('#right_ad_view .featured_data_icons img.sample').attr('title', 'Sample available');
                      $('#right_ad_view .featured_data_icons img.sample').attr('alt', 'Sample available');
                      // if(a_b_version == 1 || a_b_version == 2) {
                        sample_icon = "/static/img/search_icons/sample_orange.png";
                      // }
                }
              } else {
                if ((searchresult.featured['has_datasheet']) == '1') {
                  $('.featured_data_icons img.datasheet').attr('src', "/static/img/search_icons/datasheet_orange.png");
                  $('.featured_data_icons img.datasheet').attr('title', 'Datasheet available');
                  $('.featured_data_icons img.datasheet').attr('alt', 'Datasheet available');
                }
                if ((searchresult.featured['has_symbol']) == '1') {
                  $('.featured_data_icons img.symbol').attr('src', "/static/img/search_icons/symbol_orange.png");
                  $('.featured_data_icons img.symbol').attr('title', 'Symbol available');
                  $('.featured_data_icons img.symbol').attr('alt', 'Symbol available');
                }
                if ((searchresult.featured['has_footprint']) == '1') {
                  $('.featured_data_icons img.footprint').attr('src', "/static/img/search_icons/footprint_orange.png");
                  $('.featured_data_icons img.footprint').attr('title', 'Footprint available');
                  $('.featured_data_icons img.footprint').attr('alt', 'Footprint available');
                }
                if ((searchresult.featured['has_3d']) == '1') {
                  $('.featured_data_icons img.3d').attr('src', "/static/img/search_icons/3d_model_orange.png");
                  $('.featured_data_icons img.3d').attr('title', '3D model available');
                  $('.featured_data_icons img.3d').attr('alt', '3D model available');
                }
                if ((searchresult.featured['has_sim']) == '1'){
                    $('.featured_data_icons img.sim').attr('src', "/static/img/search_icons/sim_orange.png");
                    $('.featured_data_icons img.sim').attr('title', 'Sim model available');
                    $('.featured_data_icons img.sim').attr('alt', 'Sim model available');
                  }
                // Replaced includes for indexOf for IE compatibility
                var temp_manuf = searchresult.featured['manufacturer'];
                var featured_part = searchresult.featured;

                if(featured_part['has_sample'] && featured_part['has_sample'] != 'AJAX'){
                      $('.featured_data_icons img.sample').attr('src', "/static/img/search_icons/sample_orange.png");
                      $('.featured_data_icons img.sample').attr('title', 'Sample available');
                      $('.featured_data_icons img.sample').attr('alt', 'Sample available');
                      // if(a_b_version == 1 || a_b_version == 2) {
                        sample_icon = "/static/img/search_icons/sample_orange.png";
                      // }
                }
              }

              if (!enable_dual_topline) {
                $('.featured_buttons .download').attr('href', part_url);
                $('.featured_content .download').attr('href', part_url);
              }

              if (searchresult.featured['hasModel']==0) {
                $('.featured_buttons .download').text('View Part');
              }

              
              if (searchresult.featured['hasModel']==0) {
                $('.featured_content .download').text('View Part');
              }

              $('.featured_part').show();
              if (searchresult.inlinefeatured['type'] == 'digikey') {
              $('.digikey_tracking').show();
              }
              var relevancy = 'N/A';

              if(sample_icon !== 'N/A') {
                $('.ad_view #free-sample').show();
              }

              if (searchresult.featured['type'] == 'digikey') {
                var Properties = {
                    'Type': 'Search - Impression',
                    'image': searchresult.featured['image'],
                    'description': searchresult.featured['description'],
                    'stock': searchresult.featured['quantity'],
                    'Ad_type': searchresult.featured_part_version,
                    'Part': searchresult.featured['name'],
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                mixpanel.track('DigiKey', Properties);
                $('.featured_buttons .buy').attr('href', searchresult.featured['buyURL'] + '?utm_source=snapeda&utm_medium=aggregator&utm_campaign=buynow');

                track_db_and_mixpanel('DigiKey', 'Search - Impression', "APX803L20\u002D30SA\u002D7", '', searchresult.featured['name'])
              }

              if (searchresult.featured['type'] == 'winsource electronics') {
                var Properties = {
                    'Type': 'Search - Impression',
                    'image': searchresult.featured['image'],
                    'description': searchresult.featured['description'],
                    'stock': searchresult.featured['quantity'],
                    'ad_version': '1',
                    'Part': searchresult.featured['name'],
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                mixpanel.track('WinSource Electronics', Properties);
                $('.featured_buttons .buy').attr('href', searchresult.featured['buyURL']);
                track_db_and_mixpanel('WinSource Electronics', 'Search - Impression', '', '', searchresult.featured['name']);
              }

              if (searchresult.featured['type'] == 'mouser') {
                var Properties = {
                    'Type': 'Search - Impression',
                    'image': searchresult.featured['image'],
                    'description': searchresult.featured['description'],
                    'stock': searchresult.featured['quantity'],
                    'Part': searchresult.featured['name'],
                    'manufacturer': searchresult.featured['manufacturer'],
                    'ad_branding': 'Mouser',
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                mixpanel.track('Mouser', Properties);
                $('.featured_buttons .buy').attr('href', searchresult.featured['buyURL'] + '&utm_source=snapedaonline&utm_medium=online&utm_campaign=mouser&utm_content=search');

                 track_db_and_mixpanel('Mouser', 'Search - Impression', "APX803L20\u002D30SA\u002D7", '', searchresult.featured['name']);
                // Track Mouser ad impression in DB
                $.ajax({
                  url: '/api/record_mouser_impression_api/search',
                })
                .done(function() {
                  console.log("success");
                })
              }
              if (searchresult.featured['type'] == 'arrow') {
                var Properties = {
                    'Type': 'Search - Impression',
                    'image': searchresult.featured['image'],
                    'description': searchresult.featured['description'],
                    'stock': searchresult.featured['quantity'],
                    'ad_version': '1',
                    'Part': searchresult.featured['name'],
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                mixpanel.track('Arrow', Properties);
                $('.featured_buttons .buy').attr('href', searchresult.featured['buyURL']);
                track_db_and_mixpanel('Arrow', 'Search - Impression', '', '', searchresult.featured['name']);

                // Track Arrow ad impression in DB
                $.ajax({
                  url: '/api/record_ad_impression_api/arrow_search',
                })
                .done(function() {
                  console.log("success");
                })
              }

              if (enable_dual_topline) {
                // record impression left ad
                hide_stock = 'N/A';
                if (typeof left_ad['relevancy'] !== 'undefined' ){
                  relevancy = left_ad['relevancy'];
                }else{
                  relevancy = '';
                }
                ad_version = 'Manufacturer style';
                var Properties = {
                    'Type': 'Search - Impression',
                    'image': left_ad['image'],
                    'description': left_ad['short_description'],
                    'stock': left_ad['quantity'],
                    'ad_version': ad_version,
                    'Search Term': 'APX803L20\u002D30SA\u002D7',
                    'userid': '',
                    'relevancy': relevancy,
                    'hide_stock': hide_stock,
                    'Part': left_ad['name'],
                    'description_shown': description_shown,
                    'download_button_position': download_button_position,
                    'buy_buttom': buy_buttom,
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'is_xref': left_ad['is_xref'] || false,
                    'high_runner': left_ad && left_ad['high_runner'] === 'TRUE',
                    'ad_ab_version': left_ad['ad_ab_version'] || 'a',
                    'auction_id': left_ad['auction_id'] ? left_ad['auction_id'] + '' : 'NA',
                    'search_type': left_ad['search_type'] || '',
                    'ad_keywords': left_ad['ad_keywords'] || '',
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                Properties['Part Image'] = 'component';
                mixpanel.track(left_ad['type'], Properties);
                track_db_and_mixpanel(left_ad['type'], 'Search - Impression', 'APX803L20\u002D30SA\u002D7', relevancy, left_ad['name'], false, left_ad['ad_ab_version'], left_ad['auction_id'])
                // record impression right ad
                hide_stock = 'N/A';
                if (typeof right_ad['relevancy'] !== 'undefined' ){
                  relevancy = right_ad['relevancy'];
                }else{
                  relevancy = '';
                }
                var Properties = {
                    'Type': 'Search - Impression',
                    'image': right_ad['image'],
                    'description': right_ad['short_description'],
                    'stock': right_ad['quantity'],
                    'ad_version': ad_version,
                    'Search Term': 'APX803L20\u002D30SA\u002D7',
                    'userid': '',
                    'relevancy': relevancy,
                    'hide_stock': hide_stock,
                    'Part': right_ad['name'],
                    'description_shown': description_shown,
                    'download_button_position': download_button_position,
                    'buy_buttom': buy_buttom,
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'is_xref': right_ad['is_xref'] || false,
                    'high_runner': right_ad && right_ad['high_runner'] === 'TRUE',
                    'ad_ab_version': right_ad['ad_ab_version'] || 'a',
                    'auction_id': right_ad['auction_id'] ? right_ad['auction_id'] + '' : 'NA',
                    'search_type': right_ad['search_type'] || '',
                    'ad_keywords': right_ad['ad_keywords'] || '',
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                Properties['Part Image'] = 'component';
                mixpanel.track(right_ad['type'], Properties);
                track_db_and_mixpanel(right_ad['type'], 'Search - Impression', 'APX803L20\u002D30SA\u002D7', relevancy, right_ad['name'], false, right_ad['ad_ab_version'], right_ad['auction_id'])
              }

              if (!enable_dual_topline && ['digikey', 'mouser', 'arrow', 'winsource electronics'].indexOf(searchresult.featured['type']) === -1) {

                if (searchresult.featured['buyURL'] == 'Hide' ){
                  $('.featured_buttons .buy').remove();
                  $('.ad_view_left').show();
                  download_button_position = 'left';
                  buy_buttom = 'False';
                }else{
                  $('.featured_buttons .buy').attr('href', searchresult.featured['buyURL']);
                   if(searchresult.featured['type'] == 'rs components') {
                      window.isRSComponents = true;
                      let numberOfSkus = 0;
                      window.numberOfSkus = 0;
                      if (searchresult && searchresult.featured && 'num_of_skus' in searchresult.featured) {
                          numberOfSkus = parseInt(searchresult.featured['num_of_skus']);
                          window.numberOfSkus = numberOfSkus;
                      }
                      const showPricingModal = numberOfSkus > 1;
                      if(showPricingModal) {
                        $('.featured_buttons .buy, .ad_buy').removeAttr('href');
                        $('.ad_buy').click(function() {
                          filteredOffers = []
                          searchresult.featured.offers.forEach(function(offer){
                            if( (offer.seller && offer.seller && offer.seller.name && (offer.seller.name.toLowerCase() == 'rs components' || offer.seller.name.toLowerCase() == 'rs americas')) || (offer.seller_name && (offer.seller_name.toLowerCase() == 'rs components' || offer.seller_name.toLowerCase() == 'rs americas'))  ) {
                              filteredOffers.push(offer)
                            }
                          });
                          searchresult.featured.offers = filteredOffers
                          openModal(searchresult.featured);
                        });
                      } else {
                        $(".ad_buy").click(function() {
                          let distributor_url = $(this).find(".buy").attr("href");
                          if (distributor_url !== undefined) {
                              window.open(distributor_url, '_blank');
                          }
                        });
                      }
                  }
                  buy_buttom = 'True';
                }

                if (typeof searchresult.featured['relevancy'] !== 'undefined' ){
                  relevancy = searchresult.featured['relevancy'];
                }else{
                  relevancy = '';
                }

                ad_version = 'Manufacturer style';
                hide_stock = 'N/A';
                if (searchresult.featured['type'] == 'SAMTEC'){
                  $('.featured_buttons .download').addClass('ver2');
                  ad_version = 'Distributor style';
                  if (searchresult.featured['quantity'] == 'Not in stock'){
                    hide_stock = true;
                  }
                }

                var Properties = {
                    'Type': 'Search - Impression',
                    'image': searchresult.featured['image'],
                    'description': searchresult.featured['description'],
                    'stock': searchresult.featured['quantity'],
                    'ad_version': ad_version,
                    'Search Term': 'APX803L20\u002D30SA\u002D7',
                    'userid': '',
                    'relevancy': relevancy,
                    'hide_stock': hide_stock,
                    'Part': searchresult.featured['name'],
                    'manufacturer': searchresult.featured['manufacturer'],
                    'description_shown': description_shown,
                    'download_button_position': download_button_position,
                    'buy_buttom': buy_buttom,
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'is_xref': searchresult.featured['is_xref'] || false,
                    'high_runner': searchresult.featured && searchresult.featured['high_runner'] === 'TRUE',
                    'ad_ab_version': searchresult.featured['ad_ab_version'] || 'a',
                    'auction_id': searchresult.featured['auction_id'] ? searchresult.featured['auction_id'] + '' : 'NA',
                    'search_type': searchresult.featured['search_type'] || '',
                    'ad_keywords': searchresult.featured['ad_keywords'] || '',
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                Properties['Part Image'] = 'component';
                mixpanel.track(searchresult.featured['type'], Properties);
                track_db_and_mixpanel(searchresult.featured['type'], 'Search - Impression', 'APX803L20\u002D30SA\u002D7', relevancy, searchresult.featured['name'], false, searchresult.featured['ad_ab_version'], searchresult.featured['auction_id'])

                var featuredUnipartId = '';
                var inlineUnipartId = '';
                if (searchresult.featured) {
                  featuredUnipartId = searchresult.featured['unipart_id'];
                  // Brist ad impression tracking | untracked digikey, mouser, arrow
                  if (featuredUnipartId !== undefined && searchresult.featured['ad_ab_source'] === 'brist') {
                    // Hack to pass featured data
                    window.searchFeatured = {
                      'unipart_id': featuredUnipartId,
                      'request_id': request_id,
                      'ad_ab_version': searchresult.featured['ad_ab_version'],
                      'auction_id': searchresult.featured['auction_id'],
                      'search_type': searchresult.featured['search_type'] || '',
                      'ad_keywords': searchresult.featured['ad_keywords'] || ''
                    }
                    recordAdEvent('IMPRESSION_DATA_TYPE', featuredUnipartId + '', searchresult.featured['auction_id']);
                  }
                }
                if (searchresult.inlinefeatured) {
                  inlineUnipartId = searchresult.inlinefeatured['unipart_id'];
                  // Brist ad impression tracking | untracked digikey, mouser, arrow
                  if (searchresult.show_inline_ad !== undefined && searchresult.show_inline_ad && inlineUnipartId !== undefined && searchresult.inlinefeatured['ad_ab_source'] === 'brist') {
                    recordAdEvent('IMPRESSION_DATA_TYPE', inlineUnipartId + '', searchresult.inlinefeatured['auction_id']);
                  }
                }
                getRelevantSearchCarousel(featuredUnipartId, inlineUnipartId, searchresult.es_query, request_id);
              }

              var featured_type_name = searchresult.featured['type'],
              custom_double_ad_types = ['TEXAS'];
              if($.inArray(featured_type_name, custom_double_ad_types) > -1){

                var continue_custom_double_ad = true,
                ad_container = $('#inline_ad_template');
                ad_container.removeClass('hidden');

                var col_1_data = {
                  "manuf__name": searchresult.featured['manufacturer'],
                  "coverart": searchresult.featured['image'],
                  "part": searchresult.featured['name'],
                  "shortdesc": searchresult.featured['short_description'],
                  "description": searchresult.featured['description'],
                  "unipart_url": part_url,
                  'has_datasheet' : searchresult.featured['has_datasheet'],
                  'has_symbol' : searchresult.featured['has_symbol'],
                  'has_3d' : searchresult.featured['has_3d'],
                  'has_footprint' : searchresult.featured['has_footprint'],
                  'has_sim' : searchresult.featured['has_sim'],
                  "manuf__image_100_20": searchresult.featured['organization_image_100_20'],
                  'datasheeturl' : '',
                };
                if(searchresult.featured['has_datasheet'] > 0){
                  col_1_data.datasheeturl = 'override to show there is datasheet';
                }
                var clone_col_1 = generate_ad_by_template(col_1_data, col_1_data.unipart_url, featured_type_name, false, true, true);

                var override_button_text = 'Reference Design',
                override_button_url = '#',
                override_title = "Start designing with IoT modules",
                override_description = "Get the reference design for the "+searchresult.featured['name']+"<br>Free download from Quectel.com",
                override_tracking_type = 'Search - Referral',
                override_manuf_image = "/static/img/ads/iot.png";
                if(featured_type_name === 'TEXAS'){
                  override_button_text = 'Check Availability';
                  override_button_url = searchresult.featured['buyURL'];
                  override_title = 'Buy on TI.com';
                  override_tracking_type = 'Search - Referral';
                  override_description = 'Starting from: '+searchresult.featured['price']+' USD';
                  override_manuf_image = searchresult.featured['organization_image_100_20'];

                  if(searchresult.featured['te_param']){
                    if(searchresult.featured['te_param']['Offers'] && searchresult.featured['te_param']['Offers']['quantity_available']){
                      override_description = override_description+'<br>Quantity available: ' + searchresult.featured['te_param']['Offers']['quantity_available'];
                    }
                  }
                }

                var col_2_data = {
                  'unipart_id': searchresult.featured['unipart_id'],
                  "manuf__name": searchresult.featured['manufacturer'],
                  "coverart": override_manuf_image,
                  "part": override_title,
                  "shortdesc": override_description,
                  "unipart_url": override_button_url,
                  "manuf__image_100_20": override_manuf_image,
                  "override_button_text": override_button_text,
                  "tracking_type" : override_tracking_type,
                  "tracking_location" : "Top search",
                };

                if(featured_type_name === 'TEXAS'){
                  col_2_data.fallback_image = 'https://snapeda.s3.amazonaws.com/pinax/images/company_logos/thumbs/ti_stk_2c_pos_rgb_tiff-0.png';
                }

                var clone_col_2 = generate_ad_by_template(col_2_data, col_2_data.unipart_url, featured_type_name, true, false);

                if(continue_custom_double_ad){
                  ad_container.find('.row-fluid').append(clone_col_1);
                  ad_container.find('.row-fluid').append(clone_col_2);

                  $('.featured_part.te_ad').remove();
                }
              }

              if (searchresult.featured['manufacturer'].toUpperCase().indexOf('SUPERIOR SENSOR') !== -1) {
                $('.stock_available_button .ad_carrier_text').html('Very short lead times')
              }
              if (searchresult.featured['manufacturer'].toUpperCase().indexOf("TRACO POWER") !== -1) {
                $('.stock_available_button .ad_carrier_text').html('Very short lead times')
              }

              if (searchresult.featured && searchresult.featured['high_runner'] === 'TRUE') {
                if (searchresult.featured['manufacturer'].toUpperCase().indexOf("QUECTEL") !== -1){
                  $('#stock_available_btn .ad_carrier_text').html('Hardware Design');
                }
                $('.ad_view #free-sample').hide();
                $('#stock_available_btn').show();
              }

              if ('component_carrier' in searchresult.featured && searchresult.featured['component_carrier']) {
                $('.featured_component_carrier').html('Component Carrier for '+ searchresult.featured['carrier_package']);
                $('.ad_carrier_text').html('Component Carrier for '+ searchresult.featured['carrier_package']);
                $('.ad_view #free-sample').hide();
                $('#free_sample_btn').show();
              }
            }
              else{
                // No search ad
                var Properties = {
                                'Search Term': 'APX803L20\u002D30SA\u002D7',
                                'userid': ''
                            };
                
                    if(resultParts[0]){
                        $.ajax({
                            url: '/api/get_search_banner_featured/',
                            headers: { 'X-SNAP-REQUEST-ID': request_id },
                            data: {query: 'APX803L20\u002D30SA\u002D7', description: resultParts[0].short_description},
                            success: function (data){
                                if (data.type === 'featured_part'){
                                    // Mark's code
                                    var ad_container = $('#inline_ad_template');

                                    ad_container.removeClass('hidden');

                                    var clone_col_1 = generate_ad_by_template(data, data.unipart_url, data.manuf__name, false);
                                    ad_container.find('.row-fluid').append(clone_col_1);
                                    if(data.show_cable_banner){
                                      var col_2_data = {
                                        "manuf__name": data.manuf__name,
                                        "coverart": "/static/img/molex-design-cable.jpg",
                                        "part": "Molex Custom Cable Creator",
                                        "shortdesc": "Design your own Custom Cable Harnes\nGet the concept drawing, 3D model and samples",
                                        "unipart_url": "https://www.molex.com/molex/custom-cable-creator/snapeda?utm_source=snapeda&utm_medium=referral&utm_campaign=molex_cable&utm_content=search_banner",
                                        "manuf__image_100_20": data.manuf__image_100_20,
                                      };
                                      var clone_col_2 = generate_ad_by_template(col_2_data, col_2_data.unipart_url, col_2_data.manuf__name, true);
                                      ad_container.find('.row-fluid').append(clone_col_2);

                                      var ad_version = 'Manufacturer style',
                                      hide_stock = 'N/A',
                                      ad_type = data.manuf__name,
                                      type_tracking = 'Search - Impression',
                                      part = data.part,
                                      relevancy = 'N/A';

                                      var Properties = {
                                          'Type': type_tracking,
                                          'image': data.coverart,
                                          'description': data.shortdesc,
                                          'hide_stock': hide_stock,
                                          'ad_version': ad_version,
                                          'Ad_type': 'snapeda',
                                          'Search Term': 'APX803L20\u002D30SA\u002D7',
                                          'userid': '',
                                          'description_shown': 'default description',
                                          'download_button_position': 'left',
                                          'Part': part,
                                          'relevancy': relevancy,
                                          'buy_buttom': 'False',
                                          'a_b_version': 'b',
                                          'sample_icon': 'N/A',
                                          'is_xref': false,
                                          'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                                      };

                                      Properties['Part Image'] = 'component';

                                      mixpanel.track(ad_type, Properties);
                                      track_db_and_mixpanel(ad_type, type_tracking, 'APX803L20\u002D30SA\u002D7', relevancy, part);
                                    }
                                } else{
                                    // no parts to show, show the connector banner
                                    $('.connector_ad').show();
                                }
                            },
                            error: function (data){
                                // Error, show the connector banner
                                $('.connector_ad').show();
                            }
                        })
                    }
                
            }
          }
        }

        // Initialize popperjs script for snapverified badge for full width
        initializeSnapVerifiedBadge(searchLen);
        // Initialize popperjs script for snapverified badge for mobile layout
        initializeSnapVerifiedBadge(searchLen, true);

        $(".ad_view").click(function(e){
          e.preventDefault();
          e.stopPropagation();
          const partpage_url = $(this).find(".featured_title").attr("href");
          if (partpage_url) {
            window.location.href = partpage_url;
          }
        });

        $(".ad_view").mouseup(function(e){
            e.stopPropagation();
            if ($(e.target).hasClass('ad_buy')){
              console.log('has ad_buy');
                return
            }

            if (e.which != 3){
              if (e.target.closest('#left_ad_view')) {
                ad_version = 'Manufacturer style';
                hide_stock = 'N/A';
                if (left_ad['type'] == 'SAMTEC'){
                  $('.featured_buttons .download').addClass('ver2');
                  ad_version = 'Distributor style';
                  if (left_ad['quantity'] == 'Not in stock'){
                    hide_stock = true;
                  }
                }

                // Brist ad click tracking | untracked digikey, mouser, arrow
                if(!enable_dual_topline && ['digikey', 'mouser', 'arrow', 'winsource electronics'].indexOf(left_ad['type']) === -1) {
                  if (left_ad['unipart_id'] !== undefined && left_ad['ad_ab_source'] === 'brist') {
                    recordAdEvent('CLICK_DATA_TYPE', left_ad['unipart_id'] + '', left_ad['auction_id']);
                  }
                }

                var ad_type = $(this).attr('data-type');
                let type_tracking = 'Search - View Part'

                var Properties = {
                    'Type': type_tracking,
                    'hide_stock': hide_stock,
                    'ad_version': ad_version,
                    'Ad_type': searchresult.featured_part_version,
                    'Search Term': 'APX803L20\u002D30SA\u002D7',
                    'userid': '',
                    'description_shown': description_shown,
                    'download_button_position': download_button_position,
                    'Part': left_ad['name'],
                    'description': left_ad['short_description'],
                    'relevancy': relevancy,
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'is_xref': left_ad['is_xref'] || false,
                    'ad_ab_version': left_ad['ad_ab_version'] || 'a',
                    'auction_id': left_ad['auction_id'] ? left_ad['auction_id'] + '' : 'NA',
                    'search_type': left_ad['search_type'] || '',
                    'ad_keywords': left_ad['ad_keywords'] || '',
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                if (ad_type == 'Mouser'){
                    Properties['ad_branding'] = 'Mouser';
                }

                if (left_ad['footprint_image'] === 'N/A') {
                  Properties['Part Image'] = 'component';
                }

                mixpanel.track(ad_type, Properties);
                track_db_and_mixpanel(ad_type, type_tracking, 'APX803L20\u002D30SA\u002D7', relevancy, left_ad['name'], true, left_ad['ad_ab_version'], left_ad['auction_id']);
                return;
              }
              
              if (e.target.closest('#right_ad_view')) {
                ad_version = 'Manufacturer style';
                hide_stock = 'N/A';
                if (right_ad['type'] == 'SAMTEC'){
                  $('.featured_buttons .download').addClass('ver2');
                  ad_version = 'Distributor style';
                  if (right_ad['quantity'] == 'Not in stock'){
                    hide_stock = true;
                  }
                }

                // Brist ad click tracking | untracked digikey, mouser, arrow
                if(!enable_dual_topline && ['digikey', 'mouser', 'arrow', 'winsource electronics'].indexOf(right_ad['type']) === -1) {
                  if (right_ad['unipart_id'] !== undefined && right_ad['ad_ab_source'] === 'brist') {
                    recordAdEvent('CLICK_DATA_TYPE', right_ad['unipart_id'] + '', right_ad['auction_id']);
                  }
                }

                var ad_type = $(this).attr('data-type');
                let type_tracking = 'Search - View Part'

                var Properties = {
                    'Type': type_tracking,
                    'hide_stock': hide_stock,
                    'ad_version': ad_version,
                    'Ad_type': searchresult.featured_part_version,
                    'Search Term': 'APX803L20\u002D30SA\u002D7',
                    'userid': '',
                    'description_shown': description_shown,
                    'download_button_position': download_button_position,
                    'Part': right_ad['name'],
                    'description': right_ad['short_description'],
                    'relevancy': relevancy,
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'is_xref': right_ad['is_xref'] || false,
                    'ad_ab_version': right_ad['ad_ab_version'] || 'a',
                    'auction_id': right_ad['auction_id'] ? right_ad['auction_id'] + '' : 'NA',
                    'search_type': right_ad['search_type'] || '',
                    'ad_keywords': right_ad['ad_keywords'] || '',
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                if (ad_type == 'Mouser'){
                    Properties['ad_branding'] = 'Mouser';
                }

                if (right_ad['footprint_image'] === 'N/A') {
                  Properties['Part Image'] = 'component';
                }

                mixpanel.track(ad_type, Properties);
                track_db_and_mixpanel(ad_type, type_tracking, 'APX803L20\u002D30SA\u002D7', relevancy, right_ad['name'], true, right_ad['ad_ab_version'], right_ad['auction_id']);
                return;
            }

              ad_version = 'Manufacturer style';
              hide_stock = 'N/A';
              if (searchresult.featured['type'] == 'SAMTEC'){
                $('.featured_buttons .download').addClass('ver2');
                ad_version = 'Distributor style';
                if (searchresult.featured['quantity'] == 'Not in stock'){
                  hide_stock = true;
                }
              }

              // Brist ad click tracking | untracked digikey, mouser, arrow
              if(!enable_dual_topline && ['digikey', 'mouser', 'arrow', 'winsource electronics'].indexOf(searchresult.featured['type']) === -1) {
                if (searchresult.featured['unipart_id'] !== undefined && searchresult.featured['ad_ab_source'] === 'brist') {
                  recordAdEvent('CLICK_DATA_TYPE', searchresult.featured['unipart_id'] + '', searchresult.featured['auction_id']);
                }
              }

              var ad_type = $(this).attr('data-type');
              let type_tracking = 'Search - View Part'

              var Properties = {
                  'Type': type_tracking,
                  'hide_stock': hide_stock,
                  'ad_version': ad_version,
                  'Ad_type': searchresult.featured_part_version,
                  'Search Term': 'APX803L20\u002D30SA\u002D7',
                  'userid': '',
                  'description_shown': description_shown,
                  'download_button_position': download_button_position,
                  'Part': searchresult.featured['name'],
                  'description': searchresult.featured['short_description'],
                  'relevancy': relevancy,
                  'a_b_version': 'b',
                  'sample_icon': sample_icon,
                  'is_xref': searchresult.featured['is_xref'] || false,
                  'ad_ab_version': searchresult.featured['ad_ab_version'] || 'a',
                  'auction_id': searchresult.featured['auction_id'] ? searchresult.featured['auction_id'] + '' : 'NA',
                  'search_type': searchresult.featured['search_type'] || '',
                  'ad_keywords': searchresult.featured['ad_keywords'] || '',
                  'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
              };
              if (ad_type == 'Mouser'){
                  Properties['ad_branding'] = 'Mouser';
              }

              if (searchresult.featured['footprint_image'] === 'N/A') {
                Properties['Part Image'] = 'component';
              }

              mixpanel.track(ad_type, Properties);
              track_db_and_mixpanel(ad_type, type_tracking, 'APX803L20\u002D30SA\u002D7', relevancy, searchresult.featured['name'], true, searchresult.featured['ad_ab_version'], searchresult.featured['auction_id']);
            }

        });

        $(".ad_buy").mouseup(function(e){
            if (e.which != 3){

              // Brist ad click tracking | untracked digikey, mouser, arrow
              if(!enable_dual_topline && ['digikey', 'mouser', 'arrow', 'winsource electronics'].indexOf(searchresult.featured['type']) === -1) {
                if (searchresult.featured['unipart_id'] !== undefined && searchresult.featured['ad_ab_source'] === 'brist') {
                  recordAdEvent('CLICK_DATA_TYPE', searchresult.featured['unipart_id'] + '', searchresult.featured['auction_id']);
                }
              }

              const ad_type = $(this).attr('data-type');
              const featuredType =  searchresult.featured['type'];
              const isDigiKeyOrMouser = ad_type == 'digikey' || ad_type == 'mouser';
              if (isDigiKeyOrMouser) {
                var Properties = {
                  'Type': 'Search - Buy Part',
                  'Ad_type': searchresult.featured_part_version,
                  'Search Term': 'APX803L20\u002D30SA\u002D7',
                  'userid': '',
                  'description_shown': description_shown,
                  'Part': searchresult.featured['name'],
                  'relevancy': relevancy,
                  'a_b_version': 'b',
                  'sample_icon': sample_icon,
                  'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                }
              } else {
                var Properties = {
                  'Type': 'Search - Buy Part',
                  'ad_version': '1',
                  'Ad_type': searchresult.featured_part_version,
                  'Search Term': 'APX803L20\u002D30SA\u002D7',
                  'userid': '',
                  'description_shown': description_shown,
                  'Part': searchresult.featured['name'],
                  'relevancy': relevancy,
                  'a_b_version': 'b',
                  'sample_icon': sample_icon,
                  'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                }
              }

              if(window.numberOfSkus) {
                Properties['numberOfSkus'] = window.numberOfSkus;
              }
              mixpanel.track(ad_type, Properties);
              track_db_and_mixpanel(ad_type, 'Search - Buy Part', 'APX803L20\u002D30SA\u002D7', relevancy, searchresult.featured['name'], true);
            }
        });

        $("div.ad_buy .buy").click(function(event) {
           event.preventDefault();
        });

        if (!window.isRSComponents) {
          $(".ad_buy").click(function() {
            let distributor_url = $(this).find(".buy").attr("href");
            if (distributor_url !== undefined) {
                window.open(distributor_url, '_blank');
            }
          });
        }
            
        const inlinePos = Math.floor(resultParts.length / 2)
        if (searchresult.inlinefeatured && searchresult.inlinefeatured['description'] && inlinePos >= 5){
            var tr_1 = doc.createElement('tr');
            tr_1.id = 'inline-part-ad';
            var mfg_1 = doc.createElement('td');
            mfg_1.className = 'in-line_ad part-result inlinead_view manufacturer_mobile';
            mfg_1.colSpan = 8;
            mfg_1.style.borderLeftStyle = 'solid';
            mfg_1.style.borderLeftColor = '#FF761A';
            mfg_1.style.borderLeftWidth = '5px';
            var inline_ad = '';
            if(searchresult.inlinefeatured.buyURL === 'Hide') {
              inline_ad = doc.getElementById('designInlineManufacturer');
            } else {
              inline_ad = doc.getElementById('designInlineDistributor');
            }
            let midResult = $('#result' + inlinePos);
            mfg_1.appendChild(inline_ad);
            tr_1.appendChild(mfg_1);
            midResult.before(tr_1);

            var inlineAd = searchresult.inlinefeatured.buyURL === 'Hide' ? '#designInlineManufacturer' : '#designInlineDistributor';
            if (typeof searchresult.inlinefeatured['buyURL'] !== 'undefined' ) {
            if (searchresult.inlinefeatured['buyURL'] != 'N/A'){
              var part = searchresult.inlinefeatured;
                var part_data = {
                  part_number: part['name'],
                  manufacturer: part['manufacturer'],
                  unique_id: part['uniqueid'],
                  unipart_id: part['unipart_id'],
                  has_datasheet: part['has_datasheet'],
                  has_unipart: true,
                  row_counter: i,
                  sample_current_status: part['has_sample'],
                };
                var availability,averagePrice;
                $.getJSON("/api/get_more_resources/", part_data , function(json) {
                        availability = String(json.availability);
                        averagePrice = String(json.average_price);
                        var has_datasheet = String(json.has_datasheet);
                        var total = 0;
                        var num_items = 0;
                        var contact_us_available = false;
                        if( json.sample_status === 'available' && searchresult.inlinefeatured['has_sample'] == 'AJAX' ){
                            $('.inline_data_icons img.sample').attr('src', "/static/img/search_icons/sample_orange.png");
                            $('.inline_data_icons img.sample').attr('title', 'Sample available');
                            $('.inline_data_icons img.sample').attr('alt', 'Sample available');
                            $('.inlinead_view #free-sample').show();
                        }
                        if(json.offers && json.offers.length > 0) {
                          json.offers.forEach(function(offer){
                            if (averagePrice == 'undefined' || averagePrice == 'N/A' || !averagePrice) {
                              if (offer['price']) {
                                if(offer['price']['USD'].length){
                                    for (price in offer['price']['USD']){
                                      total += Number(offer['price']['USD'][price][1])
                                      num_items += 1
                                    }
                                }
                              }else{
                                  if(offer['prices'].length){
                                      for (price in offer['prices']){
                                        total += Number(offer['prices'][price][1])
                                        num_items += 1
                                      }
                                    }
                                  }
                            }
                            if(offer && offer.quantity_available && offer.quantity_available.toLowerCase() == "contact us"){
                              contact_us_available=true;
                            }
                          });
                          if (num_items != 0) {
                            var avg = total / num_items
                            availability = 'Available'
                            averagePrice = avg;
                          }
                        } else {
                          availability = 'Unavailable'
                          averagePrice = 'N/A';
                        }

                        if(contact_us_available){
                            availability = 'Available'
                          }

                        if (averagePrice == 'undefined' && !offers.length) {
                          averagePrice = 'N/A';
                        }
                        var price = searchresult.inlinefeatured['price'];
                        if (typeof price !== 'undefined' && typeof price != 'string'){
                          price = price;
                        }

                        if (price == 'N/A' || !price){
                          if(averagePrice !== 'N/A' && averagePrice){
                            $('.inline_price .price').text('$' + averagePrice);
                          }else{
                          $('.inline_price').hide();
                          }
                        }else{
                          $('.inline_price .price').text(price);
                        }
                        if (searchresult.inlinefeatured['quantity'] == 'Not in stock' && availability == 'Available'){
                          searchresult.inlinefeatured['quantity'] = 'In Stock'
                        }
                        if (parseInt(searchresult.inlinefeatured['quantity']) == 0){
                          $('.inline_price .quantity_text').text("on order");
                          $('.inline_price .quantity').text(numberWithCommas(searchresult.inlinefeatured['quantity_on_order']));
                        }else{
                          $('.inline_price .quantity').text(numberWithCommas(searchresult.inlinefeatured['quantity']));
                        }

                        if (searchresult.inlinefeatured['type'] == 'SAMTEC'){
                          if (searchresult.inlinefeatured['quantity'] == 'Not in stock'){
                            $('.avail_sect').hide();
                          }
                        }
                })
              var ref = research_feature_ref(searchresult.inlinefeatured['type'], searchresult.inlinefeatured['ref'], true);
              var query_encoded = encodeURIComponent('APX803L20\u002D30SA\u002D7');
              var part_url = '/parts/' + searchresult.inlinefeatured['urlname'] + '/' + searchresult.inlinefeatured['manufacturer'] + '/view-part/?ref=' + ref + '&t=APX803L20-30SA-7' + '&con_ref=None';

              if (searchresult.inlinefeatured['auction_id'] !== undefined) {
                 part_url = part_url + '&auction_id=' +  searchresult.inlinefeatured['auction_id'] + '&ad_ab_version=' + searchresult.inlinefeatured['ad_ab_version'];
              }
              // Add ab_test_case to the URL
              if (typeof searchresult !== 'undefined' && searchresult &&
                  typeof searchresult.search_ad_props !== 'undefined' && searchresult.search_ad_props &&
                  typeof searchresult.search_ad_props.ab_test_case !== 'undefined' && searchresult.search_ad_props.ab_test_case) {
                  part_url = part_url + '&ab_test_case=' + searchresult.search_ad_props.ab_test_case;
              }

              $('.inline_title').text(searchresult.inlinefeatured['name']).attr('href', part_url);
              $('.inline_desc').html(searchresult.inlinefeatured['description']);

              //hide the samtec availability when TE part is shown
              if (searchresult.inlinefeatured['type'] == 'TE'){
                $('.inline_buttons .inline_data_icons p').hide();
              }

              $('.inline_ad_part .featured_img a').attr('href', part_url);

              $('.inline_ad_part .featured_img a img').attr('src',searchresult.inlinefeatured['image']);
              $('.inline_ad_part .featured_img a img').on("error", function () {
                    $('.inline_ad_part .featured_img a img').hide();
                    getFallbackImage(searchresult.inlinefeatured['name'], searchresult.inlinefeatured['manufacturer'], function(imgUrl){
                        $('.inline_ad_part .featured_img a').html('<img src="' + imgUrl + '" alt="inlinead"/>');
                    })
              });

              $('.inline_manuf').html("by " + searchresult.inlinefeatured['manufacturer']);


              $('.inline_ad_part .manuf_img img').attr('src',searchresult.inlinefeatured['organization_image_100_20']);
              $('.inline_ad_part .manuf_img img').on("error", function () {
                  $('.inline_ad_part .manuf_img img').hide();
              });

                              // is this ad for mouser or digikey
                if (searchresult.inlinefeatured['type'] == 'digikey'){
                  $(inlineAd).each(function () {
                      this.style.setProperty( 'display', 'block', 'important' );
                  });
                  $('.inline_ad_part.mouser_ad2').remove();
                  $('.inline_ad_part.te_ad').remove();
                  $('.inline_content .ver3_img').attr('src', "/static/img/digikey_logo_80.svg");
                  $('.inline_content .ver3_img').attr('alt', 'Buy now from DigiKey');
                  $('.inline_content .buy_title').html(getWording('DigiKey'));
                  $('.inline_content a.buy').html("Check availability");
                  $('.inline_content a.buy').attr("href", searchresult.inlinefeatured.buyURL);
                  $('.inline_content a.buy').attr('data-type','DigiKey');
                  $('.inlinead_view').attr('data-type','DigiKey');
                  $('.inlinead_view').attr('data-manufacturer', formatDataManufacturer(searchresult.inlinefeatured['manufacturer']));
                  $('.inlinead_view').addClass('manuf_' + formatDataManufacturer(searchresult.inlinefeatured['manufacturer']));
                  $('.in-line_ad').addClass('inline_ad_buy');
                  $('.in-line_ad').removeClass('inlinead_view');
                  $('.in-line_ad').removeClass('part-result');
                  $('.in-line_ad .border-right').removeClass('ad_view');
                  $('.in-line_ad .border-right').addClass('inlinead_view');
                  $('.inlinead_view').attr('data-type','DigiKey');
                  // DIGIKEY TRACKING 1x1 PIXEL setup, when URLs were updated the actual implementation of this, did not
                // have any changes.
                var link_href = "https://ad.doubleclick.net/ddm/trackclk/N4481.2579422SNAPEDA/B4652602.307340238;dc_trk_aid=500214202;dc_trk_cid=102628001;dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;tfua=;ltd=";
                var img_src = "https://ad.doubleclick.net/ddm/trackimp/N4481.2579422SNAPEDA/B4652602.307340238;dc_trk_aid=500214202;dc_trk_cid=102628001;ord=1756068672;dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;tfua=;ltd=?"

                  $('.digikey_tracking').attr('href',link_href);
                  $('.digikey_tracking_img').attr('src', img_src);
                }

                if (searchresult.inlinefeatured['type'] == 'mouser'){
                  $(inlineAd).each(function () {
                      this.style.setProperty( 'display', 'block', 'important' );
                  });

                  $('.inline_ad_part.te_ad').remove();
                  $('.inline_content .ver3_img').attr('src', 'https://s3-us-west-2.amazonaws.com/snapeda-static/images/mouser_logo.png');
                  $('.inline_content .ver3_img').attr('alt', 'Buy now from Mouser');
                  $('.inline_content .buy_title').html(getWording('Mouser'));
                  $('.inline_content a.buy').html("Check availability");
                  $('.inline_content a.buy').attr("href", searchresult.inlinefeatured.buyURL);
                  $('.inline_content a.buy').attr('data-type','Mouser');
                  $('.inlinead_view').attr('data-type','Mouser');
                  $('.inlinead_view').attr('data-manufacturer', formatDataManufacturer(searchresult.inlinefeatured['manufacturer']));
                  $('.inlinead_view').addClass('manuf_' + formatDataManufacturer(searchresult.inlinefeatured['manufacturer']));
                  $('.in-line_ad').addClass('inline_ad_buy');
                  $('.in-line_ad').removeClass('inlinead_view');
                  $('.in-line_ad').removeClass('part-result');
                  $('.in-line_ad .border-right').removeClass('ad_view');
                  $('.in-line_ad .border-right').addClass('inlinead_view');
                  $('.inlinead_view').attr('data-type','Mouser');

                  // Mouser colors
                  $(".inline_ad_buy").addClass('mouser_color');
                  $(".in-line_ad a.digikey_buy").addClass('mouser_color');
                  $('.in-line_ad .sponsored_indicator').addClass('mouser_color');

                  $('.digikey_tracking').hide();

                }

                if (searchresult.inlinefeatured['type'] == 'arrow'){
                  $(inlineAd).each(function () {
                      this.style.setProperty( 'display', 'block', 'important' );
                  });

                  $('.inline_ad_part.te_ad').remove();
                  $('.inline_ad_part.mouser_ad2').remove();
                  $('.inline_content .ver3_img').attr('src', 'https://s3-us-west-2.amazonaws.com/snapeda-static/pinax/images/company_logos/arrow_logo.gif');
                  $('.inline_content .ver3_img').attr('alt', 'Buy now from Arrow');
                  $('.inline_content .buy_title').html(getWording('Arrow'));
                  $('.inline_content a.buy').html("Buy From Arrow");
                  $('.inline_content a.buy').attr("href", searchresult.inlinefeatured.buyURL);
                  $('.inline_content a.buy').attr('data-type','Arrow');
                  $('.inlinead_view').attr('data-type','Arrow');
                  $('.inlinead_view').attr('data-manufacturer', formatDataManufacturer(searchresult.inlinefeatured['manufacturer']));
                  $('.inlinead_view').addClass('manuf_' + formatDataManufacturer(searchresult.inlinefeatured['manufacturer']));
                  $('.in-line_ad').addClass('inline_ad_buy');
                  $('.in-line_ad').removeClass('inlinead_view');
                  $('.in-line_ad').removeClass('part-result');
                  $('.in-line_ad .border-right').removeClass('ad_view');
                  $('.in-line_ad .border-right').addClass('inlinead_view');
                  $('.inlinead_view').attr('data-type','Arrow');
                  $('.inlinead_view').attr('data-type','Arrow');

                  $('.digikey_tracking').hide();

                }

              if (['digikey', 'mouser', 'arrow', 'winsource electronics'].indexOf(searchresult.inlinefeatured['type']) === -1){
                $(inlineAd).each(function () {
                    this.style.setProperty( 'display', 'block', 'important' );
                });

                $('.inline_ad_buy').html("Buy From " + searchresult.inlinefeatured['type']);

                if (searchresult.inlinefeatured['type'] === 'HARTING'){
                  $('.inline_ad_buy').hide()
                }

                $('.in-line_ad').attr('data-type', searchresult.inlinefeatured['type']);
                $('.inlinead_view').attr('data-type', searchresult.inlinefeatured['type']);
                $('.inlinead_view').attr('data-manufacturer', formatDataManufacturer(searchresult.inlinefeatured['manufacturer']));
                $('.inlinead_view').addClass('manuf_' + formatDataManufacturer(searchresult.inlinefeatured['manufacturer']));
                $('.inline_ad_part a.ad_buy').attr('data-type', searchresult.inlinefeatured['type']);

              }

              if(searchresult.inlinefeatured['has_symbol'] == 'AJAX'){
                var part_data = {
                    part_number: searchresult.inlinefeatured['name'],
                    manufacturer: searchresult.inlinefeatured['manufacturer'],
                    unique_id : searchresult.inlinefeatured['uniqueid'],
                    unipart_id: searchresult.inlinefeatured['unipart_id'],
                    pin_count: searchresult.inlinefeatured['pin_count'],
                    has_datasheet: searchresult.inlinefeatured['has_datasheet'],
                    package_type: searchresult.inlinefeatured['package_type'],
                    row_counter: i,
                };
                $.getJSON("/api/get_has_sym_foot_3d/", part_data , function(json) {
                  var has_datasheet = Number(json.has_datasheet);
                  var has_symbol = Number(json.has_symbol);
                  var has_footprint = Number(json.has_footprint);
                  var has_3d = Number(json.has_3d);
                  var has_sim = Number(json.has_sim);
                  var has_sample = Number(json.has_sample);

                  if (has_datasheet) {
                    var datasheet_icon = document.getobjectbyid;
                    $('.inline_data_icons img.datasheet').attr('src', "/static/img/search_icons/datasheet_orange.png");
                    $('.inline_data_icons img.datasheet').attr('title', 'Datasheet available');
                    $('.inline_data_icons img.datasheet').attr('alt', 'Datasheet available');
                  }
                  if (has_symbol) {
                    $('.inline_data_icons img.symbol').attr('src', "/static/img/search_icons/symbol_orange.png");
                    $('.inline_data_icons img.symbol').attr('title', 'Symbol available');
                    $('.inline_data_icons img.symbol').attr('alt', 'Symbol available');
                  }
                  if (has_footprint) {
                    $('.inline_data_icons img.footprint').attr('src', "/static/img/search_icons/footprint_orange.png");
                    $('.inline_data_icons img.footprint').attr('title', 'Footprint available');
                    $('.inline_data_icons img.footprint').attr('alt', 'Footprint available');
                  }
                  if (has_3d) {
                    $('.inline_data_icons img.3d').attr('src', "/static/img/search_icons/3d_model_orange.png");
                    $('.inline_data_icons img.3d').attr('title', '3D model available');
                    $('.inline_data_icons img.3d').attr('alt', '3D model available');
                  }if (has_sim){
                    $('.inline_data_icons img.sim').attr('src', "/static/img/search_icons/sim_orange.png");
                    $('.inline_data_icons img.sim').attr('title', 'Sim model available');
                    $('.inline_data_icons img.sim').attr('alt', 'Sim model available');
                  }if (has_sample){
                      $('.inline_data_icons img.sample').attr('src', "/static/img/search_icons/sample_orange.png");
                      $('.inline_data_icons img.sample').attr('title', 'Sample available');
                      $('.inline_data_icons img.sample').attr('alt', 'Sample available');
                      // if(a_b_version == 1 || a_b_version == 2) {
                        sample_inline_icon = "/static/img/search_icons/sample_orange.png";
                      // }
                    }
                });

              }
              else{
                if ((searchresult.inlinefeatured['has_datasheet']) == '1') {
                  $('.inline_data_icons img.datasheet').attr('src', "/static/img/search_icons/datasheet_orange.png");
                  $('.inline_data_icons img.datasheet').attr('title', 'Datasheet available');
                  $('.inline_data_icons img.datasheet').attr('alt', 'Datasheet available');
                }
                if ((searchresult.inlinefeatured['has_symbol']) == '1') {
                  $('.inline_data_icons img.symbol').attr('src', "/static/img/search_icons/symbol_orange.png");
                  $('.inline_data_icons img.symbol').attr('title', 'Symbol available');
                  $('.inline_data_icons img.symbol').attr('alt', 'Symbol available');
                }
                if ((searchresult.inlinefeatured['has_footprint']) == '1') {
                  $('.inline_data_icons img.footprint').attr('src', "/static/img/search_icons/footprint_orange.png");
                  $('.inline_data_icons img.footprint').attr('title', 'Footprint available');
                  $('.inline_data_icons img.footprint').attr('alt', 'Footprint available');
                }
                if ((searchresult.inlinefeatured['has_3d']) == '1') {
                  $('.inline_data_icons img.3d').attr('src', "/static/img/search_icons/3d_model_orange.png");
                  $('.inline_data_icons img.3d').attr('title', '3D model available');
                  $('.inline_data_icons img.3d').attr('alt', '3D model available');
                }
                if ((searchresult.inlinefeatured['has_sim']) == '1'){
                  $('.inline_data_icons img.sim').attr('src', "/static/img/search_icons/sim_orange.png");
                  $('.inline_data_icons img.sim').attr('title', 'Sim model available');
                  $('.inline_data_icons img.sim').attr('alt', 'Sim model available');
                }
                // Replaced includes for indexOf for IE compatibility
                var temp_manuf = searchresult.inlinefeatured['manufacturer'];
                var featured_part = searchresult.inlinefeatured;
                if(featured_part['has_sample'] && featured_part['has_sample'] != 'AJAX'){
                    $('.inline_data_icons img.sample').attr('src', "/static/img/search_icons/sample_orange.png");
                    $('.inline_data_icons img.sample').attr('title', 'Sample available');
                    $('.inline_data_icons img.sample').attr('alt', 'Sample available');
                    // if(a_b_version == 1 || a_b_version == 2) {
                      sample_inline_icon = "/static/img/search_icons/sample_orange.png";
                    // }
                }
              }

              $('.inline_buttons .download').attr('href', part_url);
              if (searchresult.inlinefeatured['hasModel']==0){
                $('.inline_buttons .download').text('View Part');
              }

              $('.inline_content .download').attr('href', part_url);
              if (searchresult.inlinefeatured['hasModel']==0){
                $('.inline_content .download').text('View Part');
              }

              $(inlineAd).show();
              if (searchresult.inlinefeatured['type'] == 'digikey'){
              $('.digikey_tracking').show();
              }
              var relevancy = 'N/A';

              if (searchresult.inlinefeatured['type'] == 'digikey'){
                var Properties = {
                    'Type': 'In-line Search - Impression',
                    'image': searchresult.inlinefeatured['image'],
                    'description': searchresult.inlinefeatured['description'],
                    'stock': searchresult.inlinefeatured['quantity'],
                    'Ad_type': searchresult.featured_part_version,
                    'Part': searchresult.inlinefeatured['name'],
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                mixpanel.track('DigiKey', Properties);
                $('.inline_buttons .buy').attr('href', searchresult.inlinefeatured['buyURL'] + '?utm_source=snapeda&utm_medium=aggregator&utm_campaign=buynow');

                track_db_and_mixpanel('DigiKey', 'In-line Search - Impression', "APX803L20\u002D30SA\u002D7", '', searchresult.inlinefeatured['name'])
              }
              if (searchresult.inlinefeatured['type'] == 'mouser'){
                var Properties = {
                    'Type': 'In-line Search - Impression',
                    'image': searchresult.inlinefeatured['image'],
                    'description': searchresult.inlinefeatured['description'],
                    'stock': searchresult.inlinefeatured['quantity'],
                    'Part': searchresult.inlinefeatured['name'],
                    'manufacturer': searchresult.featured['manufacturer'],
                    'ad_branding': 'Mouser',
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                mixpanel.track('Mouser', Properties);
                $('.inline_buttons .buy').attr('href', searchresult.inlinefeatured['buyURL'] + '&utm_source=snapedaonline&utm_medium=online&utm_campaign=mouser&utm_content=search');

                 track_db_and_mixpanel('Mouser', 'Search - Impression', "APX803L20\u002D30SA\u002D7", '', searchresult.inlinefeatured['name']);
                // Track Mouser ad impression in DB
                $.ajax({
                  url: '/api/record_mouser_impression_api/search',
                })
                .done(function() {
                  console.log("success");
                })
              }
              if (searchresult.inlinefeatured['type'] == 'arrow'){
                var Properties = {
                    'Type': 'In-line Search - Impression',
                    'image': searchresult.inlinefeatured['image'],
                    'description': searchresult.inlinefeatured['description'],
                    'stock': searchresult.inlinefeatured['quantity'],
                    'ad_version': '1',
                    'Part': searchresult.inlinefeatured['name'],
                    'a_b_version': 'b',
                    'sample_icon': sample_icon,
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                mixpanel.track('Arrow', Properties);
                $('.inline_buttons .buy').attr('href', searchresult.inlinefeatured['buyURL']);
                track_db_and_mixpanel('Arrow', 'In-line Search - Impression', '', '', searchresult.inlinefeatured['name']);

                // Track Arrow ad impression in DB
                $.ajax({
                  url: '/api/record_ad_impression_api/arrow_search',
                })
                .done(function() {
                  console.log("success");
                })
              }

              if (['digikey', 'mouser', 'arrow', 'winsource electronics'].indexOf(searchresult.inlinefeatured['type']) === -1){

                if (searchresult.inlinefeatured['buyURL'] == 'Hide' ){
                  $('.inline_buttons .buy').remove();
                  $('.ad_view_left').show();
                  download_button_position = 'left';
                  buy_buttom = 'False';
                }else{
                  $('.inline_buttons .buy').attr('href', searchresult.inlinefeatured['buyURL']);
                  buy_buttom = 'True';
                }

                if (typeof searchresult.inlinefeatured['relevancy'] !== 'undefined' ){
                  relevancy = searchresult.inlinefeatured['relevancy'];
                }else{
                  relevancy = '';
                }

                ad_version = 'Manufacturer style';
                hide_stock = 'N/A';
                if (searchresult.inlinefeatured['type'] == 'SAMTEC'){
                  $('.featured_buttons .download').addClass('ver2');
                  ad_version = 'Distributor style';
                  if (searchresult.inlinefeatured['quantity'] == 'Not in stock'){
                    hide_stock = true;
                  }
                }

                if(sample_inline_icon !== 'N/A') {
                  $('.inlinead_view #free-sample').show();
                }

                var Properties = {
                    'Type': 'In-line Search - Impression',
                    'image': searchresult.inlinefeatured['image'],
                    'description': searchresult.inlinefeatured['short_description'],
                    'stock': searchresult.inlinefeatured['quantity'],
                    'ad_version': ad_version,
                    'Search Term': 'APX803L20\u002D30SA\u002D7',
                    'userid': '',
                    'relevancy': relevancy,
                    'hide_stock': hide_stock,
                    'Part': searchresult.inlinefeatured['name'],
                    'description_shown': description_shown,
                    'download_button_position': download_button_position,
                    'buy_buttom': buy_buttom,
                    'Part Image': 'component',
                    'a_b_version': 'b',
                    'sample_inline_icon': sample_inline_icon,
                    'is_xref': searchresult.inlinefeatured['is_xref'] || false,
                    'ad_ab_version': searchresult.inlinefeatured['ad_ab_version'] || 'a',
                    'auction_id': searchresult.inlinefeatured['auction_id'] ? searchresult.inlinefeatured['auction_id'] + '' : 'NA',
                    'search_type': searchresult.inlinefeatured['search_type'] || '',
                    'ad_keywords': searchresult.inlinefeatured['ad_keywords'] || '',
                    'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                };
                mixpanel.track(searchresult.inlinefeatured['type'], Properties);
                track_db_and_mixpanel(searchresult.inlinefeatured['type'], 'In-line Search - Impression', 'APX803L20\u002D30SA\u002D7', relevancy, searchresult.inlinefeatured['name'], false, searchresult.inlinefeatured['ad_ab_version'], searchresult.inlinefeatured['auction_id'])

              }

            }
          }
          if($(inlineAd + ' .inline_buttons .buy').length > 0 && $(inlineAd +  ' .inline_buttons .download').length > 0) {
            var btnBuy = $(inlineAd + ' .inline_buttons .buy').offset().top;
            var btnDownload = $(inlineAd + ' .inline_buttons .download').offset().top;
            if(btnBuy > btnDownload) {
                $(inlineAd + ' .inline_buttons .download').css('margin-top', btnBuy-btnDownload);
            } else if(btnDownload > btnBuy) {
                $(inlineAd + ' .inline_buttons .buy').css('margin-top', btnDownload-btnBuy);
            }
          }
        }

        $(".inlinead_view").mouseup(function(e){
            e.stopPropagation();
            if (e.which != 3){
              ad_version = 'Manufacturer style';
              hide_stock = 'N/A';

              var ad_type = $(this).attr('data-type');
              if(ad_type == 'SAMTEC') {
                $('.featured_buttons .download').addClass('ver2');
                ad_version = 'Distributor style';
              }

              // Brist ad click tracking
              if (searchresult.inlinefeatured['unipart_id'] !== undefined && searchresult.inlinefeatured['ad_ab_source'] === 'brist') {
                recordAdEvent('CLICK_DATA_TYPE', searchresult.inlinefeatured['unipart_id'] + '', searchresult.inlinefeatured['auction_id']);
              }

              var Properties = {
                  'Type': 'Search - View Part (Inline)',
                  'hide_stock': hide_stock,
                  'ad_version': ad_version,
                  'Ad_type': searchresult.featured_part_version,
                  'Search Term': 'APX803L20\u002D30SA\u002D7',
                  'userid': '',
                  'description_shown': description_shown,
                  'download_button_position': download_button_position,
                  'a_b_version': 'b',
                  'Part': searchresult.inlinefeatured['name'],
                  'description': searchresult.inlinefeatured['short_description'],
                  'sample_inline_icon': sample_inline_icon,
                  'ad_ab_version': searchresult.inlinefeatured['ad_ab_version'] || 'a',
                  'auction_id': searchresult.inlinefeatured['auction_id'] ? searchresult.inlinefeatured['auction_id'] + '' : 'NA',
                  'search_type': searchresult.inlinefeatured['search_type'] || '',
                  'ad_keywords': searchresult.inlinefeatured['ad_keywords'] || '',
                  'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
              };

              if (searchresult.inlinefeatured['footprint_image'] != 'N/A'){
                  // pass
              }else{
                Properties['Part Image'] = 'component';
              }

              mixpanel.track(ad_type, Properties);
              track_db_and_mixpanel(ad_type, 'Search - View Part (Inline)', 'APX803L20\u002D30SA\u002D7', relevancy,
                  searchresult.inlinefeatured['name'], true, searchresult.inlinefeatured['ad_ab_version'], searchresult.inlinefeatured['auction_id']);
            }

        });


        $(".inline_ad_buy").mouseup(function(e){
            if (e.which != 3){
              var ad_type = $(this).attr('data-type');
              if (searchresult.inlinefeatured['type'] == 'digikey'){
                  var Properties = {
                      'Type': 'In-line Search - Buy Part',
                      'Ad_type': searchresult.featured_part_version,
                      'Ad_type': searchresult.featured_part_version,
                      'Search Term': 'APX803L20\u002D30SA\u002D7',
                      'userid': '',
                      'description_shown': description_shown,
                      'Part': searchresult.inlinefeatured['name'],
                      'description': searchresult.inlinefeatured['short_description'],
                      'relevancy': relevancy,
                      'a_b_version': 'b',
                      'sample_icon': sample_icon,
                      'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                  }
              }else if (searchresult.featured['type'] == 'mouser') {
                  var Properties = {
                      'Type': 'In-line Search - Buy Part',
                      'Ad_type': searchresult.featured_part_version,
                      'Ad_type': searchresult.featured_part_version,
                      'Search Term': 'APX803L20\u002D30SA\u002D7',
                      'userid': '',
                      'description_shown': description_shown,
                      'Part': searchresult.inlinefeatured['name'],
                      'relevancy': relevancy,
                      'a_b_version': 'b',
                      'sample_icon': sample_icon,
                      'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                  }
              }else{
                  var Properties = {
                      'Type': 'In-line Search - Buy Part',
                      'ad_version': '1',
                      'Ad_type': searchresult.featured_part_version,
                      'Ad_type': searchresult.featured_part_version,
                      'Search Term': 'APX803L20\u002D30SA\u002D7',
                      'userid': '',
                      'description_shown': description_shown,
                      'Part': searchresult.inlinefeatured['name'],
                      'relevancy': relevancy,
                      'a_b_version': 'b',
                      'sample_icon': sample_icon,
                      'ab_test_case': (searchresult.search_ad_props && searchresult.search_ad_props.ab_test_case) ? searchresult.search_ad_props.ab_test_case : ''
                  }
              }
              mixpanel.track(ad_type, Properties);
              track_db_and_mixpanel(ad_type, 'In-line Search - Buy Part', 'APX803L20\u002D30SA\u002D7', relevancy,
                  searchresult.inlinefeatured['name']);
            }
        });

        $(".inline_ad_buy .buy").click(function(event) {
           event.preventDefault();
        });

        $(".inline_ad_buy").click(function() {
            let distributor_url = $(this).find(".buy").attr("href");
            if (distributor_url !== undefined) {
                window.open(distributor_url, '_blank');
            }
        });

        // SnapVerified badge for featured and inline ads
        if (searchresult.featured && searchresult.featured['snap_verified']) {
          // Do nothing
        } else {
          $('#snapverifiedfeatured').addClass('hidden')
        }

        if (searchresult.inlinefeatured && searchresult.inlinefeatured['snap_verified']) {
          // Do nothing
        } else {
          $('#snapverifiedinline').addClass('hidden')
        }

        if (enable_dual_topline) {
          if (!searchresult.ads[0]['snap_verified']) {
            $('#snapverifiedlefttopline').addClass('hidden');
          }

          if (!searchresult.ads[1]['snap_verified']) {
            $('#snapverifiedrighttopline').addClass('hidden');
          }
        }

        if (searchresult.featured && searchresult.featured['te_param'] && searchresult.featured['te_param']['snap_verified']) {
          // Do nothing
        } else {
          $('#snapverifieddigikey').addClass('hidden')
        }

    }
    function getFallbackImage(partname, manufacturer, cb){
        const url = '/api/get_mouser_info_api?part_name=' + partname + '&manufacturer=' + manufacturer;
        $.getJSON(url, function(r){
            cb(r['ImagePath'])
        })
    }

    function recordNonAdEvent(keywords, eventType, productId, metadata) {
      data = {
        'keywords': keywords,
        'eventType': eventType,
        'productId': productId,
        'metadata': JSON.stringify(metadata)
      }
      $.ajax({
            url: '/api/record_non_ad_event/',
            type: 'POST',
            async: true,
            data: data,
      })
    }

    function recordAdEvent(eventType, productId, auctionId) {
      payload = {
        'eventType': eventType,
        'productId': productId,
        'auctionId': auctionId
      }

      $.ajax({
            url: '/api/record_ad_event/',
            type: 'POST',
            async: true,
            data: payload,
      })
    }

    function createSnapVerifiedBadge(index, show_badge, is_mobile_layout=false) {
      var p = document.createElement("p");
      if (show_badge) {
        p.className = "snapverified";
      } else {
        p.className = "snapverified hidden";
      }

      p.id = is_mobile_layout ? "snapverifiedmobile" + index : "snapverified" + index;
      indexName = is_mobile_layout ? "mobile" + index : "" + index;

      let cssText = "display: flex; flex-direction: row; align-items: center; gap: 5px; color: #666460; font-weight: 400; line-height: inherit; font-size: 14px; cursor: pointer; margin-top: 5px;";
      if (is_mobile_layout) {
        cssText = "display: flex; flex-direction: row; align-items: center; gap: 5px; color: #666460; font-weight: 400; line-height: inherit; font-size: 14px; cursor: pointer; margin-top: 10px;";
      }

      p.style.cssText = cssText;
      p.setAttribute("aria-describedby", "tooltip");

      // Create the SVG element
      var svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svg.setAttribute("width", "18");
      svg.setAttribute("height", "18");
      svg.setAttribute("viewBox", "0 0 16 16");
      svg.setAttribute("fill", "none");

      // SVG path
      var path = document.createElementNS("http://www.w3.org/2000/svg", "path");
      path.setAttribute("d", "M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z");
      path.setAttribute("fill", "url(#paint0_radial_304_117_result" + indexName + ")");

      // Another SVG path
      var path2 = document.createElementNS("http://www.w3.org/2000/svg", "path");
      path2.setAttribute("d", "M4.57141 7.51327L7.06845 10.2857L12 5.71423");
      path2.setAttribute("stroke", "white");
      path2.setAttribute("stroke-width", "1.58267");
      path2.setAttribute("stroke-miterlimit", "10");

      // SVG defs and radialGradient
      var defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
      var radialGradient = document.createElementNS("http://www.w3.org/2000/svg", "radialGradient");
      radialGradient.id = "paint0_radial_304_117_result" + indexName;
      radialGradient.setAttribute("cx", "0");
      radialGradient.setAttribute("cy", "0");
      radialGradient.setAttribute("r", "1");
      radialGradient.setAttribute("gradientUnits", "userSpaceOnUse");
      radialGradient.setAttribute("gradientTransform", "translate(8 8.57143) rotate(90) scale(9.14286)");
      var stop1 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
      stop1.setAttribute("stop-color", "#46B0ED");
      var stop2 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
      stop2.setAttribute("offset", "1");
      stop2.setAttribute("stop-color", "#1A81BC");

      // Assemble the SVG elements
      radialGradient.appendChild(stop1);
      radialGradient.appendChild(stop2);
      defs.appendChild(radialGradient);
      svg.appendChild(defs);
      svg.appendChild(path);
      svg.appendChild(path2);
      p.appendChild(svg);

      // Text node for "SnapVerified"
      var textNode = document.createTextNode("SnapVerified");
      p.appendChild(textNode);

      return p;
    }

    function createSnapVerifiedTooltip(index, is_mobile_layout=false) {
      // Get the original element by its ID
      var originalElement = document.getElementById("snapverifiedtooltip");

      if (originalElement) {
          // Clone the original element
          indexName = is_mobile_layout ? "mobile" + index : "" + index;

          var clonedElement = originalElement.cloneNode(true);

          var newHtml = clonedElement.innerHTML.replace(/paint0_radial_304_117/g, "paint0_radial_304_117_result" + indexName);
          clonedElement.innerHTML = newHtml;

          // Change the ID of the cloned element
          clonedElement.id = is_mobile_layout ? "snapverifiedtooltipmobile" + index : "snapverifiedtooltip" + index;

          // Append the cloned element to the same parent
          originalElement.parentNode.appendChild(clonedElement);
      }
    }

    function initializeSnapVerifiedBadgeFeaturedAd() {
        const snapverifiedbutton = document.querySelector('#snapverifiedfeatured');
        const snapverifiedtooltip = document.querySelector('#snapverifiedtooltipfeatured');

        const popperInstance = Popper.createPopper(snapverifiedbutton, snapverifiedtooltip, {
            placement: 'bottom',
            modifiers: [
                {
                    name: 'offset',
                    options: {
                        offset: [0, 18],
                    },
                },
                {
                    name: 'flip',
                    options: {
                        enabled: false, // This disables the flipping behavior
                    },
                },
            ],
        });

        function show() {
            snapverifiedtooltip.setAttribute('data-show', '');

            // We need to tell Popper to update the tooltip position
            // after we show the tooltip, otherwise it will be incorrect
            popperInstance.update();
        }

        function hide() {
            snapverifiedtooltip.removeAttribute('data-show');
        }

        const showEvents = ['mouseenter', 'focus'];
        const hideEvents = ['mouseleave', 'blur'];

        showEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, show);
        });

        hideEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, hide);
        });
    }

    function initializeSnapVerifiedBadgeInlineAd() {
        const snapverifiedbutton = document.querySelector('#snapverifiedinline');
        const snapverifiedtooltip = document.querySelector('#snapverifiedtooltipinline');

        const popperInstance = Popper.createPopper(snapverifiedbutton, snapverifiedtooltip, {
            placement: 'bottom',
            modifiers: [
                {
                    name: 'offset',
                    options: {
                        offset: [0, 18],
                    },
                },
                {
                    name: 'flip',
                    options: {
                        enabled: false, // This disables the flipping behavior
                    },
                },
            ],
        });

        function show() {
            snapverifiedtooltip.setAttribute('data-show', '');

            // We need to tell Popper to update the tooltip position
            // after we show the tooltip, otherwise it will be incorrect
            popperInstance.update();
        }

        function hide() {
            snapverifiedtooltip.removeAttribute('data-show');
        }

        const showEvents = ['mouseenter', 'focus'];
        const hideEvents = ['mouseleave', 'blur'];

        showEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, show);
        });

        hideEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, hide);
        });
    }

    function initializeSnapVerifiedBadgeAdLeft() {
        const snapverifiedbutton = document.querySelector('#snapverifiedlefttopline');
        const snapverifiedtooltip = document.querySelector('#snapverifiedtooltiplefttopline');

        const popperInstance = Popper.createPopper(snapverifiedbutton, snapverifiedtooltip, {
            placement: 'bottom',
            modifiers: [
                {
                    name: 'offset',
                    options: {
                        offset: [0, 18],
                    },
                },
                {
                    name: 'flip',
                    options: {
                        enabled: false, // This disables the flipping behavior
                    },
                },
            ],
        });

        function show() {
            snapverifiedtooltip.setAttribute('data-show', '');

            // We need to tell Popper to update the tooltip position
            // after we show the tooltip, otherwise it will be incorrect
            popperInstance.update();
        }

        function hide() {
            snapverifiedtooltip.removeAttribute('data-show');
        }

        const showEvents = ['mouseenter', 'focus'];
        const hideEvents = ['mouseleave', 'blur'];

        showEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, show);
        });

        hideEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, hide);
        });
    }

    function initializeSnapVerifiedBadgeAdRight() {
        const snapverifiedbutton = document.querySelector('#snapverifiedrighttopline');
        const snapverifiedtooltip = document.querySelector('#snapverifiedtooltiprighttopline');

        const popperInstance = Popper.createPopper(snapverifiedbutton, snapverifiedtooltip, {
            placement: 'bottom',
            modifiers: [
                {
                    name: 'offset',
                    options: {
                        offset: [0, 18],
                    },
                },
                {
                    name: 'flip',
                    options: {
                        enabled: false, // This disables the flipping behavior
                    },
                },
            ],
        });

        function show() {
            snapverifiedtooltip.setAttribute('data-show', '');

            // We need to tell Popper to update the tooltip position
            // after we show the tooltip, otherwise it will be incorrect
            popperInstance.update();
        }

        function hide() {
            snapverifiedtooltip.removeAttribute('data-show');
        }

        const showEvents = ['mouseenter', 'focus'];
        const hideEvents = ['mouseleave', 'blur'];

        showEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, show);
        });

        hideEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, hide);
        });
    }

    function initializeSnapVerifiedBadgeDualTopLineAd() {
      initializeSnapVerifiedBadgeAdLeft();
      initializeSnapVerifiedBadgeAdRight();
    }

    function initializeSnapVerifiedBadgeDistributorAd() {
        const snapverifiedbutton = document.querySelector('#snapverifieddigikey');
        const snapverifiedtooltip = document.querySelector('#snapverifiedtooltipdigikey');

        const popperInstance = Popper.createPopper(snapverifiedbutton, snapverifiedtooltip, {
            placement: 'bottom',
            modifiers: [
                {
                    name: 'offset',
                    options: {
                        offset: [0, 18],
                    },
                },
                {
                    name: 'flip',
                    options: {
                        enabled: false, // This disables the flipping behavior
                    },
                },
            ],
        });

        function show() {
            snapverifiedtooltip.setAttribute('data-show', '');

            // We need to tell Popper to update the tooltip position
            // after we show the tooltip, otherwise it will be incorrect
            popperInstance.update();
        }

        function hide() {
            snapverifiedtooltip.removeAttribute('data-show');
        }

        const showEvents = ['mouseenter', 'focus'];
        const hideEvents = ['mouseleave', 'blur'];

        showEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, show);
        });

        hideEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, hide);
        });
    }

    function initializeSnapVerifiedBadge(searchLen, is_small_layout=false) {
      for (i = 0; i < searchLen; i++) {
        // Script for SnapVerified Badge
        const badgeId = is_small_layout ? "snapverifiedmobile" + i : "snapverified" + i;
        const tooltipId = is_small_layout? "snapverifiedtooltipmobile" + i : "snapverifiedtooltip" + i;
        const snapverifiedbutton = document.querySelector('#' + badgeId);
        const snapverifiedtooltip = document.querySelector('#' + tooltipId);

        const popperInstance = Popper.createPopper(snapverifiedbutton, snapverifiedtooltip, {
            placement: 'bottom',
            modifiers: [
              {
                name: 'offset',
                options: {
                    offset: [0, 18],
                  },
              },
              {
                name: 'flip',
                options: {
                    enabled: false, // This disables the flipping behavior
                },
              },
            ],
        });

        function show() {
            snapverifiedtooltip.setAttribute('data-show', '');

            // We need to tell Popper to update the tooltip position
            // after we show the tooltip, otherwise it will be incorrect
            popperInstance.update();
        }

        function hide() {
            snapverifiedtooltip.removeAttribute('data-show');
        }

        const showEvents = ['mouseenter', 'focus'];
        const hideEvents = ['mouseleave', 'blur'];

        showEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, show);
        });

        hideEvents.forEach((event) => {
            snapverifiedbutton.addEventListener(event, hide);
        });
      }


      try {
          initializeSnapVerifiedBadgeFeaturedAd();
      } catch (e) {
          console.error('Error in initializeSnapVerifiedBadgeFeaturedAd:', e);
      }

      try {
          initializeSnapVerifiedBadgeInlineAd();
      } catch (e) {
          console.error('Error in initializeSnapVerifiedBadgeInlineAd:', e);
      }

      try {
          initializeSnapVerifiedBadgeDistributorAd();
      } catch (e) {
          console.error('Error in initializeSnapVerifiedBadgeDistributorAd:', e);
      }

      try {
        initializeSnapVerifiedBadgeDualTopLineAd();
      } catch (e) {
        console.error('Error in initializeSnapVerifiedBadgeDualTopLineAd:', e);
      }

    }

    function runQueryAsync () { // load json file using jquery ajax
		$.getJSON(window.location.protocol + '//www.snapeda.com/api/v1/' + searchEndpoint + '?q=APX803L20-30SA-7&amp;page=1', function (searchresult, textStatus, jqXHR) {
            $('#searching').animate({'margin-top':'0', 'margin-bottom':'0'}, 300, function(){
				$('#searching').remove();
            });
            //Logic for determine if we need to show paginator
            doneWaiting = true;
            var zero_hits = true;
            if (searchresult.hits > 0){
                zero_hits = false;
            }
            var base_prop = {
                'SearchQuery': 'APX803L20\u002D30SA\u002D7',
                'Package Filter Selected': 'no',
                'Mounting Type Filter Selected': 'no',
                'Dimension Filter Selected': 'no',
                'Symbol Filter Selected': 'no',
                'Footprint Filter Selected': 'no',
                '3D Model Filter Selected': 'no',
                'Simulation Model Filter Selected': 'no',
                'type': "Part",
                'connector_ref': 'None', 'userid' : '',
                'isauthenticated': 'CallableBool(False)',
                'Datasheet Fiter Selected': $.urlParam('has_datasheet') === '1' ? 'yes' : 'no',
                'Manufacturer Fiter Selected':  $.urlParam('manufacturer') ,
                'ComplianceLeads Fiter Selected': $.urlParam('leadFilter') === '1' ? 'yes' : 'no',
                'CompliacneROHS Fiter Selected': $.urlParam('RoHSFilter') === '1' ? 'yes' : 'no',
                'All Fiter Selected': $.urlParam('stockFilter') !== '1' ? 'yes' : 'no',
                'InStock Fiter Selected': $.urlParam('stockFilter') === '1' ? 'yes' : 'no',
                'a_b_version': 'b',
                'query_type': searchresult.query_type,
                'zero_hits': zero_hits,
                'num_results': searchresult.hits,
                'ip_address': '2607:fb91:1b4f:5525:1c99:dbb4:7310:8952',
                '_dbg_http_code': jqXHR.status,
                '_dbg_http_status': jqXHR.statusText,
                '_dbg_http_error': searchresult.error,
                '_dbg_http_message': searchresult.message
            };
            var search_ad_props = searchresult.search_ad_props;
            // IE does not support Object.assign so let's mix those Jsons Objs
            for (key in base_prop) {
                search_ad_props[key] = base_prop[key];
            }
            var properties = search_ad_props;
            mixpanel.track('Search', properties);
            if (searchresult.error != null && searchresult.error.length > 0) {

            }
            else if (searchresult.message != null && searchresult.message.length > 0) {
                var x=document.getElementById('message');
                x.innerHTML=searchresult.message;
                document.getElementById('message').style.visibility = 'visible';
            }
            else if (searchresult.type === 'part' && typeof searchresult.results != "undefined" && searchresult.results != null && searchresult.results.length > 0) {
                var orderFragement = doc.createDocumentFragment();
                var orderDiv = doc.createElement('div');
                var fragment = doc.createDocumentFragment();
                var thead = doc.createElement('thead');
                var tr = doc.createElement('tr');
                var headers = ["Manufacturer", "Image", "Part", "Package", "Availability", "Avg. Price <span>(USD)</span>", "Description", "Data Available"];
                for (i = 0; i < headers.length; i++) {
                    var td = doc.createElement('th');
                    td.innerHTML = headers[i];
                    td.setAttribute('id','col-' + headers[i].toLowerCase().replace(/ /g, '-'))
                    tr.appendChild(td);
                }
                thead.appendChild(tr);
                fragment.appendChild(thead);

                var tbody = doc.createElement('tbody');
                fragment.appendChild(tbody);
                searchLen = searchresult.results.length;
                for (i = 0; i < searchresult.results.length; i++) {
                    var part = searchresult.results[i];
                    var tr = doc.createElement('tr');
                    tr.id = 'result' + i;

                    // Add class and unipart score data attribute
                    tr.classList.add('search-result-row');
                    if (part.search_score !== undefined) {
                      tr.setAttribute('data-search-score', part.search_score)
                      tr.setAttribute('data-unipart-id', part.unipart_id)
                    }

                    // Manufacturer logo / name
                    var mfg = doc.createElement('td');
                    mfg.className = 'part-result manufacturer_mobile';
                    mfg.width = '100px';
                    mfg.height = '35px';
                    mfg.style.verticalAlign = 'middle';

                    var img = doc.createElement('img');
                    var companyLogoURL = part['organization_image_100_20'];
                    img.src = companyLogoURL;
                    img.className = 'manufacturer_mobile_img';
                    img.title = part['manufacturer'];
                    img.alt = part['manufacturer'];
                    img.onerror = function(){
                        // text placeholder displayed if 3rd party image is not found
                        this.style.display = 'none';
                        $(this).after( $(this).attr('title') );
                    };
                    img.style.maxWidth = '80px';
                    mfg.appendChild(img);

                    var part_pic = doc.createElement('td');
                    part_pic.className = 'part-result';
                    part_pic.width = '130px';
                    part_pic.height = '35px';
                    part_pic.style.verticalAlign = 'middle';

                    // Part image
                    var img = doc.createElement('img');
                    img.id = part['unipart_id']
                    img.src = part['image']
                    img.title = part['name'];
                    img.alt = part['name'] ? part['name'] : 'Part Pic' ;
                    img.style.height = '60px';
                    img.className = 'part-image';

                    img.onerror = function(){
                        img.onerror = null
                        getFallbackImage(part['name'], part['manufacturer'], function(imgUrl){
                            $('img#'+part['unipart_id'],$(part_pic)).attr('src',imgUrl)
                        })
                    };
                    var img_mobile=img;
                    part_pic.appendChild(img_mobile.cloneNode(true));

                    var name = doc.createElement('td');
                    name.className='part-result2';
                    name.width = '150px';
                    name.style.verticalAlign = 'middle';
                    var ahref = doc.createElement('a');
                    if (!part['has_footprint']){
                        ahref.className = 'noFootprint';
                    }
                    /* TODO this should be data items*/
                    
                    ahref.href = window.location.protocol + '//www.snapeda.com/parts/' + part['urlname'] + '/' + encodeURIComponent(part['manufacturer']) + '/view-part' + '/?ref=search&t=APX803L20-30SA-7';
                    // Add ab_test_case to the URL for regular search results
                    if (typeof searchresult !== 'undefined' && searchresult &&
                        typeof searchresult.search_ad_props !== 'undefined' && searchresult.search_ad_props &&
                        typeof searchresult.search_ad_props.ab_test_case !== 'undefined' && searchresult.search_ad_props.ab_test_case) {
                        ahref.href = ahref.href + '&ab_test_case=' + searchresult.search_ad_props.ab_test_case;
                    }
                    ahref.setAttribute('data-uid', part['uniqueid']);
                    ahref.setAttribute('data-name', part['name']);
                    ahref.setAttribute('data-manufacturer', part['manufacturer']);
                    ahref.appendChild(doc.createTextNode(part['name']));
                    name.appendChild(ahref.cloneNode(true));

                    // Append SnapVerified Badge
                    show_snapverified_badge = part.te_param && part.te_param.snap_verified
                    var snapverifiedbadge = createSnapVerifiedBadge(i, show_snapverified_badge);
                    createSnapVerifiedTooltip(i);
                    name.appendChild(snapverifiedbadge);

                    // Create SnapVerified Badge for mobile layout
                    var snapverifiedbadgemobile = createSnapVerifiedBadge(i, show_snapverified_badge, true);
                    createSnapVerifiedTooltip(i, true);

                    var mobile_div1 = doc.createElement('div');
                    var mobile_div2 = doc.createElement('div');
                    var mobile_div3 = doc.createElement('div');
                    var mobile_span = doc.createElement('span');
                    img_mobile.style.width='110px';
                    img_mobile.style.height='auto';
                    img_mobile.style.margin='0px';
                    img_mobile.style.display='initial';
                    mobile_div1.appendChild(img_mobile.cloneNode(true));
                    ahref.style.display='block';
                    mobile_div2.appendChild(ahref.cloneNode(true));
                    mobile_span.appendChild(doc.createTextNode('by ' + part['manufacturer']));
                    mobile_span.style.display='inline-flex';
                    mobile_span.style.width='170px';
                    mobile_div3.style.textAlign = 'initial';
                    mobile_div1.style.display = 'inline-block';
                    mobile_div1.style.textAlign = 'initial';
                    mobile_div2.className = 'title_container';
                    mobile_div2.appendChild(mobile_span);
                    mobile_div2.appendChild(snapverifiedbadgemobile);
                    mobile_div3.appendChild(mobile_div1);
                    mobile_div3.appendChild(mobile_div2);
                    mobile_div3.className='mobile_head_content';
                    mfg.appendChild(mobile_div3);
                    tr.appendChild(mfg);
                    tr.appendChild(part_pic);
                    tr.appendChild(name);

                    var package_case = doc.createElement('td');
                    package_case.appendChild(doc.createTextNode(part['package_type'].includes('---')?'Custom':part['package_type']));
                    package_case.className='part-result';
                    package_case.style = 'vertical-align:middle;';
                    tr.appendChild(package_case);

                    var avail = doc.createElement('td');
                    avail.className='part-result';
                    avail.id = 'avail' + i;
                    avail.setAttribute('data-toggle', 'tooltip');
                    avail.width = '80px';
                    avail.style.verticalAlign = 'middle';

                    var price = doc.createElement('td');
                    price.className='part-result price-result';
                    price.id = 'price' + i;
                    price.style =  'vertical-align:middle;font-weight:bold;width:100px;text-align:center;color: #0088cc';

                    if(part['availability'] === 'AJAX'){
                        var loadingAvail = doc.createElement('div');
                        loadingAvail.className = "avail_icon loading_icon";
                        loadingAvail.id = ("loadingAvail" + i);

                        var iconAvail = doc.createElement('i');

                        iconAvail.className = "fa fa-spinner fa-pulse fa-fw";
                        var spanAvail = doc.createElement('span');
                        spanAvail.className = "sr_only";
                        spanAvail.appendChild(doc.createTextNode('Loading...'));
                        loadingAvail.appendChild(iconAvail);
                        avail.appendChild(loadingAvail)
                        tr.appendChild(avail);

                        var loadingPrice = doc.createElement('div');
                        loadingPrice.className = "avail_icon loading_icon";
                        loadingPrice.id = ("loadingPrice" + i)

                        var iconPrice = doc.createElement('i');

                        iconPrice.className = "fa fa-spinner fa-pulse fa-fw";
                        var spanPrice = doc.createElement('span');
                        spanPrice.className = "sr_only";
                        spanPrice.appendChild(doc.createTextNode('Loading...'))
                        loadingPrice.appendChild(iconPrice);
                        price.appendChild(loadingPrice);
                        tr.appendChild(price);

                    }
                    else{
                        var availImg = doc.createElement('div');
                        availImg.setAttribute('data-toggle', 'tooltip');
                        availImg.className = "avail_icon";
                        var availability = part['availability'];

                        if (availability === '') {
                            //availImg.src = "/static/img/search_icons/bad.png";
                            var icon = doc.createElement('i');
                            icon.className = "fa fa-exclamation-triangle";
                            availImg.appendChild(icon);

                            availImg.title='No availability data';
                            availImg.alt='No availability data';
                        } else if (availability === 'Unavailable') {
                            //availImg.src = "/static/img/search_icons/bad.png";
                            var icon = doc.createElement('i');
                            icon.className = "fa fa-times-circle";
                            availImg.appendChild(icon);
                            availImg.title='Not in stock';
                            availImg.alt='Not in stock';
                        } else {
                            var icon = doc.createElement('i');
                            icon.className = "fa fa-check-circle";
                            availImg.appendChild(icon);

                            availImg.title='In Stock';
                            availImg.alt='In Stock';
                        }

                        availImg.style.marginLeft = '21px';
                        availImg.style.marginRight = '21px';
                        availImg.style.height = '27px';
                        avail.appendChild(availImg);
                        tr.appendChild(avail);

                        var priceTextholder = doc.createElement('p');
                        var priceText = doc.createTextNode(part['average_price']);
                        priceTextholder.appendChild(priceText);
                        priceTextholder.setAttribute('data-toggle', 'tooltip');
                        priceTextholder.title='Average price from distributors';
                        price.appendChild(priceTextholder);

                        tr.appendChild(price);
                    }

                    var buyUrl = undefined;
                    var analogDevicesBuyUrl = undefined;
                    var part_tp_ = part.te_param;
                    var offers_ = part.offers ? part.offers : [];
                    // check if offers is already set, if not get them from te_param.Offers if it's not an array make it
                   // an array or just use it if it's an array with info
                    if (!offers_.length) {
                      offers_ = (typeof part_tp_ === "object" && part_tp_.Offers && !part_tp_.Offers.length) ?
                        [part_tp_.Offers] : (typeof part_tp_ === "object" && part_tp_.Offers &&
                          part_tp_.Offers.length) ? part_tp_.Offers : [];
                    }

                    resultParts.push(part);
                    offers_.forEach(function(offer){
                        if(offer.seller_name === 'Texas Instruments') {
                            buyUrl = offer.buy_url;
                        }
                        if(offer.seller_name === "Analog Devices"){
                          // Disable buy button of Analog Devices in search page
                          analogDevicesBuyUrl = undefined; // offer.buy_url
                        }
                        if (offer.seller_name !== undefined) {
                            var sellerNameArray = offer.seller_name.split(' ');
                            if (sellerNameArray.length > 0 && offer.logo_url === undefined) {
                                var index = -1;
                                sellerNameArray.some(function (x, i) {
                                    if (part.manufacturer.indexOf(x) >= 0) {
                                        index = i;
                                        return true;
                                    }
                                });
                                //sellerNameArray.findIndex( x => part.manufacturer.indexOf(x) >= 0 );
                                offer['logo_url'] = index === -1 ? undefined : part.organization_image_100_20;
                            }
                        }
                    });

                    var bannerHtml = '';
                    // if(buyUrl) {
                    //   tiBanner = true;
                    //   bannerHtml = '<div class="callout-banner"><div class="description-text">' + part['short_description'] + '</div><div class="div-banner"><div class="manuf-banner"  data-part="'+ part.name +'" data-manufacturer="Texas Instrument"> <a href="' + buyUrl + '" target="_blank"> <img src="/static/img/manufacturer-logos/texas-instrument-sm.png"> Buy on TI.com </a></div></div></div>'+'<div class="div-banner-mobile div-banner"><div class="manuf-banner"  data-part="'+ part.name +'" data-manufacturer="Texas Instrument"> <a href="' + buyUrl + '" target="_blank"> <img src="/static/img/manufacturer-logos/texas-instrument-sm.png"> Buy on TI.com </a></div></div>';
                    // }
                    if(analogDevicesBuyUrl) {
                      ADBanner = true;
                      var analogDevicesLogo = "/static/img/manufacturer-logos/Analog%20Devices%20Logo.png";
                      bannerHtml = '<div class="callout-banner"><div class="description-text">' + part['short_description'] + '</div><div class="div-banner"><div class="manuf-banner manuf-banner-ew"  data-part="'+ part.name +'" data-manufacturer="Analog Devices"> <a href="' + analogDevicesBuyUrl + '" target="_blank"> <img class="analog-d-logo" src="'+ analogDevicesLogo +'">Buy on Analog.com</a></div></div></div>'+'<div class="div-banner-mobile div-banner"><div class="manuf-banner manuf-banner-ew"  data-part="'+ part.name +'" data-manufacturer="Analog Devices"> <a href="' + analogDevicesBuyUrl + '" target="_blank"> <img class="analog-d-logo" src="'+ analogDevicesLogo +'">Buy on Analog.com</a></div></div>';
                    }
                    // Show optin for Queltec and Superior Sensor
                    var hasRefDesign = false;
                    var hasOrderSample = false;
                    var refManuf = part.manufacturer.toUpperCase();
                    // if (refManuf.indexOf('QUECTEL') !== -1 && part.te_param && searchresult.show_ref_dict['QUECTEL']) {
                    //   const quectelCtaTeParamKeys = ['Specifications', 'Product Brochure', 'AT Commands Manual', 'EVB Kit', 'Application Note', 'User Guide', 'Ref_hardware', 'Ref_specifications']
                    //   const existingKey  = quectelCtaTeParamKeys.find(key => part.te_param.hasOwnProperty(key));
                    //   if (part.te_param[existingKey]) {
                    //     hasRefDesign = true;
                    //     const refDesignUrl = part.te_param[existingKey];
                    //     const ctaBtnText = existingKey !== 'Ref_hardware' ? (existingKey === 'Ref_specifications' ? 'Specifications' : existingKey) : 'Reference Design';
                    //     bannerHtml = '<div class="callout-banner"><div class="description-text">' + part['short_description'] + '</div><div class="div-banner"><div class="manuf-banner manuf-banner-ew" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url="' + refDesignUrl + '"> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="' + refDesignUrl + '" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))" data-resourcekey="' + ctaBtnText + '"> <i class="fa fa-microchip"></i> ' + ctaBtnText + ' </a></div></div></div>'+'<div class="div-banner-mobile div-banner"><div class="manuf-banner" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url="' + refDesignUrl + '"> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="' + refDesignUrl + '" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))" data-resource="' + ctaBtnText + '"> <i class="fa fa-microchip"></i> ' + ctaBtnText + ' </a></div></div>';
                    //   } else if (part.te_param['specs']) {
                    //     const quectelCtaTeParamKeys = ['Specifications', 'Product Brochure', 'AT Commands Manual', 'EVB Kit', 'Application Note', 'User Guide', 'Ref_hardware', 'Ref_specifications']
                    //     const existingKey  = quectelCtaTeParamKeys.find(key => part.te_param['specs'].hasOwnProperty(key));
                    //     if (part.te_param['specs'][existingKey]) {
                    //       hasRefDesign = true;
                    //       const refDesignUrl = part.te_param['specs'][existingKey];
                    //       const ctaBtnText = existingKey !== 'Ref_hardware' ? (existingKey === 'Ref_specifications' ? 'Specifications' : existingKey) : 'Reference Design';
                    //       bannerHtml = '<div class="callout-banner"><div class="description-text">' + part['short_description'] + '</div><div class="div-banner"><div class="manuf-banner manuf-banner-ew" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url="' + refDesignUrl + '"> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="' + refDesignUrl + '" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))" data-resourcekey="' + ctaBtnText + '"> <i class="fa fa-microchip"></i> ' + ctaBtnText + ' </a></div></div></div>'+'<div class="div-banner-mobile div-banner"><div class="manuf-banner" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url="' + refDesignUrl + '"> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="' + refDesignUrl + '" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))" data-resource="' + ctaBtnText + '"> <i class="fa fa-microchip"></i> ' + ctaBtnText + ' </a></div></div>';
                    //     }
                    //   }
                    // }

                    if((refManuf.indexOf('MPD') !== -1) && searchresult.show_ref_dict['MPD']) {
                      hasOrderSample = true;
                      bannerHtml = '<div class="callout-banner"><div class="description-text">' + part['short_description'] + '</div><div class="div-banner"><div class="manuf-banner" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url=""> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))"> <i class="fa fa-gift"></i> Free Samples </a></div></div></div>'+'<div class="div-banner-mobile div-banner"><div class="manuf-banner" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url=""> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))"> <i class="fa fa-gift"></i> Free Samples </a></div></div>';
                    }

                    if(refManuf.indexOf('THALES') !== -1 && part.te_param && part.te_param['Extra Resources'] && part.show_search_link && (part.te_param['Extra Resources']['Hardware Interface'] || part.te_param['Extra Resources']['AT Commands Manual']) && searchresult.show_ref_dict['THALES']) {
                      hasRefDesign = true;
                      const refDesignUrl = part.te_param['Extra Resources']['AT Commands Manual'] || part.te_param['Extra Resources']['Hardware Interface'];
                      const resourceKey = part.te_param['Extra Resources']['AT Commands Manual'] ? 'AT Commands Manual' : 'Hardware Interface';
                      bannerHtml = '<div class="callout-banner"><div class="description-text">' + part['short_description'] + '</div><div class="div-banner"><div class="manuf-banner manuf-banner-ew" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url="' + refDesignUrl + '"> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="' + refDesignUrl + '" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" data-resourcekey="' + resourceKey + '" onclick="open_quectel_ad_lead_form($(this))"> <i class="fa fa-microchip"></i>' + resourceKey + '</a></div></div></div>'+'<div class="div-banner-mobile div-banner"><div class="manuf-banner" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url="' + refDesignUrl + '"> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="' + refDesignUrl + '" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))"> <i class="fa fa-microchip"></i> Reference Design </a></div></div>';
                    }
                    // if((refManuf.indexOf('NICOMATIC') !== -1) && searchresult.show_ref_dict['SUPERIOR SENSOR']) {
                    //   hasOrderSample = true;
                    //   bannerHtml = '<div class="callout-banner"><div class="description-text">' + part['short_description'] + '</div><div class="div-banner"><div class="manuf-banner" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url=""> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))"> <i class="fa fa-gift"></i> Free Samples </a></div></div></div>'+'<div class="div-banner-mobile div-banner"><div class="manuf-banner" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url=""> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))"> <i class="fa fa-gift"></i> Free Samples </a></div></div>';
                    // }

                    // if(refManuf.indexOf('ABRACON') !== -1) {
                    //   hasOrderSample = true;
                    //   bannerHtml = '<div class="callout-banner"><div class="description-text">' + part['short_description'] + '</div><div class="div-banner"><div class="manuf-banner" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url=""> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))"> <i class="fa fa-gift"></i> Free Samples </a></div></div></div>'+'<div class="div-banner-mobile div-banner"><div class="manuf-banner" data-part="'+ part.name +'" data-manufacturer="' + part["manufacturer"] + '" data-url=""> <a target="_blank" data-unipart-id="' + part["unipart_id"] + '" data-lead-url="" data-tracking-location="In-line organic search" data-type="' + part["manufacturer"] + '" onclick="open_quectel_ad_lead_form($(this))"> <i class="fa fa-gift"></i> Free Samples </a></div></div>';
                    // }

                    var desc = doc.createElement('td');
                    var test = 'b';
                    if(buyUrl || hasRefDesign || hasOrderSample || analogDevicesBuyUrl) {
                      desc.innerHTML = bannerHtml;
                    } else {
                      desc.appendChild(doc.createTextNode(part['short_description']));
                    }
                    desc.className='part-result' + ((buyUrl || hasRefDesign || hasOrderSample || analogDevicesBuyUrl) ? ' ti-banner' : '');
                    desc.style = 'vertical-align:middle;';
                    tr.appendChild(desc);

                    var data = doc.createElement('td');
                    data.className='part-result';
                    // Replaced includes for indexOf for IE compatibility
                    if(part['manufacturer'].toLowerCase().indexOf('recom')>= 0 || part['manufacturer'].indexOf('Samtec')>= 0 || (part['manufacturer'].indexOf('CUI')>=0)){
                        data.width = '200px';

                    }else{
                        data.width = '160px';
                    }

                    data.style.verticalAlign = 'middle';
                    data.style.position = 'relative';
                    data.id = 'data' + i;

                    var dataImg = doc.createElement('img');
                    dataImg.id = 'has_datasheet' + i;
                    if(part['has_datasheet'] == 1){
                      dataImg.src = "/static/img/search_icons/datasheet_orange.png";
                      dataImg.title = "Datasheet available";
                      dataImg.alt = "Datasheet available";
                    }
                    else{
                      dataImg.src = "/static/img/search_icons/datasheet_outline.png";
                      dataImg.title = "Datasheet not available";
                      dataImg.alt = "Datasheet not available";
                    }
                    dataImg.setAttribute('data-toggle','tooltip');
                    dataImg.className = "data-icon";
                    dataImg.style.height = '19px';
                    data.appendChild(dataImg);


                    var dataImg = doc.createElement('img');
                    dataImg.id = 'has_symbol' + i;
                    if(part['has_symbol'] == 1){
                      dataImg.src = "/static/img/search_icons/symbol_orange.png";
                      dataImg.title = "Symbol available";
                      dataImg.alt = "Symbol available";
                    }else{
                      dataImg.src = "/static/img/search_icons/symbol_outline.png";
                      dataImg.title = "Symbol not available";
                      dataImg.alt = "Symbol not available";
                    }
                    dataImg.setAttribute('data-toggle','tooltip');
                    dataImg.style.height = '21px';
                    dataImg.className = "data-icon";
                    data.appendChild(dataImg);

                    var dataImg = doc.createElement('img');
                    dataImg.id = 'has_footprint' + i;
                    if(part['has_footprint'] == 1){
                      dataImg.src = "/static/img/search_icons/footprint_orange.png";
                      dataImg.title = "Footprint available";
                      dataImg.alt = "Footprint available";
                    }else{
                      dataImg.src = "/static/img/search_icons/footprint_outline.png";
                      dataImg.title = "Footprint not available";
                      dataImg.alt = "Footprint not available";
                    }
                    dataImg.setAttribute('data-toggle','tooltip');
                    dataImg.style.height = '19px';
                    dataImg.className = "data-icon";
                    data.appendChild(dataImg);

                    var dataImg = doc.createElement('img');
                    dataImg.id = 'has_3d' + i;

                    if(part['has_3d'] == 1){
                      dataImg.src = "/static/img/search_icons/3d_model_orange.png";
                      dataImg.title = "3D model available";
                      dataImg.alt = "3D model available";
                    }else{
                      dataImg.src = "/static/img/search_icons/3d_outline.png";
                      dataImg.title = "3D not available";
                      dataImg.alt = "3D not available";
                    }
                    dataImg.setAttribute('data-toggle','tooltip');
                    dataImg.style.height = '19px';
                    dataImg.className = "data-icon";
                    data.appendChild(dataImg);

                    var dataImg = doc.createElement('img');
                    dataImg.id = 'has_sim' + i;
                    if(part['has_sim'] == 1){
                        dataImg.src = "/static/img/search_icons/sim_orange.png";
                        dataImg.title = "Sim available";
                        dataImg.alt = "Sim available";
                    }else{
                        dataImg.src = "/static/img/search_icons/sim_dotted_orange.png";
                        dataImg.title = "Sim not available";
                        dataImg.alt = "Sim not available";
                    }
                    dataImg.setAttribute('data-toggle','tooltip');
                    dataImg.style.height = '19px';
                    dataImg.className = "data-icon";
                    data.appendChild(dataImg);

                    // Replaced includes for indexOf for IE compatibility
                    if((part['has_sample'] && part['has_sample'] != 'AJAX') || ((refManuf.indexOf('MPD') !== -1) && searchresult.show_ref_dict['MPD'])){
                        var dataImg = doc.createElement('img');
                        dataImg.id = 'samples' + i;
                        dataImg.src = "/static/img/search_icons/sample_orange.png";
                        dataImg.title = "Sample available";
                        dataImg.alt = "Sample available";
                        dataImg.setAttribute('data-toggle','tooltip');
                        dataImg.style.height = '19px';
                        dataImg.className = "data-icon";
                        data.appendChild(dataImg);
                    }

                    

                    tr.appendChild(data);
                    tbody.appendChild(tr);
                    var data = doc.createElement('td');
                    data.width = '100px';
                    data.style.verticalAlign = 'middle';
                    data.id = 'data' + part['uniqueid'];
                }

                var table = doc.createElement("table");
                table.className = 'table search-results-table';
                table.style.fontSize = '14px';
                table.appendChild(fragment);

                doc.getElementById("results").appendChild(table);

                for (i = 0; i < searchresult.results.length; i++) {
                    var part = searchresult.results[i];
                    var part_data = {
                      part_number: part['name'],
                      manufacturer: part['manufacturer'],
                      unique_id: part['uniqueid'],
                      unipart_id: part['unipart_id'],
                      has_datasheet: part['has_datasheet'],
                      has_unipart: true,
                      row_counter: i,
                      sample_current_status: part['has_sample'],
                    };
                    (function(i) {
                      $.getJSON("/api/get_more_resources/", part_data , function(json) {
                        var availability = String(json.availability);
                        var averagePrice = String(json.average_price);
                        var has_datasheet = String(json.has_datasheet);
                        var total = 0;
                        var num_items = 0;
                        var contact_us_available = false;

                        resultParts[i].offers = [];
                        if(json.offers && json.offers.length > 0) {
                          json.offers.forEach(function(offer){
                            resultParts[i].offers.push(offer);
                            if (averagePrice == 'undefined') {
                              for (price in offer['price']['USD']){
                                total += parseInt(price[1])
                                num_items += 1
                              }
                            }
                            if(offer && offer.quantity_available && offer.quantity_available.toLowerCase() == "contact us"){
                              contact_us_available=true;
                            }
                          });
                          if (num_items != 0) {
                            var avg = total / num_items
                            availability = 'Available'
                            averagePrice = avg;
                          }
                        } else {
                          availability = 'Unavailable'
                          averagePrice = 'N/A';
                        }

                        if(contact_us_available){
                            availability = 'Available'
                          }

                        if (averagePrice == 'undefined' && !offers.length) {
                          averagePrice = 'N/A';
                        }

                        var loadingAvailId = String("loadingAvail" + json.row_counter)
                        var loadingAvail = document.getElementById(loadingAvailId)
                        if(loadingAvail){
                          loadingAvail.parentNode.removeChild(loadingAvail) //Remove the loading icon
                        }

                        var availId = String("avail" + json.row_counter)
                        var avail = document.getElementById(availId)
                        avail.className='part-result';
                        avail.setAttribute('data-toggle', 'tooltip');
                        avail.width = '80px';
                        avail.style.verticalAlign = 'middle';
                        var availImg = doc.createElement('div');
                        availImg.setAttribute('data-toggle', 'tooltip');
                        availImg.className = "avail_icon";

                        if (availability == '' || availability == 'undefined') {
                          var icon = doc.createElement('i');
                          icon.className = "fa fa-exclamation-triangle";
                          availImg.appendChild(icon);

                          availImg.title='No availability data';
                          availImg.alt='No availability data';
                        }else if (availability == 'Unavailable') {
                          //availImg.src = "/static/img/search_icons/bad.png";
                          var icon = doc.createElement('i');
                          icon.className = "fa fa-times-circle";
                          availImg.appendChild(icon);

                          availImg.title='Not in stock';
                          availImg.alt='Not in stock';
                        }
                        else {
                          var icon = doc.createElement('i');
                          icon.className = "fa fa-check-circle";
                          availImg.appendChild(icon);

                          availImg.title='In Stock';
                          availImg.alt='In Stock';
                        }

                        availImg.style.marginLeft = '21px';
                        availImg.style.marginRight = '21px';
                        availImg.style.height = '27px';
                        if (averagePrice) {
                            if (avail.firstChild){avail.firstChild.remove()}
                            avail.appendChild(availImg);
                        }
                        //// Replace the loading icon with real price information
                        var loadingPriceId = String("loadingPrice" + json.row_counter)
                        var loadingPrice = document.getElementById(loadingPriceId)
                        if(loadingPrice){
                          loadingPrice.parentNode.removeChild(loadingPrice) //Remove the loading icon
                        }

                        var priceId = String("price" + json.row_counter)
                        var price = document.getElementById(priceId)
                        var priceTextholder = doc.createElement('p');
                        var priceText = doc.createTextNode(averagePrice)
                        priceTextholder.appendChild(priceText)
                        priceTextholder.setAttribute('data-toggle', 'tooltip');
                        priceTextholder.title='Average price from distributors';
                        if (averagePrice){
                            if (price.firstChild){price.firstChild.remove()}
                            price.appendChild(priceTextholder);
                        }

                        dataId = 'data' + json.row_counter
                        var data = document.getElementById(dataId)


                        if (has_datasheet == 1) {
                          var dataImgId = 'has_datasheet' + String(json.row_counter)
                          var dataImg = doc.getElementById(dataImgId);
                          dataImg.src = "/static/img/search_icons/datasheet_orange.png";
                          var dataImgIdHash = '#' + dataImgId
                          $(dataImgIdHash).attr('title', 'Datasheet available')
                            .tooltip('fixTitle')
                          dataImg.style.height = '19px';
                        }
                        if(json.sample_status === 'available' || json.sample_status === 'unavailable') {
                          var img = "/static/img/search_icons/sample_orange.png";
                          if(json.sample_status === 'unavailable') {
                            img = "/static/img/search_icons/sample_dotted.png";
                          }
                          var dataImg = doc.createElement('img');
                          dataImg.id = 'samples' + i;
                          dataImg.src = img;
                          dataImg.title = json.sample_status === 'available' ? "Sample available" : "Sample unavailable";
                          dataImg.alt = json.sample_status === 'available' ? "Sample available" : "Sample unavailable";
                          dataImg.setAttribute('data-toggle','tooltip');
                          dataImg.style.height = '19px';
                          dataImg.className = "data-icon";
                          $('#result' + i + ' #data' + i) .append(dataImg);
                        }

                        $(function () {
                          $('[data-toggle="tooltip"]').tooltip()
                        });
                      });
                    })(i);
                }

                // This for loop looks up symbol and footprint of octopart results with AJAX
                for (i = 0; i < searchresult.results.length; i++) {
                    var part = searchresult.results[i];
                    if (part['has_footprint'] == 'AJAX' || part['has_symbol'] == 'AJAX'){
                        var part_data = {
                          part_number: part['name'],
                          manufacturer: part['manufacturer'],
                          unique_id : part['uniqueid'],
                          unipart_id: part['unipart_id'],
                          pin_count: part['pin_count'],
                          has_datasheet: part['has_datasheet'],
                          package_type: part['package_type'],
                          row_counter: i,
                        }
                        $.getJSON("/api/get_has_sym_foot_3d/", part_data , function(json) {
                      var has_datasheet = Number(json.has_datasheet);
                      var has_symbol = Number(json.has_symbol);
                      var has_footprint = Number(json.has_footprint);
                      dataId = 'data' + json.row_counter;
                      var data = document.getElementById(dataId);

                      if (has_datasheet == 1) {
                        var dataImgId = 'has_datasheet' + String(json.row_counter);
                        var dataImg = doc.getElementById(dataImgId);
                        dataImg.src = "/static/img/search_icons/datasheet_orange.png";
                        var dataImgIdHash = '#' + dataImgId;
                        $(dataImgIdHash).attr('title', 'Datasheet available')
                          .tooltip('fixTitle');
                        dataImg.style.height = '19px';
                      }


                      if (has_symbol == 1) {
                        var dataImgId = 'has_symbol' + String(json.row_counter)
                        var dataImg = doc.getElementById(dataImgId);
                        dataImg.src = "/static/img/search_icons/symbol_orange.png";
                        var dataImgIdHash = '#' + dataImgId
                        $(dataImgIdHash).attr('title', 'Symbol available')
                          .tooltip('fixTitle')
                        dataImg.style.height = '21px';

                      }

                      if (has_footprint == 1) {
                        var dataImgId = 'has_footprint' + String(json.row_counter);
                        var dataImg = doc.getElementById(dataImgId);
                        dataImg.src = "/static/img/search_icons/footprint_orange.png";
                        var dataImgIdHash = '#' + dataImgId;
                        $(dataImgIdHash).attr('title', 'Footprint available')
                          .tooltip('fixTitle');
                        dataImg.style.height = '19px';
                      }
                    });
                    }
                }

                thereIsPreviousResult = searchresult['page'] > 1;
                if(thereIsPreviousResult){
                  var nextButton = doc.createElement('a');
                  nextButton.appendChild(doc.createTextNode('Previous'))
                  nextButton.href = '/search/?'+ qs_pagination + '&page=' + (searchresult['page']-1)
                  document.getElementById('paginator').appendChild(nextButton)

                }

                var pagesRequired = Math.ceil(searchresult['hits'] / 20)

                request_id = jqXHR.getResponseHeader('X-SNAP-REQUEST-ID')
                $('#request-ids')[0].innerText += ` ${request_id} `
                window.request_ids = (window.request_ids || []).push(request_id);

                if(!ASYNC_ADS) {setAds(searchresult, null, request_id);}
                if(searchresult['hits'] / 20 > 1){
                  if(searchresult['hits'] / 20 > 10){

                    if(searchresult['page'] >= 1 && searchresult['page'] <10){



                      for(i=0;i < 10;i++){
                        if(i+1 == searchresult['page']){
                          var span = doc.createElement('span');
                          span.appendChild(doc.createTextNode(i+1));
                          span.className = 'current-page';
                          document.getElementById('paginator').appendChild(span);

                        }else{
                          var ahref = doc.createElement('a');
                          ahref.appendChild(doc.createTextNode(i+1));
                          ahref.href = '/search/?' + qs_pagination + '&page=' + (i+1)
                          document.getElementById('paginator').appendChild(ahref);
                        }
                        }
                    }else{
                      var pageNumber = searchresult['page']
                      var initPage = parseInt(pageNumber/10)*10;
                      if((pagesRequired - initPage) > 11){
                        var pagesToRender = 11
                      }else{
                        var pagesToRender = 1 + pagesRequired - initPage
                      }
                      for(i=initPage; i<initPage+pagesToRender;i++){
                        if(i == searchresult['page']){
                          var span = doc.createElement('span');
                          span.appendChild(doc.createTextNode(i));
                          span.className = 'current-page';
                          document.getElementById('paginator').appendChild(span);

                        }else{
                          var ahref = doc.createElement('a');
                          ahref.appendChild(doc.createTextNode(i));
                          ahref.href = '/search/?' + qs_pagination + '&page=' + (i)
                          document.getElementById('paginator').appendChild(ahref);
                        }

                      }
                    }

                  }else {
                    // Create pagination between 1 and 10
                    var number_pages = Math.ceil(searchresult['hits'] / 20)
                    for(i=0;i < number_pages;i++){

                      if(i+1 == searchresult['page']){
                      var span = doc.createElement('span');
                      span.appendChild(doc.createTextNode(i+1));
                      span.className = 'current-page';
                      document.getElementById('paginator').appendChild(span);

                    }else{
                      var ahref = doc.createElement('a');
                      ahref.appendChild(doc.createTextNode(i+1));
                      ahref.href = '/search/?' + qs_pagination + '&page=' + (i+1)
                      document.getElementById('paginator').appendChild(ahref);
                    }
                  }
                  }

                  //Next Button
                  currentPage = searchresult['page']
                  thereIsMoreResults = Math.ceil(searchresult['hits'] / 20) > searchresult['page']
                  if(thereIsMoreResults){
                    var nextButton = doc.createElement('a');
                    nextButton.appendChild(doc.createTextNode('Next'))
                    nextButton.href = '/search/?' + qs_pagination + '&page=' + (searchresult['page']+1)
                    document.getElementById('paginator').appendChild(nextButton)
                  }

                }

                $(".part-result").hover(
                    function () {
                        $('body').css('cursor', 'pointer');
                    },
                    function () {
                        $('body').css('cursor', 'default');
                    }
                );

                $(".ad_buy").hover(
                    function () {
                        $('body').css('cursor', 'pointer');
                    },
                    function () {
                        $('body').css('cursor', 'default');
                    }
                );

                 $(".ad_view").hover(
                    function () {
                        $('body').css('cursor', 'pointer');
                    },
                    function () {
                        $('body').css('cursor', 'default');
                    }
                );

                $('.part-result').click(function (event) {

                  var clickRow = true;
                  if( $(event.currentTarget).attr('class').indexOf('price-result') >= 0 ) {
                    if($(event.target).is('p')) {
                      var id = $(event.currentTarget).attr('id').replace('price','');
                      var element = resultParts[+id];
                      if(element && element.offers && element.offers.length > 0) {
                        openModal(element);
                        clickRow = false;
                      }
                    } else {
                        clickRow = false;
                    }
                  }

                  if( clickRow && $(event.currentTarget).attr('class').indexOf('ti-banner') === -1 ) {
                    event.preventDefault();
                    var openPart = $(this).parent().find('a').attr('href');
                    window.location = openPart;
                  }

                  windowTop = $(window).scrollTop();
                  marginTop = windowTop + 50;
                  //$('#buy-now-modal').css('margin-top', marginTop + 'px');
                });

                $(document).on('click', '.ad_view', function(event) {
                    event.preventDefault();
                    event.stopPropagation();

                    const $this = $(this);
                    const $target = $(event.target);

                    if ($target.hasClass('ad_buy') && $this.data('type') === 'TEXAS') {
                        return; // Exit the handler early
                    }

                    const $link = $this.find('a.featured_title').first();
                    const redirectUrl = $link.attr('href');

                    window.location.href = redirectUrl;
                });

                $('.inlinead_view').click(function (event) {
                    event.stopPropagation();
                    const openPart = $(this).parent().find('a').attr('href');
                    window.location = openPart;
                });

                // must initialize tooltips after loading the content.
                $(function () {
                  $('[data-toggle="tooltip"]').tooltip()
                });

                /*document.getElementById('sorter').style.visibility = 'visible';*/
                try {
                  document.getElementById('matchingproducts').style.visibility = 'visible';
                  var textNode = doc.createTextNode(numberWithCommas(searchresult.hits).toString());
                  document.getElementById('matchingproductsvalue').appendChild(textNode);
                  /*$("#matchingproductsvalue").digits();*/
                }catch (err) {
                  console.log(err);
                }
            }
		});

		// setting up the sponsored part for display
        if(ASYNC_ADS) {
            $.getJSON(window.location.protocol + '//www.snapeda.com/api/v1/search_featured?q=APX803L20-30SA-7&amp;page=1', function (searchresult, async_status, asyncXHR) {
                asyncResponseId = asyncXHR.getResponseHeader('X-SNAP-REQUEST-ID')
                window.request_ids = (window.request_ids || []).push(asyncResponseId);
                $('#request-ids')[0].innerText += ` ${asyncResponseId}`;
                var resultCheckInterval = setInterval(function () {
                    setAds(searchresult, resultCheckInterval, asyncResponseId);
                }, 250)

            })
        }
	}

    // async load search results
    $(function() {
      addEvents();
      runQueryAsync();
    });

    function research_feature_ref(AdType, feat_ref, is_inline_ad) {
      if (AdType) { AdType = AdType.toUpperCase(); }

      let ref = '';
      if (AdType == 'WINSOURCE ELECTRONICS') { ref = 'winsource electronics'; }
      if (AdType == 'RS COMPONENTS') { ref = 'rs components'; }
      if (AdType == 'DIGIKEY') { ref = 'dk'; }
      if (AdType == 'MOUSER') { ref = 'me'; }
      if (AdType == 'TE') { ref = 'te_in'; }
      if (AdType == 'ARROW') { ref = 'aw'; }
      if (AdType == 'SAMTEC') { ref = 'samtec_in'; }
      if (AdType == 'CUI') { ref = 'cui_in'; }
      if (AdType == 'QUECTEL') { ref = 'quectel_in'; }
      if (AdType == 'TDK') { ref = 'tdk_in'; }
      if (AdType == 'TDK INVENSENSE') { ref = 'tdk invensense_in'; }
      if (AdType == 'RECOM') { ref = 'recom_in'; }
      if (AdType == 'BEL') { ref = 'bel_in'; }
      if (AdType == 'MURATA') { ref = 'murata_in'; }
      if (AdType == 'SAME SKY') { ref = 'same sky_in'; }
      if (AdType == 'ABRACON') { ref = 'abracon_in'; }
      if (AdType == 'GLOBAL CONNECTOR TECHNOLOGY') { ref = 'global connector technology_in'; }
      if (AdType == 'DIALOG') { ref = 'dialog_in'; }
      if (AdType == 'TEXAS') { ref = 'texas_in'; }
      if (AdType == 'SUPERIOR SENSOR') { ref = 'superior sensor_in'; }
      if (AdType == 'MOLEX') { ref = 'molex_in'; }
      if (AdType == 'I-PEX') { ref = 'i-pex_in'; }

      // Default case
      if(!ref && AdType) { ref = AdType.toLowerCase() + '_in'; }

      if(is_inline_ad){
          ref = ref + '_line';
      }

      return ref;
    }

    function trackDataABTest_impression(partname){
    	var Properties = {
	        'Type': 'Search Impression',
	        
	        'ab_version': 'b (icons not shown)',
	        
	        'partname': partname

	    }
	    mixpanel.track('Data Available AB Test', Properties);
    }

    function trackDataABTest_click(partname){
    	var Properties = {
	        'Type': 'Clicked Result',
	        
	        'ab_version': 'b (icons not shown)',
	        
	        'partname': partname
	    }
	    mixpanel.track('Data Available AB Test', Properties);
    }

    function numberWithCommas(x) {
        if (typeof x != "undefined"){
          return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
        return ''

    }

    function refreshFootprintSymbol (package_id, div_id) {
    	$.ajax({
            type: "GET",
            url: "/api/refreshFootprintSymbol/" + package_id ,
            success: function ( response ) {
                if (response == 'True'){
                    var dataImg = document.createElement('img');
					dataImg.src = "/static/img/search_icons/footprint.png";
					dataImg.setAttribute('data-toggle','tooltip');
					dataImg.title = "footprint available";
					dataImg.alt = "footprint available";
					dataImg.style.height = '30px';
					document.getElementById(div_id).appendChild(dataImg);
                }else{
                    console.log("no footprint associated with this pacakge");
                }
            }
        });
    }


  function openModal(element) {
    $('#buy-now-modal').find("#loading-modal").show();
    var Properties = {
        'Type': 'Buy Now Button-Search',
        'Part Name': element.name,
        'Manufacturer': element.manufacturer,
        'Supplier' : 'N/A',
        'Search Term': 'APX803L20\u002D30SA\u002D7',
        'userid': ''
    };
    mixpanel.track('Buy', Properties);


    var featuredTemplate = '<div class="featured-tag"> <span class="featured-star">★</span> Featured </div>';

    var template = '<div class=" tab-content" id="tab-content-pricing" data-tab-id="3">'
                + '  <div id="PricingAndAvailability"> '
                + '   __INFO_AVAILABILITY__'
                + '  </div>'
                + '</div>';


    var infoAvailability =  '<table id="pricing-table" class="s-table clr-darker center medium-w">'
                        + '    <thead>'
                        + '        <tr>'
                        + '            <th width="59%">Supplier</th>'
                        + '            <th width="40%">Quantity</th>'
                        + '            <th>Price</th>'
                        + '            <th>Purchase</th>'
                        + '        </tr>'
                        + '    </thead>'
                        + '    <tbody>'
                        + '         __OFFERS__'
                        + '    </tbody>'
                        + '</table>'

    var offersTemplate = '<tr class="price_row __FEATURED_ROW_CLASS__">'
                        + '<td class="distributor col-distributor __PT__">'
                        + '__SUPPLIER1__ __FEATURED__'
                        + '</td>'
                        + '<td class="qtt-available __PT__">'
                        + '__QUANTITYAVAILABILITYTEMPLATE__'
                        + '</td>'
                        + '<td class="price_column __PT__"> __PRICECOLUMN__</td>'
                        + '<td class="hide"> __HIDE_COLUMN__ </td>'
                        + '<td class="td-buy __PT9__">'
                        + '    <a class="buy s-btn s-btn-small orange buy_btn" data-type="Buy Button in Modal-Search" target="_blank" rel="noopener" data-manufacturer="__MANUFACTURER__" data-supplier="__SUPPLIER__" data-part="__PARTNAME__" href="__BUYURL__">Visit Site</a>'
                        + '</td>'
                        + '</tr>'  ;

    var optionTemplate = '<option> __PRICE0__ x __PRICE1__ </option>'
    var optionHiddenField = '<input type="hidden" class="price_offer" data-quantity="__DATAQUANTITY__" data-price="__DATAPRICE__">'; // append in hide
    var quantityAvailableTemplate = '<div>__QTT_AVAILABLE__</div><input type="hidden" class="quantity_available" data-quantity="__QTT_AVAILABLE__">'
    var noQuantityAvailableTemplate = '<div data-toggle="tooltip" class="avail_icon" data-html="true" data-original-title="Out of stock<br>Lead time: __LEADTIMEWEEKS__" style="cursor: pointer"><i class="fa fa-info-circle"></i></div>'
    var offers = '';
    var optionsHiddenInput = '';

    if(element && element.offers) {
        offers = '';
        optionsHiddenInput = '';

        //SORT OFFERS
        var featured_offers = element.offers.filter(function(_of){ return _of.show_offerlinks == true}),
        non_featured_offers = element.offers.filter(function(_of){ return _of.show_offerlinks == false});

        if(featured_offers){
          featured_offers = search_page_sort_offers(featured_offers);
        }
        if(non_featured_offers){
          non_featured_offers = search_page_sort_offers(non_featured_offers);
        }

        var new_offers = featured_offers.concat(non_featured_offers);

        new_offers.forEach( function(offerElem) {
          var tempOffer = offersTemplate;
          if(offerElem.seller && offerElem.seller.name) {
              tempOffer = tempOffer.replaceAll('__MANUFACTURER__', element.manufacturer);
              tempOffer = tempOffer.replace('__PARTNAME__', element.name);

              var quantity = offerElem.in_stock_quantity ? offerElem.in_stock_quantity : (offerElem.avail ? offerElem.avail : (offerElem.quantity_available ? offerElem.quantity_available : 'N/A'));

              if (quantity === 'N/A' || parseInt(quantity) < 1) {
                const leadTimeMessage = offerElem.lead_time_weeks ? offerElem.lead_time_weeks + (offerElem.lead_time_weeks > 1 ? ' weeks' : ' week') : ' Not Available'
                tempOffer = tempOffer.replaceAll('__QUANTITYAVAILABILITYTEMPLATE__', noQuantityAvailableTemplate);
                tempOffer = tempOffer.replaceAll('__LEADTIMEWEEKS__', leadTimeMessage);
              } else {
                tempOffer = tempOffer.replaceAll('__QUANTITYAVAILABILITYTEMPLATE__', quantityAvailableTemplate);
                tempOffer = tempOffer.replaceAll('__QTT_AVAILABLE__', quantity === 'N/A' ? 'N/A' : formatNumber(quantity) );
              }

              tempOffer = tempOffer.replaceAll('__SUPPLIER__', (offerElem.seller && offerElem.seller.name) ? offerElem.seller.name : 'N/A');
              tempOffer = tempOffer.replace('__BUYURL__', offerElem.product_url);
              if(offerElem.show_offerlinks) {
                if(offerElem.logo_url) {
                  isRSLogo = offerElem.seller && offerElem.seller.name && offerElem.seller.name.toLowerCase() == 'rs components'
                  tempOffer = tempOffer.replaceAll('__SUPPLIER1__', '<img src="' + offerElem.logo_url + '" alt="'+ ((offerElem.seller && offerElem.seller.name) ? offerElem.seller.name : 'N/A') +'"' + (isRSLogo ? 'style="width: 60px"' : '' ) + ' >');
                } else {
                  tempOffer = tempOffer.replaceAll('__SUPPLIER1__', (offerElem.seller && offerElem.seller.name) ? offerElem.seller.name : 'N/A');
                }
                tempOffer = tempOffer.replace('__FEATURED__', featuredTemplate);
                tempOffer = tempOffer.replaceAll('__PT__', 'pt-24');
                tempOffer = tempOffer.replaceAll('__PT9__', 'pt-9');
                tempOffer = tempOffer.replace('__FEATURED_ROW_CLASS__', 'featured_offer');
              } else if (offerElem.seller.name === 'DigiKey' && offerElem.logo_url){
                  tempOffer = tempOffer.replaceAll('__SUPPLIER1__', '<img src="' + offerElem.logo_url + '" alt="'+ ((offerElem.seller && offerElem.seller.name) ? offerElem.seller.name : 'N/A') +'" >');
                  tempOffer = tempOffer.replace('__FEATURED__', '');
                  tempOffer = tempOffer.replaceAll('__PT__', '');
                  tempOffer = tempOffer.replaceAll('__PT9__', '');
                  tempOffer = tempOffer.replace('__FEATURED_ROW_CLASS__', '');
              } else {
                 if(offerElem.logo_url) {
                  isRSLogo = offerElem.seller && offerElem.seller.name && offerElem.seller.name.toLowerCase() == 'rs india'
                  tempOffer = tempOffer.replaceAll('__SUPPLIER1__', '<img src="' + offerElem.logo_url + '" alt="'+ ((offerElem.seller && offerElem.seller.name) ? offerElem.seller.name : 'N/A') +'"' + (isRSLogo ? 'style="width: 60px"' : '' ) + ' >');
                } else {
                  tempOffer = tempOffer.replaceAll('__SUPPLIER1__', (offerElem.seller && offerElem.seller.name) ? offerElem.seller.name : 'N/A');
                }
                tempOffer = tempOffer.replace('__FEATURED__', '');
                tempOffer = tempOffer.replaceAll('__PT__', '');
                tempOffer = tempOffer.replaceAll('__PT9__', '');
                tempOffer = tempOffer.replace('__FEATURED_ROW_CLASS__', '');
              }
              var prices = [];
              for (var key in offerElem.prices) {
                  var objPrices = offerElem.prices[key];
                  objPrices.forEach(function(element){
                      prices.push(element);
                  });
              };

              if(prices.length > 1) {
                  multiOptionTemplate = '<div class="select-wrapper" __SKUTEMPLATE__>'
                                      + '    <select>'
                                      + '__SELECTOPTIONS__'
                                      + '    </select>'
                                      + '</div>';
                  options = '';
                  optionsHiddenInput = '';
                  prices.forEach(function(offerPrice) {
                      var formattedPrice = (+offerPrice[1]) + '';
                      var sellerName = ('' + offerElem.seller.name).toLowerCase();
                      let currency = '$';
                      if (sellerName.indexOf('toby') !== -1 || sellerName.indexOf('rs components') !== -1) {
                        currency = '£';
                      } else if (sellerName.indexOf('actinius') !== -1) {
                        currency = '€';
                      }
                       else if (sellerName.indexOf('rs india') !== -1) {
                        currency = '₹';
                      }
                      formattedPrice = currency + formattedPrice;
                      formattedPrice = formattedPrice.replace('USD','');
                      options += optionTemplate.replace('__PRICE0__', offerPrice[0]).replace('__PRICE1__', formattedPrice);
                      optionsHiddenInput += optionHiddenField.replace('__DATAQUANTITY__', offerPrice[0]).replace('__DATAPRICE__', formattedPrice);
                  });

                  multiOptionTemplate = multiOptionTemplate.replace('__SELECTOPTIONS__', options);
                  var SellerName = offerElem.seller ? offerElem.seller.name : offerElem.seller_name;
                    let skuTooltip;

                    if (SellerName && SellerName.toLowerCase() === 'cui devices') {
                        skuTooltip = `data-toggle="tooltip" data-html="true" data-original-title="PACK_TYPE: ${offerElem.sku}" style="cursor: pointer"`;
                    } else {
                        skuTooltip = `data-toggle="tooltip" data-html="true" data-original-title="SKU: ${offerElem.sku}" style="cursor: pointer"`;
                    }
                  const sku = offerElem.sku || '';
                  const replacement = sku !== '' ? skuTooltip.replace('__SKU__', sku) : '';
                  multiOptionTemplate = multiOptionTemplate.replace('__SKUTEMPLATE__', replacement);

                  tempOffer = tempOffer.replace('__PRICECOLUMN__', multiOptionTemplate);
                  tempOffer = tempOffer.replace('__HIDE_COLUMN__', optionsHiddenInput);
              } else if (prices.length === 1) {
                  var formattedPrice = (+prices[0][1]) + '';
                  var sellerName = ('' + offerElem.seller.name).toLowerCase();
                  let currency = '$';
                  if (sellerName.indexOf('toby') !== -1 || sellerName.indexOf('rs components') !== -1) {
                    currency = '£';
                  } else if (sellerName.indexOf('actinius') !== -1) {
                    currency = '€';
                  }
                  else if (sellerName.indexOf('rs india') !== -1) {
                        currency = '₹';
                    }
                  formattedPrice = currency + formattedPrice;
                  formattedPrice = formattedPrice.replace('USD','');
                  oneItemTemplate = '<div id="one-priceperquant" __SKUTEMPLATE__>' + prices[0][0] + ' x ' + formattedPrice + '</div>';

                  var SellerName = offerElem.seller ? offerElem.seller.name : offerElem.seller_name;
                    let skuTooltip;

                    if (SellerName && SellerName.toLowerCase() === 'same sky') {
                        skuTooltip = `data-toggle="tooltip" data-html="true" data-original-title="PACK_TYPE: ${offerElem.sku}" style="cursor: pointer"`;
                    } else {
                        skuTooltip = `data-toggle="tooltip" data-html="true" data-original-title="SKU: ${offerElem.sku}" style="cursor: pointer"`;
                    }
                  const sku = offerElem.sku || '';
                  const replacement = sku !== '' ? skuTooltip.replace('__SKU__', sku) : '';
                  oneItemTemplate = oneItemTemplate.replace('__SKUTEMPLATE__', replacement);

                  optionsHiddenInput += optionHiddenField.replace('__DATAQUANTITY__', prices[0][0]).replace('__DATAPRICE__', formattedPrice);
                  tempOffer = tempOffer.replace('__PRICECOLUMN__', oneItemTemplate);
                  tempOffer = tempOffer.replace('__HIDE_COLUMN__', optionsHiddenInput);
              } else {
                  tempOffer = tempOffer.replace('__PRICECOLUMN__', '<span class="n-a">N/A</span>');
              }
          } else {

            if(offerElem.show_offerlinks) {
              tempOffer = tempOffer.replaceAll('__SUPPLIER1__', '<img src="' + offerElem.logo_url + '" alt="'+ ((offerElem.seller && offerElem.seller.name) ? offerElem.seller.name : (offerElem.seller_name ? offerElem.seller_name : 'N/A')) +'" >');
              tempOffer = tempOffer.replace('__FEATURED__', featuredTemplate);
              tempOffer = tempOffer.replaceAll('__PT__', 'pt-24');
              tempOffer = tempOffer.replaceAll('__PT9__', 'pt-9');
              tempOffer = tempOffer.replace('__FEATURED_ROW_CLASS__', 'featured_offer');
            } else {
              tempOffer = tempOffer.replaceAll('__SUPPLIER1__', offerElem.seller_name);
              tempOffer = tempOffer.replace('__FEATURED__', '');
              tempOffer = tempOffer.replaceAll('__PT__', '');
              tempOffer = tempOffer.replaceAll('__PT9__', '');
              tempOffer = tempOffer.replace('__FEATURED_ROW_CLASS__', '');
            }
            tempOffer = tempOffer.replaceAll('__SUPPLIER__', offerElem.seller_name);
            tempOffer = tempOffer.replaceAll('__MANUFACTURER__', element.manufacturer);
            tempOffer = tempOffer.replace('__PARTNAME__', element.name);
            var quantity = offerElem.in_stock_quantity ? offerElem.in_stock_quantity : (offerElem.avail ? offerElem.avail : (offerElem.quantity_available ? offerElem.quantity_available : (offerElem.seller_name ? offerElem.seller_name : 'N/A')));

            if ((quantity === 'N/A' || parseInt(quantity) < 1)) {
                const leadTimeMessage = offerElem.lead_time_weeks ? offerElem.lead_time_weeks + (offerElem.lead_time_weeks > 1 ? ' weeks' : ' week') : ' Not Available'
                tempOffer = tempOffer.replaceAll('__QUANTITYAVAILABILITYTEMPLATE__', noQuantityAvailableTemplate);
                tempOffer = tempOffer.replaceAll('__LEADTIMEWEEKS__', leadTimeMessage);
              } else {
                tempOffer = tempOffer.replaceAll('__QUANTITYAVAILABILITYTEMPLATE__', quantityAvailableTemplate);
                tempOffer = tempOffer.replaceAll('__QTT_AVAILABLE__', quantity === 'N/A' ? 'N/A' : formatNumber(quantity) );
              }

            tempOffer = tempOffer.replace('__BUYURL__', offerElem.buy_url);
            if(offerElem.prices.length > 1) {
                multiOptionTemplate = '<div class="select-wrapper" __SKUTEMPLATE__>'
                                      + '    <select>'
                                      + '__SELECTOPTIONS__'
                                      + '    </select>'
                                      + '</div>';
                options = '';
                optionsHiddenInput = '';
                offerElem.prices.forEach(function(offerPrice) {
                    var formattedPrice = (+offerPrice[1]) + '';
                    var sellerName = ('' + offerElem.seller_name).toLowerCase();
                    let currency = '$';
                    if (sellerName.indexOf('toby') !== -1 || sellerName.indexOf('rs components') !== -1) {
                      currency = '£';
                    } else if (sellerName.indexOf('actinius') !== -1) {
                      currency = '€';
                    }
                     else if (sellerName.indexOf('rs india') !== -1) {
                        currency = '₹';
                      }
                    formattedPrice = currency + formattedPrice;
                    formattedPrice = formattedPrice.replace('USD','');

                    options += optionTemplate.replace('__PRICE0__', offerPrice[0]).replace('__PRICE1__', formattedPrice);
                    optionsHiddenInput += optionHiddenField.replace('__DATAQUANTITY__', offerPrice[0]).replace('__DATAPRICE__', formattedPrice);
                });

                multiOptionTemplate = multiOptionTemplate.replace('__SELECTOPTIONS__', options);

                var SellerName = offerElem.seller ? offerElem.seller.name : offerElem.seller_name;
                let skuTooltip;

                if (SellerName && SellerName.toLowerCase() === 'cui devices') {
                    skuTooltip = `data-toggle="tooltip" data-html="true" data-original-title="PACK_TYPE: ${offerElem.sku}" style="cursor: pointer"`;
                } else {
                    skuTooltip = `data-toggle="tooltip" data-html="true" data-original-title="SKU: ${offerElem.sku}" style="cursor: pointer"`;
                }
                const sku = offerElem.sku || '';
                const replacement = sku !== '' ? skuTooltip.replace('__SKU__', sku) : '';
                multiOptionTemplate = multiOptionTemplate.replace('__SKUTEMPLATE__', replacement);

                tempOffer = tempOffer.replace('__PRICECOLUMN__', multiOptionTemplate);
                tempOffer = tempOffer.replace('__HIDE_COLUMN__', optionsHiddenInput);
            } else if (offerElem.prices.length === 1) {
                var formattedPrice = (+offerElem.prices[0][1]) + '';
                var sellerName = ('' + offerElem.seller_name).toLowerCase();
                let currency = '$';
                if (sellerName.indexOf('toby') !== -1 || sellerName.indexOf('rs components') !== -1) {
                  currency = '£';
                } else if (sellerName.indexOf('actinius') !== -1) {
                  currency = '€';
                }
                 else if (sellerName.indexOf('rs india') !== -1) {
                        currency = '₹';
                }
                formattedPrice = currency + formattedPrice;
                formattedPrice = formattedPrice.replace('USD','');

                oneItemTemplate = '<div id="one-priceperquant" __SKUTEMPLATE__>' + offerElem.prices[0][0] + ' x ' + formattedPrice + '</div>';

                var SellerName = offerElem.seller ? offerElem.seller.name : offerElem.seller_name;
                let skuTooltip;

                if (SellerName && SellerName.toLowerCase() === 'same sky') {
                    skuTooltip = `data-toggle="tooltip" data-html="true" data-original-title="PACK_TYPE: ${offerElem.sku}" style="cursor: pointer"`;
                } else {
                    skuTooltip = `data-toggle="tooltip" data-html="true" data-original-title="SKU: ${offerElem.sku}" style="cursor: pointer"`;
                }
                const sku = offerElem.sku || '';
                const replacement = sku !== '' ? skuTooltip.replace('__SKU__', sku) : '';
                oneItemTemplate = oneItemTemplate.replace('__SKUTEMPLATE__', replacement);

                optionsHiddenInput += optionHiddenField.replace('__DATAQUANTITY__', offerElem.prices[0][0]).replace('__DATAPRICE__', formattedPrice);
                tempOffer = tempOffer.replace('__PRICECOLUMN__', oneItemTemplate);
                tempOffer = tempOffer.replace('__HIDE_COLUMN__', optionsHiddenInput);
            } else {
                tempOffer = tempOffer.replace('__PRICECOLUMN__', '<span class="n-a">N/A</span>');
            }
          }
          offers += tempOffer;
      });
    }

    tInfoAv = infoAvailability;
    tInfoAv = tInfoAv.replace('__PARTNAME__', element.name);
    tInfoAv = tInfoAv.replaceAll('__MANUFACTURER__', element.manufacturer);
    tInfoAv = tInfoAv.replace('__OFFERS__', offers);

    $('#buy-now-modal').find("#loading-modal").hide();
    $('#buy-now-modal').append(template.replace('__INFO_AVAILABILITY__', tInfoAv));
    $('[data-toggle="tooltip"]').tooltip();
    $('#buy-now-modal').show();
    $('#pricing-table tr.price_row img').each(function(){
     if($(this).attr("alt")=='DigiKey'){
        var link_href = "https://ad.doubleclick.net/ddm/trackclk/N4481.2579422SNAPEDA/B4652602.307340238;dc_trk_aid=500214202;dc_trk_cid=102628001;dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;tfua=;ltd=";
        var img_src = "https://ad.doubleclick.net/ddm/trackimp/N4481.2579422SNAPEDA/B4652602.307340238;dc_trk_aid=500214202;dc_trk_cid=102628001;ord=1756068672;dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;tfua=;ltd=?"
        $('.digikey_tracking').attr('href',link_href);
        $('.digikey_tracking_img').attr('src', img_src);
        $('.digikey_tracking').show();
      }
    });
    $('.bnm-overlay').show();

    $('#buy-now-modal .modal-close').unbind();
    $('#buy-now-modal .modal-close').on('click', function() {
      $('#buy-now-modal').hide();
      $('#buy-now-modal').find("#loading-modal").show();
      $('.bnm-overlay').hide();
      $('#buy-now-modal #tab-content-pricing').remove();
    });

    $(".buy_btn").unbind();
    $(".buy_btn").on("click", function(){
        var currElem = $(this)[0];
        var type = $(currElem).attr('data-type');
        var part = $(currElem).attr('data-part');

        var manufacturer = $(currElem).attr('data-manufacturer');
        var supplier = $(currElem).attr('data-supplier');

        var Properties = {
            'Type': type,
            'Part Name': part,
            'Manufacturer': manufacturer,
            'Supplier' : supplier,
            'userid': ''
        };
        mixpanel.track('Buy', Properties);

        if (supplier != 'N/A') {
          var action = 'Search - Buy Button In Modal';
          track_db_and_mixpanel (supplier, action, 'APX803L20\u002D30SA\u002D7', '',part);
        }
    });
  }

  $(document).mouseup(function(e) {
    var container = $("#buy-now-modal");
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      $('#buy-now-modal').hide();
      $('.bnm-overlay').hide();
      $('#buy-now-modal #tab-content-pricing').remove();
    }
  });

  String.prototype.replaceAll = function (find, replace) {
    var str = this;
    return str.replace(new RegExp(find, 'g'), replace);
  };

  function track_mixpanel_click(tracking_data){
      var useAsync = true;
      useAsync = navigator.userAgent.toLowerCase().indexOf('firefox') < 0;
        $.ajax({
            type: "POST",
            async: useAsync,
            url: "/api/mixpanel_click_track/",
            data: tracking_data
        });
  }

  function track_db_and_mixpanel(org, action, search_term, relevancy, partname, checkFireFox, ad_ab_version, auction_id) {
        // Set default param values
        ad_ab_version = ad_ab_version || 'a';
        auction_id = auction_id || 'NA';

        var useAsync = true;

        if(checkFireFox){
            useAsync = navigator.userAgent.toLowerCase().indexOf('firefox') < 0;
        }

        let payload = {
            'ad_org': org,
            'action_type': action,
            'partname': partname,
            'search_term': search_term,
            'relevancy': relevancy,
            'ad_ab_version': ad_ab_version,
            'auction_id': auction_id + ''
          }

        if (window.numberOfSkus && org && org.toUpperCase() === 'RS COMPONENTS') {
                payload['numberOfSkus'] = window.numberOfSkus;
        }

        $.ajax({
            url: '/api/track_ad/',
            type: 'POST',
            async: useAsync,
            data: payload,
        })
    }

  // Determin the position of the buy now modal
  $('.buy-now-left').click(function(event) {
      windowTop = $(window).scrollTop();

      marginTop = windowTop + 50;
      //$('#buy-now-modal').css('margin-top', marginTop + 'px');
  });

  
  function formatNumber(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }


  function getRelevantSearchCarousel(top_id, inline_id, query_search, request_id){
    $.ajax({
      url: '/api/get_related_search_carousel',
      headers: { 'X-SNAP-REQUEST-ID': request_id },
      data: {
        "top_featured_id": top_id,
        "inline_featured_id": inline_id,
        "query_search": query_search
      },
      success: function (data){
        if(data != 'Invalid data'){
          $(data).insertAfter($('#paginator'));

          var type_tracking = $('#related-search-carousel').data('impression'),
          ad_type = $('#related-search-carousel').data('ad-orgs').split(','),
          relevancy = 'N/A';
          var top_part = $('#related-search-carousel').data('top-part');
          var inline_part = $('#related-search-carousel').data('inline-part');
          var third_part = $('#related-search-carousel').data('third-part');

          track_db_and_mixpanel(ad_type[0], type_tracking, '', relevancy, top_part);
          track_db_and_mixpanel(ad_type[1], type_tracking, '', relevancy, inline_part);
          track_db_and_mixpanel(ad_type[2], type_tracking, '', relevancy, third_part);

          $(document).off('click', '#related-search-carousel .c-set').on('click', '#related-search-carousel .c-set', function(e){
              
              var clickable = true;
              /*if(e.target.tagName.toLowerCase() === 'a' && $(this).hasClass('c-set')) {
                  clickable = false;
              }*/

              
              if (clickable) {
                  var tmp_part_link = $(this).data('url');
                  if(tmp_part_link != undefined){
                      var clickType = $('#related-search-carousel').data('click');
                      var event = $(this).attr('data-ad-org');
                      var relPartName = $(this).attr('data-part-name');
                      var properties = {
                          'rel part name': relPartName,
                          'manufacturer': $(this).attr('data-manufacturer'),
                          'rel unipart id': $(this).attr('data-unipart-id'),
                          'type': clickType,
                          'ref': 'carousel_search'
                      }
                      track_db_and_mixpanel(event, clickType, '', properties, relPartName, true);
                      mixpanel.track(event, properties);
                      
                      window.open(tmp_part_link, '_self');
                  }
              }
          });
        }
      }
    });
  }

  $(document).ready(function() {


    // Event listener for handling 'clicks' event tracking in search results banner
    // (e.g Buy TE, Reference Design, Free Samples)
    $(document.body).on('click', '.manuf-banner', function(event) {
      const manufacturer = $(this).data('manufacturer');
      const part = $(this).data('part');
      var ad_org = '';

      var properties = {
        'manufacturer': manufacturer,
        'type': 'Click',
        'location': 'Search',
        'userid': '',
        'part': part
      }

      if (manufacturer === 'Texas Instrument') {
        ad_org = 'TEXAS';
        properties.Buy_TI_version = 'Design 1';
        properties.url = event.target.href
      } else if (manufacturer.toUpperCase().indexOf('QUECTEL') !== -1) {
        ad_org = 'QUECTEL';
        properties.url = $(this).data('url');
      } else if (manufacturer.toUpperCase().indexOf('ABRACON') !== -1) {
        ad_org = 'ABRACON';
        properties.url =$(this).data('url');
      } else if (manufacturer.toUpperCase().indexOf('SUPERIOR SENSOR') !== -1) {
        ad_org = 'SUPERIOR SENSOR';
        properties.url = $(this).data('url');
      } else if (manufacturer.toUpperCase().indexOf('THALES') !== -1) {
        ad_org = 'THALES';
        properties.url = $(this).data('url');
      } else if (manufacturer.toUpperCase().indexOf('ANALOG DEVICES') !== -1) {
        ad_org = 'ANALOG DEVICES';
        properties.url = event.target.href
      }
        var tracking_data = properties;
        tracking_data.event_name = 'Referral'
        track_mixpanel_click(tracking_data)
        track_db_and_mixpanel(ad_org, 'Reflink - Search - Click', 'APX803L20\u002D30SA\u002D7', properties);
    });

    // addAutoComplete('search_autocomplete');
  });

  function getWording(manufacturer) {
    var s = [ "Shop on ", "Check Prices on ", "Visit "];
    
    var rn = Math.floor(Math.random() * 2) - 1;
    var message = s[2];
    if(rn >=0 && rn <2) {
      message = s[rn];
    }
    

    if (manufacturer == "RS Components") {
      message += "RS";
    }else {
      message += manufacturer;
    }
    return message;
  }
</script>


<A class="digikey_tracking" HREF="">
    <IMG class="digikey_tracking_img" SRC="" BORDER=0 WIDTH=1 HEIGHT=1 ALT="Advertisement">
</A>

<div class='bnm-overlay'></div>
<div class="lean-modal bnmSearch" id="buy-now-modal" >
    <div class="modal-top-sect bg-blue-gradient">
        <p class="align-center modal-title">
            Shop Now
            <span class="modal-close">&times;</span>
        </p>
    </div>
    <div id="loading-modal">
        <div id="searching" class="spinner">
          <div class="rect1"></div>
          <div class="rect2"></div>
          <div class="rect3"></div>
          <div class="rect4"></div>
          <div class="rect5"></div>
        </div>
    </div>
</div>

<div id="snapverifiedtooltip" class="snapverifiedtooltip" role="tooltip">
    <div class="arrow" data-popper-arrow></div>
    <div style="display: flex; flex-direction: column; font-color: #2C2C35; font-weight: 400; gap: 8px;">
        <span style="font-weight: 600; color: #304e70; display: flex; flex-direction: row; align-items: center; gap: 5px;">
            <svg width="22" height="22" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.32947 0.699935C7.69505 0.318325 8.30495 0.318325 8.67053 0.699934L9.2856 1.34197C9.52497 1.59184 9.88342 1.68789 10.2156 1.59118L11.0693 1.34269C11.5767 1.195 12.1049 1.49995 12.2307 2.01322L12.4424 2.87678C12.5247 3.21286 12.7871 3.47526 13.1232 3.55762L13.9868 3.76927C14.5 3.89507 14.805 4.42326 14.6573 4.93066L14.4088 5.78435C14.3121 6.11658 14.4082 6.47503 14.658 6.7144L15.3001 7.32947C15.6817 7.69505 15.6817 8.30495 15.3001 8.67053L14.658 9.2856C14.4082 9.52497 14.3121 9.88342 14.4088 10.2156L14.6573 11.0693C14.805 11.5767 14.5 12.1049 13.9868 12.2307L13.1232 12.4424C12.7871 12.5247 12.5247 12.7871 12.4424 13.1232L12.2307 13.9868C12.1049 14.5 11.5767 14.805 11.0693 14.6573L10.2156 14.4088C9.88342 14.3121 9.52497 14.4082 9.2856 14.658L8.67053 15.3001C8.30495 15.6817 7.69505 15.6817 7.32947 15.3001L6.7144 14.658C6.47503 14.4082 6.11658 14.3121 5.78435 14.4088L4.93067 14.6573C4.42326 14.805 3.89507 14.5 3.76927 13.9868L3.55762 13.1232C3.47526 12.7871 3.21286 12.5247 2.87678 12.4424L2.01322 12.2307C1.49995 12.1049 1.195 11.5767 1.34269 11.0693L1.59118 10.2156C1.68789 9.88342 1.59184 9.52497 1.34197 9.2856L0.699935 8.67053C0.318325 8.30495 0.318325 7.69505 0.699934 7.32947L1.34197 6.7144C1.59184 6.47503 1.68789 6.11658 1.59118 5.78435L1.34269 4.93067C1.195 4.42326 1.49995 3.89507 2.01322 3.76927L2.87678 3.55762C3.21286 3.47526 3.47526 3.21286 3.55762 2.87678L3.76927 2.01322C3.89507 1.49995 4.42326 1.195 4.93066 1.34269L5.78435 1.59118C6.11658 1.68789 6.47503 1.59184 6.7144 1.34197L7.32947 0.699935Z" fill="url(#paint0_radial_304_117)"/>
            <path d="M4.57141 7.51327L7.06845 10.2857L12 5.71423" stroke="white" stroke-width="1.58267" stroke-miterlimit="10"/>
            <defs>
            <radialGradient id="paint0_radial_304_117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8 8.57143) rotate(90) scale(9.14286)">
            <stop stop-color="#46B0ED"/>
            <stop offset="1" stop-color="#1A81BC"/>
            </radialGradient>
            </defs>
            </svg>
            This product is verified
        </span>
        <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
            <svg width="22" height="22" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.25 1V2.5M10.75 1V2.5M6.25 14.5V16M10.75 14.5V16M14.5 6.25H16M14.5 10H16M1 6.25H2.5M1 10H2.5M6.1 14.5H10.9C12.1601 14.5 12.7902 14.5 13.2715 14.2548C13.6948 14.039 14.039 13.6948 14.2548 13.2715C14.5 12.7902 14.5 12.1601 14.5 10.9V6.1C14.5 4.83988 14.5 4.20982 14.2548 3.72852C14.039 3.30516 13.6948 2.96095 13.2715 2.74524C12.7902 2.5 12.1601 2.5 10.9 2.5H6.1C4.83988 2.5 4.20982 2.5 3.72852 2.74524C3.30516 2.96095 2.96095 3.30516 2.74524 3.72852C2.5 4.20982 2.5 4.83988 2.5 6.1V10.9C2.5 12.1601 2.5 12.7902 2.74524 13.2715C2.96095 13.6948 3.30516 14.039 3.72852 14.2548C4.20982 14.5 4.83988 14.5 6.1 14.5ZM7.45 10.75H9.55C9.97004 10.75 10.1801 10.75 10.3405 10.6683C10.4816 10.5963 10.5963 10.4816 10.6683 10.3405C10.75 10.1801 10.75 9.97004 10.75 9.55V7.45C10.75 7.02996 10.75 6.81994 10.6683 6.65951C10.5963 6.51839 10.4816 6.40365 10.3405 6.33175C10.1801 6.25 9.97004 6.25 9.55 6.25H7.45C7.02996 6.25 6.81994 6.25 6.65951 6.33175C6.51839 6.40365 6.40365 6.51839 6.33175 6.65951C6.25 6.81994 6.25 7.02996 6.25 7.45V9.55C6.25 9.97004 6.25 10.1801 6.33175 10.3405C6.40365 10.4816 6.51839 10.5963 6.65951 10.6683C6.81994 10.75 7.02996 10.75 7.45 10.75Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            SnapMagic crafted CAD
        </span>
        <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
            <svg width="19" height="22" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.5 7.75H4M5.5 10.75H4M10 4.75H4M13 4.6V12.4C13 13.6601 13 14.2902 12.7548 14.7715C12.539 15.1948 12.1948 15.539 11.7715 15.7548C11.2902 16 10.6601 16 9.4 16H4.6C3.33988 16 2.70982 16 2.22852 15.7548C1.80516 15.539 1.46095 15.1948 1.24524 14.7715C1 14.2902 1 13.6601 1 12.4V4.6C1 3.33988 1 2.70982 1.24524 2.22852C1.46095 1.80516 1.80516 1.46095 2.22852 1.24524C2.70982 1 3.33988 1 4.6 1H9.4C10.6601 1 11.2902 1 11.7715 1.24524C12.1948 1.46095 12.539 1.80516 12.7548 2.22852C13 2.70982 13 3.33988 13 4.6Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Datasheet refreshed this week
        </span>
        <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
            <svg width="21" height="22" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14.9725 3.25C14.9725 4.49264 11.8447 5.5 7.98627 5.5C4.12786 5.5 1 4.49264 1 3.25M14.9725 3.25C14.9725 2.00736 11.8447 1 7.98627 1C4.12786 1 1 2.00736 1 3.25M14.9725 3.25V13.75C14.9725 14.995 11.8675 16 7.98627 16C4.10501 16 1 14.995 1 13.75V3.25M14.9725 8.5C14.9725 9.745 11.8675 10.75 7.98627 10.75C4.10501 10.75 1 9.745 1 8.5" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Images & specs from supplier
        </span>
        <span style="display: flex; flex-direction: row; align-items: center; gap: 5px;">
            <svg width="20" height="22" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.88864 13.75C9.88864 14.9926 8.84719 16 7.5625 16C6.27781 16 5.23636 14.9926 5.23636 13.75M8.95547 4.17892C9.29315 3.84148 9.50095 3.38171 9.50095 2.875C9.50095 1.83947 8.63308 1 7.5625 1C6.49192 1 5.62405 1.83947 5.62405 2.875C5.62405 3.38171 5.83185 3.84148 6.16953 4.17892M12.2148 7.9C12.2148 6.86566 11.7246 5.87368 10.8522 5.14228C9.97969 4.41089 8.79636 4 7.5625 4C6.32864 4 5.14531 4.41089 4.27284 5.14228C3.40037 5.87368 2.91022 6.86566 2.91022 7.9C2.91022 9.61135 2.47145 10.8629 1.92398 11.7585C1.30002 12.7792 0.988031 13.2896 1.00035 13.4115C1.01445 13.551 1.0404 13.595 1.15735 13.6777C1.25957 13.75 1.77313 13.75 2.80026 13.75H12.3247C13.3519 13.75 13.8654 13.75 13.9677 13.6777C14.0846 13.595 14.1106 13.551 14.1246 13.4115C14.137 13.2896 13.825 12.7792 13.201 11.7585C12.6535 10.8629 12.2148 9.61135 12.2148 7.9Z" stroke="#2C2C35" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Lifecycle & change notifications
        </span>
    </div>
</div>



	            </div>
	        
	    </div>
	</div>
	<div id="sticky-footer">
    	



<footer id="site-footer">
    <div class="container">
        <div class="footer-row links">
            <div class="footer-column">
                <h3>SnapMagic</h3>
                <ul>
                    <li><a href="/about/">About</a></li>
                    <li><a href="/about/#contact_us">Contact</a></li>
                    <li><a href="/pricing/">Pricing</a></li>
                    <li><a href="https://careers.snapeda.com" target="_blank">Careers</a></li>
                    <li><a href="https://changelog.snapeda.com/" target="_blank">💎 What's new</a></li>
                    <!--<li><a href="/jobs/">Jobs</a></li>!-->
                </ul>
            </div>
            <div class="footer-column">
                <h3>Community</h3>
                <ul>
                    <li><a href="/profiles/">Our Community</a></li>
                    <li><a href="/questions/">Q &amp; A</a></li>
                    <li><a href="https://blog.snapeda.com/">Blog</a></li>
                    <li><a href="/made-with-snapeda/">Made With SnapMagic Search</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>Product</h3>
                <ul>
                    <li><a href="/parts/">Parts Library</a></li>
                    <li><a href="/instapart/">InstaPart</a></li>
                    <li><a href="/instabuild/">InstaBuild</a></li>
                    <!-- <li><a href="/part-requests/">Recent Requests</a></li> -->
                    <li><a href="/plugins/">Plugins</a></li>
                    
                    <li><a href="/get-api/">API</a></li>
                    <!-- <li><a href="/feature/request/">Send Feature Request</a></li> -->
                    <li><a href="/pcb-manufacturing/">PCB Suppliers</a></li>
                    <li><a href="https://support.snapmagic.com/en/articles/5599920-snapeda-desktop-app">SnapMagic Search Desktop App</a></li>
                </ul>
            </div>
            <!--  Orcad and KiCad to be updated -->
            <div class="footer-column">
                <h3>Tools</h3>
                <ul>
                <li><a href="/allegro/">Allegro</a></li>
                <li><a href="/altium-libraries/">Altium</a></li>
                <li><a href="/autodeskfusion360">Autodesk Fusion</a></li>
                <li><a href="/circuitstudio/">CircuitStudio</a></li>
                <li><a href="/cr-5000">CR-8000/CR-5000</a></li>
                <li><a href="/designspark/">DesignSpark</a></li>
                <li><a href="/diptrace/">DipTrace</a></li>
                <li><a href="/eagle/">Eagle</a></li>
                <li><a href="/easy-pc">Easy-PC</a></li>
                <li><a href="/ecadstar">eCADSTAR</a></li>
                <li><a href="/expresspcbplus">ExpressPCB Plus</a></li>
                <li><a href="/kicad/">KiCad</a></li>
                <li><a href="/orcad/">OrCAD</a></li>
                <li><a href="/pads/">PADS &amp; DxDesigner</a></li>
                <li><a href="/pcb123-libraries/">PCB123</a></li>
                <li><a href="/p-cad/">P-CAD</a></li>
                <li><a href="/proteus/">Proteus</a></li>
                <li><a href="/pulsonix">Pulsonix</a></li>
                <li><a href="/target-3001/">Target 3001!</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>Support</h3>
                <ul>
                    <li><a href="https://support.snapeda.com/" target="_blank">FAQ</a></li>
                    <li><a href="/about/import/">How to Import</a></li>
                    <li><a href="/standards/">Standards</a></li>
                    <li><a href="/cdn-cgi/l/email-protection#2b42454d446b58454a5b4e4f4a05484446">Contact Us</a></li>
                    <li><a href="https://support.snapeda.com/en/articles/5948134-design-fundamentals">Design Resources</a></li>
                    <li><a href="/about/terms/">Terms Of Service</a></li>
                    <li><a href="/about/privacy/">Privacy</a></li>
                </ul>
            </div>

        </div>

        <div id="home-newsletter-signup" class="footer-column social">
            <div id="mc_embed_signup">
                <h3 style="display: none;" id="title-newsletter">Join Our Newsletter</h3>
                <form  style="display: none;" action="//092552f0.sibforms.com/serve/MUIEANXky0hiqpnWKig9cHQOdX7_HLNrniK4d-bmzT6cm7SqyaucLhG10uf5lupdoyWC7dfBaWvBmJJwVHZQ5gMV_1H74H9SDO-4ov-cvjDpCEhpJRc1E7rJD1iHtbmjPkY7c5X3WCQyK-DGmI9V9wcqkxTOAYjCNj_KI13-6LxhR6y1OBvWex7GZmQSjFyV7TaYr064RjBrhHvI"
                    method="post"
                    id="sib-form"
                    class="validate"
                    name="mc-embedded-subscribe-form"
                    target="_blank"
                    data-type="subscription">
                    <div id="sib-form-container" class="sib-form-container">
                        <div id="error-message" class="sib-form-message-panel" style="margin-bottom:10px;">
                            <div class="sib-form-message-panel__text sib-form-message-panel__text--center">
                                <svg style="display:none !important" viewBox="0 0 512 512" class="sib-icon sib-notification__icon">
                                    <path d="M256 40c118.621 0 216 96.075 216 216 0 119.291-96.61 216-216 216-119.244 0-216-96.562-216-216 0-119.203 96.602-216 216-216m0-32C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm-11.49 120h22.979c6.823 0 12.274 5.682 11.99 12.5l-7 168c-.268 6.428-5.556 11.5-11.99 11.5h-8.979c-6.433 0-11.722-5.073-11.99-11.5l-7-168c-.283-6.818 5.167-12.5 11.99-12.5zM256 340c-15.464 0-28 12.536-28 28s12.536 28 28 28 28-12.536 28-28-12.536-28-28-28z"
                                    />
                                </svg>
                                <span class="sib-form-message-panel__inner-text">Your subscription could not be saved. Please try again.</span>
                            </div>
                        </div>
                        <div></div>
                        <div id="success-message" class="sib-form-message-panel">
                          <div class="sib-form-message-panel__text sib-form-message-panel__text--center">
                            <svg style="display:none !important" viewBox="0 0 512 512" class="sib-icon sib-notification__icon">
                              <path d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 464c-118.664 0-216-96.055-216-216 0-118.663 96.055-216 216-216 118.664 0 216 96.055 216 216 0 118.663-96.055 216-216 216zm141.63-274.961L217.15 376.071c-4.705 4.667-12.303 4.637-16.97-.068l-85.878-86.572c-4.667-4.705-4.637-12.303.068-16.97l8.52-8.451c4.705-4.667 12.303-4.637 16.97.068l68.976 69.533 163.441-162.13c4.705-4.667 12.303-4.637 16.97.068l8.451 8.52c4.668 4.705 4.637 12.303-.068 16.97z"
                              />
                            </svg>
                            <span class="sib-form-message-panel__inner-text">Thanks for subscribing to the SnapMagic Search newsletter. We&#039;re excited to have you as part of our community.</span>
                          </div>
                        </div>
                        <div></div>
                        <!-- <label for="mce-EMAIL">Keep in Touch! Subscribe to Our Newsletter</label> -->
                        <div class="sib-input sib-form-block">
                            <div class="form__entry entry_block">
                                <div class="form__label-row ">
                                    <div class="entry__field">
                                        <input type="email" value="" name="EMAIL" class="email input" id="mce-EMAIL" placeholder="email address" data-required="true" required style="-webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box;">
                                    </div>
                                </div>
                                <label class="entry__error entry__error--primary" style="font-size: 12px;text-align:left;color:#661d1d;background-color:#ffeded;border-radius:3px;border-color:#ff4949;padding: 0 5px;"></label>
                            </div>
                        </div>
                        <!-- real people should not fill this in and expect good things - do not remove this or risk form bot signups-->
                        <div style="position: absolute; left: -5000px;">
                            <input type="text" name="b_59ee0d7ec121b9b37313fd775_adb224cc06" tabindex="-1" value="">
                        </div>
                        <div class="sib-captcha sib-form-block">
                            <div class="form__entry entry_block">
                                <div class="form__label-row ">
                                    <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script>
                                        function handleCaptchaResponse() {
                                        var event = new Event('captchaChange');
                                        document.getElementById('sib-captcha').dispatchEvent(event);
                                        }
                                    </script>
                                    <div class="g-recaptcha sib-visible-recaptcha" id="sib-captcha" 
                                        data-sitekey="6LfDwKcUAAAAAF3aAYWzseFo_OIJwQXdHv6EIcCK" 
                                        data-callback="handleCaptchaResponse" 
                                        style="transform: scale(0.626);transform-origin: 0 0; margin: 0 auto -25px auto;">
                                    </div>
                                </div>
                                <label class="entry__error entry__error--primary" style="font-size: 12px;text-align:left;color:#661d1d;background-color:#ffeded;border-radius:3px;border-color:#ff4949;padding: 0 5px;"></label>
                            </div>
                        </div>
                        <div class="clear">
                            <!-- <input type="submit" value="Subscribe" name="subscribe" id="mc-embedded-subscribe" class="button"> -->
                            <button class="sib-form-block__button sib-form-block__button-with-loader" 
                                form="sib-form"
                                type="submit"
                                id="mc-embedded-subscribe"
                                style="font-size: 13px;font-weight: 400;line-height: 18px;">
                                <svg style="display:none !important" class="icon clickable__icon progress-indicator__icon sib-hide-loader-icon" viewBox="0 0 512 512">
                                  <path d="M460.116 373.846l-20.823-12.022c-5.541-3.199-7.54-10.159-4.663-15.874 30.137-59.886 28.343-131.652-5.386-189.946-33.641-58.394-94.896-95.833-161.827-99.676C261.028 55.961 256 50.751 256 44.352V20.309c0-6.904 5.808-12.337 12.703-11.982 83.556 4.306 160.163 50.864 202.11 123.677 42.063 72.696 44.079 162.316 6.031 236.832-3.14 6.148-10.75 8.461-16.728 5.01z"
                                  />
                                </svg>
                                Subscribe
                            </button>
                        </div>
                    </div>
                    <input type="text" name="email_address_check" value="" class="input--hidden">
                    <input type="hidden" name="locale" value="en">
                </form>
                <div class="contact-icons">
                    <ul>
                        <li class="social-facebook"><a rel="me" href="https://www.facebook.com/pages/SnapEDA/393047664096498">Facebook</a></li>
                        <li class="social-linkedin"><a rel="me" href="https://www.linkedin.com/company/snapeda">LinkedIn</a></li>
                        <li class="social-twitter"><a rel="me" href="https://twitter.com/SnapEDA">Twitter</a></li>
                    </ul>
                </div>
                <div class="footer-row copyright">
                    <p><i class="fa fa-phone"></i> **************</p>
                    <p><span class="copyright-symbol">©</span> 2013 - 2025 SnapMagic</p>
                </div>
            </div>
        </div>
        <div id="request-ids" style="clear: both; text-align: center; opacity: 0.25; font-size:.75em;color: #232323;">68d860d74efd4495bfa9ff0501737e87</div>
    </div>
</footer>

<script>
  window.REQUIRED_CODE_ERROR_MESSAGE = 'Please choose a country code';

  window.EMAIL_INVALID_MESSAGE = window.SMS_INVALID_MESSAGE = "The information provided is invalid. Please review the field format and try again.";

  window.REQUIRED_ERROR_MESSAGE = "This field cannot be left blank. ";

  window.GENERIC_INVALID_MESSAGE = "The information provided is invalid. Please review the field format and try again.";




  window.translation = {
    common: {
      selectedList: '{quantity} list selected',
      selectedLists: '{quantity} lists selected'
    }
  };

  var AUTOHIDE = Boolean(0);
</script>
<script src="https://sibforms.com/forms/end-form/build/main.js"></script>
<script src="https://www.recaptcha.net/recaptcha/api.js" async defer></script>
    </div>
    <link rel="stylesheet" type="text/css" href="/static/css/font-awesome.css">

    <script type="text/javascript" src="/static/js/core.min.js" charset="utf-8"></script>
    
<script>
// $('.search-query').focus( function(){
//     $(this).parent().css('backgroundColor', 'transparent');
//     $(this).css('color', 'white');
//     if( $(this).parent().attr('id') == $('#navbar-search').attr('id')){
//         $('#navbar-search').css('borderColor', '#304e70');

//     }
// }).focusout(function(){
//     $(this).parent().css('backgroundColor', 'white');
//     $(this).css('color', '#082c48');
//     if( $(this).parent().attr('id') == $('#navbar-search').attr('id')){
//         $('#navbar-search').css('borderColor', '#d1d1d1');

//     }
// });
</script>
<script>

// ux for search button highlithing the btn just when search is valid
// $("#navbar-search input[type='text']").bind("change paste keyup", function() {
//     if( $(this).val().length > 0 ){
//         $(this).next().css({
//             'borderColor': '#304e70',
//             'color': '#304e70'
//         });
//     }else{
//         $(this).next().css({
//             'borderColor': '#d1d1d1',
//             'color': '#d1d1d1'
//         });
//     }
// });

</script>
<script>
    var stop_mixpanel_logging = 10;
    // var mixpanelWait = setInterval(function() {
    //   if (mixpanel) {
    //     mixpanel.track_forms("#navbar-search-form", "Navbar Search", search_form_data);
    //     clearInterval(mixpanelWait);
    //   }else{
    //     stop_mixpanel_logging = stop_mixpanel_logging - 1;
    //     if(stop_mixpanel_logging < 0){
    //         clearInterval(mixpanelWait);
    //     }
    //   }
    // }, 100);

    // var search_form_data = function () {
    //     d = {};
    //     d["query"]=$("#navbar-serch-query").val();
    //     return d;
    // }
</script>
    











<script>
var ua = window.navigator.userAgent;
var isIE = /MSIE|Trident/.test(ua);
var filterUsed = false;


$.urlParam = function(name){
    var results = new RegExp('[\?&]' + name + '=([^&#]*)').exec(window.location.href);
    if (results == null){
        return undefined;
    }
    else {
        return decodeURI(results[1]) || 0;
    }
}

var IEPARAMS = [];
var IEParams = function(){
    return {
        init : function(){
            var url = window.location.href;
            IEPARAMS = [];
            if(url.indexOf('?')!=-1){
                var ary = url.split('?')[1].split('&');
                if(ary.length > 0){
                    for(i=0;i<=ary.length-1;i++){
                        var splitted = ary[i].split('=');
                        if(splitted.length > 1){
                            IEPARAMS.push({ name : splitted[0], value : splitted[1]});
                        }
                    }
                }
            }
        },
        get : function(name, is_array){
            if(is_array === undefined){
                is_array = false;
            }
            if(IEPARAMS.length == 0){
                this.init();
            }

            var results = [];
            IEPARAMS.some(function(obj, i) {
                if (obj.name == name) {
                    results.push(obj.value);
                }
            });

            if(is_array){
                return results;
            }else{
                if(results.length > 0){
                    return results[0];
                }
            }
            return "";//empty if not found
        }
    }
}();

function checkFilterUsed() {
    const urlParams = new URLSearchParams(window.location.search);
    compLeads = urlParams.get('leadFilter');
    compROHS = urlParams.get('RoHSFilter');
    manufacturerList = urlParams.getAll('manufacturer');
    packageList = urlParams.getAll('package');
    dimensionList = urlParams.getAll('dimension');
    mountingTypeList = urlParams.getAll('mounting_type');
    inStockFilter = urlParams.get('stockFilter');
    querystring = urlParams.get('q');
    if(compLeads === '1' || compROHS === '1' || inStockFilter === '1' || manufacturerList.length > 0 || packageList.length > 0 || dimensionList.length > 0 || mountingTypeList.length > 0 || $('.cad-filters input[type="checkbox"]:checked').length>0 ) {
        return true;
    }
    return false;
}

function init() {

    var compLeads;
    var compROHS;
    var manufacturerList = [];
    var inStockFilter;
    var packageList = [];
    var dimensionList = [];
    var mountingTypeList = [];
    var querystring;

    if(isIE) {
        
        compLeads = $.urlParam('leadFilter');
        compROHS = $.urlParam('RoHSFilter');
        inStockFilter = $.urlParam('stockFilter');
        querystring = $.urlParam('q');
        packageList = IEParams.get('package', true);
        dimensionList = IEParams.get('dimension', true);
        manufacturerList = IEParams.get('manufacturer', true);
        mountingTypeList = IEParams.get('mounting_type', true);
    } else {
        const urlParams = new URLSearchParams(window.location.search);
        compLeads = urlParams.get('leadFilter');
        compROHS = urlParams.get('RoHSFilter');
        manufacturerList = urlParams.getAll('manufacturer');
        packageList = urlParams.getAll('package');
        dimensionList = urlParams.getAll('dimension');
        mountingTypeList = urlParams.getAll('mounting_type');
        inStockFilter = urlParams.get('stockFilter');
        querystring = urlParams.get('q');
    }
    manufacturerList = manufacturerList.filter(function (word) {
        return word
    });
    packageList = packageList.filter(function (word) {
        return word
    });

    
    $('#filter-spinner').show();
    // add filter values in dom elements
    // field name = filter field (e.g. manufacturer, package, etc.)
    // currentSelectedList = array of values currently ticked based on URL params
    // filterClassName = classname for filter used for jquery selector

    let filters = [
        {"fieldName": 'manufacturer', "currentSelectedList": manufacturerList, "filterClassName": 'manufacturers-filter'},
        {"fieldName": 'package', "currentSelectedList": packageList, "filterClassName": 'packages-filter'},
        {"fieldName": 'mounting_type', "currentSelectedList": mountingTypeList, "filterClassName": 'mounting-types-filter'},
        {"fieldName": 'dimension', "currentSelectedList": dimensionList, "filterClassName": 'dimensions-filter'}
    ];

    let requestURL = "/api/v1/search_available_field_values/all";
    var qstring = 'q=APX803L20-30SA-7&amp;page=1';
    if (qstring){
       qstring = qstring.replace(/&amp;/g, '&') + "&";
    }
    qstring += "aggs=1";
    $.ajax({
        url: requestURL + '?' + qstring,
        dataType:"json",
        success:function(values)
        {
            for (let x in filters) {
                var filterHtml = '';
                var selectedHtml = '';
                var fieldName = filters[x]['fieldName'];
                var currentSelectedList = filters[x]['currentSelectedList'];
                var filterClassName = filters[x]['filterClassName'];
                var _valueList = [];
                values[fieldName].forEach(function(element) {
                    var id = element.id.replace( /\s/g, '');
                    var _elementName = element.name;
                    if(fieldName.indexOf('dimension') !== -1) {
                        _elementName = element.name.replace(/['"]+/g, '');
                        id = id.replace(/['"]+/g, '');
                    }
                    if(id) {
                        if (currentSelectedList.indexOf(_elementName) !== -1) {
                            selectedHtml += '<div class="d-flex align-items-center"><input  checked="true" type="checkbox" ' + fieldName + '-name="' + _elementName + '" id="' + id + '" name="' + fieldName + '" value="' + _elementName + '"><label class="list-item-label" for="' + id + '"> ' + element.name + '</label></div>';
                        } else {
                            filterHtml += '<div class="d-flex align-items-center"><input type="checkbox" ' + fieldName + '-name="' + _elementName + '"  id="' + id + '" name="' + fieldName + '" value="' + _elementName + '"><label class="list-item-label" for="' + id + '"> ' + element.name + '</label></div>';
                        }
                        _valueList.push(id)
                    }
                });
                currentSelectedList.forEach(function(element) {
                    var _id = element.replace( /\s/g, '');
                    if(fieldName.indexOf('dimension') !== -1) {
                        _id = _id.replace(/['"]+/g, '');
                    }
                    if(_id) {
                        if (_valueList.indexOf(_id) == -1) {
                            if (element.name !== undefined) {
                               selectedHtml += '<div class="d-flex align-items-center"><input  checked="true" type="checkbox" ' + fieldName + '-name="' + element + '" id="' + _id + '" name="' + fieldName + '" value="' + element + '"><label class="list-item-label" for="' + _id + '"> ' + element.name + '</label></div>';
                            }
                        }
                    }
                })
                $('.' + filterClassName).html(selectedHtml + filterHtml);
            }
            applyParametricFilter();
        },
        error :function(e){
            console.log(e)
            applyParametricFilter()
        }
    })

    function applyParametricFilter() {
        if(compLeads === '1') {
            $('#Lead').prop('checked','true');
        } else {
            $('#Lead').removeAttr('checked  ');
        }
        if(compROHS === '1') {
            $('#RoHS').prop('checked','true');
        } else {
            $('#RoHS').removeAttr('checked');
        }
        if(inStockFilter === '1') {
            $('#avialabilityToggle').prop('checked','true');
        } else {
            $('#avialabilityToggle').removeAttr('checked');
        }

        $('.search-filters').slideToggle();
        $('.adv-search').hide();
        filterUsed = true;
        $('.adv-search-selected').show();
        
            var Properties = {
                'userid': 'Not Signed in',
                'Type': 'Search Page',
            }
            mixpanel.track('SearchFilter', Properties);
        

        $('#filter-spinner').hide();
    }
}

function addEvents() {
    $('.manufacturers-filter input[type="checkbox"]').on('click', function(event){
        $('.manufacturers-filter input[type="checkbox"]').not(this).prop('checked', false);
    });
    $('.packages-filter input[type="checkbox"]').on('click', function(event){
        $('.packages-filter input[type="checkbox"]').not(this).prop('checked', false);
    });
    $('.dimensions-filter input[type="checkbox"]').on('click', function(event){
        $('.dimensions-filter input[type="checkbox"]').not(this).prop('checked', false);
    });
    $( "input:checkbox[id='symbol']" ).change(function() {
        var ischecked= $(this).is(':checked');
        if(ischecked){
            $( "input:checkbox[id='footprint']" ).prop('checked','true');
        }
    });

    $('.adv-search').click(function(event){
        $(this).hide();
        init();
    });

    $('.adv-search-selected').click(function(event){
        clearAll();
        $('.search-filters').slideToggle();
        $(this).hide();
        filterUsed = false;
        $('.adv-search').show();
    });

    if (checkFilterUsed()) {
        // trigger adv-search click event
        $('.adv-search').trigger('click');
    }
}

function getFilterValues() {
    var filters = {
        cad: [],
        manufacturer: [],
        compliance: [],
        package: [],
        avialability: false
    }
    $('.cad-filters input:checked').each(function() {
        filters.cad.push($(this).attr('name'));
    });
    $('.manufacturers-filter input:checked').each(function() {
        filters.manufacturer.push($(this).attr('value'));
    });

    $('.packages-filter input:checked').each(function() {
        filters.package.push($(this).attr('value'));
    });

    $('.dimensions-filter input:checked').each(function() {
        filters.dimension.push($(this).attr('value'));
    });


    $('.compliance-filter input:checked').each(function() {
        filters.compliance.push($(this).attr('value'));
    });
    filters.avialability = $('input#avialabilityToggle:checked').length === 0 ? false : true;

    return filters;
}

function clearAll() {
    $('.search-filters input[type="checkbox"]').removeAttr('checked');
}

function updateSortBy(type)
{
    $('#sortSelected').attr("value",type);
    $('#searchSubmit').trigger( "click" );
}

$('#searchForm').submit(function() {
    $('#searchForm').find('input').each(function() {
        var input = $(this);
        if (!input.val()) {
            input.remove(); // or input.prop('disabled', true);
        }
    });
});


$(".search_request_button").on("click", function(){

    var Properties = {
        'Type': 'Search Page',
        'userid': ''
    }
    mixpanel.track('Request', Properties);
    console.log(Properties);

});

$('.search-tab').click(function(event) {
    $(".search-tab").removeClass('active');
    $(this).addClass('active');

    if ($(this).hasClass('search-component')){
        $(".search-part-package-section").removeClass('hidden');
        $(".search-resistor-section").addClass('hidden');
        $("input.search-type").val('parts');
        $('#filter-options').removeClass('hidden');
        $(".package-example").addClass('hidden');
        $('.mainSearch').attr('placeholder', 'Search by Part Numnber or Type');
        $('.searchForm').attr('action', '/search');
        $(".search-connector-section").addClass('hidden');
        $('#matchingproducts').show();
    }else if ($(this).hasClass('search-package')){
        $(".search-part-package-section").removeClass('hidden');
        $(".search-resistor-section").addClass('hidden');
        $(".package-example").removeClass('hidden');
        $("input.search-type").val('packages');
        $('#filter-options').addClass('hidden');
        $('.mainSearch').attr('placeholder', 'Search By Package or IPC Name');
        $('.searchForm').attr('action', '/search');
        $(".search-connector-section").addClass('hidden');
    }else if ($(this).hasClass('search-resistor')){
        $(".search-part-package-section").addClass('hidden');
        $(".search-resistor-section").removeClass('hidden');
        $("input.search-type").val('resistor');
        $('.searchForm').attr('action', '/search-resistor');
        $(".search-connector-section").addClass('hidden');
    }else if ($(this).hasClass('search-connector')){
        $(".search-part-package-section").addClass('hidden');
        $(".search-resistor-section").addClass('hidden');
        $(".search-connector-section").removeClass('hidden');
        $('#matchingproducts').hide();

        var Properties = {
            'userid': ''
        }
        mixpanel.track('Clicked on connector search', Properties);
        // $("input.search-type").val('resistor');
        // $('.searchForm').attr('action', '/search-resistor');
    }
});


// $(".search-component").click(function(event) {
//     $(".search-tab").removeClass('active');
//     $(".search-component").addClass('active');
//     $("input.search-type").val('parts');
//     $('#filter-options').removeClass('hidden');
//     $(".package-example").addClass('hidden');
//     $('.mainSearch').attr('placeholder', 'Search by Part Numnber or Type');
// });

// $(".search-package").click(function(event) {
//     $(".package-example").removeClass('hidden');
//     $(".search-tab").removeClass('active');
//     $(".search-package").addClass('active');
//     $("input.search-type").val('packages');
//     $('#filter-options').addClass('hidden');
//     $('.mainSearch').attr('placeholder', 'Search By Package or IPC Name');
// });

</script>



    <script>
        $("a[rel*=leanModal]").leanModal(
          {
            top : 10,
            closeButton: ".modal-close",
            overlay : 0.4
          }
        );

        $( "#lean_overlay" ).click(function() {
          var vid_src = $("#s-video-home").attr('src');
          $("#s-video-home").attr('src','');
          $("#s-video-home").attr('src', vid_src);
        });
        $( ".modal-close" ).click(function() {
          var vid_src = $("#s-video-home").attr('src');
          $("#s-video-home").attr('src','');
          $("#s-video-home").attr('src', vid_src);
        });



    

    $(document).ready(function() {
            
        });

    </script>

    
        <script>
            window.intercomSettings = {
              app_id: "owbpizd5"
            };
        </script>
    

    <script>(function(){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',intercomSettings);}else{var d=document;var i=function(){i.c(arguments)};i.q=[];i.c=function(args){i.q.push(args)};w.Intercom=i;function l(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://widget.intercom.io/widget/owbpizd5';var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s,x);}if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}})()</script>

    

    <script type="text/javascript">
        window.addEventListener('message', (event) => {
            const { type, payload } = event.data;
            if (type === 'notify intercom') {
                window.intercomSettings['used_web_copilot'] = true;
                window.Intercom('update', window.intercomSettings);
            }
        });
    </script>


    <script type="text/javascript">
        adroll_adv_id = "E5JOHRIMLRBBZHMVKSEUGT";
        adroll_pix_id = "OINORE6YJVH6XI52DJKCXZ";
        
        (function () {
            var _onload = function(){
                if (document.readyState && !/loaded|complete/.test(document.readyState)){setTimeout(_onload, 10);return}
                if (!window.__adroll_loaded){__adroll_loaded=true;setTimeout(_onload, 50);return}
                var scr = document.createElement("script");
                var host = (("https:" == document.location.protocol) ? "https://s.adroll.com" : "http://a.adroll.com");
                scr.setAttribute('async', 'true');
                scr.type = "text/javascript";
                scr.src = host + "/j/roundtrip.js";
                ((document.getElementsByTagName('head') || [null])[0] ||
                    document.getElementsByTagName('script')[0].parentNode).appendChild(scr);
            };
            if (window.addEventListener) {window.addEventListener('load', _onload, false);}
            else {window.attachEvent('onload', _onload)}
        }());


    </script>

    <!-- Sentry integration -->
    <script src='https://js.sentry-cdn.com/d2019c20912d44c3b7ee0c0f42296c42.min.js' crossorigin="anonymous"></script>
    <script>
      Sentry.init({ dsn: 'https://<EMAIL>/1286597',
        tracesSampleRate: 0.2 });
    </script>

    <script>!function () {var reb2b = window.reb2b = window.reb2b || []; if (reb2b.invoked) return;reb2b.invoked = true;reb2b.methods = ["identify", "collect"]; reb2b.factory = function (method) {return function () {var args = Array.prototype.slice.call(arguments); args.unshift(method);reb2b.push(args);return reb2b;};}; for (var i = 0; i < reb2b.methods.length; i++) {var key = reb2b.methods[i];reb2b[key] = reb2b.factory(key);} reb2b.load = function (key) {var script = document.createElement("script");script.type = "text/javascript";script.async = true; script.src = "https://s3-us-west-2.amazonaws.com/b2bjsstore/b/" + key + "/reb2b.js.gz"; var first = document.getElementsByTagName("script")[0]; first.parentNode.insertBefore(script, first);}; reb2b.SNIPPET_VERSION = "1.0.1";reb2b.load("5NRP9HG3Z2O1");}();</script>
</body>
</html>
