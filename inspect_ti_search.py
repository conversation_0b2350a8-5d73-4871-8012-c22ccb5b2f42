#!/usr/bin/env python3
"""
Inspect TI search box to get exact HTML and selectors
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def inspect_ti_search():
    print("INSPECTING TI SEARCH BOX")
    print("=" * 30)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        print("1. Loading ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(20)
        
        print("2. Getting all HTML elements that might be search...")
        
        # Get all elements with search-related attributes
        js_code = """
        var searchElements = [];
        
        // Find all elements with search-related attributes
        var allElements = document.querySelectorAll('*');
        
        for (var i = 0; i < allElements.length; i++) {
            var elem = allElements[i];
            var html = elem.outerHTML;
            
            if (html.toLowerCase().includes('search') || 
                html.toLowerCase().includes('coveo') ||
                elem.tagName === 'INPUT') {
                
                searchElements.push({
                    tag: elem.tagName,
                    id: elem.id || '',
                    className: elem.className || '',
                    type: elem.type || '',
                    placeholder: elem.placeholder || '',
                    name: elem.name || '',
                    html: elem.outerHTML.substring(0, 200)
                });
            }
        }
        
        return searchElements;
        """
        
        elements = driver.execute_script(js_code)
        
        print(f"Found {len(elements)} search-related elements:")
        
        for i, elem in enumerate(elements):
            print(f"\nElement {i+1}:")
            print(f"  Tag: {elem['tag']}")
            print(f"  ID: {elem['id']}")
            print(f"  Class: {elem['className']}")
            print(f"  Type: {elem['type']}")
            print(f"  Placeholder: {elem['placeholder']}")
            print(f"  Name: {elem['name']}")
            print(f"  HTML: {elem['html']}")
        
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    inspect_ti_search()
