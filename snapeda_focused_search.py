#!/usr/bin/env python3
"""
Focused SnapEDA search - directly search their main page
"""

import requests
from bs4 import BeautifulSoup
import json
import time

def search_snapeda_main_page(part_number):
    print(f"🔍 FOCUSED SNAPEDA SEARCH FOR: {part_number}")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    print("1. Getting SnapEDA main page...")
    
    try:
        # First get the main page to see the search form
        main_response = session.get('https://www.snapeda.com/', timeout=30)
        print(f"   Main page status: {main_response.status_code}")
        
        if main_response.status_code != 200:
            print(f"   ❌ Failed to get main page")
            return False
        
        # Save main page
        with open('snapeda_homepage.html', 'w', encoding='utf-8') as f:
            f.write(main_response.text)
        print("   📄 Saved main page")
        
        # Parse to find search form
        soup = BeautifulSoup(main_response.text, 'html.parser')
        
        # Look for search form
        search_form = soup.find('form')
        if search_form:
            print(f"   ✅ Found search form")
            action = search_form.get('action', '/search')
            method = search_form.get('method', 'GET').upper()
            print(f"   Form action: {action}")
            print(f"   Form method: {method}")
        
        # Look for search input field
        search_inputs = soup.find_all('input', {'type': ['search', 'text']})
        search_field_name = None
        
        for inp in search_inputs:
            name = inp.get('name', '')
            placeholder = inp.get('placeholder', '').lower()
            if 'search' in name.lower() or 'search' in placeholder or 'part' in placeholder:
                search_field_name = name
                print(f"   ✅ Found search field: {name}")
                break
        
        if not search_field_name:
            # Try common field names
            for common_name in ['q', 'query', 'search', 'term', 'keyword']:
                search_field_name = common_name
                print(f"   🔍 Trying common field name: {common_name}")
                break
        
        print(f"\n2. Searching for {part_number}...")
        
        # Try different search approaches
        search_attempts = [
            {
                'url': 'https://www.snapeda.com/search',
                'params': {search_field_name or 'q': part_number}
            },
            {
                'url': 'https://www.snapeda.com/search',
                'params': {'q': part_number}
            },
            {
                'url': 'https://www.snapeda.com/search',
                'params': {'term': part_number}
            },
            {
                'url': f'https://www.snapeda.com/parts/{part_number}',
                'params': {}
            }
        ]
        
        for i, attempt in enumerate(search_attempts, 1):
            print(f"\n   Attempt {i}: {attempt['url']}")
            print(f"   Params: {attempt['params']}")
            
            try:
                if attempt['params']:
                    search_response = session.get(attempt['url'], params=attempt['params'], timeout=30)
                else:
                    search_response = session.get(attempt['url'], timeout=30)
                
                print(f"   Status: {search_response.status_code}")
                print(f"   Final URL: {search_response.url}")
                
                if search_response.status_code == 200:
                    # Save this response
                    filename = f'snapeda_search_attempt_{i}.html'
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(search_response.text)
                    print(f"   📄 Saved to {filename}")
                    
                    # Check if we found the part
                    response_text = search_response.text.lower()
                    if part_number.lower() in response_text:
                        print(f"   ✅ Found {part_number} in response!")
                        
                        # Look for download links
                        soup = BeautifulSoup(search_response.text, 'html.parser')
                        
                        # Look for STEP/3D model links
                        step_links = []
                        for link in soup.find_all('a', href=True):
                            href = link.get('href', '').lower()
                            text = link.get_text(strip=True).lower()
                            
                            if any(keyword in href or keyword in text for keyword in ['step', '3d', 'model', 'cad', 'download']):
                                step_links.append({
                                    'url': link.get('href'),
                                    'text': link.get_text(strip=True)
                                })
                        
                        if step_links:
                            print(f"   🎯 Found {len(step_links)} potential 3D/STEP links:")
                            for j, link in enumerate(step_links[:5], 1):  # Show first 5
                                print(f"   {j}. {link['text']}")
                                print(f"      URL: {link['url']}")
                            return True
                        else:
                            print(f"   ⚠️  Part found but no 3D/STEP links detected")
                            
                            # Look for any download links
                            download_links = []
                            for link in soup.find_all('a', href=True):
                                text = link.get_text(strip=True).lower()
                                if 'download' in text:
                                    download_links.append({
                                        'url': link.get('href'),
                                        'text': link.get_text(strip=True)
                                    })
                            
                            if download_links:
                                print(f"   📥 Found {len(download_links)} download links:")
                                for j, link in enumerate(download_links[:3], 1):
                                    print(f"   {j}. {link['text']}")
                            
                            return True
                    else:
                        print(f"   ❌ {part_number} not found in response")
                        
                        # Check if we got a search results page
                        if 'search' in search_response.url.lower() or 'results' in response_text:
                            print(f"   📋 Got search results page, but no matches")
                        
                elif search_response.status_code == 404:
                    print(f"   ❌ Not found (404)")
                else:
                    print(f"   ❌ Failed with status {search_response.status_code}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                continue
        
        print(f"\n❌ All search attempts failed for {part_number}")
        return False
        
    except Exception as e:
        print(f"❌ Main error: {e}")
        return False

def main():
    part_number = "APX803L20-30SA-7"
    
    print("🚀 SNAPEDA FOCUSED SEARCH")
    print("=" * 60)
    
    success = search_snapeda_main_page(part_number)
    
    print("\n" + "=" * 60)
    if success:
        print("✅ SUCCESS: Found part on SnapEDA!")
    else:
        print("❌ FAILED: Part not found on SnapEDA")
        print("\nNext steps:")
        print("1. Try different part number variations")
        print("2. Check manufacturer website directly")
        print("3. Try other 3D model sources")

if __name__ == "__main__":
    main()
