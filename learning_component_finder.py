#!/usr/bin/env python3
"""
Learning Component Finder - Saves Successful Search Patterns

This system learns from successful searches and saves the patterns for future use:
1. Saves successful search URLs and methods for each manufacturer
2. Stores extraction patterns that worked for finding part pages
3. Caches datasheet and 3D model link patterns
4. Builds a knowledge base that improves over time

Usage:
    python learning_component_finder.py "Diodes Inc" "APX803L20-30SA-7"
"""

import requests
from bs4 import BeautifulSoup
import re
import os
import json
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse, quote
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LearningComponentFinder:
    def __init__(self, download_dir="files-download", knowledge_base_file="manufacturer_knowledge.json"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.knowledge_base_file = Path(knowledge_base_file)
        self.session = self._create_session()
        
        # Load existing knowledge base
        self.knowledge_base = self._load_knowledge_base()
    
    def _create_session(self):
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive'
        })
        return session
    
    def _load_knowledge_base(self):
        """Load the manufacturer knowledge base from file"""
        if self.knowledge_base_file.exists():
            try:
                with open(self.knowledge_base_file, 'r') as f:
                    knowledge = json.load(f)
                logger.info(f"📚 Loaded knowledge base with {len(knowledge)} manufacturers")
                return knowledge
            except Exception as e:
                logger.error(f"Error loading knowledge base: {e}")
        
        # Default knowledge base structure
        return {}
    
    def _save_knowledge_base(self):
        """Save the knowledge base to file"""
        try:
            with open(self.knowledge_base_file, 'w') as f:
                json.dump(self.knowledge_base, f, indent=2)
            logger.info(f"💾 Saved knowledge base to {self.knowledge_base_file}")
        except Exception as e:
            logger.error(f"Error saving knowledge base: {e}")
    
    def _get_manufacturer_key(self, manufacturer):
        """Get normalized manufacturer key"""
        return manufacturer.lower().strip()
    
    def _initialize_manufacturer_knowledge(self, manufacturer):
        """Initialize knowledge structure for a new manufacturer"""
        key = self._get_manufacturer_key(manufacturer)
        if key not in self.knowledge_base:
            self.knowledge_base[key] = {
                "name": manufacturer,
                "base_url": "",
                "search_patterns": {
                    "successful_urls": [],
                    "search_methods": [],
                    "part_extraction_patterns": []
                },
                "datasheet_patterns": {
                    "url_patterns": [],
                    "link_selectors": []
                },
                "3d_model_patterns": {
                    "url_patterns": [],
                    "link_selectors": [],
                    "file_extensions": [".step", ".stp"]
                },
                "package_extraction": {
                    "table_patterns": [],
                    "text_patterns": []
                },
                "success_count": 0,
                "last_updated": time.time()
            }
    
    def learn_from_successful_search(self, manufacturer, part_number, search_url, search_method, part_page_url):
        """Learn from a successful search and save the pattern"""
        key = self._get_manufacturer_key(manufacturer)
        self._initialize_manufacturer_knowledge(manufacturer)
        
        knowledge = self.knowledge_base[key]
        
        # Save successful search URL pattern
        if search_url not in knowledge["search_patterns"]["successful_urls"]:
            knowledge["search_patterns"]["successful_urls"].append(search_url)
            logger.info(f"📚 Learned new search URL pattern: {search_url}")
        
        # Save search method
        if search_method not in knowledge["search_patterns"]["search_methods"]:
            knowledge["search_patterns"]["search_methods"].append(search_method)
            logger.info(f"📚 Learned new search method: {search_method}")
        
        # Extract base URL if not set
        if not knowledge["base_url"]:
            knowledge["base_url"] = f"{urlparse(search_url).scheme}://{urlparse(search_url).netloc}"
            logger.info(f"📚 Learned base URL: {knowledge['base_url']}")
        
        # Increment success count
        knowledge["success_count"] += 1
        knowledge["last_updated"] = time.time()
        
        self._save_knowledge_base()
    
    def learn_datasheet_pattern(self, manufacturer, datasheet_url, extraction_method):
        """Learn from successful datasheet extraction"""
        key = self._get_manufacturer_key(manufacturer)
        if key not in self.knowledge_base:
            return
        
        knowledge = self.knowledge_base[key]
        
        # Extract URL pattern
        base_url = knowledge.get("base_url", "")
        if base_url and datasheet_url.startswith(base_url):
            pattern = datasheet_url.replace(base_url, "")
            if pattern not in knowledge["datasheet_patterns"]["url_patterns"]:
                knowledge["datasheet_patterns"]["url_patterns"].append(pattern)
                logger.info(f"📚 Learned datasheet URL pattern: {pattern}")
        
        # Save extraction method
        if extraction_method not in knowledge["datasheet_patterns"]["link_selectors"]:
            knowledge["datasheet_patterns"]["link_selectors"].append(extraction_method)
            logger.info(f"📚 Learned datasheet extraction method: {extraction_method}")
        
        self._save_knowledge_base()
    
    def learn_3d_model_patterns(self, manufacturer, model_urls, extraction_methods):
        """Learn from successful 3D model extraction"""
        key = self._get_manufacturer_key(manufacturer)
        if key not in self.knowledge_base:
            return
        
        knowledge = self.knowledge_base[key]
        base_url = knowledge.get("base_url", "")
        
        for url in model_urls:
            # Extract URL pattern
            if base_url and url.startswith(base_url):
                pattern = url.replace(base_url, "")
                # Generalize the pattern by replacing specific package names with placeholder
                generalized_pattern = re.sub(r'(SOT\d+|SC\d+|QFN\d*|BGA\d*)', '{package}', pattern, flags=re.IGNORECASE)
                
                if generalized_pattern not in knowledge["3d_model_patterns"]["url_patterns"]:
                    knowledge["3d_model_patterns"]["url_patterns"].append(generalized_pattern)
                    logger.info(f"📚 Learned 3D model URL pattern: {generalized_pattern}")
        
        # Save extraction methods
        for method in extraction_methods:
            if method not in knowledge["3d_model_patterns"]["link_selectors"]:
                knowledge["3d_model_patterns"]["link_selectors"].append(method)
                logger.info(f"📚 Learned 3D model extraction method: {method}")
        
        self._save_knowledge_base()
    
    def learn_package_extraction(self, manufacturer, package_type, extraction_method, context):
        """Learn from successful package type extraction"""
        key = self._get_manufacturer_key(manufacturer)
        if key not in self.knowledge_base:
            return
        
        knowledge = self.knowledge_base[key]
        
        pattern_info = {
            "package": package_type,
            "method": extraction_method,
            "context": context,
            "timestamp": time.time()
        }
        
        if extraction_method == "table_extraction":
            knowledge["package_extraction"]["table_patterns"].append(pattern_info)
        else:
            knowledge["package_extraction"]["text_patterns"].append(pattern_info)
        
        logger.info(f"📚 Learned package extraction: {package_type} via {extraction_method}")
        self._save_knowledge_base()
    
    def use_learned_patterns(self, manufacturer, part_number):
        """Use previously learned patterns to search for a component"""
        key = self._get_manufacturer_key(manufacturer)
        
        if key not in self.knowledge_base:
            logger.info(f"🆕 No learned patterns for {manufacturer}, will learn from this search")
            return None
        
        knowledge = self.knowledge_base[key]
        logger.info(f"📚 Using learned patterns for {manufacturer} (success count: {knowledge['success_count']})")
        
        # Try learned search patterns
        for search_url_pattern in knowledge["search_patterns"]["successful_urls"]:
            try:
                # Replace the previous part number with the new one
                search_url = search_url_pattern.replace("APX803L20-30SA-7", part_number)
                search_url = re.sub(r'q=[^&]+', f'q={quote(part_number)}', search_url)
                
                logger.info(f"🔍 Trying learned search pattern: {search_url}")
                
                response = self.session.get(search_url, timeout=30)
                if response.status_code == 200 and part_number.lower() in response.text.lower():
                    logger.info(f"✅ Learned pattern worked!")
                    return self._extract_part_page_from_search_learned(response.text, part_number, knowledge)
                    
            except Exception as e:
                logger.debug(f"Learned pattern failed: {e}")
                continue
        
        return None
    
    def _extract_part_page_from_search_learned(self, search_html, part_number, knowledge):
        """Extract part page using learned patterns"""
        soup = BeautifulSoup(search_html, 'html.parser')
        
        # Use learned extraction patterns
        base_part = part_number.split('-')[0]
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
        
        # Try to find the part family link
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().strip()
            
            if (part_family.lower() in text.lower() or 
                part_family.lower() in href.lower()):
                
                base_url = knowledge.get("base_url", "https://www.diodes.com")
                full_url = urljoin(base_url, href)
                
                logger.info(f"✅ Found part page using learned patterns: {full_url}")
                
                try:
                    response = self.session.get(full_url, timeout=30)
                    if response.status_code == 200:
                        return response.text, full_url
                except Exception as e:
                    logger.error(f"Failed to get part page: {e}")
        
        return None, None
    
    def find_component_with_learning(self, manufacturer, part_number):
        """Find component using learned patterns, and learn from the process"""
        results = {
            'manufacturer': manufacturer,
            'part_number': part_number,
            'package_type': None,
            'datasheet_file': None,
            '3d_model_files': [],
            'part_page_url': None,
            'used_learned_patterns': False,
            'success': False
        }
        
        logger.info(f"🔍 Searching for {manufacturer} {part_number} with learning")
        
        # Step 1: Try to use learned patterns first
        learned_result = self.use_learned_patterns(manufacturer, part_number)
        
        if learned_result:
            part_html, part_page_url = learned_result
            results['used_learned_patterns'] = True
            logger.info("✅ Used learned patterns successfully!")
        else:
            # Step 2: Fall back to discovery mode (like the original proper_component_finder)
            logger.info("🔍 No learned patterns available, using discovery mode")
            part_html, part_page_url = self._discover_search_method(manufacturer, part_number)
            
            if part_html and part_page_url:
                # Learn from this successful discovery
                search_url = f"https://www.diodes.com/search?q={quote(part_number)}"
                self.learn_from_successful_search(manufacturer, part_number, search_url, "GET", part_page_url)
        
        if not part_html:
            logger.error("❌ Could not find part page")
            return results
        
        results['part_page_url'] = part_page_url
        
        # Step 3: Extract information and learn from successful extractions
        package_type = self._extract_package_type_and_learn(part_html, part_number, manufacturer)
        results['package_type'] = package_type
        
        datasheet_file = self._extract_datasheet_and_learn(part_html, part_page_url, part_number, manufacturer)
        results['datasheet_file'] = datasheet_file
        
        model_files = self._extract_3d_models_and_learn(part_html, part_page_url, part_number, manufacturer, package_type)
        results['3d_model_files'] = model_files
        
        results['success'] = bool(results['datasheet_file'] or results['3d_model_files'])
        return results
    
    def _discover_search_method(self, manufacturer, part_number):
        """Discover search method for unknown manufacturer (fallback to original logic)"""
        if manufacturer.lower() == "diodes inc":
            try:
                search_url = f"https://www.diodes.com/search?q={quote(part_number)}"
                response = self.session.get(search_url, timeout=30)
                
                if response.status_code == 200 and part_number.lower() in response.text.lower():
                    return self._extract_part_page_from_search_original(response.text, part_number)
            except Exception as e:
                logger.error(f"Discovery search failed: {e}")
        
        return None, None
    
    def _extract_part_page_from_search_original(self, search_html, part_number):
        """Original extraction logic for discovery mode"""
        soup = BeautifulSoup(search_html, 'html.parser')
        
        base_part = part_number.split('-')[0]
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().strip()
            
            if (part_family.lower() in text.lower() or 
                part_family.lower() in href.lower()):
                
                full_url = urljoin("https://www.diodes.com", href)
                
                try:
                    response = self.session.get(full_url, timeout=30)
                    if response.status_code == 200:
                        if part_number.lower() in response.text.lower():
                            return response.text, full_url
                except Exception as e:
                    continue
        
        return None, None

    def _extract_package_type_and_learn(self, html, part_number, manufacturer):
        """Extract package type and learn from the process"""
        if not html:
            return None

        soup = BeautifulSoup(html, 'html.parser')

        # Try table extraction first
        for row in soup.find_all('tr'):
            row_text = row.get_text()
            if part_number in row_text:
                cells = row.find_all(['td', 'th'])
                for cell in cells:
                    cell_text = cell.get_text().strip()
                    common_packages = ['SOT23', 'SOT-23', 'SOT323', 'SOT-323', 'SOT25', 'SOT-25',
                                     'SC59', 'SC-59', 'SC70', 'SC-70', 'SOIC', 'QFN', 'BGA', 'DIP']

                    if cell_text in common_packages:
                        logger.info(f"📦 Found package type: {cell_text}")
                        # Learn from this successful extraction
                        self.learn_package_extraction(manufacturer, cell_text, "table_extraction", row_text[:100])
                        return cell_text

        # Fallback to text search
        html_lower = html.lower()
        package_patterns = {
            'SOT23': ['sot-23', 'sot23'],
            'SOT323': ['sot-323', 'sot323'],
            'SOT25': ['sot-25', 'sot25'],
            'SC59': ['sc-59', 'sc59']
        }

        for pkg_name, patterns in package_patterns.items():
            for pattern in patterns:
                if pattern in html_lower:
                    logger.info(f"📦 Found package type (text search): {pkg_name}")
                    self.learn_package_extraction(manufacturer, pkg_name, "text_search", pattern)
                    return pkg_name

        return None

    def _extract_datasheet_and_learn(self, html, base_url, part_number, manufacturer):
        """Extract datasheet and learn from the process"""
        if not html:
            return None

        soup = BeautifulSoup(html, 'html.parser')

        # Look for datasheet links
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().lower()

            if (href.endswith('.pdf') and
                any(keyword in text for keyword in ['datasheet', 'data sheet'])):
                full_url = urljoin(base_url, href)
                logger.info(f"✅ Found datasheet link: {full_url}")

                # Download the datasheet
                datasheet_file = self.download_file(full_url, f"{part_number}_datasheet.pdf")
                if datasheet_file:
                    # Learn from this successful extraction
                    self.learn_datasheet_pattern(manufacturer, full_url, "link_text_search")
                    return datasheet_file

        # Try regex patterns
        datasheet_patterns = [
            r'href="([^"]*\.pdf[^"]*)"[^>]*>.*?datasheet',
            r'href="([^"]*datasheet[^"]*\.pdf)"'
        ]

        for pattern in datasheet_patterns:
            matches = re.finditer(pattern, html, re.IGNORECASE)
            for match in matches:
                link = match.group(1)
                full_url = urljoin(base_url, link)
                logger.info(f"✅ Found datasheet via regex: {full_url}")

                datasheet_file = self.download_file(full_url, f"{part_number}_datasheet.pdf")
                if datasheet_file:
                    self.learn_datasheet_pattern(manufacturer, full_url, "regex_pattern")
                    return datasheet_file

        return None

    def _extract_3d_models_and_learn(self, html, base_url, part_number, manufacturer, package_type):
        """Extract 3D models and learn from the process"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')
        model_files = []
        found_urls = []
        extraction_methods = []

        # Strategy 1: Look for part-specific row in table
        target_row = None
        for row in soup.find_all('tr'):
            if part_number in row.get_text():
                target_row = row
                break

        if target_row:
            for link in target_row.find_all('a', href=True):
                href = link['href']
                if any(ext in href.lower() for ext in ['.step', '.stp']):
                    full_url = urljoin(base_url, href)
                    found_urls.append(full_url)
                    extraction_methods.append("table_row_extraction")

        # Strategy 2: Look for all STEP/STP links on page
        for link in soup.find_all('a', href=True):
            href = link['href']
            if any(ext in href.lower() for ext in ['.step', '.stp']):
                full_url = urljoin(base_url, href)
                if full_url not in found_urls:
                    found_urls.append(full_url)
                    extraction_methods.append("general_link_search")

        # Download the models
        for i, url in enumerate(found_urls):
            model_file = self.download_file(url, f"{part_number}_{package_type or 'unknown'}_{i+1}.step")
            if model_file:
                model_files.append(model_file)

        # Learn from successful extractions
        if found_urls:
            self.learn_3d_model_patterns(manufacturer, found_urls, extraction_methods)

        return model_files

    def download_file(self, url, filename=None):
        """Download file"""
        try:
            logger.info(f"📥 Downloading: {url}")
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()

            if not filename:
                filename = os.path.basename(urlparse(url).path)
                if not filename or '.' not in filename:
                    if '.pdf' in url.lower():
                        filename = f"datasheet_{int(time.time())}.pdf"
                    elif any(ext in url.lower() for ext in ['.step', '.stp']):
                        filename = f"3d_model_{int(time.time())}.step"

            filepath = self.download_dir / filename

            # Handle existing files
            counter = 1
            original_filepath = filepath
            while filepath.exists():
                name, ext = original_filepath.stem, original_filepath.suffix
                filepath = self.download_dir / f"{name}_{counter}{ext}"
                counter += 1

            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            file_size = os.path.getsize(filepath)
            logger.info(f"✅ Downloaded: {filepath} ({file_size:,} bytes)")
            return str(filepath)

        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            return None

def test_learning_system():
    """Test the learning system"""
    finder = LearningComponentFinder()
    
    # Test with the known working part
    manufacturer = "Diodes Inc"
    part_number = "APX803L20-30SA-7"
    
    print(f"🚀 Testing Learning Component Finder")
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print("=" * 60)
    
    results = finder.find_component_with_learning(manufacturer, part_number)
    
    print("\n" + "="*60)
    print("LEARNING SYSTEM RESULTS")
    print("="*60)
    print(json.dumps(results, indent=2))
    
    # Show what was learned
    print("\n" + "="*60)
    print("KNOWLEDGE BASE SUMMARY")
    print("="*60)
    
    key = finder._get_manufacturer_key(manufacturer)
    if key in finder.knowledge_base:
        knowledge = finder.knowledge_base[key]
        print(f"Manufacturer: {knowledge['name']}")
        print(f"Success Count: {knowledge['success_count']}")
        print(f"Search URLs: {len(knowledge['search_patterns']['successful_urls'])}")
        print(f"Datasheet Patterns: {len(knowledge['datasheet_patterns']['url_patterns'])}")
        print(f"3D Model Patterns: {len(knowledge['3d_model_patterns']['url_patterns'])}")
    
    return results

def main():
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description='Learning Component Finder')
    parser.add_argument('manufacturer', help='Manufacturer name')
    parser.add_argument('part_number', help='Part number')
    parser.add_argument('--show-knowledge', action='store_true', help='Show learned knowledge base')
    
    args = parser.parse_args()
    
    finder = LearningComponentFinder()
    
    if args.show_knowledge:
        print("\n" + "="*60)
        print("CURRENT KNOWLEDGE BASE")
        print("="*60)
        print(json.dumps(finder.knowledge_base, indent=2))
        return
    
    results = finder.find_component_with_learning(args.manufacturer, args.part_number)
    
    print("\n" + "="*60)
    print("LEARNING COMPONENT FINDER RESULTS")
    print("="*60)
    print(f"Manufacturer: {results['manufacturer']}")
    print(f"Part Number: {results['part_number']}")
    print(f"Used Learned Patterns: {results['used_learned_patterns']}")
    print(f"Package Type: {results['package_type'] or 'Unknown'}")
    print(f"Part Page: {results['part_page_url'] or 'Not found'}")
    print()
    
    if results['datasheet_file']:
        print(f"✅ Datasheet: {results['datasheet_file']}")
    else:
        print("❌ Datasheet: Not found")
    
    if results['3d_model_files']:
        print(f"✅ 3D Models: {len(results['3d_model_files'])} found")
        for model in results['3d_model_files']:
            print(f"  - {model}")
    else:
        print("❌ 3D Models: Not found")
    
    if results['success']:
        print("\n🎉 Search completed successfully!")
    else:
        print("\n⚠️ Search completed with limited results")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        test_learning_system()
    else:
        main()
