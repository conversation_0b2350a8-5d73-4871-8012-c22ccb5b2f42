#!/usr/bin/env python3
"""
Search the manufacturer website (Diodes Inc) for the part
Goal: Find 3D models and additional resources
"""

import requests
from bs4 import BeautifulSoup
import os

def search_diodes_website():
    print("🔍 SEARCHING DIODES INC WEBSITE")
    print("=" * 40)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # First, let's access the homepage to see the search method
    print("1. Accessing Diodes Inc homepage...")
    try:
        homepage_url = "https://www.diodes.com"
        response = session.get(homepage_url, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save homepage
            with open('diodes_homepage.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("   📄 Saved homepage")
            
            # Look for search form
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find search forms
            search_forms = soup.find_all('form')
            search_inputs = soup.find_all('input', {'type': ['search', 'text']})
            
            print(f"   Found {len(search_forms)} forms")
            print(f"   Found {len(search_inputs)} search inputs")
            
            # Look for search URLs or patterns
            search_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href', '').lower()
                if 'search' in href or 'part' in href:
                    search_links.append(link.get('href'))
            
            if search_links:
                print(f"   Found {len(search_links)} search-related links:")
                for link in search_links[:3]:
                    print(f"   - {link}")
            
            return True
        else:
            print(f"   ❌ Failed to access homepage: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def try_direct_part_search():
    print("\n2. Trying direct part search...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    part_number = "APX803L20-30SA-7"
    
    # Try common search URL patterns
    search_patterns = [
        f"https://www.diodes.com/part/search/{part_number}/",
        f"https://www.diodes.com/search?q={part_number}",
        f"https://www.diodes.com/products/search?part={part_number}",
        f"https://www.diodes.com/part/{part_number}",
    ]
    
    for i, url in enumerate(search_patterns, 1):
        try:
            print(f"   Trying pattern {i}: {url}")
            response = session.get(url, timeout=30)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                # Save the response
                filename = f'diodes_search_pattern_{i}.html'
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"   📄 Saved: {filename}")
                
                # Check if part found
                if part_number in response.text:
                    print(f"   ✅ FOUND PART in pattern {i}!")
                    return True, url, filename
                else:
                    print(f"   ❌ Part not found in response")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error with pattern {i}: {e}")
    
    return False, None, None

def main():
    print("🚀 MANUFACTURER WEBSITE SEARCH")
    print("Manufacturer: Diodes Incorporated")
    print("Website: www.diodes.com")
    print("Part: APX803L20-30SA-7")
    print("=" * 50)
    
    # Step 1: Get homepage and understand search method
    homepage_success = search_diodes_website()
    
    # Step 2: Try direct part searches
    if homepage_success:
        part_found, successful_url, response_file = try_direct_part_search()
        
        print("\n" + "=" * 50)
        if part_found:
            print("✅ SUCCESS: Found part on manufacturer website!")
            print(f"   URL: {successful_url}")
            print(f"   Response saved: {response_file}")
            print("\n📄 Next: Analyze the part page for 3D models")
        else:
            print("❌ PART NOT FOUND: Could not locate part on manufacturer website")
            print("   This might mean:")
            print("   - Different search method needed")
            print("   - Part might be discontinued")
            print("   - Need to search by base part number (APX803L)")
    else:
        print("\n❌ FAILED: Could not access manufacturer website")

if __name__ == "__main__":
    main()
