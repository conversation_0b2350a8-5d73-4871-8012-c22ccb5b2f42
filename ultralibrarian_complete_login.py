#!/usr/bin/env python3
"""
Complete UltraLibrarian login with ALL form fields
"""

import requests
from bs4 import BeautifulSoup

def complete_login_test():
    print("🔍 COMPLETE ULTRALIBRARIAN LOGIN TEST")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    # Step 1: Get login page
    print("1. Getting login page...")
    response = session.get('https://www.ultralibrarian.com/wp-login.php')
    
    if response.status_code != 200:
        print(f"   ❌ Failed: {response.status_code}")
        return False
    
    print(f"   ✅ Got login page")
    
    # Step 2: Parse ALL form fields
    soup = BeautifulSoup(response.text, 'html.parser')
    form = soup.find('form', {'id': 'loginform'})
    
    if not form:
        print("   ❌ No login form found")
        return False
    
    # Extract ALL form data
    form_data = {}
    
    # Get all input fields
    for inp in form.find_all('input'):
        name = inp.get('name')
        value = inp.get('value', '')
        input_type = inp.get('type', 'text')
        
        if name and input_type != 'submit':
            form_data[name] = value
    
    # Set credentials
    form_data['log'] = '<EMAIL>'
    form_data['pwd'] = 'Lennyai123#'
    
    # Ensure we have the submit button value
    submit_btn = form.find('input', {'type': 'submit'})
    if submit_btn and submit_btn.get('name'):
        form_data[submit_btn.get('name')] = submit_btn.get('value', 'Log In')
    
    print(f"2. Complete form data:")
    for key, value in form_data.items():
        if key == 'pwd':
            print(f"   {key}: [HIDDEN]")
        else:
            print(f"   {key}: '{value}'")
    
    # Step 3: Submit with proper headers
    session.headers.update({
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://www.ultralibrarian.com',
        'Referer': 'https://www.ultralibrarian.com/wp-login.php',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
    })
    
    print(f"3. Submitting login...")
    
    login_response = session.post(
        'https://www.ultralibrarian.com/wp-login.php',
        data=form_data,
        allow_redirects=True
    )
    
    print(f"   Status: {login_response.status_code}")
    print(f"   Final URL: {login_response.url}")
    
    # Save response
    with open('ultralibrarian_complete_response.html', 'w', encoding='utf-8') as f:
        f.write(login_response.text)
    
    # Check for success
    if 'wp-admin' in login_response.url:
        print("   ✅ SUCCESS: Redirected to wp-admin!")
        return True
    elif 'logout' in login_response.text.lower():
        print("   ✅ SUCCESS: Found logout link!")
        return True
    elif 'login_error' in login_response.text:
        print("   ❌ LOGIN ERROR")
        # Extract error message
        soup = BeautifulSoup(login_response.text, 'html.parser')
        error_div = soup.find('div', {'id': 'login_error'})
        if error_div:
            error_text = error_div.get_text(strip=True)
            print(f"   Error: {error_text}")
        return False
    else:
        print("   ❓ UNCLEAR RESULT")
        return False

def test_with_cookies():
    """Test if we can maintain session with cookies"""
    print("\n🍪 TESTING WITH COOKIE PERSISTENCE")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # First, try to access a protected page to see what cookies we get
    print("1. Testing protected page access...")
    test_response = session.get('https://www.ultralibrarian.com/wp-admin/')
    print(f"   Status: {test_response.status_code}")
    print(f"   Cookies received: {len(session.cookies)}")
    
    for cookie in session.cookies:
        print(f"   Cookie: {cookie.name} = {cookie.value[:20]}...")
    
    # Now try login
    if complete_login_test():
        print("\n2. Testing post-login access...")
        protected_response = session.get('https://www.ultralibrarian.com/wp-admin/')
        print(f"   Status: {protected_response.status_code}")
        print(f"   Final URL: {protected_response.url}")
        
        if 'wp-admin' in protected_response.url:
            print("   ✅ Successfully accessing protected area!")
            return True
    
    return False

def main():
    print("🚀 COMPLETE ULTRALIBRARIAN LOGIN TEST")
    print("This test includes ALL form fields and proper headers")
    print("=" * 60)
    
    # Test 1: Complete login
    success1 = complete_login_test()
    
    # Test 2: Cookie persistence
    success2 = test_with_cookies()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    print(f"   Complete login: {'✅ SUCCESS' if success1 else '❌ FAILED'}")
    print(f"   Cookie test: {'✅ SUCCESS' if success2 else '❌ FAILED'}")
    
    if success1 or success2:
        print("\n🎉 LOGIN WORKING!")
        print("   The UltraLibrarian login is now functional")
    else:
        print("\n❌ LOGIN STILL FAILING")
        print("   This confirms UltraLibrarian has additional security measures")
        print("   that prevent automated logins (bot detection)")

if __name__ == "__main__":
    main()
