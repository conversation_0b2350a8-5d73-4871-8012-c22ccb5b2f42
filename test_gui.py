#!/usr/bin/env python3
"""
Simple GUI test to verify tkin<PERSON> is working
"""

import tkinter as tk
from tkinter import ttk, messagebox

def test_button_click():
    messagebox.showinfo("Test", "GUI is working! ✅")

def main():
    print("🧪 Starting GUI test...")
    
    # Create main window
    root = tk.Tk()
    root.title("GUI Test - Component Finder")
    root.geometry("400x200")
    
    # Add some content
    label = ttk.Label(root, text="🧪 GUI Test Window", font=("Arial", 16))
    label.pack(pady=20)
    
    button = ttk.Button(root, text="Click to Test", command=test_button_click)
    button.pack(pady=10)
    
    info_label = ttk.Label(root, text="If you can see this window, the GUI is working!")
    info_label.pack(pady=10)
    
    print("✅ GUI window should be visible now")
    print("📋 Window title: 'GUI Test - Component Finder'")
    print("📐 Window size: 400x200")
    
    # Start the GUI
    root.mainloop()
    
    print("🏁 GUI test completed")

if __name__ == "__main__":
    main()
