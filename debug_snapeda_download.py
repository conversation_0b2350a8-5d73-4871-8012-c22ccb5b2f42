#!/usr/bin/env python3
"""
Debug SnapEDA 3D model download - find correct selectors
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time
import json

def debug_snapeda_download():
    print("🔍 DEBUGGING SNAPEDA 3D MODEL DOWNLOAD")
    print("=" * 50)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Load credentials
        with open('component_site_credentials.json', 'r') as f:
            creds = json.load(f)
            snapeda_creds = creds['SnapEDA']
        
        # Login to SnapEDA
        print("1. Logging into SnapEDA...")
        driver.get("https://www.snapeda.com/account/login")
        time.sleep(5)
        
        username_field = driver.find_element(By.NAME, "username")
        password_field = driver.find_element(By.NAME, "password")
        
        username_field.send_keys(snapeda_creds['email'])
        password_field.send_keys(snapeda_creds['password'])
        
        login_button = driver.find_element(By.CSS_SELECTOR, "input[value='Log in']")
        login_button.click()
        time.sleep(10)
        
        print("2. Going to LM358N 3D model page...")
        driver.get("https://www.snapeda.com/parts/LM358N/NOPB/Texas%20Instruments/view-part/")
        time.sleep(10)
        
        # Click 3D Model tab
        print("3. Clicking 3D Model tab...")
        try:
            model_tab = driver.find_element(By.XPATH, "//a[contains(text(), '3D Model')]")
            model_tab.click()
            time.sleep(10)
        except:
            print("Could not find 3D Model tab")
        
        print("4. Analyzing page for download elements...")
        
        # Get all elements with download-related text
        download_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Download') or contains(text(), 'download')]")
        
        print(f"Found {len(download_elements)} elements with 'Download' text:")
        for i, elem in enumerate(download_elements):
            try:
                tag = elem.tag_name
                text = elem.text.strip()
                classes = elem.get_attribute('class') or ''
                onclick = elem.get_attribute('onclick') or ''
                href = elem.get_attribute('href') or ''
                
                print(f"  {i+1}. <{tag}> '{text}'")
                print(f"      class='{classes}'")
                if onclick:
                    print(f"      onclick='{onclick}'")
                if href:
                    print(f"      href='{href}'")
                print()
            except:
                continue
        
        # Look for buttons specifically
        print("5. Looking for button elements...")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        
        print(f"Found {len(buttons)} button elements:")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                classes = btn.get_attribute('class') or ''
                onclick = btn.get_attribute('onclick') or ''
                
                if 'download' in text.lower() or 'download' in classes.lower():
                    print(f"  {i+1}. BUTTON: '{text}'")
                    print(f"      class='{classes}'")
                    if onclick:
                        print(f"      onclick='{onclick}'")
                    print()
            except:
                continue
        
        # Look for links with download in href
        print("6. Looking for download links...")
        links = driver.find_elements(By.TAG_NAME, "a")
        
        download_links = []
        for link in links:
            try:
                href = link.get_attribute('href') or ''
                text = link.text.strip()
                
                if ('download' in href.lower() or 
                    'download' in text.lower() or
                    '.step' in href.lower() or
                    '.stp' in href.lower()):
                    download_links.append((text, href))
            except:
                continue
        
        print(f"Found {len(download_links)} potential download links:")
        for i, (text, href) in enumerate(download_links):
            print(f"  {i+1}. '{text}' -> {href}")
        
        # Try clicking the most promising download element
        if download_links:
            print("7. Trying to click first download link...")
            try:
                first_link = driver.find_element(By.XPATH, f"//a[@href='{download_links[0][1]}']")
                first_link.click()
                time.sleep(10)
                print("✅ Clicked download link!")
                
                # Check if file was downloaded
                import os
                downloads_dir = os.path.expanduser("~/Downloads")
                files_before = set(os.listdir(downloads_dir))
                time.sleep(5)
                files_after = set(os.listdir(downloads_dir))
                new_files = files_after - files_before
                
                if new_files:
                    print(f"✅ New files downloaded: {new_files}")
                else:
                    print("❌ No new files found")
                    
            except Exception as e:
                print(f"❌ Click failed: {e}")
        
        input("Press Enter to close browser...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_snapeda_download()
