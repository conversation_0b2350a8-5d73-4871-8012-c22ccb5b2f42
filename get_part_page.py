#!/usr/bin/env python3
"""
Access the specific part page to look for 3D models
"""

import requests
from bs4 import <PERSON>Soup

def get_part_page():
    print("📄 ACCESSING PART PAGE FOR 3D MODELS")
    print("=" * 40)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Access the APX803L part page
    part_url = "https://www.diodes.com/part/view/APX803L"
    
    try:
        print(f"Accessing: {part_url}")
        response = session.get(part_url, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the part page
            with open('diodes_part_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("📄 Saved part page")
            
            # Look for 3D model links
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Search for 3D/CAD/model related content
            model_keywords = ['3d', 'cad', 'model', 'step', 'solidworks', 'altium']
            found_models = []
            
            # Check all links
            for link in soup.find_all('a', href=True):
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                
                if any(keyword in href or keyword in text for keyword in model_keywords):
                    found_models.append({
                        'url': link.get('href'),
                        'text': link.get_text(strip=True),
                        'title': link.get('title', '')
                    })
            
            if found_models:
                print(f"\n🎯 FOUND {len(found_models)} 3D/CAD MODEL LINKS:")
                for i, model in enumerate(found_models, 1):
                    print(f"   {i}. {model['text']}")
                    print(f"      URL: {model['url']}")
                    if model['title']:
                        print(f"      Title: {model['title']}")
                    print()
            else:
                print("\n❌ No 3D/CAD model links found")
            
            # Also look for download sections
            download_sections = soup.find_all(['div', 'section'], class_=lambda x: x and ('download' in x.lower() or 'resource' in x.lower()))
            
            if download_sections:
                print(f"📥 FOUND {len(download_sections)} DOWNLOAD SECTIONS:")
                for i, section in enumerate(download_sections, 1):
                    print(f"   Section {i}: {section.get('class')}")
            
            # Look for specific part number on the page
            if "APX803L20-30SA-7" in response.text:
                print(f"\n✅ CONFIRMED: Found APX803L20-30SA-7 on part page")
            elif "APX803L20" in response.text:
                print(f"\n✅ PARTIAL MATCH: Found APX803L20 on part page")
            else:
                print(f"\n⚠️  Specific part number not found, but this is the family page")
            
            return True
            
        else:
            print(f"❌ Failed to access part page: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🚀 PART PAGE ANALYSIS")
    print("Looking for 3D models on manufacturer website")
    print("=" * 50)
    
    success = get_part_page()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ SUCCESS: Accessed part page")
        print("📄 Check 'diodes_part_page.html' for full content")
        print("📄 Next: Analyze any 3D model links found")
    else:
        print("❌ FAILED: Could not access part page")

if __name__ == "__main__":
    main()
