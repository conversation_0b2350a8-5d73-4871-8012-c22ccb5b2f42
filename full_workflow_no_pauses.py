#!/usr/bin/env python3
"""
FULL WORKFLOW NO PAUSES
=======================
Complete workflow without pauses - just show what happens at each step.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def full_workflow():
    print("🎯 FULL ULTRALIBRARIAN WORKFLOW - NO PAUSES")
    print("=" * 60)
    
    # Setup Chrome with downloads
    chrome_options = Options()
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    initial_files = set(os.listdir('3D'))
    
    try:
        # STEP 1: Load UltraLibrarian
        print("\n📺 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print(f"✅ Loaded: {driver.title}")
        print(f"URL: {driver.current_url}")
        
        # STEP 2: Search for LM358N
        print("\n📺 STEP 2: Searching for LM358N...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_box = None
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                search_box = inp
                break
        
        if not search_box:
            print("❌ No search box found!")
            return
        
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        print("✅ Entered LM358N and submitted search")
        time.sleep(10)
        print(f"Search results URL: {driver.current_url}")
        
        # STEP 3: Click Texas Instruments LM358N
        print("\n📺 STEP 3: Finding and clicking Texas Instruments LM358N...")
        links = driver.find_elements(By.TAG_NAME, "a")
        ti_link = None
        
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed()):
                    ti_link = link
                    print(f"✅ Found TI LM358N: '{text}'")
                    break
            except:
                continue
        
        if not ti_link:
            print("❌ No Texas Instruments LM358N found!")
            return
        
        ti_link.click()
        print("✅ Clicked Texas Instruments LM358N")
        time.sleep(8)
        print(f"Part details URL: {driver.current_url}")
        
        # STEP 4: Click Download Now
        print("\n📺 STEP 4: Looking for and clicking Download Now...")
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        
        if not download_btns:
            print("❌ No Download Now button found!")
            # Show available buttons
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons:")
            for btn in buttons[:10]:
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"  - '{text}'")
                except:
                    continue
            return
        
        print(f"✅ Found Download Now: '{download_btns[0].text}'")
        driver.execute_script("arguments[0].click();", download_btns[0])
        print("✅ Clicked Download Now")
        time.sleep(5)
        
        # STEP 5: Look for 3D CAD Model option
        print("\n📺 STEP 5: Looking for 3D CAD Model option...")
        
        # Check for new window
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        # Look for 3D CAD options
        cad_selectors = [
            "//button[contains(text(), '3D CAD Model')]",
            "//a[contains(text(), '3D CAD Model')]",
            "//button[contains(text(), '3D Model')]",
            "//a[contains(text(), '3D Model')]",
            "//button[contains(text(), 'CAD Model')]"
        ]
        
        cad_element = None
        for selector in cad_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    cad_element = elem
                    print(f"✅ Found 3D CAD option: '{elem.text}'")
                    break
            if cad_element:
                break
        
        if not cad_element:
            print("❌ No 3D CAD Model option found!")
            # Show what's available
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons after Download Now:")
            for btn in buttons[:15]:
                try:
                    text = btn.text.strip()
                    visible = btn.is_displayed()
                    if text:
                        print(f"  - '{text}' (visible={visible})")
                except:
                    continue
            
            links = driver.find_elements(By.TAG_NAME, "a")
            print("Available links after Download Now:")
            for link in links[:15]:
                try:
                    text = link.text.strip()
                    visible = link.is_displayed()
                    if text and len(text) < 50:
                        print(f"  - '{text}' (visible={visible})")
                except:
                    continue
            
            print("\n🔍 ANALYSIS: What should happen after Download Now?")
            print("Browser will stay open for 30 seconds for inspection...")
            time.sleep(30)
            return
        
        # Click 3D CAD Model
        driver.execute_script("arguments[0].click();", cad_element)
        print("✅ Clicked 3D CAD Model")
        time.sleep(5)
        
        # STEP 6: Look for STEP format
        print("\n📺 STEP 6: Looking for STEP format option...")
        
        step_selectors = [
            "//button[contains(text(), 'STEP')]",
            "//a[contains(text(), 'STEP')]",
            "//input[@value='STEP']",
            "//div[contains(text(), 'STEP') and @onclick]"
        ]
        
        step_element = None
        for selector in step_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    step_element = elem
                    print(f"✅ Found STEP option: '{elem.text or elem.get_attribute('value')}'")
                    break
            if step_element:
                break
        
        if not step_element:
            print("❌ No STEP format option found!")
            return
        
        driver.execute_script("arguments[0].click();", step_element)
        print("✅ Selected STEP format")
        time.sleep(3)
        
        # STEP 7: Final download
        print("\n📺 STEP 7: Looking for final download button...")
        
        final_download_selectors = [
            "//button[contains(text(), 'Download')]",
            "//a[contains(text(), 'Download')]",
            "//input[@type='submit']"
        ]
        
        download_element = None
        for selector in final_download_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    download_element = elem
                    print(f"✅ Found final download: '{elem.text or elem.get_attribute('value')}'")
                    break
            if download_element:
                break
        
        if not download_element:
            print("❌ No final download button found!")
            return
        
        driver.execute_script("arguments[0].click();", download_element)
        print("✅ Clicked final download")
        time.sleep(8)
        
        # STEP 8: Monitor for downloads
        print("\n📺 STEP 8: Monitoring for file downloads...")
        
        for i in range(24):  # 2 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 NEW FILES: {list(new_files)}")
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    print(f"✅ SUCCESS: STEP file downloaded: {step_files[0]}")
                    return step_files[0]
            
            print(f"  Checking... ({(i+1)*5}/120 seconds)")
        
        print("⏳ No files downloaded after 2 minutes")
        print("Browser will stay open for inspection...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        driver.quit()
        print("✅ Browser closed")

if __name__ == "__main__":
    result = full_workflow()
    if result:
        print(f"\n🎉 SUCCESS: {result}")
    else:
        print(f"\n⚠️ Workflow completed but no STEP file obtained")
