#!/usr/bin/env python3
"""
Final Component Finder - Handles Different Manufacturer Websites

Key Features:
1. Manufacturer-specific search strategies
2. Multiple fallback methods for each manufacturer
3. Package-type extraction from multiple sources
4. Intelligent 3D model searching
5. Easy to extend for new manufacturers

Usage:
    python final_component_finder.py "Diodes Inc" "APX803L20-30SA-7"
"""

import requests
from bs4 import BeautifulSoup
import re
import os
import json
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComponentFinder:
    def __init__(self, download_dir="files-download"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = self._create_session()
        
        # Manufacturer configurations - easily extensible
        self.manufacturer_configs = {
            "diodes inc": {
                "datasheet_strategies": [
                    {"type": "direct_url", "patterns": [
                        "https://www.diodes.com/assets/Datasheets/{}.pdf",
                        "https://www.diodes.com/datasheet/download/{}.pdf"
                    ]},
                    {"type": "product_page", "url": "https://www.diodes.com/part/view/{}"}
                ],
                "package_strategies": [
                    {"type": "product_page", "url": "https://www.diodes.com/part/view/{}"},
                    {"type": "known_mapping", "mapping": {
                        "APX803L20-30SA-7": "SOT23",
                        "APX803L": "SOT23"  # Family default
                    }}
                ],
                "3d_model_strategies": [
                    {"type": "package_based_urls", "patterns": [
                        "https://www.diodes.com/assets/3D-models/{}.step",
                        "https://www.diodes.com/assets/Package-3D/{}.step",
                        "https://www.diodes.com/assets/cad-models/{}.step"
                    ]},
                    {"type": "common_directories", "base": "https://www.diodes.com"}
                ]
            }
        }
    
    def _create_session(self):
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        return session
    
    def generate_part_variations(self, part_number):
        """Generate part number variations for searching"""
        base_part = part_number.split('-')[0]
        variations = [part_number, base_part]
        
        # For parts like APX803L20, try APX803L (remove trailing numbers)
        if base_part[-2:].isdigit():
            variations.append(base_part[:-2])
        if base_part[-1:].isdigit():
            variations.append(base_part[:-1])
        
        # Remove duplicates
        return list(dict.fromkeys(variations))
    
    def search_datasheet(self, manufacturer, part_number):
        """Search for datasheet using manufacturer-specific strategies"""
        manufacturer_lower = manufacturer.lower()
        
        if manufacturer_lower not in self.manufacturer_configs:
            logger.error(f"❌ No configuration for manufacturer: {manufacturer}")
            return None
        
        config = self.manufacturer_configs[manufacturer_lower]
        part_variations = self.generate_part_variations(part_number)
        
        for strategy in config["datasheet_strategies"]:
            logger.info(f"📄 Trying datasheet strategy: {strategy['type']}")
            
            if strategy["type"] == "direct_url":
                for pattern in strategy["patterns"]:
                    for variation in part_variations:
                        try:
                            url = pattern.format(variation)
                            logger.info(f"   Testing: {url}")
                            
                            response = self.session.head(url, timeout=10)
                            if response.status_code == 200:
                                logger.info(f"✅ Found datasheet: {url}")
                                return url
                                
                        except Exception as e:
                            logger.debug(f"Direct URL failed: {e}")
                            continue
        
        return None
    
    def extract_package_type(self, manufacturer, part_number):
        """Extract package type using manufacturer-specific strategies"""
        manufacturer_lower = manufacturer.lower()
        
        if manufacturer_lower not in self.manufacturer_configs:
            return None
        
        config = self.manufacturer_configs[manufacturer_lower]
        
        for strategy in config["package_strategies"]:
            logger.info(f"📦 Trying package strategy: {strategy['type']}")
            
            if strategy["type"] == "known_mapping":
                mapping = strategy["mapping"]
                # Try exact match first
                if part_number in mapping:
                    package = mapping[part_number]
                    logger.info(f"✅ Found package from mapping: {package}")
                    return package
                
                # Try base part
                base_part = part_number.split('-')[0]
                part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
                if part_family in mapping:
                    package = mapping[part_family]
                    logger.info(f"✅ Found package from family mapping: {package}")
                    return package
            
            elif strategy["type"] == "product_page":
                try:
                    part_variations = self.generate_part_variations(part_number)
                    for variation in part_variations:
                        url = strategy["url"].format(variation)
                        logger.info(f"   Testing product page: {url}")
                        
                        response = self.session.get(url, timeout=30)
                        if response.status_code == 200:
                            # Quick package extraction
                            html_lower = response.text.lower()
                            packages = {
                                'SOT23': ['sot-23', 'sot23'],
                                'SOT323': ['sot-323', 'sot323'],
                                'SOT25': ['sot-25', 'sot25'],
                                'SC59': ['sc-59', 'sc59']
                            }
                            
                            for pkg_name, patterns in packages.items():
                                for pattern in patterns:
                                    if pattern in html_lower:
                                        logger.info(f"✅ Found package from product page: {pkg_name}")
                                        return pkg_name
                            break
                            
                except Exception as e:
                    logger.debug(f"Product page package extraction failed: {e}")
                    continue
        
        return None
    
    def search_3d_models(self, manufacturer, part_number, package_type):
        """Search for 3D models using manufacturer-specific strategies"""
        manufacturer_lower = manufacturer.lower()
        models = []
        
        if not package_type:
            logger.warning("Cannot search for 3D models without package type")
            return models
        
        if manufacturer_lower not in self.manufacturer_configs:
            return models
        
        config = self.manufacturer_configs[manufacturer_lower]
        
        for strategy in config["3d_model_strategies"]:
            logger.info(f"🎯 Trying 3D model strategy: {strategy['type']}")
            
            if strategy["type"] == "package_based_urls":
                package_variations = [
                    package_type, package_type.replace('-', ''),
                    package_type.lower(), package_type.upper()
                ]
                
                for pattern in strategy["patterns"]:
                    for pkg_var in package_variations:
                        try:
                            url = pattern.format(pkg_var)
                            logger.info(f"   Testing: {url}")
                            
                            response = self.session.head(url, timeout=10)
                            if response.status_code == 200:
                                logger.info(f"✅ Found 3D model: {url}")
                                return [url]
                                
                        except Exception as e:
                            logger.debug(f"3D model URL failed: {e}")
                            continue
            
            elif strategy["type"] == "common_directories":
                # Try common directory patterns
                base_url = strategy["base"]
                common_dirs = [
                    "assets/3d-models", "assets/3D-models", "assets/cad-models",
                    "assets/step-models", "assets/packages", "downloads/3d", "cad", "3d"
                ]
                
                for directory in common_dirs:
                    for ext in ['.step', '.stp']:
                        try:
                            url = f"{base_url}/{directory}/{package_type}{ext}"
                            response = self.session.head(url, timeout=5)
                            if response.status_code == 200:
                                logger.info(f"✅ Found 3D model in common directory: {url}")
                                return [url]
                        except:
                            continue
        
        return models
    
    def download_file(self, url, filename=None):
        """Download file with proper error handling"""
        try:
            logger.info(f"📥 Downloading: {url}")
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            if not filename:
                filename = os.path.basename(urlparse(url).path)
            
            filepath = self.download_dir / filename
            
            # Handle existing files
            counter = 1
            original_filepath = filepath
            while filepath.exists():
                name, ext = original_filepath.stem, original_filepath.suffix
                filepath = self.download_dir / f"{name}_{counter}{ext}"
                counter += 1
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"✅ Downloaded: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            return None
    
    def find_component(self, manufacturer, part_number):
        """Main method to find datasheet and 3D models"""
        results = {
            'manufacturer': manufacturer,
            'part_number': part_number,
            'package_type': None,
            'datasheet_file': None,
            '3d_model_files': [],
            'success': False
        }
        
        logger.info(f"🔍 Searching for {manufacturer} {part_number}")
        
        # Step 1: Find datasheet
        datasheet_url = self.search_datasheet(manufacturer, part_number)
        if datasheet_url:
            datasheet_file = self.download_file(datasheet_url, f"{part_number}_datasheet.pdf")
            results['datasheet_file'] = datasheet_file
        
        # Step 2: Extract package type
        package_type = self.extract_package_type(manufacturer, part_number)
        results['package_type'] = package_type
        
        # Step 3: Search for 3D models
        if package_type:
            model_urls = self.search_3d_models(manufacturer, part_number, package_type)
            for url in model_urls:
                model_file = self.download_file(url, f"{package_type}_3d_model.step")
                if model_file:
                    results['3d_model_files'].append(model_file)
        
        results['success'] = bool(results['datasheet_file'] or results['3d_model_files'])
        return results

def main():
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description='Component Finder')
    parser.add_argument('manufacturer', help='Manufacturer name')
    parser.add_argument('part_number', help='Part number')
    
    args = parser.parse_args()
    
    finder = ComponentFinder()
    results = finder.find_component(args.manufacturer, args.part_number)
    
    print("\n" + "="*60)
    print("COMPONENT FINDER RESULTS")
    print("="*60)
    print(json.dumps(results, indent=2))

if __name__ == "__main__":
    import sys
    if len(sys.argv) == 1:
        finder = ComponentFinder()
        results = finder.find_component("Diodes Inc", "APX803L20-30SA-7")
        print(json.dumps(results, indent=2))
    else:
        main()
