#!/usr/bin/env python3
"""
Test if LOGIN link is found and clickable
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def test_login_link():
    print("🔸 Testing LOGIN link on UltraLibrarian first page")
    print("=" * 60)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    try:
        # Load UltraLibrarian
        driver.get("https://app.ultralibrarian.com")
        time.sleep(15)
        
        print(f"✅ Page loaded: {driver.title}")
        print(f"✅ Current URL: {driver.current_url}")
        
        # Test multiple LOGIN link selectors
        print(f"\n🔍 Testing LOGIN link selectors...")

        selectors = [
            "//a[text()='LOGIN']",
            "//a[contains(text(), 'LOGIN')]",
            "//a[contains(text(), 'Login')]",
            "//a[contains(text(), 'login')]",
            "//a[contains(@href, 'Login')]"
        ]

        login_links = []
        for i, selector in enumerate(selectors):
            try:
                links = driver.find_elements(By.XPATH, selector)
                print(f"   Selector {i+1}: '{selector}' found {len(links)} links")
                if links:
                    login_links = links
                    print(f"   ✅ Using selector {i+1}")
                    break
            except Exception as e:
                print(f"   ❌ Selector {i+1} failed: {e}")

        print(f"✅ Final result: Found {len(login_links)} LOGIN links")
        
        if login_links:
            login_link = login_links[0]
            text = login_link.text.strip()
            href = login_link.get_attribute('href')
            visible = login_link.is_displayed()
            clickable = login_link.is_enabled()
            
            print(f"✅ LOGIN link details:")
            print(f"   Text: '{text}'")
            print(f"   Href: {href}")
            print(f"   Visible: {visible}")
            print(f"   Clickable: {clickable}")
            
            if visible and clickable:
                print(f"\n✅ LOGIN link is ready to click!")
                print(f"🔍 Clicking LOGIN link...")
                
                initial_url = driver.current_url
                login_link.click()
                time.sleep(8)
                new_url = driver.current_url
                
                print(f"✅ URL changed from {initial_url} to {new_url}")
                
                # Check if we're on login page
                if 'login' in new_url.lower():
                    print(f"✅ Successfully navigated to login page!")
                    
                    # Look for login form
                    email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email'], input[placeholder*='email']")
                    password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
                    
                    print(f"✅ Found {len(email_inputs)} email inputs and {len(password_inputs)} password inputs")
                    
                    if email_inputs and password_inputs:
                        print(f"✅ LOGIN FORM IS READY!")
                    else:
                        print(f"❌ Login form not found")
                else:
                    print(f"❌ Did not navigate to login page")
            else:
                print(f"❌ LOGIN link is not clickable")
        else:
            print(f"❌ No LOGIN links found")
        
        # Keep browser open for inspection
        print(f"\n🔍 Browser will stay open for 30 seconds...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        driver.quit()

if __name__ == "__main__":
    test_login_link()
