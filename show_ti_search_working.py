#!/usr/bin/env python3
"""
Show TI search working - just the search part
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def show_ti_search():
    print("SHOWING TI SEARCH WORKING")
    print("=" * 40)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Step 1: Go to TI.com
        print("1. Going to TI.com...")
        driver.get("https://www.ti.com")
        time.sleep(10)
        print(f"   Loaded: {driver.title}")
        
        # Step 2: Use working search method
        part_number = "LM358N"
        search_url = f"https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm={part_number}"
        
        print(f"2. Searching for {part_number}...")
        print(f"   Using: {search_url}")
        
        driver.get(search_url)
        time.sleep(10)
        
        # Step 3: Show results
        print(f"3. Results:")
        print(f"   Current URL: {driver.current_url}")
        
        if part_number.upper() in driver.page_source.upper():
            print(f"   ✅ SUCCESS: Found {part_number} in results!")
        else:
            print(f"   ❌ FAILED: No {part_number} found")
        
        print("\n4. Search is working! Press Enter to close...")
        input()
        
    finally:
        driver.quit()

if __name__ == "__main__":
    show_ti_search()
