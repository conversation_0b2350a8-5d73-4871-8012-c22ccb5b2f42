#!/usr/bin/env python3
"""
FINAL WORKING AUTOMATION
========================
Fixed version with JavaScript click for intercepted elements.
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome with download preferences"""
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def final_automation():
    """Final working automation with proper click handling"""
    print("🎯 FINAL WORKING ULTRALIBRARIAN AUTOMATION")
    print("=" * 60)
    
    driver = setup_driver()
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # STEP 1: Load page
        print("\n🔸 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print(f"✅ Page loaded: {driver.title}")
        
        # STEP 2: Search
        print("\n🔸 STEP 2: Searching for LM358N...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                print("✅ Search submitted")
                break
        time.sleep(10)
        
        # STEP 3: Click TI LM358N
        print("\n🔸 STEP 3: Clicking Texas Instruments LM358N...")
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed()):
                    link.click()
                    print(f"✅ Clicked: {text}")
                    break
            except:
                continue
        time.sleep(8)
        
        # STEP 4: Click first Download Now (using JavaScript to avoid interception)
        print("\n🔸 STEP 4: Clicking first 'Download Now' (JavaScript click)...")
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            # Use JavaScript click to avoid element interception
            driver.execute_script("arguments[0].click();", download_btns[0])
            print("✅ Clicked first 'Download Now' using JavaScript")
            time.sleep(5)
        else:
            print("❌ No 'Download Now' button found!")
            return None
        
        # STEP 5: Click 3D CAD Model (using JavaScript to avoid interception)
        print("\n🔸 STEP 5: Clicking '3D CAD Model' (JavaScript click)...")
        model_btns = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
        if model_btns:
            # Use JavaScript click to avoid element interception
            driver.execute_script("arguments[0].click();", model_btns[0])
            print("✅ Clicked '3D CAD Model' using JavaScript")
            time.sleep(8)
        else:
            print("❌ No '3D CAD Model' button found!")
            return None
        
        print(f"Current URL: {driver.current_url}")
        
        # STEP 6: Handle new window/screen
        print("\n🔸 STEP 6: Checking for new window...")
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
            print(f"New window URL: {driver.current_url}")
        
        # STEP 7: Look for STEP format selection
        print("\n🔸 STEP 7: Looking for STEP format selection...")
        
        # Look for STEP elements
        step_elements = []
        buttons = driver.find_elements(By.TAG_NAME, "button")
        for btn in buttons:
            try:
                if 'step' in btn.text.lower() and btn.is_displayed():
                    step_elements.append(btn)
                    print(f"  Found STEP button: '{btn.text}'")
            except:
                continue
        
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                if 'step' in link.text.lower() and link.is_displayed():
                    step_elements.append(link)
                    print(f"  Found STEP link: '{link.text}'")
            except:
                continue
        
        if step_elements:
            print(f"✅ Selecting STEP format...")
            # Use JavaScript click for STEP selection too
            driver.execute_script("arguments[0].click();", step_elements[0])
            time.sleep(5)
            print("✅ Selected STEP format")
        else:
            print("⚠️ No STEP format selection found")
        
        # STEP 8: Look for second Download Now
        print("\n🔸 STEP 8: Looking for second 'Download Now'...")
        
        download_btns_2 = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')] | //a[contains(text(), 'Download Now')]")
        
        if download_btns_2:
            for btn in download_btns_2:
                try:
                    if btn.is_displayed() and btn.is_enabled():
                        print(f"✅ Found second Download Now: '{btn.text}'")
                        # Use JavaScript click
                        driver.execute_script("arguments[0].click();", btn)
                        print("✅ Clicked second 'Download Now' using JavaScript")
                        time.sleep(8)
                        break
                except:
                    continue
        else:
            print("⚠️ No second Download Now found")
        
        # STEP 9: Handle login if needed
        print("\n🔸 STEP 9: Checking for login...")
        
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email']")
        if email_inputs:
            print("🔐 Login form detected, attempting automatic login...")
            
            try:
                # Load credentials
                with open('component_site_credentials.json', 'r') as f:
                    credentials = json.load(f)
                
                email = credentials['UltraLibrarian']['email']
                password = credentials['UltraLibrarian']['password']
                
                # Enter email
                email_input = None
                for inp in email_inputs:
                    if inp.is_displayed() and inp.is_enabled():
                        email_input = inp
                        break
                
                if email_input:
                    email_input.clear()
                    email_input.send_keys(email)
                    print("✅ Entered email")
                    
                    # Enter password
                    password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
                    if password_inputs:
                        password_inputs[0].clear()
                        password_inputs[0].send_keys(password)
                        print("✅ Entered password")
                        
                        # Click login
                        login_btns = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
                        if login_btns:
                            login_btns[0].click()
                            print("✅ Clicked login")
                            time.sleep(10)
                        
            except Exception as e:
                print(f"⚠️ Login attempt failed: {e}")
        else:
            print("ℹ️ No login form detected")
        
        # STEP 10: Monitor for downloads
        print("\n🔸 STEP 10: Monitoring for downloads...")
        
        for i in range(24):  # Monitor for 2 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 NEW FILES DETECTED: {list(new_files)}")
                
                # Check for STEP files
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    print(f"✅ STEP FILES FOUND: {step_files}")
                    return step_files[0]
                
                # Check for ZIP files
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP FILE FOUND: {zip_files[0]}")
                    try:
                        import zipfile
                        zip_path = os.path.join('3D', zip_files[0])
                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        
                        # Check for extracted STEP files
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            print(f"✅ STEP FILE EXTRACTED: {step_files[0]}")
                            return step_files[0]
                    except Exception as e:
                        print(f"Error extracting ZIP: {e}")
            
            print(f"  Checking... ({(i+1)*5}/120 seconds)")
        
        print("⏳ No files downloaded after 2 minutes")
        
        print("\n🔍 FINAL STATE ANALYSIS")
        print("Browser will stay open for manual inspection...")
        input("Press Enter to close...")
        
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        driver.quit()

if __name__ == "__main__":
    result = final_automation()
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded to 3D/ folder!")
    else:
        print(f"\n⚠️ No STEP file obtained - manual completion may be needed")
