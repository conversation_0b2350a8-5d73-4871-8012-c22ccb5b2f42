#!/usr/bin/env python3
"""
Fixed UltraLibrarian login - properly extracting all form fields
"""

import requests
from bs4 import BeautifulSoup
import os
import time
from datetime import datetime

def log_message(message):
    """Write message to log file"""
    print(message)
    with open('ultralibrarian_fixed_log.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {message}\n")

def ultralibrarian_login_and_search():
    log_message("🔍 ULTRALIBRARIAN FIXED LOGIN AND SEARCH")
    log_message("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    # Step 1: Get fresh login page
    log_message("\n1. Getting fresh login page...")
    login_url = 'https://www.ultralibrarian.com/wp-login.php'
    
    try:
        login_page = session.get(login_url, timeout=30)
        log_message(f"   Status: {login_page.status_code}")
        
        if login_page.status_code == 503:
            log_message(f"   ❌ Still blocked by Wordfence")
            return False
        elif login_page.status_code != 200:
            log_message(f"   ❌ Cannot access login page: {login_page.status_code}")
            return False
        
        log_message(f"   ✅ Got login page ({len(login_page.text)} chars)")
        
        # Save login page for debugging
        with open('ultralibrarian_fresh_login.html', 'w', encoding='utf-8') as f:
            f.write(login_page.text)
        log_message(f"   📄 Saved fresh login page")
        
    except Exception as e:
        log_message(f"   ❌ Error getting login page: {e}")
        return False
    
    # Step 2: Parse form and extract ALL fields
    log_message(f"\n2. Parsing login form...")
    
    try:
        soup = BeautifulSoup(login_page.text, 'html.parser')
        form = soup.find('form', {'id': 'loginform'})
        
        if not form:
            log_message(f"   ❌ Cannot find login form")
            return False
        
        log_message(f"   ✅ Found login form")
        
        # Extract ALL input fields
        form_data = {}
        inputs = form.find_all('input')
        
        log_message(f"   Found {len(inputs)} input fields:")
        for inp in inputs:
            name = inp.get('name')
            value = inp.get('value', '')
            input_type = inp.get('type', 'text')
            
            if name:
                form_data[name] = value
                log_message(f"     {name}: '{value}' (type: {input_type})")
        
        # Set our credentials (overriding any existing values)
        form_data['log'] = '<EMAIL>'
        form_data['pwd'] = 'Lennyai123#'
        
        log_message(f"   ✅ Set credentials")
        log_message(f"   Final form data: {list(form_data.keys())}")
        
    except Exception as e:
        log_message(f"   ❌ Error parsing form: {e}")
        return False
    
    # Step 3: Submit login with proper headers
    log_message(f"\n3. Submitting login...")
    
    try:
        # Update headers for form submission
        session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://www.ultralibrarian.com',
            'Referer': login_url,
            'Cache-Control': 'max-age=0',
        })
        
        # Submit the form
        login_response = session.post(
            login_url, 
            data=form_data, 
            timeout=30, 
            allow_redirects=True
        )
        
        log_message(f"   Status: {login_response.status_code}")
        log_message(f"   Final URL: {login_response.url}")
        log_message(f"   Response length: {len(login_response.text)} chars")
        
        # Save login response
        with open('ultralibrarian_fixed_login_response.html', 'w', encoding='utf-8') as f:
            f.write(login_response.text)
        log_message(f"   📄 Saved login response")
        
    except Exception as e:
        log_message(f"   ❌ Error submitting login: {e}")
        return False
    
    # Step 4: Check login result
    log_message(f"\n4. Checking login result...")
    
    # Check for error messages
    if 'login_error' in login_response.text:
        log_message(f"   ❌ Login failed - found error message")
        soup = BeautifulSoup(login_response.text, 'html.parser')
        error_div = soup.find('div', {'id': 'login_error'})
        if error_div:
            error_text = error_div.get_text(strip=True)
            log_message(f"   Error: {error_text}")
        return False
    
    # Check for success indicators
    success_indicators = [
        ('wp-admin' in login_response.url, 'Redirected to wp-admin'),
        ('dashboard' in login_response.url, 'Redirected to dashboard'),
        ('logout' in login_response.text.lower(), 'Logout link found'),
        ('welcome' in login_response.text.lower(), 'Welcome message found'),
    ]
    
    success_count = 0
    for indicator, description in success_indicators:
        if indicator:
            log_message(f"   ✅ {description}")
            success_count += 1
        else:
            log_message(f"   ❌ {description}")
    
    if success_count == 0:
        log_message(f"   ❌ No success indicators found")
        return False
    
    log_message(f"   🎉 Login successful! ({success_count}/{len(success_indicators)} indicators)")
    
    # Step 5: Search for APX803L20-30SA-7
    log_message(f"\n5. Searching for APX803L20-30SA-7...")
    
    try:
        search_url = "https://www.ultralibrarian.com/search"
        search_params = {'q': 'APX803L20-30SA-7'}
        
        search_response = session.get(search_url, params=search_params, timeout=30)
        log_message(f"   Search status: {search_response.status_code}")
        
        if search_response.status_code == 200:
            # Save search results
            with open('ultralibrarian_search_results.html', 'w', encoding='utf-8') as f:
                f.write(search_response.text)
            log_message(f"   📄 Saved search results")
            
            # Check if part is found
            if 'APX803L20-30SA-7'.lower() in search_response.text.lower():
                log_message(f"   ✅ Found APX803L20-30SA-7 in search results!")
                
                # Look for download links
                soup = BeautifulSoup(search_response.text, 'html.parser')
                download_links = []
                
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    text = link.get_text(strip=True).lower()
                    
                    if any(keyword in text for keyword in ['download', 'step', '3d', 'model']):
                        download_links.append({
                            'url': href,
                            'text': text
                        })
                
                log_message(f"   Found {len(download_links)} potential download links:")
                for i, link in enumerate(download_links[:5], 1):
                    log_message(f"   {i}. {link['text']}")
                    log_message(f"      URL: {link['url']}")
                
                # Try to download STEP files
                for link in download_links:
                    if '.step' in link['url'].lower() or '.stp' in link['url'].lower():
                        success = try_download_step_file(session, link)
                        if success:
                            return True
                
                return len(download_links) > 0
            else:
                log_message(f"   ❌ APX803L20-30SA-7 not found in search results")
                return False
        else:
            log_message(f"   ❌ Search failed: {search_response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Search error: {e}")
        return False

def try_download_step_file(session, link):
    """Try to download a STEP file"""
    log_message(f"   🔽 Attempting to download STEP file...")
    
    try:
        # Make URL absolute if needed
        url = link['url']
        if not url.startswith('http'):
            url = f"https://www.ultralibrarian.com{url}"
        
        log_message(f"   Download URL: {url}")
        
        download_response = session.get(url, timeout=60, stream=True)
        log_message(f"   Download status: {download_response.status_code}")
        
        if download_response.status_code == 200:
            # Create 3d directory
            os.makedirs('3d', exist_ok=True)
            
            # Determine filename
            filename = "APX803L20-30SA-7_UltraLibrarian.step"
            filepath = os.path.join('3d', filename)
            
            # Save file
            with open(filepath, 'wb') as f:
                for chunk in download_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            log_message(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                log_message(f"   🎉 SUCCESS: Downloaded actual STEP file!")
                return True
            else:
                log_message(f"   ⚠️  File too small, might be error page")
                return False
        else:
            log_message(f"   ❌ Download failed: {download_response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Download error: {e}")
        return False

def main():
    # Clear log
    with open('ultralibrarian_fixed_log.txt', 'w') as f:
        f.write(f"ULTRALIBRARIAN FIXED LOGIN LOG - {datetime.now()}\n")
        f.write("=" * 60 + "\n")
    
    log_message("🚀 ULTRALIBRARIAN FIXED LOGIN AND SEARCH")
    log_message("=" * 60)
    
    success = ultralibrarian_login_and_search()
    
    # Summary
    log_message("\n" + "=" * 60)
    if success:
        log_message("🎉 SUCCESS: UltraLibrarian login and search completed!")
    else:
        log_message("❌ FAILED: UltraLibrarian login or search failed")
    
    # Check for actual STEP files
    step_files = []
    if os.path.exists('3d'):
        for file in os.listdir('3d'):
            if file.endswith('.step') and not file.startswith('KiCad_'):
                step_files.append(file)
    
    if step_files:
        log_message(f"\n🎯 ACTUAL STEP FILES DOWNLOADED:")
        for file in step_files:
            filepath = os.path.join('3d', file)
            size = os.path.getsize(filepath)
            log_message(f"   ✅ {file} ({size:,} bytes)")
        log_message(f"\n🎉 SUCCESS: We now have real manufacturer STEP files!")
    else:
        log_message(f"\n❌ NO STEP FILES DOWNLOADED")
        log_message(f"   Check saved HTML files for manual inspection")
    
    log_message(f"\n📄 Full log saved to ultralibrarian_fixed_log.txt")

if __name__ == "__main__":
    main()
