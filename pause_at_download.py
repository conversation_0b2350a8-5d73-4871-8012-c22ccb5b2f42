#!/usr/bin/env python3
"""
PAUSE AT DOWNLOAD
=================
Get to the Download Now step and pause for inspection.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def pause_at_download():
    print("🎯 PAUSE AT DOWNLOAD TEST")
    print("=" * 40)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("🔸 Navigating to Download Now step...")
        
        # Load UltraLibrarian
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print("✅ Page loaded")
        
        # Search for LM358N
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                print("✅ Search submitted")
                break
        time.sleep(10)
        
        # Click TI LM358N
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed()):
                    link.click()
                    print(f"✅ Clicked: {text}")
                    break
            except:
                continue
        time.sleep(8)
        
        print(f"Current URL: {driver.current_url}")
        
        # Show what's on the page before clicking Download Now
        print("\n🔍 BEFORE CLICKING DOWNLOAD NOW:")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print("Available buttons:")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if text:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        # Find Download Now button
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            print(f"\n✅ Found Download Now button: '{download_btns[0].text}'")
            
            input("Press Enter to click 'Download Now'...")
            
            # Click Download Now
            driver.execute_script("arguments[0].click();", download_btns[0])
            print("✅ Clicked 'Download Now'")
            
            # Wait a moment for page to update
            time.sleep(5)
            
            print(f"\nNew URL: {driver.current_url}")
            
            # Show what's on the page after clicking Download Now
            print("\n🔍 AFTER CLICKING DOWNLOAD NOW:")
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons:")
            for i, btn in enumerate(buttons):
                try:
                    text = btn.text.strip()
                    visible = btn.is_displayed()
                    if text:
                        print(f"  {i}: '{text}' (visible={visible})")
                except:
                    continue
            
            links = driver.find_elements(By.TAG_NAME, "a")
            print("\nAvailable links:")
            for i, link in enumerate(links[:10]):
                try:
                    text = link.text.strip()
                    visible = link.is_displayed()
                    if text and len(text) < 50:
                        print(f"  {i}: '{text}' (visible={visible})")
                except:
                    continue
            
            print("\n🎯 WHAT DO YOU SEE?")
            print("Please look at the browser and tell me what options are available.")
            print("What should be the next step?")
            
            input("Press Enter to close browser...")
            
        else:
            print("❌ No 'Download Now' button found!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    pause_at_download()
