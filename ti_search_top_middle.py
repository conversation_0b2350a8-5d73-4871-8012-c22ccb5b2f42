#!/usr/bin/env python3
"""
Find search box in top middle of TI.com and use it
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time

def ti_search_top_middle():
    print("TI SEARCH - Top Middle")
    print("=" * 30)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Open TI.com
        print("Opening ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(15)  # Wait for page to load
        
        # Look for search box in top area - try different approaches
        print("Looking for search box in top middle...")
        
        # Method 1: Look for visible input elements
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(all_inputs)} input elements total")
        
        search_box = None
        for i, inp in enumerate(all_inputs):
            try:
                if inp.is_displayed():
                    # Get position to see if it's in top area
                    location = inp.location
                    size = inp.size
                    print(f"Input {i+1}: position y={location['y']}, displayed={inp.is_displayed()}")
                    
                    # If it's in the top part of the page (y < 200 pixels)
                    if location['y'] < 200:
                        search_box = inp
                        print(f"Found search box in top area!")
                        break
            except:
                continue
        
        # Method 2: If no input found, try clicking on search-looking elements
        if not search_box:
            print("No input found, trying to activate search...")
            search_elements = driver.find_elements(By.XPATH, "//*[contains(@class, 'search') or contains(text(), 'Search') or contains(text(), 'search')]")
            
            for elem in search_elements:
                try:
                    if elem.is_displayed() and elem.location['y'] < 200:
                        print(f"Clicking search element: {elem.text}")
                        elem.click()
                        time.sleep(3)
                        
                        # Look for input again after clicking
                        new_inputs = driver.find_elements(By.TAG_NAME, "input")
                        for inp in new_inputs:
                            if inp.is_displayed():
                                search_box = inp
                                print("Found input after clicking!")
                                break
                        if search_box:
                            break
                except:
                    continue
        
        # Use the search box
        if search_box:
            print("Entering LM358N...")
            search_box.clear()
            search_box.send_keys("LM358N")
            search_box.send_keys(Keys.RETURN)
            time.sleep(10)
            
            print(f"Search completed! URL: {driver.current_url}")
            
            if "LM358N" in driver.page_source.upper():
                print("✅ SUCCESS: Found LM358N!")
            else:
                print("❌ No LM358N found")
        else:
            print("❌ Could not find search box")
        
    finally:
        input("Press Enter to close...")
        driver.quit()

if __name__ == "__main__":
    ti_search_top_middle()
