#!/usr/bin/env python3
"""
Download SnapEDA page directly and analyze the search form
"""

import requests
from bs4 import BeautifulSoup

def download_and_analyze_snapeda():
    print("🔍 DOWNLOADING SNAPEDA PAGE TO ANALYZE SEARCH")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        print("1. Downloading SnapEDA homepage...")
        response = session.get('https://www.snapeda.com/', timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the raw HTML
            with open('snapeda_homepage_raw.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("   📄 Saved raw HTML")
            
            # Parse and analyze
            soup = BeautifulSoup(response.text, 'html.parser')
            
            print("\n2. Analyzing search form...")
            
            # Find all forms
            forms = soup.find_all('form')
            print(f"   Found {len(forms)} forms")
            
            for i, form in enumerate(forms, 1):
                print(f"\n   Form {i}:")
                print(f"   Action: {form.get('action', 'None')}")
                print(f"   Method: {form.get('method', 'GET')}")
                print(f"   Class: {form.get('class', 'None')}")
                print(f"   ID: {form.get('id', 'None')}")
                
                # Find inputs in this form
                inputs = form.find_all('input')
                print(f"   Inputs: {len(inputs)}")
                
                for inp in inputs:
                    name = inp.get('name', 'None')
                    input_type = inp.get('type', 'text')
                    placeholder = inp.get('placeholder', 'None')
                    value = inp.get('value', 'None')
                    print(f"     - {input_type}: name='{name}' placeholder='{placeholder}' value='{value}'")
            
            # Look for search-related elements
            print("\n3. Looking for search elements...")
            
            # Search inputs
            search_inputs = soup.find_all('input', {'type': ['search', 'text']})
            print(f"   Found {len(search_inputs)} search/text inputs:")
            
            for inp in search_inputs:
                name = inp.get('name', 'None')
                placeholder = inp.get('placeholder', 'None')
                class_attr = inp.get('class', [])
                print(f"     - name='{name}' placeholder='{placeholder}' class='{class_attr}'")
            
            # Search buttons
            search_buttons = soup.find_all(['button', 'input'], {'type': 'submit'})
            print(f"   Found {len(search_buttons)} submit buttons:")
            
            for btn in search_buttons:
                text = btn.get_text(strip=True) if btn.name == 'button' else btn.get('value', '')
                print(f"     - '{text}'")
            
            # Look for JavaScript that might handle search
            print("\n4. Looking for search-related JavaScript...")
            scripts = soup.find_all('script')
            search_js = []
            
            for script in scripts:
                if script.string:
                    script_text = script.string.lower()
                    if 'search' in script_text and ('ajax' in script_text or 'fetch' in script_text):
                        search_js.append(script.string[:200] + "...")
            
            if search_js:
                print(f"   Found {len(search_js)} search-related scripts:")
                for i, js in enumerate(search_js[:2], 1):
                    print(f"     Script {i}: {js}")
            
            # Look for API endpoints
            print("\n5. Looking for API endpoints...")
            api_patterns = ['api/', '/search', '/parts', 'ajax']
            
            for pattern in api_patterns:
                if pattern in response.text:
                    print(f"   ✅ Found '{pattern}' in page")
                    
                    # Extract URLs containing this pattern
                    import re
                    urls = re.findall(r'["\']([^"\']*' + re.escape(pattern) + r'[^"\']*)["\']', response.text)
                    if urls:
                        unique_urls = list(set(urls))[:3]  # First 3 unique URLs
                        for url in unique_urls:
                            print(f"     - {url}")
            
            return True
            
        else:
            print(f"   ❌ Failed to download: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_snapeda_search_with_analysis():
    """Test SnapEDA search using the analyzed form data"""
    print("\n🔍 TESTING SNAPEDA SEARCH WITH ANALYSIS")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    part_number = "APX803L20-30SA-7"
    
    # Try different search approaches based on common patterns
    search_attempts = [
        {
            'name': 'Direct search with q parameter',
            'url': 'https://www.snapeda.com/search',
            'params': {'q': part_number}
        },
        {
            'name': 'Search with query parameter',
            'url': 'https://www.snapeda.com/search',
            'params': {'query': part_number}
        },
        {
            'name': 'Parts API endpoint',
            'url': 'https://www.snapeda.com/api/search',
            'params': {'q': part_number}
        },
        {
            'name': 'Direct part URL',
            'url': f'https://www.snapeda.com/parts/{part_number}',
            'params': {}
        }
    ]
    
    for i, attempt in enumerate(search_attempts, 1):
        print(f"\n{i}. {attempt['name']}")
        print(f"   URL: {attempt['url']}")
        print(f"   Params: {attempt['params']}")
        
        try:
            if attempt['params']:
                response = session.get(attempt['url'], params=attempt['params'], timeout=30)
            else:
                response = session.get(attempt['url'], timeout=30)
            
            print(f"   Status: {response.status_code}")
            print(f"   Final URL: {response.url}")
            
            if response.status_code == 200:
                filename = f'snapeda_test_{i}.html'
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"   📄 Saved to {filename}")
                
                # Quick check for the part
                if part_number.lower() in response.text.lower():
                    print(f"   ✅ Found {part_number}!")
                    
                    # Look for download links
                    soup = BeautifulSoup(response.text, 'html.parser')
                    download_links = []
                    
                    for link in soup.find_all('a', href=True):
                        href = link.get('href', '').lower()
                        text = link.get_text(strip=True).lower()
                        
                        if any(keyword in href or keyword in text for keyword in ['download', 'step', '3d', 'model']):
                            download_links.append({
                                'url': link.get('href'),
                                'text': link.get_text(strip=True)
                            })
                    
                    if download_links:
                        print(f"   🎯 Found {len(download_links)} download links:")
                        for j, link in enumerate(download_links[:3], 1):
                            print(f"   {j}. {link['text']}")
                            print(f"      {link['url']}")
                        return True
                else:
                    print(f"   ❌ {part_number} not found")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return False

def main():
    print("🚀 SNAPEDA DIRECT ANALYSIS AND SEARCH")
    print("=" * 60)
    
    # Step 1: Download and analyze
    analysis_success = download_and_analyze_snapeda()
    
    if analysis_success:
        # Step 2: Test search based on analysis
        search_success = test_snapeda_search_with_analysis()
        
        print("\n" + "=" * 60)
        if search_success:
            print("✅ SUCCESS: Found part and download links on SnapEDA!")
        else:
            print("❌ SEARCH FAILED: Part not found despite analysis")
    else:
        print("❌ ANALYSIS FAILED: Could not analyze SnapEDA page")

if __name__ == "__main__":
    main()
