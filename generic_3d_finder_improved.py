#!/usr/bin/env python3
"""
IMPROVED GENERIC 3D MODEL FINDER
===============================
Enhanced version with proper error handling, verification, and detailed output.
Works with any manufacturer website by learning patterns.

Features:
- Detailed step-by-step output
- Error handling with immediate stop
- File verification (size, format, content)
- Success/failure reporting
- Proper logging
- Universal website pattern detection

Usage:
    python generic_3d_finder_improved.py "Texas Instruments" "LM358N"
"""

import os
import sys
import time
import shutil
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from urllib.parse import urljoin, urlparse
import argparse

class Generic3DFinder:
    def __init__(self):
        self.driver = None
        self.session = requests.Session()
        self.models_3d_dir = "3d"
        self.downloads_dir = os.path.expanduser("~/Downloads")
        
        # Common 3D model indicators
        self.model_keywords = [
            '3d', '3D', 'step', 'STEP', 'stp', 'STP', 'cad', 'CAD',
            'model', 'Model', 'mechanical', 'Mechanical', 'package', 'Package'
        ]
        
        # Common download indicators
        self.download_keywords = [
            'download', 'Download', 'get', 'Get', 'export', 'Export'
        ]
        
    def log(self, message, level="INFO"):
        """Enhanced logging with timestamps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if level == "ERROR":
            print(f"❌ [{timestamp}] ERROR: {message}")
        elif level == "SUCCESS":
            print(f"✅ [{timestamp}] SUCCESS: {message}")
        elif level == "WARNING":
            print(f"⚠️ [{timestamp}] WARNING: {message}")
        else:
            print(f"ℹ️ [{timestamp}] INFO: {message}")
    
    def setup_browser(self):
        """Setup Chrome browser with proper options"""
        self.log("Setting up Chrome browser...")
        
        try:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.log("Browser setup successful", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Browser setup failed: {e}", "ERROR")
            return False
    
    def setup_directories(self):
        """Create necessary directories"""
        self.log("Setting up directories...")
        
        try:
            os.makedirs(self.models_3d_dir, exist_ok=True)
            self.log(f"Directory '{self.models_3d_dir}' ready", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Error creating directory: {e}", "ERROR")
            return False
    
    def handle_cookie_popup(self):
        """Handle cookie consent popup"""
        self.log("Checking for cookie popup...")
        
        try:
            cookie_texts = ["Accept", "Accept All", "OK", "Got it", "I Agree", "Continue", "Allow"]
            
            for text in cookie_texts:
                try:
                    elements = self.driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')] | //a[contains(text(), '{text}')]")
                    for element in elements:
                        if element.is_displayed():
                            self.log(f"Clicking cookie button: '{text}'")
                            element.click()
                            time.sleep(2)
                            return True
                except:
                    continue
            
            self.log("No cookie popup found")
            return True
            
        except Exception as e:
            self.log(f"Error handling cookie popup: {e}", "WARNING")
            return True  # Continue even if cookie handling fails
    
    def determine_manufacturer_website(self, manufacturer):
        """Determine manufacturer website URL"""
        self.log(f"Determining website for manufacturer: {manufacturer}")
        
        # Common manufacturer website patterns
        manufacturer_sites = {
            'texas instruments': 'https://www.ti.com',
            'ti': 'https://www.ti.com',
            'analog devices': 'https://www.analog.com',
            'adi': 'https://www.analog.com',
            'diodes inc': 'https://www.diodes.com',
            'diodes incorporated': 'https://www.diodes.com',
            'infineon': 'https://www.infineon.com',
            'maxim': 'https://www.maximintegrated.com',
            'linear technology': 'https://www.linear.com',
            'microchip': 'https://www.microchip.com',
            'stmicroelectronics': 'https://www.st.com',
            'nxp': 'https://www.nxp.com'
        }
        
        manufacturer_lower = manufacturer.lower().strip()
        
        if manufacturer_lower in manufacturer_sites:
            website = manufacturer_sites[manufacturer_lower]
            self.log(f"Found known website: {website}", "SUCCESS")
            return website
        
        # Try to construct website URL
        manufacturer_clean = manufacturer_lower.replace(' ', '').replace('inc', '').replace('incorporated', '').strip()
        constructed_url = f"https://www.{manufacturer_clean}.com"
        
        self.log(f"Trying constructed URL: {constructed_url}")
        return constructed_url
    
    def navigate_to_manufacturer_site(self, manufacturer):
        """Navigate to manufacturer website"""
        website = self.determine_manufacturer_website(manufacturer)
        
        self.log(f"Navigating to {manufacturer} website...")
        
        try:
            self.driver.get(website)
            time.sleep(8)
            
            # Handle cookie popup
            self.handle_cookie_popup()
            
            # Check if site loaded successfully
            if "404" in self.driver.title.lower() or "not found" in self.driver.page_source.lower():
                self.log(f"Website not accessible: {website}", "ERROR")
                return False
            
            self.log(f"Successfully loaded {manufacturer} website", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error navigating to manufacturer site: {e}", "ERROR")
            return False
    
    def search_for_part(self, part_number):
        """Search for part on manufacturer website"""
        self.log(f"Searching for part: {part_number}")
        
        try:
            # Find search box using multiple strategies
            search_selectors = [
                "input[type='search']",
                "input[placeholder*='search' i]",
                "input[placeholder*='part' i]",
                "input[name*='search']",
                "input[name='q']",
                "#search",
                ".search-input",
                "[data-search]"
            ]
            
            search_box = None
            for selector in search_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            search_box = elem
                            break
                    if search_box:
                        break
                except:
                    continue
            
            if not search_box:
                self.log("Search box not found", "ERROR")
                return False
            
            self.log("Found search box")
            
            # Enter search term
            search_box.clear()
            search_box.send_keys(part_number)
            search_box.send_keys(Keys.RETURN)
            time.sleep(8)
            
            self.log(f"Search completed for: {part_number}")
            
            # Check if part was found
            if part_number.upper() not in self.driver.page_source.upper():
                self.log("Part not found in search results", "ERROR")
                return False
            
            self.log("Part found in search results", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error during search: {e}", "ERROR")
            return False
    
    def find_part_page(self, part_number):
        """Find and navigate to specific part page"""
        self.log("Looking for part page...")
        
        try:
            # Look for part links
            part_selectors = [
                f"//a[contains(text(), '{part_number}')]",
                f"//a[contains(@href, '{part_number}')]",
                "//a[contains(@class, 'part')]",
                "//a[contains(@class, 'product')]",
                "//td[contains(text(), '{part_number}')]/parent::tr//a".replace('{part_number}', part_number)
            ]
            
            for selector in part_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''
                            
                            if part_number.upper() in text.upper() or part_number.lower() in href.lower():
                                self.log(f"Clicking part link: '{text}'")
                                element.click()
                                time.sleep(8)
                                return True
                except:
                    continue
            
            self.log("No specific part page found - continuing on current page", "WARNING")
            return True  # Continue anyway
            
        except Exception as e:
            self.log(f"Error finding part page: {e}", "ERROR")
            return False
    
    def find_3d_model_section(self):
        """Find 3D model section on the page"""
        self.log("Looking for 3D model section...")
        
        try:
            # Look for 3D model indicators
            for keyword in self.model_keywords:
                elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                for element in elements:
                    try:
                        if element.is_displayed():
                            text = element.text.strip()
                            self.log(f"Found 3D indicator: '{text}' (keyword: {keyword})")
                            
                            # Try clicking if it's clickable
                            if element.tag_name in ['a', 'button'] or 'click' in element.get_attribute('onclick') or '':
                                self.log(f"Clicking 3D section: '{text}'")
                                element.click()
                                time.sleep(5)
                                return True
                    except:
                        continue
            
            self.log("No clickable 3D model section found", "WARNING")
            return True  # Continue anyway
            
        except Exception as e:
            self.log(f"Error finding 3D model section: {e}", "ERROR")
            return False
    
    def download_3d_model(self, manufacturer, part_number):
        """Download 3D model from current page"""
        self.log("Looking for 3D model download...")
        
        try:
            # Look for download elements
            download_selectors = []
            
            # Combine model and download keywords
            for model_kw in self.model_keywords:
                for dl_kw in self.download_keywords:
                    download_selectors.extend([
                        f"//a[contains(text(), '{model_kw}') and contains(text(), '{dl_kw}')]",
                        f"//button[contains(text(), '{model_kw}') and contains(text(), '{dl_kw}')]"
                    ])
            
            # Add direct file links
            download_selectors.extend([
                "//a[contains(@href, '.step')]",
                "//a[contains(@href, '.stp')]",
                "//a[contains(@href, '.STEP')]",
                "//a[contains(@href, '.STP')]"
            ])
            
            download_element = None
            for selector in download_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''
                            
                            download_element = element
                            self.log(f"Found download element: '{text}' -> {href[:50]}...")
                            break
                    if download_element:
                        break
                except:
                    continue
            
            if not download_element:
                self.log("No 3D download element found", "ERROR")
                return False
            
            # Get file count before download
            before_files = set(os.listdir(self.downloads_dir))
            
            # Click download
            self.log("Clicking download...")
            download_element.click()
            time.sleep(15)
            
            # Check for new files
            after_files = set(os.listdir(self.downloads_dir))
            new_files = after_files - before_files
            
            step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
            
            if not step_files:
                self.log("No STEP files downloaded", "ERROR")
                return False
            
            # Move and verify the file
            downloaded_file = os.path.join(self.downloads_dir, step_files[0])
            target_filename = f"generic_{manufacturer.replace(' ', '_')}_{part_number.replace('/', '_')}.step"
            target_path = os.path.join(self.models_3d_dir, target_filename)
            
            return self.verify_and_move_file(downloaded_file, target_path, target_filename)
            
        except Exception as e:
            self.log(f"Error downloading 3D model: {e}", "ERROR")
            return False
    
    def verify_and_move_file(self, source_path, target_path, filename):
        """Verify and move the downloaded file"""
        self.log("Verifying downloaded file...")
        
        try:
            # Check if source file exists
            if not os.path.exists(source_path):
                self.log("Downloaded file does not exist", "ERROR")
                return False
            
            # Check file size
            file_size = os.path.getsize(source_path)
            if file_size == 0:
                self.log("Downloaded file is empty", "ERROR")
                return False
            elif file_size < 100:  # Less than 100 bytes
                self.log(f"Downloaded file is very small ({file_size} bytes) - may be invalid", "WARNING")
            
            # Check if it's a STEP file (basic check)
            try:
                with open(source_path, 'r', encoding='utf-8', errors='ignore') as f:
                    first_line = f.readline().strip()
                    if 'STEP' not in first_line.upper():
                        self.log("Downloaded file may not be a valid STEP file", "WARNING")
            except:
                self.log("Could not verify STEP file format", "WARNING")
            
            # Move file to target location
            shutil.move(source_path, target_path)
            
            self.log(f"File verified and moved: {filename} ({file_size:,} bytes)", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error verifying/moving file: {e}", "ERROR")
            return False
    
    def find_3d_model(self, manufacturer, part_number):
        """Main method to find and download 3D model"""
        self.log(f"Starting generic 3D model search for: {manufacturer} {part_number}")
        self.log("=" * 60)
        
        try:
            # Step 1: Setup browser
            if not self.setup_browser():
                return False
            
            # Step 2: Setup directories
            if not self.setup_directories():
                return False
            
            # Step 3: Navigate to manufacturer site
            if not self.navigate_to_manufacturer_site(manufacturer):
                return False
            
            # Step 4: Search for part
            if not self.search_for_part(part_number):
                return False
            
            # Step 5: Find part page
            if not self.find_part_page(part_number):
                return False
            
            # Step 6: Find 3D model section
            if not self.find_3d_model_section():
                return False
            
            # Step 7: Download 3D model
            if not self.download_3d_model(manufacturer, part_number):
                return False
            
            self.log("3D model download completed successfully!", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Unexpected error: {e}", "ERROR")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                self.log("Browser closed")

def main():
    parser = argparse.ArgumentParser(description='Download 3D model from any manufacturer website')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Texas Instruments")')
    parser.add_argument('part_number', help='Part number (e.g., "LM358N")')
    
    args = parser.parse_args()
    
    finder = Generic3DFinder()
    success = finder.find_3d_model(args.manufacturer, args.part_number)
    
    if success:
        print(f"\n🎉 SUCCESS: 3D model downloaded for {args.manufacturer} {args.part_number}")
        sys.exit(0)
    else:
        print(f"\n💥 FAILED: Could not download 3D model for {args.manufacturer} {args.part_number}")
        sys.exit(1)

if __name__ == "__main__":
    main()
