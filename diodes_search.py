#!/usr/bin/env python3

import requests
from bs4 import BeautifulSoup
import re
import os
import json
from pathlib import Path
from urllib.parse import urljoin

class DiodesSearcher:
    def __init__(self, download_dir="files-download"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def search_part(self, part_number):
        """Search for a specific part on Diodes website"""
        print(f"🔍 Searching for part: {part_number}")
        
        # Step 1: Get the product family page
        base_part = part_number.split('-')[0]  # APX803L20-30SA-7 -> APX803L20
        
        # For Diodes, try removing numbers from the end to get the family
        part_family = base_part
        if base_part[-2:].isdigit():  # If ends with 2 digits
            part_family = base_part[:-2]  # APX803L20 -> APX803L
        elif base_part[-1:].isdigit():  # If ends with 1 digit
            part_family = base_part[:-1]  # APX803L2 -> APX803L
        
        product_url = f"https://www.diodes.com/part/view/{part_family}"
        print(f"📄 Fetching product page: {product_url}")
        
        try:
            response = self.session.get(product_url, timeout=30)
            if response.status_code != 200:
                print(f"❌ Failed to fetch product page: {response.status_code}")
                return None
            
            print(f"✅ Product page fetched ({len(response.text)} chars)")
            return response.text, product_url
            
        except Exception as e:
            print(f"❌ Error fetching product page: {e}")
            return None
    
    def extract_package_type(self, html, part_number):
        """Extract package type for the specific part number"""
        if not html:
            return None
        
        soup = BeautifulSoup(html, 'html.parser')
        
        # Look for the specific part number in the table
        part_rows = soup.find_all(text=re.compile(part_number, re.IGNORECASE))
        
        for part_text in part_rows:
            # Find the parent row
            row = part_text.find_parent('tr') if hasattr(part_text, 'find_parent') else None
            if row:
                # Look for package information in the same row
                package_cell = row.find('td')  # Assuming package is in a specific column
                if package_cell:
                    package_text = package_cell.get_text().strip()
                    # Common package types
                    packages = ['SOT23', 'SOT323', 'SOT25', 'SC59', 'SC70', 'SOIC', 'QFN', 'BGA']
                    for pkg in packages:
                        if pkg.lower() in package_text.lower():
                            print(f"📦 Found package type: {pkg}")
                            return pkg
        
        # Fallback: search in general content
        html_lower = html.lower()
        packages = ['sot-23', 'sot23', 'sot-323', 'sot323', 'sot-25', 'sot25', 'sc-59', 'sc59']
        for pkg in packages:
            if pkg in html_lower:
                clean_pkg = pkg.replace('-', '').upper()
                if clean_pkg.startswith('SOT'):
                    clean_pkg = 'SOT-' + clean_pkg[3:]
                elif clean_pkg.startswith('SC'):
                    clean_pkg = 'SC-' + clean_pkg[2:]
                print(f"📦 Found package type (fallback): {clean_pkg}")
                return clean_pkg
        
        return None
    
    def find_3d_models(self, html, base_url, package_type, part_number):
        """Find 3D model files"""
        models = []
        
        if not html or not package_type:
            return models
        
        # Method 1: Look for direct STEP/STP links in HTML
        step_patterns = [
            r'href="([^"]*\.step[^"]*)"',
            r'href="([^"]*\.stp[^"]*)"',
            r'src="([^"]*\.step[^"]*)"',
            r'src="([^"]*\.stp[^"]*)"'
        ]
        
        for pattern in step_patterns:
            matches = re.finditer(pattern, html, re.IGNORECASE)
            for match in matches:
                link = match.group(1)
                full_url = urljoin(base_url, link)
                models.append(full_url)
        
        # Method 2: Try common 3D model directory patterns
        package_clean = package_type.replace('-', '').lower()
        model_url_patterns = [
            f"https://www.diodes.com/assets/3D-models/{package_type}.step",
            f"https://www.diodes.com/assets/3D-models/{package_clean}.step",
            f"https://www.diodes.com/assets/3D-models/{package_type}.stp",
            f"https://www.diodes.com/assets/3D-models/{package_clean}.stp",
            f"https://www.diodes.com/assets/Package-3D/{package_type}.step",
            f"https://www.diodes.com/assets/Package-3D/{package_clean}.step",
            f"https://www.diodes.com/assets/cad-models/{package_type}.step",
            f"https://www.diodes.com/assets/cad-models/{package_clean}.step",
        ]
        
        print(f"🎯 Trying {len(model_url_patterns)} potential 3D model URLs...")
        
        for url in model_url_patterns:
            try:
                print(f"   Testing: {url}")
                response = self.session.head(url, timeout=10)
                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '')
                    print(f"   ✅ Found 3D model: {url}")
                    models.append(url)
                else:
                    print(f"   ❌ {response.status_code}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return models
    
    def download_file(self, url, filename=None):
        """Download a file from URL"""
        try:
            print(f"📥 Downloading: {url}")
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            if not filename:
                filename = os.path.basename(url)
                if not filename or '.' not in filename:
                    if '.pdf' in url.lower():
                        filename = f"datasheet_{int(time.time())}.pdf"
                    elif any(ext in url.lower() for ext in ['.step', '.stp']):
                        filename = f"3d_model_{int(time.time())}.step"
                    else:
                        filename = f"download_{int(time.time())}"
            
            filepath = self.download_dir / filename
            
            # Avoid overwriting
            counter = 1
            original_filepath = filepath
            while filepath.exists():
                name, ext = original_filepath.stem, original_filepath.suffix
                filepath = self.download_dir / f"{name}_{counter}{ext}"
                counter += 1
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"✅ Downloaded: {filepath}")
            return str(filepath)
            
        except Exception as e:
            print(f"❌ Download failed: {e}")
            return None
    
    def search_and_download(self, part_number):
        """Main method to search and download datasheet and 3D model"""
        results = {
            'part_number': part_number,
            'package_type': None,
            'datasheet_file': None,
            'model_files': []
        }
        
        # Step 1: Search for the part
        search_result = self.search_part(part_number)
        if not search_result:
            return results
        
        html, product_url = search_result
        
        # Step 2: Extract package type
        package_type = self.extract_package_type(html, part_number)
        results['package_type'] = package_type
        
        # Step 3: Download datasheet (we know this works)
        base_part = part_number.split('-')[0]
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
        
        datasheet_urls = [
            f"https://www.diodes.com/assets/Datasheets/{part_family}.pdf",
            f"https://www.diodes.com/datasheet/download/{part_family}.pdf"
        ]
        
        for ds_url in datasheet_urls:
            try:
                response = self.session.head(ds_url, timeout=10)
                if response.status_code == 200:
                    datasheet_file = self.download_file(ds_url, f"{part_family}_datasheet.pdf")
                    if datasheet_file:
                        results['datasheet_file'] = datasheet_file
                        break
            except:
                continue
        
        # Step 4: Find and download 3D models
        if package_type:
            model_urls = self.find_3d_models(html, product_url, package_type, part_number)
            for model_url in model_urls:
                model_file = self.download_file(model_url, f"{package_type}_3d_model.step")
                if model_file:
                    results['model_files'].append(model_file)
        
        return results

def main():
    searcher = DiodesSearcher()
    results = searcher.search_and_download("APX803L20-30SA-7")
    
    print("\n" + "="*60)
    print("SEARCH RESULTS")
    print("="*60)
    print(f"Part Number: {results['part_number']}")
    print(f"Package Type: {results['package_type'] or 'Unknown'}")
    print(f"Datasheet: {results['datasheet_file'] or 'Not found'}")
    print(f"3D Models: {len(results['model_files'])} found")
    for model in results['model_files']:
        print(f"  - {model}")

if __name__ == "__main__":
    main()
