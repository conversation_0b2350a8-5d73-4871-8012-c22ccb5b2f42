    def screen_6_login_and_download(self, driver, manufacturer, part_number):
        """SCREEN 6: Wait for download (no login required)"""
        print(f"\n🔸 SCREEN 6: Waiting for download...")

        # Get initial file count in Downloads folder
        downloads_dir = os.path.expanduser("~/Downloads")
        initial_files = set(os.listdir(downloads_dir))
        
        # Wait and monitor for downloads
        print(f"   ⏳ Monitoring Downloads folder for 60 seconds...")
        
        for i in range(12):  # Check every 5 seconds for 60 seconds
            time.sleep(5)
            
            current_files = set(os.listdir(downloads_dir))
            new_files = current_files - initial_files
            
            step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
            zip_files = [f for f in new_files if f.lower().endswith('.zip')]
            
            if step_files:
                step_file = step_files[0]
                print(f"   ✅ STEP file downloaded: {step_file}")
                
                # Move to 3d directory
                src_path = os.path.join(downloads_dir, step_file)
                new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                dst_path = os.path.join('3d', new_name)
                
                try:
                    import shutil
                    shutil.move(src_path, dst_path)
                    self.create_log_file(new_name, manufacturer, part_number)
                    print(f"   ✅ Moved to: 3d/{new_name}")
                    return new_name
                except Exception as e:
                    print(f"   ❌ Error moving file: {e}")
                    return step_file
                    
            elif zip_files:
                zip_file = zip_files[0]
                print(f"   📦 ZIP file downloaded: {zip_file}")
                
                # Extract ZIP and look for STEP files
                try:
                    import zipfile
                    zip_path = os.path.join(downloads_dir, zip_file)
                    
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall('3d')
                    
                    # Look for extracted STEP files
                    extracted_files = os.listdir('3d')
                    step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                    
                    if step_files:
                        step_file = step_files[0]
                        new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                        
                        old_path = os.path.join('3d', step_file)
                        new_path = os.path.join('3d', new_name)
                        
                        os.rename(old_path, new_path)
                        self.create_log_file(new_name, manufacturer, part_number)
                        print(f"   ✅ Extracted and renamed to: {new_name}")
                        return new_name
                        
                except Exception as e:
                    print(f"   ❌ Error extracting ZIP: {e}")
            
            print(f"   ⏳ Checking... ({(i+1)*5}/60 seconds)")
        
        print(f"   ❌ No files downloaded after 60 seconds")
        return None
