ISO-10303-21;
HEADER;
/* R_0402_1005Metric.step 3D STEP model for use in ECAD systems
 * Copyright (C) 2018, kicad StepUp
 * 
 * This work is licensed under the [Creative Commons CC-BY-SA 4.0 License](https://creativecommons.org/licenses/by-sa/4.0/legalcode), 
 * with the following exception:
 * To the extent that the creation of electronic designs that use 'Licensed Material' can be considered to be 'Adapted Material', 
 * then the copyright holder waives article 3 of the license with respect to these designs and any generated files which use data provided 
 * as part of the 'Licensed Material'.
 * You are free to use the library data in your own projects without the obligation to share your project files under this or any other license agreement.
 * However, if you wish to redistribute these libraries, or parts thereof (including in modified form) as a collection then the exception above does not apply. 
 * Please refer to https://github.com/KiCad/kicad-packages3D/blob/master/LICENSE.md for further clarification of the exception.
 * Disclaimer of Warranties and Limitation of Liability.
 * These libraries are provided in the hope that they will be useful, but are provided without warranty of any kind, express or implied.
 * *USE 3D CAD DATA AT YOUR OWN RISK*
 * *DO NOT RELY UPON ANY INFORMATION FOUND HERE WITHOUT INDEPENDENT VERIFICATION.*
 * 
 */

FILE_DESCRIPTION(
/* description */ ('model of R_0402_1005Metric'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'R_0402_1005Metric.step',
/* time_stamp */ '2018-01-04T00:45:35',
/* author */ ('kicad StepUp','ksu'),
/* organization */ ('FreeCAD'),
/* preprocessor_version */ 'OCC',
/* originating_system */ 'kicad StepUp',
/* authorisation */ '');

FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;

DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('R_0402_1005Metric','R_0402_1005Metric','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#805);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#57,#140,#165,#190,#257,#274,#291,#340,#357,
#374,#423,#440,#509,#540,#564,#633,#657,#674,#691,#708,#725,#742,
#759,#776,#793));
#17 = ADVANCED_FACE('',(#18),#52,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#30,#38,#46));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(-0.5,-0.25,3.5E-02));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(-0.5,-0.25,0.315));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(0.,0.,1.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#22,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(-0.5,0.25,3.5E-02));
#34 = LINE('',#35,#36);
#35 = CARTESIAN_POINT('',(-0.5,-0.25,3.5E-02));
#36 = VECTOR('',#37,1.);
#37 = DIRECTION('',(0.,1.,0.));
#38 = ORIENTED_EDGE('',*,*,#39,.T.);
#39 = EDGE_CURVE('',#32,#40,#42,.T.);
#40 = VERTEX_POINT('',#41);
#41 = CARTESIAN_POINT('',(-0.5,0.25,0.315));
#42 = LINE('',#43,#44);
#43 = CARTESIAN_POINT('',(-0.5,0.25,0.));
#44 = VECTOR('',#45,1.);
#45 = DIRECTION('',(0.,0.,1.));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#24,#40,#48,.T.);
#48 = LINE('',#49,#50);
#49 = CARTESIAN_POINT('',(-0.5,-0.25,0.315));
#50 = VECTOR('',#51,1.);
#51 = DIRECTION('',(0.,1.,0.));
#52 = PLANE('',#53);
#53 = AXIS2_PLACEMENT_3D('',#54,#55,#56);
#54 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#55 = DIRECTION('',(1.,0.,0.));
#56 = DIRECTION('',(0.,0.,1.));
#57 = ADVANCED_FACE('',(#58),#135,.F.);
#58 = FACE_BOUND('',#59,.F.);
#59 = EDGE_LOOP('',(#60,#70,#77,#78,#87,#95,#104,#112,#120,#128));
#60 = ORIENTED_EDGE('',*,*,#61,.F.);
#61 = EDGE_CURVE('',#62,#64,#66,.T.);
#62 = VERTEX_POINT('',#63);
#63 = CARTESIAN_POINT('',(-0.465,-0.25,-2.775557561563E-17));
#64 = VERTEX_POINT('',#65);
#65 = CARTESIAN_POINT('',(-0.335,-0.25,-2.775557561563E-17));
#66 = LINE('',#67,#68);
#67 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#68 = VECTOR('',#69,1.);
#69 = DIRECTION('',(1.,0.,0.));
#70 = ORIENTED_EDGE('',*,*,#71,.F.);
#71 = EDGE_CURVE('',#22,#62,#72,.T.);
#72 = CIRCLE('',#73,3.5E-02);
#73 = AXIS2_PLACEMENT_3D('',#74,#75,#76);
#74 = CARTESIAN_POINT('',(-0.465,-0.25,3.5E-02));
#75 = DIRECTION('',(0.,-1.,0.));
#76 = DIRECTION('',(0.,0.,1.));
#77 = ORIENTED_EDGE('',*,*,#21,.T.);
#78 = ORIENTED_EDGE('',*,*,#79,.T.);
#79 = EDGE_CURVE('',#24,#80,#82,.T.);
#80 = VERTEX_POINT('',#81);
#81 = CARTESIAN_POINT('',(-0.465,-0.25,0.35));
#82 = CIRCLE('',#83,3.5E-02);
#83 = AXIS2_PLACEMENT_3D('',#84,#85,#86);
#84 = CARTESIAN_POINT('',(-0.465,-0.25,0.315));
#85 = DIRECTION('',(0.,1.,0.));
#86 = DIRECTION('',(0.,0.,1.));
#87 = ORIENTED_EDGE('',*,*,#88,.T.);
#88 = EDGE_CURVE('',#80,#89,#91,.T.);
#89 = VERTEX_POINT('',#90);
#90 = CARTESIAN_POINT('',(-0.335,-0.25,0.35));
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(-0.5,-0.25,0.35));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(1.,0.,0.));
#95 = ORIENTED_EDGE('',*,*,#96,.F.);
#96 = EDGE_CURVE('',#97,#89,#99,.T.);
#97 = VERTEX_POINT('',#98);
#98 = CARTESIAN_POINT('',(-0.3,-0.25,0.315));
#99 = CIRCLE('',#100,3.5E-02);
#100 = AXIS2_PLACEMENT_3D('',#101,#102,#103);
#101 = CARTESIAN_POINT('',(-0.335,-0.25,0.315));
#102 = DIRECTION('',(0.,-1.,0.));
#103 = DIRECTION('',(0.,0.,1.));
#104 = ORIENTED_EDGE('',*,*,#105,.F.);
#105 = EDGE_CURVE('',#106,#97,#108,.T.);
#106 = VERTEX_POINT('',#107);
#107 = CARTESIAN_POINT('',(-0.465,-0.25,0.315));
#108 = LINE('',#109,#110);
#109 = CARTESIAN_POINT('',(-0.465,-0.25,0.315));
#110 = VECTOR('',#111,1.);
#111 = DIRECTION('',(1.,0.,0.));
#112 = ORIENTED_EDGE('',*,*,#113,.F.);
#113 = EDGE_CURVE('',#114,#106,#116,.T.);
#114 = VERTEX_POINT('',#115);
#115 = CARTESIAN_POINT('',(-0.465,-0.25,3.5E-02));
#116 = LINE('',#117,#118);
#117 = CARTESIAN_POINT('',(-0.465,-0.25,3.5E-02));
#118 = VECTOR('',#119,1.);
#119 = DIRECTION('',(0.,0.,1.));
#120 = ORIENTED_EDGE('',*,*,#121,.T.);
#121 = EDGE_CURVE('',#114,#122,#124,.T.);
#122 = VERTEX_POINT('',#123);
#123 = CARTESIAN_POINT('',(-0.3,-0.25,3.5E-02));
#124 = LINE('',#125,#126);
#125 = CARTESIAN_POINT('',(-0.465,-0.25,3.5E-02));
#126 = VECTOR('',#127,1.);
#127 = DIRECTION('',(1.,0.,0.));
#128 = ORIENTED_EDGE('',*,*,#129,.T.);
#129 = EDGE_CURVE('',#122,#64,#130,.T.);
#130 = CIRCLE('',#131,3.5E-02);
#131 = AXIS2_PLACEMENT_3D('',#132,#133,#134);
#132 = CARTESIAN_POINT('',(-0.335,-0.25,3.5E-02));
#133 = DIRECTION('',(0.,1.,0.));
#134 = DIRECTION('',(0.,0.,1.));
#135 = PLANE('',#136);
#136 = AXIS2_PLACEMENT_3D('',#137,#138,#139);
#137 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#138 = DIRECTION('',(0.,1.,0.));
#139 = DIRECTION('',(0.,0.,1.));
#140 = ADVANCED_FACE('',(#141),#160,.T.);
#141 = FACE_BOUND('',#142,.F.);
#142 = EDGE_LOOP('',(#143,#144,#152,#159));
#143 = ORIENTED_EDGE('',*,*,#71,.T.);
#144 = ORIENTED_EDGE('',*,*,#145,.T.);
#145 = EDGE_CURVE('',#62,#146,#148,.T.);
#146 = VERTEX_POINT('',#147);
#147 = CARTESIAN_POINT('',(-0.465,0.25,-2.775557561563E-17));
#148 = LINE('',#149,#150);
#149 = CARTESIAN_POINT('',(-0.465,-0.25,-2.775557561563E-17));
#150 = VECTOR('',#151,1.);
#151 = DIRECTION('',(0.,1.,0.));
#152 = ORIENTED_EDGE('',*,*,#153,.F.);
#153 = EDGE_CURVE('',#32,#146,#154,.T.);
#154 = CIRCLE('',#155,3.5E-02);
#155 = AXIS2_PLACEMENT_3D('',#156,#157,#158);
#156 = CARTESIAN_POINT('',(-0.465,0.25,3.5E-02));
#157 = DIRECTION('',(0.,-1.,0.));
#158 = DIRECTION('',(0.,0.,1.));
#159 = ORIENTED_EDGE('',*,*,#31,.F.);
#160 = CYLINDRICAL_SURFACE('',#161,3.5E-02);
#161 = AXIS2_PLACEMENT_3D('',#162,#163,#164);
#162 = CARTESIAN_POINT('',(-0.465,-0.25,3.5E-02));
#163 = DIRECTION('',(0.,1.,0.));
#164 = DIRECTION('',(-1.,0.,0.));
#165 = ADVANCED_FACE('',(#166),#185,.T.);
#166 = FACE_BOUND('',#167,.T.);
#167 = EDGE_LOOP('',(#168,#169,#177,#184));
#168 = ORIENTED_EDGE('',*,*,#79,.T.);
#169 = ORIENTED_EDGE('',*,*,#170,.T.);
#170 = EDGE_CURVE('',#80,#171,#173,.T.);
#171 = VERTEX_POINT('',#172);
#172 = CARTESIAN_POINT('',(-0.465,0.25,0.35));
#173 = LINE('',#174,#175);
#174 = CARTESIAN_POINT('',(-0.465,-0.25,0.35));
#175 = VECTOR('',#176,1.);
#176 = DIRECTION('',(0.,1.,0.));
#177 = ORIENTED_EDGE('',*,*,#178,.F.);
#178 = EDGE_CURVE('',#40,#171,#179,.T.);
#179 = CIRCLE('',#180,3.5E-02);
#180 = AXIS2_PLACEMENT_3D('',#181,#182,#183);
#181 = CARTESIAN_POINT('',(-0.465,0.25,0.315));
#182 = DIRECTION('',(0.,1.,0.));
#183 = DIRECTION('',(0.,0.,1.));
#184 = ORIENTED_EDGE('',*,*,#47,.F.);
#185 = CYLINDRICAL_SURFACE('',#186,3.5E-02);
#186 = AXIS2_PLACEMENT_3D('',#187,#188,#189);
#187 = CARTESIAN_POINT('',(-0.465,-0.25,0.315));
#188 = DIRECTION('',(0.,1.,0.));
#189 = DIRECTION('',(-1.,0.,0.));
#190 = ADVANCED_FACE('',(#191),#252,.T.);
#191 = FACE_BOUND('',#192,.T.);
#192 = EDGE_LOOP('',(#193,#201,#202,#203,#204,#212,#221,#229,#237,#245)
);
#193 = ORIENTED_EDGE('',*,*,#194,.F.);
#194 = EDGE_CURVE('',#146,#195,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(-0.335,0.25,-2.775557561563E-17));
#197 = LINE('',#198,#199);
#198 = CARTESIAN_POINT('',(-0.5,0.25,0.));
#199 = VECTOR('',#200,1.);
#200 = DIRECTION('',(1.,0.,0.));
#201 = ORIENTED_EDGE('',*,*,#153,.F.);
#202 = ORIENTED_EDGE('',*,*,#39,.T.);
#203 = ORIENTED_EDGE('',*,*,#178,.T.);
#204 = ORIENTED_EDGE('',*,*,#205,.T.);
#205 = EDGE_CURVE('',#171,#206,#208,.T.);
#206 = VERTEX_POINT('',#207);
#207 = CARTESIAN_POINT('',(-0.335,0.25,0.35));
#208 = LINE('',#209,#210);
#209 = CARTESIAN_POINT('',(-0.5,0.25,0.35));
#210 = VECTOR('',#211,1.);
#211 = DIRECTION('',(1.,0.,0.));
#212 = ORIENTED_EDGE('',*,*,#213,.F.);
#213 = EDGE_CURVE('',#214,#206,#216,.T.);
#214 = VERTEX_POINT('',#215);
#215 = CARTESIAN_POINT('',(-0.3,0.25,0.315));
#216 = CIRCLE('',#217,3.5E-02);
#217 = AXIS2_PLACEMENT_3D('',#218,#219,#220);
#218 = CARTESIAN_POINT('',(-0.335,0.25,0.315));
#219 = DIRECTION('',(0.,-1.,0.));
#220 = DIRECTION('',(0.,0.,1.));
#221 = ORIENTED_EDGE('',*,*,#222,.F.);
#222 = EDGE_CURVE('',#223,#214,#225,.T.);
#223 = VERTEX_POINT('',#224);
#224 = CARTESIAN_POINT('',(-0.465,0.25,0.315));
#225 = LINE('',#226,#227);
#226 = CARTESIAN_POINT('',(-0.465,0.25,0.315));
#227 = VECTOR('',#228,1.);
#228 = DIRECTION('',(1.,0.,0.));
#229 = ORIENTED_EDGE('',*,*,#230,.F.);
#230 = EDGE_CURVE('',#231,#223,#233,.T.);
#231 = VERTEX_POINT('',#232);
#232 = CARTESIAN_POINT('',(-0.465,0.25,3.5E-02));
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(-0.465,0.25,3.5E-02));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(0.,0.,1.));
#237 = ORIENTED_EDGE('',*,*,#238,.T.);
#238 = EDGE_CURVE('',#231,#239,#241,.T.);
#239 = VERTEX_POINT('',#240);
#240 = CARTESIAN_POINT('',(-0.3,0.25,3.5E-02));
#241 = LINE('',#242,#243);
#242 = CARTESIAN_POINT('',(-0.465,0.25,3.5E-02));
#243 = VECTOR('',#244,1.);
#244 = DIRECTION('',(1.,0.,0.));
#245 = ORIENTED_EDGE('',*,*,#246,.T.);
#246 = EDGE_CURVE('',#239,#195,#247,.T.);
#247 = CIRCLE('',#248,3.5E-02);
#248 = AXIS2_PLACEMENT_3D('',#249,#250,#251);
#249 = CARTESIAN_POINT('',(-0.335,0.25,3.5E-02));
#250 = DIRECTION('',(0.,1.,0.));
#251 = DIRECTION('',(0.,0.,1.));
#252 = PLANE('',#253);
#253 = AXIS2_PLACEMENT_3D('',#254,#255,#256);
#254 = CARTESIAN_POINT('',(-0.5,0.25,0.));
#255 = DIRECTION('',(0.,1.,0.));
#256 = DIRECTION('',(0.,0.,1.));
#257 = ADVANCED_FACE('',(#258),#269,.F.);
#258 = FACE_BOUND('',#259,.F.);
#259 = EDGE_LOOP('',(#260,#261,#262,#263));
#260 = ORIENTED_EDGE('',*,*,#194,.F.);
#261 = ORIENTED_EDGE('',*,*,#145,.F.);
#262 = ORIENTED_EDGE('',*,*,#61,.T.);
#263 = ORIENTED_EDGE('',*,*,#264,.T.);
#264 = EDGE_CURVE('',#64,#195,#265,.T.);
#265 = LINE('',#266,#267);
#266 = CARTESIAN_POINT('',(-0.335,-0.25,-2.775557561563E-17));
#267 = VECTOR('',#268,1.);
#268 = DIRECTION('',(0.,1.,0.));
#269 = PLANE('',#270);
#270 = AXIS2_PLACEMENT_3D('',#271,#272,#273);
#271 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#272 = DIRECTION('',(0.,0.,1.));
#273 = DIRECTION('',(1.,0.,0.));
#274 = ADVANCED_FACE('',(#275),#286,.T.);
#275 = FACE_BOUND('',#276,.T.);
#276 = EDGE_LOOP('',(#277,#278,#279,#280));
#277 = ORIENTED_EDGE('',*,*,#129,.T.);
#278 = ORIENTED_EDGE('',*,*,#264,.T.);
#279 = ORIENTED_EDGE('',*,*,#246,.F.);
#280 = ORIENTED_EDGE('',*,*,#281,.F.);
#281 = EDGE_CURVE('',#122,#239,#282,.T.);
#282 = LINE('',#283,#284);
#283 = CARTESIAN_POINT('',(-0.3,-0.25,3.5E-02));
#284 = VECTOR('',#285,1.);
#285 = DIRECTION('',(0.,1.,0.));
#286 = CYLINDRICAL_SURFACE('',#287,3.5E-02);
#287 = AXIS2_PLACEMENT_3D('',#288,#289,#290);
#288 = CARTESIAN_POINT('',(-0.335,-0.25,3.5E-02));
#289 = DIRECTION('',(0.,1.,0.));
#290 = DIRECTION('',(1.,0.,0.));
#291 = ADVANCED_FACE('',(#292),#335,.F.);
#292 = FACE_BOUND('',#293,.F.);
#293 = EDGE_LOOP('',(#294,#295,#296,#297,#305,#313,#321,#329));
#294 = ORIENTED_EDGE('',*,*,#121,.F.);
#295 = ORIENTED_EDGE('',*,*,#113,.T.);
#296 = ORIENTED_EDGE('',*,*,#105,.T.);
#297 = ORIENTED_EDGE('',*,*,#298,.T.);
#298 = EDGE_CURVE('',#97,#299,#301,.T.);
#299 = VERTEX_POINT('',#300);
#300 = CARTESIAN_POINT('',(0.3,-0.25,0.315));
#301 = LINE('',#302,#303);
#302 = CARTESIAN_POINT('',(-0.3,-0.25,0.315));
#303 = VECTOR('',#304,1.);
#304 = DIRECTION('',(1.,0.,0.));
#305 = ORIENTED_EDGE('',*,*,#306,.T.);
#306 = EDGE_CURVE('',#299,#307,#309,.T.);
#307 = VERTEX_POINT('',#308);
#308 = CARTESIAN_POINT('',(0.465,-0.25,0.315));
#309 = LINE('',#310,#311);
#310 = CARTESIAN_POINT('',(-0.465,-0.25,0.315));
#311 = VECTOR('',#312,1.);
#312 = DIRECTION('',(1.,0.,0.));
#313 = ORIENTED_EDGE('',*,*,#314,.F.);
#314 = EDGE_CURVE('',#315,#307,#317,.T.);
#315 = VERTEX_POINT('',#316);
#316 = CARTESIAN_POINT('',(0.465,-0.25,3.5E-02));
#317 = LINE('',#318,#319);
#318 = CARTESIAN_POINT('',(0.465,-0.25,3.5E-02));
#319 = VECTOR('',#320,1.);
#320 = DIRECTION('',(0.,0.,1.));
#321 = ORIENTED_EDGE('',*,*,#322,.F.);
#322 = EDGE_CURVE('',#323,#315,#325,.T.);
#323 = VERTEX_POINT('',#324);
#324 = CARTESIAN_POINT('',(0.3,-0.25,3.5E-02));
#325 = LINE('',#326,#327);
#326 = CARTESIAN_POINT('',(-0.465,-0.25,3.5E-02));
#327 = VECTOR('',#328,1.);
#328 = DIRECTION('',(1.,0.,0.));
#329 = ORIENTED_EDGE('',*,*,#330,.F.);
#330 = EDGE_CURVE('',#122,#323,#331,.T.);
#331 = LINE('',#332,#333);
#332 = CARTESIAN_POINT('',(-0.465,-0.25,3.5E-02));
#333 = VECTOR('',#334,1.);
#334 = DIRECTION('',(1.,0.,0.));
#335 = PLANE('',#336);
#336 = AXIS2_PLACEMENT_3D('',#337,#338,#339);
#337 = CARTESIAN_POINT('',(-0.465,-0.25,3.5E-02));
#338 = DIRECTION('',(0.,1.,0.));
#339 = DIRECTION('',(0.,0.,1.));
#340 = ADVANCED_FACE('',(#341),#352,.T.);
#341 = FACE_BOUND('',#342,.T.);
#342 = EDGE_LOOP('',(#343,#344,#345,#346));
#343 = ORIENTED_EDGE('',*,*,#205,.F.);
#344 = ORIENTED_EDGE('',*,*,#170,.F.);
#345 = ORIENTED_EDGE('',*,*,#88,.T.);
#346 = ORIENTED_EDGE('',*,*,#347,.T.);
#347 = EDGE_CURVE('',#89,#206,#348,.T.);
#348 = LINE('',#349,#350);
#349 = CARTESIAN_POINT('',(-0.335,-0.25,0.35));
#350 = VECTOR('',#351,1.);
#351 = DIRECTION('',(0.,1.,0.));
#352 = PLANE('',#353);
#353 = AXIS2_PLACEMENT_3D('',#354,#355,#356);
#354 = CARTESIAN_POINT('',(-0.5,-0.25,0.35));
#355 = DIRECTION('',(0.,0.,1.));
#356 = DIRECTION('',(1.,0.,0.));
#357 = ADVANCED_FACE('',(#358),#369,.T.);
#358 = FACE_BOUND('',#359,.F.);
#359 = EDGE_LOOP('',(#360,#361,#362,#363));
#360 = ORIENTED_EDGE('',*,*,#96,.T.);
#361 = ORIENTED_EDGE('',*,*,#347,.T.);
#362 = ORIENTED_EDGE('',*,*,#213,.F.);
#363 = ORIENTED_EDGE('',*,*,#364,.F.);
#364 = EDGE_CURVE('',#97,#214,#365,.T.);
#365 = LINE('',#366,#367);
#366 = CARTESIAN_POINT('',(-0.3,-0.25,0.315));
#367 = VECTOR('',#368,1.);
#368 = DIRECTION('',(0.,1.,0.));
#369 = CYLINDRICAL_SURFACE('',#370,3.5E-02);
#370 = AXIS2_PLACEMENT_3D('',#371,#372,#373);
#371 = CARTESIAN_POINT('',(-0.335,-0.25,0.315));
#372 = DIRECTION('',(0.,1.,0.));
#373 = DIRECTION('',(1.,0.,0.));
#374 = ADVANCED_FACE('',(#375),#418,.T.);
#375 = FACE_BOUND('',#376,.T.);
#376 = EDGE_LOOP('',(#377,#378,#379,#380,#388,#396,#404,#412));
#377 = ORIENTED_EDGE('',*,*,#238,.F.);
#378 = ORIENTED_EDGE('',*,*,#230,.T.);
#379 = ORIENTED_EDGE('',*,*,#222,.T.);
#380 = ORIENTED_EDGE('',*,*,#381,.T.);
#381 = EDGE_CURVE('',#214,#382,#384,.T.);
#382 = VERTEX_POINT('',#383);
#383 = CARTESIAN_POINT('',(0.3,0.25,0.315));
#384 = LINE('',#385,#386);
#385 = CARTESIAN_POINT('',(-0.3,0.25,0.315));
#386 = VECTOR('',#387,1.);
#387 = DIRECTION('',(1.,0.,0.));
#388 = ORIENTED_EDGE('',*,*,#389,.T.);
#389 = EDGE_CURVE('',#382,#390,#392,.T.);
#390 = VERTEX_POINT('',#391);
#391 = CARTESIAN_POINT('',(0.465,0.25,0.315));
#392 = LINE('',#393,#394);
#393 = CARTESIAN_POINT('',(-0.465,0.25,0.315));
#394 = VECTOR('',#395,1.);
#395 = DIRECTION('',(1.,0.,0.));
#396 = ORIENTED_EDGE('',*,*,#397,.F.);
#397 = EDGE_CURVE('',#398,#390,#400,.T.);
#398 = VERTEX_POINT('',#399);
#399 = CARTESIAN_POINT('',(0.465,0.25,3.5E-02));
#400 = LINE('',#401,#402);
#401 = CARTESIAN_POINT('',(0.465,0.25,3.5E-02));
#402 = VECTOR('',#403,1.);
#403 = DIRECTION('',(0.,0.,1.));
#404 = ORIENTED_EDGE('',*,*,#405,.F.);
#405 = EDGE_CURVE('',#406,#398,#408,.T.);
#406 = VERTEX_POINT('',#407);
#407 = CARTESIAN_POINT('',(0.3,0.25,3.5E-02));
#408 = LINE('',#409,#410);
#409 = CARTESIAN_POINT('',(-0.465,0.25,3.5E-02));
#410 = VECTOR('',#411,1.);
#411 = DIRECTION('',(1.,0.,0.));
#412 = ORIENTED_EDGE('',*,*,#413,.F.);
#413 = EDGE_CURVE('',#239,#406,#414,.T.);
#414 = LINE('',#415,#416);
#415 = CARTESIAN_POINT('',(-0.465,0.25,3.5E-02));
#416 = VECTOR('',#417,1.);
#417 = DIRECTION('',(1.,0.,0.));
#418 = PLANE('',#419);
#419 = AXIS2_PLACEMENT_3D('',#420,#421,#422);
#420 = CARTESIAN_POINT('',(-0.465,0.25,3.5E-02));
#421 = DIRECTION('',(0.,1.,0.));
#422 = DIRECTION('',(0.,0.,1.));
#423 = ADVANCED_FACE('',(#424),#435,.F.);
#424 = FACE_BOUND('',#425,.F.);
#425 = EDGE_LOOP('',(#426,#427,#428,#434));
#426 = ORIENTED_EDGE('',*,*,#281,.F.);
#427 = ORIENTED_EDGE('',*,*,#330,.T.);
#428 = ORIENTED_EDGE('',*,*,#429,.T.);
#429 = EDGE_CURVE('',#323,#406,#430,.T.);
#430 = LINE('',#431,#432);
#431 = CARTESIAN_POINT('',(0.3,-0.25,3.5E-02));
#432 = VECTOR('',#433,1.);
#433 = DIRECTION('',(0.,1.,0.));
#434 = ORIENTED_EDGE('',*,*,#413,.F.);
#435 = PLANE('',#436);
#436 = AXIS2_PLACEMENT_3D('',#437,#438,#439);
#437 = CARTESIAN_POINT('',(-0.465,-0.25,3.5E-02));
#438 = DIRECTION('',(0.,0.,1.));
#439 = DIRECTION('',(1.,0.,0.));
#440 = ADVANCED_FACE('',(#441),#504,.F.);
#441 = FACE_BOUND('',#442,.F.);
#442 = EDGE_LOOP('',(#443,#453,#460,#461,#462,#463,#472,#480,#489,#497)
);
#443 = ORIENTED_EDGE('',*,*,#444,.F.);
#444 = EDGE_CURVE('',#445,#447,#449,.T.);
#445 = VERTEX_POINT('',#446);
#446 = CARTESIAN_POINT('',(0.335,-0.25,-2.775557561563E-17));
#447 = VERTEX_POINT('',#448);
#448 = CARTESIAN_POINT('',(0.465,-0.25,-2.775557561563E-17));
#449 = LINE('',#450,#451);
#450 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#451 = VECTOR('',#452,1.);
#452 = DIRECTION('',(1.,0.,0.));
#453 = ORIENTED_EDGE('',*,*,#454,.F.);
#454 = EDGE_CURVE('',#323,#445,#455,.T.);
#455 = CIRCLE('',#456,3.5E-02);
#456 = AXIS2_PLACEMENT_3D('',#457,#458,#459);
#457 = CARTESIAN_POINT('',(0.335,-0.25,3.5E-02));
#458 = DIRECTION('',(0.,-1.,0.));
#459 = DIRECTION('',(0.,0.,1.));
#460 = ORIENTED_EDGE('',*,*,#322,.T.);
#461 = ORIENTED_EDGE('',*,*,#314,.T.);
#462 = ORIENTED_EDGE('',*,*,#306,.F.);
#463 = ORIENTED_EDGE('',*,*,#464,.T.);
#464 = EDGE_CURVE('',#299,#465,#467,.T.);
#465 = VERTEX_POINT('',#466);
#466 = CARTESIAN_POINT('',(0.335,-0.25,0.35));
#467 = CIRCLE('',#468,3.5E-02);
#468 = AXIS2_PLACEMENT_3D('',#469,#470,#471);
#469 = CARTESIAN_POINT('',(0.335,-0.25,0.315));
#470 = DIRECTION('',(0.,1.,0.));
#471 = DIRECTION('',(0.,0.,1.));
#472 = ORIENTED_EDGE('',*,*,#473,.T.);
#473 = EDGE_CURVE('',#465,#474,#476,.T.);
#474 = VERTEX_POINT('',#475);
#475 = CARTESIAN_POINT('',(0.465,-0.25,0.35));
#476 = LINE('',#477,#478);
#477 = CARTESIAN_POINT('',(0.3,-0.25,0.35));
#478 = VECTOR('',#479,1.);
#479 = DIRECTION('',(1.,0.,0.));
#480 = ORIENTED_EDGE('',*,*,#481,.F.);
#481 = EDGE_CURVE('',#482,#474,#484,.T.);
#482 = VERTEX_POINT('',#483);
#483 = CARTESIAN_POINT('',(0.5,-0.25,0.315));
#484 = CIRCLE('',#485,3.5E-02);
#485 = AXIS2_PLACEMENT_3D('',#486,#487,#488);
#486 = CARTESIAN_POINT('',(0.465,-0.25,0.315));
#487 = DIRECTION('',(0.,-1.,0.));
#488 = DIRECTION('',(0.,0.,1.));
#489 = ORIENTED_EDGE('',*,*,#490,.F.);
#490 = EDGE_CURVE('',#491,#482,#493,.T.);
#491 = VERTEX_POINT('',#492);
#492 = CARTESIAN_POINT('',(0.5,-0.25,3.5E-02));
#493 = LINE('',#494,#495);
#494 = CARTESIAN_POINT('',(0.5,-0.25,0.));
#495 = VECTOR('',#496,1.);
#496 = DIRECTION('',(0.,0.,1.));
#497 = ORIENTED_EDGE('',*,*,#498,.T.);
#498 = EDGE_CURVE('',#491,#447,#499,.T.);
#499 = CIRCLE('',#500,3.5E-02);
#500 = AXIS2_PLACEMENT_3D('',#501,#502,#503);
#501 = CARTESIAN_POINT('',(0.465,-0.25,3.5E-02));
#502 = DIRECTION('',(0.,1.,0.));
#503 = DIRECTION('',(0.,0.,1.));
#504 = PLANE('',#505);
#505 = AXIS2_PLACEMENT_3D('',#506,#507,#508);
#506 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#507 = DIRECTION('',(0.,1.,0.));
#508 = DIRECTION('',(0.,0.,1.));
#509 = ADVANCED_FACE('',(#510),#535,.F.);
#510 = FACE_BOUND('',#511,.F.);
#511 = EDGE_LOOP('',(#512,#513,#521,#529));
#512 = ORIENTED_EDGE('',*,*,#298,.F.);
#513 = ORIENTED_EDGE('',*,*,#514,.T.);
#514 = EDGE_CURVE('',#97,#515,#517,.T.);
#515 = VERTEX_POINT('',#516);
#516 = CARTESIAN_POINT('',(-0.3,-0.25,0.35));
#517 = LINE('',#518,#519);
#518 = CARTESIAN_POINT('',(-0.3,-0.25,0.315));
#519 = VECTOR('',#520,1.);
#520 = DIRECTION('',(0.,0.,1.));
#521 = ORIENTED_EDGE('',*,*,#522,.T.);
#522 = EDGE_CURVE('',#515,#523,#525,.T.);
#523 = VERTEX_POINT('',#524);
#524 = CARTESIAN_POINT('',(0.3,-0.25,0.35));
#525 = LINE('',#526,#527);
#526 = CARTESIAN_POINT('',(-0.3,-0.25,0.35));
#527 = VECTOR('',#528,1.);
#528 = DIRECTION('',(1.,0.,0.));
#529 = ORIENTED_EDGE('',*,*,#530,.F.);
#530 = EDGE_CURVE('',#299,#523,#531,.T.);
#531 = LINE('',#532,#533);
#532 = CARTESIAN_POINT('',(0.3,-0.25,0.315));
#533 = VECTOR('',#534,1.);
#534 = DIRECTION('',(0.,0.,1.));
#535 = PLANE('',#536);
#536 = AXIS2_PLACEMENT_3D('',#537,#538,#539);
#537 = CARTESIAN_POINT('',(-0.3,-0.25,0.315));
#538 = DIRECTION('',(0.,1.,0.));
#539 = DIRECTION('',(0.,0.,1.));
#540 = ADVANCED_FACE('',(#541),#559,.F.);
#541 = FACE_BOUND('',#542,.F.);
#542 = EDGE_LOOP('',(#543,#544,#545,#553));
#543 = ORIENTED_EDGE('',*,*,#514,.F.);
#544 = ORIENTED_EDGE('',*,*,#364,.T.);
#545 = ORIENTED_EDGE('',*,*,#546,.T.);
#546 = EDGE_CURVE('',#214,#547,#549,.T.);
#547 = VERTEX_POINT('',#548);
#548 = CARTESIAN_POINT('',(-0.3,0.25,0.35));
#549 = LINE('',#550,#551);
#550 = CARTESIAN_POINT('',(-0.3,0.25,0.315));
#551 = VECTOR('',#552,1.);
#552 = DIRECTION('',(0.,0.,1.));
#553 = ORIENTED_EDGE('',*,*,#554,.F.);
#554 = EDGE_CURVE('',#515,#547,#555,.T.);
#555 = LINE('',#556,#557);
#556 = CARTESIAN_POINT('',(-0.3,-0.25,0.35));
#557 = VECTOR('',#558,1.);
#558 = DIRECTION('',(0.,1.,0.));
#559 = PLANE('',#560);
#560 = AXIS2_PLACEMENT_3D('',#561,#562,#563);
#561 = CARTESIAN_POINT('',(-0.3,-0.25,0.315));
#562 = DIRECTION('',(1.,0.,0.));
#563 = DIRECTION('',(0.,0.,1.));
#564 = ADVANCED_FACE('',(#565),#628,.T.);
#565 = FACE_BOUND('',#566,.T.);
#566 = EDGE_LOOP('',(#567,#577,#584,#585,#586,#587,#596,#604,#613,#621)
);
#567 = ORIENTED_EDGE('',*,*,#568,.F.);
#568 = EDGE_CURVE('',#569,#571,#573,.T.);
#569 = VERTEX_POINT('',#570);
#570 = CARTESIAN_POINT('',(0.335,0.25,-2.775557561563E-17));
#571 = VERTEX_POINT('',#572);
#572 = CARTESIAN_POINT('',(0.465,0.25,-2.775557561563E-17));
#573 = LINE('',#574,#575);
#574 = CARTESIAN_POINT('',(0.3,0.25,0.));
#575 = VECTOR('',#576,1.);
#576 = DIRECTION('',(1.,0.,0.));
#577 = ORIENTED_EDGE('',*,*,#578,.F.);
#578 = EDGE_CURVE('',#406,#569,#579,.T.);
#579 = CIRCLE('',#580,3.5E-02);
#580 = AXIS2_PLACEMENT_3D('',#581,#582,#583);
#581 = CARTESIAN_POINT('',(0.335,0.25,3.5E-02));
#582 = DIRECTION('',(0.,-1.,0.));
#583 = DIRECTION('',(0.,0.,1.));
#584 = ORIENTED_EDGE('',*,*,#405,.T.);
#585 = ORIENTED_EDGE('',*,*,#397,.T.);
#586 = ORIENTED_EDGE('',*,*,#389,.F.);
#587 = ORIENTED_EDGE('',*,*,#588,.T.);
#588 = EDGE_CURVE('',#382,#589,#591,.T.);
#589 = VERTEX_POINT('',#590);
#590 = CARTESIAN_POINT('',(0.335,0.25,0.35));
#591 = CIRCLE('',#592,3.5E-02);
#592 = AXIS2_PLACEMENT_3D('',#593,#594,#595);
#593 = CARTESIAN_POINT('',(0.335,0.25,0.315));
#594 = DIRECTION('',(0.,1.,0.));
#595 = DIRECTION('',(0.,0.,1.));
#596 = ORIENTED_EDGE('',*,*,#597,.T.);
#597 = EDGE_CURVE('',#589,#598,#600,.T.);
#598 = VERTEX_POINT('',#599);
#599 = CARTESIAN_POINT('',(0.465,0.25,0.35));
#600 = LINE('',#601,#602);
#601 = CARTESIAN_POINT('',(0.3,0.25,0.35));
#602 = VECTOR('',#603,1.);
#603 = DIRECTION('',(1.,0.,0.));
#604 = ORIENTED_EDGE('',*,*,#605,.F.);
#605 = EDGE_CURVE('',#606,#598,#608,.T.);
#606 = VERTEX_POINT('',#607);
#607 = CARTESIAN_POINT('',(0.5,0.25,0.315));
#608 = CIRCLE('',#609,3.5E-02);
#609 = AXIS2_PLACEMENT_3D('',#610,#611,#612);
#610 = CARTESIAN_POINT('',(0.465,0.25,0.315));
#611 = DIRECTION('',(0.,-1.,0.));
#612 = DIRECTION('',(0.,0.,1.));
#613 = ORIENTED_EDGE('',*,*,#614,.F.);
#614 = EDGE_CURVE('',#615,#606,#617,.T.);
#615 = VERTEX_POINT('',#616);
#616 = CARTESIAN_POINT('',(0.5,0.25,3.5E-02));
#617 = LINE('',#618,#619);
#618 = CARTESIAN_POINT('',(0.5,0.25,0.));
#619 = VECTOR('',#620,1.);
#620 = DIRECTION('',(0.,0.,1.));
#621 = ORIENTED_EDGE('',*,*,#622,.T.);
#622 = EDGE_CURVE('',#615,#571,#623,.T.);
#623 = CIRCLE('',#624,3.5E-02);
#624 = AXIS2_PLACEMENT_3D('',#625,#626,#627);
#625 = CARTESIAN_POINT('',(0.465,0.25,3.5E-02));
#626 = DIRECTION('',(0.,1.,0.));
#627 = DIRECTION('',(0.,0.,1.));
#628 = PLANE('',#629);
#629 = AXIS2_PLACEMENT_3D('',#630,#631,#632);
#630 = CARTESIAN_POINT('',(0.3,0.25,0.));
#631 = DIRECTION('',(0.,1.,0.));
#632 = DIRECTION('',(0.,0.,1.));
#633 = ADVANCED_FACE('',(#634),#652,.T.);
#634 = FACE_BOUND('',#635,.T.);
#635 = EDGE_LOOP('',(#636,#637,#638,#646));
#636 = ORIENTED_EDGE('',*,*,#381,.F.);
#637 = ORIENTED_EDGE('',*,*,#546,.T.);
#638 = ORIENTED_EDGE('',*,*,#639,.T.);
#639 = EDGE_CURVE('',#547,#640,#642,.T.);
#640 = VERTEX_POINT('',#641);
#641 = CARTESIAN_POINT('',(0.3,0.25,0.35));
#642 = LINE('',#643,#644);
#643 = CARTESIAN_POINT('',(-0.3,0.25,0.35));
#644 = VECTOR('',#645,1.);
#645 = DIRECTION('',(1.,0.,0.));
#646 = ORIENTED_EDGE('',*,*,#647,.F.);
#647 = EDGE_CURVE('',#382,#640,#648,.T.);
#648 = LINE('',#649,#650);
#649 = CARTESIAN_POINT('',(0.3,0.25,0.315));
#650 = VECTOR('',#651,1.);
#651 = DIRECTION('',(0.,0.,1.));
#652 = PLANE('',#653);
#653 = AXIS2_PLACEMENT_3D('',#654,#655,#656);
#654 = CARTESIAN_POINT('',(-0.3,0.25,0.315));
#655 = DIRECTION('',(0.,1.,0.));
#656 = DIRECTION('',(0.,0.,1.));
#657 = ADVANCED_FACE('',(#658),#669,.T.);
#658 = FACE_BOUND('',#659,.F.);
#659 = EDGE_LOOP('',(#660,#661,#667,#668));
#660 = ORIENTED_EDGE('',*,*,#454,.T.);
#661 = ORIENTED_EDGE('',*,*,#662,.T.);
#662 = EDGE_CURVE('',#445,#569,#663,.T.);
#663 = LINE('',#664,#665);
#664 = CARTESIAN_POINT('',(0.335,-0.25,-2.775557561563E-17));
#665 = VECTOR('',#666,1.);
#666 = DIRECTION('',(0.,1.,0.));
#667 = ORIENTED_EDGE('',*,*,#578,.F.);
#668 = ORIENTED_EDGE('',*,*,#429,.F.);
#669 = CYLINDRICAL_SURFACE('',#670,3.5E-02);
#670 = AXIS2_PLACEMENT_3D('',#671,#672,#673);
#671 = CARTESIAN_POINT('',(0.335,-0.25,3.5E-02));
#672 = DIRECTION('',(0.,1.,0.));
#673 = DIRECTION('',(-1.,0.,0.));
#674 = ADVANCED_FACE('',(#675),#686,.F.);
#675 = FACE_BOUND('',#676,.F.);
#676 = EDGE_LOOP('',(#677,#678,#679,#680));
#677 = ORIENTED_EDGE('',*,*,#568,.F.);
#678 = ORIENTED_EDGE('',*,*,#662,.F.);
#679 = ORIENTED_EDGE('',*,*,#444,.T.);
#680 = ORIENTED_EDGE('',*,*,#681,.T.);
#681 = EDGE_CURVE('',#447,#571,#682,.T.);
#682 = LINE('',#683,#684);
#683 = CARTESIAN_POINT('',(0.465,-0.25,-2.775557561563E-17));
#684 = VECTOR('',#685,1.);
#685 = DIRECTION('',(0.,1.,0.));
#686 = PLANE('',#687);
#687 = AXIS2_PLACEMENT_3D('',#688,#689,#690);
#688 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#689 = DIRECTION('',(0.,0.,1.));
#690 = DIRECTION('',(1.,0.,0.));
#691 = ADVANCED_FACE('',(#692),#703,.T.);
#692 = FACE_BOUND('',#693,.T.);
#693 = EDGE_LOOP('',(#694,#695,#696,#697));
#694 = ORIENTED_EDGE('',*,*,#498,.T.);
#695 = ORIENTED_EDGE('',*,*,#681,.T.);
#696 = ORIENTED_EDGE('',*,*,#622,.F.);
#697 = ORIENTED_EDGE('',*,*,#698,.F.);
#698 = EDGE_CURVE('',#491,#615,#699,.T.);
#699 = LINE('',#700,#701);
#700 = CARTESIAN_POINT('',(0.5,-0.25,3.5E-02));
#701 = VECTOR('',#702,1.);
#702 = DIRECTION('',(0.,1.,0.));
#703 = CYLINDRICAL_SURFACE('',#704,3.5E-02);
#704 = AXIS2_PLACEMENT_3D('',#705,#706,#707);
#705 = CARTESIAN_POINT('',(0.465,-0.25,3.5E-02));
#706 = DIRECTION('',(0.,1.,0.));
#707 = DIRECTION('',(1.,0.,0.));
#708 = ADVANCED_FACE('',(#709),#720,.T.);
#709 = FACE_BOUND('',#710,.T.);
#710 = EDGE_LOOP('',(#711,#712,#713,#714));
#711 = ORIENTED_EDGE('',*,*,#490,.F.);
#712 = ORIENTED_EDGE('',*,*,#698,.T.);
#713 = ORIENTED_EDGE('',*,*,#614,.T.);
#714 = ORIENTED_EDGE('',*,*,#715,.F.);
#715 = EDGE_CURVE('',#482,#606,#716,.T.);
#716 = LINE('',#717,#718);
#717 = CARTESIAN_POINT('',(0.5,-0.25,0.315));
#718 = VECTOR('',#719,1.);
#719 = DIRECTION('',(0.,1.,0.));
#720 = PLANE('',#721);
#721 = AXIS2_PLACEMENT_3D('',#722,#723,#724);
#722 = CARTESIAN_POINT('',(0.5,-0.25,0.));
#723 = DIRECTION('',(1.,0.,0.));
#724 = DIRECTION('',(0.,0.,1.));
#725 = ADVANCED_FACE('',(#726),#737,.T.);
#726 = FACE_BOUND('',#727,.F.);
#727 = EDGE_LOOP('',(#728,#729,#735,#736));
#728 = ORIENTED_EDGE('',*,*,#481,.T.);
#729 = ORIENTED_EDGE('',*,*,#730,.T.);
#730 = EDGE_CURVE('',#474,#598,#731,.T.);
#731 = LINE('',#732,#733);
#732 = CARTESIAN_POINT('',(0.465,-0.25,0.35));
#733 = VECTOR('',#734,1.);
#734 = DIRECTION('',(0.,1.,0.));
#735 = ORIENTED_EDGE('',*,*,#605,.F.);
#736 = ORIENTED_EDGE('',*,*,#715,.F.);
#737 = CYLINDRICAL_SURFACE('',#738,3.5E-02);
#738 = AXIS2_PLACEMENT_3D('',#739,#740,#741);
#739 = CARTESIAN_POINT('',(0.465,-0.25,0.315));
#740 = DIRECTION('',(0.,1.,0.));
#741 = DIRECTION('',(1.,0.,0.));
#742 = ADVANCED_FACE('',(#743),#754,.T.);
#743 = FACE_BOUND('',#744,.T.);
#744 = EDGE_LOOP('',(#745,#746,#752,#753));
#745 = ORIENTED_EDGE('',*,*,#597,.F.);
#746 = ORIENTED_EDGE('',*,*,#747,.F.);
#747 = EDGE_CURVE('',#465,#589,#748,.T.);
#748 = LINE('',#749,#750);
#749 = CARTESIAN_POINT('',(0.335,-0.25,0.35));
#750 = VECTOR('',#751,1.);
#751 = DIRECTION('',(0.,1.,0.));
#752 = ORIENTED_EDGE('',*,*,#473,.T.);
#753 = ORIENTED_EDGE('',*,*,#730,.T.);
#754 = PLANE('',#755);
#755 = AXIS2_PLACEMENT_3D('',#756,#757,#758);
#756 = CARTESIAN_POINT('',(0.3,-0.25,0.35));
#757 = DIRECTION('',(0.,0.,1.));
#758 = DIRECTION('',(1.,0.,0.));
#759 = ADVANCED_FACE('',(#760),#771,.T.);
#760 = FACE_BOUND('',#761,.T.);
#761 = EDGE_LOOP('',(#762,#763,#764,#765));
#762 = ORIENTED_EDGE('',*,*,#464,.T.);
#763 = ORIENTED_EDGE('',*,*,#747,.T.);
#764 = ORIENTED_EDGE('',*,*,#588,.F.);
#765 = ORIENTED_EDGE('',*,*,#766,.F.);
#766 = EDGE_CURVE('',#299,#382,#767,.T.);
#767 = LINE('',#768,#769);
#768 = CARTESIAN_POINT('',(0.3,-0.25,0.315));
#769 = VECTOR('',#770,1.);
#770 = DIRECTION('',(0.,1.,0.));
#771 = CYLINDRICAL_SURFACE('',#772,3.5E-02);
#772 = AXIS2_PLACEMENT_3D('',#773,#774,#775);
#773 = CARTESIAN_POINT('',(0.335,-0.25,0.315));
#774 = DIRECTION('',(0.,1.,0.));
#775 = DIRECTION('',(-1.,0.,0.));
#776 = ADVANCED_FACE('',(#777),#788,.T.);
#777 = FACE_BOUND('',#778,.T.);
#778 = EDGE_LOOP('',(#779,#780,#781,#782));
#779 = ORIENTED_EDGE('',*,*,#530,.F.);
#780 = ORIENTED_EDGE('',*,*,#766,.T.);
#781 = ORIENTED_EDGE('',*,*,#647,.T.);
#782 = ORIENTED_EDGE('',*,*,#783,.F.);
#783 = EDGE_CURVE('',#523,#640,#784,.T.);
#784 = LINE('',#785,#786);
#785 = CARTESIAN_POINT('',(0.3,-0.25,0.35));
#786 = VECTOR('',#787,1.);
#787 = DIRECTION('',(0.,1.,0.));
#788 = PLANE('',#789);
#789 = AXIS2_PLACEMENT_3D('',#790,#791,#792);
#790 = CARTESIAN_POINT('',(0.3,-0.25,0.315));
#791 = DIRECTION('',(1.,0.,0.));
#792 = DIRECTION('',(0.,0.,1.));
#793 = ADVANCED_FACE('',(#794),#800,.T.);
#794 = FACE_BOUND('',#795,.T.);
#795 = EDGE_LOOP('',(#796,#797,#798,#799));
#796 = ORIENTED_EDGE('',*,*,#554,.F.);
#797 = ORIENTED_EDGE('',*,*,#522,.T.);
#798 = ORIENTED_EDGE('',*,*,#783,.T.);
#799 = ORIENTED_EDGE('',*,*,#639,.F.);
#800 = PLANE('',#801);
#801 = AXIS2_PLACEMENT_3D('',#802,#803,#804);
#802 = CARTESIAN_POINT('',(-0.3,-0.25,0.35));
#803 = DIRECTION('',(0.,0.,1.));
#804 = DIRECTION('',(1.,0.,0.));
#805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#809)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#806,#807,#808)) REPRESENTATION_CONTEXT('Context #1',
'3D Context with UNIT and UNCERTAINTY') );
#806 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#807 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#808 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#809 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#806,
'distance_accuracy_value','confusion accuracy');
#810 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#811 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#812,
#820,#827,#834,#841,#848,#855,#862,#870,#877,#884,#891,#898,#905,
#913,#920,#927,#934,#941,#948,#955,#962,#969,#976,#983,#990),#805);
#812 = STYLED_ITEM('color',(#813),#17);
#813 = PRESENTATION_STYLE_ASSIGNMENT((#814));
#814 = SURFACE_STYLE_USAGE(.BOTH.,#815);
#815 = SURFACE_SIDE_STYLE('',(#816));
#816 = SURFACE_STYLE_FILL_AREA(#817);
#817 = FILL_AREA_STYLE('',(#818));
#818 = FILL_AREA_STYLE_COLOUR('',#819);
#819 = COLOUR_RGB('',0.824000000954,0.819999992847,0.78100001812);
#820 = STYLED_ITEM('color',(#821),#57);
#821 = PRESENTATION_STYLE_ASSIGNMENT((#822));
#822 = SURFACE_STYLE_USAGE(.BOTH.,#823);
#823 = SURFACE_SIDE_STYLE('',(#824));
#824 = SURFACE_STYLE_FILL_AREA(#825);
#825 = FILL_AREA_STYLE('',(#826));
#826 = FILL_AREA_STYLE_COLOUR('',#819);
#827 = STYLED_ITEM('color',(#828),#140);
#828 = PRESENTATION_STYLE_ASSIGNMENT((#829));
#829 = SURFACE_STYLE_USAGE(.BOTH.,#830);
#830 = SURFACE_SIDE_STYLE('',(#831));
#831 = SURFACE_STYLE_FILL_AREA(#832);
#832 = FILL_AREA_STYLE('',(#833));
#833 = FILL_AREA_STYLE_COLOUR('',#819);
#834 = STYLED_ITEM('color',(#835),#165);
#835 = PRESENTATION_STYLE_ASSIGNMENT((#836));
#836 = SURFACE_STYLE_USAGE(.BOTH.,#837);
#837 = SURFACE_SIDE_STYLE('',(#838));
#838 = SURFACE_STYLE_FILL_AREA(#839);
#839 = FILL_AREA_STYLE('',(#840));
#840 = FILL_AREA_STYLE_COLOUR('',#819);
#841 = STYLED_ITEM('color',(#842),#190);
#842 = PRESENTATION_STYLE_ASSIGNMENT((#843));
#843 = SURFACE_STYLE_USAGE(.BOTH.,#844);
#844 = SURFACE_SIDE_STYLE('',(#845));
#845 = SURFACE_STYLE_FILL_AREA(#846);
#846 = FILL_AREA_STYLE('',(#847));
#847 = FILL_AREA_STYLE_COLOUR('',#819);
#848 = STYLED_ITEM('color',(#849),#257);
#849 = PRESENTATION_STYLE_ASSIGNMENT((#850));
#850 = SURFACE_STYLE_USAGE(.BOTH.,#851);
#851 = SURFACE_SIDE_STYLE('',(#852));
#852 = SURFACE_STYLE_FILL_AREA(#853);
#853 = FILL_AREA_STYLE('',(#854));
#854 = FILL_AREA_STYLE_COLOUR('',#819);
#855 = STYLED_ITEM('color',(#856),#274);
#856 = PRESENTATION_STYLE_ASSIGNMENT((#857));
#857 = SURFACE_STYLE_USAGE(.BOTH.,#858);
#858 = SURFACE_SIDE_STYLE('',(#859));
#859 = SURFACE_STYLE_FILL_AREA(#860);
#860 = FILL_AREA_STYLE('',(#861));
#861 = FILL_AREA_STYLE_COLOUR('',#819);
#862 = STYLED_ITEM('color',(#863),#291);
#863 = PRESENTATION_STYLE_ASSIGNMENT((#864));
#864 = SURFACE_STYLE_USAGE(.BOTH.,#865);
#865 = SURFACE_SIDE_STYLE('',(#866));
#866 = SURFACE_STYLE_FILL_AREA(#867);
#867 = FILL_AREA_STYLE('',(#868));
#868 = FILL_AREA_STYLE_COLOUR('',#869);
#869 = COLOUR_RGB('',0.894999980927,0.89099997282,0.813000023365);
#870 = STYLED_ITEM('color',(#871),#340);
#871 = PRESENTATION_STYLE_ASSIGNMENT((#872));
#872 = SURFACE_STYLE_USAGE(.BOTH.,#873);
#873 = SURFACE_SIDE_STYLE('',(#874));
#874 = SURFACE_STYLE_FILL_AREA(#875);
#875 = FILL_AREA_STYLE('',(#876));
#876 = FILL_AREA_STYLE_COLOUR('',#819);
#877 = STYLED_ITEM('color',(#878),#357);
#878 = PRESENTATION_STYLE_ASSIGNMENT((#879));
#879 = SURFACE_STYLE_USAGE(.BOTH.,#880);
#880 = SURFACE_SIDE_STYLE('',(#881));
#881 = SURFACE_STYLE_FILL_AREA(#882);
#882 = FILL_AREA_STYLE('',(#883));
#883 = FILL_AREA_STYLE_COLOUR('',#819);
#884 = STYLED_ITEM('color',(#885),#374);
#885 = PRESENTATION_STYLE_ASSIGNMENT((#886));
#886 = SURFACE_STYLE_USAGE(.BOTH.,#887);
#887 = SURFACE_SIDE_STYLE('',(#888));
#888 = SURFACE_STYLE_FILL_AREA(#889);
#889 = FILL_AREA_STYLE('',(#890));
#890 = FILL_AREA_STYLE_COLOUR('',#869);
#891 = STYLED_ITEM('color',(#892),#423);
#892 = PRESENTATION_STYLE_ASSIGNMENT((#893));
#893 = SURFACE_STYLE_USAGE(.BOTH.,#894);
#894 = SURFACE_SIDE_STYLE('',(#895));
#895 = SURFACE_STYLE_FILL_AREA(#896);
#896 = FILL_AREA_STYLE('',(#897));
#897 = FILL_AREA_STYLE_COLOUR('',#869);
#898 = STYLED_ITEM('color',(#899),#440);
#899 = PRESENTATION_STYLE_ASSIGNMENT((#900));
#900 = SURFACE_STYLE_USAGE(.BOTH.,#901);
#901 = SURFACE_SIDE_STYLE('',(#902));
#902 = SURFACE_STYLE_FILL_AREA(#903);
#903 = FILL_AREA_STYLE('',(#904));
#904 = FILL_AREA_STYLE_COLOUR('',#819);
#905 = STYLED_ITEM('color',(#906),#509);
#906 = PRESENTATION_STYLE_ASSIGNMENT((#907));
#907 = SURFACE_STYLE_USAGE(.BOTH.,#908);
#908 = SURFACE_SIDE_STYLE('',(#909));
#909 = SURFACE_STYLE_FILL_AREA(#910);
#910 = FILL_AREA_STYLE('',(#911));
#911 = FILL_AREA_STYLE_COLOUR('',#912);
#912 = COLOUR_RGB('',8.200000226498E-02,8.600000292063E-02,
9.399999678135E-02);
#913 = STYLED_ITEM('color',(#914),#540);
#914 = PRESENTATION_STYLE_ASSIGNMENT((#915));
#915 = SURFACE_STYLE_USAGE(.BOTH.,#916);
#916 = SURFACE_SIDE_STYLE('',(#917));
#917 = SURFACE_STYLE_FILL_AREA(#918);
#918 = FILL_AREA_STYLE('',(#919));
#919 = FILL_AREA_STYLE_COLOUR('',#912);
#920 = STYLED_ITEM('color',(#921),#564);
#921 = PRESENTATION_STYLE_ASSIGNMENT((#922));
#922 = SURFACE_STYLE_USAGE(.BOTH.,#923);
#923 = SURFACE_SIDE_STYLE('',(#924));
#924 = SURFACE_STYLE_FILL_AREA(#925);
#925 = FILL_AREA_STYLE('',(#926));
#926 = FILL_AREA_STYLE_COLOUR('',#819);
#927 = STYLED_ITEM('color',(#928),#633);
#928 = PRESENTATION_STYLE_ASSIGNMENT((#929));
#929 = SURFACE_STYLE_USAGE(.BOTH.,#930);
#930 = SURFACE_SIDE_STYLE('',(#931));
#931 = SURFACE_STYLE_FILL_AREA(#932);
#932 = FILL_AREA_STYLE('',(#933));
#933 = FILL_AREA_STYLE_COLOUR('',#912);
#934 = STYLED_ITEM('color',(#935),#657);
#935 = PRESENTATION_STYLE_ASSIGNMENT((#936));
#936 = SURFACE_STYLE_USAGE(.BOTH.,#937);
#937 = SURFACE_SIDE_STYLE('',(#938));
#938 = SURFACE_STYLE_FILL_AREA(#939);
#939 = FILL_AREA_STYLE('',(#940));
#940 = FILL_AREA_STYLE_COLOUR('',#819);
#941 = STYLED_ITEM('color',(#942),#674);
#942 = PRESENTATION_STYLE_ASSIGNMENT((#943));
#943 = SURFACE_STYLE_USAGE(.BOTH.,#944);
#944 = SURFACE_SIDE_STYLE('',(#945));
#945 = SURFACE_STYLE_FILL_AREA(#946);
#946 = FILL_AREA_STYLE('',(#947));
#947 = FILL_AREA_STYLE_COLOUR('',#819);
#948 = STYLED_ITEM('color',(#949),#691);
#949 = PRESENTATION_STYLE_ASSIGNMENT((#950));
#950 = SURFACE_STYLE_USAGE(.BOTH.,#951);
#951 = SURFACE_SIDE_STYLE('',(#952));
#952 = SURFACE_STYLE_FILL_AREA(#953);
#953 = FILL_AREA_STYLE('',(#954));
#954 = FILL_AREA_STYLE_COLOUR('',#819);
#955 = STYLED_ITEM('color',(#956),#708);
#956 = PRESENTATION_STYLE_ASSIGNMENT((#957));
#957 = SURFACE_STYLE_USAGE(.BOTH.,#958);
#958 = SURFACE_SIDE_STYLE('',(#959));
#959 = SURFACE_STYLE_FILL_AREA(#960);
#960 = FILL_AREA_STYLE('',(#961));
#961 = FILL_AREA_STYLE_COLOUR('',#819);
#962 = STYLED_ITEM('color',(#963),#725);
#963 = PRESENTATION_STYLE_ASSIGNMENT((#964));
#964 = SURFACE_STYLE_USAGE(.BOTH.,#965);
#965 = SURFACE_SIDE_STYLE('',(#966));
#966 = SURFACE_STYLE_FILL_AREA(#967);
#967 = FILL_AREA_STYLE('',(#968));
#968 = FILL_AREA_STYLE_COLOUR('',#819);
#969 = STYLED_ITEM('color',(#970),#742);
#970 = PRESENTATION_STYLE_ASSIGNMENT((#971));
#971 = SURFACE_STYLE_USAGE(.BOTH.,#972);
#972 = SURFACE_SIDE_STYLE('',(#973));
#973 = SURFACE_STYLE_FILL_AREA(#974);
#974 = FILL_AREA_STYLE('',(#975));
#975 = FILL_AREA_STYLE_COLOUR('',#819);
#976 = STYLED_ITEM('color',(#977),#759);
#977 = PRESENTATION_STYLE_ASSIGNMENT((#978));
#978 = SURFACE_STYLE_USAGE(.BOTH.,#979);
#979 = SURFACE_SIDE_STYLE('',(#980));
#980 = SURFACE_STYLE_FILL_AREA(#981);
#981 = FILL_AREA_STYLE('',(#982));
#982 = FILL_AREA_STYLE_COLOUR('',#819);
#983 = STYLED_ITEM('color',(#984),#776);
#984 = PRESENTATION_STYLE_ASSIGNMENT((#985));
#985 = SURFACE_STYLE_USAGE(.BOTH.,#986);
#986 = SURFACE_SIDE_STYLE('',(#987));
#987 = SURFACE_STYLE_FILL_AREA(#988);
#988 = FILL_AREA_STYLE('',(#989));
#989 = FILL_AREA_STYLE_COLOUR('',#912);
#990 = STYLED_ITEM('color',(#991),#793);
#991 = PRESENTATION_STYLE_ASSIGNMENT((#992));
#992 = SURFACE_STYLE_USAGE(.BOTH.,#993);
#993 = SURFACE_SIDE_STYLE('',(#994));
#994 = SURFACE_STYLE_FILL_AREA(#995);
#995 = FILL_AREA_STYLE('',(#996));
#996 = FILL_AREA_STYLE_COLOUR('',#912);
ENDSEC;
END-ISO-10303-21;
