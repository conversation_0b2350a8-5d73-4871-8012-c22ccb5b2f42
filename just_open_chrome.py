#!/usr/bin/env python3
"""
JUST OPEN CHROME
================
Just open Chrome and keep it open - no automation.
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

def just_open_chrome():
    print("🎯 JUST OPENING CHROME")
    print("=" * 25)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    print("✅ Chrome opened")
    
    driver.get('https://app.ultralibrarian.com')
    print("✅ Loaded UltraLibrarian")
    
    print("\n🔒 CHROME IS OPEN AND WILL STAY OPEN")
    print("You can now manually navigate through the screens")
    print("The automation will not do anything - just keep Chrome open")
    
    # Just wait forever
    try:
        while True:
            time.sleep(10)
    except KeyboardInterrupt:
        print("\nClosing Chrome...")
        driver.quit()

if __name__ == "__main__":
    just_open_chrome()
