#!/usr/bin/env python3
"""
Actually check if LM358N has a 3D model on TI
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time

def check_lm358n_3d_model():
    print("CHECKING IF LM358N ACTUALLY HAS 3D MODEL")
    print("=" * 50)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        print("1. Searching for LM358N on TI...")
        driver.get("https://www.ti.com")
        time.sleep(20)
        
        # Search for LM358N
        js_find_input = """
        var searchBox = document.querySelector('#headerSearchBox');
        if (searchBox && searchBox.shadowRoot) {
            return searchBox.shadowRoot.querySelector('input');
        }
        var allInputs = document.querySelectorAll('input');
        for (var i = 0; i < allInputs.length; i++) {
            if (allInputs[i].offsetWidth > 0) return allInputs[i];
        }
        return null;
        """
        
        input_element = driver.execute_script(js_find_input)
        if input_element:
            input_element.click()
            time.sleep(2)
            input_element.clear()
            input_element.send_keys("LM358N")
            input_element.send_keys(Keys.RETURN)
            time.sleep(15)
        
        print(f"2. Search results URL: {driver.current_url}")
        
        # Look for actual LM358N product results (not just keyword counts)
        print("3. Looking for actual LM358N product results...")
        
        # Get visible text only
        body = driver.find_element(By.TAG_NAME, "body")
        visible_text = body.text
        
        print("First 500 characters of visible text:")
        print(f"'{visible_text[:500]}...'")
        
        # Look for specific LM358N product mentions
        lines = visible_text.split('\n')
        lm358_lines = [line.strip() for line in lines if 'LM358' in line.upper() and line.strip()]
        
        print(f"\n4. Found {len(lm358_lines)} lines mentioning LM358:")
        for i, line in enumerate(lm358_lines[:10]):
            print(f"  {i+1}. {line}")
        
        # Try to find and click on actual LM358N product
        print("\n5. Looking for clickable LM358N product...")
        
        # Look for elements containing LM358N text
        elements_with_lm358 = driver.find_elements(By.XPATH, "//*[contains(text(), 'LM358N')]")
        
        print(f"Found {len(elements_with_lm358)} elements containing 'LM358N'")
        
        clickable_found = False
        for i, elem in enumerate(elements_with_lm358[:5]):
            try:
                text = elem.text.strip()
                tag = elem.tag_name
                clickable = elem.is_enabled() and elem.is_displayed()
                
                print(f"  Element {i+1}: <{tag}> '{text}' (clickable: {clickable})")
                
                # Try to click if it looks like a product link
                if clickable and ('LM358N' in text or tag == 'a'):
                    print(f"  Trying to click element {i+1}...")
                    elem.click()
                    time.sleep(10)
                    
                    if driver.current_url != driver.current_url:  # URL changed
                        print(f"  ✅ Clicked successfully! New URL: {driver.current_url}")
                        clickable_found = True
                        break
                    else:
                        print(f"  ❌ Click didn't change URL")
            except Exception as e:
                print(f"  Error with element {i+1}: {e}")
        
        if not clickable_found:
            print("❌ Could not find clickable LM358N product")
            return False
        
        # Now check if this product page has 3D models
        print("\n6. Checking product page for 3D models...")
        
        product_text = driver.find_element(By.TAG_NAME, "body").text.lower()
        
        # Look for specific 3D model indicators
        model_indicators = [
            '3d model', 'step file', '.step', '.stp', 
            'mechanical drawing', 'cad model', 'download 3d'
        ]
        
        found_indicators = []
        for indicator in model_indicators:
            if indicator in product_text:
                found_indicators.append(indicator)
        
        if found_indicators:
            print(f"✅ Found 3D model indicators: {found_indicators}")
            
            # Look for actual download links
            download_links = driver.find_elements(By.XPATH, "//a[contains(@href, '.step') or contains(@href, '.stp') or contains(text(), '3D') or contains(text(), 'STEP')]")
            
            if download_links:
                print(f"✅ Found {len(download_links)} potential download links!")
                for i, link in enumerate(download_links[:3]):
                    try:
                        text = link.text.strip()
                        href = link.get_attribute('href')
                        print(f"  Download {i+1}: '{text}' -> {href}")
                    except:
                        continue
                return True
            else:
                print("❌ No actual download links found")
                return False
        else:
            print("❌ No 3D model indicators found on product page")
            return False
        
    finally:
        input("Press Enter to close...")
        driver.quit()

if __name__ == "__main__":
    has_3d = check_lm358n_3d_model()
    if has_3d:
        print("\n✅ CONFIRMED: LM358N has 3D models available")
    else:
        print("\n❌ CONFIRMED: LM358N does NOT have 3D models available")
