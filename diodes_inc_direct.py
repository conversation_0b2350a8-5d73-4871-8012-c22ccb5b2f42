#!/usr/bin/env python3
"""
Direct search on Diodes Inc website for APX803L20-30SA-7
This worked yesterday - let's get the datasheet
"""

import requests
from bs4 import BeautifulSoup
import os

def search_diodes_inc(part_number):
    print(f"🔍 SEARCHING DIODES INC FOR {part_number}")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        # Go to Diodes Inc search page
        search_url = f"https://www.diodes.com/part/search/{part_number}/"
        
        print(f"   Searching: {search_url}")
        response = session.get(search_url, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the search results
            with open('diodes_inc_search.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"   📄 Saved search results")
            
            # Check if part found
            if part_number.upper() in response.text.upper():
                print(f"   ✅ Found {part_number} on Diodes Inc")
                
                # Look for datasheet link
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Look for datasheet links
                datasheet_links = []
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '').lower()
                    text = link.get_text(strip=True).lower()
                    
                    if 'datasheet' in href or 'datasheet' in text or '.pdf' in href:
                        datasheet_links.append({
                            'url': link.get('href'),
                            'text': link.get_text(strip=True)
                        })
                
                if datasheet_links:
                    print(f"   🎯 Found {len(datasheet_links)} datasheet links:")
                    for i, link in enumerate(datasheet_links, 1):
                        print(f"   {i}. {link['text']}")
                        print(f"      URL: {link['url']}")
                    
                    # Try to download the first datasheet
                    return download_datasheet(session, datasheet_links[0], part_number)
                else:
                    print(f"   ❌ No datasheet links found")
                    return False
            else:
                print(f"   ❌ {part_number} not found")
                return False
        else:
            print(f"   ❌ Search failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def download_datasheet(session, link, part_number):
    """Download the datasheet"""
    print(f"\n📥 Downloading datasheet...")
    
    try:
        url = link['url']
        
        # Make URL absolute if needed
        if not url.startswith('http'):
            url = f"https://www.diodes.com{url}"
        
        print(f"   URL: {url}")
        
        response = session.get(url, timeout=60, stream=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Create files-download directory
            os.makedirs('files-download', exist_ok=True)
            
            # Save datasheet
            filename = f"{part_number}_datasheet.pdf"
            filepath = os.path.join('files-download', filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 10000:  # At least 10KB
                print(f"   🎉 SUCCESS: Datasheet downloaded!")
                return True
            else:
                print(f"   ⚠️  File too small, might be error page")
                return False
        else:
            print(f"   ❌ Download failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Download error: {e}")
        return False

def main():
    part_number = "APX803L20-30SA-7"
    
    print("🚀 DIODES INC DIRECT SEARCH")
    print("This worked yesterday - getting datasheet")
    print("=" * 50)
    
    success = search_diodes_inc(part_number)
    
    print("\n" + "=" * 50)
    if success:
        print("✅ SUCCESS: Downloaded datasheet from Diodes Inc!")
        
        # Check what we got
        if os.path.exists('files-download'):
            files = [f for f in os.listdir('files-download') if f.endswith('.pdf')]
            if files:
                print(f"\n📄 DOWNLOADED FILES:")
                for file in files:
                    filepath = os.path.join('files-download', file)
                    size = os.path.getsize(filepath)
                    print(f"   ✅ {file} ({size:,} bytes)")
    else:
        print("❌ FAILED: Could not download datasheet")

if __name__ == "__main__":
    main()
