#!/usr/bin/env python3
"""
TEST ULTRALIBRARIAN AUTOMATION
==============================
Test version that shows exactly what's happening at each step.
"""

import os
import time
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def test_ultralibrarian():
    print("🔧 TEST ULTRALIBRARIAN AUTOMATION")
    print("=" * 40)
    
    # Setup Chrome
    print("Setting up Chrome driver...")
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Chrome driver created successfully")
    except Exception as e:
        print(f"❌ Chrome driver failed: {e}")
        return False
    
    try:
        # Test 1: Load homepage
        print("\n🔸 TEST 1: Loading UltraLibrarian homepage...")
        driver.get('https://www.ultralibrarian.com')
        time.sleep(3)
        
        title = driver.title
        url = driver.current_url
        print(f"   Page title: {title}")
        print(f"   Current URL: {url}")
        
        if "ultralibrarian" in url.lower():
            print("   ✅ Homepage loaded successfully")
        else:
            print("   ❌ Homepage failed to load")
            return False
        
        # Test 2: Find search box
        print("\n🔸 TEST 2: Looking for search box...")
        
        search_selectors = [
            "input[type='search']",
            "input[name*='search']", 
            "input[placeholder*='search']",
            "input[placeholder*='Search']",
            "input[type='text']"
        ]
        
        search_input = None
        for i, selector in enumerate(search_selectors):
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                print(f"   Trying selector {i+1}: {selector} - Found {len(elements)} elements")
                
                for j, element in enumerate(elements):
                    try:
                        if element.is_displayed():
                            search_input = element
                            print(f"   ✅ Found visible search box with selector: {selector}")
                            break
                    except:
                        continue
                
                if search_input:
                    break
                    
            except Exception as e:
                print(f"   Selector {i+1} failed: {e}")
                continue
        
        if not search_input:
            print("   ❌ No search box found!")
            return False
        
        # Test 3: Enter search term
        print("\n🔸 TEST 3: Entering search term...")
        search_term = "TI LM358N"
        
        try:
            search_input.clear()
            search_input.send_keys(search_term)
            print(f"   ✅ Entered: '{search_term}'")
        except Exception as e:
            print(f"   ❌ Failed to enter search term: {e}")
            return False
        
        # Test 4: Submit search
        print("\n🔸 TEST 4: Submitting search...")
        
        try:
            # Try submit button first
            submit_buttons = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
            if submit_buttons:
                submit_buttons[0].click()
                print("   ✅ Clicked submit button")
            else:
                # Press Enter
                search_input.send_keys(Keys.RETURN)
                print("   ✅ Pressed Enter")
            
            # Wait for results
            print("   Waiting 8 seconds for results...")
            time.sleep(8)
            
        except Exception as e:
            print(f"   ❌ Failed to submit search: {e}")
            return False
        
        # Test 5: Check results page
        print("\n🔸 TEST 5: Checking results page...")
        
        new_url = driver.current_url
        new_title = driver.title
        print(f"   New URL: {new_url}")
        print(f"   New title: {new_title}")
        
        # Look for any links that might be results
        all_links = driver.find_elements(By.TAG_NAME, "a")
        result_links = []
        
        for link in all_links:
            try:
                href = link.get_attribute('href')
                text = link.text.strip()
                
                if href and text and len(text) > 2:
                    if any(keyword in text.lower() for keyword in ['lm358', 'part', 'component']):
                        result_links.append((text[:50], href))
            except:
                continue
        
        print(f"   Found {len(result_links)} potential result links:")
        for i, (text, href) in enumerate(result_links[:5]):
            print(f"      {i+1}. {text}...")
        
        if result_links:
            print("   ✅ Found potential results")
        else:
            print("   ❌ No results found")
        
        # Keep browser open for manual inspection
        print(f"\n🔍 MANUAL INSPECTION:")
        print(f"   Browser is open for you to inspect")
        print(f"   Check if the search worked correctly")
        print(f"   Look for LM358N results")
        print(f"   Press Enter to close browser...")
        
        input("Press Enter to close: ")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    finally:
        driver.quit()
        print("🔚 Browser closed")

if __name__ == "__main__":
    success = test_ultralibrarian()
    if success:
        print("\n✅ TEST COMPLETED")
    else:
        print("\n❌ TEST FAILED")
