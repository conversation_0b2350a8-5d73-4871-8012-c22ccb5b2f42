#!/usr/bin/env python3
print('🧪 STEP BY STEP TEST')

import working_ultralibrarian_3d_finder

print('Creating finder...')
finder = working_ultralibrarian_3d_finder.ScreenAwareUltraLibrarian()

print('Setting up driver...')
driver = finder.setup_driver()

if not driver:
    print('❌ Driver setup failed')
    exit(1)

print('✅ Driver setup successful')

try:
    print('Testing screen_1_login_and_search...')
    result = finder.screen_1_login_and_search(driver, "TI", "LM358N")
    print(f'Screen 1 result: {result}')
    
except Exception as e:
    print(f'❌ Error in screen 1: {e}')
    import traceback
    traceback.print_exc()

finally:
    print('Closing driver...')
    driver.quit()
    print('✅ Test complete')
