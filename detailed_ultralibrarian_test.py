#!/usr/bin/env python3
"""
DETAILED ULTRALIBRARIAN TEST
============================
Run through all steps with detailed output, no pauses.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome with download preferences"""
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def detailed_test():
    """Run the complete test with detailed output"""
    print("🎯 DETAILED ULTRALIBRARIAN TEST")
    print("=" * 50)
    
    driver = setup_driver()
    
    try:
        # STEP 1-7: Run through the known working steps
        print("\n🔸 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print(f"✅ Page loaded: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        print("\n🔸 STEP 2: Finding search box...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_box = None
        for inp in inputs:
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower():
                        search_box = inp
                        print(f"✅ Found search box: '{placeholder}'")
                        break
            except:
                continue
        
        if not search_box:
            print("❌ No search box found!")
            return
        
        print("\n🔸 STEP 3: Searching for LM358N...")
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        time.sleep(10)
        print(f"✅ Search completed: {driver.current_url}")
        
        print("\n🔸 STEP 4: Finding Texas Instruments LM358N...")
        all_links = driver.find_elements(By.TAG_NAME, "a")
        ti_link = None
        for link in all_links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed() and link.is_enabled()):
                    ti_link = link
                    print(f"✅ Found TI LM358N: '{text}'")
                    break
            except:
                continue
        
        if not ti_link:
            print("❌ No Texas Instruments LM358N found!")
            return
        
        print("\n🔸 STEP 5: Clicking on TI LM358N...")
        driver.execute_script("arguments[0].scrollIntoView(true);", ti_link)
        time.sleep(2)
        ti_link.click()
        time.sleep(8)
        print(f"✅ Clicked on part: {driver.current_url}")
        
        print("\n🔸 STEP 6: Clicking first 'Download Now'...")
        download_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')] | //a[contains(text(), 'Download Now')]")
        if download_buttons:
            download_buttons[0].click()
            time.sleep(5)
            print("✅ Clicked first 'Download Now'")
        else:
            print("❌ No 'Download Now' button found!")
            return
        
        print("\n🔸 STEP 7: Clicking '3D CAD Model'...")
        model_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //a[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
        if model_buttons:
            model_buttons[0].click()
            time.sleep(5)
            print("✅ Clicked '3D CAD Model'")
            print(f"New URL: {driver.current_url}")
        else:
            print("❌ No '3D CAD Model' button found!")
            return
        
        # STEP 8: Detailed analysis of current page
        print("\n🔸 STEP 8: DETAILED PAGE ANALYSIS")
        print("=" * 40)
        
        # Check for new windows
        if len(driver.window_handles) > 1:
            print("✅ Multiple windows detected, switching to latest...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
            print(f"New window URL: {driver.current_url}")
        
        # Get all page elements
        buttons = driver.find_elements(By.TAG_NAME, "button")
        links = driver.find_elements(By.TAG_NAME, "a")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        divs = driver.find_elements(By.TAG_NAME, "div")
        
        print(f"\nPage elements: {len(buttons)} buttons, {len(links)} links, {len(inputs)} inputs, {len(divs)} divs")
        
        print(f"\n📋 ALL BUTTONS ({len(buttons)} total):")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                enabled = btn.is_enabled()
                if text:
                    print(f"  {i:2d}: '{text}' (visible={visible}, enabled={enabled})")
            except:
                continue
        
        print(f"\n🔗 ALL LINKS ({len(links)} total, showing first 20):")
        for i, link in enumerate(links[:20]):
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                visible = link.is_displayed()
                if text and len(text) < 80:
                    print(f"  {i:2d}: '{text}' -> {href[:60]}... (visible={visible})")
            except:
                continue
        
        print(f"\n📝 ALL INPUTS ({len(inputs)} total):")
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute('type') or 'text'
                placeholder = inp.get_attribute('placeholder') or ''
                name = inp.get_attribute('name') or ''
                value = inp.get_attribute('value') or ''
                visible = inp.is_displayed()
                enabled = inp.is_enabled()
                print(f"  {i:2d}: type='{input_type}', placeholder='{placeholder}', name='{name}', value='{value}' (visible={visible}, enabled={enabled})")
            except:
                continue
        
        print(f"\n🔍 SEARCHING FOR STEP-RELATED ELEMENTS:")
        step_elements = []
        
        # Check buttons for STEP
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip().lower()
                if 'step' in text and btn.is_displayed():
                    step_elements.append(('button', i, btn.text.strip(), btn))
                    print(f"  STEP Button {i}: '{btn.text.strip()}'")
            except:
                continue
        
        # Check links for STEP
        for i, link in enumerate(links):
            try:
                text = link.text.strip().lower()
                if 'step' in text and link.is_displayed():
                    step_elements.append(('link', i, link.text.strip(), link))
                    print(f"  STEP Link {i}: '{link.text.strip()}'")
            except:
                continue
        
        # Check divs for STEP (clickable ones)
        for i, div in enumerate(divs[:50]):  # Limit to first 50 divs
            try:
                text = div.text.strip().lower()
                if 'step' in text and div.is_displayed() and len(text) < 100:
                    onclick = div.get_attribute('onclick')
                    if onclick or 'click' in div.get_attribute('class').lower():
                        step_elements.append(('div', i, div.text.strip(), div))
                        print(f"  STEP Div {i}: '{div.text.strip()}'")
            except:
                continue
        
        print(f"\n🔍 SEARCHING FOR DOWNLOAD-RELATED ELEMENTS:")
        download_elements = []
        
        # Check for Download Now buttons
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip().lower()
                if 'download' in text and btn.is_displayed():
                    download_elements.append(('button', i, btn.text.strip(), btn))
                    print(f"  Download Button {i}: '{btn.text.strip()}'")
            except:
                continue
        
        # Check for Download links
        for i, link in enumerate(links):
            try:
                text = link.text.strip().lower()
                if 'download' in text and link.is_displayed():
                    download_elements.append(('link', i, link.text.strip(), link))
                    print(f"  Download Link {i}: '{link.text.strip()}'")
            except:
                continue
        
        print(f"\n🔍 SEARCHING FOR LOGIN-RELATED ELEMENTS:")
        login_elements = []
        
        # Check for email inputs
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute('type') or ''
                name = inp.get_attribute('name') or ''
                placeholder = inp.get_attribute('placeholder') or ''
                if (input_type.lower() == 'email' or 'email' in name.lower() or 'email' in placeholder.lower()) and inp.is_displayed():
                    login_elements.append(('email_input', i, f"type={input_type}, name={name}, placeholder={placeholder}", inp))
                    print(f"  Email Input {i}: type={input_type}, name={name}, placeholder={placeholder}")
            except:
                continue
        
        # Check for password inputs
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute('type') or ''
                if input_type.lower() == 'password' and inp.is_displayed():
                    login_elements.append(('password_input', i, f"type={input_type}", inp))
                    print(f"  Password Input {i}: type={input_type}")
            except:
                continue
        
        print(f"\n📊 SUMMARY:")
        print(f"  STEP elements found: {len(step_elements)}")
        print(f"  Download elements found: {len(download_elements)}")
        print(f"  Login elements found: {len(login_elements)}")
        
        print(f"\n🎯 NEXT STEPS ANALYSIS:")
        if step_elements:
            print("  ✅ STEP format selection available")
        if download_elements:
            print("  ✅ Download options available")
        if login_elements:
            print("  ✅ Login form detected")
        
        if not step_elements and not download_elements and not login_elements:
            print("  ⚠️ No obvious next steps found")
        
        print(f"\n🔍 DETAILED ANALYSIS COMPLETE")
        print("Browser will stay open for manual inspection...")
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    detailed_test()
