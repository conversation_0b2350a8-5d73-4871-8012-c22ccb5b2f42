#!/usr/bin/env python3
"""
Proper Component Finder - Uses Website Search Function

This follows the correct workflow:
1. Use the manufacturer's search function to find the part
2. Click on the search result to get to the specific part page  
3. Extract the direct 3D model link from that page
4. Download both datasheet and 3D model

Usage:
    python proper_component_finder.py "Diodes Inc" "APX803L20-30SA-7"
"""

import requests
from bs4 import BeautifulSoup
import re
import os
import json
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse, quote
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProperComponentFinder:
    def __init__(self, download_dir="files-download"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = self._create_session()
    
    def _create_session(self):
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive'
        })
        return session
    
    def search_diodes_website(self, part_number):
        """Use Diodes Inc search function to find the part"""
        logger.info(f"🔍 Searching Diodes website for: {part_number}")
        
        # Step 1: Get the main page to understand search functionality
        try:
            main_page = self.session.get("https://www.diodes.com", timeout=30)
            if main_page.status_code != 200:
                logger.error("Failed to access Diodes main page")
                return None, None
            
            # Step 2: Try different search approaches
            search_attempts = [
                # Try GET request with query parameter
                f"https://www.diodes.com/search?q={quote(part_number)}",
                f"https://www.diodes.com/products/search?q={quote(part_number)}",
                f"https://www.diodes.com/search/?query={quote(part_number)}",
                
                # Try POST request to search endpoint
                ("POST", "https://www.diodes.com/search", {"q": part_number}),
                ("POST", "https://www.diodes.com/products/search", {"query": part_number}),
            ]
            
            for attempt in search_attempts:
                try:
                    if isinstance(attempt, tuple):
                        method, url, data = attempt
                        logger.info(f"   Trying {method}: {url} with data: {data}")
                        response = self.session.post(url, data=data, timeout=30)
                    else:
                        url = attempt
                        logger.info(f"   Trying GET: {url}")
                        response = self.session.get(url, timeout=30)
                    
                    if response.status_code == 200 and len(response.text) > 1000:
                        # Check if we got search results
                        if part_number.lower() in response.text.lower():
                            logger.info(f"✅ Found search results!")
                            return self._extract_part_page_from_search(response.text, part_number)
                        
                except Exception as e:
                    logger.debug(f"Search attempt failed: {e}")
                    continue
            
            # Step 3: If search doesn't work, try direct product family page
            logger.info("🔍 Search didn't work, trying direct product family page...")
            return self._try_direct_product_page(part_number)
            
        except Exception as e:
            logger.error(f"Diodes search failed: {e}")
            return None, None
    
    def _extract_part_page_from_search(self, search_html, part_number):
        """Extract the specific part page URL from search results"""
        soup = BeautifulSoup(search_html, 'html.parser')

        # For Diodes search results, look for the part family link
        # The search shows APX803L as the main result for APX803L20-30SA-7
        base_part = part_number.split('-')[0]  # APX803L20-30SA-7 -> APX803L20
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part  # APX803L20 -> APX803L

        logger.info(f"   Looking for part family: {part_family}")

        # Look for links that contain the part family name
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().strip()

            # Check if this link is for our part family (APX803L)
            if (part_family.lower() in text.lower() or
                part_family.lower() in href.lower() or
                part_number.lower() in text.lower()):

                full_url = urljoin("https://www.diodes.com", href)
                logger.info(f"✅ Found part family page from search: {full_url}")
                logger.info(f"   Link text: {text}")

                # Get the specific part page
                try:
                    response = self.session.get(full_url, timeout=30)
                    if response.status_code == 200:
                        # Verify this page contains our specific part number
                        if part_number.lower() in response.text.lower():
                            logger.info(f"✅ Confirmed page contains part {part_number}")
                            return response.text, full_url
                        else:
                            logger.info(f"✅ Found family page for {part_family}")
                            return response.text, full_url

                except Exception as e:
                    logger.error(f"Failed to get part page: {e}")
                    continue

        logger.warning("Could not find part page link in search results")
        return None, None
    
    def _try_direct_product_page(self, part_number):
        """Try to access the product family page directly"""
        base_part = part_number.split('-')[0]
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
        
        url = f"https://www.diodes.com/part/view/{part_family}"
        logger.info(f"🔍 Trying direct product page: {url}")
        
        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                # Check if our specific part is on this page
                if part_number.lower() in response.text.lower():
                    logger.info(f"✅ Found part on family page: {url}")
                    return response.text, url
                else:
                    logger.warning(f"Part not found on family page")
            else:
                logger.error(f"Family page returned: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Direct page access failed: {e}")
        
        return None, None
    
    def extract_package_type_from_page(self, html, part_number):
        """Extract package type from the part page"""
        if not html:
            return None
        
        soup = BeautifulSoup(html, 'html.parser')
        
        # Look for the specific part number in a table row
        for row in soup.find_all('tr'):
            row_text = row.get_text()
            if part_number in row_text:
                # Look for package information in the same row
                cells = row.find_all(['td', 'th'])
                for cell in cells:
                    cell_text = cell.get_text().strip()
                    # Check if this cell contains a package type
                    common_packages = ['SOT23', 'SOT-23', 'SOT323', 'SOT-323', 'SOT25', 'SOT-25', 
                                     'SC59', 'SC-59', 'SC70', 'SC-70', 'SOIC', 'QFN', 'BGA', 'DIP']
                    
                    if cell_text in common_packages:
                        logger.info(f"📦 Found package type: {cell_text}")
                        return cell_text
        
        # Fallback: search in general content
        html_lower = html.lower()
        package_patterns = {
            'SOT23': ['sot-23', 'sot23'],
            'SOT323': ['sot-323', 'sot323'],
            'SOT25': ['sot-25', 'sot25'],
            'SC59': ['sc-59', 'sc59']
        }
        
        for pkg_name, patterns in package_patterns.items():
            for pattern in patterns:
                if pattern in html_lower:
                    logger.info(f"📦 Found package type (fallback): {pkg_name}")
                    return pkg_name
        
        return None
    
    def extract_3d_model_links_from_page(self, html, base_url, part_number):
        """Extract 3D model download links from the part page"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')
        model_links = []

        logger.info(f"🎯 Looking for 3D model links on part page...")

        # Strategy 1: Look for CAD Model column in the parts table
        # Find the table row for our specific part number
        target_row = None
        for row in soup.find_all('tr'):
            if part_number in row.get_text():
                target_row = row
                logger.info(f"✅ Found table row for {part_number}")
                break

        if target_row:
            # Look for CAD model links in this specific row
            for link in target_row.find_all('a', href=True):
                href = link['href']
                text = link.get_text().strip()

                # Check if this is a CAD model link
                if (any(ext in href.lower() for ext in ['.step', '.stp']) or
                    any(keyword in text.lower() for keyword in ['cad', '3d', 'model', 'step'])):

                    full_url = urljoin(base_url, href)
                    logger.info(f"✅ Found CAD model link in part row: {full_url}")
                    model_links.append(full_url)

            # Also check for any icons or buttons in the CAD Model column
            for element in target_row.find_all(['img', 'button', 'span']):
                parent = element.parent
                if parent and parent.name == 'a' and parent.get('href'):
                    href = parent['href']
                    if any(ext in href.lower() for ext in ['.step', '.stp']):
                        full_url = urljoin(base_url, href)
                        logger.info(f"✅ Found CAD model via icon/button: {full_url}")
                        model_links.append(full_url)

        # Strategy 2: Look for general CAD/3D model links on the page
        cad_keywords = ['cad model', '3d model', 'step model', 'download cad', 'download 3d']

        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().lower()

            # Direct STEP/STP file links
            if any(ext in href.lower() for ext in ['.step', '.stp']):
                full_url = urljoin(base_url, href)
                logger.info(f"✅ Found direct 3D file link: {full_url}")
                model_links.append(full_url)

            # Links with CAD-related text
            elif any(keyword in text for keyword in cad_keywords):
                full_url = urljoin(base_url, href)
                logger.info(f"🔍 Found potential CAD link: {full_url}")

                # Follow the link to see if it leads to a 3D model
                try:
                    response = self.session.get(full_url, timeout=15)
                    if response.status_code == 200:
                        # Check if this page has direct 3D model downloads
                        cad_soup = BeautifulSoup(response.text, 'html.parser')
                        for cad_link in cad_soup.find_all('a', href=True):
                            cad_href = cad_link['href']
                            if any(ext in cad_href.lower() for ext in ['.step', '.stp']):
                                cad_full_url = urljoin(full_url, cad_href)
                                logger.info(f"✅ Found 3D model on CAD page: {cad_full_url}")
                                model_links.append(cad_full_url)

                except Exception as e:
                    logger.debug(f"Failed to check CAD page {full_url}: {e}")

        # Strategy 3: Look for JavaScript data or hidden attributes
        # Sometimes 3D model URLs are in data attributes or script tags
        for script in soup.find_all('script'):
            script_text = script.get_text()
            if script_text:
                # Look for STEP/STP URLs in JavaScript
                step_matches = re.findall(r'["\']([^"\']*\.(?:step|stp)[^"\']*)["\']', script_text, re.IGNORECASE)
                for match in step_matches:
                    full_url = urljoin(base_url, match)
                    logger.info(f"✅ Found 3D model in JavaScript: {full_url}")
                    model_links.append(full_url)

        return list(set(model_links))  # Remove duplicates
    
    def _get_3d_links_from_cad_page(self, cad_page_url):
        """Get 3D model links from a dedicated CAD page"""
        try:
            logger.info(f"   Checking CAD page: {cad_page_url}")
            response = self.session.get(cad_page_url, timeout=20)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                links = []
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    if any(ext in href.lower() for ext in ['.step', '.stp']):
                        full_url = urljoin(cad_page_url, href)
                        logger.info(f"✅ Found 3D model on CAD page: {full_url}")
                        links.append(full_url)
                
                return links
                
        except Exception as e:
            logger.error(f"CAD page access failed: {e}")
        
        return []
    
    def download_file(self, url, filename=None):
        """Download file"""
        try:
            logger.info(f"📥 Downloading: {url}")
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            if not filename:
                filename = os.path.basename(urlparse(url).path)
                if not filename or '.' not in filename:
                    if '.pdf' in url.lower():
                        filename = f"datasheet_{int(time.time())}.pdf"
                    elif any(ext in url.lower() for ext in ['.step', '.stp']):
                        filename = f"3d_model_{int(time.time())}.step"
            
            filepath = self.download_dir / filename
            
            # Handle existing files
            counter = 1
            original_filepath = filepath
            while filepath.exists():
                name, ext = original_filepath.stem, original_filepath.suffix
                filepath = self.download_dir / f"{name}_{counter}{ext}"
                counter += 1
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            logger.info(f"✅ Downloaded: {filepath} ({file_size:,} bytes)")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            return None
    
    def find_component_proper_way(self, manufacturer, part_number):
        """Find component using proper search workflow"""
        results = {
            'manufacturer': manufacturer,
            'part_number': part_number,
            'package_type': None,
            'datasheet_file': None,
            '3d_model_files': [],
            'part_page_url': None,
            'success': False
        }
        
        logger.info(f"🔍 Searching for {manufacturer} {part_number} using proper workflow")
        
        # Step 1: Use website search to find the part
        if manufacturer.lower() == "diodes inc":
            part_html, part_page_url = self.search_diodes_website(part_number)
        else:
            logger.error(f"❌ No search implementation for {manufacturer}")
            return results
        
        if not part_html:
            logger.error("❌ Could not find part page")
            return results
        
        results['part_page_url'] = part_page_url
        logger.info(f"✅ Found part page: {part_page_url}")
        
        # Step 2: Extract package type from the part page
        package_type = self.extract_package_type_from_page(part_html, part_number)
        results['package_type'] = package_type
        
        # Step 3: Extract datasheet link from the part page
        datasheet_url = self._extract_datasheet_link_from_page(part_html, part_page_url)
        if datasheet_url:
            datasheet_file = self.download_file(datasheet_url, f"{part_number}_datasheet.pdf")
            results['datasheet_file'] = datasheet_file
        
        # Step 4: Extract 3D model links from the part page
        model_urls = self.extract_3d_model_links_from_page(part_html, part_page_url, part_number)
        
        for i, url in enumerate(model_urls):
            model_file = self.download_file(url, f"{part_number}_{package_type or 'unknown'}_{i+1}.step")
            if model_file:
                results['3d_model_files'].append(model_file)
        
        results['success'] = bool(results['datasheet_file'] or results['3d_model_files'])
        return results
    
    def _extract_datasheet_link_from_page(self, html, base_url):
        """Extract datasheet download link from part page"""
        if not html:
            return None
        
        soup = BeautifulSoup(html, 'html.parser')
        
        # Look for datasheet links
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().lower()
            
            if (href.endswith('.pdf') and 
                any(keyword in text for keyword in ['datasheet', 'data sheet'])):
                full_url = urljoin(base_url, href)
                logger.info(f"✅ Found datasheet link on page: {full_url}")
                return full_url
        
        # Also try regex patterns
        datasheet_patterns = [
            r'href="([^"]*\.pdf[^"]*)"[^>]*>.*?datasheet',
            r'href="([^"]*datasheet[^"]*\.pdf)"'
        ]
        
        for pattern in datasheet_patterns:
            matches = re.finditer(pattern, html, re.IGNORECASE)
            for match in matches:
                link = match.group(1)
                full_url = urljoin(base_url, link)
                logger.info(f"✅ Found datasheet via pattern: {full_url}")
                return full_url
        
        return None

def test_proper_workflow():
    """Test the proper workflow"""
    finder = ProperComponentFinder()
    
    manufacturer = "Diodes Inc"
    part_number = "APX803L20-30SA-7"
    
    print(f"🚀 Testing proper workflow")
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print("=" * 60)
    
    results = finder.find_component_proper_way(manufacturer, part_number)
    
    print("\n" + "="*60)
    print("PROPER WORKFLOW RESULTS")
    print("="*60)
    print(json.dumps(results, indent=2))
    
    return results

def main():
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description='Proper Component Finder')
    parser.add_argument('manufacturer', help='Manufacturer name')
    parser.add_argument('part_number', help='Part number')
    
    args = parser.parse_args()
    
    finder = ProperComponentFinder()
    results = finder.find_component_proper_way(args.manufacturer, args.part_number)
    
    print("\n" + "="*60)
    print("COMPONENT FINDER RESULTS")
    print("="*60)
    print(f"Manufacturer: {results['manufacturer']}")
    print(f"Part Number: {results['part_number']}")
    print(f"Package Type: {results['package_type'] or 'Unknown'}")
    print(f"Part Page: {results['part_page_url'] or 'Not found'}")
    print()
    
    if results['datasheet_file']:
        print(f"✅ Datasheet: {results['datasheet_file']}")
    else:
        print("❌ Datasheet: Not found")
    
    if results['3d_model_files']:
        print(f"✅ 3D Models: {len(results['3d_model_files'])} found")
        for model in results['3d_model_files']:
            print(f"  - {model}")
    else:
        print("❌ 3D Models: Not found")
    
    if results['success']:
        print("\n🎉 Search completed successfully!")
    else:
        print("\n⚠️ Search completed with limited results")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        test_proper_workflow()
    else:
        main()
