#!/usr/bin/env python3
"""
FINAL ULTRALIBRARIAN AUTOMATION
===============================
TESTED AND WORKING automation for UltraLibrarian 6-screen process.
"""

import os
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from webdriver_manager.chrome import ChromeDriverManager

class FinalUltraLibrarianAutomation:
    def __init__(self):
        self.base_url = 'https://www.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("🚀 FINAL ULTRALIBRARIAN AUTOMATION", flush=True)

    def setup_driver(self):
        """Setup Chrome driver - TESTED AND WORKING"""
        print("Setting up Chrome driver...", flush=True)
        
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3D'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✅ Chrome driver ready", flush=True)
            return driver
        except Exception as e:
            print(f"❌ Chrome setup failed: {e}", flush=True)
            return None

    def screen_1_search(self, driver, manufacturer, part_number):
        """SCREEN 1: Enter part number and search - TESTED"""
        print(f"\n🔸 SCREEN 1: Search for {manufacturer} {part_number}", flush=True)
        
        try:
            driver.get(self.base_url)
            time.sleep(5)
            print(f"   ✅ Loaded: {driver.title}", flush=True)
            
            # Find search box
            search_selectors = [
                "input[type='search']",
                "input[name*='search']", 
                "input[placeholder*='search']",
                "input[placeholder*='Search']",
                "input[type='text']"
            ]
            
            search_input = None
            for selector in search_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            search_input = element
                            print(f"   ✅ Found search box", flush=True)
                            break
                    if search_input:
                        break
                except:
                    continue
            
            if not search_input:
                print(f"   ❌ No search box found", flush=True)
                return False
            
            # Enter and submit search
            search_term = f"{manufacturer} {part_number}"
            search_input.clear()
            search_input.send_keys(search_term)
            search_input.send_keys(Keys.RETURN)
            
            print(f"   ✅ Searched for: {search_term}", flush=True)
            time.sleep(8)
            
            return True
            
        except Exception as e:
            print(f"   ❌ Screen 1 failed: {e}", flush=True)
            return False

    def screen_2_select_part(self, driver, part_number):
        """SCREEN 2: Select specific part from results"""
        print(f"\n🔸 SCREEN 2: Select part from results", flush=True)
        
        try:
            # Look for part results
            result_xpaths = [
                f"//a[contains(text(), '{part_number}')]",
                f"//a[contains(translate(text(), 'abcdefghijklmnopqrstuvwxyz', 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'), '{part_number.upper()}')]",
                "//a[contains(@href, 'part')]",
                "//a[contains(@href, 'component')]",
                "//tr//a"
            ]
            
            for xpath in result_xpaths:
                try:
                    elements = driver.find_elements(By.XPATH, xpath)
                    for element in elements:
                        text = element.text.strip()
                        if part_number.upper() in text.upper() and len(text) > 2:
                            print(f"   Clicking: {text[:50]}...", flush=True)
                            driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(2)
                            element.click()
                            time.sleep(5)
                            print(f"   ✅ Selected part result", flush=True)
                            return True
                except:
                    continue
            
            print(f"   ❌ No matching part found", flush=True)
            return False
            
        except Exception as e:
            print(f"   ❌ Screen 2 failed: {e}", flush=True)
            return False

    def screen_3_find_variant(self, driver, part_number):
        """SCREEN 3: Find the right variant"""
        print(f"\n🔸 SCREEN 3: Find {part_number} variant", flush=True)
        
        try:
            # Look for specific variant
            variant_xpaths = [
                f"//a[contains(text(), '{part_number}')]",
                f"//tr[contains(., '{part_number}')]//a",
                f"//td[contains(text(), '{part_number}')]/parent::tr//a"
            ]
            
            for xpath in variant_xpaths:
                try:
                    elements = driver.find_elements(By.XPATH, xpath)
                    for element in elements:
                        text = element.text.strip()
                        if part_number.upper() in text.upper():
                            print(f"   Found variant: {text[:50]}...", flush=True)
                            driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(2)
                            element.click()
                            time.sleep(5)
                            print(f"   ✅ Selected variant", flush=True)
                            return True
                except:
                    continue
            
            print(f"   ❌ No variant found", flush=True)
            return False
            
        except Exception as e:
            print(f"   ❌ Screen 3 failed: {e}", flush=True)
            return False

    def screen_4_download_now(self, driver):
        """SCREEN 4: Hit 'Download Now'"""
        print(f"\n🔸 SCREEN 4: Hit 'Download Now'", flush=True)
        
        try:
            download_xpaths = [
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download now')]",
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download now')]",
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download')]",
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download')]"
            ]
            
            for xpath in download_xpaths:
                try:
                    elements = driver.find_elements(By.XPATH, xpath)
                    if elements:
                        element = elements[0]
                        text = element.text.strip()
                        print(f"   Found: {text}", flush=True)
                        driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        time.sleep(2)
                        element.click()
                        time.sleep(5)
                        print(f"   ✅ Clicked 'Download Now'", flush=True)
                        return True
                except:
                    continue
            
            print(f"   ❌ No 'Download Now' button found", flush=True)
            return False
            
        except Exception as e:
            print(f"   ❌ Screen 4 failed: {e}", flush=True)
            return False

    def screen_5_3d_model(self, driver):
        """SCREEN 5: Hit '3D Model'"""
        print(f"\n🔸 SCREEN 5: Hit '3D Model'", flush=True)
        
        try:
            model_xpaths = [
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d model')]",
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d model')]",
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')]",
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')]"
            ]
            
            for xpath in model_xpaths:
                try:
                    elements = driver.find_elements(By.XPATH, xpath)
                    if elements:
                        element = elements[0]
                        text = element.text.strip()
                        print(f"   Found: {text}", flush=True)
                        driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        time.sleep(2)
                        element.click()
                        time.sleep(5)
                        print(f"   ✅ Clicked '3D Model'", flush=True)
                        return True
                except:
                    continue
            
            print(f"   ❌ No '3D Model' option found", flush=True)
            return False
            
        except Exception as e:
            print(f"   ❌ Screen 5 failed: {e}", flush=True)
            return False

    def screen_6_login_download(self, driver, manufacturer, part_number):
        """SCREEN 6: Login and download"""
        print(f"\n🔸 SCREEN 6: Login required", flush=True)
        print(f"   🔐 Please login with your existing account", flush=True)
        print(f"   ⏳ After login, download should start automatically", flush=True)
        print(f"   📁 Files will be saved to: 3D/", flush=True)
        
        input("   Press Enter after login and download complete: ")
        
        # Check for new files
        try:
            files = os.listdir('3D')
            step_files = [f for f in files if any(ext in f.lower() for ext in ['.step', '.stp', '.zip'])]
            
            if step_files:
                latest_file = max(step_files, key=lambda f: os.path.getctime(os.path.join('3D', f)))
                print(f"   ✅ Downloaded: {latest_file}", flush=True)
                
                # Create log
                self.create_log(latest_file, manufacturer, part_number)
                return latest_file
            else:
                print(f"   ❌ No STEP files found", flush=True)
                return None
                
        except Exception as e:
            print(f"   ❌ File check failed: {e}", flush=True)
            return None

    def create_log(self, filename, manufacturer, part_number):
        """Create success log"""
        log_name = filename.replace('.step', '.txt').replace('.stp', '.txt').replace('.zip', '.txt')
        log_path = os.path.join('3D', log_name)
        
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(f"ULTRALIBRARIAN AUTOMATION SUCCESS LOG\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Part: {manufacturer} {part_number}\n")
            f.write(f"File: {filename}\n")
            f.write(f"Source: UltraLibrarian (6-Screen Automation)\n")
            f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("All 6 screens completed successfully! ✅\n")

    def run_automation(self, manufacturer, part_number):
        """Run complete automation"""
        print(f"\n🎯 RUNNING COMPLETE AUTOMATION", flush=True)
        print(f"Target: {manufacturer} {part_number}", flush=True)
        print("=" * 50, flush=True)
        
        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            # Execute all screens
            if not self.screen_1_search(driver, manufacturer, part_number):
                return None
            
            if not self.screen_2_select_part(driver, part_number):
                return None
            
            if not self.screen_3_find_variant(driver, part_number):
                return None
            
            if not self.screen_4_download_now(driver):
                return None
            
            if not self.screen_5_3d_model(driver):
                return None
            
            result = self.screen_6_login_download(driver, manufacturer, part_number)
            
            if result:
                print(f"\n🎉 AUTOMATION SUCCESSFUL!", flush=True)
                print(f"Downloaded: {result}", flush=True)
                return result
            else:
                print(f"\n❌ Download failed", flush=True)
                return None
            
        except Exception as e:
            print(f"❌ Automation failed: {e}", flush=True)
            return None
        finally:
            driver.quit()

def main():
    if len(sys.argv) < 3:
        print("Usage: python final_ultralibrarian_automation.py \"Manufacturer\" \"Part Number\"")
        print("\nExample: python final_ultralibrarian_automation.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = sys.argv[1]
    part_number = sys.argv[2]
    
    automation = FinalUltraLibrarianAutomation()
    result = automation.run_automation(manufacturer, part_number)
    
    if result:
        print(f"\n✅ SUCCESS: {result}")
    else:
        print(f"\n❌ FAILED")

if __name__ == "__main__":
    main()
