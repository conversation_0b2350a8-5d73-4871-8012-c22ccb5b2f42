#!/usr/bin/env python3
"""
Test using existing search results to find Texas Instruments LM358N
"""

from digikey_searcher import <PERSON><PERSON><PERSON><PERSON><PERSON>cher

def test_existing_search():
    print("🧪 TESTING WITH EXISTING SEARCH RESULTS")
    print("=" * 50)

    searcher = DigikeySearcher()

    # Read the fresh search results (this should be the category page with exact matches)
    with open('digikey_fresh_search_LM358N.html', 'r', encoding='utf-8') as f:
        html_content = f.read()

    print("📄 Using existing search results...")
    print(f"   Checking if exactMatch is in content: {'exactMatch' in html_content}")
    print(f"   Checking if datasheetUrl is in content: {'datasheetUrl' in html_content}")

    # Force it to use exact matches by calling the method directly
    if '"exactMatch":' in html_content:
        print("   🎯 Using exact matches logic...")
        part_info = searcher.handle_exact_matches(html_content, "LM358N", "Texas Instruments")
    else:
        print("   ⚠️  No exact matches found, using regular extraction...")
        part_info = searcher.extract_part_info(html_content, "LM358N", "Texas Instruments")
    
    if part_info:
        print(f"✅ SUCCESS: Found part info!")
        print(f"   Part: {part_info['part_number']}")
        print(f"   Manufacturer: {part_info['manufacturer']}")
        print(f"   Datasheet: {part_info['datasheet_url']}")
        
        # Try to download
        success = searcher.download_datasheet(part_info)
        if success:
            print(f"🎉 Downloaded Texas Instruments datasheet!")
        else:
            print(f"❌ Download failed")
    else:
        print(f"❌ Could not extract part info")

if __name__ == "__main__":
    test_existing_search()
