#!/usr/bin/env python3
"""
IMPROVED SAMACSYS 3D MODEL FINDER
================================
Enhanced version with proper error handling, verification, and detailed output.

Features:
- Detailed step-by-step output
- Error handling with immediate stop
- File verification (size, format, content)
- Success/failure reporting
- Proper logging

Usage:
    python samacsys_3d_finder_improved.py "Texas Instruments" "LM358N"
"""

import os
import sys
import time
import shutil
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import argparse

class SamacSys3DFinder:
    def __init__(self):
        self.driver = None
        self.session = requests.Session()
        self.models_3d_dir = "3d"
        self.downloads_dir = os.path.expanduser("~/Downloads")
        
    def log(self, message, level="INFO"):
        """Enhanced logging with timestamps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if level == "ERROR":
            print(f"❌ [{timestamp}] ERROR: {message}")
        elif level == "SUCCESS":
            print(f"✅ [{timestamp}] SUCCESS: {message}")
        elif level == "WARNING":
            print(f"⚠️ [{timestamp}] WARNING: {message}")
        else:
            print(f"ℹ️ [{timestamp}] INFO: {message}")
    
    def setup_browser(self):
        """Setup Chrome browser with proper options"""
        self.log("Setting up Chrome browser...")
        
        try:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.log("Browser setup successful", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Browser setup failed: {e}", "ERROR")
            return False
    
    def setup_directories(self):
        """Create necessary directories"""
        self.log("Setting up directories...")
        
        try:
            os.makedirs(self.models_3d_dir, exist_ok=True)
            self.log(f"Directory '{self.models_3d_dir}' ready", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Error creating directory: {e}", "ERROR")
            return False
    
    def handle_cookie_popup(self):
        """Handle cookie consent popup"""
        self.log("Checking for cookie popup...")
        
        try:
            cookie_texts = ["Accept", "Accept All", "OK", "Got it", "I Agree", "Continue", "Allow"]
            
            for text in cookie_texts:
                try:
                    elements = self.driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')] | //a[contains(text(), '{text}')]")
                    for element in elements:
                        if element.is_displayed():
                            self.log(f"Clicking cookie button: '{text}'")
                            element.click()
                            time.sleep(2)
                            return True
                except:
                    continue
            
            self.log("No cookie popup found")
            return True
            
        except Exception as e:
            self.log(f"Error handling cookie popup: {e}", "WARNING")
            return True  # Continue even if cookie handling fails
    
    def navigate_to_samacsys(self):
        """Navigate to SamacSys website"""
        self.log("Navigating to SamacSys...")
        
        try:
            self.driver.get("https://www.samacsys.com/")
            time.sleep(5)
            
            # Handle cookie popup
            self.handle_cookie_popup()
            
            self.log("Successfully loaded SamacSys", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error navigating to SamacSys: {e}", "ERROR")
            return False
    
    def search_part(self, manufacturer, part_number):
        """Search for the part on SamacSys"""
        self.log(f"Searching for part: {manufacturer} {part_number}")
        
        try:
            # Find search box
            search_selectors = [
                "input[type='search']",
                "input[placeholder*='search' i]",
                "input[name='q']",
                "#search",
                ".search-input",
                "input[placeholder*='part' i]"
            ]
            
            search_box = None
            for selector in search_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            search_box = elem
                            break
                    if search_box:
                        break
                except:
                    continue
            
            if not search_box:
                self.log("Search box not found", "ERROR")
                return False
            
            self.log("Found search box")
            
            # Enter search term
            search_term = part_number  # SamacSys works better with just part number
            search_box.clear()
            search_box.send_keys(search_term)
            search_box.send_keys(Keys.RETURN)
            time.sleep(8)
            
            self.log(f"Search completed for: {search_term}")
            self.log(f"Current URL: {self.driver.current_url}")
            
            # Check if part was found
            if part_number.upper() not in self.driver.page_source.upper():
                self.log("Part not found in search results", "ERROR")
                return False
            
            self.log("Part found in search results", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error during search: {e}", "ERROR")
            return False
    
    def find_part_link(self, part_number):
        """Find and click the part link"""
        self.log("Looking for part link...")
        
        try:
            # Look for part links in search results
            part_selectors = [
                f"//a[contains(text(), '{part_number}')]",
                f"//a[contains(@href, '{part_number}')]",
                "//a[contains(@class, 'part-link')]",
                "//a[contains(@class, 'result-link')]",
                "//a[contains(@class, 'component')]"
            ]
            
            for selector in part_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''
                            
                            if part_number.upper() in text.upper() or part_number.lower() in href.lower():
                                self.log(f"Clicking part link: '{text}'")
                                element.click()
                                time.sleep(8)
                                return True
                except:
                    continue
            
            self.log("No clickable part link found", "ERROR")
            return False
            
        except Exception as e:
            self.log(f"Error finding part link: {e}", "ERROR")
            return False
    
    def find_3d_download(self, manufacturer, part_number):
        """Find and download 3D model"""
        self.log("Looking for 3D model download...")
        
        try:
            # Look for 3D model download elements
            download_selectors = [
                "//a[contains(text(), '3D Model')]",
                "//a[contains(text(), 'STEP')]",
                "//a[contains(text(), 'Download')]",
                "//button[contains(text(), '3D Model')]",
                "//button[contains(text(), 'STEP')]",
                "//button[contains(text(), 'Download')]",
                "//a[contains(@href, '.step')]",
                "//a[contains(@href, '.stp')]",
                ".download-3d",
                ".step-download",
                ".cad-download",
                "//a[contains(text(), 'CAD')]",
                "//button[contains(text(), 'CAD')]"
            ]
            
            download_element = None
            for selector in download_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''
                            
                            if any(word in text.lower() for word in ['3d', 'step', 'model', 'download', 'cad']) or \
                               any(ext in href.lower() for ext in ['.step', '.stp']):
                                download_element = element
                                self.log(f"Found download element: '{text}' -> {href[:50]}...")
                                break
                    if download_element:
                        break
                except:
                    continue
            
            if not download_element:
                self.log("No 3D download element found", "ERROR")
                return False
            
            # Get file count before download
            before_files = set(os.listdir(self.downloads_dir))
            
            # Click download
            self.log("Clicking download...")
            download_element.click()
            time.sleep(15)  # SamacSys can be slow
            
            # Check for new files
            after_files = set(os.listdir(self.downloads_dir))
            new_files = after_files - before_files
            
            step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
            
            if not step_files:
                self.log("No STEP files downloaded", "ERROR")
                return False
            
            # Move and verify the file
            downloaded_file = os.path.join(self.downloads_dir, step_files[0])
            target_filename = f"samacsys_{manufacturer.replace(' ', '_')}_{part_number.replace('/', '_')}.step"
            target_path = os.path.join(self.models_3d_dir, target_filename)
            
            return self.verify_and_move_file(downloaded_file, target_path, target_filename)
            
        except Exception as e:
            self.log(f"Error downloading 3D model: {e}", "ERROR")
            return False
    
    def verify_and_move_file(self, source_path, target_path, filename):
        """Verify and move the downloaded file"""
        self.log("Verifying downloaded file...")
        
        try:
            # Check if source file exists
            if not os.path.exists(source_path):
                self.log("Downloaded file does not exist", "ERROR")
                return False
            
            # Check file size
            file_size = os.path.getsize(source_path)
            if file_size == 0:
                self.log("Downloaded file is empty", "ERROR")
                return False
            elif file_size < 100:  # Less than 100 bytes
                self.log(f"Downloaded file is very small ({file_size} bytes) - may be invalid", "WARNING")
            
            # Check if it's a STEP file (basic check)
            try:
                with open(source_path, 'r', encoding='utf-8', errors='ignore') as f:
                    first_line = f.readline().strip()
                    if 'STEP' not in first_line.upper():
                        self.log("Downloaded file may not be a valid STEP file", "WARNING")
            except:
                self.log("Could not verify STEP file format", "WARNING")
            
            # Move file to target location
            shutil.move(source_path, target_path)
            
            self.log(f"File verified and moved: {filename} ({file_size:,} bytes)", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error verifying/moving file: {e}", "ERROR")
            return False
    
    def find_3d_model(self, manufacturer, part_number):
        """Main method to find and download 3D model"""
        self.log(f"Starting 3D model search for: {manufacturer} {part_number}")
        self.log("=" * 60)
        
        try:
            # Step 1: Setup browser
            if not self.setup_browser():
                return False
            
            # Step 2: Setup directories
            if not self.setup_directories():
                return False
            
            # Step 3: Navigate to SamacSys
            if not self.navigate_to_samacsys():
                return False
            
            # Step 4: Search for part
            if not self.search_part(manufacturer, part_number):
                return False
            
            # Step 5: Find and click part link
            if not self.find_part_link(part_number):
                return False
            
            # Step 6: Download 3D model
            if not self.find_3d_download(manufacturer, part_number):
                return False
            
            self.log("3D model download completed successfully!", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Unexpected error: {e}", "ERROR")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                self.log("Browser closed")

def main():
    parser = argparse.ArgumentParser(description='Download 3D model from SamacSys')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Texas Instruments")')
    parser.add_argument('part_number', help='Part number (e.g., "LM358N")')
    
    args = parser.parse_args()
    
    finder = SamacSys3DFinder()
    success = finder.find_3d_model(args.manufacturer, args.part_number)
    
    if success:
        print(f"\n🎉 SUCCESS: 3D model downloaded for {args.manufacturer} {args.part_number}")
        sys.exit(0)
    else:
        print(f"\n💥 FAILED: Could not download 3D model for {args.manufacturer} {args.part_number}")
        sys.exit(1)

if __name__ == "__main__":
    main()
