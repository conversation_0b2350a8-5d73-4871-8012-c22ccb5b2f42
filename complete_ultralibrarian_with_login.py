#!/usr/bin/env python3
"""
COMPLETE ULTRALIBRARIAN WITH LOGIN
==================================
Complete workflow: Search -> Part -> Download Now -> 3D CAD -> STEP -> Login -> Download
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome with download preferences"""
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def complete_ultralibrarian_with_login():
    """Complete UltraLibrarian workflow with login"""
    print("🎯 COMPLETE ULTRALIBRARIAN WITH LOGIN")
    print("=" * 60)
    
    driver = setup_driver()
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # STEP 1: Load UltraLibrarian
        print("\n🔸 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print(f"✅ Loaded: {driver.title}")
        
        # STEP 2: Search for LM358N
        print("\n🔸 STEP 2: Searching for LM358N...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_box = None
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                search_box = inp
                break
        
        if not search_box:
            print("❌ No search box found!")
            return None
        
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        print("✅ Search submitted")
        time.sleep(10)
        
        # STEP 3: Click Texas Instruments LM358N
        print("\n🔸 STEP 3: Clicking Texas Instruments LM358N...")
        links = driver.find_elements(By.TAG_NAME, "a")
        ti_link = None
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed()):
                    ti_link = link
                    break
            except:
                continue
        
        if not ti_link:
            print("❌ No Texas Instruments LM358N found!")
            return None
        
        ti_link.click()
        print("✅ Clicked Texas Instruments LM358N")
        time.sleep(8)
        
        # STEP 4: Click Download Now
        print("\n🔸 STEP 4: Clicking Download Now...")
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if not download_btns:
            print("❌ No Download Now button found!")
            return None
        
        driver.execute_script("arguments[0].click();", download_btns[0])
        print("✅ Clicked Download Now")
        time.sleep(5)
        
        # STEP 5: Click 3D CAD Model
        print("\n🔸 STEP 5: Clicking 3D CAD Model...")
        cad_selectors = [
            "//button[contains(text(), '3D CAD Model')]",
            "//a[contains(text(), '3D CAD Model')]",
            "//button[contains(text(), '3D Model')]"
        ]
        
        cad_element = None
        for selector in cad_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    cad_element = elem
                    break
            if cad_element:
                break
        
        if not cad_element:
            print("❌ No 3D CAD Model option found!")
            return None
        
        driver.execute_script("arguments[0].click();", cad_element)
        print("✅ Clicked 3D CAD Model")
        time.sleep(5)
        
        # Check for new window
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        # STEP 6: Select STEP format
        print("\n🔸 STEP 6: Selecting STEP format...")
        
        # Try multiple approaches to find STEP selection
        step_element = None
        
        # Method 1: Look for STEP buttons/links
        step_selectors = [
            "//button[contains(text(), 'STEP')]",
            "//a[contains(text(), 'STEP')]",
            "//input[@value='STEP']",
            "//option[contains(text(), 'STEP')]",
            "//div[contains(text(), 'STEP') and (@onclick or @click)]",
            "//span[contains(text(), 'STEP') and parent::*[@onclick]]",
            "//label[contains(text(), 'STEP')]"
        ]
        
        for selector in step_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                try:
                    if elem.is_displayed():
                        step_element = elem
                        print(f"✅ Found STEP option: '{elem.text or elem.get_attribute('value')}'")
                        break
                except:
                    continue
            if step_element:
                break
        
        # Method 2: Look for radio buttons or checkboxes with STEP
        if not step_element:
            radio_inputs = driver.find_elements(By.XPATH, "//input[@type='radio']")
            for radio in radio_inputs:
                try:
                    if radio.is_displayed():
                        # Check if parent or sibling contains STEP
                        parent = radio.find_element(By.XPATH, "..")
                        if 'step' in parent.text.lower():
                            step_element = radio
                            print(f"✅ Found STEP radio button")
                            break
                except:
                    continue
        
        if not step_element:
            print("❌ No STEP format option found!")
            # Show what's available for debugging
            print("Available elements with 'step' text:")
            all_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP') or contains(text(), 'step')]")
            for elem in all_elements[:10]:
                try:
                    if elem.is_displayed():
                        print(f"  {elem.tag_name}: '{elem.text}'")
                except:
                    continue
            return None
        
        # Click STEP selection
        driver.execute_script("arguments[0].click();", step_element)
        print("✅ Selected STEP format")
        time.sleep(3)
        
        # STEP 7: Look for download button after STEP selection
        print("\n🔸 STEP 7: Looking for download button...")
        
        download_selectors = [
            "//button[contains(text(), 'Download')]",
            "//a[contains(text(), 'Download')]",
            "//input[@type='submit' and contains(@value, 'Download')]",
            "//button[@type='submit']"
        ]
        
        download_element = None
        for selector in download_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                try:
                    if elem.is_displayed() and elem.is_enabled():
                        download_element = elem
                        print(f"✅ Found download button: '{elem.text or elem.get_attribute('value')}'")
                        break
                except:
                    continue
            if download_element:
                break
        
        if download_element:
            driver.execute_script("arguments[0].click();", download_element)
            print("✅ Clicked download button")
            time.sleep(5)
        
        # STEP 8: Handle login
        print("\n🔸 STEP 8: Checking for login requirement...")
        
        # Look for login form
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email'], input[placeholder*='email']")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        if email_inputs or password_inputs:
            print("🔐 Login form detected, attempting login...")
            
            try:
                # Load credentials
                with open('component_site_credentials.json', 'r') as f:
                    credentials = json.load(f)
                
                email = credentials['UltraLibrarian']['email']
                password = credentials['UltraLibrarian']['password']
                
                # Enter email
                if email_inputs:
                    email_input = None
                    for inp in email_inputs:
                        if inp.is_displayed() and inp.is_enabled():
                            email_input = inp
                            break
                    
                    if email_input:
                        email_input.clear()
                        email_input.send_keys(email)
                        print("✅ Entered email")
                
                # Enter password
                if password_inputs:
                    password_input = None
                    for inp in password_inputs:
                        if inp.is_displayed() and inp.is_enabled():
                            password_input = inp
                            break
                    
                    if password_input:
                        password_input.clear()
                        password_input.send_keys(password)
                        print("✅ Entered password")
                
                # Submit login
                login_buttons = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit'], button:contains('Login'), button:contains('Sign In')")
                if login_buttons:
                    for btn in login_buttons:
                        if btn.is_displayed() and btn.is_enabled():
                            driver.execute_script("arguments[0].click();", btn)
                            print("✅ Submitted login")
                            break
                else:
                    # Try pressing Enter on password field
                    if password_input:
                        password_input.send_keys(Keys.RETURN)
                        print("✅ Submitted login with Enter")
                
                time.sleep(10)  # Wait for login to process
                
            except Exception as e:
                print(f"⚠️ Login attempt failed: {e}")
        else:
            print("ℹ️ No login form detected")
        
        # STEP 9: Monitor for downloads
        print("\n🔸 STEP 9: Monitoring for file downloads...")
        
        for i in range(30):  # Monitor for 2.5 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 NEW FILES: {list(new_files)}")
                
                # Check for STEP files
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    print(f"✅ STEP FILES: {step_files}")
                    return step_files[0]
                
                # Check for ZIP files
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP FILE: {zip_files[0]}")
                    try:
                        import zipfile
                        with zipfile.ZipFile(os.path.join('3D', zip_files[0]), 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            print(f"✅ STEP FILE EXTRACTED: {step_files[0]}")
                            return step_files[0]
                    except Exception as e:
                        print(f"Error extracting ZIP: {e}")
            
            print(f"  Checking... ({(i+1)*5}/150 seconds)")
        
        print("⏳ No files downloaded after monitoring period")
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        driver.quit()

if __name__ == "__main__":
    result = complete_ultralibrarian_with_login()
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded to 3D/ folder!")
    else:
        print(f"\n⚠️ No STEP file obtained")
