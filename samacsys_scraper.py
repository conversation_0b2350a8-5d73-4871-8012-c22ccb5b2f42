#!/usr/bin/env python3
"""
SamacSys HTML scraper - No API available, using web scraping
"""

import requests
from bs4 import BeautifulSoup
import time
import os
import json
import random
from urllib.parse import urljoin, quote

class SamacSysScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        })
        
        # Create directories
        os.makedirs('samacsys_models', exist_ok=True)
        os.makedirs('datasheets', exist_ok=True)
    
    def search_component(self, manufacturer, part_number):
        """Search for component on SamacSys"""
        print(f"🔍 SEARCHING SAMACSYS")
        print(f"   Manufacturer: {manufacturer}")
        print(f"   Part Number: {part_number}")
        print("=" * 50)
        
        results = {
            'found': False,
            'datasheet_url': None,
            'datasheet_file': None,
            'symbol_url': None,
            'footprint_url': None,
            'model_3d_url': None,
            'package_type': None,
            'cad_formats': [],
            'library_loader_url': None
        }
        
        # SamacSys search patterns
        search_urls = [
            f"https://www.samacsys.com/search?q={quote(part_number)}",
            f"https://componentsearchengine.com/search?q={quote(part_number)}",
            f"https://www.samacsys.com/library/{quote(manufacturer)}/{quote(part_number)}",
            f"https://componentsearchengine.com/part-view/{quote(part_number)}"
        ]
        
        for i, search_url in enumerate(search_urls, 1):
            try:
                print(f"   🔍 Search attempt {i}: {search_url[:60]}...")
                
                time.sleep(random.uniform(2, 4))  # Be respectful
                response = self.session.get(search_url, timeout=30)
                
                print(f"      Status: {response.status_code}")
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Save response for analysis
                    with open(f'samacsys_search_{i}.html', 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    
                    # Check if part found
                    if self.is_part_found(soup, part_number):
                        print(f"      ✅ Found {part_number} on SamacSys")
                        results['found'] = True
                        
                        # Extract information
                        self.extract_component_info(soup, results, manufacturer, part_number)
                        
                        # Look for detailed part page
                        part_url = self.find_part_detail_url(soup, part_number)
                        if part_url:
                            print(f"      🔗 Found detail page: {part_url[:50]}...")
                            self.scrape_part_details(part_url, results, manufacturer, part_number)
                        
                        return results
                    else:
                        print(f"      ❌ Part not found in search {i}")
                else:
                    print(f"      ❌ HTTP Error: {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ Search {i} error: {str(e)[:50]}")
                continue
        
        print(f"   ❌ {part_number} not found on SamacSys")
        return results
    
    def is_part_found(self, soup, part_number):
        """Check if the part was found in the search results"""
        page_text = soup.get_text().lower()
        part_lower = part_number.lower()
        
        # Look for the part number in various contexts
        indicators = [
            part_lower in page_text,
            part_lower.replace('-', '') in page_text.replace('-', ''),
            part_lower.replace('-', ' ') in page_text,
            any(part_lower in link.get('href', '').lower() for link in soup.find_all('a', href=True)),
            soup.find('title') and part_lower in soup.find('title').get_text().lower()
        ]
        
        return any(indicators)
    
    def extract_component_info(self, soup, results, manufacturer, part_number):
        """Extract component information from search results"""
        try:
            # Look for datasheet links
            datasheet_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                
                if ('datasheet' in text or 'data sheet' in text or 
                    'datasheet' in href or '.pdf' in href):
                    
                    full_url = link.get('href')
                    if not full_url.startswith('http'):
                        full_url = urljoin('https://www.samacsys.com', full_url)
                    
                    datasheet_links.append(full_url)
                    print(f"      📄 Found datasheet link: {full_url[:50]}...")
            
            if datasheet_links:
                results['datasheet_url'] = datasheet_links[0]
            
            # Look for Library Loader links (SamacSys specific)
            library_loader_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                
                if ('library loader' in text or 'download' in text or 
                    'cad model' in text or 'ecad' in text):
                    
                    full_url = link.get('href')
                    if not full_url.startswith('http'):
                        full_url = urljoin('https://www.samacsys.com', full_url)
                    
                    library_loader_links.append(full_url)
                    print(f"      🔧 Found Library Loader link: {full_url[:50]}...")
            
            if library_loader_links:
                results['library_loader_url'] = library_loader_links[0]
            
            # Look for CAD format indicators
            cad_formats = []
            page_text = soup.get_text().lower()
            
            cad_format_keywords = [
                'altium', 'eagle', 'kicad', 'orcad', 'pads', 'cadence', 
                'mentor', 'zuken', 'proteus', 'diptrace', 'pulsonix'
            ]
            
            for fmt in cad_format_keywords:
                if fmt in page_text:
                    cad_formats.append(fmt.title())
            
            results['cad_formats'] = list(set(cad_formats))  # Remove duplicates
            
            # Look for 3D model indicators
            if any(indicator in page_text for indicator in ['3d model', 'step file', '.step', '3d view']):
                results['model_3d_url'] = 'Available'  # Placeholder
                print(f"      🧊 3D model available")
            
            # Look for symbol/footprint indicators
            if any(indicator in page_text for indicator in ['symbol', 'schematic', 'footprint', 'pcb']):
                results['symbol_url'] = 'Available'  # Placeholder
                results['footprint_url'] = 'Available'  # Placeholder
                print(f"      🔣 Symbol and footprint available")
            
            # Look for package information
            package_keywords = ['sot23', 'sot-23', 'soic', 'qfn', 'dfn', 'sop', 'msop', 'tssop', 'ssop', 'pdip', 'dip', 'bga', 'lga']
            
            for package in package_keywords:
                if package in page_text:
                    results['package_type'] = package.upper()
                    print(f"      📦 Package type: {package.upper()}")
                    break
            
            print(f"      🔧 CAD formats available: {', '.join(cad_formats)}")
            
        except Exception as e:
            print(f"      ⚠️ Info extraction error: {str(e)[:30]}")
    
    def find_part_detail_url(self, soup, part_number):
        """Find the detailed part page URL"""
        try:
            # Look for links that contain the part number and lead to detail pages
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text(strip=True)
                
                if (part_number.lower() in text.lower() or part_number.lower() in href.lower()):
                    # Check if it looks like a detail page
                    if any(indicator in href.lower() for indicator in ['part-view', 'component', 'detail', 'library']):
                        if not href.startswith('http'):
                            href = urljoin('https://www.samacsys.com', href)
                        return href
            
            return None
            
        except Exception as e:
            print(f"      ⚠️ Detail URL search error: {str(e)[:30]}")
            return None
    
    def scrape_part_details(self, part_url, results, manufacturer, part_number):
        """Scrape the detailed part page"""
        try:
            print(f"      🔍 Scraping part details...")
            
            time.sleep(random.uniform(2, 4))  # Be respectful
            response = self.session.get(part_url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Save part page
                with open(f'samacsys_{part_number}_details.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"      💾 Saved part details page")
                
                # Look for download links
                download_links = self.find_download_links(soup)
                
                if download_links:
                    print(f"      📥 Found {len(download_links)} download links")
                    
                    # Try to download models
                    for download_type, url in download_links.items():
                        if self.download_model(url, manufacturer, part_number, download_type):
                            if download_type == 'datasheet':
                                results['datasheet_file'] = f"{manufacturer.replace(' ', '_')}_{part_number}_datasheet.pdf"
                
                # Look for Library Loader integration
                self.check_library_loader_integration(soup, results)
                
            else:
                print(f"      ❌ Part details error: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"      ⚠️ Part details scraping error: {str(e)[:30]}")
    
    def find_download_links(self, soup):
        """Find download links on the part detail page"""
        download_links = {}
        
        try:
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text(strip=True).lower()
                
                # Categorize download links
                if 'datasheet' in text or '.pdf' in href.lower():
                    download_links['datasheet'] = href
                elif 'library loader' in text:
                    download_links['library_loader'] = href
                elif 'download' in text and any(fmt in text for fmt in ['altium', 'eagle', 'kicad']):
                    download_links['cad_package'] = href
            
            return download_links
            
        except Exception as e:
            print(f"         ⚠️ Download link search error: {str(e)[:30]}")
            return {}
    
    def check_library_loader_integration(self, soup, results):
        """Check for SamacSys Library Loader integration"""
        try:
            page_text = soup.get_text().lower()
            
            if 'library loader' in page_text:
                print(f"      🔧 Library Loader integration available")
                results['library_loader_url'] = 'Available'
                
                # Look for supported CAD tools
                cad_tools = ['altium', 'eagle', 'kicad', 'orcad', 'pads', 'cadence']
                supported_tools = []
                
                for tool in cad_tools:
                    if tool in page_text:
                        supported_tools.append(tool.title())
                
                if supported_tools:
                    results['cad_formats'].extend(supported_tools)
                    results['cad_formats'] = list(set(results['cad_formats']))  # Remove duplicates
                    print(f"      🔧 Library Loader supports: {', '.join(supported_tools)}")
            
        except Exception as e:
            print(f"      ⚠️ Library Loader check error: {str(e)[:30]}")
    
    def download_model(self, url, manufacturer, part_number, model_type):
        """Download model file from URL"""
        try:
            if not url.startswith('http'):
                url = urljoin('https://www.samacsys.com', url)
            
            print(f"         📥 Downloading {model_type}: {url[:40]}...")
            
            response = self.session.get(url, timeout=60)
            
            if response.status_code == 200:
                # Determine file extension and folder
                if model_type == 'datasheet':
                    ext = '.pdf'
                    folder = 'datasheets'
                elif model_type == 'library_loader':
                    ext = '.zip'
                    folder = 'samacsys_models'
                else:
                    ext = '.zip'
                    folder = 'samacsys_models'
                
                # Create filename
                manufacturer_clean = manufacturer.replace(" ", "_").replace(".", "").replace(",", "")
                filename = f"SamacSys_{manufacturer_clean}_{part_number}_{model_type}{ext}"
                filepath = os.path.join(folder, filename)
                
                # Save file
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                print(f"         ✅ Downloaded: {filename}")
                return True
            else:
                print(f"         ❌ Download failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"         ❌ Download error: {str(e)[:30]}")
            return False

def test_samacsys_scraper():
    """Test SamacSys scraper"""
    print("🚀 SAMACSYS SCRAPER TEST")
    print("Testing HTML scraping approach")
    print("=" * 60)
    
    scraper = SamacSysScraper()
    
    # Test cases
    test_cases = [
        ("Diodes Inc", "APX803L20-30SA-7"),
        ("Texas Instruments", "LM358N"),
        ("Texas Instruments", "LM555")
    ]
    
    successful_searches = 0
    
    for i, (manufacturer, part_number) in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}: {manufacturer} {part_number}")
        print("-" * 40)
        
        results = scraper.search_component(manufacturer, part_number)
        
        if results['found']:
            successful_searches += 1
            print(f"   ✅ SUCCESS!")
            print(f"      Datasheet: {'✅' if results['datasheet_url'] else '❌'}")
            print(f"      Library Loader: {'✅' if results['library_loader_url'] else '❌'}")
            print(f"      CAD Models: {'✅' if results['cad_formats'] else '❌'}")
            print(f"      Package: {results['package_type'] or 'Not found'}")
            print(f"      Formats: {', '.join(results['cad_formats']) if results['cad_formats'] else 'None'}")
        else:
            print(f"   ❌ Not found")
        
        # Wait between tests
        time.sleep(5)
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"📊 SAMACSYS TEST SUMMARY")
    print(f"=" * 60)
    print(f"Successful searches: {successful_searches}/{len(test_cases)}")
    success_rate = (successful_searches / len(test_cases)) * 100
    print(f"Success rate: {success_rate:.1f}%")
    
    if success_rate >= 70:
        print(f"✅ EXCELLENT: SamacSys scraper is working well")
    elif success_rate >= 50:
        print(f"⚠️ GOOD: SamacSys scraper works but may need refinement")
    else:
        print(f"❌ NEEDS WORK: SamacSys scraper needs improvement")
    
    print(f"\n📁 Files created:")
    print(f"   • samacsys_search_*.html (search results)")
    print(f"   • samacsys_models/ (downloaded models)")
    print(f"   • datasheets/ (downloaded datasheets)")

if __name__ == "__main__":
    test_samacsys_scraper()
