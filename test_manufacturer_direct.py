#!/usr/bin/env python3
"""
Test direct manufacturer website search for STEP files
APX803L20-30SA-7 is made by Diodes Incorporated
"""

import requests
from bs4 import BeautifulSoup
import re

def search_diodes_website(part_number):
    print(f"🔍 SEARCHING DIODES INCORPORATED WEBSITE FOR: {part_number}")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    print("\n1. Searching Diodes Inc website...")
    
    try:
        # Diodes Inc search URL
        search_url = "https://www.diodes.com/part-search/"
        params = {
            'q': part_number
        }
        
        print(f"   Searching: {search_url}")
        search_response = session.get(search_url, params=params, timeout=30)
        print(f"   Search Status: {search_response.status_code}")
        print(f"   Final URL: {search_response.url}")
        
        if search_response.status_code != 200:
            print(f"   ❌ Search failed with status {search_response.status_code}")
            return False
        
        # Save search results
        with open('diodes_search_results.html', 'w', encoding='utf-8') as f:
            f.write(search_response.text)
        print("   📄 Saved search results to diodes_search_results.html")
        
        # Parse search results
        soup = BeautifulSoup(search_response.text, 'html.parser')
        
        # Look for part links
        part_links = []
        
        # Look for links that might lead to part pages
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            text = link.get_text(strip=True)
            
            if href and part_number.lower() in text.lower():
                full_url = href if href.startswith('http') else f"https://www.diodes.com{href}"
                part_links.append({
                    'url': full_url,
                    'text': text
                })
        
        print(f"\n2. Found {len(part_links)} potential part matches:")
        for i, link in enumerate(part_links[:3], 1):
            print(f"   {i}. {link['text']}")
            print(f"      URL: {link['url']}")
        
        if part_links:
            print(f"\n3. Checking first match...")
            return check_diodes_part_page(session, part_links[0]['url'], part_number)
        else:
            # Try alternative search approach
            print("\n3. No direct matches found, trying alternative search...")
            return try_diodes_alternative_search(session, part_number)
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def try_diodes_alternative_search(session, part_number):
    print("   Trying Diodes product page directly...")
    
    try:
        # Try to construct direct product URL
        # Diodes often uses URLs like: /products/analog/voltage-supervisors/
        base_url = "https://www.diodes.com"
        
        # Try searching for voltage supervisors since APX803 is a voltage supervisor
        supervisor_url = f"{base_url}/products/analog/voltage-supervisors/"
        
        print(f"   Checking voltage supervisors page: {supervisor_url}")
        response = session.get(supervisor_url, timeout=30)
        
        if response.status_code == 200:
            with open('diodes_voltage_supervisors.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("   📄 Saved voltage supervisors page")
            
            # Look for our part in the page
            if part_number.lower() in response.text.lower():
                print(f"   ✅ Found {part_number} mentioned on voltage supervisors page!")
                return True
            else:
                print(f"   ❌ {part_number} not found on voltage supervisors page")
        
        return False
        
    except Exception as e:
        print(f"   ❌ Alternative search error: {e}")
        return False

def check_diodes_part_page(session, part_url, part_number):
    print(f"   Accessing part page: {part_url}")
    
    try:
        part_response = session.get(part_url, timeout=30)
        print(f"   Part page status: {part_response.status_code}")
        
        if part_response.status_code != 200:
            return False
        
        # Save part page
        with open('diodes_part_page.html', 'w', encoding='utf-8') as f:
            f.write(part_response.text)
        print("   📄 Saved part page")
        
        # Look for 3D model downloads
        soup = BeautifulSoup(part_response.text, 'html.parser')
        
        # Look for download links
        download_keywords = ['step', 'stp', '3d', 'model', 'mechanical', 'cad']
        download_links = []
        
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            text = link.get_text(strip=True).lower()
            
            if any(keyword in text for keyword in download_keywords):
                download_links.append({
                    'url': href,
                    'text': text
                })
            elif any(keyword in href.lower() for keyword in download_keywords):
                download_links.append({
                    'url': href,
                    'text': text
                })
        
        print(f"   Found {len(download_links)} potential download links:")
        for i, link in enumerate(download_links[:5], 1):
            print(f"   {i}. {link['text']}")
            print(f"      URL: {link['url']}")
        
        return len(download_links) > 0
        
    except Exception as e:
        print(f"   ❌ Error checking part page: {e}")
        return False

def search_other_manufacturers():
    print("\n🔍 TRYING OTHER COMMON STEP FILE SOURCES")
    print("=" * 50)
    
    sources = [
        {
            'name': '3D ContentCentral',
            'url': 'https://www.3dcontentcentral.com',
            'search_path': '/search.aspx'
        },
        {
            'name': 'TraceParts',
            'url': 'https://www.traceparts.com',
            'search_path': '/en/search'
        },
        {
            'name': 'GrabCAD',
            'url': 'https://grabcad.com',
            'search_path': '/library'
        }
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    for source in sources:
        print(f"\n   Testing {source['name']}...")
        try:
            response = session.get(source['url'], timeout=15)
            if response.status_code == 200:
                print(f"   ✅ {source['name']} is accessible")
            else:
                print(f"   ❌ {source['name']} returned {response.status_code}")
        except Exception as e:
            print(f"   ❌ {source['name']} error: {e}")

def main():
    part_number = "APX803L20-30SA-7"
    
    print("🚀 MANUFACTURER DIRECT SEARCH")
    print("=" * 50)
    
    # Search Diodes Inc directly
    diodes_success = search_diodes_website(part_number)
    
    if diodes_success:
        print(f"\n✅ SUCCESS: Found content for {part_number} on Diodes Inc website!")
    else:
        print(f"\n❌ No specific content found on Diodes Inc website")
    
    # Test other sources
    search_other_manufacturers()
    
    print(f"\n📋 SUMMARY:")
    print(f"   Diodes Inc: {'✅ Found' if diodes_success else '❌ Not found'}")
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"   1. Check saved HTML files for manual inspection")
    print(f"   2. Try contacting Diodes Inc support directly")
    print(f"   3. Look for similar parts with available 3D models")
    print(f"   4. Consider using generic SOT-23 package models")

if __name__ == "__main__":
    main()
