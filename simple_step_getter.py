#!/usr/bin/env python3
"""
SIMPLE STEP FILE GETTER
=======================
Gets ONE 3D model file for a part number from manufacturer website.
Doesn't care about file names or package types - just gets the file.

Usage:
    python simple_step_getter.py "Diodes Inc" "APX803L20-30SA-7"
    python simple_step_getter.py "TI" "LM358N"
"""

import requests
import os
import time
from urllib.parse import urljoin
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import argparse

class SimpleStepGetter:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # Simple manufacturer URL patterns
        self.manufacturer_urls = {
            'diodes inc': 'https://www.diodes.com/part/{part}',
            'diodes': 'https://www.diodes.com/part/{part}',
            'diodes incorporated': 'https://www.diodes.com/part/{part}',
            'ti': 'https://www.ti.com/product/{part}',
            'texas instruments': 'https://www.ti.com/product/{part}',
            'adi': 'https://www.analog.com/en/products/{part}',
            'analog devices': 'https://www.analog.com/en/products/{part}',
            'st': 'https://www.st.com/en/products/{part}',
            'stmicroelectronics': 'https://www.st.com/en/products/{part}',
            'infineon': 'https://www.infineon.com/cms/en/product/{part}',
        }
        
        os.makedirs('3D', exist_ok=True)
        print("Simple STEP File Getter Ready!")

    def get_manufacturer_url(self, manufacturer, part_number):
        """Get the manufacturer URL for this part"""
        manufacturer_key = manufacturer.lower().strip()
        
        if manufacturer_key in self.manufacturer_urls:
            return self.manufacturer_urls[manufacturer_key].format(part=part_number)
        
        # Try generic patterns
        clean_mfg = manufacturer.lower().replace(' ', '').replace('.', '').replace(',', '')
        generic_urls = [
            f"https://www.{clean_mfg}.com/products/{part_number}",
            f"https://www.{clean_mfg}.com/product/{part_number}",
            f"https://www.{clean_mfg}.com/part/{part_number}",
        ]
        
        return generic_urls[0]  # Return first generic attempt

    def setup_driver(self):
        """Setup Chrome driver for downloads"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        # Set download directory
        prefs = {
            "download.default_directory": os.path.abspath('3D'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"❌ Chrome driver failed: {e}")
            return None

    def find_step_download(self, driver):
        """Find any 3D/STEP download on the page"""
        print("   Looking for 3D model downloads...")
        
        # Look for direct STEP file links first
        step_links = []
        
        try:
            # Direct file links
            direct_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='.step'], a[href*='.stp']")
            for link in direct_links:
                href = link.get_attribute('href')
                text = link.text.strip()
                step_links.append((link, href, text, 'direct'))
                print(f"   Found direct STEP: {text[:30]}...")
        except:
            pass
        
        try:
            # Look for 3D/CAD download buttons/links
            cad_elements = driver.find_elements(By.XPATH, 
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')] | " +
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'step')] | " +
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'cad')] | " +
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')] | " +
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'step')] | " +
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'cad')]"
            )
            
            for element in cad_elements:
                text = element.text.strip()
                href = element.get_attribute('href') or ''
                if text and len(text) < 100:  # Reasonable text length
                    step_links.append((element, href, text, 'button'))
                    print(f"   🔘 Found CAD button: {text[:30]}...")
        except:
            pass
        
        return step_links

    def try_download(self, driver, element, href, text, element_type):
        """Try to download from this element"""
        try:
            print(f"   📥 Trying: {text[:40]}...")
            
            # Get file count before
            initial_files = set(os.listdir('3D'))
            
            if element_type == 'direct' and href:
                # Direct download
                response = self.session.get(href, timeout=30)
                if response.status_code == 200:
                    filename = href.split('/')[-1]
                    if not filename.endswith(('.step', '.stp')):
                        filename += '.step'
                    
                    filepath = os.path.join('3D', filename)
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    
                    return filename
            else:
                # Click element and wait for download
                driver.execute_script("arguments[0].click();", element)
                time.sleep(8)  # Wait for download
                
                # Check for new files
                current_files = set(os.listdir('3D'))
                new_files = current_files - initial_files
                
                for new_file in new_files:
                    if any(ext in new_file.lower() for ext in ['.step', '.stp', '.3d']):
                        return new_file
                
                # If no obvious STEP file, check all new files
                if new_files:
                    return list(new_files)[0]  # Return first new file
            
        except Exception as e:
            print(f"   ⚠️ Download attempt failed: {e}")
        
        return None

    def get_step_file(self, manufacturer, part_number):
        """Get ONE 3D model file for this part"""
        print(f"\nGETTING 3D MODEL")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("-" * 30)
        
        # Get manufacturer URL
        url = self.get_manufacturer_url(manufacturer, part_number)
        print(f"Checking: {url}")
        
        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            # Load the page
            driver.get(url)
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Find download options
            step_links = self.find_step_download(driver)
            
            if not step_links:
                print("   ❌ No 3D model downloads found")
                return None
            
            print(f"   ✅ Found {len(step_links)} download option(s)")
            
            # Try each download option until one works
            for element, href, text, element_type in step_links:
                downloaded_file = self.try_download(driver, element, href, text, element_type)
                
                if downloaded_file:
                    # Rename to proper format
                    old_path = os.path.join('3D', downloaded_file)
                    new_name = f"{manufacturer.replace(' ', '_')}_{part_number}.step"
                    new_path = os.path.join('3D', new_name)
                    
                    try:
                        os.rename(old_path, new_path)
                        final_name = new_name
                    except:
                        final_name = downloaded_file
                    
                    print(f"   ✅ SUCCESS: {final_name}")
                    return final_name
            
            print("   ❌ All download attempts failed")
            return None
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return None
        finally:
            driver.quit()

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python simple_step_getter.py \"Manufacturer\" \"Part Number\"")
        print("Examples:")
        print("  python simple_step_getter.py \"Diodes Inc\" \"APX803L20-30SA-7\"")
        print("  python simple_step_getter.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    getter = SimpleStepGetter()
    result = getter.get_step_file(manufacturer, part_number)
    
    if result:
        print(f"\n🎉 DOWNLOADED: {result}")
        print(f"📂 Location: step_files/{result}")
    else:
        print(f"\n😞 No 3D model found for {part_number}")
        print(f"💡 Try checking the manufacturer website manually")

if __name__ == "__main__":
    main()
