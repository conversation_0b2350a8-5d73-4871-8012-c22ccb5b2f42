#!/usr/bin/env python3
"""
Simple Digikey search for APX803L20-30SA-7
"""

import requests

def search_digikey_simple():
    print("🔍 SEARCHING DIGIKEY FOR APX803L20-30SA-7")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Try Digikey search
    part_number = "APX803L20-30SA-7"
    search_url = f"https://www.digikey.com/en/products/detail/{part_number}"
    
    print(f"Trying: {search_url}")
    
    try:
        response = session.get(search_url, timeout=30)
        print(f"Status: {response.status_code}")
        
        # Save the response
        with open('digikey_part_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("📄 Saved response")
        
        # Check if part found
        if part_number in response.text:
            print("✅ FOUND THE PART!")
        else:
            print("❌ Part not found")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    search_digikey_simple()
