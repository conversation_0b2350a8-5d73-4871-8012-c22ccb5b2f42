#!/usr/bin/env python3
"""
SIMPLE CHROME OPEN
==================
Just open Chrome to UltraLibrarian and keep it open.
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

def simple_chrome_open():
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.get('https://app.ultralibrarian.com')
    
    print("Chrome opened to UltraLibrarian")
    print("Navigate manually through the screens")
    
    # Keep it open forever
    try:
        while True:
            time.sleep(1)
    except:
        pass

if __name__ == "__main__":
    simple_chrome_open()
