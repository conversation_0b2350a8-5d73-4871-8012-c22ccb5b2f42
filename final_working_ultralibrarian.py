#!/usr/bin/env python3
"""
FINAL WORKING ULTRALIBRARIAN AUTOMATION
=======================================
Based on the successful 4-screen navigation, with enhanced download detection.
"""

import os
import time
import json
import zipfile
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

class FinalUltraLibrarianAutomation:
    def __init__(self):
        self.base_url = 'https://app.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("🎯 Final Working UltraLibrarian Automation!")

    def setup_driver(self):
        """Setup Chrome with enhanced download detection"""
        chrome_options = Options()
        
        # Download preferences - check multiple locations
        download_dir = os.path.abspath('3D')
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "profile.default_content_settings.popups": 0
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # Basic options
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(60)
        return driver

    def run_successful_automation(self, manufacturer, part_number):
        """Run the automation that we know works through Screen 4"""
        print(f"\n🚀 FINAL ULTRALIBRARIAN AUTOMATION")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("=" * 60)
        
        driver = self.setup_driver()
        
        try:
            # Get initial file state
            initial_files = self.get_all_files()
            print(f"📁 Initial files: {len(initial_files)}")
            
            # Screen 1: Search
            print(f"\n🔸 SCREEN 1: Search for {part_number}")
            driver.get(self.base_url)
            time.sleep(10)
            
            # Find and use search box
            inputs = driver.find_elements(By.TAG_NAME, "input")
            search_box = None
            for inp in inputs:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower():
                        search_box = inp
                        break
            
            if not search_box:
                print("❌ No search box found")
                return None
            
            search_box.clear()
            search_box.send_keys(part_number)
            search_box.send_keys(Keys.RETURN)
            time.sleep(8)
            print("✅ Screen 1 complete")
            
            # Screen 2: Select part
            print(f"\n🔸 SCREEN 2: Select {part_number}")
            elements = driver.find_elements(By.XPATH, f"//a[contains(text(), '{part_number}')]")
            if not elements:
                print("❌ No part results found")
                return None
            
            elements[0].click()
            time.sleep(5)
            print("✅ Screen 2 complete")
            
            # Screen 3: Download Now
            print(f"\n🔸 SCREEN 3: Download Now")
            download_elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')] | //a[contains(text(), 'Download Now')]")
            if not download_elements:
                print("❌ No Download Now button found")
                return None
            
            download_elements[0].click()
            time.sleep(3)
            print("✅ Screen 3 complete")
            
            # Screen 4: 3D CAD Model
            print(f"\n🔸 SCREEN 4: 3D CAD Model")
            model_elements = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //a[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')] | //a[contains(text(), '3D Model')]")
            if not model_elements:
                print("❌ No 3D CAD Model button found")
                return None
            
            model_elements[0].click()
            time.sleep(5)
            print("✅ Screen 4 complete")
            
            # Screen 5: Enhanced download detection
            print(f"\n🔸 SCREEN 5: Enhanced download detection")
            
            # Try automatic login first
            result = self.try_automatic_login(driver)
            if result:
                print(f"✅ Automatic login successful")
            
            # Wait and monitor for downloads
            print(f"⏳ Monitoring for downloads (60 seconds)...")
            
            for i in range(12):  # Check every 5 seconds for 60 seconds
                time.sleep(5)
                current_files = self.get_all_files()
                new_files = current_files - initial_files
                
                if new_files:
                    print(f"📁 New files detected: {list(new_files)}")
                    
                    # Process any new files
                    for new_file in new_files:
                        result = self.process_downloaded_file(new_file, manufacturer, part_number)
                        if result:
                            print(f"🎉 SUCCESS: {result}")
                            return result
                
                print(f"   Checking... ({(i+1)*5}/60 seconds)")
            
            # Final check in Downloads folder
            print(f"📁 Checking Windows Downloads folder...")
            downloads_folder = os.path.expanduser("~/Downloads")
            if os.path.exists(downloads_folder):
                downloads_files = set(os.listdir(downloads_folder))
                for file in downloads_files:
                    if (part_number.lower() in file.lower() and 
                        any(ext in file.lower() for ext in ['.step', '.stp', '.zip'])):
                        print(f"📁 Found in Downloads: {file}")
                        
                        # Copy to 3D folder
                        src = os.path.join(downloads_folder, file)
                        dst = os.path.join('3D', file)
                        
                        try:
                            import shutil
                            shutil.copy2(src, dst)
                            result = self.process_downloaded_file(file, manufacturer, part_number)
                            if result:
                                print(f"🎉 SUCCESS: {result}")
                                return result
                        except Exception as e:
                            print(f"Error copying file: {e}")
            
            print(f"❌ No STEP files found after 60 seconds")
            
            # Manual intervention
            print(f"\n🔧 MANUAL INTERVENTION REQUIRED")
            print(f"The automation successfully navigated to the download screen.")
            print(f"Please manually complete the download if needed.")
            print(f"Browser will stay open for manual completion...")
            
            input("Press Enter after manual download completion: ")
            
            # Final file check
            final_files = self.get_all_files()
            final_new_files = final_files - initial_files
            
            if final_new_files:
                for new_file in final_new_files:
                    result = self.process_downloaded_file(new_file, manufacturer, part_number)
                    if result:
                        print(f"🎉 MANUAL SUCCESS: {result}")
                        return result
            
            return None
            
        except Exception as e:
            print(f"❌ Automation error: {e}")
            return None
        
        finally:
            driver.quit()

    def get_all_files(self):
        """Get all files in 3D directory"""
        try:
            return set(os.listdir('3D'))
        except:
            return set()

    def try_automatic_login(self, driver):
        """Try to login automatically if login form is present"""
        try:
            # Load credentials
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            
            # Look for email input
            email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email']")
            if not email_inputs:
                return False
            
            email_input = None
            for inp in email_inputs:
                if inp.is_displayed() and inp.is_enabled():
                    email_input = inp
                    break
            
            if not email_input:
                return False
            
            # Enter credentials
            email_input.clear()
            email_input.send_keys(email)
            
            password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
            if password_inputs:
                password_input = password_inputs[0]
                password_input.clear()
                password_input.send_keys(password)
                
                # Find and click login
                login_buttons = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
                if login_buttons:
                    login_buttons[0].click()
                    time.sleep(5)
                    return True
            
            return False
            
        except Exception as e:
            print(f"Login attempt error: {e}")
            return False

    def process_downloaded_file(self, filename, manufacturer, part_number):
        """Process a downloaded file"""
        try:
            filepath = os.path.join('3D', filename)
            
            # Check if it's a STEP file
            if filename.lower().endswith(('.step', '.stp')):
                print(f"✅ STEP file found: {filename}")
                return filename
            
            # Check if it's a ZIP file
            elif filename.lower().endswith('.zip'):
                print(f"📦 ZIP file found: {filename}")
                
                # Extract ZIP file
                with zipfile.ZipFile(filepath, 'r') as zip_ref:
                    zip_ref.extractall('3D')
                
                # Look for STEP files in extracted content
                extracted_files = set(os.listdir('3D'))
                for extracted_file in extracted_files:
                    if extracted_file.lower().endswith(('.step', '.stp')):
                        print(f"✅ STEP file extracted: {extracted_file}")
                        return extracted_file
            
            return None
            
        except Exception as e:
            print(f"File processing error: {e}")
            return None

if __name__ == "__main__":
    automation = FinalUltraLibrarianAutomation()
    result = automation.run_successful_automation("Texas Instruments", "LM358N")
    
    if result:
        print(f"\n🎉 FINAL SUCCESS: {result} downloaded!")
        print(f"📁 Location: 3D/{result}")
    else:
        print(f"\n❌ FINAL FAILURE: No STEP file obtained")
