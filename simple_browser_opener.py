#!/usr/bin/env python3
"""
SIMPLE BROWSER OPENER
====================
Opens UltraLibrarian search page and keeps it open for manual inspection.
Shows you exactly what we're working with.
"""

import os
import time
from urllib.parse import quote
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class SimpleBrowserOpener:
    def __init__(self):
        self.base_url = 'https://www.ultralibrarian.com'
        print("Simple Browser Opener Ready!")

    def setup_driver(self):
        """Setup Chrome driver for manual inspection"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            return driver
        except Exception as e:
            print(f"Chrome driver setup failed: {e}")
            return None

    def open_and_inspect(self, manufacturer, part_number):
        """Open UltraLibrarian search page and keep it open for inspection"""
        print(f"\nOPENING ULTRALIBRARIAN FOR INSPECTION")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("-" * 50)
        
        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            # First go to homepage to see the structure
            print(f"Loading homepage: {self.base_url}")
            driver.get(self.base_url)
            time.sleep(3)

            print("Looking for search box...")

            # Try to find search input and use it
            search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='search'], input[name*='search'], input[placeholder*='search']")
            if not search_inputs:
                search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='text']")

            if search_inputs:
                search_input = search_inputs[0]
                search_term = f"{manufacturer} {part_number}"
                print(f"Found search input, entering: {search_term}")

                search_input.clear()
                search_input.send_keys(search_term)

                # Look for search button
                search_buttons = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit'], button:contains('Search')")
                if search_buttons:
                    print("Clicking search button...")
                    search_buttons[0].click()
                else:
                    # Try pressing Enter
                    from selenium.webdriver.common.keys import Keys
                    search_input.send_keys(Keys.RETURN)

                time.sleep(5)  # Wait for search results
            else:
                # Fallback to direct URL
                search_term = f"{manufacturer} {part_number}"
                search_url = f"{self.base_url}/search?q={quote(search_term)}"
                print(f"No search box found, trying direct URL: {search_url}")
                driver.get(search_url)
            time.sleep(5)  # Wait for page load
            
            print("\n✅ Browser opened with UltraLibrarian search")
            print("📋 You can now see the page structure")
            print("🔍 Look for:")
            print("   - Search results")
            print("   - Part links")
            print("   - Download buttons")
            print("   - 3D model links")
            print("   - STEP file options")
            
            # Get page info
            try:
                page_title = driver.title
                current_url = driver.current_url
                print(f"\n📄 Page Title: {page_title}")
                print(f"🌐 Current URL: {current_url}")
                
                # Try to find some basic elements
                print(f"\n🔍 BASIC ELEMENT SCAN:")
                
                # Look for links
                all_links = driver.find_elements(By.TAG_NAME, "a")
                print(f"   Total links found: {len(all_links)}")
                
                # Look for buttons
                all_buttons = driver.find_elements(By.TAG_NAME, "button")
                print(f"   Total buttons found: {len(all_buttons)}")
                
                # Look for forms
                all_forms = driver.find_elements(By.TAG_NAME, "form")
                print(f"   Total forms found: {len(all_forms)}")
                
                # Look for tables
                all_tables = driver.find_elements(By.TAG_NAME, "table")
                print(f"   Total tables found: {len(all_tables)}")
                
                # Look for divs with specific classes
                result_divs = driver.find_elements(By.CSS_SELECTOR, "div[class*='result']")
                print(f"   Divs with 'result' in class: {len(result_divs)}")
                
                # Look for any text containing our part number
                part_elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{part_number}')]")
                print(f"   Elements containing '{part_number}': {len(part_elements)}")
                
                # Look for download-related text
                download_elements = driver.find_elements(By.XPATH, "//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download')]")
                print(f"   Elements containing 'download': {len(download_elements)}")
                
                # Look for 3D-related text
                threed_elements = driver.find_elements(By.XPATH, "//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')]")
                print(f"   Elements containing '3d': {len(threed_elements)}")
                
                # Look for STEP-related text
                step_elements = driver.find_elements(By.XPATH, "//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'step')]")
                print(f"   Elements containing 'step': {len(step_elements)}")
                
            except Exception as e:
                print(f"   Error during element scan: {e}")
            
            print(f"\n⏳ Browser will stay open for manual inspection...")
            print(f"💡 Press Ctrl+C in terminal to close when done")
            
            # Keep browser open until user interrupts
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print(f"\n👋 Closing browser...")
                
        except Exception as e:
            print(f"Error: {e}")
        finally:
            driver.quit()

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python simple_browser_opener.py \"Manufacturer\" \"Part Number\"")
        print("\nExample: python simple_browser_opener.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    opener = SimpleBrowserOpener()
    opener.open_and_inspect(manufacturer, part_number)

if __name__ == "__main__":
    main()
