#!/usr/bin/env python3
"""
Debug UltraLibrarian login page to find correct selectors
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

def debug_ultralibrarian_login():
    print("🔍 DEBUGGING ULTRALIBRARIAN LOGIN")
    print("=" * 40)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Go through the UltraLibrarian flow to get to login
        print("1. Going to UltraLibrarian...")
        driver.get("https://app.ultralibrarian.com")
        time.sleep(10)
        
        print("2. Searching for LM358N...")
        search_box = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='part']")
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys("\n")
        time.sleep(10)
        
        print("3. Clicking first result...")
        first_result = driver.find_element(By.CSS_SELECTOR, ".search-result:first-child a")
        first_result.click()
        time.sleep(10)
        
        print("4. Clicking Download Now...")
        download_btn = driver.find_element(By.XPATH, "//button[contains(text(), 'Download Now')]")
        download_btn.click()
        time.sleep(5)
        
        print("5. Clicking 3D Model...")
        model_btn = driver.find_element(By.XPATH, "//button[contains(text(), '3D')]")
        model_btn.click()
        time.sleep(10)
        
        print("6. Analyzing login page...")
        print(f"Current URL: {driver.current_url}")
        
        # Get all input elements
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"\nFound {len(inputs)} input elements:")
        
        for i, inp in enumerate(inputs):
            try:
                inp_type = inp.get_attribute('type') or 'text'
                inp_name = inp.get_attribute('name') or ''
                inp_id = inp.get_attribute('id') or ''
                inp_placeholder = inp.get_attribute('placeholder') or ''
                inp_class = inp.get_attribute('class') or ''
                visible = inp.is_displayed()
                
                print(f"  {i+1}. type='{inp_type}' name='{inp_name}' id='{inp_id}'")
                print(f"      placeholder='{inp_placeholder}' visible={visible}")
                print(f"      class='{inp_class}'")
                print()
            except:
                continue
        
        # Look for forms
        forms = driver.find_elements(By.TAG_NAME, "form")
        print(f"Found {len(forms)} form elements")
        
        # Look for any text containing login/email
        page_text = driver.page_source.lower()
        if 'email' in page_text:
            print("✅ 'email' found in page source")
        if 'login' in page_text:
            print("✅ 'login' found in page source")
        if 'password' in page_text:
            print("✅ 'password' found in page source")
        
        input("Press Enter to close...")
        
    except Exception as e:
        print(f"Error during debug: {e}")
        input("Press Enter to close...")
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_ultralibrarian_login()
