#!/usr/bin/env python3
"""
Move all 3D files to the correct 3d/ directory
"""

import os
import shutil
import glob

def move_3d_files():
    print("MOVING ALL 3D FILES TO 3d/ DIRECTORY")
    print("=" * 40)
    
    # Ensure 3d directory exists
    os.makedirs('3d', exist_ok=True)
    
    # Source directories to check
    source_dirs = [
        'step_downloads',
        'files-download', 
        '3D',
        'ultralibrarian_models',
        'samacsys_models'
    ]
    
    # File extensions to move
    extensions = ['*.step', '*.stp', '*.STEP', '*.STP']
    
    moved_count = 0
    
    for source_dir in source_dirs:
        if os.path.exists(source_dir):
            print(f"\nChecking {source_dir}/...")
            
            for ext in extensions:
                pattern = os.path.join(source_dir, ext)
                files = glob.glob(pattern)
                
                for file_path in files:
                    filename = os.path.basename(file_path)
                    target_path = os.path.join('3d', filename)
                    
                    try:
                        shutil.move(file_path, target_path)
                        print(f"  ✅ Moved: {filename}")
                        moved_count += 1
                    except Exception as e:
                        print(f"  ❌ Failed to move {filename}: {e}")
        else:
            print(f"\n❌ Directory {source_dir}/ does not exist")
    
    print(f"\n📊 SUMMARY:")
    print(f"Total files moved: {moved_count}")
    
    # List final contents of 3d directory
    print(f"\n📁 Contents of 3d/ directory:")
    if os.path.exists('3d'):
        files = os.listdir('3d')
        if files:
            for i, file in enumerate(files, 1):
                print(f"  {i}. {file}")
        else:
            print("  (empty)")
    
    print(f"\n✅ All 3D files are now in the correct 3d/ directory!")

if __name__ == "__main__":
    move_3d_files()
