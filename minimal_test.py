#!/usr/bin/env python3
print("🧪 MINIMAL ULTRALIBRARIAN TEST")
print("Starting...")

try:
    from selenium import webdriver
    print("✅ Selenium imported")
    
    driver = webdriver.Chrome()
    print("✅ Chrome started")
    
    driver.get('https://www.ultralibrarian.com/')
    print("✅ Page loaded")
    
    print(f"Title: {driver.title}")
    print(f"URL: {driver.current_url}")
    
    driver.quit()
    print("✅ Test complete")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
