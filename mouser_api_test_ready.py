#!/usr/bin/env python3
"""
Mouser API test script - ready for when you get your API key
"""

import requests
import json
import os

def test_mouser_api():
    print("🔑 MOUSER API TEST")
    print("=" * 50)
    
    # Get API key from user
    print("Enter your Mouser API key from the email:")
    api_key = input("API Key: ").strip()
    
    if not api_key:
        print("❌ API key is required")
        return False
    
    print(f"🔐 Testing API key: {api_key[:20]}...")
    
    # Test with APX803L20-30SA-7
    part_number = "APX803L20-30SA-7"
    
    # Mouser API typically uses different endpoints than Digikey
    # Common Mouser API patterns:
    endpoints_to_try = [
        {
            'name': 'Search API v1',
            'url': f'https://api.mouser.com/api/v1/search/keyword?apiKey={api_key}',
            'method': 'POST',
            'data': {
                'SearchByKeywordRequest': {
                    'keyword': part_number,
                    'records': 5,
                    'startingRecord': 0
                }
            }
        },
        {
            'name': 'Part Search API',
            'url': f'https://api.mouser.com/api/v1/search/partnumber?apiKey={api_key}',
            'method': 'POST',
            'data': {
                'SearchByPartRequest': {
                    'mouserPartNumber': part_number,
                    'partSearchOptions': 'string'
                }
            }
        },
        {
            'name': 'Keyword Search',
            'url': f'https://api.mouser.com/api/v1/search/keyword',
            'method': 'GET',
            'params': {
                'apiKey': api_key,
                'keyword': part_number,
                'records': 5
            }
        }
    ]
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    for i, endpoint in enumerate(endpoints_to_try, 1):
        try:
            print(f"\n🧪 Testing endpoint {i}: {endpoint['name']}")
            print(f"   URL: {endpoint['url'][:60]}...")
            
            if endpoint['method'] == 'POST':
                response = requests.post(
                    endpoint['url'], 
                    headers=headers, 
                    json=endpoint['data'], 
                    timeout=30
                )
            else:
                response = requests.get(
                    endpoint['url'], 
                    headers=headers, 
                    params=endpoint.get('params', {}), 
                    timeout=30
                )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS! {endpoint['name']} works!")
                
                try:
                    results = response.json()
                    
                    # Save successful results
                    with open(f'mouser_success_{i}.json', 'w') as f:
                        json.dump(results, f, indent=2)
                    print(f"   💾 Results saved to mouser_success_{i}.json")
                    
                    # Try to extract product info
                    products = extract_mouser_products(results)
                    if products:
                        print(f"   📦 Found {len(products)} products!")
                        show_mouser_product(products[0])
                        
                        # Save API key for future use
                        save_mouser_credentials(api_key)
                        return True
                    else:
                        print(f"   ⚠️ API works but no products found")
                        
                except json.JSONDecodeError:
                    print(f"   ⚠️ Response is not JSON")
                    print(f"   Content: {response.text[:100]}...")
                    
            elif response.status_code == 401:
                print(f"   ❌ Authentication failed - check API key")
            elif response.status_code == 403:
                print(f"   ❌ Access forbidden - API key may not have permissions")
            elif response.status_code == 404:
                print(f"   ❌ Endpoint not found")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Response: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)[:50]}")
            continue
    
    return False

def extract_mouser_products(results):
    """Extract products from Mouser API response"""
    # Mouser API typically uses different structure than Digikey
    possible_paths = [
        ['SearchResults', 'Parts'],
        ['SearchByKeywordResponse', 'SearchResults', 'Parts'],
        ['SearchByPartResponse', 'SearchResults', 'Parts'],
        ['Parts'],
        ['Results'],
        ['Products']
    ]
    
    for path in possible_paths:
        current = results
        try:
            for key in path:
                current = current[key]
            
            if isinstance(current, list) and len(current) > 0:
                return current
        except (KeyError, TypeError):
            continue
    
    return []

def show_mouser_product(product):
    """Display Mouser product information"""
    print(f"\n   📦 PRODUCT FOUND:")
    
    # Mouser API field names (may vary)
    part_num = (product.get('MouserPartNumber') or 
                product.get('ManufacturerPartNumber') or 
                product.get('PartNumber') or 'N/A')
    
    manufacturer = (product.get('Manufacturer') or 'N/A')
    
    description = (product.get('Description') or 
                  product.get('ProductDescription') or 'N/A')
    
    price = (product.get('PriceBreaks', [{}])[0].get('Price') if product.get('PriceBreaks') 
            else product.get('UnitPrice') or 0)
    
    stock = (product.get('AvailabilityInStock') or 
            product.get('Stock') or 0)
    
    datasheet = (product.get('DataSheetUrl') or 
                product.get('DatasheetUrl') or '')
    
    print(f"      Part: {part_num}")
    print(f"      Manufacturer: {manufacturer}")
    print(f"      Description: {description[:60]}...")
    print(f"      Price: ${price}")
    print(f"      Stock: {stock}")
    
    if datasheet:
        print(f"      📄 Datasheet: {datasheet}")

def save_mouser_credentials(api_key):
    """Save Mouser API credentials"""
    try:
        creds = {'api_key': api_key}
        with open('mouser_api_credentials.json', 'w') as f:
            json.dump(creds, f, indent=2)
        print(f"   💾 Mouser credentials saved!")
    except Exception as e:
        print(f"   ⚠️ Could not save credentials: {e}")

def main():
    print("🚀 MOUSER API TEST - READY FOR YOUR API KEY")
    print("Run this script when you receive your Mouser API key email")
    print("=" * 70)
    
    success = test_mouser_api()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 SUCCESS! Your Mouser API is working!")
        print("✅ Both Digikey and Mouser APIs are now functional")
        print("✅ Complete distributor coverage achieved")
        print("✅ No more bot protection issues")
    else:
        print("⚠️ Mouser API test failed")
        print("💡 Check your API key and try again")
        print("📧 Make sure you received the API approval email")

if __name__ == "__main__":
    main()
