#!/usr/bin/env python3
"""
QUICK TEST
==========
Just open UltraLibrarian and show what we find immediately.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def quick_test():
    print("🧪 QUICK ULTRALIBRARIAN TEST")
    print("=" * 40)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("📍 Opening UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        
        print("⏳ Waiting 10 seconds...")
        time.sleep(10)
        
        print(f"✅ Page loaded!")
        print(f"Title: {driver.title}")
        print(f"URL: {driver.current_url}")
        
        # Count elements
        inputs = driver.find_elements(By.TAG_NAME, "input")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        
        print(f"Found {len(inputs)} inputs, {len(buttons)} buttons")
        
        # Show first few inputs
        print("\nInputs:")
        for i, inp in enumerate(inputs[:3]):
            try:
                placeholder = inp.get_attribute('placeholder') or 'none'
                visible = inp.is_displayed()
                print(f"  {i}: placeholder='{placeholder}', visible={visible}")
            except Exception as e:
                print(f"  {i}: Error - {e}")
        
        print("\n✅ Quick test complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        print("Closing in 5 seconds...")
        time.sleep(5)
        driver.quit()

if __name__ == "__main__":
    quick_test()
