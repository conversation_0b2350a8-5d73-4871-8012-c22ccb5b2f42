#!/usr/bin/env python3
"""
SIMPLE DOWNLOAD TEST
===================
Just get to the download dialog and wait for the file.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def simple_download_test():
    print("🎯 SIMPLE DOWNLOAD TEST")
    print("=" * 40)
    
    # Setup Chrome with download preferences
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    initial_files = set(os.listdir('3D'))
    print(f"Initial files: {len(initial_files)}")
    
    try:
        # Quick navigation to download
        print("\n🔸 Navigating to download...")
        
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        # Search
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        # Click TI LM358N
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed()):
                    link.click()
                    break
            except:
                continue
        time.sleep(8)
        
        # Click Download Now (JavaScript click)
        print("\n🔸 Clicking 'Download Now'...")
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            driver.execute_script("arguments[0].click();", download_btns[0])
            print("✅ Clicked 'Download Now' - file save dialog should appear")
        else:
            print("❌ No 'Download Now' button found!")
            return
        
        # Monitor for downloads
        print("\n🔸 Monitoring for downloads...")
        print("The file save dialog should be open now.")
        print("Chrome should automatically save to 3D/ folder...")
        
        for i in range(30):  # Monitor for 2.5 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"\n🎉 NEW FILES DETECTED: {list(new_files)}")
                
                # Check for STEP files
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    print(f"✅ STEP FILES FOUND: {step_files}")
                    return step_files[0]
                
                # Check for ZIP files
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP FILE FOUND: {zip_files[0]}")
                    # Try to extract
                    try:
                        import zipfile
                        zip_path = os.path.join('3D', zip_files[0])
                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        
                        # Check for extracted STEP files
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            print(f"✅ STEP FILE EXTRACTED: {step_files[0]}")
                            return step_files[0]
                    except Exception as e:
                        print(f"Error extracting ZIP: {e}")
                
                # Check for other files
                other_files = [f for f in new_files if not f.lower().endswith(('.txt', '.log'))]
                if other_files:
                    print(f"📄 OTHER FILES: {other_files}")
            
            print(f"  Checking... ({(i+1)*5}/150 seconds)")
        
        print("\n⏳ No files downloaded after 2.5 minutes")
        print("The file save dialog might need manual interaction.")
        
        print("\n🔍 CURRENT STATE:")
        print("Browser will stay open for manual completion...")
        print("If you see a file save dialog, please save the file to complete the download.")
        
        input("Press Enter after completing the download (or to close)...")
        
        # Final check
        final_files = set(os.listdir('3D'))
        final_new_files = final_files - initial_files
        
        if final_new_files:
            step_files = [f for f in final_new_files if f.lower().endswith(('.step', '.stp'))]
            if step_files:
                print(f"🎉 MANUAL SUCCESS: {step_files[0]}")
                return step_files[0]
        
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        driver.quit()

if __name__ == "__main__":
    result = simple_download_test()
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded to 3D/ folder!")
    else:
        print(f"\n⚠️ No STEP file obtained")
