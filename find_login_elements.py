#!/usr/bin/env python3
"""
Find login elements on UltraLibrarian page
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

def find_login_elements():
    print("🔍 FINDING LOGIN ELEMENTS ON ULTRALIBRARIAN")
    print("=" * 50)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Navigate to UltraLibrarian
        print("🌐 Loading https://www.ultralibrarian.com/")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(10)
        
        print(f"📄 Page title: {driver.title}")
        print(f"📄 Current URL: {driver.current_url}")
        
        # Look for login-related elements
        login_selectors = [
            ("text", "Login"),
            ("text", "Sign In"),
            ("text", "Sign Up"),
            ("text", "Account"),
            ("text", "Log In"),
            ("css", "a[href*='login']"),
            ("css", "a[href*='signin']"),
            ("css", "a[href*='account']"),
            ("css", "button[class*='login']"),
            ("css", "button[class*='signin']"),
            ("css", ".login"),
            ("css", ".signin"),
            ("css", ".account"),
            ("xpath", "//a[contains(@href, 'login')]"),
            ("xpath", "//a[contains(@href, 'signin')]"),
            ("xpath", "//button[contains(text(), 'Login')]"),
            ("xpath", "//button[contains(text(), 'Sign')]"),
            ("xpath", "//a[contains(text(), 'Login')]"),
            ("xpath", "//a[contains(text(), 'Sign')]")
        ]
        
        found_elements = []
        
        for selector_type, selector in login_selectors:
            try:
                if selector_type == "text":
                    elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{selector}')]")
                elif selector_type == "css":
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                elif selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                
                for element in elements:
                    if element.is_displayed():
                        text = element.text.strip()
                        tag = element.tag_name
                        href = element.get_attribute('href') or ''
                        class_attr = element.get_attribute('class') or ''
                        id_attr = element.get_attribute('id') or ''
                        
                        found_elements.append({
                            'selector': f"{selector_type}: {selector}",
                            'text': text,
                            'tag': tag,
                            'href': href,
                            'class': class_attr,
                            'id': id_attr
                        })
            except Exception as e:
                continue
        
        # Remove duplicates and print results
        unique_elements = []
        seen = set()
        
        for elem in found_elements:
            key = (elem['text'], elem['href'], elem['class'])
            if key not in seen:
                seen.add(key)
                unique_elements.append(elem)
        
        if unique_elements:
            print(f"\n✅ Found {len(unique_elements)} login-related elements:")
            for i, elem in enumerate(unique_elements, 1):
                print(f"\n{i}. {elem['tag'].upper()} element:")
                print(f"   Text: '{elem['text']}'")
                print(f"   Href: '{elem['href']}'")
                print(f"   Class: '{elem['class']}'")
                print(f"   ID: '{elem['id']}'")
                print(f"   Found by: {elem['selector']}")
        else:
            print("\n❌ No login elements found")
        
        # Also check navigation/header area specifically
        print(f"\n🔍 Checking navigation/header area...")
        nav_selectors = [
            "nav",
            "header", 
            ".navbar",
            ".navigation",
            ".header",
            ".menu",
            ".top-bar"
        ]
        
        for nav_sel in nav_selectors:
            try:
                nav_elements = driver.find_elements(By.CSS_SELECTOR, nav_sel)
                for nav in nav_elements:
                    if nav.is_displayed():
                        nav_text = nav.text
                        if any(word in nav_text.lower() for word in ['login', 'sign', 'account']):
                            print(f"   📍 Found in {nav_sel}: {nav_text[:200]}")
            except:
                continue
        
        # Take screenshot
        driver.save_screenshot("ultralibrarian_login_search.png")
        print(f"\n📸 Screenshot saved: ultralibrarian_login_search.png")
        
    finally:
        print(f"\n🔧 Keeping browser open for manual inspection...")
        input("Press Enter to close browser: ")
        driver.quit()

if __name__ == "__main__":
    find_login_elements()
