#!/usr/bin/env python3
"""
CLICK DOWNLOAD NOW
==================
Click the Download Now button.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def click_download_now():
    print("🎯 CLICK DOWNLOAD NOW")
    print("=" * 25)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick navigation to part details page
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        # Search
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        # Click TI part
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    link.click()
                    break
            except:
                continue
        time.sleep(8)
        print("✅ At Screen 3 - part details")
        
        # Click Download Now
        print("🔸 Looking for Download Now button...")
        
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        
        if download_btns:
            print(f"✅ Found Download Now: {download_btns[0].text}")
            print("🔸 Clicking Download Now...")
            driver.execute_script("arguments[0].click();", download_btns[0])
            time.sleep(5)
            print("✅ Clicked Download Now - should be at Screen 4 now")
        else:
            print("❌ No Download Now button found")
        
        # Stay open
        print("\n🔒 BROWSER STAYING OPEN")
        print("You should now see Screen 4 - download options")
        
        while True:
            time.sleep(10)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        while True:
            time.sleep(10)

if __name__ == "__main__":
    click_download_now()
