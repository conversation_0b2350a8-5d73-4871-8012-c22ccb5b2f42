#!/usr/bin/env python3
"""
MANUAL ULTRALIBRARIAN GUIDE
===========================
Opens UltraLibrarian in your regular browser and guides you through the process.
"""

import webbrowser
import time
import os

def guide_ultralibrarian_search(manufacturer, part_number):
    """Guide user through UltraLibrarian search process"""
    print(f"\n🎯 ULTRALIBRARIAN MANUAL GUIDE")
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print("=" * 50)
    
    # Create instruction file
    instructions_file = f"3D/{manufacturer.replace(' ', '_')}_{part_number}_GUIDE.txt"
    
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(f"ULTRALIBRARIAN SEARCH GUIDE\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"Target Part: {manufacturer} {part_number}\n")
        f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("STEP-BY-STEP PROCESS:\n")
        f.write("====================\n\n")
        
        f.write("STEP 1: Search\n")
        f.write("--------------\n")
        f.write("- Browser will open to UltraLibrarian homepage\n")
        f.write("- Find the search box (usually at the top)\n")
        f.write(f"- Enter: {manufacturer} {part_number}\n")
        f.write("- Click Search or press Enter\n\n")
        
        f.write("STEP 2: Results Page\n")
        f.write("-------------------\n")
        f.write("- Look for search results containing your part number\n")
        f.write("- Click on the most relevant result\n")
        f.write("- Look for exact part number matches\n\n")
        
        f.write("STEP 3: Part Details Page\n")
        f.write("------------------------\n")
        f.write("- You should now be on a part-specific page\n")
        f.write("- Look for download options:\n")
        f.write("  * 'Download' buttons\n")
        f.write("  * '3D Model' links\n")
        f.write("  * 'CAD Files' sections\n")
        f.write("  * 'STEP' file options\n\n")
        
        f.write("STEP 4: Download Screen(s)\n")
        f.write("-------------------------\n")
        f.write("- Click on 3D/STEP download option\n")
        f.write("- May require free account registration\n")
        f.write("- May have multiple screens to navigate\n")
        f.write("- Look for file format options (choose STEP)\n")
        f.write("- Look for package options (DIP, SOIC, etc.)\n\n")
        
        f.write("STEP 5: Final Download\n")
        f.write("---------------------\n")
        f.write("- Click final download button\n")
        f.write("- File should download to your Downloads folder\n")
        f.write(f"- Move/rename file to: 3D/{manufacturer.replace(' ', '_')}_{part_number}_UL.step\n\n")
        
        f.write("WHAT TO LOOK FOR:\n")
        f.write("=================\n")
        f.write("- Part number matches\n")
        f.write("- Package type (DIP-8, SOIC-8, etc.)\n")
        f.write("- File formats (.step, .stp preferred)\n")
        f.write("- Download buttons/links\n")
        f.write("- Registration requirements\n\n")
        
        f.write("TROUBLESHOOTING:\n")
        f.write("===============\n")
        f.write("- No results? Try just the part number without manufacturer\n")
        f.write("- Try different manufacturer names (TI vs Texas Instruments)\n")
        f.write("- Look for similar part numbers (LM358 vs LM358N)\n")
        f.write("- Check if free registration is required\n")
        f.write("- Some parts may not have 3D models available\n\n")
        
        f.write("REPORT BACK:\n")
        f.write("===========\n")
        f.write("After completing the process, please tell me:\n")
        f.write("1. What screens did you go through?\n")
        f.write("2. What buttons/links did you click?\n")
        f.write("3. Did you find the STEP file?\n")
        f.write("4. What was the exact navigation path?\n")
        f.write("5. Any registration/login required?\n\n")
        
        f.write("This information will help me build the automation!\n")
    
    print(f"\n📋 INSTRUCTIONS:")
    print(f"1. Browser will open to UltraLibrarian")
    print(f"2. Follow the step-by-step guide")
    print(f"3. Instructions saved to: {instructions_file}")
    print(f"4. Report back what you find!")
    
    print(f"\n🌐 Opening UltraLibrarian...")
    
    try:
        webbrowser.open('https://www.ultralibrarian.com')
        print(f"✅ Browser opened!")
        print(f"📖 Check the instruction file for detailed steps")
        print(f"🔍 Search for: {manufacturer} {part_number}")
        
        return instructions_file
        
    except Exception as e:
        print(f"❌ Failed to open browser: {e}")
        print(f"📖 Manual URL: https://www.ultralibrarian.com")
        return instructions_file

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python manual_ultralibrarian_guide.py \"Manufacturer\" \"Part Number\"")
        print("\nExample: python manual_ultralibrarian_guide.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    os.makedirs('3D', exist_ok=True)
    
    guide_file = guide_ultralibrarian_search(manufacturer, part_number)
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Use the browser that just opened")
    print(f"2. Follow the guide in: {guide_file}")
    print(f"3. Tell me exactly what screens you go through")
    print(f"4. I'll build automation based on your findings!")

if __name__ == "__main__":
    main()
