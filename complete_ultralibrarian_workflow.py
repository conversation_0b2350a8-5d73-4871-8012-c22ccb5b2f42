#!/usr/bin/env python3
"""
COMPLETE ULTRALIBRARIAN WORKFLOW
================================
Complete automation with correct sequence:
1. Search LM358N
2. Click TI LM358N
3. Click Download Now
4. Click 3D CAD Model
5. Select STEP format
6. Click final Download
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome with download preferences"""
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def complete_workflow():
    """Complete UltraLibrarian workflow"""
    print("🎯 COMPLETE ULTRALIBRARIAN WORKFLOW")
    print("=" * 60)
    
    driver = setup_driver()
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # STEP 1: Load UltraLibrarian
        print("\n🔸 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print(f"✅ Page loaded: {driver.title}")
        
        # STEP 2: Search for LM358N
        print("\n🔸 STEP 2: Searching for LM358N...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_box = None
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                search_box = inp
                break
        
        if not search_box:
            print("❌ No search box found!")
            return None
        
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        print("✅ Search submitted")
        time.sleep(10)
        
        # STEP 3: Click Texas Instruments LM358N
        print("\n🔸 STEP 3: Clicking Texas Instruments LM358N...")
        links = driver.find_elements(By.TAG_NAME, "a")
        ti_link = None
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed()):
                    ti_link = link
                    break
            except:
                continue
        
        if not ti_link:
            print("❌ No Texas Instruments LM358N found!")
            return None
        
        ti_link.click()
        print(f"✅ Clicked Texas Instruments LM358N")
        time.sleep(8)
        
        # STEP 4: Click "Download Now"
        print("\n🔸 STEP 4: Clicking 'Download Now'...")
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if not download_btns:
            print("❌ No 'Download Now' button found!")
            return None
        
        # Use JavaScript click to avoid interception
        driver.execute_script("arguments[0].click();", download_btns[0])
        print("✅ Clicked 'Download Now'")
        time.sleep(5)
        
        # STEP 5: Click "3D CAD Model" selection
        print("\n🔸 STEP 5: Clicking '3D CAD Model'...")
        
        # Look for 3D CAD Model buttons/links
        cad_selectors = [
            "//button[contains(text(), '3D CAD Model')]",
            "//a[contains(text(), '3D CAD Model')]",
            "//button[contains(text(), '3D Model')]",
            "//a[contains(text(), '3D Model')]",
            "//button[contains(text(), 'CAD Model')]",
            "//a[contains(text(), 'CAD Model')]"
        ]
        
        cad_element = None
        for selector in cad_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed() and elem.is_enabled():
                    cad_element = elem
                    print(f"✅ Found 3D CAD option: '{elem.text}'")
                    break
            if cad_element:
                break
        
        if not cad_element:
            print("❌ No 3D CAD Model option found!")
            # Show available buttons for debugging
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons:")
            for btn in buttons[:10]:
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"  - '{text}'")
                except:
                    continue
            return None
        
        # Use JavaScript click
        driver.execute_script("arguments[0].click();", cad_element)
        print("✅ Clicked 3D CAD Model")
        time.sleep(5)
        
        # Check for new window
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        # STEP 6: Select STEP format
        print("\n🔸 STEP 6: Selecting STEP format...")
        
        step_selectors = [
            "//button[contains(text(), 'STEP')]",
            "//a[contains(text(), 'STEP')]",
            "//input[@value='STEP']",
            "//option[contains(text(), 'STEP')]",
            "//div[contains(text(), 'STEP') and @onclick]",
            "//span[contains(text(), 'STEP') and parent::*[@onclick]]"
        ]
        
        step_element = None
        for selector in step_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                try:
                    if elem.is_displayed():
                        step_element = elem
                        print(f"✅ Found STEP option: '{elem.text or elem.get_attribute('value')}'")
                        break
                except:
                    continue
            if step_element:
                break
        
        if not step_element:
            print("❌ No STEP format option found!")
            return None
        
        # Use JavaScript click
        driver.execute_script("arguments[0].click();", step_element)
        print("✅ Selected STEP format")
        time.sleep(3)
        
        # STEP 7: Click final Download
        print("\n🔸 STEP 7: Clicking final Download...")
        
        final_download_selectors = [
            "//button[contains(text(), 'Download')]",
            "//a[contains(text(), 'Download')]",
            "//input[@type='submit' and contains(@value, 'Download')]"
        ]
        
        download_element = None
        for selector in final_download_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                try:
                    if elem.is_displayed() and elem.is_enabled():
                        download_element = elem
                        print(f"✅ Found final download: '{elem.text or elem.get_attribute('value')}'")
                        break
                except:
                    continue
            if download_element:
                break
        
        if not download_element:
            print("❌ No final download button found!")
            return None
        
        # Use JavaScript click
        driver.execute_script("arguments[0].click();", download_element)
        print("✅ Clicked final Download")
        time.sleep(8)
        
        # STEP 8: Handle login if needed
        print("\n🔸 STEP 8: Checking for login...")
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email']")
        if email_inputs:
            print("🔐 Login form detected")
            try:
                with open('component_site_credentials.json', 'r') as f:
                    credentials = json.load(f)
                
                email = credentials['UltraLibrarian']['email']
                password = credentials['UltraLibrarian']['password']
                
                # Enter credentials
                email_inputs[0].clear()
                email_inputs[0].send_keys(email)
                
                password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
                if password_inputs:
                    password_inputs[0].clear()
                    password_inputs[0].send_keys(password)
                    
                    # Submit
                    login_btns = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
                    if login_btns:
                        login_btns[0].click()
                        print("✅ Login submitted")
                        time.sleep(10)
                        
            except Exception as e:
                print(f"⚠️ Login failed: {e}")
        
        # STEP 9: Monitor for downloads
        print("\n🔸 STEP 9: Monitoring for downloads...")
        
        for i in range(24):  # Monitor for 2 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 NEW FILES: {list(new_files)}")
                
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    print(f"✅ STEP FILES: {step_files}")
                    return step_files[0]
                
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP FILE: {zip_files[0]}")
                    # Extract and check for STEP files
                    try:
                        import zipfile
                        with zipfile.ZipFile(os.path.join('3D', zip_files[0]), 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            print(f"✅ STEP FILE EXTRACTED: {step_files[0]}")
                            return step_files[0]
                    except Exception as e:
                        print(f"Error extracting: {e}")
            
            print(f"  Checking... ({(i+1)*5}/120 seconds)")
        
        print("⏳ No files downloaded")
        input("Press Enter to close browser...")
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        driver.quit()

if __name__ == "__main__":
    result = complete_workflow()
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded!")
    else:
        print(f"\n⚠️ No STEP file obtained")
