@echo off
REM ========================================
REM VERSION RESTORE SYSTEM
REM Restores previous versions of Python files
REM ========================================

echo.
echo ========================================
echo COMPONENT FINDER VERSION RESTORE SYSTEM
echo ========================================
echo.

REM Check if save directory exists
if not exist "save" (
    echo Error: save directory not found!
    echo Run backup_version.bat first to create backups.
    pause
    exit /b 1
)

REM Check if version file exists
if not exist "version.txt" (
    echo Error: version.txt not found!
    echo Run backup_version.bat first to create version tracking.
    pause
    exit /b 1
)

REM Read current version
set /p CURRENT_VERSION=<version.txt
echo Current version: %CURRENT_VERSION%

echo.
echo Available backup versions in save directory:
echo.

REM Show available versions
for /f "tokens=*" %%f in ('dir save /b ^| findstr "_v[0-9]"') do (
    echo   %%f
)

echo.
set /p RESTORE_VERSION="Enter version number to restore (or 'list' to see files): "

if "%RESTORE_VERSION%"=="list" (
    echo.
    echo Available backup files:
    dir save /b
    echo.
    set /p RESTORE_VERSION="Enter version number to restore: "
)

echo.
echo Restoring version %RESTORE_VERSION%...
echo.

REM Essential files to restore
set FILES=simple_digikey_with_csv snapeda_api_test_ready mouser_api_test_ready ultralibrarian_scraper rs_components_scraper samacsys_scraper component_finder_gui refresh_digikey_token cleanup_directory

REM Restore each file
for %%f in (%FILES%) do (
    if exist "save\%%f_v%RESTORE_VERSION%.py" (
        copy "save\%%f_v%RESTORE_VERSION%.py" "%%f.py" >nul
        if !errorlevel! equ 0 (
            echo ✓ Restored: %%f.py from version %RESTORE_VERSION%
        ) else (
            echo ✗ Failed to restore: %%f.py
        )
    ) else (
        echo ! Backup not found: save\%%f_v%RESTORE_VERSION%.py
    )
)

REM Restore configuration files
echo.
echo Restoring configuration files...
if exist "save\digikey_api_credentials_v%RESTORE_VERSION%.json" (
    copy "save\digikey_api_credentials_v%RESTORE_VERSION%.json" "digikey_api_credentials.json" >nul
    echo ✓ Restored: digikey_api_credentials.json
)

if exist "save\component_site_credentials_v%RESTORE_VERSION%.json" (
    copy "save\component_site_credentials_v%RESTORE_VERSION%.json" "component_site_credentials.json" >nul
    echo ✓ Restored: component_site_credentials.json
)

if exist "save\manufacturer_knowledge_v%RESTORE_VERSION%.json" (
    copy "save\manufacturer_knowledge_v%RESTORE_VERSION%.json" "manufacturer_knowledge.json" >nul
    echo ✓ Restored: manufacturer_knowledge.json
)

echo.
echo ========================================
echo RESTORE COMPLETE!
echo ========================================
echo Restored to version: %RESTORE_VERSION%
echo Current working files updated
echo.
echo Note: Current version in version.txt is still %CURRENT_VERSION%
echo Run backup_version.bat to create a new backup if needed
echo.
pause
