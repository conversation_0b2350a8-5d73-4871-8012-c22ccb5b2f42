#!/usr/bin/env python3
"""
Show ALL elements on UltraLibrarian first page to find the login
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def show_all_elements():
    print("🔸 Showing ALL elements on UltraLibrarian first page")
    print("=" * 60)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    try:
        # Load UltraLibrarian
        driver.get("https://app.ultralibrarian.com")
        time.sleep(15)  # Wait longer for page to fully load
        
        print(f"✅ Page loaded: {driver.title}")
        print(f"✅ Current URL: {driver.current_url}")
        
        # Show ALL text on the page
        print(f"\n🔍 ALL PAGE TEXT:")
        page_text = driver.page_source
        if 'login' in page_text.lower():
            print("✅ Found 'login' in page source")
        else:
            print("❌ No 'login' found in page source")
        
        # Show ALL clickable elements with ANY text containing 'log'
        print(f"\n🔍 ALL ELEMENTS CONTAINING 'LOG':")
        all_elements = driver.find_elements(By.XPATH, "//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'log')]")
        print(f"Found {len(all_elements)} elements containing 'log'")
        
        for i, elem in enumerate(all_elements):
            try:
                tag = elem.tag_name
                text = elem.text.strip()
                visible = elem.is_displayed()
                clickable = elem.is_enabled()
                print(f"  {i}: <{tag}> '{text}' visible={visible} clickable={clickable}")
            except:
                continue
        
        # Show ALL links
        print(f"\n🔍 ALL LINKS:")
        all_links = driver.find_elements(By.TAG_NAME, "a")
        print(f"Found {len(all_links)} total links")
        
        for i, link in enumerate(all_links):
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                visible = link.is_displayed()
                if visible and text:  # Only show visible links with text
                    print(f"  {i}: '{text}' -> {href}")
            except:
                continue
        
        # Show ALL buttons
        print(f"\n🔍 ALL BUTTONS:")
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"Found {len(all_buttons)} total buttons")
        
        for i, btn in enumerate(all_buttons):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if visible and text:  # Only show visible buttons with text
                    print(f"  {i}: '{text}'")
            except:
                continue
        
        # Keep browser open for inspection
        print(f"\n🔍 Browser will stay open for 60 seconds for inspection...")
        time.sleep(60)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        driver.quit()

if __name__ == "__main__":
    show_all_elements()
