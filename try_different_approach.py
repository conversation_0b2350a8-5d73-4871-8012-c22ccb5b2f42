#!/usr/bin/env python3
"""
Try different approach to access TI page
"""

import requests
import time

def try_different_approach():
    print("🔄 TRYING DIFFERENT APPROACH")
    print("=" * 40)
    
    # Wait longer
    print("⏳ Waiting 60 seconds...")
    time.sleep(60)
    
    # Try with minimal headers
    session = requests.Session()
    session.headers.clear()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    detail_url = "https://www.digikey.com/en/products/detail/texas-instruments/LM358N/3708502"
    
    print(f"📄 Trying with minimal headers: {detail_url}")
    
    try:
        response = session.get(detail_url, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ SUCCESS!")
            with open('ti_detail_success.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"   💾 Saved to ti_detail_success.html")
            return True
        else:
            print(f"   ❌ Still failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    try_different_approach()
