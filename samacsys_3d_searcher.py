#!/usr/bin/env python3
"""
SamacSys (Component Search Engine) 3D model searcher
Can be used by main program or run standalone
"""

import requests
from bs4 import BeautifulSoup
import json
import os
import sys

class SamacSys3DSearcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
    
    def search_part_3d(self, part_number):
        """Search SamacSys Component Search Engine for 3D models"""
        print(f"🔍 SEARCHING SAMACSYS FOR: {part_number}")
        
        try:
            # SamacSys Component Search Engine URL
            search_url = f"https://componentsearchengine.com/search?term={part_number}"
            
            response = self.session.get(search_url, timeout=30)
            print(f"   Search Status: {response.status_code}")
            
            if response.status_code == 200:
                # Save search results
                with open('samacsys_search.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                # Look for parts
                if part_number in response.text:
                    print(f"   ✅ Found part in search results")
                    return self.extract_part_links(response.text, part_number)
                else:
                    print(f"   ❌ Part not found in search results")
                    return None
            else:
                print(f"   ❌ Search failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ SEARCH ERROR: {e}")
            return None
    
    def extract_part_links(self, html_content, part_number):
        """Extract part detail links from search results"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for part detail links
            part_links = []
            
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text(strip=True)
                
                # Look for part detail pages
                if ('/part/' in href or '/component/' in href) and part_number.upper() in text.upper():
                    part_links.append({
                        'url': href,
                        'text': text
                    })
            
            if part_links:
                print(f"   🎯 Found {len(part_links)} part detail links")
                # Try the first part link
                return self.get_part_detail_3d(part_links[0], part_number)
            else:
                print(f"   ❌ No part detail links found")
                return None
                
        except Exception as e:
            print(f"   ❌ EXTRACTION ERROR: {e}")
            return None
    
    def get_part_detail_3d(self, part_link, part_number):
        """Access part detail page for 3D models"""
        try:
            url = part_link['url']
            
            # Make URL absolute
            if not url.startswith('http'):
                url = f"https://componentsearchengine.com{url}"
            
            print(f"   Accessing part detail: {url}")
            
            response = self.session.get(url, timeout=30)
            print(f"   Part detail status: {response.status_code}")
            
            if response.status_code == 200:
                # Save part page
                with open('samacsys_part_detail.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                # Look for 3D model downloads
                soup = BeautifulSoup(response.text, 'html.parser')
                
                download_links = []
                
                # Look for download buttons/links
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '').lower()
                    text = link.get_text(strip=True).lower()
                    title = link.get('title', '').lower()
                    
                    if any(keyword in href or keyword in text or keyword in title
                           for keyword in ['step', '3d', 'model', 'download', 'cad']):
                        download_links.append({
                            'url': link.get('href'),
                            'text': link.get_text(strip=True),
                            'title': link.get('title', '')
                        })
                
                # Also look for form submissions (SamacSys often uses forms for downloads)
                for form in soup.find_all('form'):
                    action = form.get('action', '')
                    if 'download' in action.lower() or 'step' in action.lower():
                        download_links.append({
                            'url': action,
                            'text': 'Form Download',
                            'title': 'Download Form'
                        })
                
                if download_links:
                    print(f"   🎯 Found {len(download_links)} download options")
                    return self.download_3d_models(download_links, part_number)
                else:
                    print(f"   ❌ No download options found")
                    return None
            else:
                print(f"   ❌ Failed to access part detail")
                return None
                
        except Exception as e:
            print(f"   ❌ PART DETAIL ERROR: {e}")
            return None
    
    def download_3d_models(self, download_links, part_number):
        """Download 3D models from SamacSys"""
        downloaded_files = []
        
        for i, link in enumerate(download_links[:3], 1):  # Try first 3
            try:
                url = link['url']
                
                # Make URL absolute
                if not url.startswith('http'):
                    url = f"https://componentsearchengine.com{url}"
                
                print(f"📥 Trying download {i}: {link['text']}")
                print(f"   URL: {url}")
                
                response = self.session.get(url, timeout=60, stream=True)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    
                    # Check if it's a STEP file
                    if any(ct in content_type for ct in ['application/octet-stream', 'application/step', 'model/step']):
                        filename = f"SamacSys-{part_number}.step"
                        if self.save_step_file(response, filename):
                            downloaded_files.append(filename)
                    
                    # Check if it's a ZIP file
                    elif 'application/zip' in content_type:
                        filename = f"SamacSys-{part_number}.zip"
                        if self.save_zip_file(response, filename):
                            downloaded_files.append(filename)
                    
                    # If it's HTML, might be a download page or form
                    elif 'text/html' in content_type:
                        # Look for direct STEP file links
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        step_links = []
                        for step_link in soup.find_all('a', href=True):
                            href = step_link.get('href')
                            if any(ext in href.lower() for ext in ['.step', '.stp']):
                                step_links.append(href)
                        
                        if step_links:
                            step_url = step_links[0]
                            if not step_url.startswith('http'):
                                step_url = f"https://componentsearchengine.com{step_url}"
                            
                            print(f"   Found direct STEP link: {step_url}")
                            step_response = self.session.get(step_url, timeout=60, stream=True)
                            
                            if step_response.status_code == 200:
                                filename = f"SamacSys-{part_number}.step"
                                if self.save_step_file(step_response, filename):
                                    downloaded_files.append(filename)
                        else:
                            print(f"   ⚠️  HTML page, no direct STEP links found")
                    
                    else:
                        print(f"   ⚠️  Unexpected content type: {content_type}")
                
            except Exception as e:
                print(f"   ❌ Download {i} error: {e}")
        
        return downloaded_files
    
    def save_step_file(self, response, filename):
        """Save STEP file and verify"""
        try:
            os.makedirs('3d', exist_ok=True)
            filepath = os.path.join('3d', filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    first_lines = f.read(200)
                
                if 'ISO-10303' in first_lines or 'STEP' in first_lines:
                    print(f"   🎉 SUCCESS: Valid STEP file!")
                    return True
                else:
                    print(f"   ⚠️  File doesn't appear to be STEP format")
                    return False
            else:
                print(f"   ⚠️  File too small")
                return False
                
        except Exception as e:
            print(f"   ❌ Save error: {e}")
            return False
    
    def save_zip_file(self, response, filename):
        """Save ZIP file"""
        try:
            os.makedirs('3d', exist_ok=True)
            filepath = os.path.join('3d', filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"   ✅ Downloaded ZIP: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                print(f"   📦 ZIP package downloaded (may contain STEP files)")
                return True
            else:
                print(f"   ⚠️  ZIP file too small")
                return False
                
        except Exception as e:
            print(f"   ❌ ZIP save error: {e}")
            return False

def main():
    """Standalone usage"""
    if len(sys.argv) < 2:
        print("Usage: python samacsys_3d_searcher.py <part_number>")
        print("Example: python samacsys_3d_searcher.py APX803L20-30SA-7")
        return
    
    part_number = sys.argv[1]
    
    print("🚀 SAMACSYS 3D MODEL SEARCHER")
    print("=" * 40)
    
    searcher = SamacSys3DSearcher()
    
    # Search for 3D models
    downloaded = searcher.search_part_3d(part_number)
    
    print("\n" + "=" * 40)
    if downloaded:
        print(f"✅ SUCCESS: Downloaded {len(downloaded)} files from SamacSys!")
        for file in downloaded:
            print(f"   🎯 {file}")
    else:
        print("❌ FAILED: Could not find or download 3D models from SamacSys")

if __name__ == "__main__":
    main()
