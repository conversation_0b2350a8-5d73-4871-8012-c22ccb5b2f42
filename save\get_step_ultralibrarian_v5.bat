@echo off
echo ========================================
echo    ULTRALIBRARIAN MANUAL HELPER
echo ========================================
echo.

if "%~2"=="" (
    echo Usage: get_step_ultralibrarian.bat "Manufacturer" "Part Number"
    echo.
    echo Examples:
    echo   get_step_ultralibrarian.bat "TI" "LM358N"
    echo   get_step_ultralibrarian.bat "Analog Devices" "AD8066"
    echo   get_step_ultralibrarian.bat "ST" "STM32F103"
    echo.
    pause
    exit /b 1
)

echo Opening UltraLibrarian for: %~1 %~2
echo.

python ultralibrarian_helper.py "%~1" "%~2"

echo.
echo ========================================
echo Browser should have opened to UltraLibrarian
echo Check the 3D folder for instruction files
echo ========================================
pause
