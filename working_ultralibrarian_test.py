#!/usr/bin/env python3
"""
WORKING ULTRALIBRARIAN TEST
===========================
Now that Chrome is working, let's test the actual UltraLibrarian automation.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from webdriver_manager.chrome import ChromeDriverManager

class WorkingUltraLibrarianTest:
    def __init__(self):
        self.base_url = 'https://www.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("Working UltraLibrarian Test Ready!")

    def setup_driver(self):
        """Setup Chrome driver with proper webdriver-manager"""
        print("Setting up Chrome driver...")
        
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3D'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            # Use webdriver-manager to handle ChromeDriver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Hide automation
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Chrome driver created successfully")
            return driver
        except Exception as e:
            print(f"❌ Chrome driver setup failed: {e}")
            return None

    def test_screen_1_search(self, driver, manufacturer, part_number):
        """Test Screen 1: Enter part number and search"""
        print(f"\n🔸 SCREEN 1 TEST: Enter part number and search")
        
        try:
            # Load homepage
            print(f"   Loading: {self.base_url}")
            driver.get(self.base_url)
            time.sleep(5)
            
            print(f"   Page loaded: {driver.title}")
            print(f"   URL: {driver.current_url}")
            
            # Find search box
            print(f"   Looking for search box...")
            search_selectors = [
                "input[type='search']",
                "input[name*='search']", 
                "input[placeholder*='search']",
                "input[placeholder*='Search']",
                "input[id*='search']",
                "input[class*='search']",
                "input[type='text']"
            ]
            
            search_input = None
            for i, selector in enumerate(search_selectors):
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    print(f"      Selector {i+1} ({selector}): {len(elements)} elements")
                    
                    for element in elements:
                        if element.is_displayed():
                            search_input = element
                            print(f"   ✅ Found search box with: {selector}")
                            break
                    
                    if search_input:
                        break
                except Exception as e:
                    print(f"      Selector {i+1} error: {e}")
            
            if not search_input:
                print(f"   ❌ No search box found!")
                return False
            
            # Enter search term
            search_term = f"{manufacturer} {part_number}"
            print(f"   Entering search term: '{search_term}'")
            
            search_input.clear()
            search_input.send_keys(search_term)
            print(f"   ✅ Search term entered")
            
            # Submit search
            print(f"   Submitting search...")
            
            # Try submit button first
            submit_buttons = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
            if submit_buttons:
                submit_buttons[0].click()
                print(f"   ✅ Clicked submit button")
            else:
                search_input.send_keys(Keys.RETURN)
                print(f"   ✅ Pressed Enter")
            
            # Wait for results
            print(f"   Waiting 8 seconds for search results...")
            time.sleep(8)
            
            # Check new page
            new_url = driver.current_url
            new_title = driver.title
            print(f"   Results page URL: {new_url}")
            print(f"   Results page title: {new_title}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Screen 1 failed: {e}")
            return False

    def test_screen_2_results(self, driver, part_number):
        """Test Screen 2: Look for search results"""
        print(f"\n🔸 SCREEN 2 TEST: Look for search results")
        
        try:
            # Look for any links that might be results
            print(f"   Scanning page for results...")
            
            # Multiple strategies to find results
            result_strategies = [
                f"//a[contains(text(), '{part_number}')]",
                f"//a[contains(translate(text(), 'abcdefghijklmnopqrstuvwxyz', 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'), '{part_number.upper()}')]",
                "//a[contains(@href, 'part')]",
                "//a[contains(@href, 'component')]",
                "//tr//a",
                "//table//a"
            ]
            
            all_results = []
            for i, strategy in enumerate(result_strategies):
                try:
                    elements = driver.find_elements(By.XPATH, strategy)
                    print(f"      Strategy {i+1}: {len(elements)} elements")
                    
                    for element in elements:
                        try:
                            text = element.text.strip()
                            href = element.get_attribute('href')
                            
                            if text and href and len(text) > 2:
                                priority = 0
                                if part_number.upper() in text.upper():
                                    priority = 10
                                elif 'lm358' in text.lower():
                                    priority = 5
                                
                                all_results.append({
                                    'text': text,
                                    'href': href,
                                    'priority': priority,
                                    'element': element
                                })
                        except:
                            continue
                except Exception as e:
                    print(f"      Strategy {i+1} error: {e}")
            
            # Sort by priority
            all_results.sort(key=lambda x: x['priority'], reverse=True)
            
            print(f"   Found {len(all_results)} total results")
            print(f"   Top 5 results:")
            for i, result in enumerate(all_results[:5]):
                print(f"      {i+1}. {result['text'][:60]}... (Priority: {result['priority']})")
            
            if all_results:
                print(f"   ✅ Found search results")
                return True
            else:
                print(f"   ❌ No search results found")
                return False
                
        except Exception as e:
            print(f"   ❌ Screen 2 failed: {e}")
            return False

    def run_test(self, manufacturer, part_number):
        """Run the test automation"""
        print(f"\nWORKING ULTRALIBRARIAN TEST")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("=" * 50)
        
        driver = self.setup_driver()
        if not driver:
            return False
        
        try:
            # Test Screen 1
            if not self.test_screen_1_search(driver, manufacturer, part_number):
                print(f"\n❌ Screen 1 test failed!")
                return False
            
            # Test Screen 2
            if not self.test_screen_2_results(driver, part_number):
                print(f"\n❌ Screen 2 test failed!")
                return False
            
            print(f"\n🎉 TESTS PASSED!")
            print(f"   Screen 1: Search ✅")
            print(f"   Screen 2: Results ✅")
            
            # Keep browser open for manual inspection
            print(f"\n🔍 MANUAL INSPECTION:")
            print(f"   Browser is open for you to continue manually")
            print(f"   Try clicking on a result to see Screen 3")
            print(f"   Press Enter when done...")
            
            input("Press Enter to close browser: ")
            
            return True
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
        finally:
            driver.quit()
            print("🔚 Browser closed")

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python working_ultralibrarian_test.py \"Manufacturer\" \"Part Number\"")
        print("\nExample: python working_ultralibrarian_test.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    tester = WorkingUltraLibrarianTest()
    success = tester.run_test(manufacturer, part_number)
    
    if success:
        print(f"\n✅ TEST SUCCESSFUL!")
        print(f"   The automation is working correctly")
        print(f"   Ready to build the complete version")
    else:
        print(f"\n❌ TEST FAILED!")
        print(f"   Need to debug the automation")

if __name__ == "__main__":
    main()
