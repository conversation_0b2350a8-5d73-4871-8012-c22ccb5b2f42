#!/usr/bin/env python3
"""
UltraLibrarian Screen 1 Only - Search
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def screen_1_only():
    print("🔸 SCREEN 1: Search for LM358N")
    print("=" * 40)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    try:
        # Load UltraLibrarian
        driver.get("https://app.ultralibrarian.com")
        print("   Loading https://app.ultralibrarian.com...")
        time.sleep(10)
        
        print(f"   ✅ Page loaded: {driver.title}")
        
        # Find search input
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"   Found {len(inputs)} input elements")
        
        search_input = None
        for i, inp in enumerate(inputs):
            try:
                if inp.is_displayed() and inp.is_enabled():
                    input_type = inp.get_attribute('type') or ''
                    placeholder = inp.get_attribute('placeholder') or ''
                    name = inp.get_attribute('name') or ''
                    
                    print(f"   Input {i}: type='{input_type}' placeholder='{placeholder}' name='{name}'")
                    
                    if input_type.lower() in ['text', ''] and (
                        'search' in placeholder.lower() or 
                        'part' in placeholder.lower() or
                        'keyword' in placeholder.lower()
                    ):
                        search_input = inp
                        print(f"   ✅ Using input {i} as search box")
                        break
            except:
                continue
        
        if not search_input:
            print("   ❌ No search input found!")
            return False
        
        # Type LM358N
        print("   Typing 'LM358N'...")
        search_input.clear()
        search_input.send_keys("LM358N")
        
        # Submit search
        print("   Submitting search...")
        search_input.send_keys(Keys.RETURN)
        time.sleep(8)
        
        print(f"   ✅ Search completed")
        print(f"   ✅ Current URL: {driver.current_url}")
        
        # Check if we have results
        page_source = driver.page_source.lower()
        if 'lm358n' in page_source:
            print("   ✅ LM358N found in search results")
        else:
            print("   ❌ LM358N not found in results")
        
        print("\n🎯 SCREEN 1 COMPLETE")
        print("Browser will stay open for 60 seconds for you to inspect")
        
        # Keep browser open
        time.sleep(60)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    screen_1_only()
