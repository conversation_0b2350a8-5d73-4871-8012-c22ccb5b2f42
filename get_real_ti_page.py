#!/usr/bin/env python3
"""
Get the real TI page from the redirect
"""

import requests
import os

def get_real_ti_page():
    print("🎯 GETTING REAL TI PAGE FROM REDIRECT")
    print("=" * 50)
    
    # The actual TI URL from the redirect
    ti_url = "https://www.ti.com?HQS=dis-dk-null-digikeymode-dsf-pf-null-wwe&DCM=yes&distId=10"
    
    print(f"📄 Accessing real TI page...")
    print(f"   URL: {ti_url}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        response = session.get(ti_url, timeout=60, allow_redirects=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the real TI page
            with open('ti_real_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"   ✅ Saved real TI page to ti_real_page.html")
            print(f"   Content length: {len(response.text)}")
            
            # Look for PDF links
            import re
            pdf_matches = re.findall(r'href="([^"]*\.pdf[^"]*)"', response.text, re.IGNORECASE)
            
            if pdf_matches:
                print(f"   🎯 Found PDF links:")
                for i, pdf_url in enumerate(pdf_matches[:5], 1):
                    print(f"      {i}. {pdf_url}")
                
                # Try to download the first PDF
                pdf_url = pdf_matches[0]
                if not pdf_url.startswith('http'):
                    pdf_url = f"https://www.ti.com{pdf_url}"
                
                print(f"\n📥 Downloading PDF from: {pdf_url}")
                
                pdf_response = session.get(pdf_url, timeout=60, stream=True)
                print(f"   PDF Status: {pdf_response.status_code}")
                
                if pdf_response.status_code == 200:
                    os.makedirs('datasheets', exist_ok=True)
                    filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                    
                    with open(filename, 'wb') as f:
                        for chunk in pdf_response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    
                    file_size = os.path.getsize(filename)
                    print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                    
                    if file_size > 10000:
                        print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                        return True
                    else:
                        print(f"   ⚠️  File too small")
                        return False
                else:
                    print(f"   ❌ PDF download failed: {pdf_response.status_code}")
                    return False
            else:
                print(f"   ❌ No PDF links found")
                
                # Look for LM358 or datasheet references
                if 'lm358' in response.text.lower():
                    print(f"   ✅ Found 'LM358' references in page")
                if 'datasheet' in response.text.lower():
                    print(f"   ✅ Found 'datasheet' references in page")
                
                return False
        else:
            print(f"   ❌ Failed to access real TI page: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = get_real_ti_page()
    if success:
        print("\n🎉 TEXAS INSTRUMENTS DATASHEET SUCCESSFULLY DOWNLOADED!")
    else:
        print("\n❌ FAILED TO DOWNLOAD TI DATASHEET")
