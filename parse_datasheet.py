#!/usr/bin/env python3
"""
Parse the downloaded datasheet to verify part number and find package type
"""

import os
import subprocess

def parse_datasheet():
    print("📄 PARSING DATASHEET")
    print("=" * 30)

    datasheet_path = os.path.join('files-download', 'APX803L20-30SA-7_datasheet.pdf')

    if not os.path.exists(datasheet_path):
        print("❌ Datasheet file not found!")
        return False

    print(f"📖 Found datasheet: {datasheet_path}")
    file_size = os.path.getsize(datasheet_path)
    print(f"   File size: {file_size:,} bytes")

    # Try to extract text using pdfplumber (more reliable)
    try:
        import pdfplumber

        with pdfplumber.open(datasheet_path) as pdf:
            print(f"📖 PDF has {len(pdf.pages)} pages")

            # Extract text from more pages to find ordering info
            all_text = ""
            for page_num in range(min(10, len(pdf.pages))):  # Check first 10 pages
                page = pdf.pages[page_num]
                text = page.extract_text()
                if text:
                    all_text += text + "\n"
                    print(f"   Extracted text from page {page_num + 1}")

            # Save extracted text for analysis
            with open('datasheet_text.txt', 'w', encoding='utf-8') as f:
                f.write(all_text)
            print("📄 Saved extracted text")

    except ImportError:
        print("⚠️  pdfplumber not installed, trying basic file analysis...")

        # Just check if it's a valid PDF and get basic info
        with open(datasheet_path, 'rb') as f:
            header = f.read(100)
            if b'%PDF' in header:
                print("✅ Valid PDF file detected")
                all_text = "PDF file detected but cannot extract text without pdfplumber"
            else:
                print("❌ Not a valid PDF file")
                return False
            
            # Check for our part number
            target_part = "APX803L20-30SA-7"
            if target_part in all_text:
                print(f"✅ CORRECT DATASHEET: Found {target_part}")
            else:
                print(f"⚠️  Part number {target_part} not found in text")
                # Check for partial matches
                if "APX803L20" in all_text:
                    print("   Found APX803L20 (partial match)")
                elif "APX803" in all_text:
                    print("   Found APX803 (base part)")
            
            # Look for package information
            package_keywords = [
                "SOT-23", "SOT23", "SOT-23-3", "SOT23-3",
                "package", "Package", "PACKAGE",
                "outline", "Outline", "OUTLINE",
                "mechanical", "Mechanical", "MECHANICAL"
            ]
            
            found_packages = []
            for keyword in package_keywords:
                if keyword in all_text:
                    found_packages.append(keyword)
            
            if found_packages:
                print(f"📦 PACKAGE INFO FOUND:")
                for pkg in found_packages:
                    print(f"   ✅ {pkg}")
            else:
                print("❌ No package information found")
            
            # Look for specific package dimensions or drawings
            dimension_keywords = ["mm", "mil", "inch", "dimension", "drawing"]
            found_dimensions = []
            for keyword in dimension_keywords:
                if keyword.lower() in all_text.lower():
                    found_dimensions.append(keyword)
            
            if found_dimensions:
                print(f"📏 DIMENSION INFO FOUND:")
                for dim in found_dimensions:
                    print(f"   ✅ {dim}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error parsing PDF: {e}")
        return False

def search_text_for_details():
    """Search the extracted text for specific details"""
    print("\n🔍 SEARCHING FOR SPECIFIC DETAILS")
    print("=" * 40)
    
    try:
        with open('datasheet_text.txt', 'r', encoding='utf-8') as f:
            text = f.read()
        
        # Split into lines for easier searching
        lines = text.split('\n')
        
        # Look for lines containing our part number
        part_lines = []
        for i, line in enumerate(lines):
            if "APX803L20-30SA-7" in line:
                part_lines.append((i, line.strip()))
        
        if part_lines:
            print(f"📍 FOUND PART NUMBER IN {len(part_lines)} LINES:")
            for line_num, line in part_lines:
                print(f"   Line {line_num}: {line}")
        
        # Look for package-related lines
        package_lines = []
        for i, line in enumerate(lines):
            if any(pkg in line.upper() for pkg in ["SOT-23", "SOT23", "PACKAGE"]):
                package_lines.append((i, line.strip()))
        
        if package_lines:
            print(f"\n📦 FOUND PACKAGE INFO IN {len(package_lines)} LINES:")
            for line_num, line in package_lines[:5]:  # Show first 5 matches
                print(f"   Line {line_num}: {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error searching text: {e}")
        return False

def main():
    print("🚀 DATASHEET ANALYSIS")
    print("=" * 40)
    
    success = parse_datasheet()
    
    if success:
        search_text_for_details()
        
        print("\n" + "=" * 40)
        print("✅ DATASHEET ANALYSIS COMPLETE")
        print("📄 Check 'datasheet_text.txt' for full extracted text")
    else:
        print("\n❌ DATASHEET ANALYSIS FAILED")

if __name__ == "__main__":
    main()
