#!/usr/bin/env python3
"""
Direct download of TI LM358N datasheet
"""

import requests
import os

def download_ti_datasheet_direct():
    print("📥 DIRECT TI DATASHEET DOWNLOAD")
    print("=" * 40)
    
    # Try the direct TI literature URL for LM358/LM158
    direct_urls = [
        "https://www.ti.com/lit/ds/symlink/lm158-n.pdf",
        "https://www.ti.com/lit/ds/symlink/lm358.pdf",
        "https://www.ti.com/lit/gpn/lm158-n",
        "https://www.ti.com/lit/gpn/lm358"
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    for i, url in enumerate(direct_urls, 1):
        print(f"\n🎯 Trying URL {i}: {url}")
        
        try:
            response = session.get(url, timeout=30, allow_redirects=True)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '').lower()
                print(f"   Content-Type: {content_type}")
                
                if 'application/pdf' in content_type:
                    print("   ✅ Found PDF!")
                    
                    os.makedirs('datasheets', exist_ok=True)
                    filename = f'datasheets/TI-LM358N-{i}.pdf'
                    
                    with open(filename, 'wb') as f:
                        f.write(response.content)
                    
                    file_size = os.path.getsize(filename)
                    print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                    
                    if file_size > 10000:
                        print(f"   🎉 SUCCESS!")
                        return True
                    else:
                        print(f"   ⚠️  File too small, continuing...")
                        
                elif 'text/html' in content_type:
                    print("   📄 Got HTML, looking for PDF links...")
                    
                    # Save HTML for inspection
                    with open(f'ti_response_{i}.html', 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    
                    # Look for PDF links
                    import re
                    pdf_links = re.findall(r'href="([^"]*\.pdf[^"]*)"', response.text, re.IGNORECASE)
                    
                    if pdf_links:
                        print(f"   🔗 Found {len(pdf_links)} PDF links:")
                        for j, link in enumerate(pdf_links[:3], 1):
                            print(f"      {j}. {link}")
                        
                        # Try first PDF link
                        pdf_url = pdf_links[0]
                        if not pdf_url.startswith('http'):
                            pdf_url = f"https://www.ti.com{pdf_url}"
                        
                        print(f"   📥 Downloading: {pdf_url}")
                        
                        pdf_response = session.get(pdf_url, timeout=30)
                        if pdf_response.status_code == 200 and 'application/pdf' in pdf_response.headers.get('content-type', ''):
                            os.makedirs('datasheets', exist_ok=True)
                            filename = f'datasheets/TI-LM358N-from-link-{i}.pdf'
                            
                            with open(filename, 'wb') as f:
                                f.write(pdf_response.content)
                            
                            file_size = os.path.getsize(filename)
                            print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                            
                            if file_size > 10000:
                                print(f"   🎉 SUCCESS!")
                                return True
                    else:
                        print("   ❌ No PDF links found")
                else:
                    print(f"   ❓ Unknown content type: {content_type}")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n❌ All attempts failed")
    return False

if __name__ == "__main__":
    success = download_ti_datasheet_direct()
    if success:
        print("\n🎉 TI DATASHEET SUCCESSFULLY DOWNLOADED!")
    else:
        print("\n❌ FAILED TO DOWNLOAD TI DATASHEET")
