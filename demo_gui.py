#!/usr/bin/env python3
"""
Demo script to show GUI features and test different scenarios
"""

import os
import json
from pathlib import Path

def show_demo_info():
    print("🎉 Component Finder GUI - Demo Information")
    print("=" * 60)
    print()
    
    print("📁 DIRECTORY STRUCTURE:")
    print("   datasheets/     - All datasheets saved here")
    print("   3d/             - All 3D models saved here")
    print("   manufacturer_knowledge.json - Learned patterns")
    print()
    
    print("🔧 GUI FEATURES:")
    print("   1. Enter manufacturer name (e.g., 'Diodes Inc')")
    print("   2. System tries to find website automatically")
    print("   3. If not found, asks you for website URL")
    print("   4. Enter part number (e.g., 'APX803L20-30SA-7')")
    print("   5. Click 'Search Component'")
    print("   6. Watch real-time comments and results")
    print("   7. Files automatically organized in correct folders")
    print()
    
    print("🤝 INTERACTIVE LEARNING:")
    print("   - First time: System learns manufacturer's website structure")
    print("   - Next time: Uses learned patterns (much faster)")
    print("   - If automatic discovery fails: Asks for manual help")
    print("   - You can provide direct part page URLs")
    print("   - System learns from your input for future searches")
    print()
    
    print("📚 KNOWLEDGE BASE:")
    knowledge_file = Path("manufacturer_knowledge.json")
    if knowledge_file.exists():
        try:
            with open(knowledge_file, 'r') as f:
                knowledge = json.load(f)
            
            print(f"   Currently knows {len(knowledge)} manufacturer(s):")
            for manufacturer, data in knowledge.items():
                print(f"   - {data['name']}: {data['success_count']} successful searches")
        except:
            print("   Knowledge base exists but couldn't read it")
    else:
        print("   No knowledge base yet - will be created on first search")
    
    print()
    print("🚀 TO START THE GUI:")
    print("   python component_finder_gui.py")
    print()
    
    print("🧪 TEST SCENARIOS:")
    print("   1. Known manufacturer: 'Diodes Inc' + 'APX803L20-30SA-7'")
    print("   2. Unknown manufacturer: 'Analog Devices' (will ask for analog.com)")
    print("   3. Invalid manufacturer: 'XYZ Corp' (will ask for website)")
    print()

def check_directories():
    """Check if required directories exist"""
    dirs = ['datasheets', '3d']
    
    print("📁 CHECKING DIRECTORIES:")
    for dir_name in dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            files = list(dir_path.glob('*'))
            print(f"   ✅ {dir_name}/ exists ({len(files)} files)")
        else:
            print(f"   ❌ {dir_name}/ missing")
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"   ✅ Created {dir_name}/")
            except Exception as e:
                print(f"   ❌ Failed to create {dir_name}/: {e}")

if __name__ == "__main__":
    show_demo_info()
    check_directories()
    
    print("\n" + "=" * 60)
    print("Ready to run the GUI!")
    print("Execute: python component_finder_gui.py")
    print("=" * 60)
