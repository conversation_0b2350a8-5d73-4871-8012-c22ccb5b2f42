#!/usr/bin/env python3
"""
Test EACH screen step by step to see exactly what's happening
NO ASSUMPTIONS - just test what actually works
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def test_screen_by_screen():
    print("🧪 TESTING EACH SCREEN STEP BY STEP")
    print("=" * 60)
    print("NO ASSUMPTIONS - JUST TESTING WHAT ACTUALLY WORKS")
    print("=" * 60)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    prefs = {
        "download.default_directory": os.path.abspath('3d'),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # SCREEN 1: Homepage
        print("\n🔸 SCREEN 1: UltraLibrarian Homepage")
        print("-" * 40)
        
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(5)
        
        print(f"✅ Loaded: {driver.current_url}")
        driver.save_screenshot("screen1_homepage.png")
        print("📸 Screenshot: screen1_homepage.png")
        
        # Test LOGIN link
        login_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='Login']")
        if login_links:
            print(f"✅ Found LOGIN link: {login_links[0].text}")
            print("🖱️ CLICKING LOGIN LINK...")
            login_links[0].click()
            time.sleep(5)
        else:
            print("❌ NO LOGIN LINK FOUND")
            return False
        
        # SCREEN 2: Login Page
        print(f"\n🔸 SCREEN 2: Login Page")
        print("-" * 40)
        
        print(f"✅ Current URL: {driver.current_url}")
        driver.save_screenshot("screen2_login.png")
        print("📸 Screenshot: screen2_login.png")
        
        # Test login form
        email_inputs = driver.find_elements(By.XPATH, "//input[contains(@placeholder, 'Email')]")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        if email_inputs and password_inputs:
            print("✅ Found email and password fields")
            
            # Load credentials
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            
            print("📝 FILLING LOGIN FORM...")
            email_inputs[0].send_keys(credentials['UltraLibrarian']['email'])
            password_inputs[0].send_keys(credentials['UltraLibrarian']['password'])
            
            # Find login button
            login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')]")
            if login_buttons:
                print("🖱️ CLICKING LOGIN BUTTON...")
                login_buttons[0].click()
                time.sleep(10)
            else:
                print("❌ NO LOGIN BUTTON FOUND")
                return False
        else:
            print("❌ NO LOGIN FORM FOUND")
            return False
        
        # SCREEN 3: After Login (Search Page)
        print(f"\n🔸 SCREEN 3: After Login")
        print("-" * 40)
        
        print(f"✅ Current URL: {driver.current_url}")
        driver.save_screenshot("screen3_after_login.png")
        print("📸 Screenshot: screen3_after_login.png")
        
        # Test search box
        search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i], input[type='text'], input[type='search']")
        
        search_input = None
        for inp in search_inputs:
            if inp.is_displayed() and inp.is_enabled():
                search_input = inp
                print(f"✅ Found search box: {inp.get_attribute('placeholder')}")
                break
        
        if search_input:
            print("🔍 SEARCHING FOR LM358N...")
            search_input.send_keys("LM358N")
            search_input.send_keys(Keys.RETURN)
            time.sleep(8)
        else:
            print("❌ NO SEARCH BOX FOUND")
            return False
        
        # SCREEN 4: Search Results
        print(f"\n🔸 SCREEN 4: Search Results")
        print("-" * 40)
        
        print(f"✅ Current URL: {driver.current_url}")
        driver.save_screenshot("screen4_search_results.png")
        print("📸 Screenshot: screen4_search_results.png")
        
        # Find ALL links on the page
        all_links = driver.find_elements(By.TAG_NAME, "a")
        result_links = []
        
        for link in all_links:
            try:
                if link.is_displayed():
                    text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    if text and ('LM358N' in text.upper() or 'details' in href):
                        result_links.append({
                            'text': text,
                            'href': href
                        })
            except:
                continue
        
        if result_links:
            print(f"✅ Found {len(result_links)} potential part links:")
            for i, link in enumerate(result_links[:5], 1):
                print(f"   {i}. Text: '{link['text'][:50]}' | Href: '{link['href'][:80]}'")
            
            # Click the FIRST one
            first_link = None
            for link in all_links:
                try:
                    if link.is_displayed():
                        text = link.text.strip()
                        href = link.get_attribute('href') or ''
                        if text and ('LM358N' in text.upper() or 'details' in href):
                            first_link = link
                            break
                except:
                    continue
            
            if first_link:
                print(f"🖱️ CLICKING FIRST PART: {first_link.text}")
                first_link.click()
                time.sleep(8)
            else:
                print("❌ NO CLICKABLE FIRST PART FOUND")
                return False
        else:
            print("❌ NO PART LINKS FOUND")
            return False
        
        # SCREEN 5: Part Details Page
        print(f"\n🔸 SCREEN 5: Part Details Page")
        print("-" * 40)
        
        print(f"✅ Current URL: {driver.current_url}")
        driver.save_screenshot("screen5_part_details.png")
        print("📸 Screenshot: screen5_part_details.png")
        
        # Look for download buttons/links
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        all_links = driver.find_elements(By.TAG_NAME, "a")
        
        download_elements = []
        
        # Check buttons
        for button in all_buttons:
            try:
                if button.is_displayed():
                    text = button.text.strip()
                    if any(word in text.lower() for word in ['download', 'get', 'save']):
                        download_elements.append({
                            'type': 'button',
                            'text': text,
                            'element': button
                        })
            except:
                continue
        
        # Check links
        for link in all_links:
            try:
                if link.is_displayed():
                    text = link.text.strip()
                    if any(word in text.lower() for word in ['download', 'get', 'save']):
                        download_elements.append({
                            'type': 'link',
                            'text': text,
                            'element': link
                        })
            except:
                continue
        
        if download_elements:
            print(f"✅ Found {len(download_elements)} download elements:")
            for i, elem in enumerate(download_elements, 1):
                print(f"   {i}. {elem['type'].upper()}: '{elem['text']}'")
            
            print("🖱️ CLICKING FIRST DOWNLOAD ELEMENT...")
            download_elements[0]['element'].click()
            time.sleep(5)
        else:
            print("❌ NO DOWNLOAD ELEMENTS FOUND")
            print("📋 Available buttons:")
            for i, button in enumerate(all_buttons[:10], 1):
                try:
                    if button.is_displayed():
                        print(f"   {i}. BUTTON: '{button.text.strip()}'")
                except:
                    continue
            return False
        
        print(f"\n✅ REACHED SCREEN 5 - Part details page with download options")
        print(f"📄 Final URL: {driver.current_url}")
        
        print(f"\n⏸️ MANUAL CHECK:")
        print(f"1. Look at all the screenshots")
        print(f"2. Check what happened at each screen")
        print(f"3. See if download started")
        
        input("Press Enter to close...")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        driver.quit()

if __name__ == "__main__":
    success = test_screen_by_screen()
    if success:
        print("\n🎉 SCREEN TEST COMPLETED")
    else:
        print("\n❌ SCREEN TEST FAILED")
