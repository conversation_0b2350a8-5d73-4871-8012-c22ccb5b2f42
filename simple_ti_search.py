#!/usr/bin/env python3
"""
SIMPLE: Go to ti.com, enter part number, hit return
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time

def simple_ti_search():
    print("SIMPLE TI SEARCH")
    print("=" * 30)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Step 1: Go to ti.com
        print("1. Going to ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(20)  # Wait longer for JavaScript to load
        print(f"   Loaded: {driver.title}")

        # Step 2: Wait for input fields to appear and find them
        print("2. Waiting for input fields to load...")
        search_input = None

        # Try multiple times as page loads dynamically
        for attempt in range(10):
            print(f"   Attempt {attempt + 1}/10...")
            inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"   Found {len(inputs)} input elements")

            if inputs:
                break
            time.sleep(2)

        print("3. Looking for usable input field...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        
        search_input = None
        for inp in inputs:
            try:
                if inp.is_displayed() and inp.is_enabled():
                    search_input = inp
                    print(f"   Found input field")
                    break
            except:
                continue
        
        if not search_input:
            print("   No input field found - trying to click search area...")
            # Try clicking on search-related elements
            clickable = driver.find_elements(By.XPATH, "//*[contains(text(), 'Search') or contains(@class, 'search')]")
            for elem in clickable:
                try:
                    if elem.is_displayed():
                        print(f"   Clicking: {elem.text}")
                        elem.click()
                        time.sleep(3)
                        
                        # Look for input again
                        inputs = driver.find_elements(By.TAG_NAME, "input")
                        for inp in inputs:
                            if inp.is_displayed() and inp.is_enabled():
                                search_input = inp
                                print(f"   Found input after click")
                                break
                        if search_input:
                            break
                except:
                    continue
        
        # Step 4: Enter part number
        if search_input:
            print("4. Entering LM358N...")
            search_input.clear()
            search_input.send_keys("LM358N")

            print("5. Hitting return...")
            search_input.send_keys(Keys.RETURN)
            time.sleep(10)

            print(f"6. Done! URL: {driver.current_url}")
            
            if "LM358N" in driver.page_source.upper():
                print("✅ SUCCESS: Found LM358N!")
            else:
                print("❌ No LM358N found")
        else:
            print("❌ Could not find any input field")
        
    finally:
        input("Press Enter to close...")
        driver.quit()

if __name__ == "__main__":
    simple_ti_search()
