#!/usr/bin/env python3
"""
SIMPLE PART SELECT
==================
Get to part selection and click TI LM358N, then show next screen.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def simple_part_select():
    print("🎯 SIMPLE PART SELECT")
    print("=" * 30)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Load and search
        print("🔸 Loading UltraLibrarian and searching...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        # Search for LM358N
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        print("✅ Search completed")
        
        # Click Texas Instruments LM358N
        print("🔸 Clicking Texas Instruments LM358N...")
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed()):
                    link.click()
                    print(f"✅ Clicked: {text}")
                    break
            except:
                continue
        time.sleep(8)
        
        print(f"Current URL: {driver.current_url}")
        
        # Show what's on Screen 3 (part details page)
        print("\n📺 SCREEN 3: Part details page")
        print("🔍 Available buttons:")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if text:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        print("\n🔍 Available links:")
        links = driver.find_elements(By.TAG_NAME, "a")
        for i, link in enumerate(links[:10]):
            try:
                text = link.text.strip()
                visible = link.is_displayed()
                if text and len(text) < 50:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        print("\n❓ What should I click next?")
        response = input("Tell me which button/link to click: ")
        print(f"You said: {response}")
        
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    simple_part_select()
