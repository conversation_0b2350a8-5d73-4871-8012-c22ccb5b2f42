#!/usr/bin/env python3
"""
Direct SnapEDA API access to get actual part data
"""

import requests
import json

def get_snapeda_part_data(part_number):
    print(f"🔍 GETTING SNAPEDA API DATA FOR: {part_number}")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    })
    
    try:
        # Step 1: Get search API data (using the correct endpoint)
        print("1. Accessing SnapEDA search API...")
        api_url = 'https://www.snapeda.com/api/v1/search_local'
        params = {'q': part_number, 'page': 1}
        
        response = session.get(api_url, params=params, timeout=30)
        print(f"   Status: {response.status_code}")

        if response.status_code != 200:
            print(f"   ❌ API failed")
            return False

        # Check content encoding
        content_encoding = response.headers.get('content-encoding', '')
        content_type = response.headers.get('content-type', '')
        print(f"   Content-Type: {content_type}")
        print(f"   Content-Encoding: {content_encoding}")

        # The response should be automatically decompressed by requests
        # Save raw response for debugging
        try:
            with open('snapeda_api_raw_response.txt', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"   📄 Saved raw response ({len(response.text)} chars)")
        except:
            print(f"   ⚠️  Could not save raw response as text")

        # Parse JSON
        try:
            data = response.json()
            print(f"   ✅ Got JSON response")
        except Exception as e:
            print(f"   ❌ Failed to parse JSON: {e}")
            # Try to decode manually if needed
            try:
                if content_encoding == 'gzip':
                    import gzip
                    decompressed = gzip.decompress(response.content).decode('utf-8')
                    data = json.loads(decompressed)
                    print(f"   ✅ Manually decompressed gzip and parsed JSON")
                elif content_encoding == 'br':
                    import brotli
                    decompressed = brotli.decompress(response.content).decode('utf-8')
                    data = json.loads(decompressed)
                    print(f"   ✅ Manually decompressed brotli and parsed JSON")
                else:
                    print(f"   Raw content length: {len(response.content)}")
                    print(f"   First 100 bytes: {response.content[:100]}")
                    return False
            except Exception as e2:
                print(f"   ❌ Manual decompression failed: {e2}")
                # Try installing brotli if needed
                if content_encoding == 'br':
                    print(f"   💡 Try: pip install brotli")
                return False
        
        # Save raw API response
        with open('snapeda_api_response.json', 'w') as f:
            json.dump(data, f, indent=2)
        print(f"   📄 Saved API response")
        
        # Step 2: Analyze the data
        print("\n2. Analyzing part data...")
        
        parts = data.get('parts', [])
        print(f"   Found {len(parts)} parts")
        
        if not parts:
            print(f"   ❌ No parts in response")
            return False
        
        # Look for our specific part
        target_part = None
        for i, part in enumerate(parts):
            part_name = part.get('name', '')
            print(f"   Part {i+1}: {part_name}")
            
            if part_number.upper() in part_name.upper():
                target_part = part
                print(f"   🎯 FOUND TARGET PART: {part_name}")
                break
        
        if not target_part:
            print(f"   ❌ Target part not found")
            return False
        
        # Step 3: Check what's available for this part
        print(f"\n3. Checking availability for {target_part['name']}...")
        
        # Check all the availability flags
        availability = {
            'has_datasheet': target_part.get('has_datasheet', 0),
            'has_symbol': target_part.get('has_symbol', 0),
            'has_footprint': target_part.get('has_footprint', 0),
            'has_3d': target_part.get('has_3d', 0),
            'has_sim': target_part.get('has_sim', 0),
        }
        
        print(f"   Availability flags:")
        for key, value in availability.items():
            status = "✅ YES" if value == 1 else "❌ NO"
            print(f"   {key}: {value} ({status})")
        
        # Step 4: If 3D model is available, get the part URL
        if availability['has_3d'] == 1:
            print(f"\n4. 🎉 3D MODEL IS AVAILABLE!")
            
            part_url = f"https://www.snapeda.com/parts/{target_part['urlname']}/{target_part['manufacturer']}/view-part/"
            print(f"   Part URL: {part_url}")
            
            # Try to access the part page
            print(f"\n5. Accessing part page...")
            part_response = session.get(part_url, timeout=30)
            print(f"   Part page status: {part_response.status_code}")
            
            if part_response.status_code == 200:
                with open('snapeda_part_page_direct.html', 'w', encoding='utf-8') as f:
                    f.write(part_response.text)
                print(f"   📄 Saved part page")
                
                # Look for download links on the part page
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(part_response.text, 'html.parser')
                
                # Look for download buttons/links
                download_elements = []
                
                # Check for various download patterns
                selectors = [
                    'a[href*="download"]',
                    'a[href*="step"]',
                    'a[href*="3d"]',
                    'button[onclick*="download"]',
                    '.download-btn',
                    '.btn-download'
                ]
                
                for selector in selectors:
                    elements = soup.select(selector)
                    for elem in elements:
                        download_elements.append({
                            'tag': elem.name,
                            'href': elem.get('href', ''),
                            'onclick': elem.get('onclick', ''),
                            'text': elem.get_text(strip=True),
                            'class': elem.get('class', [])
                        })
                
                if download_elements:
                    print(f"\n6. 🎯 Found {len(download_elements)} download elements:")
                    for i, elem in enumerate(download_elements, 1):
                        print(f"   {i}. {elem['text']}")
                        print(f"      href: {elem['href']}")
                        print(f"      onclick: {elem['onclick']}")
                        print(f"      class: {elem['class']}")
                else:
                    print(f"\n6. ❌ No download elements found on part page")
                
                return True
            else:
                print(f"   ❌ Failed to access part page")
                return False
        else:
            print(f"\n4. ❌ No 3D model available for this part")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    part_number = "APX803L20-30SA-7"
    
    print("🚀 SNAPEDA API DIRECT ACCESS")
    print("=" * 60)
    
    success = get_snapeda_part_data(part_number)
    
    print("\n" + "=" * 60)
    if success:
        print("✅ SUCCESS: Found part data and analyzed availability!")
    else:
        print("❌ FAILED: Could not get complete part data")

if __name__ == "__main__":
    main()
