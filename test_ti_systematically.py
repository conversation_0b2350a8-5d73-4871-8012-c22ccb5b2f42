#!/usr/bin/env python3
"""
Systematically test TI search approaches
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time

def test_ti_search():
    options = Options()
    options.add_argument('--start-maximized')
    driver = webdriver.Chrome(options=options)

    try:
        print('Loading TI.com...')
        driver.get('https://www.ti.com')
        time.sleep(25)  # Wait even longer
        
        print('Looking for search elements...')
        
        # Test 1: Look for any input elements
        all_inputs = driver.find_elements(By.TAG_NAME, 'input')
        print(f'Found {len(all_inputs)} total input elements')
        
        if len(all_inputs) == 0:
            print('No input elements found - page may not be fully loaded')
            time.sleep(15)
            all_inputs = driver.find_elements(By.TAG_NAME, 'input')
            print(f'After waiting: Found {len(all_inputs)} input elements')
        
        # Test 2: Try clicking on search-related elements first
        search_clickables = driver.find_elements(By.XPATH, "//*[contains(text(), 'Search') or contains(@class, 'search')]")
        print(f'Found {len(search_clickables)} search-related clickable elements')
        
        for i, elem in enumerate(search_clickables[:3]):
            try:
                if elem.is_displayed():
                    print(f'Clicking search element {i+1}: {elem.text}')
                    elem.click()
                    time.sleep(5)
                    
                    # Look for inputs after clicking
                    new_inputs = driver.find_elements(By.TAG_NAME, 'input')
                    print(f'After clicking: Found {len(new_inputs)} input elements')
                    
                    for inp in new_inputs:
                        try:
                            if inp.is_displayed() and inp.is_enabled():
                                print('Found usable input after click - trying it...')
                                inp.clear()
                                inp.send_keys('LM358N')
                                inp.send_keys(Keys.RETURN)
                                time.sleep(10)
                                
                                if 'LM358N' in driver.page_source.upper():
                                    print('SUCCESS!')
                                    print(f'Final URL: {driver.current_url}')
                                    return True
                        except:
                            continue
            except:
                continue
        
        # Test 3: Try JavaScript approach
        print('Trying JavaScript approach...')
        try:
            driver.execute_script("window.location.href = 'https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm=LM358N';")
            time.sleep(10)
            
            if 'LM358N' in driver.page_source.upper():
                print('SUCCESS with JavaScript!')
                print(f'Final URL: {driver.current_url}')
                return True
        except Exception as e:
            print(f'JavaScript failed: {e}')
        
        print('All approaches failed')
        return False
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_ti_search()
