#!/usr/bin/env python3
"""
ULTRALIBRARIAN STEP FILE GETTER
===============================
Specialized tool for getting STEP files from UltraLibrarian.
Handles their specific website structure and download process.
"""

import requests
import os
import time
import json
from urllib.parse import urljoin, quote
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class UltraLibrarianStepGetter:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.base_url = 'https://www.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("UltraLibrarian STEP File Getter Ready!")

    def setup_driver(self):
        """Setup Chrome driver with UltraLibrarian-specific settings"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3D'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"Chrome driver failed: {e}")
            return None

    def search_ultralibrarian(self, manufacturer, part_number):
        """Search UltraLibrarian for the part"""
        print(f"Searching UltraLibrarian for {manufacturer} {part_number}...")
        
        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            # Try different search strategies
            search_terms = [
                f"{manufacturer} {part_number}",
                part_number,
                f"{part_number} {manufacturer}"
            ]
            
            for search_term in search_terms:
                print(f"   Trying search: '{search_term}'")
                
                search_url = f"{self.base_url}/search?q={quote(search_term)}"
                driver.get(search_url)
                
                # Wait for search results
                try:
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                    time.sleep(3)  # Let page fully load
                    
                    # Look for part results
                    part_links = self.find_part_results(driver)
                    
                    if part_links:
                        print(f"   Found {len(part_links)} potential matches")
                        
                        # Try each part result
                        for part_link, part_text in part_links[:3]:  # Try first 3 results
                            print(f"   Checking: {part_text[:50]}...")
                            
                            result = self.try_part_download(driver, part_link, manufacturer, part_number)
                            if result:
                                return result
                    else:
                        print(f"   No results for '{search_term}'")
                        
                except TimeoutException:
                    print(f"   Search timeout for '{search_term}'")
                    continue
            
            print("   No matches found in UltraLibrarian")
            return None
            
        except Exception as e:
            print(f"UltraLibrarian search error: {e}")
            return None
        finally:
            driver.quit()

    def find_part_results(self, driver):
        """Find part result links on search page"""
        part_links = []
        
        try:
            # Look for various result link patterns
            selectors = [
                "a[href*='/part/']",
                "a[href*='/component/']",
                ".search-result a",
                ".part-result a",
                ".component-link"
            ]
            
            for selector in selectors:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    href = element.get_attribute('href')
                    text = element.text.strip()
                    
                    if href and text and len(text) > 3:
                        part_links.append((href, text))
            
            # Also look for clickable part names/numbers
            part_elements = driver.find_elements(By.XPATH, 
                "//a[contains(@href, 'part') or contains(@href, 'component')]"
            )
            
            for element in part_elements:
                href = element.get_attribute('href')
                text = element.text.strip()
                
                if href and text and len(text) > 3:
                    part_links.append((href, text))
            
        except Exception as e:
            print(f"   Error finding part results: {e}")
        
        return part_links

    def try_part_download(self, driver, part_url, manufacturer, part_number):
        """Try to download from a specific part page"""
        try:
            print(f"   Loading part page: {part_url[:60]}...")
            driver.get(part_url)
            
            # Wait for part page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(3)
            
            # Look for download options
            download_elements = self.find_download_options(driver)
            
            if not download_elements:
                print("   No download options found on this part page")
                return None
            
            print(f"   Found {len(download_elements)} download options")
            
            # Try each download option
            for element, text in download_elements:
                print(f"   Trying download: {text[:40]}...")
                
                # Get initial file count
                initial_files = set(os.listdir('3D'))
                
                try:
                    # Click download
                    driver.execute_script("arguments[0].click();", element)
                    time.sleep(8)  # Wait for download
                    
                    # Check for new files
                    current_files = set(os.listdir('3D'))
                    new_files = current_files - initial_files
                    
                    for new_file in new_files:
                        if any(ext in new_file.lower() for ext in ['.step', '.stp', '.zip']):
                            # Rename to standard format
                            old_path = os.path.join('3D', new_file)
                            new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                            new_path = os.path.join('3D', new_name)
                            
                            try:
                                if new_file.lower().endswith('.zip'):
                                    # Handle ZIP files - extract STEP files
                                    extracted = self.extract_step_from_zip(old_path, manufacturer, part_number)
                                    if extracted:
                                        os.remove(old_path)  # Remove ZIP
                                        return extracted
                                else:
                                    os.rename(old_path, new_path)
                                    self.create_log_file(new_name, manufacturer, part_number, part_url, "UltraLibrarian")
                                    return new_name
                            except:
                                # If rename fails, keep original name
                                self.create_log_file(new_file, manufacturer, part_number, part_url, "UltraLibrarian")
                                return new_file
                
                except Exception as e:
                    print(f"   Download attempt failed: {e}")
                    continue
            
            return None
            
        except Exception as e:
            print(f"   Part page error: {e}")
            return None

    def find_download_options(self, driver):
        """Find download buttons/links on part page"""
        download_elements = []
        
        try:
            # Look for download buttons and links
            selectors = [
                "button[onclick*='download']",
                "a[href*='download']",
                ".download-btn",
                ".btn-download",
                "button:contains('Download')",
                "a:contains('Download')",
                "button[title*='download']",
                "a[title*='download']"
            ]
            
            for selector in selectors:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.strip()
                    if text and any(keyword in text.lower() for keyword in ['download', '3d', 'step', 'cad']):
                        download_elements.append((element, text))
            
            # Also look for file links
            file_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='.step'], a[href*='.stp'], a[href*='.zip']")
            for link in file_links:
                text = link.text.strip() or link.get_attribute('href').split('/')[-1]
                download_elements.append((link, text))
                
        except Exception as e:
            print(f"   Error finding downloads: {e}")
        
        return download_elements

    def extract_step_from_zip(self, zip_path, manufacturer, part_number):
        """Extract STEP files from ZIP archive"""
        try:
            import zipfile
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Look for STEP files in the ZIP
                step_files = [f for f in zip_ref.namelist() if f.lower().endswith(('.step', '.stp'))]
                
                if step_files:
                    # Extract the first STEP file
                    step_file = step_files[0]
                    zip_ref.extract(step_file, '3D')
                    
                    # Rename to standard format
                    extracted_path = os.path.join('3D', step_file)
                    new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                    new_path = os.path.join('3D', new_name)
                    
                    os.rename(extracted_path, new_path)
                    print(f"   Extracted STEP file from ZIP: {new_name}")
                    return new_name
            
        except Exception as e:
            print(f"   ZIP extraction failed: {e}")
        
        return None

    def create_log_file(self, filename, manufacturer, part_number, source_url, source_site):
        """Create log file for the download"""
        log_name = filename.replace('.step', '.txt')
        log_path = os.path.join('3D', log_name)
        
        with open(log_path, 'w') as log_file:
            log_file.write(f"{manufacturer.upper()} {part_number} STEP FILE DOWNLOAD LOG\n")
            log_file.write("=" * 50 + "\n\n")
            log_file.write(f"Manufacturer: {manufacturer}\n")
            log_file.write(f"Part Number: {part_number}\n")
            log_file.write(f"Source: {source_site}\n")
            log_file.write(f"Source URL: {source_url}\n")
            log_file.write(f"Downloaded File: {filename}\n")
            log_file.write(f"Location: 3D/{filename}\n\n")
            log_file.write("DOWNLOAD DETAILS:\n")
            log_file.write("================\n")
            log_file.write(f"Method: {source_site} web scraping\n")
            log_file.write("Status: SUCCESS\n\n")
            log_file.write("NOTES:\n")
            log_file.write("======\n")
            log_file.write(f"- Downloaded from {source_site}\n")
            log_file.write("- Third-party CAD model library\n")
            log_file.write("- Verify dimensions against datasheet\n")

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python ultralibrarian_step_getter.py \"Manufacturer\" \"Part Number\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    getter = UltraLibrarianStepGetter()
    result = getter.search_ultralibrarian(manufacturer, part_number)
    
    if result:
        print(f"\nSUCCESS: Downloaded {result}")
        print(f"Location: 3D/{result}")
    else:
        print(f"\nFAILED: No STEP file found for {part_number} on UltraLibrarian")

if __name__ == "__main__":
    main()
