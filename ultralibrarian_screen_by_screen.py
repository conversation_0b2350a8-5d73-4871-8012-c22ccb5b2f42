#!/usr/bin/env python3
"""
UltraLibrarian Screen-by-Screen Test
Go through each screen step by step with manual verification
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

class ScreenByScreenTest:
    def __init__(self):
        self.manufacturer = "TI"
        self.part_number = "LM358N"
        os.makedirs('3d', exist_ok=True)

    def setup_driver(self):
        """Setup Chrome driver"""
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3d'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(options=chrome_options)
        return driver

    def wait_for_user(self, message):
        """Wait for user to verify screen"""
        print(f"\n⏸️  {message}")
        input("   Press Enter to continue...")

    def take_screenshot(self, driver, screen_name):
        """Take screenshot"""
        screenshot_path = f"screen_{screen_name}.png"
        driver.save_screenshot(screenshot_path)
        print(f"   📸 Screenshot: {screenshot_path}")

    def screen_1_navigate_and_login(self, driver):
        """Screen 1: Navigate to UltraLibrarian and login"""
        print(f"\n🔸 SCREEN 1: Navigate and Login")
        print("=" * 50)
        
        # Navigate to UltraLibrarian
        print("   🌐 Navigating to https://www.ultralibrarian.com/")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(5)
        
        self.take_screenshot(driver, "1_homepage")
        print(f"   📄 Current URL: {driver.current_url}")
        print(f"   📄 Page Title: {driver.title}")
        
        self.wait_for_user("VERIFY: Can you see the UltraLibrarian homepage with LOGIN link?")
        
        # Find LOGIN link
        print("   🔍 Looking for LOGIN link...")
        login_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'LOGIN')]")
        
        if not login_links:
            print("   ❌ No LOGIN link found")
            return False
        
        login_link = login_links[0]
        print(f"   ✅ Found LOGIN link: {login_link.text}")
        print(f"   🔗 LOGIN href: {login_link.get_attribute('href')}")
        
        self.wait_for_user("VERIFY: Can you see the LOGIN link highlighted?")
        
        # Click LOGIN link
        print("   🖱️ Clicking LOGIN link...")
        login_link.click()
        time.sleep(5)
        
        self.take_screenshot(driver, "1_login_page")
        print(f"   📄 After click URL: {driver.current_url}")
        
        self.wait_for_user("VERIFY: Are you now on the login page with email/password fields?")
        
        # Check for login form
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email']")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        print(f"   📝 Found {len(email_inputs)} email inputs")
        print(f"   📝 Found {len(password_inputs)} password inputs")
        
        if not email_inputs or not password_inputs:
            print("   ❌ Login form not found")
            return False
        
        # Load credentials
        try:
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            print(f"   ✅ Credentials loaded for: {email}")
        except Exception as e:
            print(f"   ❌ Could not load credentials: {e}")
            return False
        
        # Fill login form
        print("   📝 Filling email...")
        email_input = email_inputs[0]
        email_input.clear()
        email_input.send_keys(email)
        
        print("   📝 Filling password...")
        password_input = password_inputs[0]
        password_input.clear()
        password_input.send_keys(password)
        
        self.wait_for_user("VERIFY: Are email and password filled in correctly?")
        
        # Find and click login button
        login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')] | //input[@type='submit'] | //button[@type='submit']")
        
        if not login_buttons:
            print("   ❌ No login button found")
            return False
        
        login_button = login_buttons[0]
        print(f"   ✅ Found login button: {login_button.text or 'Submit'}")
        
        self.wait_for_user("VERIFY: Ready to click login button?")
        
        print("   🖱️ Clicking login button...")
        login_button.click()
        time.sleep(10)
        
        self.take_screenshot(driver, "1_after_login")
        print(f"   📄 After login URL: {driver.current_url}")
        
        self.wait_for_user("VERIFY: Did login succeed? Are you on a search page now?")
        
        print("   ✅ Screen 1 completed")
        return True

    def screen_2_search(self, driver):
        """Screen 2: Search for the part"""
        print(f"\n🔸 SCREEN 2: Search for {self.part_number}")
        print("=" * 50)
        
        # Find search box
        print("   🔍 Looking for search box...")
        search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i], input[type='text'], input[type='search']")
        
        search_input = None
        for inp in search_inputs:
            if inp.is_displayed() and inp.is_enabled():
                placeholder = inp.get_attribute('placeholder') or ''
                print(f"   📝 Found input: {placeholder}")
                if 'search' in placeholder.lower() or 'part' in placeholder.lower():
                    search_input = inp
                    break
        
        if not search_input and search_inputs:
            search_input = search_inputs[0]  # Use first visible input
        
        if not search_input:
            print("   ❌ No search box found")
            return False
        
        print(f"   ✅ Using search box: {search_input.get_attribute('placeholder')}")
        
        self.wait_for_user("VERIFY: Can you see the search box highlighted?")
        
        # Perform search
        print(f"   🔍 Searching for: {self.part_number}")
        search_input.clear()
        search_input.send_keys(self.part_number)
        
        self.wait_for_user(f"VERIFY: Is '{self.part_number}' typed in the search box?")
        
        search_input.send_keys(Keys.RETURN)
        time.sleep(8)
        
        self.take_screenshot(driver, "2_search_results")
        print(f"   📄 Search results URL: {driver.current_url}")
        
        self.wait_for_user("VERIFY: Can you see search results with LM358N parts?")
        
        print("   ✅ Screen 2 completed")
        return True

    def run_test(self):
        """Run the complete screen-by-screen test"""
        print("🧪 ULTRALIBRARIAN SCREEN-BY-SCREEN TEST")
        print("=" * 60)
        print(f"Testing: {self.manufacturer} {self.part_number}")
        print("We'll go through each screen step by step")
        
        driver = self.setup_driver()
        
        try:
            if not self.screen_1_navigate_and_login(driver):
                print("\n❌ Screen 1 failed")
                return False
            
            if not self.screen_2_search(driver):
                print("\n❌ Screen 2 failed")
                return False
            
            print("\n🎉 Screens 1-2 completed successfully!")
            print("Ready to continue with remaining screens...")
            
            self.wait_for_user("Continue with Screen 3 (Select Part)?")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            self.wait_for_user("Test complete. Close browser?")
            driver.quit()

if __name__ == "__main__":
    test = ScreenByScreenTest()
    test.run_test()
