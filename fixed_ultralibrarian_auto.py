#!/usr/bin/env python3
"""
Fixed UltraLibrarian - Actually performs actions automatically
"""

import os
import time
import json
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome driver"""
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    # Download preferences
    prefs = {
        "download.default_directory": os.path.abspath('3d'),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    return webdriver.Chrome(options=chrome_options)

def main():
    print("🚀 FIXED ULTRALIBRARIAN AUTO DOWNLOADER")
    print("=" * 50)

    manufacturer = "TI"
    part_number = "LM358N"

    os.makedirs('3d', exist_ok=True)
    
    driver = setup_driver()
    
    try:
        # Step 1: Navigate to homepage
        print("🔸 STEP 1: Loading UltraLibrarian homepage...")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(5)
        print(f"   ✅ Loaded: {driver.current_url}")
        
        # Step 2: Find and click LOGIN link
        print("🔸 STEP 2: Finding LOGIN link...")

        # Try multiple selectors for LOGIN link
        login_selectors = [
            ("xpath", "//a[contains(text(), 'LOGIN')]"),
            ("xpath", "//a[text()='LOGIN']"),
            ("css", "a[href*='Login']"),
            ("css", "a[href*='login']")
        ]

        login_link = None
        for selector_type, selector in login_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                for element in elements:
                    if element.is_displayed():
                        login_link = element
                        print(f"   ✅ Found LOGIN link with {selector_type}: {element.text}")
                        break

                if login_link:
                    break
            except:
                continue

        if not login_link:
            print("   ❌ No LOGIN link found")
            return False
        
        print(f"   🖱️ Clicking LOGIN link...")
        login_link.click()
        time.sleep(5)
        
        print(f"   ✅ Navigated to: {driver.current_url}")
        
        # Step 3: Fill login form
        print("🔸 STEP 3: Filling login form...")
        
        # Load credentials
        try:
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            print(f"   ✅ Using credentials for: {email}")
        except Exception as e:
            print(f"   ❌ Could not load credentials: {e}")
            return False
        
        # Find email input (it's actually a text input with placeholder "Email")
        email_inputs = driver.find_elements(By.XPATH, "//input[contains(@placeholder, 'Email')]")
        if not email_inputs:
            # Fallback to other selectors
            email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[name='Username'], input[id='Username']")

        if not email_inputs:
            print("   ❌ No email input found")
            return False

        # Find password input
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        if not password_inputs:
            print("   ❌ No password input found")
            return False
        
        # Fill form
        print("   📝 Entering email...")
        email_inputs[0].clear()
        email_inputs[0].send_keys(email)
        
        print("   📝 Entering password...")
        password_inputs[0].clear()
        password_inputs[0].send_keys(password)
        
        # Find and click login button
        login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')]")
        if not login_buttons:
            # Fallback to other button selectors
            login_buttons = driver.find_elements(By.XPATH, "//input[@type='submit'] | //button[@type='submit']")

        if not login_buttons:
            print("   ❌ No login button found")
            return False
        
        print("   🖱️ Clicking login button...")
        login_buttons[0].click()
        time.sleep(10)
        
        print(f"   ✅ Login completed: {driver.current_url}")
        
        # Step 4: Search for part
        print("🔸 STEP 4: Searching for LM358N...")
        
        # Find search box
        search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i], input[type='text'], input[type='search']")
        
        search_input = None
        for inp in search_inputs:
            if inp.is_displayed() and inp.is_enabled():
                search_input = inp
                break
        
        if not search_input:
            print("   ❌ No search box found")
            return False
        
        print("   🔍 Entering search term...")
        search_input.clear()
        search_input.send_keys("LM358N")
        search_input.send_keys(Keys.RETURN)
        time.sleep(8)
        
        print(f"   ✅ Search completed: {driver.current_url}")
        
        # Step 5: Click on FIRST part in search results
        print("🔸 STEP 5: Selecting FIRST LM358N part from results...")

        # Look for the first part link in search results
        # Try multiple selectors to find the first search result
        first_part_selectors = [
            ("css", ".search-result:first-child a"),
            ("css", ".result-item:first-child a"),
            ("css", "[data-testid='search-result']:first-child a"),
            ("xpath", "(//div[contains(@class, 'result')]//a)[1]"),
            ("xpath", "(//a[contains(@href, '/details/')])[1]"),
            ("xpath", "(//a[contains(text(), 'LM358N')])[1]")
        ]

        first_part_link = None
        for selector_type, selector in first_part_selectors:
            try:
                if selector_type == "css":
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                else:
                    elements = driver.find_elements(By.XPATH, selector)

                for element in elements:
                    if element.is_displayed() and element.get_attribute('href'):
                        first_part_link = element
                        print(f"   ✅ Found FIRST part with {selector_type}: {element.text}")
                        break

                if first_part_link:
                    break
            except:
                continue

        if not first_part_link:
            print("   ❌ No first part link found")
            return False

        print("   🖱️ Clicking FIRST part link...")
        first_part_link.click()
        time.sleep(8)
        
        print(f"   ✅ Part selected: {driver.current_url}")
        
        # Step 6: Click Download Now
        print("🔸 STEP 6: Clicking Download Now...")

        download_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')]")
        if not download_buttons:
            print("   ❌ No Download button found")
            return False

        download_button = download_buttons[0]
        print(f"   ✅ Found download button: {download_button.text}")

        # Scroll to button and make sure it's visible
        driver.execute_script("arguments[0].scrollIntoView(true);", download_button)
        time.sleep(2)

        # Try clicking with JavaScript if regular click fails
        try:
            print("   🖱️ Clicking download button...")
            download_button.click()
        except Exception as e:
            print(f"   ⚠️ Regular click failed: {e}")
            print("   🖱️ Trying JavaScript click...")
            driver.execute_script("arguments[0].click();", download_button)

        time.sleep(5)
        
        print(f"   ✅ Download clicked: {driver.current_url}")
        
        # Step 7: Select 3D Model
        print("🔸 STEP 7: Selecting 3D Model...")
        
        model_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '3D')] | //a[contains(text(), '3D')]")
        if not model_buttons:
            print("   ❌ No 3D Model button found")
            return False
        
        print(f"   ✅ Found 3D button: {model_buttons[0].text}")
        print("   🖱️ Clicking 3D model button...")
        model_buttons[0].click()
        time.sleep(8)
        
        print(f"   ✅ 3D Model selected: {driver.current_url}")
        
        # Step 8: Final download
        print("🔸 STEP 8: Final download...")

        final_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //input[@type='submit'] | //button[@type='submit']")
        if final_buttons:
            final_button = final_buttons[0]
            print(f"   ✅ Found final button: {final_button.text or 'Submit'}")

            # Scroll to button and make sure it's visible
            driver.execute_script("arguments[0].scrollIntoView(true);", final_button)
            time.sleep(2)

            # Try clicking with JavaScript if regular click fails
            try:
                print("   🖱️ Clicking final download...")
                final_button.click()
            except Exception as e:
                print(f"   ⚠️ Regular click failed: {e}")
                print("   🖱️ Trying JavaScript click...")
                driver.execute_script("arguments[0].click();", final_button)

            time.sleep(5)
        
        # Step 9: Monitor for download in 3d folder (where Chrome should download)
        print("🔸 STEP 9: Monitoring for download...")

        local_3d_dir = os.path.abspath('3d')
        downloads_dir = os.path.expanduser("~/Downloads")  # Backup check

        print(f"   📁 Primary download location: {local_3d_dir}")
        print(f"   📁 Backup check location: {downloads_dir}")

        # Get initial file lists
        initial_3d = set(os.listdir(local_3d_dir)) if os.path.exists(local_3d_dir) else set()
        initial_downloads = set(os.listdir(downloads_dir)) if os.path.exists(downloads_dir) else set()

        # Record start time to only check for new files
        import time as time_module
        start_time = time_module.time()

        for i in range(24):  # Check for 2 minutes
            time.sleep(5)

            # First check 3d folder (primary download location)
            if os.path.exists(local_3d_dir):
                current_3d = set(os.listdir(local_3d_dir))
                new_3d_files = current_3d - initial_3d

                # Check for new STEP files
                step_files = [f for f in new_3d_files if f.lower().endswith(('.step', '.stp'))]

                if step_files:
                    step_file = step_files[0]
                    file_path = os.path.join(local_3d_dir, step_file)
                    file_time = os.path.getmtime(file_path)

                    # Only accept files created after we started
                    if file_time > start_time:
                        print(f"   ✅ NEW STEP file found in 3d folder: {step_file}")

                        # Rename to standard format with proper part number
                        if "LM358N" in step_file.upper():
                            base_name = step_file.replace(" (1)", "").replace(" ", "_")
                            new_name = f"ultralibrarian_{manufacturer}_{base_name}"
                        else:
                            new_name = f"ultralibrarian_{manufacturer}_{part_number}.step"

                        if step_file != new_name:
                            old_path = os.path.join(local_3d_dir, step_file)
                            new_path = os.path.join(local_3d_dir, new_name)
                            os.rename(old_path, new_path)
                            print(f"   ✅ Renamed to: {new_name}")

                        return True

            # Backup check: Downloads folder for new files
            if os.path.exists(downloads_dir):
                current_downloads = set(os.listdir(downloads_dir))
                new_files = current_downloads - initial_downloads

                # Look for STEP files with timestamp check
                step_files = []
                zip_files = []

                for f in new_files:
                    file_path = os.path.join(downloads_dir, f)
                    file_time = os.path.getmtime(file_path)

                    # Only accept files created after we started
                    if file_time > start_time:
                        if f.lower().endswith(('.step', '.stp')):
                            step_files.append(f)
                        elif f.lower().endswith('.zip'):
                            zip_files.append(f)

                if step_files:
                    step_file = step_files[0]
                    print(f"   ✅ NEW STEP file found in Downloads: {step_file}")

                    # Move to 3d folder with proper naming
                    src_path = os.path.join(downloads_dir, step_file)

                    # Extract the actual part number from the filename or use original
                    if "LM358N" in step_file.upper():
                        # Use the original filename but clean it up
                        base_name = step_file.replace(" (1)", "").replace(" ", "_")
                        new_name = f"ultralibrarian_{manufacturer}_{base_name}"
                    else:
                        new_name = f"ultralibrarian_{manufacturer}_{part_number}.step"

                    dst_path = os.path.join(local_3d_dir, new_name)

                    try:
                        import shutil
                        shutil.move(src_path, dst_path)
                        print(f"   ✅ Moved to 3d folder: {new_name}")
                        return True
                    except Exception as e:
                        print(f"   ⚠️ Error moving file: {e}")
                        # Try copying instead
                        try:
                            shutil.copy2(src_path, dst_path)
                            print(f"   ✅ Copied to 3d folder: {new_name}")
                            return True
                        except Exception as e2:
                            print(f"   ❌ Error copying file: {e2}")

                elif zip_files:
                    zip_file = zip_files[0]
                    print(f"   📦 ZIP file found: {zip_file}")

                    # Extract ZIP file
                    import zipfile
                    zip_path = os.path.join(downloads_dir, zip_file)

                    try:
                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall(local_3d_dir)

                        # Check for extracted STEP files
                        current_3d = set(os.listdir(local_3d_dir))
                        extracted_files = current_3d - initial_3d
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]

                        if step_files:
                            step_file = step_files[0]

                            # Use proper part number naming
                            if "LM358N" in step_file.upper():
                                base_name = step_file.replace(" (1)", "").replace(" ", "_")
                                new_name = f"ultralibrarian_{manufacturer}_{base_name}"
                            else:
                                new_name = f"ultralibrarian_{manufacturer}_{part_number}.step"

                            old_path = os.path.join(local_3d_dir, step_file)
                            new_path = os.path.join(local_3d_dir, new_name)

                            os.rename(old_path, new_path)
                            print(f"   ✅ Extracted and renamed: {new_name}")
                            return True
                    except Exception as e:
                        print(f"   ❌ Error extracting ZIP: {e}")

            # Also check if file appeared directly in 3d folder
            if os.path.exists(local_3d_dir):
                current_3d = set(os.listdir(local_3d_dir))
                new_3d_files = current_3d - initial_3d
                step_files = [f for f in new_3d_files if f.lower().endswith(('.step', '.stp'))]

                if step_files:
                    step_file = step_files[0]
                    print(f"   ✅ STEP file in 3d folder: {step_file}")

                    # Rename to standard format with proper part number
                    if "LM358N" in step_file.upper():
                        base_name = step_file.replace(" (1)", "").replace(" ", "_")
                        new_name = f"ultralibrarian_{manufacturer}_{base_name}"
                    else:
                        new_name = f"ultralibrarian_{manufacturer}_{part_number}.step"

                    old_path = os.path.join(local_3d_dir, step_file)
                    new_path = os.path.join(local_3d_dir, new_name)

                    if old_path != new_path:
                        os.rename(old_path, new_path)
                        print(f"   ✅ Renamed to: {new_name}")

                    return True

            print(f"   ⏳ Checking... ({(i+1)*5}/120 seconds)")

        print("   ❌ No files downloaded after 2 minutes")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print("\n🔧 Closing browser automatically...")
        time.sleep(5)  # Brief pause to see final state
        driver.quit()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 SUCCESS: File downloaded!")
    else:
        print("\n❌ FAILED: No file downloaded")
