#!/usr/bin/env python3
"""
STEP BY STEP ULTRALIBRARIAN AUTOMATION
======================================
Goes through UltraLibrarian step by step, showing you each screen.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

class StepByStepUltraLibrarian:
    def __init__(self):
        self.base_url = 'https://www.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("Step-by-Step UltraLibrarian Automation Ready!")

    def setup_driver(self):
        """Setup Chrome driver with stealth options"""
        chrome_options = Options()

        # Stealth options to avoid detection
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')

        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3D'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)

            # Execute script to hide automation
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            return driver
        except Exception as e:
            print(f"Chrome driver setup failed: {e}")
            return None

    def step_by_step_search(self, manufacturer, part_number):
        """Go through UltraLibrarian step by step"""
        print(f"\nSTEP-BY-STEP ULTRALIBRARIAN SEARCH")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("=" * 50)
        
        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            # STEP 1: Load homepage
            print(f"\n🔸 STEP 1: Loading UltraLibrarian homepage...")
            driver.get(self.base_url)
            time.sleep(3)
            print(f"   ✅ Homepage loaded: {driver.title}")
            
            # STEP 2: Find search box
            print(f"\n🔸 STEP 2: Looking for search box...")
            
            # Try multiple search box selectors
            search_selectors = [
                "input[type='search']",
                "input[name*='search']",
                "input[placeholder*='search']",
                "input[placeholder*='Search']",
                "input[id*='search']",
                "input[class*='search']",
                ".search-input",
                "#search",
                "input[type='text']"
            ]
            
            search_input = None
            for selector in search_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        search_input = elements[0]
                        print(f"   ✅ Found search box with selector: {selector}")
                        break
                except:
                    continue
            
            if not search_input:
                print(f"   ❌ No search box found!")
                input("Press Enter to continue and inspect manually...")
                return None
            
            # STEP 3: Enter search term
            print(f"\n🔸 STEP 3: Entering search term...")
            search_term = f"{manufacturer} {part_number}"
            print(f"   Searching for: '{search_term}'")
            
            search_input.clear()
            search_input.send_keys(search_term)
            print(f"   ✅ Search term entered")
            
            # STEP 4: Submit search
            print(f"\n🔸 STEP 4: Submitting search...")
            
            # Try to find submit button
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('Search')",
                ".search-button",
                ".search-btn",
                "button[class*='search']"
            ]
            
            submitted = False
            for selector in submit_selectors:
                try:
                    buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                    if buttons:
                        buttons[0].click()
                        print(f"   ✅ Clicked search button: {selector}")
                        submitted = True
                        break
                except:
                    continue
            
            if not submitted:
                # Try pressing Enter
                try:
                    search_input.send_keys(Keys.RETURN)
                    print(f"   ✅ Pressed Enter to search")
                    submitted = True
                except:
                    print(f"   ❌ Could not submit search")
            
            if not submitted:
                print(f"   ❌ Search submission failed")
                input("Press Enter to continue and try manually...")
                return None
            
            # STEP 5: Wait for results
            print(f"\n🔸 STEP 5: Waiting for search results...")
            time.sleep(5)
            
            current_url = driver.current_url
            page_title = driver.title
            print(f"   Current URL: {current_url}")
            print(f"   Page Title: {page_title}")
            
            # STEP 6: Analyze results page
            print(f"\n🔸 STEP 6: Analyzing search results...")
            
            # Look for result elements
            result_selectors = [
                "a[href*='part']",
                "a[href*='component']",
                ".search-result",
                ".part-result",
                "tr td a",
                "table a",
                "div[class*='result'] a"
            ]
            
            all_results = []
            for selector in result_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        text = element.text.strip()
                        if href and text and len(text) > 2:
                            all_results.append({
                                'selector': selector,
                                'href': href,
                                'text': text[:60] + "..." if len(text) > 60 else text
                            })
                except:
                    continue
            
            if all_results:
                print(f"   ✅ Found {len(all_results)} potential results:")
                for i, result in enumerate(all_results[:10]):  # Show first 10
                    print(f"      {i+1}. {result['text']}")
                    print(f"         URL: {result['href']}")
                    print(f"         Selector: {result['selector']}")
                    print()
            else:
                print(f"   ❌ No results found")
            
            # STEP 7: Keep browser open for manual inspection
            print(f"\n🔸 STEP 7: Browser ready for manual inspection")
            print(f"   🌐 You can now see the search results page")
            print(f"   🔍 Look for your part: {part_number}")
            print(f"   📋 Click on a part to see the next screen")
            print(f"   ⏳ Browser will stay open...")
            print(f"   💡 Press Ctrl+C in terminal when done")
            
            # Keep browser open
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print(f"\n👋 Closing browser...")
                
        except Exception as e:
            print(f"Error: {e}")
        finally:
            driver.quit()

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python step_by_step_ultralibrarian.py \"Manufacturer\" \"Part Number\"")
        print("\nExample: python step_by_step_ultralibrarian.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    scraper = StepByStepUltraLibrarian()
    scraper.step_by_step_search(manufacturer, part_number)

if __name__ == "__main__":
    main()
