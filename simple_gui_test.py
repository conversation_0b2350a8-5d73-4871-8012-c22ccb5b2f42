#!/usr/bin/env python3
"""
Simple GUI test to verify tkinter is working properly
"""

import tkinter as tk
from tkinter import ttk, messagebox

def test_button():
    messagebox.showinfo("Test", "GUI is working! ✅\n\nIf you can see this, tkinter is fine.\nThe issue might be with the main program.")

def main():
    print("🧪 Starting simple GUI test...")
    
    # Create main window
    root = tk.Tk()
    root.title("GUI Test - Component Finder")
    root.geometry("500x300")
    root.configure(bg='lightblue')
    
    # Make sure window appears on top
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    # Add content
    title_label = tk.Label(root, text="🧪 GUI Test Window", 
                          font=("Arial", 16, "bold"), 
                          bg='lightblue', fg='darkblue')
    title_label.pack(pady=20)
    
    info_label = tk.Label(root, text="If you can see this window clearly,\ntkinter is working properly!", 
                         font=("Arial", 12), 
                         bg='lightblue', fg='black')
    info_label.pack(pady=10)
    
    test_button = tk.Button(root, text="Click to Test", 
                           command=test_button,
                           font=("Arial", 12),
                           bg='green', fg='white',
                           padx=20, pady=10)
    test_button.pack(pady=20)
    
    status_label = tk.Label(root, text="Status: GUI test window is visible", 
                           font=("Arial", 10), 
                           bg='lightblue', fg='darkgreen')
    status_label.pack(pady=10)
    
    # Add instructions
    instructions = tk.Label(root, 
                           text="If this window shows properly:\n• Close this window\n• Try the main program again\n\nIf this window is also blank/white:\n• There's a tkinter display issue", 
                           font=("Arial", 9), 
                           bg='lightblue', fg='darkred',
                           justify=tk.LEFT)
    instructions.pack(pady=10)
    
    print("✅ GUI test window should be visible now")
    print("📋 Window title: 'GUI Test - Component Finder'")
    print("🎨 Background: Light blue")
    print("📐 Size: 500x300")
    
    # Start the GUI
    root.mainloop()
    
    print("🏁 GUI test completed")

if __name__ == "__main__":
    main()
