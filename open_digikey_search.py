#!/usr/bin/env python3
"""
Open Digi-Key search in browser to see what the actual search looks like
"""

import webbrowser
from urllib.parse import quote

def open_digikey_search():
    part_number = "435151014845"
    
    print("🌐 Opening Digi-Key search in browser...")
    print(f"🔍 Searching for: {part_number}")
    
    # The same URLs our program uses
    search_urls = [
        f"https://www.digikey.com/en/products/result?keywords={quote(part_number)}&stock=1",
        f"https://www.digikey.com/products/en?keywords={quote(part_number)}",
        f"https://www.digikey.com/en/products/result?keywords={quote(part_number)}"
    ]
    
    print("\n📋 URLs that will be tested:")
    for i, url in enumerate(search_urls, 1):
        print(f"{i}. {url}")
    
    print(f"\n🚀 Opening first URL in browser...")
    webbrowser.open(search_urls[0])
    
    print("✅ Browser opened!")
    print("👀 Check if you can see the WURTH part 435151014845")
    print("📄 Look for datasheet links")
    print("🏭 Look for manufacturer information")

if __name__ == "__main__":
    open_digikey_search()
