#!/usr/bin/env python3
"""
ULTRALIBRARIAN GENERIC AUTOMATION
==================================
Generic UltraLibrarian automation for any electronic component.

Usage: python ultralibrarian.py "PART_NUMBER"
Example: python ultralibrarian.py "LM358N"
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome with download preferences"""
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def ultralibrarian_automation(part_number):
    """
    Generic UltraLibrarian automation for any part number
    
    Args:
        part_number (str): Electronic component part number
        
    Returns:
        str: Downloaded STEP filename or None if failed
    """
    print(f"🎯 ULTRALIBRARIAN AUTOMATION: {part_number}")
    print("=" * 60)
    
    driver = setup_driver()
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # STEP 1: Load UltraLibrarian
        print("\n🔸 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print(f"✅ Loaded: {driver.title}")
        
        # STEP 2: Search for part
        print(f"\n🔸 STEP 2: Searching for {part_number}...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_box = None
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                search_box = inp
                break
        
        if not search_box:
            print("❌ No search box found!")
            return None
        
        search_box.clear()
        search_box.send_keys(part_number)
        search_box.send_keys(Keys.RETURN)
        print("✅ Search submitted")
        time.sleep(10)
        
        # STEP 3: Find and click first relevant part
        print(f"\n🔸 STEP 3: Looking for {part_number} in results...")
        links = driver.find_elements(By.TAG_NAME, "a")
        part_link = None
        
        # Look for exact match first, then partial match
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (part_number.lower() in text.lower() or part_number.lower() in href.lower()) and \
                   'details' in href.lower() and 'login' not in href.lower() and link.is_displayed():
                    part_link = link
                    print(f"✅ Found part: {text}")
                    break
            except:
                continue
        
        if not part_link:
            print(f"❌ No {part_number} found in results!")
            return None
        
        part_link.click()
        print("✅ Clicked on part")
        time.sleep(8)
        
        # STEP 4: Click Download Now
        print("\n🔸 STEP 4: Clicking Download Now...")
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if not download_btns:
            print("❌ No Download Now button found!")
            return None
        
        driver.execute_script("arguments[0].click();", download_btns[0])
        print("✅ Clicked Download Now")
        time.sleep(5)
        
        # STEP 5: Click 3D CAD Model
        print("\n🔸 STEP 5: Clicking 3D CAD Model...")
        cad_selectors = [
            "//button[contains(text(), '3D CAD Model')]",
            "//a[contains(text(), '3D CAD Model')]",
            "//button[contains(text(), '3D Model')]",
            "//a[contains(text(), '3D Model')]"
        ]
        
        cad_element = None
        for selector in cad_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    cad_element = elem
                    break
            if cad_element:
                break
        
        if not cad_element:
            print("❌ No 3D CAD Model option found!")
            return None
        
        driver.execute_script("arguments[0].click();", cad_element)
        print("✅ Clicked 3D CAD Model")
        time.sleep(5)
        
        # Check for new window
        if len(driver.window_handles) > 1:
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        # STEP 6: Select STEP format
        print("\n🔸 STEP 6: Selecting STEP format...")
        step_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP')]")
        step_element = None
        for elem in step_elements:
            if elem.is_displayed():
                step_element = elem
                break
        
        if step_element:
            driver.execute_script("arguments[0].click();", step_element)
            print("✅ Selected STEP format")
            time.sleep(3)
        
        # STEP 7: Click download
        print("\n🔸 STEP 7: Clicking download...")
        download_elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')]")
        download_element = None
        for elem in download_elements:
            if elem.is_displayed():
                download_element = elem
                break
        
        if download_element:
            driver.execute_script("arguments[0].click();", download_element)
            print("✅ Clicked download")
            time.sleep(5)
        
        # STEP 8: Handle login if needed
        print("\n🔸 STEP 8: Checking for login...")

        # Check if we're on login screen (URL contains sso or login)
        current_url = driver.current_url.lower()
        login_required = ('sso' in current_url or 'login' in current_url)

        # Also check for login form elements
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email'], input[placeholder*='email'], input[name*='Email'], input[name*='username'], input[name*='Username']")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")

        if email_inputs or password_inputs or login_required:
            print("🔐 Login required, attempting login...")
            
            try:
                with open('component_site_credentials.json', 'r') as f:
                    credentials = json.load(f)
                
                email = credentials['UltraLibrarian']['email']
                password = credentials['UltraLibrarian']['password']
                
                # Enter email
                email_input = None
                for inp in email_inputs:
                    if inp.is_displayed() and inp.is_enabled():
                        email_input = inp
                        break

                if not email_input:
                    text_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='text']")
                    for inp in text_inputs:
                        if inp.is_displayed() and inp.is_enabled():
                            email_input = inp
                            break

                if email_input:
                    email_input.clear()
                    email_input.send_keys(email)
                    print("✅ Entered email")
                    time.sleep(2)

                # Enter password
                password_input = None
                for inp in password_inputs:
                    if inp.is_displayed() and inp.is_enabled():
                        password_input = inp
                        break

                if password_input:
                    password_input.clear()
                    password_input.send_keys(password)
                    print("✅ Entered password")
                    time.sleep(2)
                
                # Submit login - try multiple methods
                login_selectors = [
                    "button[type='submit']",
                    "input[type='submit']",
                    "//button[contains(text(), 'Login')]",
                    "//button[contains(text(), 'Sign In')]",
                    "//button[contains(text(), 'Log In')]"
                ]

                login_clicked = False
                for selector in login_selectors:
                    try:
                        if selector.startswith("//"):
                            buttons = driver.find_elements(By.XPATH, selector)
                        else:
                            buttons = driver.find_elements(By.CSS_SELECTOR, selector)

                        for btn in buttons:
                            if btn.is_displayed() and btn.is_enabled():
                                driver.execute_script("arguments[0].click();", btn)
                                print("✅ Clicked login button")
                                login_clicked = True
                                break

                        if login_clicked:
                            break
                    except:
                        continue

                if not login_clicked:
                    # Try pressing Enter on password field
                    password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
                    if password_inputs:
                        password_inputs[0].send_keys(Keys.RETURN)
                        print("✅ Pressed Enter to submit login")

                time.sleep(10)
                print(f"After login URL: {driver.current_url}")
                print("✅ Login completed - back at previous screen")

                # CORRECT SEQUENCE: 3D CAD -> STEP -> DOWNLOAD
                print("\n🔸 CORRECT SEQUENCE: 3D CAD -> STEP -> DOWNLOAD")

                # STEP 1: Click 3D CAD again
                print("\n🔸 POST-LOGIN STEP 1: Clicking 3D CAD again...")
                cad_btns_2 = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')] | //a[contains(text(), '3D CAD Model')] | //a[contains(text(), '3D Model')]")

                cad_clicked = False
                for btn in cad_btns_2:
                    if btn.is_displayed() and btn.is_enabled():
                        driver.execute_script("arguments[0].click();", btn)
                        print("✅ Clicked 3D CAD Model again")
                        cad_clicked = True
                        time.sleep(5)
                        break

                if not cad_clicked:
                    print("⚠️ No 3D CAD button found, continuing...")

                # Check for new window again
                if len(driver.window_handles) > 1:
                    print("✅ Switching to latest window...")
                    driver.switch_to.window(driver.window_handles[-1])
                    time.sleep(3)

                # STEP 2: Select STEP again and VERIFY it stays selected
                print("\n🔸 POST-LOGIN STEP 2: Selecting STEP format again...")
                step_elements_2 = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP')]")

                step_clicked = False
                for elem in step_elements_2:
                    if elem.is_displayed():
                        driver.execute_script("arguments[0].click();", elem)
                        print("✅ Clicked STEP format")
                        step_clicked = True
                        time.sleep(2)

                        # VERIFY STEP is still selected
                        print("🔸 Verifying STEP remains selected...")
                        time.sleep(3)  # Wait longer to see if it gets deselected

                        # Check if STEP is still active/selected
                        try:
                            class_attr = elem.get_attribute('class') or ''
                            selected_attr = elem.get_attribute('selected') or ''
                            checked_attr = elem.get_attribute('checked') or ''

                            print(f"STEP element status: class='{class_attr}' selected='{selected_attr}' checked='{checked_attr}'")

                            # If it got deselected, click it again
                            if 'active' not in class_attr.lower() and not selected_attr and not checked_attr:
                                print("⚠️ STEP got deselected! Clicking again...")
                                driver.execute_script("arguments[0].click();", elem)
                                time.sleep(2)
                                print("✅ Re-selected STEP format")
                        except Exception as e:
                            print(f"⚠️ Could not verify STEP status: {e}")

                        break

                if not step_clicked:
                    print("⚠️ No STEP element found, continuing...")

                # STEP 3: Click Download automatically
                print("\n🔸 POST-LOGIN STEP 3: Clicking Download...")
                download_elements_2 = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')] | //input[@type='submit']")

                download_clicked = False
                for elem in download_elements_2:
                    if elem.is_displayed() and elem.is_enabled():
                        driver.execute_script("arguments[0].click();", elem)
                        print("✅ Clicked Download")
                        download_clicked = True
                        time.sleep(5)
                        break

                if not download_clicked:
                    print("⚠️ No download button found")

                # MONITOR FOR DOWNLOADS
                print("\n🔸 MONITORING FOR DOWNLOADS...")

                # Monitor for downloads
                for i in range(30):  # Wait up to 2.5 minutes for download
                    time.sleep(5)
                    current_files = set(os.listdir('3D'))
                    new_files = current_files - initial_files

                    if new_files:
                        print(f"🎉 NEW FILES DETECTED: {list(new_files)}")

                        # Check for STEP files
                        step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                        if step_files:
                            original_file = step_files[0]
                            clean_part = part_number.replace('/', '-').replace('\\', '-')
                            new_name = f'ultra-{clean_part.lower()}.step'

                            original_path = os.path.join('3D', original_file)
                            new_path = os.path.join('3D', new_name)

                            try:
                                os.rename(original_path, new_path)
                                print(f"✅ RENAMED: {original_file} -> {new_name}")
                                return new_name
                            except Exception as e:
                                print(f"⚠️ Rename failed: {e}")
                                return original_file

                        # Check for ZIP files
                        zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                        if zip_files:
                            print(f"📦 ZIP FILE: {zip_files[0]}")
                            try:
                                import zipfile
                                zip_path = os.path.join('3D', zip_files[0])

                                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                                    zip_ref.extractall('3D')
                                print(f"✅ Extracted ZIP file: {zip_files[0]}")

                                time.sleep(2)
                                final_files = set(os.listdir('3D'))
                                extracted_files = final_files - current_files - {zip_files[0]}
                                step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]

                                if step_files:
                                    original_file = step_files[0]
                                    clean_part = part_number.replace('/', '-').replace('\\', '-')
                                    new_name = f'ultra-{clean_part.lower()}.step'

                                    original_path = os.path.join('3D', original_file)
                                    new_path = os.path.join('3D', new_name)

                                    try:
                                        os.rename(original_path, new_path)
                                        print(f"✅ EXTRACTED AND RENAMED: {original_file} -> {new_name}")
                                        return new_name
                                    except Exception as e:
                                        print(f"⚠️ Rename failed: {e}")
                                        return original_file
                            except Exception as e:
                                print(f"❌ Error extracting ZIP: {e}")

                    print(f"  Checking... ({(i+1)*5}/150 seconds)")

                print("⏳ No files downloaded after 2.5 minutes")
                return None
                
            except Exception as e:
                print(f"⚠️ Login failed: {e}")
        
        # STEP 9: Monitor for downloads
        print("\n🔸 STEP 9: Monitoring for downloads...")
        
        for i in range(30):  # Monitor for 2.5 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 NEW FILES: {list(new_files)}")
                
                # Check for STEP files
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    original_file = step_files[0]
                    # Create standardized filename
                    clean_part = part_number.replace('/', '-').replace('\\', '-')
                    new_name = f'ultra-{clean_part.lower()}.step'
                    
                    original_path = os.path.join('3D', original_file)
                    new_path = os.path.join('3D', new_name)
                    
                    try:
                        os.rename(original_path, new_path)
                        print(f"✅ RENAMED: {original_file} -> {new_name}")
                        return new_name
                    except Exception as e:
                        print(f"⚠️ Rename failed: {e}")
                        return original_file
                
                # Check for ZIP files
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP FILE: {zip_files[0]}")
                    try:
                        import zipfile
                        zip_path = os.path.join('3D', zip_files[0])

                        # Extract ZIP file
                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        print(f"✅ Extracted ZIP file: {zip_files[0]}")

                        # Check for extracted STEP files
                        time.sleep(2)  # Give time for extraction
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files - {zip_files[0]}  # Exclude the ZIP file itself
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]

                        print(f"Extracted files: {list(extracted_files)}")
                        print(f"STEP files found: {step_files}")

                        if step_files:
                            original_file = step_files[0]
                            clean_part = part_number.replace('/', '-').replace('\\', '-')
                            new_name = f'ultra-{clean_part.lower()}.step'

                            original_path = os.path.join('3D', original_file)
                            new_path = os.path.join('3D', new_name)

                            try:
                                os.rename(original_path, new_path)
                                print(f"✅ EXTRACTED AND RENAMED: {original_file} -> {new_name}")
                                return new_name
                            except Exception as e:
                                print(f"⚠️ Rename failed: {e}")
                                return original_file
                        else:
                            print("⚠️ No STEP files found in extracted content")
                    except Exception as e:
                        print(f"❌ Error extracting ZIP: {e}")
            
            print(f"  Checking... ({(i+1)*5}/150 seconds)")
        
        print("⏳ No files downloaded")
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        driver.quit()

def main():
    """Main function to handle command line arguments"""
    if len(sys.argv) != 2:
        print("Usage: python ultralibrarian.py \"PART_NUMBER\"")
        print("Example: python ultralibrarian.py \"LM358N\"")
        sys.exit(1)
    
    part_number = sys.argv[1]
    result = ultralibrarian_automation(part_number)
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded to 3D/ folder!")
        sys.exit(0)
    else:
        print(f"\n❌ FAILED: Could not download STEP file for {part_number}")
        sys.exit(1)

if __name__ == "__main__":
    main()
