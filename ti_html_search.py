#!/usr/bin/env python3
"""
Use HTML parsing approach that worked before
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time
import re

def ti_html_search():
    print("TI HTML SEARCH")
    print("=" * 30)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Open TI.com
        print("Opening ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(15)
        
        # Get HTML and analyze it
        print("Getting HTML...")
        html = driver.page_source
        print(f"HTML length: {len(html)} characters")
        
        # Look for search-related elements in HTML
        search_patterns = re.findall(r'<[^>]*(?:search|Search)[^>]*>', html, re.IGNORECASE)
        print(f"Found {len(search_patterns)} search-related elements")
        
        # Look for input elements
        input_patterns = re.findall(r'<input[^>]*>', html, re.IGNORECASE)
        print(f"Found {len(input_patterns)} input elements in HTML")
        
        # Show first few inputs
        for i, inp in enumerate(input_patterns[:3]):
            print(f"Input {i+1}: {inp}")
        
        # Look specifically for searchboxheader
        if 'searchboxheader' in html.lower():
            print("✅ Found 'searchboxheader' in HTML!")
            
            # Use JavaScript to interact with it
            print("Using JavaScript to search...")
            
            js_commands = [
                # Try to set value and submit
                f"document.querySelector('#searchboxheader input').value = 'LM358N';",
                f"document.querySelector('#searchboxheader input').dispatchEvent(new Event('input'));",
                f"document.querySelector('#searchboxheader form').submit();",
                
                # Alternative: direct navigation
                f"window.location.href = 'https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm=LM358N';"
            ]
            
            for cmd in js_commands:
                try:
                    print(f"Executing: {cmd}")
                    driver.execute_script(cmd)
                    time.sleep(3)
                except Exception as e:
                    print(f"Error: {e}")
            
            time.sleep(10)
            print(f"Final URL: {driver.current_url}")
            
            if "LM358N" in driver.page_source.upper():
                print("✅ SUCCESS: Found LM358N!")
            else:
                print("❌ No LM358N found")
        else:
            print("❌ No 'searchboxheader' found in HTML")
        
    finally:
        input("Press Enter to close...")
        driver.quit()

if __name__ == "__main__":
    ti_html_search()
