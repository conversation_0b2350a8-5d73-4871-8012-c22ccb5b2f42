#!/usr/bin/env python3
"""
Smart Component Finder - Handles Different Manufacturer Website Structures

This addresses the challenge that every manufacturer website is different by using:
1. Manufacturer-specific adapters
2. Multiple search strategies per manufacturer  
3. Fallback methods for unknown manufacturers
4. Package-type based 3D model searching
5. Web search integration for difficult cases

Usage:
    python smart_component_finder.py "Diodes Inc" "APX803L20-30SA-7"
"""

import requests
from bs4 import BeautifulSoup
import re
import os
import json
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ManufacturerAdapter:
    """Base class for manufacturer-specific adapters"""
    
    def __init__(self, session):
        self.session = session
    
    def search_datasheet(self, part_number):
        """Search for datasheet - to be implemented by subclasses"""
        raise NotImplementedError
    
    def extract_package_type(self, part_number, datasheet_content=None, product_html=None):
        """Extract package type - to be implemented by subclasses"""
        raise NotImplementedError
    
    def search_3d_models(self, part_number, package_type):
        """Search for 3D models - to be implemented by subclasses"""
        raise NotImplementedError

class DiodesAdapter(ManufacturerAdapter):
    """Adapter for Diodes Inc website"""
    
    def __init__(self, session):
        super().__init__(session)
        self.base_url = "https://www.diodes.com"
    
    def search_datasheet(self, part_number):
        """Search for Diodes datasheet"""
        # Generate part variations
        base_part = part_number.split('-')[0]
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
        
        variations = [part_family, base_part, part_number]
        
        datasheet_patterns = [
            "https://www.diodes.com/assets/Datasheets/{}.pdf",
            "https://www.diodes.com/datasheet/download/{}.pdf"
        ]
        
        for pattern in datasheet_patterns:
            for variation in variations:
                try:
                    url = pattern.format(variation)
                    logger.info(f"📄 Testing: {url}")
                    
                    response = self.session.head(url, timeout=10)
                    if response.status_code == 200:
                        logger.info(f"✅ Found datasheet: {url}")
                        return url
                        
                except Exception as e:
                    logger.debug(f"Datasheet test failed: {e}")
                    continue
        
        return None
    
    def extract_package_type(self, part_number, datasheet_content=None, product_html=None):
        """Extract package type for Diodes parts"""
        
        # Method 1: Get from product page
        base_part = part_number.split('-')[0]
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
        
        try:
            product_url = f"https://www.diodes.com/part/view/{part_family}"
            logger.info(f"📦 Getting package info from: {product_url}")
            
            response = self.session.get(product_url, timeout=30)
            if response.status_code == 200:
                # Look for the specific part number and its package
                html = response.text
                
                # Find the part number in the table and extract package
                soup = BeautifulSoup(html, 'html.parser')
                
                # Look for table rows containing the part number
                for row in soup.find_all('tr'):
                    row_text = row.get_text()
                    if part_number in row_text:
                        # Look for package information in the same row
                        cells = row.find_all(['td', 'th'])
                        for cell in cells:
                            cell_text = cell.get_text().strip()
                            # Common Diodes packages
                            if cell_text in ['SOT23', 'SOT-23', 'SOT323', 'SOT-323', 'SOT25', 'SOT-25', 'SC59', 'SC-59']:
                                logger.info(f"📦 Found package from product page: {cell_text}")
                                return cell_text
                
                # Fallback: search in general HTML content
                html_lower = html.lower()
                packages = ['sot-23', 'sot23', 'sot-323', 'sot323', 'sot-25', 'sot25', 'sc-59', 'sc59']
                for pkg in packages:
                    if pkg in html_lower:
                        clean_pkg = pkg.replace('-', '').upper()
                        if clean_pkg.startswith('SOT'):
                            clean_pkg = 'SOT-' + clean_pkg[3:]
                        elif clean_pkg.startswith('SC'):
                            clean_pkg = 'SC-' + clean_pkg[2:]
                        logger.info(f"📦 Found package (fallback): {clean_pkg}")
                        return clean_pkg
                        
        except Exception as e:
            logger.error(f"Error getting package info: {e}")
        
        return None
    
    def search_3d_models(self, part_number, package_type):
        """Search for 3D models on Diodes website"""
        models = []
        
        if not package_type:
            return models
        
        # Strategy 1: Direct package-based URLs
        package_variations = [
            package_type,
            package_type.replace('-', ''),
            package_type.lower(),
            package_type.lower().replace('-', ''),
            package_type.upper(),
            package_type.upper().replace('-', '')
        ]
        
        model_url_patterns = [
            "https://www.diodes.com/assets/3D-models/{}.step",
            "https://www.diodes.com/assets/3D-models/{}.stp", 
            "https://www.diodes.com/assets/Package-3D/{}.step",
            "https://www.diodes.com/assets/cad-models/{}.step",
            "https://www.diodes.com/assets/3D-package-models/{}.step",
            "https://www.diodes.com/assets/step/{}.step",
            "https://www.diodes.com/downloads/3d/{}.step"
        ]
        
        logger.info(f"🎯 Searching for {package_type} 3D models...")
        
        for pattern in model_url_patterns:
            for pkg_var in package_variations:
                try:
                    url = pattern.format(pkg_var)
                    logger.info(f"   Testing: {url}")
                    
                    response = self.session.head(url, timeout=10)
                    if response.status_code == 200:
                        logger.info(f"✅ Found 3D model: {url}")
                        return [url]  # Return the URL for download
                        
                except Exception as e:
                    logger.debug(f"3D model URL failed: {e}")
                    continue
        
        # Strategy 2: Search in product page for CAD model links
        try:
            base_part = part_number.split('-')[0]
            part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part
            product_url = f"https://www.diodes.com/part/view/{part_family}"
            
            response = self.session.get(product_url, timeout=30)
            if response.status_code == 200:
                # Look for any STEP/STP links
                step_pattern = r'href="([^"]*\.(?:step|stp)[^"]*)"'
                matches = re.finditer(step_pattern, response.text, re.IGNORECASE)
                
                for match in matches:
                    link = match.group(1)
                    full_url = urljoin(product_url, link)
                    logger.info(f"✅ Found 3D model in product page: {full_url}")
                    return [full_url]
                    
        except Exception as e:
            logger.error(f"Product page 3D search failed: {e}")
        
        return models

class SmartComponentFinder:
    """Main component finder that uses manufacturer-specific adapters"""
    
    def __init__(self, download_dir="files-download"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = self._create_session()
        
        # Initialize manufacturer adapters
        self.adapters = {
            "diodes inc": DiodesAdapter(self.session)
        }
    
    def _create_session(self):
        """Create HTTP session"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        return session
    
    def download_file(self, url, filename=None):
        """Download a file"""
        try:
            logger.info(f"📥 Downloading: {url}")
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            if not filename:
                filename = os.path.basename(urlparse(url).path)
                if not filename or '.' not in filename:
                    if '.pdf' in url.lower():
                        filename = f"datasheet_{int(time.time())}.pdf"
                    elif any(ext in url.lower() for ext in ['.step', '.stp']):
                        filename = f"3d_model_{int(time.time())}.step"
            
            filepath = self.download_dir / filename
            
            # Avoid overwriting
            counter = 1
            original_filepath = filepath
            while filepath.exists():
                name, ext = original_filepath.stem, original_filepath.suffix
                filepath = self.download_dir / f"{name}_{counter}{ext}"
                counter += 1
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"✅ Downloaded: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            return None
    
    def search_component(self, manufacturer, part_number):
        """Main search method"""
        results = {
            'manufacturer': manufacturer,
            'part_number': part_number,
            'package_type': None,
            'datasheet_file': None,
            '3d_model_files': [],
            'success': False
        }
        
        manufacturer_lower = manufacturer.lower()
        
        # Check if we have a specific adapter for this manufacturer
        if manufacturer_lower in self.adapters:
            adapter = self.adapters[manufacturer_lower]
            logger.info(f"✅ Using specific adapter for {manufacturer}")
        else:
            logger.warning(f"⚠️ No specific adapter for {manufacturer}, using generic methods")
            return self._generic_search(manufacturer, part_number)
        
        # Step 1: Search for datasheet
        datasheet_url = adapter.search_datasheet(part_number)
        if datasheet_url:
            datasheet_file = self.download_file(datasheet_url, f"{part_number}_datasheet.pdf")
            results['datasheet_file'] = datasheet_file
        
        # Step 2: Extract package type
        package_type = adapter.extract_package_type(part_number)
        results['package_type'] = package_type
        
        # Step 3: Search for 3D models
        if package_type:
            model_urls = adapter.search_3d_models(part_number, package_type)
            for url in model_urls:
                model_file = self.download_file(url, f"{package_type}_3d_model.step")
                if model_file:
                    results['3d_model_files'].append(model_file)
        
        results['success'] = bool(results['datasheet_file'] or results['3d_model_files'])
        return results
    
    def _generic_search(self, manufacturer, part_number):
        """Generic search for unknown manufacturers"""
        # This would implement web search and common patterns
        logger.info("Generic search not fully implemented")
        return {
            'manufacturer': manufacturer,
            'part_number': part_number,
            'package_type': None,
            'datasheet_file': None,
            '3d_model_files': [],
            'success': False
        }

def main():
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description='Smart Component Finder')
    parser.add_argument('manufacturer', help='Manufacturer name')
    parser.add_argument('part_number', help='Part number')
    parser.add_argument('--download-dir', default='files-download', help='Download directory')
    
    args = parser.parse_args()
    
    finder = SmartComponentFinder(download_dir=args.download_dir)
    results = finder.search_component(args.manufacturer, args.part_number)
    
    print("\n" + "="*60)
    print("SMART COMPONENT FINDER RESULTS")
    print("="*60)
    print(f"Manufacturer: {results['manufacturer']}")
    print(f"Part Number: {results['part_number']}")
    print(f"Package Type: {results['package_type'] or 'Unknown'}")
    print()
    
    if results['datasheet_file']:
        print(f"✅ Datasheet: {results['datasheet_file']}")
    else:
        print("❌ Datasheet: Not found")
    
    if results['3d_model_files']:
        print(f"✅ 3D Models: {len(results['3d_model_files'])} found")
        for model in results['3d_model_files']:
            print(f"  - {model}")
    else:
        print("❌ 3D Models: Not found")
    
    print()
    if results['success']:
        print("🎉 Search completed successfully!")
    else:
        print("⚠️ Search completed with limited results")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        # Test mode
        finder = SmartComponentFinder()
        results = finder.search_component("Diodes Inc", "APX803L20-30SA-7")
        print(json.dumps(results, indent=2))
    else:
        main()
