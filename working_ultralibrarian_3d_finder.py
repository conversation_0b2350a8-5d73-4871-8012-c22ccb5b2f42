#!/usr/bin/env python3
"""
Screen-Aware UltraLibrarian 3D Finder - Verifies each screen before proceeding
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class ScreenAwareUltraLibrarian:
    def __init__(self):
        self.base_url = 'https://app.ultralibrarian.com'  # Use app subdomain for login
        self.search_url = 'https://app.ultralibrarian.com'  # Direct to app for search
        os.makedirs('3d', exist_ok=True)
        self.wait_timeout = 15

    def setup_driver(self):
        """Setup Chrome driver with better options"""
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3d'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)

        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"Chrome driver setup failed: {e}")
            return None

    def take_screenshot(self, driver, step_name):
        """Take screenshot for debugging"""
        try:
            screenshot_path = f"ultralibrarian_{step_name}.png"
            driver.save_screenshot(screenshot_path)
            print(f"   📸 Screenshot saved: {screenshot_path}")
        except Exception as e:
            print(f"   ⚠️ Screenshot failed: {e}")

    def verify_screen_content(self, driver, expected_elements, screen_name):
        """Verify that expected elements are present on the screen"""
        print(f"   🔍 Verifying {screen_name} screen...")

        found_elements = []
        for element_desc, selectors in expected_elements.items():
            element_found = False

            for selector_type, selector in selectors:
                try:
                    if selector_type == "xpath":
                        elements = driver.find_elements(By.XPATH, selector)
                    elif selector_type == "css":
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    elif selector_type == "text":
                        elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{selector}')]")

                    if elements and elements[0].is_displayed():
                        found_elements.append(element_desc)
                        print(f"      ✅ Found: {element_desc}")
                        element_found = True
                        break
                except Exception as e:
                    continue

            if not element_found:
                print(f"      ❌ Missing: {element_desc}")

        return found_elements

    def wait_for_page_change(self, driver, timeout=15):
        """Wait for page to finish loading by checking for changes"""
        wait = WebDriverWait(driver, timeout)
        try:
            # Wait for page to be ready
            wait.until(lambda d: d.execute_script("return document.readyState") == "complete")
            time.sleep(2)  # Small buffer for dynamic content
            return True
        except:
            return False

    def screen_1_login_and_search(self, driver, manufacturer, part_number):
        """Screen 1: Login first, then search for the part"""
        print(f"\n🔸 SCREEN 1: Login and search for {part_number}")

        # Navigate to UltraLibrarian main page
        driver.get('https://www.ultralibrarian.com/')
        if not self.wait_for_page_change(driver):
            print(f"   ❌ Page failed to load")
            return False

        self.take_screenshot(driver, "screen1_initial")

        # First, click LOGIN link
        print(f"   🔐 Looking for LOGIN link...")
        login_link = None
        login_selectors = [
            ("xpath", "//a[contains(text(), 'LOGIN')]"),
            ("xpath", "//a[contains(text(), 'Login')]"),
            ("css", "a[href*='Login']"),
            ("css", "a[href*='login']")
        ]

        for selector_type, selector in login_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        login_link = element
                        print(f"   ✅ Found LOGIN link: {element.text}")
                        break

                if login_link:
                    break
            except:
                continue

        if not login_link:
            print(f"   ❌ No LOGIN link found")
            return False

        # Click login link
        print(f"   🖱️ Clicking LOGIN link...")
        login_link.click()

        # Wait for login page to load
        if not self.wait_for_page_change(driver):
            print(f"   ❌ Login page failed to load")
            return False

        self.take_screenshot(driver, "screen1_login_page")

        # Handle login form
        print(f"   🔐 Filling login form...")
        try:
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            print(f"   ✅ Credentials loaded for: {email}")
        except Exception as e:
            print(f"   ❌ Could not load credentials: {e}")
            return False

        # Fill email
        email_input = None
        email_selectors = [
            ("css", "input[name='Username']"),
            ("css", "input[id='Username']"),
            ("xpath", "//input[contains(@placeholder, 'Email')]"),
            ("css", "input[type='email']"),
            ("css", "input[name*='email']"),
            ("css", "input[id*='email']"),
            ("xpath", "//input[contains(@placeholder, 'email')]")
        ]

        for selector_type, selector in email_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements and elements[0].is_displayed():
                    email_input = elements[0]
                    break
            except:
                continue

        if not email_input:
            print(f"   ❌ No email input found")
            return False

        email_input.clear()
        email_input.send_keys(email)

        # Fill password
        password_input = None
        password_selectors = [
            ("css", "input[type='password']"),
            ("css", "input[name*='password']"),
            ("css", "input[id*='password']")
        ]

        for selector_type, selector in password_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements and elements[0].is_displayed():
                    password_input = elements[0]
                    break
            except:
                continue

        if not password_input:
            print(f"   ❌ No password input found")
            return False

        password_input.clear()
        password_input.send_keys(password)

        # Click login button
        login_button = None
        login_button_selectors = [
            ("xpath", "//button[contains(text(), 'Login')]"),
            ("xpath", "//button[contains(text(), 'Sign In')]"),
            ("xpath", "//input[@type='submit']"),
            ("css", "button[type='submit']"),
            ("css", ".login-btn")
        ]

        for selector_type, selector in login_button_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements and elements[0].is_displayed():
                    login_button = elements[0]
                    break
            except:
                continue

        if not login_button:
            print(f"   ❌ No login button found")
            return False

        print(f"   🖱️ Clicking login button...")
        login_button.click()

        # Wait for login to complete and redirect to search page
        if not self.wait_for_page_change(driver, timeout=20):
            print(f"   ❌ Login failed or redirect failed")
            return False

        self.take_screenshot(driver, "screen1_after_login")

        # Now search for the part
        print(f"   🔍 Now searching for: {part_number}")

        # Find search box
        search_input = None
        search_selectors = [
            ("css", "input[placeholder*='search' i]"),
            ("css", "input[placeholder*='part' i]"),
            ("css", "input[name*='search' i]"),
            ("css", "input[type='text']"),
            ("css", "input[type='search']")
        ]

        for selector_type, selector in search_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        search_input = element
                        placeholder = element.get_attribute('placeholder') or ''
                        print(f"   ✅ Found search box: {placeholder}")
                        break
                if search_input:
                    break
            except:
                continue

        if not search_input:
            print(f"   ❌ No search box found after login")
            return False

        # Perform search
        search_input.clear()
        search_input.send_keys(part_number)
        search_input.send_keys(Keys.RETURN)

        # Wait for search results to load
        if not self.wait_for_page_change(driver):
            print(f"   ❌ Search results failed to load")
            return False

        self.take_screenshot(driver, "screen1_search_results")

        # Check for search results
        result_found = False
        result_selectors = [
            ("xpath", f"//a[contains(text(), '{part_number}')]"),
            ("xpath", "//a[contains(@href, '/details/')]"),
            ("css", ".search-result a"),
            ("css", ".result a"),
            ("css", "a[href*='component']")
        ]

        for selector_type, selector in result_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements:
                    for element in elements:
                        if element.is_displayed() and part_number.upper() in element.text.upper():
                            print(f"   ✅ Found result: {element.text[:50]}")
                            result_found = True
                            break
                if result_found:
                    break
            except:
                continue

        if not result_found:
            print(f"   ❌ No search results found for {part_number}")
            return False

        print(f"   ✅ Login and search completed successfully")
        return True
    def screen_2_select_part(self, driver, part_number):
        """Screen 2: Select the specific part from search results"""
        print(f"\n🔸 SCREEN 2: Select part {part_number}")

        # Find and click part link immediately
        part_link = None
        part_selectors = [
            ("xpath", f"//a[contains(text(), '{part_number}')]"),
            ("css", "a[href*='/details/']"),
            ("css", "a[href*='/part/']"),
            ("css", "a[href*='/component/']"),
            ("css", ".search-result a"),
            ("css", ".result a")
        ]

        for selector_type, selector in part_selectors:
            try:
                if selector_type == "xpath":
                    links = driver.find_elements(By.XPATH, selector)
                else:
                    links = driver.find_elements(By.CSS_SELECTOR, selector)

                for link in links:
                    if link.is_displayed() and part_number.upper() in link.text.upper():
                        part_link = link
                        print(f"   ✅ Found part link: {link.text[:50]}")
                        break

                if part_link:
                    break
            except:
                continue

        if not part_link:
            print(f"   ❌ No part link found for {part_number}")
            return False

        # Click the part link
        print(f"   🖱️ Clicking part link...")
        part_link.click()

        # Wait for part details page to load
        if not self.wait_for_page_change(driver):
            print(f"   ❌ Part details page failed to load")
            return False

        self.take_screenshot(driver, "screen2_part_details")

        # Check for download button immediately
        download_found = False
        download_selectors = [
            ("xpath", "//button[contains(text(), 'Download')]"),
            ("xpath", "//a[contains(text(), 'Download')]"),
            ("css", ".download-btn"),
            ("css", "[data-action='download']"),
            ("css", "button[class*='download']")
        ]

        for selector_type, selector in download_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements and elements[0].is_displayed():
                    print(f"   ✅ Found download button: {elements[0].text}")
                    download_found = True
                    break
            except:
                continue

        if not download_found:
            print(f"   ❌ No download button found on part details page")
            return False

        print(f"   ✅ Part selected successfully")
        return True

    def screen_3_download_now(self, driver):
        """Screen 3: Click Download Now button"""
        print(f"\n🔸 SCREEN 3: Click Download Now")

        # Find and click Download Now button immediately
        download_btn = None
        download_selectors = [
            ("xpath", "//button[contains(text(), 'Download Now')]"),
            ("xpath", "//button[contains(text(), 'Download')]"),
            ("xpath", "//a[contains(text(), 'Download Now')]"),
            ("xpath", "//a[contains(text(), 'Download')]"),
            ("css", ".download-now"),
            ("css", "[data-action='download']"),
            ("css", "button[class*='download']")
        ]

        for selector_type, selector in download_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        download_btn = element
                        print(f"   ✅ Found download button: {download_btn.text}")
                        break

                if download_btn:
                    break
            except:
                continue

        if not download_btn:
            print(f"   ❌ No Download button found")
            return False

        # Click download button
        print(f"   🖱️ Clicking download button...")
        download_btn.click()

        # Wait for format selection screen
        if not self.wait_for_page_change(driver):
            print(f"   ❌ Format selection screen failed to load")
            return False

        self.take_screenshot(driver, "screen3_format_selection")

        # Check for format options immediately
        format_found = False
        format_selectors = [
            ("xpath", "//*[contains(text(), '3D')]"),
            ("xpath", "//*[contains(text(), 'CAD')]"),
            ("xpath", "//*[contains(text(), 'Model')]"),
            ("css", "input[type='checkbox']"),
            ("css", "input[type='radio']"),
            ("css", ".format-option")
        ]

        for selector_type, selector in format_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements:
                    for element in elements:
                        if element.is_displayed():
                            print(f"   ✅ Found format option: {element.text or element.get_attribute('value')}")
                            format_found = True
                            break
                if format_found:
                    break
            except:
                continue

        if not format_found:
            print(f"   ❌ No format options found")
            return False

        print(f"   ✅ Download Now clicked successfully")
        return True
    def screen_4_select_3d_model(self, driver):
        """Screen 4: Select 3D Model format"""
        print(f"\n🔸 SCREEN 4: Select 3D Model")

        # Find and click 3D model option immediately
        model_element = None
        model_selectors = [
            ("xpath", "//button[contains(text(), '3D CAD Model')]"),
            ("xpath", "//a[contains(text(), '3D CAD Model')]"),
            ("xpath", "//button[contains(text(), '3D Model')]"),
            ("xpath", "//a[contains(text(), '3D Model')]"),
            ("xpath", "//button[contains(text(), '3D')]"),
            ("xpath", "//a[contains(text(), '3D')]"),
            ("xpath", "//*[contains(text(), '3D') and (self::button or self::a)]"),
            ("css", "input[value*='3D']"),
            ("css", ".model-3d")
        ]

        for selector_type, selector in model_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        text = element.text or element.get_attribute('value') or ''
                        if '3D' in text.upper():
                            model_element = element
                            print(f"   ✅ Found 3D option: {text}")
                            break

                if model_element:
                    break
            except:
                continue

        if not model_element:
            print(f"   ❌ No 3D model option found")
            return False

        # Click the 3D model option
        print(f"   🖱️ Clicking 3D model option...")
        model_element.click()

        # Give page time to respond then proceed
        print(f"   ⏳ Waiting for next screen...")
        time.sleep(8)

        self.take_screenshot(driver, "screen4_login_download")

        # Check for any form elements or buttons on the page
        next_screen_found = False
        next_screen_selectors = [
            ("xpath", "//button[contains(text(), 'Download')]"),
            ("xpath", "//input[@type='submit']"),
            ("xpath", "//button[@type='submit']"),
            ("css", "input[type='checkbox']"),
            ("css", "input[type='radio']"),
            ("xpath", "//input[contains(@placeholder, 'email')]"),
            ("css", "input[type='email']"),
            ("css", "input[name*='email']"),
            ("css", ".download-final"),
            ("css", "form"),
            ("xpath", "//form")
        ]

        for selector_type, selector in next_screen_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements and elements[0].is_displayed():
                    element_type = elements[0].get_attribute('type') or elements[0].tag_name
                    print(f"   ✅ Found next screen element: {element_type}")
                    next_screen_found = True
                    break
            except:
                continue

        if not next_screen_found:
            print(f"   ❌ Next screen not properly loaded")
            return False

        print(f"   ✅ 3D Model selected successfully")
        return True
    def screen_5_final_download(self, driver, manufacturer, part_number):
        """Screen 5: Handle final download (login already done in screen 1)"""
        print(f"\n🔸 SCREEN 5: Final download")

        # Look for STEP format selection with multiple selectors
        format_selected = False

        # Try multiple ways to find STEP checkbox
        step_selectors = [
            ("id", "STEP"),
            ("xpath", "//input[@type='checkbox' and contains(@value, 'STEP')]"),
            ("xpath", "//input[@type='checkbox' and contains(@name, 'STEP')]"),
            ("xpath", "//input[@type='checkbox']/following-sibling::*[contains(text(), 'STEP')]/../input"),
            ("xpath", "//label[contains(text(), 'STEP')]/input"),
            ("xpath", "//label[contains(text(), 'Step')]/input"),
            ("xpath", "//input[@type='checkbox'][contains(../text(), 'STEP')]"),
            ("css", "input[type='checkbox'][value*='STEP']"),
            ("css", "input[type='checkbox'][name*='STEP']")
        ]

        for selector_type, selector in step_selectors:
            try:
                if selector_type == "id":
                    checkbox = driver.find_element(By.ID, selector)
                elif selector_type == "xpath":
                    checkbox = driver.find_element(By.XPATH, selector)
                else:
                    checkbox = driver.find_element(By.CSS_SELECTOR, selector)

                if checkbox.is_displayed() and not checkbox.is_selected():
                    print(f"   🖱️ Selecting STEP format")
                    checkbox.click()
                    format_selected = True
                    break
            except:
                continue

        # If STEP not found, try other 3D formats
        if not format_selected:
            other_format_options = [
                ('MfrThreeDModel', 'Manufacturer 3D Model'),
                ('IGES', 'IGES Format'),
                ('STL', 'STL Format')
            ]

            for format_id, format_name in other_format_options:
                try:
                    checkbox = driver.find_element(By.ID, format_id)
                    if checkbox.is_displayed() and not checkbox.is_selected():
                        print(f"   🖱️ Selecting format: {format_name}")
                        checkbox.click()
                        format_selected = True
                        break
                except:
                    continue

        if not format_selected:
            print(f"   ⚠️ No specific format selected, proceeding with default")

        # Find and click final download button immediately
        download_clicked = False
        download_selectors = [
            ("xpath", "//button[contains(text(), 'Download')]"),
            ("xpath", "//input[@type='submit']"),
            ("xpath", "//button[@type='submit']"),
            ("css", ".download-final"),
            ("css", "[data-action='download']")
        ]

        for selector_type, selector in download_selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   🖱️ Clicking final download: {element.text or 'Submit'}")
                        element.click()
                        download_clicked = True
                        break

                if download_clicked:
                    break
            except:
                continue

        if not download_clicked:
            print(f"   ❌ No final download button found")
            return False

        print(f"   ✅ Download initiated")
        return True
    def screen_6_monitor_download(self, driver, manufacturer, part_number):
        """Screen 6: Monitor for file download with faster checking"""
        print(f"\n🔸 SCREEN 6: Monitor download")

        # Check both Downloads folder and 3d folder
        downloads_dir = os.path.expanduser("~/Downloads")
        local_3d_dir = os.path.abspath('3d')

        initial_downloads = set(os.listdir(downloads_dir)) if os.path.exists(downloads_dir) else set()
        initial_3d = set(os.listdir(local_3d_dir)) if os.path.exists(local_3d_dir) else set()

        print(f"   📁 Monitoring: {downloads_dir}")
        print(f"   📁 Monitoring: {local_3d_dir}")

        # Check more frequently - every 2 seconds for first 30 seconds, then every 5 seconds
        check_intervals = [2] * 15 + [5] * 18  # 30 seconds fast, then 90 seconds normal

        for i, interval in enumerate(check_intervals):
            time.sleep(interval)

            # Check Downloads folder
            current_downloads = set(os.listdir(downloads_dir)) if os.path.exists(downloads_dir) else set()
            new_downloads = current_downloads - initial_downloads

            # Check 3d folder
            current_3d = set(os.listdir(local_3d_dir)) if os.path.exists(local_3d_dir) else set()
            new_3d = current_3d - initial_3d

            # Look for any new files immediately
            if new_downloads or new_3d:
                print(f"   📁 New files detected!")
                if new_downloads:
                    print(f"      Downloads: {list(new_downloads)}")
                if new_3d:
                    print(f"      3D folder: {list(new_3d)}")

            # Process new files in 3d folder first (direct download)
            direct_step_files = [f for f in new_3d if f.lower().endswith(('.step', '.stp'))]

            if direct_step_files:
                step_file = direct_step_files[0]
                print(f"   ✅ STEP file found in 3d folder: {step_file}")

                # Rename to standard format
                new_name = f"ultralibrarian_{manufacturer.replace(' ', '_')}_{part_number}.step"
                old_path = os.path.join(local_3d_dir, step_file)
                new_path = os.path.join(local_3d_dir, new_name)

                if old_path != new_path:
                    os.rename(old_path, new_path)
                    print(f"   ✅ Renamed to: {new_name}")
                    return new_name
                else:
                    return step_file

            # Process new files in Downloads folder
            step_files = [f for f in new_downloads if f.lower().endswith(('.step', '.stp'))]
            zip_files = [f for f in new_downloads if f.lower().endswith('.zip')]

            if step_files:
                step_file = step_files[0]
                print(f"   ✅ STEP file downloaded: {step_file}")

                # Move to 3d directory
                src_path = os.path.join(downloads_dir, step_file)
                new_name = f"ultralibrarian_{manufacturer.replace(' ', '_')}_{part_number}.step"
                dst_path = os.path.join(local_3d_dir, new_name)

                import shutil
                shutil.move(src_path, dst_path)
                print(f"   ✅ Moved to: 3d/{new_name}")
                return new_name

            elif zip_files:
                zip_file = zip_files[0]
                print(f"   📦 ZIP file downloaded: {zip_file}")

                # Extract and look for STEP files
                import zipfile
                zip_path = os.path.join(downloads_dir, zip_file)

                try:
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(local_3d_dir)

                    # Check for extracted STEP files
                    final_3d = set(os.listdir(local_3d_dir))
                    extracted_files = final_3d - current_3d
                    step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]

                    if step_files:
                        step_file = step_files[0]
                        new_name = f"ultralibrarian_{manufacturer.replace(' ', '_')}_{part_number}.step"

                        old_path = os.path.join(local_3d_dir, step_file)
                        new_path = os.path.join(local_3d_dir, new_name)

                        os.rename(old_path, new_path)
                        print(f"   ✅ Extracted and renamed to: {new_name}")
                        return new_name
                except Exception as e:
                    print(f"   ❌ Error extracting ZIP: {e}")

            # Show progress
            elapsed = sum(check_intervals[:i+1])
            print(f"   ⏳ Checking... ({elapsed}/120 seconds)")

        print(f"   ❌ No files downloaded after 2 minutes")
        return None

    def search_and_download(self, manufacturer, part_number):
        """Complete search and download process with screen verification"""
        print(f"\nSCREEN-AWARE ULTRALIBRARIAN 3D FINDER")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("=" * 60)

        driver = self.setup_driver()
        if not driver:
            return None

        try:
            # Execute all screens in sequence
            if not self.screen_1_login_and_search(driver, manufacturer, part_number):
                return None

            if not self.screen_2_select_part(driver, part_number):
                return None

            if not self.screen_3_download_now(driver):
                return None

            if not self.screen_4_select_3d_model(driver):
                return None

            if not self.screen_5_final_download(driver, manufacturer, part_number):
                return None

            result = self.screen_6_monitor_download(driver, manufacturer, part_number)

            if result:
                print(f"\n🎉 SUCCESS: Downloaded {result}")
                return result
            else:
                print(f"\n❌ FAILED: No file downloaded")
                return None

        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            if driver:
                driver.quit()

def main():
    import sys

    if len(sys.argv) != 3:
        print("Usage: python working_ultralibrarian_3d_finder.py 'Manufacturer' 'PartNumber'")
        print("Example: python working_ultralibrarian_3d_finder.py 'TI' 'LM358N'")
        return

    manufacturer = sys.argv[1]
    part_number = sys.argv[2]

    finder = ScreenAwareUltraLibrarian()
    result = finder.search_and_download(manufacturer, part_number)

    if result:
        print(f"\n🎉 FINAL SUCCESS: Downloaded {result}")
    else:
        print(f"\n❌ FINAL FAILURE: Could not download 3D model")

if __name__ == "__main__":
    main()
