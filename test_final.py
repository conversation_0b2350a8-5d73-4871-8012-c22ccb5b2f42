#!/usr/bin/env python3
"""
Final test of the fixed Digikey searcher
"""

import time
from digikey_searcher import <PERSON><PERSON><PERSON><PERSON>earcher

def test_final():
    print("🎯 FINAL TEST - FIXED DIGIKEY SEARCHER")
    print("=" * 50)
    
    # Wait to avoid rate limiting
    print("⏳ Waiting 30 seconds to avoid rate limiting...")
    time.sleep(30)
    
    searcher = DigikeySearcher()
    
    # Test with Texas Instruments
    print("\n🔍 Testing: LM358N from Texas Instruments")
    part_info = searcher.search_part("LM358N", "Texas Instruments")
    
    if part_info:
        print(f"✅ SUCCESS!")
        print(f"   Part: {part_info['part_number']}")
        print(f"   Manufacturer: {part_info['manufacturer']}")
        print(f"   Datasheet: {part_info['datasheet_url']}")
        
        # Try download
        success = searcher.download_datasheet(part_info)
        if success:
            print(f"🎉 Downloaded Texas Instruments datasheet!")
        else:
            print(f"⚠️  Download failed but part info was correct")
    else:
        print(f"❌ Failed to find Texas Instruments LM358N")

if __name__ == "__main__":
    test_final()
