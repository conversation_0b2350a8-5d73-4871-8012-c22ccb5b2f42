#!/usr/bin/env python3

import requests
from bs4 import BeautifulSoup
import re
import os
import json
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UniversalComponentSearcher:
    def __init__(self, download_dir="files-download"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = self._create_session()
        
        # Manufacturer-specific configurations
        self.manufacturer_configs = {
            "diodes inc": {
                "base_url": "https://www.diodes.com",
                "product_url_patterns": [
                    "https://www.diodes.com/part/view/{}",
                    "https://www.diodes.com/products/{}"
                ],
                "datasheet_url_patterns": [
                    "https://www.diodes.com/assets/Datasheets/{}.pdf",
                    "https://www.diodes.com/datasheet/download/{}.pdf"
                ],
                "3d_model_strategies": [
                    "package_based_urls",
                    "search_in_page",
                    "common_directories"
                ],
                "3d_model_url_patterns": [
                    "https://www.diodes.com/assets/3D-models/{}.step",
                    "https://www.diodes.com/assets/3D-models/{}.stp",
                    "https://www.diodes.com/assets/Package-3D/{}.step",
                    "https://www.diodes.com/assets/cad-models/{}.step",
                    "https://www.diodes.com/assets/3D-package-models/{}.step"
                ]
            },
            "texas instruments": {
                "base_url": "https://www.ti.com",
                "product_url_patterns": [
                    "https://www.ti.com/product/{}",
                    "https://www.ti.com/lit/ds/symlink/{}.pdf"
                ],
                "3d_model_strategies": ["search_in_page", "package_based_urls"]
            },
            "analog devices": {
                "base_url": "https://www.analog.com",
                "product_url_patterns": [
                    "https://www.analog.com/en/products/{}.html"
                ],
                "3d_model_strategies": ["search_in_page", "package_based_urls"]
            }
        }
    
    def _create_session(self):
        """Create a robust HTTP session"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        return session
    
    def find_manufacturer_website(self, manufacturer):
        """Find the manufacturer's website if not in our config"""
        if manufacturer.lower() in self.manufacturer_configs:
            return self.manufacturer_configs[manufacturer.lower()]["base_url"]
        
        # Use web search to find the manufacturer's website
        try:
            from web_search import web_search
            query = f"{manufacturer} semiconductor official website"
            results = web_search(query, num_results=3)
            
            if results:
                # Look for official website (usually first result)
                for result in results:
                    url = result.get('url', '')
                    if any(domain in url for domain in ['.com', '.org', '.net']) and 'wikipedia' not in url:
                        logger.info(f"Found potential website: {url}")
                        return url
        except:
            pass
        
        return None
    
    def extract_package_from_datasheet(self, datasheet_path):
        """Extract package information from downloaded datasheet"""
        try:
            # For now, we'll do basic text search
            # In a full implementation, you'd use PyPDF2 or similar to extract text
            
            # Try to read as text (won't work for binary PDFs, but worth trying)
            try:
                with open(datasheet_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
            except:
                # If that fails, we'd need PDF parsing library
                logger.warning("Cannot read PDF content - would need PyPDF2 or similar")
                return None
            
            # Look for package information
            content_lower = content.lower()
            package_patterns = {
                'SOT-23': [r'sot-?23', r'sot23'],
                'SOT-323': [r'sot-?323', r'sot323'],
                'SOT-25': [r'sot-?25', r'sot25'],
                'SC-59': [r'sc-?59', r'sc59'],
                'SC-70': [r'sc-?70', r'sc70'],
                'SOIC-8': [r'soic-?8', r'so-?8'],
                'QFN': [r'qfn'],
                'BGA': [r'bga'],
                'DIP': [r'dip', r'pdip'],
                'TO-220': [r'to-?220'],
                'TO-252': [r'to-?252']
            }
            
            for package_name, patterns in package_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, content_lower):
                        logger.info(f"📦 Found package in datasheet: {package_name}")
                        return package_name
            
            return None
            
        except Exception as e:
            logger.error(f"Error reading datasheet: {e}")
            return None
    
    def search_3d_models_by_package(self, manufacturer, package_type):
        """Search for 3D models based on package type"""
        manufacturer_lower = manufacturer.lower()
        models = []
        
        if manufacturer_lower not in self.manufacturer_configs:
            return models
        
        config = self.manufacturer_configs[manufacturer_lower]
        
        # Strategy 1: Try direct URL patterns
        if "3d_model_url_patterns" in config:
            package_variations = [
                package_type,
                package_type.replace('-', ''),
                package_type.lower(),
                package_type.lower().replace('-', ''),
                package_type.upper(),
                package_type.upper().replace('-', '')
            ]
            
            for pattern in config["3d_model_url_patterns"]:
                for pkg_var in package_variations:
                    try:
                        url = pattern.format(pkg_var)
                        logger.info(f"🎯 Testing 3D model URL: {url}")
                        
                        response = self.session.head(url, timeout=10)
                        if response.status_code == 200:
                            content_type = response.headers.get('Content-Type', '')
                            logger.info(f"✅ Found 3D model: {url} ({content_type})")
                            models.append(url)
                            
                    except Exception as e:
                        logger.debug(f"3D model URL test failed: {e}")
                        continue
        
        # Strategy 2: Web search for 3D models
        if not models:
            models.extend(self._web_search_3d_models(manufacturer, package_type))
        
        return models
    
    def _web_search_3d_models(self, manufacturer, package_type):
        """Use web search to find 3D models"""
        models = []
        
        try:
            # Search for 3D models
            search_queries = [
                f'site:{self.manufacturer_configs.get(manufacturer.lower(), {}).get("base_url", "")} {package_type} 3D model STEP',
                f'site:{self.manufacturer_configs.get(manufacturer.lower(), {}).get("base_url", "")} {package_type} CAD model',
                f'"{package_type}" 3D model STEP {manufacturer}',
                f'"{package_type}" CAD model download {manufacturer}'
            ]
            
            for query in search_queries:
                if not query.startswith('site:'):  # Skip if no base URL
                    continue
                    
                logger.info(f"🔍 Web searching: {query}")
                # Here you would integrate with web search API
                # For now, we'll skip this and rely on direct URL testing
                
        except Exception as e:
            logger.error(f"Web search failed: {e}")
        
        return models
    
    def download_file(self, url, filename=None):
        """Download a file from URL"""
        try:
            logger.info(f"📥 Downloading: {url}")
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            if not filename:
                filename = os.path.basename(urlparse(url).path)
                if not filename or '.' not in filename:
                    if '.pdf' in url.lower():
                        filename = f"datasheet_{int(time.time())}.pdf"
                    elif any(ext in url.lower() for ext in ['.step', '.stp']):
                        filename = f"3d_model_{int(time.time())}.step"
                    else:
                        filename = f"download_{int(time.time())}"
            
            filepath = self.download_dir / filename
            
            # Avoid overwriting
            counter = 1
            original_filepath = filepath
            while filepath.exists():
                name, ext = original_filepath.stem, original_filepath.suffix
                filepath = self.download_dir / f"{name}_{counter}{ext}"
                counter += 1
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"✅ Downloaded: {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            return None

    def _search_in_product_page(self, manufacturer, part_number, package_type):
        """Search for 3D models in the product page"""
        models = []
        manufacturer_lower = manufacturer.lower()

        if manufacturer_lower not in self.manufacturer_configs:
            return models

        config = self.manufacturer_configs[manufacturer_lower]

        # Get the product page
        base_part = part_number.split('-')[0]
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part

        for url_pattern in config.get("product_url_patterns", []):
            try:
                url = url_pattern.format(part_family)
                logger.info(f"🔍 Searching product page: {url}")

                response = self.session.get(url, timeout=30)
                if response.status_code == 200:
                    # Look for 3D model links in the page
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # Look for STEP/STP files
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        if any(ext in href.lower() for ext in ['.step', '.stp']):
                            full_url = urljoin(url, href)
                            model_file = self.download_file(full_url, f"{package_type}_3d_model.step")
                            if model_file:
                                models.append(model_file)

                    if models:
                        return models

            except Exception as e:
                logger.debug(f"Product page search failed: {e}")
                continue

        return models

    def _try_common_directories(self, base_url, package_type):
        """Try common directory structures for 3D models"""
        models = []

        if not base_url:
            return models

        # Common directory patterns across manufacturers
        common_patterns = [
            f"{base_url}/assets/3d-models/{{}}",
            f"{base_url}/assets/3D-models/{{}}",
            f"{base_url}/assets/cad-models/{{}}",
            f"{base_url}/assets/step-models/{{}}",
            f"{base_url}/downloads/3d-models/{{}}",
            f"{base_url}/cad/{{}}",
            f"{base_url}/3d/{{}}",
            f"{base_url}/assets/packages/{{}}"
        ]

        package_variations = [
            f"{package_type}.step",
            f"{package_type}.stp",
            f"{package_type.replace('-', '')}.step",
            f"{package_type.lower()}.step",
            f"{package_type.upper()}.step"
        ]

        for pattern in common_patterns:
            for pkg_file in package_variations:
                try:
                    url = pattern.format(pkg_file)
                    response = self.session.head(url, timeout=5)
                    if response.status_code == 200:
                        model_file = self.download_file(url, f"{package_type}_3d_model.step")
                        if model_file:
                            models.append(model_file)
                            return models
                except:
                    continue

        return models

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Universal Component Searcher')
    parser.add_argument('manufacturer', help='Manufacturer name')
    parser.add_argument('part_number', help='Part number')
    
    args = parser.parse_args()
    
    searcher = UniversalComponentSearcher()
    results = searcher.search_and_download(args.part_number)
    
    print("\n" + "="*60)
    print("SEARCH RESULTS")
    print("="*60)
    print(json.dumps(results, indent=2))

    def search_component_complete(self, manufacturer, part_number):
        """Complete search process for any manufacturer"""
        results = {
            'manufacturer': manufacturer,
            'part_number': part_number,
            'package_type': None,
            'datasheet_url': None,
            'datasheet_file': None,
            '3d_model_urls': [],
            '3d_model_files': [],
            'search_strategy_used': [],
            'success': False
        }

        logger.info(f"🔍 Starting search for {manufacturer} {part_number}")

        # Step 1: Try to find manufacturer website if not configured
        manufacturer_lower = manufacturer.lower()
        if manufacturer_lower not in self.manufacturer_configs:
            base_url = self.find_manufacturer_website(manufacturer)
            if base_url:
                # Create basic config for unknown manufacturer
                self.manufacturer_configs[manufacturer_lower] = {
                    "base_url": base_url,
                    "3d_model_strategies": ["web_search", "common_directories"]
                }

        # Step 2: Search for datasheet and extract package type
        datasheet_file, package_type = self._search_datasheet_and_package(manufacturer, part_number)
        results['datasheet_file'] = datasheet_file
        results['package_type'] = package_type

        if datasheet_file:
            results['search_strategy_used'].append("datasheet_found")

        # Step 3: Search for 3D models using multiple strategies
        if package_type:
            logger.info(f"📦 Package type identified: {package_type}")
            model_files = self._search_3d_models_multi_strategy(manufacturer, part_number, package_type)
            results['3d_model_files'] = model_files
            results['search_strategy_used'].extend([f"3d_search_{i}" for i in range(len(model_files))])
        else:
            logger.warning("⚠️ No package type found, 3D model search will be limited")

        results['success'] = bool(datasheet_file or results['3d_model_files'])
        return results

    def _search_datasheet_and_package(self, manufacturer, part_number):
        """Search for datasheet and extract package type"""
        manufacturer_lower = manufacturer.lower()

        if manufacturer_lower in self.manufacturer_configs:
            config = self.manufacturer_configs[manufacturer_lower]

            # Try direct datasheet URLs first
            if "datasheet_url_patterns" in config:
                base_part = part_number.split('-')[0]
                part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part

                part_variations = [part_family, base_part, part_number]

                for pattern in config["datasheet_url_patterns"]:
                    for part_var in part_variations:
                        try:
                            url = pattern.format(part_var)
                            logger.info(f"📄 Testing datasheet URL: {url}")

                            response = self.session.head(url, timeout=10)
                            if response.status_code == 200:
                                # Download the datasheet
                                datasheet_file = self.download_file(url, f"{part_var}_datasheet.pdf")
                                if datasheet_file:
                                    # Try to extract package from datasheet
                                    package_type = self.extract_package_from_datasheet(datasheet_file)
                                    return datasheet_file, package_type

                        except Exception as e:
                            logger.debug(f"Datasheet URL failed: {e}")
                            continue

        return None, None

    def _search_3d_models_multi_strategy(self, manufacturer, part_number, package_type):
        """Use multiple strategies to find 3D models"""
        model_files = []
        manufacturer_lower = manufacturer.lower()

        if manufacturer_lower not in self.manufacturer_configs:
            logger.warning(f"No configuration for {manufacturer}")
            return model_files

        config = self.manufacturer_configs[manufacturer_lower]
        strategies = config.get("3d_model_strategies", ["package_based_urls"])

        for strategy in strategies:
            logger.info(f"🎯 Trying 3D model strategy: {strategy}")

            if strategy == "package_based_urls":
                models = self._try_package_based_urls(config, package_type)
                model_files.extend(models)

            elif strategy == "search_in_page":
                models = self._search_in_product_page(manufacturer, part_number, package_type)
                model_files.extend(models)

            elif strategy == "common_directories":
                models = self._try_common_directories(config.get("base_url"), package_type)
                model_files.extend(models)

            elif strategy == "web_search":
                models = self._web_search_3d_models(manufacturer, package_type)
                model_files.extend(models)

            # If we found models, we can stop
            if model_files:
                logger.info(f"✅ Found models using strategy: {strategy}")
                break

        return model_files

    def _try_package_based_urls(self, config, package_type):
        """Try direct URLs based on package type"""
        models = []

        if "3d_model_url_patterns" not in config:
            return models

        package_variations = [
            package_type,
            package_type.replace('-', ''),
            package_type.lower(),
            package_type.lower().replace('-', ''),
            package_type.upper(),
            package_type.upper().replace('-', '')
        ]

        for pattern in config["3d_model_url_patterns"]:
            for pkg_var in package_variations:
                try:
                    url = pattern.format(pkg_var)
                    logger.info(f"   Testing: {url}")

                    response = self.session.head(url, timeout=10)
                    if response.status_code == 200:
                        model_file = self.download_file(url, f"{package_type}_3d_model.step")
                        if model_file:
                            models.append(model_file)
                            return models  # Found one, that's enough

                except Exception as e:
                    logger.debug(f"Package URL failed: {e}")
                    continue

        return models

if __name__ == "__main__":
    import sys

    if len(sys.argv) == 1:
        # Test mode
        searcher = UniversalComponentSearcher()
        results = searcher.search_component_complete("Diodes Inc", "APX803L20-30SA-7")
        print(json.dumps(results, indent=2))
    else:
        main()
