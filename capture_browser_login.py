#!/usr/bin/env python3
"""
Capture the exact browser login request to replicate it
"""

import requests
from bs4 import BeautifulSoup
import time

def capture_browser_style_login():
    print("🔍 CAPTURING BROWSER-STYLE LOGIN")
    print("=" * 50)
    
    # Create session that mimics browser behavior more closely
    session = requests.Session()
    
    # Set headers that exactly match a real browser
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    })
    
    print("\n1. Getting login page with browser-like headers...")
    login_url = 'https://www.ultralibrarian.com/wp-login.php'
    
    try:
        # First, get the login page
        login_page = session.get(login_url, timeout=30)
        print(f"   Status: {login_page.status_code}")
        
        if login_page.status_code != 200:
            print(f"   ❌ Failed to get login page")
            return False
            
        # Save cookies from the initial request
        print(f"   Cookies received: {len(session.cookies)} cookies")
        for cookie in session.cookies:
            print(f"     {cookie.name}: {cookie.value[:20]}...")
            
    except Exception as e:
        print(f"   ❌ Error getting login page: {e}")
        return False
    
    print("\n2. Parsing form and extracting all fields...")
    try:
        soup = BeautifulSoup(login_page.text, 'html.parser')
        form = soup.find('form', {'id': 'loginform'})
        
        if not form:
            print("   ❌ Could not find login form")
            return False
        
        # Extract ALL form fields
        form_data = {}
        inputs = form.find_all('input')
        
        for inp in inputs:
            name = inp.get('name')
            value = inp.get('value', '')
            input_type = inp.get('type', 'text')
            
            if name:
                form_data[name] = value
                print(f"     {name}: '{value}' (type: {input_type})")
        
        # Set our credentials
        form_data['log'] = '<EMAIL>'
        form_data['pwd'] = 'Lennyai123#'
        
    except Exception as e:
        print(f"   ❌ Error parsing form: {e}")
        return False
    
    print("\n3. Waiting 2 seconds to mimic human behavior...")
    time.sleep(2)
    
    print("\n4. Submitting login with exact browser headers...")
    try:
        # Update headers for form submission
        session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://www.ultralibrarian.com',
            'Referer': login_url,
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
        
        # Submit the form
        login_response = session.post(
            login_url, 
            data=form_data, 
            timeout=30, 
            allow_redirects=True
        )
        
        print(f"   Status: {login_response.status_code}")
        print(f"   Final URL: {login_response.url}")
        print(f"   Response cookies: {len(session.cookies)} cookies")
        
        # Save response
        with open('browser_style_login_response.html', 'w', encoding='utf-8') as f:
            f.write(login_response.text)
        print("   📄 Saved response to browser_style_login_response.html")
        
        # Check response headers
        print(f"   Response headers:")
        important_headers = ['location', 'set-cookie', 'content-type', 'cache-control']
        for key, value in login_response.headers.items():
            if key.lower() in important_headers:
                print(f"     {key}: {value}")
        
    except Exception as e:
        print(f"   ❌ Error submitting login: {e}")
        return False
    
    print("\n5. Analyzing response...")
    
    # Check for error messages
    if 'login_error' in login_response.text:
        print("   ❌ Found login error in response")
        soup = BeautifulSoup(login_response.text, 'html.parser')
        error_div = soup.find('div', {'id': 'login_error'})
        if error_div:
            error_text = error_div.get_text(strip=True)
            print(f"   Error message: {error_text}")
        return False
    
    # Check for success indicators
    success_indicators = [
        ('wp-admin' in login_response.url, 'Redirected to wp-admin'),
        ('dashboard' in login_response.url, 'Redirected to dashboard'),
        ('logout' in login_response.text.lower(), 'Logout link found'),
        ('welcome' in login_response.text.lower(), 'Welcome message found'),
        ('profile' in login_response.text.lower(), 'Profile link found'),
    ]
    
    success_count = 0
    for indicator, description in success_indicators:
        if indicator:
            print(f"   ✅ {description}")
            success_count += 1
        else:
            print(f"   ❌ {description}")
    
    print(f"\n   Success indicators: {success_count}/{len(success_indicators)}")
    
    if success_count > 0:
        print("   🎉 Login appears to be successful!")
        
        # Try to access a protected page to confirm
        print("\n6. Testing access to protected area...")
        try:
            profile_url = 'https://www.ultralibrarian.com/wp-admin/profile.php'
            profile_response = session.get(profile_url, timeout=30)
            print(f"   Profile page status: {profile_response.status_code}")
            
            if profile_response.status_code == 200 and 'wp-admin' in profile_response.url:
                print("   ✅ Successfully accessed protected area!")
                return True
            else:
                print("   ❌ Could not access protected area")
                return False
                
        except Exception as e:
            print(f"   ❌ Error testing protected access: {e}")
            return False
    else:
        print("   😞 Login appears to have failed")
        return False

if __name__ == "__main__":
    success = capture_browser_style_login()
    if success:
        print("\n✅ BROWSER-STYLE LOGIN SUCCESSFUL!")
    else:
        print("\n❌ BROWSER-STYLE LOGIN FAILED!")
