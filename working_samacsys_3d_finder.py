#!/usr/bin/env python3
"""
Working SamacSys 3D Finder - Selenium-based for actual downloads
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

class WorkingSamacSys:
    def __init__(self):
        self.base_url = 'https://componentsearchengine.com'
        os.makedirs('3d', exist_ok=True)

    def setup_driver(self):
        """Setup Chrome driver"""
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3d'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"Chrome driver setup failed: {e}")
            return None

    def search_and_download(self, manufacturer, part_number):
        """Complete search and download process"""
        print(f"\nWORKING SAMACSYS 3D FINDER")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("=" * 50)
        
        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            # Step 1: Go to SamacSys part page directly
            print(f"\n🔸 STEP 1: Going to part page...")
            part_url = f"{self.base_url}/part-view/{part_number}"
            driver.get(part_url)
            time.sleep(10)
            print(f"   ✅ Loaded part page")
            
            # Step 2: Look for 3D model availability
            print(f"\n🔸 STEP 2: Checking for 3D model...")
            page_text = driver.page_source.lower()
            
            if '3d model' not in page_text and 'step' not in page_text:
                print(f"   ❌ No 3D model found for {part_number}")
                return None
            
            print(f"   ✅ 3D model available")
            
            # Step 3: Look for download links
            print(f"\n🔸 STEP 3: Looking for download links...")
            
            download_selectors = [
                "//a[contains(text(), 'Download')]",
                "//button[contains(text(), 'Download')]",
                "//a[contains(text(), 'STEP')]",
                "//button[contains(text(), 'STEP')]",
                "//a[contains(text(), '3D')]",
                "//button[contains(text(), '3D')]",
                "//a[contains(@href, 'download')]",
                "//a[contains(@href, 'step')]",
                "//a[contains(@href, '3d')]"
            ]
            
            download_clicked = False
            for selector in download_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''
                            
                            if any(word in text.lower() for word in ['download', 'step', '3d']) or \
                               any(word in href.lower() for word in ['download', 'step', '3d']):
                                print(f"   ✅ Found download link: {text} -> {href[:50]}...")
                                element.click()
                                time.sleep(5)
                                download_clicked = True
                                break
                    if download_clicked:
                        break
                except:
                    continue
            
            if not download_clicked:
                print(f"   ❌ No download links found")
                return None
            
            # Step 4: Monitor for download
            print(f"\n🔸 STEP 4: Monitoring for download...")
            downloads_dir = os.path.expanduser("~/Downloads")
            initial_files = set(os.listdir(downloads_dir))
            
            for i in range(24):  # Check for 2 minutes
                time.sleep(5)
                
                current_files = set(os.listdir(downloads_dir))
                new_files = current_files - initial_files
                
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                
                if step_files:
                    step_file = step_files[0]
                    print(f"   ✅ STEP file downloaded: {step_file}")
                    
                    # Move to 3d directory
                    src_path = os.path.join(downloads_dir, step_file)
                    new_name = f"samacsys_{manufacturer.replace(' ', '_')}_{part_number}.step"
                    dst_path = os.path.join('3d', new_name)
                    
                    import shutil
                    shutil.move(src_path, dst_path)
                    print(f"   ✅ Moved to: 3d/{new_name}")
                    return new_name
                    
                elif zip_files:
                    zip_file = zip_files[0]
                    print(f"   📦 ZIP file downloaded: {zip_file}")
                    
                    # Extract and look for STEP files
                    import zipfile
                    zip_path = os.path.join(downloads_dir, zip_file)
                    
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall('3d')
                    
                    extracted_files = os.listdir('3d')
                    step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                    
                    if step_files:
                        step_file = step_files[0]
                        new_name = f"samacsys_{manufacturer.replace(' ', '_')}_{part_number}.step"
                        
                        old_path = os.path.join('3d', step_file)
                        new_path = os.path.join('3d', new_name)
                        
                        os.rename(old_path, new_path)
                        print(f"   ✅ Extracted and renamed to: {new_name}")
                        return new_name
                
                print(f"   ⏳ Checking... ({(i+1)*5}/120 seconds)")
            
            print(f"   ❌ No files downloaded after 2 minutes")
            return None
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            driver.quit()

def main():
    if len(sys.argv) != 3:
        print("Usage: python working_samacsys_3d_finder.py 'Manufacturer' 'PartNumber'")
        return
    
    manufacturer = sys.argv[1]
    part_number = sys.argv[2]
    
    finder = WorkingSamacSys()
    result = finder.search_and_download(manufacturer, part_number)
    
    if result:
        print(f"\n🎉 SUCCESS: Downloaded {result}")
    else:
        print(f"\n❌ FAILED: Could not download 3D model")

if __name__ == "__main__":
    import sys
    main()
