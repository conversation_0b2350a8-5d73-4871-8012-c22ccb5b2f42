#!/usr/bin/env python3
"""
Simple Digikey search with clean CSV output
Creates: 2 PDF datasheets + 1 simple CSV file
"""

import requests
import json
import os
import time
from datetime import datetime

def simple_digikey_search():
    print("🚀 SIMPLE DIGIKEY SEARCH WITH CSV")
    print("=" * 50)
    
    # Load credentials
    try:
        with open('digikey_api_credentials.json', 'r') as f:
            creds = json.load(f)
            client_id = creds['client_id']
            access_token = creds['access_token']
        print("✅ Loaded Digikey credentials")
    except:
        print("❌ No saved credentials found")
        return False
    
    # Test parts
    test_parts = [
        ("Diodes Inc", "APX803L20-30SA-7"),
        ("Texas Instruments", "LM358N")
    ]
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-DIGIKEY-Client-Id': client_id
    }
    
    # Create datasheets directory
    os.makedirs('datasheets', exist_ok=True)
    
    # Results for CSV
    csv_data = []
    
    for i, (manufacturer, part_number) in enumerate(test_parts, 1):
        print(f"\n🔍 SEARCH {i}/2: {manufacturer} {part_number}")
        print("-" * 40)
        
        try:
            # Search API
            search_url = "https://api.digikey.com/products/v4/search/keyword"
            search_data = {
                'keywords': part_number,
                'recordCount': 3,
                'recordStartPosition': 0,
                'filters': {},
                'sort': {'option': 'SortByUnitPrice', 'direction': 'Ascending'},
                'requestedQuantity': 1
            }
            
            response = requests.post(search_url, headers=headers, json=search_data, timeout=30)
            
            if response.status_code == 200:
                results = response.json()
                products = results.get('Products', [])
                
                if products:
                    product = products[0]
                    
                    # Extract data
                    api_manufacturer = product.get('Manufacturer', {}).get('Name', 'N/A')
                    api_part_number = product.get('ManufacturerProductNumber', 'N/A')
                    description = product.get('Description', {}).get('ProductDescription', 'N/A')
                    package = extract_package_from_api(product)
                    price = product.get('UnitPrice', 0)
                    stock = product.get('QuantityAvailable', 0)
                    datasheet_url = product.get('DatasheetUrl', '')
                    website = extract_manufacturer_website(datasheet_url)
                    
                    print(f"✅ FOUND: {api_manufacturer} {api_part_number}")
                    print(f"   Package: {package}")
                    print(f"   Price: ${price}")
                    print(f"   Stock: {stock}")
                    print(f"   Website: {website}")
                    
                    # Download datasheet
                    datasheet_file = ""
                    if datasheet_url:
                        filename = f"{clean_text(manufacturer)}-{clean_text(api_part_number)}.pdf"
                        if download_datasheet(datasheet_url, filename):
                            datasheet_file = filename
                            print(f"   ✅ Downloaded: {filename}")
                        else:
                            print(f"   ❌ Download failed")
                    
                    # Add to CSV data
                    csv_data.append({
                        'Manufacturer': manufacturer,
                        'Part_Number': api_part_number,
                        'Package': package,
                        'Price': f"${price}",
                        'Stock': stock,
                        'Website': website,
                        'Datasheet_File': datasheet_file
                    })
                    
                else:
                    print(f"   ❌ No products found")
                    
            else:
                print(f"   ❌ API Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)[:50]}")
        
        time.sleep(2)
    
    # Create CSV file
    create_csv_file(csv_data)
    
    return len(csv_data) > 0

def extract_package_from_api(product):
    """Extract package type from API response"""
    package_sources = [
        product.get('Packaging', {}).get('Value', ''),
        product.get('Package', ''),
        product.get('PackageType', ''),
        product.get('Description', {}).get('ProductDescription', '')
    ]
    
    for source in package_sources:
        if source:
            package = extract_package_from_text(source)
            if package:
                return package
    
    return 'Not found'

def extract_package_from_text(text):
    """Extract package type from text"""
    if not text:
        return None
    
    text_upper = text.upper()
    
    # Common package patterns
    import re
    package_patterns = [
        r'\b(SOT-?23-?[0-9]*)\b',
        r'\b(SOT-?89-?[0-9]*)\b',
        r'\b(SOIC-?[0-9]+)\b',
        r'\b(QFN-?[0-9]+)\b',
        r'\b(DFN-?[0-9]+)\b',
        r'\b(MSOP-?[0-9]+)\b',
        r'\b(TSSOP-?[0-9]+)\b',
        r'\b(SSOP-?[0-9]+)\b',
        r'\b(PDIP-?[0-9]+)\b',
        r'\b(DIP-?[0-9]+)\b',
        r'\b([0-9]+-?DIP)\b',
        r'\b([0-9]+-?SOP)\b'
    ]
    
    for pattern in package_patterns:
        match = re.search(pattern, text_upper)
        if match:
            return match.group(1)
    
    # Simple keyword search
    simple_packages = ['SOT23', 'SOT89', 'SOIC', 'QFN', 'DFN', 'MSOP', 'TSSOP', 'PDIP', 'DIP', 'BGA']
    for package in simple_packages:
        if package in text_upper:
            return package
    
    return None

def extract_manufacturer_website(datasheet_url):
    """Extract manufacturer's main website from datasheet URL"""
    if not datasheet_url:
        return 'No URL'
    
    try:
        from urllib.parse import urlparse
        parsed = urlparse(datasheet_url)
        domain = parsed.netloc.lower()
        
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Known manufacturer domains
        manufacturer_domains = {
            'diodes.com': 'https://www.diodes.com',
            'ti.com': 'https://www.ti.com',
            'analog.com': 'https://www.analog.com',
            'microchip.com': 'https://www.microchip.com',
            'st.com': 'https://www.st.com',
            'infineon.com': 'https://www.infineon.com',
            'nxp.com': 'https://www.nxp.com'
        }
        
        for known_domain, clean_url in manufacturer_domains.items():
            if known_domain in domain:
                return clean_url
        
        return f"https://www.{domain}"
        
    except:
        return 'URL error'

def download_datasheet(url, filename):
    """Download datasheet file"""
    try:
        response = requests.get(url, timeout=60)
        if response.status_code == 200:
            filepath = os.path.join('datasheets', filename)
            with open(filepath, 'wb') as f:
                f.write(response.content)
            return True
        return False
    except:
        return False

def clean_text(text):
    """Clean text for filename"""
    if not text or text == 'N/A':
        return 'Unknown'
    import re
    text = re.sub(r'[<>:"/\\|?*]', '_', text)
    text = re.sub(r'[^\w\s\-_.]', '', text)
    text = re.sub(r'\s+', '_', text)
    text = re.sub(r'_+', '_', text)
    return text.strip('_')

def create_csv_file(csv_data):
    """Create simple CSV file"""
    print(f"\n📊 CREATING CSV FILE")
    print("-" * 30)
    
    csv_file = "datasheets/component_database.csv"
    
    try:
        with open(csv_file, 'w', encoding='utf-8') as f:
            # Write header
            f.write("Manufacturer,Part_Number,Package,Price,Stock,Website,Datasheet_File\n")
            
            # Write data
            for row in csv_data:
                line = f"{row['Manufacturer']},{row['Part_Number']},{row['Package']},{row['Price']},{row['Stock']},{row['Website']},{row['Datasheet_File']}\n"
                f.write(line)
        
        print(f"✅ Created CSV file: {csv_file}")
        print(f"   Entries: {len(csv_data)}")
        
    except Exception as e:
        print(f"❌ CSV creation error: {str(e)}")

def main():
    print("🎯 SIMPLE DIGIKEY SEARCH")
    print("Creates: 2 PDF datasheets + 1 CSV file")
    print("=" * 60)
    
    success = simple_digikey_search()
    
    if success:
        print(f"\n🎉 SUCCESS! Check the datasheets folder:")
        print(f"   📄 2 PDF datasheets")
        print(f"   📊 1 CSV database file")
    else:
        print(f"\n❌ Search failed")

if __name__ == "__main__":
    main()
