#!/usr/bin/env python3
"""
Take screenshot of TI website to see what's actually loading
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def take_screenshot():
    print("Taking screenshot of TI website")
    print("=" * 50)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Load TI website
        print("Loading https://www.ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(20)
        
        print(f"Page title: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        # Take screenshot
        screenshot_path = "ti_website_screenshot.png"
        driver.save_screenshot(screenshot_path)
        print(f"Screenshot saved: {screenshot_path}")
        
        # Get page source length
        page_source_length = len(driver.page_source)
        print(f"Page source length: {page_source_length} characters")
        
        # Check if page contains search-related text
        page_text = driver.page_source.lower()
        search_indicators = ['search', 'coveo', 'input', 'form']
        
        for indicator in search_indicators:
            count = page_text.count(indicator)
            print(f"'{indicator}' appears {count} times in page source")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    take_screenshot()
