#!/usr/bin/env python3
"""
Cookie Popup <PERSON>ler - Universal handler for cookie consent popups
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def handle_cookie_popups(driver, max_attempts=3):
    """
    Universal cookie popup handler
    Tries multiple common patterns to dismiss cookie popups
    """
    print("🍪 Checking for cookie popups...")
    
    # Common cookie popup button texts (multiple languages)
    cookie_button_texts = [
        # English
        "Accept", "Accept All", "Accept Cookies", "Accept All Cookies",
        "OK", "Got it", "I Agree", "Agree", "Continue", "Dismiss",
        "Allow", "Allow All", "Consent", "I Accept",
        # German (for Infineon)
        "Akzeptieren", "Alle akzeptieren", "Einverstanden", "Zustimmen",
        # French
        "Accepter", "Accepter tout", "J'accepte", "Continuer",
        # Spanish
        "Aceptar", "Aceptar todo", "Acepto", "Continuar"
    ]
    
    # Common cookie popup selectors
    cookie_selectors = [
        # By button text
        *[f"//button[contains(text(), '{text}')]" for text in cookie_button_texts],
        *[f"//a[contains(text(), '{text}')]" for text in cookie_button_texts],
        *[f"//div[contains(text(), '{text}')][@role='button']" for text in cookie_button_texts],
        
        # By common CSS classes/IDs
        "button[id*='cookie']", "button[class*='cookie']",
        "button[id*='consent']", "button[class*='consent']",
        "button[id*='accept']", "button[class*='accept']",
        ".cookie-accept", ".cookie-consent", ".accept-cookies",
        "#cookie-accept", "#cookie-consent", "#accept-cookies",
        
        # By common attributes
        "button[data-cookie-accept]", "button[data-consent]",
        "button[aria-label*='accept']", "button[aria-label*='cookie']",
        
        # Generic close buttons in overlays
        ".modal button", ".overlay button", ".popup button",
        ".cookie-banner button", ".consent-banner button"
    ]
    
    attempts = 0
    while attempts < max_attempts:
        attempts += 1
        print(f"  Attempt {attempts}/{max_attempts}...")
        
        popup_dismissed = False
        
        # Try each selector
        for selector in cookie_selectors:
            try:
                if selector.startswith("//"):
                    # XPath selector
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    # CSS selector
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                for element in elements:
                    try:
                        if element.is_displayed() and element.is_enabled():
                            text = element.text.strip()
                            print(f"    🎯 Found cookie button: '{text}' ({selector})")
                            
                            # Click the button
                            element.click()
                            time.sleep(2)
                            
                            print(f"    ✅ Clicked cookie button")
                            popup_dismissed = True
                            break
                    except Exception as e:
                        continue
                
                if popup_dismissed:
                    break
                    
            except Exception as e:
                continue
        
        if popup_dismissed:
            print("  ✅ Cookie popup dismissed")
            break
        else:
            print("  ⏳ No cookie popup found, waiting...")
            time.sleep(2)
    
    if not popup_dismissed:
        print("  ⚠️ No cookie popup found or could not dismiss")
    
    return popup_dismissed

def test_cookie_handler():
    """Test the cookie handler on various sites"""
    print("🎯 TESTING COOKIE POPUP HANDLER")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    test_sites = [
        ("Analog Devices", "https://www.analog.com/"),
        ("Infineon", "https://www.infineon.com/"),
        ("SnapEDA", "https://www.snapeda.com/")
    ]
    
    try:
        for site_name, url in test_sites:
            print(f"\n📱 Testing {site_name}: {url}")
            
            driver.get(url)
            time.sleep(5)
            
            # Handle cookie popup
            handle_cookie_popups(driver)
            
            print(f"✅ {site_name} cookie handling complete")
            time.sleep(3)
        
        print(f"\n🔸 All sites tested - browser staying open for inspection...")
        time.sleep(60)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    test_cookie_handler()
