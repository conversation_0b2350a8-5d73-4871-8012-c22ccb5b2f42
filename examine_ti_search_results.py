#!/usr/bin/env python3
"""
Examine what's actually on TI search results page
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time

def examine_ti_search_results():
    print("EXAMINING TI SEARCH RESULTS PAGE")
    print("=" * 40)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        print("1. Loading ti.com and searching for LM358N...")
        driver.get("https://www.ti.com")
        time.sleep(20)
        
        # Find and use search box
        js_find_input = """
        var searchBox = document.querySelector('#headerSearchBox');
        if (searchBox) {
            if (searchBox.shadowRoot) {
                var input = searchBox.shadowRoot.querySelector('input');
                if (input) return input;
            }
            var input = searchBox.querySelector('input');
            if (input) return input;
        }
        var allInputs = document.querySelectorAll('input');
        for (var i = 0; i < allInputs.length; i++) {
            var inp = allInputs[i];
            if (inp.offsetWidth > 0 && inp.offsetHeight > 0) {
                return inp;
            }
        }
        return null;
        """
        
        input_element = driver.execute_script(js_find_input)
        if input_element:
            input_element.click()
            time.sleep(2)
            input_element.clear()
            input_element.send_keys("LM358N")
            input_element.send_keys(Keys.RETURN)
            time.sleep(15)
        
        print(f"2. Current URL: {driver.current_url}")
        
        # Look for LM358N results
        print("3. Looking for LM358N results on page...")
        page_text = driver.page_source.lower()
        
        if "lm358n" in page_text:
            print("✅ Found LM358N on page")
        else:
            print("❌ No LM358N found on page")
        
        # Look for product links
        print("4. Looking for product links...")
        links = driver.find_elements(By.TAG_NAME, "a")
        lm358_links = []
        
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if 'lm358' in text.lower() or 'lm358' in href.lower():
                    lm358_links.append((text, href))
            except:
                continue
        
        print(f"Found {len(lm358_links)} LM358 links:")
        for i, (text, href) in enumerate(lm358_links[:5]):
            print(f"  {i+1}. '{text}' -> {href}")
        
        # Look for 3D/CAD related content
        print("5. Looking for 3D/CAD content...")
        keywords = ['3d', 'step', 'model', 'cad', 'mechanical', 'package', 'download']
        
        for keyword in keywords:
            count = page_text.count(keyword)
            if count > 0:
                print(f"  '{keyword}': {count} occurrences")
        
        # If we found product links, click on the first one
        if lm358_links:
            print("6. Clicking on first LM358 product link...")
            try:
                first_link = driver.find_element(By.XPATH, f"//a[contains(text(), 'LM358') or contains(@href, 'lm358')]")
                first_link.click()
                time.sleep(15)
                
                print(f"Product page URL: {driver.current_url}")
                
                # Look for 3D content on product page
                product_page = driver.page_source.lower()
                print("7. Looking for 3D content on product page...")
                
                for keyword in keywords:
                    count = product_page.count(keyword)
                    if count > 0:
                        print(f"  '{keyword}': {count} occurrences")
                
                # Look for download links
                download_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'download') or contains(text(), '3D') or contains(text(), 'STEP') or contains(@href, '.step') or contains(@href, '.stp')]")
                
                print(f"Found {len(download_links)} potential download links:")
                for i, link in enumerate(download_links[:3]):
                    try:
                        text = link.text.strip()
                        href = link.get_attribute('href') or ''
                        print(f"  {i+1}. '{text}' -> {href}")
                    except:
                        continue
                        
            except Exception as e:
                print(f"Error clicking product link: {e}")
        
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    examine_ti_search_results()
