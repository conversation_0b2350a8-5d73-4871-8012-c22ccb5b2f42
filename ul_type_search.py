#!/usr/bin/env python3
"""
Type LM358N in search box
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options

def type_search():
    print("🔸 Typing LM358N in search box...")
    
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        driver.get("https://app.ultralibrarian.com")
        time.sleep(10)
        
        # Find search box
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_input = None
        
        for inp in inputs:
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower() or 'part' in placeholder.lower():
                        search_input = inp
                        break
            except:
                continue
        
        if search_input:
            print("✅ Found search box, typing LM358N...")
            search_input.clear()
            search_input.send_keys("LM358N")
            print("✅ Typed LM358N")
            
            input("✋ LM358N is typed. Press Enter to submit search...")
            
            search_input.send_keys(Keys.RETURN)
            time.sleep(8)
            
            print("✅ Search submitted")
            print(f"✅ New URL: {driver.current_url}")
            
            input("✋ Search complete. What do you see on the results page? Press Enter when ready...")
        else:
            print("❌ Could not find search box")
            
        return driver
        
    except Exception as e:
        print(f"❌ Error: {e}")
        driver.quit()
        return None

if __name__ == "__main__":
    driver = type_search()
    if driver:
        input("Press Enter to close browser...")
        driver.quit()
