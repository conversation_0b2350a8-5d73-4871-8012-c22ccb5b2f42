#!/usr/bin/env python3
"""
UltraLibrarian STEP file downloader - requires manual login
Since UltraLibrarian blocks automated logins, this script will:
1. Tell you to log in manually
2. Wait for you to confirm you're logged in
3. Then search and download the STEP file
"""

import requests
from bs4 import BeautifulSoup
import os
import time
from datetime import datetime

def log_message(message):
    print(message)
    with open('ultralibrarian_manual_log.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {message}\n")

def wait_for_manual_login():
    """Wait for user to manually log in"""
    log_message("🔐 MANUAL LOGIN REQUIRED")
    log_message("=" * 60)
    log_message("UltraLibrarian blocks automated logins.")
    log_message("Please follow these steps:")
    log_message("")
    log_message("1. Open your browser")
    log_message("2. Go to: https://www.ultralibrarian.com/wp-login.php")
    log_message("3. Log in with: lenny<PERSON><PERSON><PERSON>@gmail.com / Lennyai123#")
    log_message("4. Verify you can access your account")
    log_message("5. Come back here and press ENTER")
    log_message("")
    
    input("Press ENTER when you have successfully logged in manually...")
    
    log_message("✅ User confirmed manual login complete")
    return True

def get_session_cookies():
    """Get session cookies from user"""
    log_message("\n🍪 COOKIE EXTRACTION")
    log_message("=" * 60)
    log_message("We need your login cookies to access UltraLibrarian.")
    log_message("Please follow these steps:")
    log_message("")
    log_message("1. In your browser (while logged in), press F12")
    log_message("2. Go to Application tab (Chrome) or Storage tab (Firefox)")
    log_message("3. Click on Cookies → https://www.ultralibrarian.com")
    log_message("4. Find cookies that start with 'wordpress_logged_in_'")
    log_message("5. Copy the cookie value (the long string)")
    log_message("")
    
    cookie_value = input("Paste the wordpress_logged_in_ cookie value here: ").strip()
    
    if not cookie_value:
        log_message("❌ No cookie provided")
        return None
    
    log_message(f"✅ Got cookie ({len(cookie_value)} chars)")
    return cookie_value

def search_ultralibrarian_with_cookies(cookie_value, part_number):
    """Search UltraLibrarian using manual login cookies"""
    log_message(f"\n🔍 SEARCHING ULTRALIBRARIAN FOR {part_number}")
    log_message("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    # Set the login cookie
    session.cookies.set('wordpress_logged_in_ultralibrarian', cookie_value, domain='www.ultralibrarian.com')
    
    try:
        # Test if we're logged in by accessing a protected page
        log_message("1. Testing login status...")
        test_response = session.get('https://www.ultralibrarian.com/wp-admin/', timeout=30)
        
        if test_response.status_code == 200 and 'wp-admin' in test_response.url:
            log_message("   ✅ Successfully authenticated!")
        else:
            log_message("   ❌ Authentication failed - cookie might be wrong")
            return False
        
        # Search for the part
        log_message(f"2. Searching for {part_number}...")
        search_url = f"https://www.ultralibrarian.com/search?q={part_number}"
        
        search_response = session.get(search_url, timeout=30)
        log_message(f"   Search status: {search_response.status_code}")
        
        if search_response.status_code != 200:
            log_message("   ❌ Search failed")
            return False
        
        # Save search results
        with open('ultralibrarian_authenticated_search.html', 'w', encoding='utf-8') as f:
            f.write(search_response.text)
        log_message("   📄 Saved search results")
        
        # Check if part found
        if part_number.lower() in search_response.text.lower():
            log_message(f"   ✅ Found {part_number} in search results!")
            
            # Look for download links
            soup = BeautifulSoup(search_response.text, 'html.parser')
            
            # Look for STEP download links
            step_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text(strip=True).lower()
                
                if any(keyword in text for keyword in ['download', 'step', '3d', 'model']) or \
                   any(ext in href.lower() for ext in ['.step', '.stp']):
                    step_links.append({
                        'url': href,
                        'text': text
                    })
            
            log_message(f"   Found {len(step_links)} potential download links:")
            for i, link in enumerate(step_links, 1):
                log_message(f"   {i}. {link['text']}")
                log_message(f"      URL: {link['url']}")
            
            # Try to download STEP files
            for link in step_links:
                if download_step_file(session, link, part_number):
                    return True
            
            log_message("   ⚠️  Found links but no STEP files downloaded")
            return False
        else:
            log_message(f"   ❌ {part_number} not found in search results")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Error: {e}")
        return False

def download_step_file(session, link, part_number):
    """Try to download a STEP file"""
    log_message(f"   🔽 Attempting to download STEP file...")
    
    try:
        # Make URL absolute
        url = link['url']
        if not url.startswith('http'):
            url = f"https://www.ultralibrarian.com{url}"
        
        log_message(f"   Download URL: {url}")
        
        download_response = session.get(url, timeout=60, stream=True)
        log_message(f"   Download status: {download_response.status_code}")
        
        if download_response.status_code == 200:
            # Create 3d directory
            os.makedirs('3d', exist_ok=True)
            
            # Determine filename
            filename = f"{part_number}_UltraLibrarian.step"
            filepath = os.path.join('3d', filename)
            
            # Save file
            with open(filepath, 'wb') as f:
                for chunk in download_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            log_message(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                # Check if it's actually a STEP file
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    first_lines = f.read(200)
                
                if 'ISO-10303' in first_lines or 'STEP' in first_lines:
                    log_message(f"   🎉 SUCCESS: Valid STEP file downloaded!")
                    return True
                else:
                    log_message(f"   ⚠️  File doesn't appear to be STEP format")
                    return False
            else:
                log_message(f"   ⚠️  File too small, might be error page")
                return False
        else:
            log_message(f"   ❌ Download failed: {download_response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Download error: {e}")
        return False

def main():
    # Clear log
    with open('ultralibrarian_manual_log.txt', 'w') as f:
        f.write(f"ULTRALIBRARIAN MANUAL LOGIN LOG - {datetime.now()}\n")
        f.write("=" * 60 + "\n")
    
    part_number = "APX803L20-30SA-7"
    
    log_message("🚀 ULTRALIBRARIAN MANUAL LOGIN STEP DOWNLOADER")
    log_message("=" * 60)
    
    # Step 1: Wait for manual login
    if not wait_for_manual_login():
        return
    
    # Step 2: Get cookies
    cookie_value = get_session_cookies()
    if not cookie_value:
        return
    
    # Step 3: Search and download
    success = search_ultralibrarian_with_cookies(cookie_value, part_number)
    
    # Summary
    log_message("\n" + "=" * 60)
    if success:
        log_message("🎉 SUCCESS: Downloaded STEP file from UltraLibrarian!")
    else:
        log_message("❌ FAILED: Could not download STEP file")
    
    # Check for actual STEP files
    step_files = []
    if os.path.exists('3d'):
        for file in os.listdir('3d'):
            if file.endswith('.step') and not file.startswith('KiCad_'):
                step_files.append(file)
    
    if step_files:
        log_message(f"\n🎯 STEP FILES DOWNLOADED:")
        for file in step_files:
            filepath = os.path.join('3d', file)
            size = os.path.getsize(filepath)
            log_message(f"   ✅ {file} ({size:,} bytes)")
        log_message(f"\n🎉 SUCCESS: We now have real manufacturer STEP files!")
    else:
        log_message(f"\n❌ NO STEP FILES DOWNLOADED")
    
    log_message(f"\n📄 Full log saved to ultralibrarian_manual_log.txt")

if __name__ == "__main__":
    main()
