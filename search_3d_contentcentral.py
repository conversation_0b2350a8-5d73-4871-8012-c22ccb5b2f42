#!/usr/bin/env python3
"""
Search 3D ContentCentral for APX803L20-30SA-7 STEP files
3D ContentCentral is Dassault's official 3D model library
"""

import requests
from bs4 import BeautifulSoup
import os
import time

def search_3d_contentcentral(part_number):
    print(f"🔍 SEARCHING 3D CONTENTCENTRAL FOR: {part_number}")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
    })
    
    print("\n1. Accessing 3D ContentCentral...")
    
    try:
        # 3D ContentCentral search URL
        base_url = "https://www.3dcontentcentral.com"
        search_url = f"{base_url}/search.aspx"
        
        # First get the search page to see the form structure
        print(f"   Getting search page: {search_url}")
        search_page = session.get(search_url, timeout=30)
        print(f"   Search page status: {search_page.status_code}")
        
        if search_page.status_code != 200:
            print(f"   ❌ Cannot access 3D ContentCentral")
            return False
        
        # Save the search page for inspection
        with open('3dcontentcentral_search_page.html', 'w', encoding='utf-8') as f:
            f.write(search_page.text)
        print("   📄 Saved search page to 3dcontentcentral_search_page.html")
        
        # Try direct search with parameters
        print(f"\n2. Searching for {part_number}...")
        search_params = {
            'searchterm': part_number,
            'searchtype': 'parts'
        }
        
        search_response = session.get(search_url, params=search_params, timeout=30)
        print(f"   Search status: {search_response.status_code}")
        print(f"   Final URL: {search_response.url}")
        
        # Save search results
        with open('3dcontentcentral_search_results.html', 'w', encoding='utf-8') as f:
            f.write(search_response.text)
        print("   📄 Saved search results to 3dcontentcentral_search_results.html")
        
        # Parse results
        soup = BeautifulSoup(search_response.text, 'html.parser')
        
        # Look for part results
        part_links = []
        download_links = []
        
        # Look for various link patterns that might indicate parts or downloads
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            text = link.get_text(strip=True)
            
            if href:
                # Look for part detail pages
                if 'detail' in href.lower() or 'part' in href.lower():
                    if part_number.lower() in text.lower():
                        full_url = href if href.startswith('http') else f"{base_url}{href}"
                        part_links.append({'url': full_url, 'text': text})
                
                # Look for direct download links
                if any(ext in href.lower() for ext in ['.step', '.stp', '.zip']):
                    full_url = href if href.startswith('http') else f"{base_url}{href}"
                    download_links.append({'url': full_url, 'text': text})
        
        print(f"\n3. Analysis Results:")
        print(f"   Part detail pages found: {len(part_links)}")
        print(f"   Direct download links found: {len(download_links)}")
        
        # Show part links
        if part_links:
            print(f"\n   Part detail pages:")
            for i, link in enumerate(part_links[:3], 1):
                print(f"   {i}. {link['text']}")
                print(f"      URL: {link['url']}")
        
        # Show download links
        if download_links:
            print(f"\n   Direct download links:")
            for i, link in enumerate(download_links[:3], 1):
                print(f"   {i}. {link['text']}")
                print(f"      URL: {link['url']}")
                
            # Try to download the first STEP file
            return try_download_step_file(session, download_links[0], part_number)
        
        # If no direct downloads, try the first part page
        if part_links:
            return check_part_detail_page(session, part_links[0], part_number)
        
        print(f"   ❌ No relevant results found for {part_number}")
        return False
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def check_part_detail_page(session, part_link, part_number):
    print(f"\n4. Checking part detail page...")
    print(f"   URL: {part_link['url']}")
    
    try:
        detail_response = session.get(part_link['url'], timeout=30)
        print(f"   Detail page status: {detail_response.status_code}")
        
        if detail_response.status_code != 200:
            return False
        
        # Save detail page
        with open('3dcontentcentral_detail_page.html', 'w', encoding='utf-8') as f:
            f.write(detail_response.text)
        print("   📄 Saved detail page")
        
        # Look for download buttons/links on the detail page
        soup = BeautifulSoup(detail_response.text, 'html.parser')
        
        download_buttons = []
        for element in soup.find_all(['a', 'button'], href=True):
            href = element.get('href', '')
            text = element.get_text(strip=True).lower()
            
            if any(keyword in text for keyword in ['download', 'step', 'stp', 'cad']):
                if any(ext in href.lower() for ext in ['.step', '.stp', '.zip']):
                    base_url = "https://www.3dcontentcentral.com"
                    full_url = href if href.startswith('http') else f"{base_url}{href}"
                    download_buttons.append({'url': full_url, 'text': text})
        
        print(f"   Found {len(download_buttons)} download options:")
        for i, btn in enumerate(download_buttons[:3], 1):
            print(f"   {i}. {btn['text']}")
            print(f"      URL: {btn['url']}")
        
        if download_buttons:
            return try_download_step_file(session, download_buttons[0], part_number)
        
        return False
        
    except Exception as e:
        print(f"   ❌ Error checking detail page: {e}")
        return False

def try_download_step_file(session, download_link, part_number):
    print(f"\n5. Attempting to download STEP file...")
    print(f"   Download URL: {download_link['url']}")
    
    try:
        # Create 3d directory if it doesn't exist
        os.makedirs('3d', exist_ok=True)
        
        download_response = session.get(download_link['url'], timeout=60, stream=True)
        print(f"   Download status: {download_response.status_code}")
        
        if download_response.status_code == 200:
            # Determine filename
            filename = f"{part_number}_3DContentCentral.step"
            filepath = os.path.join('3d', filename)
            
            # Save the file
            with open(filepath, 'wb') as f:
                for chunk in download_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"   ✅ Downloaded: {filename}")
            print(f"   📁 Location: {filepath}")
            print(f"   📏 Size: {file_size:,} bytes")
            
            if file_size > 1000:  # Reasonable size for a STEP file
                print(f"   🎉 SUCCESS: Downloaded actual STEP file!")
                return True
            else:
                print(f"   ⚠️  File seems too small, might be an error page")
                return False
        else:
            print(f"   ❌ Download failed with status {download_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Download error: {e}")
        return False

def main():
    part_number = "APX803L20-30SA-7"
    
    print("🚀 3D CONTENTCENTRAL SEARCH")
    print("=" * 50)
    
    success = search_3d_contentcentral(part_number)
    
    if success:
        print(f"\n✅ SUCCESS: Downloaded STEP file for {part_number}!")
        print(f"\n📁 Check the '3d' folder for your STEP file")
        print(f"🎯 This is a real manufacturer STEP file, not a generic KiCad model")
    else:
        print(f"\n❌ Could not find/download STEP file for {part_number}")
        print(f"\n💡 Next steps:")
        print(f"   1. Check saved HTML files for manual inspection")
        print(f"   2. Try TraceParts or GrabCAD")
        print(f"   3. Contact Diodes Inc directly")

if __name__ == "__main__":
    main()
