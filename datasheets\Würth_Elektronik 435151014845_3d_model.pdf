













	<!DOCTYPE html>
	
		<html lang="en">
	
	<head>
		
		
			
				
			
			<link rel="canonical" href="https://www.we-online.com/en/company/quality/overview-certificates" /> <link rel="alternate" hreflang="en" href="https://www.we-online.com/en/company/quality/overview-certificates"/> <link rel="alternate" hreflang="x-default" href="https://www.we-online.com/en/company/quality/overview-certificates"/> <link rel="alternate" hreflang="de" href="https://www.we-online.com/de/unternehmen/qualitaet/ueberblick-zertifikate"/> 
    <script>
        (function () {
			const errorLog = new Set();
			let isReportingError = false;
			const blockedFetchUrl = "https://www.we-online.com/en/special/storedb";
			const environment = "prod";

			function reportError(error) {
				if (isReportingError) return;
				isReportingError = true;

				try {
					const errorKey = JSON.stringify(error);
					if (errorLog.has(errorKey)) return; // Prevent duplicate logging
					errorLog.add(errorKey);

					console.error("🔴 Captured Error from WEAPP_49029730_bae2c06047b84700-7C49BD62-CAF3-2C2A-C4AECD0146AC9B92:", error);

					let navigator_details = {
						user_agent: navigator.userAgent,
						platform: navigator.platform,
						language: navigator.language,
						url: window.location.href,
						screen_resolution: `${screen.width}x${screen.height}`,
						connection_type: navigator.connection?.effectiveType || "unknown"
					};

					fetch("https://www.we-online.com/en/special/storedb", {
						method: "POST",
						headers: { "Content-Type": "application/json" },
						body: JSON.stringify({
							error,
							sessionid: "WEAPP_49029730_bae2c06047b84700-7C49BD62-CAF3-2C2A-C4AECD0146AC9B92",
							navigator_details,
							env: environment
						}),
					}).catch(err => console.warn("Error reporting failed:", err));
				} finally {
					isReportingError = false;
				}
			}

			// ✅ Capture console errors
			const originalConsoleError = console.error;
			console.error = function (...args) {
				reportError({ type: "console", message: args.map(arg => typeof arg === "object" ? JSON.stringify(arg) : arg).join(" ") });
				originalConsoleError.apply(console, args);
			};

			// ✅ Capture JavaScript runtime errors
			window.onerror = function (message, source, lineno, colno, error) {
				reportError({ type: "runtime", message, source, lineno, colno, stack: error?.stack });
			};

			// ✅ Capture unhandled promise rejections
			window.addEventListener("unhandledrejection", function (event) {
				if (event.reason instanceof Error && event.reason.message === 'Failed to fetch') return;
				reportError({ type: "promise", message: JSON.stringify(event.reason) });
			});

			// ✅ Capture failed fetch requests
			const originalFetch = window.fetch;
			window.fetch = async function (...args) {
				const url = args[0];
				const options = args[1] || {};

				if (url === blockedFetchUrl) return originalFetch.apply(this, args);

				try {
					const response = await originalFetch(...args);
					if (!response.ok) {
						throw new Error(`HTTP Error: ${response.status} - ${response.statusText}`);
					}
					return response;
				} catch (error) {
					reportError({
						type: "network",
						message: error.message,
						url,
						method: options.method || "GET",
						headers: options.headers || {},
						body: options.body || null
					});
					return Promise.reject(error);
				}
			};

			// ✅ Capture XMLHttpRequest errors
			const originalOpen = XMLHttpRequest.prototype.open;
			const originalSend = XMLHttpRequest.prototype.send;

			XMLHttpRequest.prototype.open = function (method, url) {
				this._requestMethod = method;
				this._requestURL = url;
				return originalOpen.apply(this, arguments);
			};

			XMLHttpRequest.prototype.send = function (body) {
				this._requestBody = body;

				this.addEventListener("error", function () {
					reportError({
						type: "xhr",
						message: "Network request failed",
						url: this._requestURL,
						method: this._requestMethod,
						requestBody: this._requestBody,
						status: this.status,
						statusText: this.statusText,
						responseText: this.responseText
					});
				});

				this.addEventListener("load", function () {
					if (this.status < 200 || this.status >= 300) {
						reportError({
							type: "xhr",
							message: `HTTP Error: ${this.status} - ${this.statusText}`,
							url: this._requestURL,
							method: this._requestMethod,
							requestBody: this._requestBody,
							responseStatus: this.status,
							responseText: this.responseText,
							responseHeaders: this.getAllResponseHeaders()
						});
					}
				});

				this.addEventListener("timeout", function () {
					reportError({
						type: "xhr",
						message: "Request timed out",
						url: this._requestURL,
						method: this._requestMethod,
						requestBody: this._requestBody
					});
				});

				this.addEventListener("abort", function () {
					reportError({
						type: "xhr",
						message: "Request aborted",
						url: this._requestURL,
						method: this._requestMethod,
						requestBody: this._requestBody
					});
				});

				return originalSend.apply(this, arguments);
			};

			// ✅ Capture failed resource loads
			window.addEventListener("error", function (event) {
				if (event.target instanceof HTMLImageElement || event.target instanceof HTMLScriptElement || event.target instanceof HTMLLinkElement) {
					reportError({ type: "resource", url: event.target.src || event.target.href });
				}
			}, true);
		})();

    </script>

				
			
			<script src="https://coco.we-online.com/public/app.js?apiKey=7cfcf0c27eb2981e8bc2442e583f8b782e8d74fd9fa379ed&amp;domain=350c57b" referrerpolicy="origin"></script>
			<script type="text/javascript" async src="https://www.we-online.com/assets/custom.coco.js"></script>
		
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta http-equiv="x-ua-compatible" content="ie=edge" />
		<meta name="viewport" content="width=device-width" />
		<meta name="robots" content="index, follow">
		<meta name="keywords" content="certifications,certificates,certificat,certification">
		
		
			
			
			




	
		
			<script type="application/ld+json">
				{"potentialAction":{"@type":"SearchAction","query-input":"required name=search_term_string","target":{"@type":"EntryPoint","urlTemplate":"https://www.we-online.com/en/system/search?sword={search_term_string}"}},"@context":"https://schema.org","@type":"WebSite","url":"https://www.we-online.com/","name":"Würth Elektronik","alternateName":["WE","Wuerth Elektronik","we-online.com","we-online.de","wuerth-elektronik","wuerthelektronik","wuerth electronics"]}
			</script>
		
	
	


		
		<link rel="shortcut icon" href="https://www.we-online.com/files/png1/favicon_we_2022.png" type="image/png">

		<link rel="stylesheet" href="https://www.we-online.com/assets/main.css"/>
		<link rel="stylesheet" href="https://www.we-online.com/assets/collections.css"/>
		<link rel="stylesheet" href="https://www.we-online.com/assets/main-desktop.css" media="(min-width: 700px)"/>

		

		<link rel="preload" as="style" href="https://www.we-online.com/assets/main-print.css" media="print" onload="this.onload=null;this.rel='stylesheet'"/>

		
		
			<script src="https://www.we-online.com/assets/runtime.js"></script>
		

		
		
			
		
		
	<meta name="CONTENS CMS" property="pid" content="642" />
<!-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
Published by CONTENS CMS 5.8.0 | CONTENS Software GmbH, 1999 - 2025 | www.contens.com
PageID: 642 | FolderID: 413 | ChannelID: 1 | TargetgroupID: 1
Last Update: 2025-07-24 07:53 (Generated: 2025-07-24 08:05, Published: 2025-07-24 08:05)
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - --><meta property="og:url" content="https://www.we-online.com/en/company/quality/overview-certificates" /><meta property="og:locale" content="en"/><meta name="twitter:card" content="summary_large_image"/>
				<meta charset="UTF-8">
				<meta property="og:type" content="website">
				<title>Learn more about the WE certificates</title>
				<meta name="title" content="Learn more about the WE certificates">
				<meta property="og:title" content="Learn more about the WE certificates" />
				<meta name="twitter:title" content="Learn more about the WE certificates" />
				<meta name="description" content="Quality that convinces. As an international company, we offer &quot;One Global Quality&quot;. Info on our quality certificates, environmental certificates, material compliance and conflict materials.">
				<meta property="og:description" content="Quality that convinces. As an international company, we offer &quot;One Global Quality&quot;. Info on our quality certificates, environmental certificates, material compliance and conflict materials." />
				<meta name="twitter:description" content="Quality that convinces. As an international company, we offer &quot;One Global Quality&quot;. Info on our quality certificates, environmental certificates, material compliance and conflict materials." />
			
                              <script>!function(e){var n="https://s.go-mpulse.net/boomerang/";if("False"=="True")e.BOOMR_config=e.BOOMR_config||{},e.BOOMR_config.PageParams=e.BOOMR_config.PageParams||{},e.BOOMR_config.PageParams.pci=!0,n="https://s2.go-mpulse.net/boomerang/";if(window.BOOMR_API_key="SCEWZ-F86WX-HCWTH-5J344-G5CP9",function(){function e(){if(!o){var e=document.createElement("script");e.id="boomr-scr-as",e.src=window.BOOMR.url,e.async=!0,i.parentNode.appendChild(e),o=!0}}function t(e){o=!0;var n,t,a,r,d=document,O=window;if(window.BOOMR.snippetMethod=e?"if":"i",t=function(e,n){var t=d.createElement("script");t.id=n||"boomr-if-as",t.src=window.BOOMR.url,BOOMR_lstart=(new Date).getTime(),e=e||d.body,e.appendChild(t)},!window.addEventListener&&window.attachEvent&&navigator.userAgent.match(/MSIE [67]\./))return window.BOOMR.snippetMethod="s",void t(i.parentNode,"boomr-async");a=document.createElement("IFRAME"),a.src="about:blank",a.title="",a.role="presentation",a.loading="eager",r=(a.frameElement||a).style,r.width=0,r.height=0,r.border=0,r.display="none",i.parentNode.appendChild(a);try{O=a.contentWindow,d=O.document.open()}catch(_){n=document.domain,a.src="javascript:var d=document.open();d.domain='"+n+"';void(0);",O=a.contentWindow,d=O.document.open()}if(n)d._boomrl=function(){this.domain=n,t()},d.write("<bo"+"dy onload='document._boomrl();'>");else if(O._boomrl=function(){t()},O.addEventListener)O.addEventListener("load",O._boomrl,!1);else if(O.attachEvent)O.attachEvent("onload",O._boomrl);d.close()}function a(e){window.BOOMR_onload=e&&e.timeStamp||(new Date).getTime()}if(!window.BOOMR||!window.BOOMR.version&&!window.BOOMR.snippetExecuted){window.BOOMR=window.BOOMR||{},window.BOOMR.snippetStart=(new Date).getTime(),window.BOOMR.snippetExecuted=!0,window.BOOMR.snippetVersion=12,window.BOOMR.url=n+"SCEWZ-F86WX-HCWTH-5J344-G5CP9";var i=document.currentScript||document.getElementsByTagName("script")[0],o=!1,r=document.createElement("link");if(r.relList&&"function"==typeof r.relList.supports&&r.relList.supports("preload")&&"as"in r)window.BOOMR.snippetMethod="p",r.href=window.BOOMR.url,r.rel="preload",r.as="script",r.addEventListener("load",e),r.addEventListener("error",function(){t(!0)}),setTimeout(function(){if(!o)t(!0)},3e3),BOOMR_lstart=(new Date).getTime(),i.parentNode.appendChild(r);else t(!1);if(window.addEventListener)window.addEventListener("load",a,!1);else if(window.attachEvent)window.attachEvent("onload",a)}}(),"".length>0)if(e&&"performance"in e&&e.performance&&"function"==typeof e.performance.setResourceTimingBufferSize)e.performance.setResourceTimingBufferSize();!function(){if(BOOMR=e.BOOMR||{},BOOMR.plugins=BOOMR.plugins||{},!BOOMR.plugins.AK){var n=""=="true"?1:0,t="",a="eyd7xei3j5kskjqacqdlyaaaezukutdo-f-e163df9bc-clienttons-s.akamaihd.net",i="false"=="true"?2:1,o={"ak.v":"39","ak.cp":"1532442","ak.ai":parseInt("975835",10),"ak.ol":"0","ak.cr":98,"ak.ipv":6,"ak.proto":"http/1.1","ak.rid":"8fb2e8a","ak.r":40285,"ak.a2":n,"ak.m":"dsca","ak.n":"essl","ak.bpcip":"2607:fb91:1b4f:5525::","ak.cport":52413,"ak.gh":"************","ak.quicv":"","ak.tlsv":"tls1.3","ak.0rtt":"","ak.0rtt.ed":"","ak.csrc":"-","ak.acc":"bbr","ak.t":"1755991150","ak.ak":"hOBiQwZUYzCg5VSAfCLimQ==NAEil7qnX5+ZjYNTtg8/LRVHNXzHt7ZpFc9ugIXPumKaFpwXYyt1swcWUBIZMrlD7ahZ2Hga63eOhLlA+zaEKWwLJ5s81FaECHI70vwSWMFbCXZK1Op7I2QSB2A1F0ll/BBVil6Nz63JreGTYWkPXlS6DQc8j89FotAwkitekwj8f4OUGznBTBfvhuASrynGv1jhF66bSXVWAxJWpn8wSPJ131ratjcdeufQllEcag0YmGZCrA9DYoWBqHs+qmgSGoyOqAhXvjU0+Hveh1lGsFSw9Qky5E59u4vdsynWYYvga4WK5yqKmImmVOqN7JKhgqY/Q2tgZYMiYfPLIosnWNBgn37p8dKxTWjYgCVHCcGNYb83Ql6gjcslldp+i+DJp6qgTp5RhJONk5Cpp8vqxKJ+IAK6484SumFD0UccdN4=","ak.pv":"28","ak.dpoabenc":"","ak.tf":i};if(""!==t)o["ak.ruds"]=t;var r={i:!1,av:function(n){var t="http.initiator";if(n&&(!n[t]||"spa_hard"===n[t]))o["ak.feo"]=void 0!==e.aFeoApplied?1:0,BOOMR.addVar(o)},rv:function(){var e=["ak.bpcip","ak.cport","ak.cr","ak.csrc","ak.gh","ak.ipv","ak.m","ak.n","ak.ol","ak.proto","ak.quicv","ak.tlsv","ak.0rtt","ak.0rtt.ed","ak.r","ak.acc","ak.t","ak.tf"];BOOMR.removeVar(e)}};BOOMR.plugins.AK={akVars:o,akDNSPreFetchDomain:a,init:function(){if(!r.i){var e=BOOMR.subscribe;e("before_beacon",r.av,null,null),e("onbeacon",r.rv,null,null),r.i=!0}return this},is_complete:function(){return!0}}}}()}(window);</script></head>
	<body>
		
		
		
			
				
			
		
		

		
		
		
		
			
			

			

	<div class="weWidgetSide weWidgetSide--hidden">
		<div class="weWidgetSide__toggleButton ">
			<svg class="weSwiperNewsImage__arrowIcon" width="20" height="20" viewBox="230 0 298 511">
				<use href="#webSvg__angleLeft-24"></use>
			</svg>
		</div>
		<div class="weWidgetSide__content weWidgetSide__content--hidden">
			<div class="weWidgetSide__content__toggleButton">
				<svg class="weSwiperNewsImage__arrowIcon" width="20" height="20" viewBox="0 0 298 511">
					<use href="#webSvg__angleRight-24"></use>
				</svg>
			</div>
			<div class="weWidgetSide__content__elements">
				
					
                        <div class="weWidgetSide__content__elements__element weWidgetSide__content__elements__element--not-last">
                            <a class="weWidgetSide__content__elements__element__tile" href="https://www.we-online.com/en/support/contact" target="_self">
                                <div class="weWidgetSide__content__elements__element__tileImageContainer">
                                    <img class="weWidgetSide__content__elements__element__tileImage" src="https://www.we-online.com/files/svg1/we_webicon_contact_neg-11.svg" />
                                </div>
                                <div class="weWidgetSide__content__elements__element__tileTitle">Contact</div>
                            </a>
                        </div>
					
				
					
                        <div class="weWidgetSide__content__elements__element weWidgetSide__content__elements__element--not-last">
                            <a class="weWidgetSide__content__elements__element__tile" href="https://www.we-online.com/en/company/quality#i6311" target="_self">
                                <div class="weWidgetSide__content__elements__element__tileImageContainer">
                                    <img class="weWidgetSide__content__elements__element__tileImage" src="https://www.we-online.com/files/svg1/lip_geschaeftsbedingungen_neg-1111.svg" />
                                </div>
                                <div class="weWidgetSide__content__elements__element__tileTitle">Important documents</div>
                            </a>
                        </div>
					
				
					
                        <div class="weWidgetSide__content__elements__element weWidgetSide__content__elements__element--not-last">
                            <a class="weWidgetSide__content__elements__element__tile" href="https://www.we-online.com/en/career/open-positions" target="_self">
                                <div class="weWidgetSide__content__elements__element__tileImageContainer">
                                    <img class="weWidgetSide__content__elements__element__tileImage" src="https://www.we-online.com/files/svg1/we_webicon_jobs_neg-11.svg" />
                                </div>
                                <div class="weWidgetSide__content__elements__element__tileTitle">Jobs</div>
                            </a>
                        </div>
					
				
					
                        <div class="weWidgetSide__content__elements__element ">
                            <a class="weWidgetSide__content__elements__element__tile" href="https://www.we-online.com/en/special/surveys/online-user-tests" target="_self">
                                <div class="weWidgetSide__content__elements__element__tileImageContainer">
                                    <img class="weWidgetSide__content__elements__element__tileImage" src="https://www.we-online.com/files/svg1/lip_geschaeftsbedingungen_neg-1111111111111112.svg" />
                                </div>
                                <div class="weWidgetSide__content__elements__element__tileTitle">Online Evaluations</div>
                            </a>
                        </div>
					
				
			</div>
		</div>
	</div>



		
		
	




	<header class="weHeader" data-menu-payload="https://www.we-online.com/en/system/navigation.cfm?dt=66038873">
		<script type="text/html" data-menu-fragment="mobileHeader">
			











































































	<div class="weHeaderMenuMobile__header">
		<div class="weHeaderMenuMobile__widget weHeaderMenuMobile__widget--root">
			<div class="weHeaderMenuMobile__widgetPage weHeaderMenuMobile__widgetPage--root">
				<a class="weHeaderMenuMobile__widgetLogo weUtils__dNone--md" href="https://www.we-online.com/en">
					<svg class="weHeaderMenuMobile__widgetLogoSvg" viewBox="0 0 46 46">
						<use href="#webSvg__logo-46">
					</svg>
				</a>
				













































































	
	
		
	





	<form class="weHeaderSearch weHeaderSearch--incremental weHeaderSearch--menuMobile" method="get" action="https://www.we-online.com/en/system/search">
		<label class="weHeaderSearch__label" for="weHeaderSearchInput--menuMobile">
			<svg width="24" height="24">
				<use href="#webSvg__search-24" />
			</svg>
		</label>

		<input
			autocomplete="off"
			class="weHeaderSearch__input"
			id="weHeaderSearchInput--menuMobile"
			name="sword"
			placeholder="Enter a search term..."
			type="text"
		/>

		<button class="weHeaderSearch__submit" type="submit" aria-label="Search">
			<svg width="24" height="24">
				<use href="#webSvg__arrowRight-24" />
			</svg>
		</button>
	</form>


			</div>

			<div class="weHeaderMenuMobile__widgetPage weHeaderMenuMobile__widgetPage--child">
				<button class="weHeaderMenuMobile__widgetButton" data-menu-action="gotoParent">
					<svg class="weHeaderMenuMobile__widgetButtonIcon" width="24" height="24">
						<use href="#webSvg__arrowLeft-24">
					</svg>
				</button>
				<div class="weHeaderMenuMobile__widgetDivider weHeaderMenuMobile__widgetDivider--fillRight"></div>
				<div class="weHeaderMenuMobile__widgetDivider weUtils__dNone weUtils__dBlock--md"></div>
			</div>
		</div>

		<div class="weHeaderMenuMobile__headerTools">
			














    



	<button href="false" data-menu-action="hideMenu" class="weHeader__barToolsButton">
		
			<svg class="weHeader__barToolsIcon" width="24" height="24">
				<use href="#webSvg__closeLg-24" />
			</svg>
		
		
	</button>


		</div>
	</div>


		</script>
		
		
			<script type="text/html" data-menu-fragment="mobileLoginButton">
				




	<div class="weHeaderMenuMobile__login">
		<a class="weHeaderMenuMobile__loginButton" href='https://auth01.we-online.com/as/authorization.oauth2?code_challenge=B_W20Qpmx_TVdqdmL2YIKQWrK7MkbtlaCREm6ix7xnU&client_id=we.online.prod&redirect_uri=https://www.we-online.com/en/dashboard&code_challenge_method=S256&response_type=code '>
			<svg class="weHeaderMenuMobile__loginButtonIcon" width="24" height="24">
				<use href="#webSvg__user-24"></use>
			</svg>
			<span class="weHeaderMenuMobile__loginButtonLabel">Login</span>
		</a>
	</div>


			</script>
		

		<script type="text/html" data-menu-fragment="mobileRootFooter">
			













  <ul class="weHeaderMenuMobile__footer">
		
      <li class="weHeaderMenuMobile__footerItem">
				<a class="weHeaderMenuMobile__footerLink" href="https://www.we-online.com/en/service/contact">Contact</a>
      </li>
    
      <li class="weHeaderMenuMobile__footerItem">
				<a class="weHeaderMenuMobile__footerLink" href="https://www.we-online.com/en/service/imprint">Imprint</a>
      </li>
    
      <li class="weHeaderMenuMobile__footerItem">
				<a class="weHeaderMenuMobile__footerLink" href="https://www.we-online.com/en/service/data-privacy">Data Privacy</a>
      </li>
    
		
			<li class="weHeaderMenuMobile__footerItem">
				<dl class="weUtilsFlyout" tabindex="-1">
					<dt CLASS="weUtilsFlyout__label weUtilsFlyout__label--arrow weHeaderMenuMobile__footerFlyoutLabel">ENGLISH</dt>
					<dd class="weUtilsFlyout__panel">
						









	



	<ul class="weHeader__barFlyoutMenu weUtils__languageNavigation">
		
			<li class="weHeader__barFlyoutMenuItem">
				<a class="weHeader__barFlyoutMenuLink" href="https://www.we-online.com/en/company/quality/overview-certificates" hreflang="en" lang="en">English</a>
			</li>
		
			<li class="weHeader__barFlyoutMenuItem">
				<a class="weHeader__barFlyoutMenuLink" href="https://www.we-online.com/de/unternehmen/qualitaet/ueberblick-zertifikate" hreflang="de" lang="de">Deutsch</a>
			</li>
		
	</ul>


					</dd>
				</dl>
			</li>
		
  </ul>


		</script>

		
			











































































	
	
		
	





	
	
	
		
	
	
	
		<template class="weSearchLayer__template">
			<div class="weSearchLayer">
				<div class="weSearchLayer__container">
					<div class="weSearchLayer__panel">
						<div class="weSearchLayer__panelHeader">
							<form class="weSearchLayer__form" action="https://www.we-online.com/en/system/search">
								<label class="weSearchLayer__formLabel" for="sword">
									<svg width="24" height="24">
										<use href="#webSvg__search-24" />
									</svg>
								</label>
								<input
									autocomplete="off"
									class="weSearchLayer__formInput"
									id="weSearchLayerInput"
									name="sword"
									placeholder="Suchbegriff eingeben..."
									type="text"
								/>
								<button class="weSearchLayer__formSubmit" type="submit">
									<svg width="24" height="24">
										<use href="#webSvg__arrowRight-24" />
									</svg>
								</button>
							</form>
							<button class="weSearchLayer__panelCloser" data-layer-close>
								<svg width="24" height="24"><use href="#webSvg__closeLg-24"/></svg>
							</button>
						</div>
						<div class="weSearchLayer__panelBody">
							<div class="weSearchLayer__panelContent weSearchLayer__panelContent--results">
							</div>
							<div class="weSearchLayer__panelFooter weUtils__dNone">
								<a class="weButton weButton--default" href="#">
									<span>Show all <span class="weSearchLayer__totalResultsLabel"></span> results</span>
									<svg width="24" height="24"><use href="#webSvg__arrowRight-24"></use></svg>
								</a>
							</div>
							<div class="weSearchLayer__panelPreloader weSearchLayer__panelPreloader--init">Loading results <span>...</span></div>
						</div>
					</div>
				</div>
			</div>
		</template>
	

		

		<div class="weHeader__bar">
			<div class="weHeader__barContainer">
				<div class="weHeader__barLogo">
					<a class="weHeader__barLogoLink" href="https://www.we-online.com/en" aria-label="Würth Elektronik Logo">
						


	<svg class="weHeader__barLogoSvg" width="46" height="46" viewBox="0 0 46 46">
		<defs>
            <path id="webSvg__arrowDown-16" fill="none" stroke="currentColor" d="m2 5 6 6 6-6"/>
            <path id="webSvg__arrowDown-24" fill="currentColor" d="m13.413 5h-2.826v9.778l-5.087-5.037v4.74l6.5 6.519 6.5-6.519v-4.74l-5.087 5.037z"/>
            <path id="webSvg__arrowDown-64" fill="currentColor" d="m29.416 17.371h5.168v17.88l9.302-9.211v8.669l-11.886 11.92-11.886-11.92v-8.669l9.302 9.211z"/>
            <path id="webSvg__arrowLeft-24" fill="currentColor" d="m20 14.413v-2.826h-9.778l5.037-5.087h-4.74l-6.519 6.5 6.519 6.5h4.74l-5.037-5.087z"/>
            <path id="webSvg__arrowLeft-64" fill="currentColor" d="m48 34.826v-5.652h-19.556l10.074-10.174h-9.481l-13.037 13 13.037 13h9.481l-10.074-10.174z"/>
            <path id="webSvg__arrowRight-64" fill="currentColor" d="m16 34.826v-5.652h19.556l-10.074-10.174h9.481l13.037 13-13.037 13h-9.481l10.074-10.174z"/>
            <path id="webSvg__arrowRight-24" fill="currentColor" d="m4 14.413v-2.826h9.778l-5.037-5.087h4.74l6.519 6.5-6.519 6.5h-4.74l5.037-5.087z"/>
            <path id="webSvg__angleRight-24" fill="currentColor" d="M70.77 499.85c-16.24 16.17-42.53 16.09-58.69-.15-16.17-16.25-16.09-42.54.15-58.7l185.5-185.03L12.23 70.93c-16.24-16.16-16.32-42.45-.15-58.7 16.16-16.24 42.45-16.32 58.69-.15l215.15 214.61c16.17 16.25 16.09 42.54-.15 58.7l-215 214.46z"/>
            <path id="webSvg__angleLeft-24" fill="currentColor" d="M441.23 499.85c16.24 16.17 42.53 16.09 58.69-.15 16.17-16.25 16.09-42.54-.15-58.7L314.27 256.97 497.77 70.93c16.24-16.16 16.32-42.45.15-58.7-16.16-16.24-42.45-16.32-58.69-.15L224.08 226.69c-16.17 16.25-16.09 42.54.15 58.7l215 214.46z"/>
            <path id="webSvg__arrowUp-24" fill="currentColor" d="m13.413 21h-2.826v-9.778l-5.087 5.037v-4.74l6.5-6.519 6.5 6.519v4.74l-5.087-5.037z"/>
            <path id="webSvg__basket-24" fill="currentColor" d="m21.262 6.335c.226 0 .44.104.579.282.14.179.189.411.135.631l-1.79 7.234c-.081.328-.376.559-.714.559h-11.453c-.34 0-.636-.234-.715-.565l-2.391-10.005h-1.847c-.407 0-.736-.329-.736-.735 0-.407.329-.736.736-.736h2.428c.34 0 .636.234.715.565l.662 2.77zm-12.661 7.234h10.296l1.426-5.763h-13.099zm6.849 4.979c0-1.352 1.1-2.453 2.453-2.453 1.352 0 2.452 1.101 2.452 2.453s-1.1 2.452-2.452 2.452c-1.353 0-2.453-1.1-2.453-2.452zm1.471 0c0 .541.44.981.981.981s.981-.44.981-.981-.44-.981-.981-.981-.981.44-.981.981zm-7.92-2.453c-1.353 0-2.453 1.101-2.453 2.453s1.1 2.452 2.453 2.452c1.352 0 2.452-1.1 2.452-2.452s-1.1-2.453-2.452-2.453zm-.001 3.434c-.541 0-.981-.44-.981-.981s.44-.981.981-.981.981.44.981.981-.44.981-.981.981z"/>
            <path id="webSvg__bookmark-24" fill="currentColor" d="m12.13 14.25 7.2 6.75v-18h-14.4v18zm-5.64 3.149v-12.839h11.28v12.839l-4.573-4.287c-.6-.563-1.534-.563-2.134 0z"/>
            <path id="webSvg__checkbox-16" fill="currentColor" d="m6.751 8.823 4.84-4.823 1.409 1.414-6.251 6.23-3.749-3.749 1.412-1.412z"/>
            <path id="webSvg__checkmark-24" fill="currentColor" d="m12 2c5.519 0 10 4.481 10 10s-4.481 10-10 10-10-4.481-10-10 4.481-10 10-10zm-1.249 10.823-2.339-2.34-1.412 1.412 3.749 3.749 6.251-6.23-1.409-1.414z"/>
            <path id="webSvg__chevronDown-24" fill="currentColor" d="m12 13.127-6.127-6.127-1.873 1.873 8 8 8-8-1.873-1.873z"/>
            <path id="webSvg__chevronLeft-16" fill="none" stroke="currentColor" d="m10.66666,1.66667l-6,6l6,6"/>
            <path id="webSvg__chevronLeft-24" fill="currentColor" d="m12.595 12-4.595 4.595 1.405 1.405 6-6-6-6-1.405 1.405z"/>
            <path id="webSvg__chevronLeft-64" fill="currentColor" d="m45.171 55.329-23.33-23.329 23.33-23.329-1.671-1.671-25 25 25 25z"/>
            <path id="webSvg__chevronRight-16" fill="none" stroke="currentColor" d="m5.25,1.75l6,6l-6,6"/>
            <path id="webSvg__chevronRight-64" fill="currentColor" d="m18.5 55.329 23.329-23.329-23.329-23.329 1.671-1.671 25 25-25 25z"/>
            <path id="webSvg__close-16" fill="currentColor" d="m6.498 8-3.498-3.498 1.502-1.502 3.498 3.498 3.498-3.498 1.502 1.502-3.498 3.498 3.498 3.498-1.502 1.502-3.498-3.498-3.498 3.498-1.502-1.502z"/>
            <path id="webSvg__closeLg-24" fill="currentColor" d="m10.363 12-7.363 7.364 1.636 1.636 7.364-7.363 7.364 7.363 1.636-1.636-7.364-7.364 7.364-7.364-1.636-1.636-7.364 7.364-7.364-7.363-1.636 1.636z"/>
            <path id="webSvg__download-24" fill="currentColor" d="m19.273 22v-2.727h-14.546v2.727zm-8.557-20h2.569v8.889l4.624-4.579v4.309l-5.909 5.926-5.909-5.926v-4.309l4.625 4.579z"/>
            <path id="webSvg__download-48" fill="currentColor" d="m24,0l0,32.87111l-14.76889,-14.78667l-1.57333,1.56889l17.45333,17.47556l17.45333,-17.47556l-1.57111,-1.56889l-14.77111,14.78667l0,-32.87111l-2.22222,0zm-20,47.77778l40,0l0,-2.22222l-40,0l0,2.22222z" />
            <path id="webSvg__filter-24" fill="currentColor" d="m15.273 3h-1.637v5.727h1.637zm-12.273 1.636h9.818v1.637h-9.818zm6.545 6.546h11.455v1.636h-11.455zm3.273 6.545h-9.818v1.637h9.818zm3.273-13.091h4.909v1.637h-4.909zm-9 4.091h1.636v5.728h-1.636zm8.182 6.546h-1.637v5.727h1.637zm.818 2.454h4.909v1.637h-4.909zm-9.818-6.545h-3.273v1.636h3.273z"/>
            <path id="webSvg__grid-16" fill="currentColor" d="M5 2H0V7H5V2ZM5 8.00098H0V13.001H5V8.00098ZM6 2H11V7H6V2ZM11 8H6V13H11V8ZM12 2H17V7H12V2ZM17 8H12V13H17V8Z"/>
            <path id="webSvg__list-16" fill="currentColor" d="M0 4H16V2H0V4ZM0 7H16V5H0V7ZM16 10H0V8H16V10ZM0 13H16V11H0V13Z"/>
            <path id="webSvg__logo-46" fill="currentColor" d="m18.682 32.167 11.114-18.75h13.621l-2.792 4.664h-8.012l-1.348 2.18h8.036l-2.771 4.659h-8.072l-1.601 2.674h8.075l-2.751 4.573zm-9.173-18.75h-4.842v18.75h5.064l2.164-3.221.468 3.221h4.914l11.172-18.75h-5.381l-5.967 9.835-.526-2.991h-4.972l-2.106 2.883z"/>
            <path id="webSvg__logoTypo" fill="currentColor" d="M42.8961 14.8228H34.8714V16.7945H37.8061V25.8051H40.0989V16.7945H42.8961V14.8228ZM23.9807 14.8228H16.8273V25.8051H23.9234V23.8677H19.0513V21.2998H22.9604V19.328H19.0513V16.7143H23.9234L23.9807 14.8228ZM7.56449 14.8228H0.456909V25.8051H7.56449V23.8677H2.62358V21.2998H6.53274V19.328H2.62358V16.7143H7.50717L7.56449 14.8228ZM12.7003 10.9824L14.8096 0H12.5169L11.1641 9.0679H10.9234L8.95161 0H6.31493L4.34315 9.0679H4.10241L2.7726 0.0114641H0.479833L2.49747 10.9824H5.8793L7.59888 2.62522L9.32992 10.9824H12.7003ZM9.08918 25.8051H15.3828V23.8333H11.3132V14.8228H9.08918V25.8051ZM16.3687 2.24691H18.6615V0.0114641H16.3687V2.24691ZM22.5133 2.24691H24.7373V0.0114641H22.5133V2.24691ZM24.7373 7.27954V3.27866H22.5133V7.3254C22.4756 7.81793 22.2533 8.27806 21.8909 8.61374C21.5285 8.94942 21.0527 9.13591 20.5588 9.13591C20.0648 9.13591 19.589 8.94942 19.2266 8.61374C18.8642 8.27806 18.6419 7.81793 18.6042 7.3254V3.27866H16.3114V7.27954C16.3114 10.0194 17.7558 11.1887 20.4957 11.1887C23.2356 11.1887 24.68 10.0423 24.68 7.27954H24.7373ZM27.7752 14.8228H25.6086V25.8051H27.8326V21.5979L29.3229 21.4259L31.1685 25.8051H33.725L31.2603 20.3369L33.725 14.8228H31.1342L29.3229 19.4885L27.8326 19.649L27.7752 14.8228ZM32.7047 3.65697C32.7047 4.63139 32.2461 5.39947 31.1342 5.39947H29.1394V1.96032H31.1685C31.3951 1.94597 31.622 1.98241 31.8327 2.06696C32.0434 2.15151 32.2326 2.28204 32.3864 2.44902C32.5402 2.61601 32.6547 2.81523 32.7217 3.03214C32.7887 3.24906 32.8064 3.4782 32.7735 3.70282L32.7047 3.65697ZM35.0663 3.70282C35.0663 1.2037 33.7021 0.0917107 31.1915 0.0917107H26.8467V10.9824H29.1394V7.30247H31.2832L32.5786 10.9824H35.0204L33.4613 6.77513C33.9911 6.44339 34.4205 5.97353 34.7033 5.41605C34.986 4.85857 35.1115 4.23456 35.0663 3.61111V3.70282ZM41.4287 1.96032H44.329V0.0114641H36.3043V1.97178H39.2391V10.9824H41.5319L41.4287 1.96032ZM46.6103 16.7143H48.628C49.6941 16.7143 50.2215 17.4136 50.2215 18.4683C50.2215 19.5229 49.7744 20.2222 48.628 20.2222H46.5645L46.6103 16.7143ZM48.628 14.8228H44.3864V25.8051H46.6103V22.1252H48.7541L50.038 25.8051H52.4798L50.9208 21.6094C51.4527 21.2791 51.8843 20.8098 52.1692 20.2522C52.454 19.6947 52.5813 19.0699 52.5372 18.4453C52.5372 15.9347 51.1615 14.8228 48.6624 14.8228H48.628ZM52.2162 10.9938H54.4402V0.0114641H52.2162V4.49383H48.135V0.0114641H45.8423V10.9824H48.135V6.39683H52.2162V10.9938ZM60.8599 20.3827C60.8599 22.9162 60.4128 24.0626 58.5671 24.0626C56.7215 24.0626 56.2744 22.9162 56.2744 20.3827C56.2744 17.8492 56.7215 16.5653 58.5671 16.5653C60.4128 16.5653 60.8599 17.8492 60.8599 20.3827ZM63.1527 20.3827C63.1527 16.6684 62.0063 14.6508 58.5671 14.6508C55.128 14.6508 53.9816 16.6914 53.9816 20.3827C53.9816 24.0741 55.128 26 58.5671 26C62.0063 26 63.1527 24.097 63.1527 20.3827ZM70.0998 25.8051H73.7682V14.8228H71.5557V23.8677H71.3952L68.7356 14.8228H64.9754V25.8051H67.2109V16.7143H67.3714L70.0998 25.8051ZM76.1068 25.8051H78.3423V14.8228H76.1412L76.1068 25.8051ZM86.1033 20.3369L88.4878 14.8228H85.9543L84.1659 19.4885L82.6871 19.649V14.8228H80.3943V25.8051H82.6871V21.5979L84.1659 21.4259L86.0116 25.8051H88.5795L86.1033 20.3369Z" />
            <path id="webSvg__logout-18" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M4.21875 0C3.75163 0 3.375 0.378616 3.375 0.84375V5.90625C3.375 6.37338 3.75163 6.75 4.21875 6.75C4.68588 6.75 5.0625 6.37338 5.0625 5.90625V1.6875H16.3125V16.3125H5.0625V12.6562C5.0625 12.1891 4.68588 11.8125 4.21875 11.8125C3.75163 11.8125 3.375 12.1891 3.375 12.6562V17.1562C3.375 17.6234 3.75163 18 4.21875 18H17.1562C17.6234 18 18 17.6234 18 17.1562V0.84375C18 0.378616 17.6234 0 17.1562 0H4.21875ZM10.4179 4.73438C10.0893 4.40625 9.41004 4.40625 9.08191 4.73438C8.75379 5.0625 8.75379 5.74179 9.08191 6.06991L11.2412 8.4375H0.84375C0.378616 8.4375 0 8.81413 0 9.28125C0 9.74838 0.378616 10.125 0.84375 10.125H11.1386L9.27185 11.9981C8.94322 12.3014 8.90512 12.9818 9.2084 13.3104C9.51167 13.639 10.1921 13.6771 10.5207 13.3739L14.3207 10.0781C14.5026 9.91894 14.6166 9.54657 14.6244 9.177C14.6322 8.80744 14.5321 8.44759 14.2931 8.20859L10.4179 4.73438Z"/>
    				<path id="webSvg__menu-24" fill="currentColor" d="m3 5h18v2h-18zm0 6h18v2h-18zm18 6h-18v2h18z"/>
            <g id="webSvg__pdf-24">
              <path fill="none" stroke="currentColor" stroke-opacity=".2" d="m.5 5.5h23v13h-23z"/>
              <path fill="currentColor" fill-rule="nonzero" d="m7.2 13.25h-.98v1.75h-1.22v-6h2.2c1.43 0 2.14.7 2.14 2.08 0 .7-.18 1.24-.55 1.62-.35.37-.88.55-1.58.55zm8.63 1.75v-6h3.83v1.06h-2.61v1.76h2.14v1.06h-2.14v2.12zm-3.6 0h-2.03v-6h2.02c.51 0 .94.06 1.28.17.33.1.59.28.77.54.2.24.32.54.4.88a8.2 8.2 0 0 1 0 2.66 2.6 2.6 0 0 1 -.37.96c-.18.27-.44.47-.78.6a3.9 3.9 0 0 1 -1.3.19zm1.28-2.38a12.95 12.95 0 0 0 -.02-1.58c-.03-.2-.08-.38-.17-.55a.68.68 0 0 0 -.4-.33c-.17-.07-.4-.1-.7-.1h-.8v3.88h.8c.45 0 .77-.11.97-.33.17-.19.28-.52.32-1zm-7.28-.4h.98c.6 0 .9-.38.9-1.14 0-.37-.07-.63-.21-.8-.15-.16-.38-.24-.7-.24h-.97v2.17z"/>
            </g>
            <g id="webSvg__calendar" viewBox="0 0 260 265">
              <path transform="translate(19.996406 28.581623)" fill="#fff" stroke="#000" stroke-width="13" d="M0 0h223.1209v203.2879H0Z"/>
              <path transform="translate(19.791137 27.427264)" fill="#fff" stroke="#000" stroke-width="13" d="M0 0h223.3261v51.9068H0Z"/>
              <line x1="0" y1="-18.495539" x2="0" y2="18.49554" transform="translate(63.513876 27.427264)" fill="none" stroke="#000" stroke-width="18"/>
              <line x1="0" y1="-18.495539" x2="0" y2="18.49554" transform="translate(199.61512 27.427264)" fill="none" stroke="#000" stroke-width="18"/>
            </g>
            <path id="webSvg__play-46" fill="currentColor" d="m13.714 32.286v-18.572l18.572 9.286z"/>
            <path id="webSvg__plus-24" fill="none" stroke="currentColor" stroke-width="2" d="m12 7v10m5-5h-10"/>
            <path id="webSvg__search-24" fill="currentColor" d="m20.78 19.72-5.118-5.119c.991-1.225 1.588-2.781 1.588-4.476 0-3.928-3.196-7.125-7.125-7.125s-7.125 3.196-7.125 7.125 3.197 7.125 7.125 7.125c1.695 0 3.251-.597 4.476-1.588l5.119 5.118c.146.147.338.22.53.22s.384-.073.53-.22c.294-.293.294-.767 0-1.06zm-10.655-3.97c-3.102 0-5.625-2.523-5.625-5.625s2.523-5.625 5.625-5.625 5.625 2.523 5.625 5.625-2.523 5.625-5.625 5.625z"/>
            <path id="webSvg__search-32" fill="currentColor" d="m28.683 27.151-7.394-7.394c1.432-1.769 2.294-4.017 2.294-6.465 0-5.675-4.617-10.292-10.291-10.292-5.675 0-10.292 4.617-10.292 10.292 0 5.674 4.617 10.291 10.292 10.291 2.448 0 4.696-.862 6.465-2.294l7.394 7.394c.211.211.488.317.766.317.277 0 .554-.106.766-.317.423-.424.423-1.109 0-1.532zm-15.391-5.734c-4.481 0-8.125-3.645-8.125-8.125 0-4.481 3.644-8.125 8.125-8.125 4.48 0 8.125 3.644 8.125 8.125 0 4.48-3.645 8.125-8.125 8.125z"/>
            <path id="webSvg__socialFacebook-24" fill="currentColor" d="m22.25009,12.06246c0,-5.69607 -4.58852,-10.31256 -10.25009,-10.31256c-5.66156,0 -10.25009,4.61649 -10.25009,10.31256c0,5.14817 3.74769,9.4142 8.64852,10.18763l0,-7.20671l-2.60258,0l0,-2.98092l2.60258,0l0,-2.272c0,-2.5842 1.52951,-4.01223 3.87178,-4.01223c1.1211,0 2.29429,0.20141 2.29429,0.20141l0,2.53788l-1.29324,0c-1.27332,0 -1.66968,0.7956 -1.66968,1.61131l0,1.93362l2.8428,0l-0.45446,2.98092l-2.38834,0l0,7.20671c4.90083,-0.77343 8.64852,-5.03946 8.64852,-10.18763z"/>
            <path id="webSvg__socialInstagram-24" fill="currentColor" d="m8.701 4.136h5.898c2.714 0 4.915 2.201 4.915 4.915v5.898c0 2.714-2.201 4.915-4.915 4.915h-5.898c-2.714 0-4.915-2.201-4.915-4.915v-5.898c0-2.714 2.201-4.915 4.915-4.915zm5.9 14.254c1.897 0 3.441-1.543 3.441-3.44v-5.899c0-1.897-1.544-3.44-3.441-3.44h-5.898c-1.897 0-3.441 1.543-3.441 3.44v5.899c0 1.897 1.544 3.44 3.441 3.44zm-6.882-6.39c0-2.171 1.761-3.932 3.932-3.932 2.172 0 3.933 1.761 3.933 3.932 0 2.172-1.761 3.932-3.933 3.932-2.171 0-3.932-1.76-3.932-3.932zm1.476.001c0 1.354 1.103 2.457 2.457 2.457 1.355 0 2.458-1.103 2.458-2.457 0-1.356-1.103-2.458-2.458-2.458-1.354 0-2.457 1.102-2.457 2.458zm7.208-4.228c0 .29-.235.524-.524.524s-.524-.234-.524-.524c0-.289.235-.523.524-.523s.524.234.524.523z"/>
            <path id="webSvg__socialLinkedIn-24" transform="translate(-1, 0)" fill="currentColor" d="m5.782 4.155c-1.204 0-1.992.79-1.992 1.829 0 1.016.765 1.83 1.945 1.83h.024c1.227 0 1.991-.814 1.991-1.83-.023-1.039-.764-1.829-1.968-1.829zm14.428 9.619v6.071h-3.519v-5.664c0-1.423-.509-2.394-1.783-2.394-.973 0-1.551.655-1.806 1.288-.093.226-.117.541-.117.858v5.912h-3.52s.047-9.593 0-10.587h3.52v1.501c-.003.005-.007.011-.011.016-.004.006-.008.012-.012.018h.023v-.034c.468-.721 1.303-1.75 3.173-1.75 2.316 0 4.052 1.514 4.052 4.765zm-12.69 6.071h-3.519v-10.587h3.519z"/>
            <path id="webSvg__socialTikTok-24" fill="currentColor" d="m12.35132,3.0135c0.87254,-0.0135 1.74009,-0.0055 2.60668,-0.0135c0.0525,1.02055 0.4195,2.06011 1.1665,2.78165c0.7456,0.73954 1.8001,1.07806 2.8262,1.19256l0,2.68465c-0.9616,-0.03151 -1.9276,-0.23152 -2.8002,-0.64554c-0.38,-0.17201 -0.734,-0.39352 -1.0805,-0.62003c-0.0045,1.9481 0.008,3.89371 -0.0125,5.83381c-0.052,0.932 -0.35956,1.8596 -0.90159,2.6276c-0.87204,1.2786 -2.38563,2.1122 -3.94021,2.1382c-0.95355,0.0545 -1.9061,-0.2056 -2.71864,-0.6846c-1.34657,-0.794 -2.29412,-2.2476 -2.43213,-3.8077c-0.016,-0.3335 -0.0215,-0.6665 -0.008,-0.992c0.12,-1.26861 0.74754,-2.48217 1.72159,-3.30772c1.10406,-0.96155 2.65064,-1.41957 4.09872,-1.14856c0.0135,0.98756 -0.026,1.97411 -0.026,2.96166c-0.66154,-0.21401 -1.43458,-0.15401 -2.01261,0.24751c-0.42202,0.27352 -0.74254,0.69254 -0.90955,1.16661c-0.13801,0.338 -0.09851,0.7135 -0.0905,1.0725c0.1585,1.0941 1.21056,2.0136 2.33362,1.9141c0.74454,-0.008 1.45808,-0.44 1.8461,-1.0725c0.1255,-0.2216 0.26601,-0.4481 0.27351,-0.7086c0.06551,-1.1925 0.0395,-2.38011 0.0475,-3.57267c0.00551,-2.68764 -0.008,-5.36779 0.01251,-8.04693l-0.0005,-0.0005z" />
            <path id="webSvg__socialXing-24" fill="currentColor" d="m18.517 4.428c.068.099.068.214 0 .345l-4.924 8.71v.009l3.133 5.735c.069.124.072.239.01.345-.063.093-.162.14-.299.14h-2.228c-.262 0-.467-.14-.616-.42l-3.161-5.8c.112-.199 1.762-3.127 4.952-8.784.155-.28.354-.42.596-.42h2.248c.137 0 .233.047.289.14zm-9.663 2.9c.249 0 .454.14.616.42l1.529 2.667c-.062.112-.861 1.529-2.397 4.252-.167.286-.37.429-.606.429h-2.228c-.131 0-.227-.053-.29-.158-.062-.106-.062-.218 0-.336l2.36-4.178c.006 0 .006-.003 0-.009l-1.502-2.602c-.074-.137-.077-.252-.009-.345.056-.093.155-.14.298-.14z"/>
            <path id="webSvg__socialTwitter-24" transform="scale(0.8) translate(4, 5)" fill="currentColor" d="M 11.902344 8.464844 L 19.347656 0 L 17.582031 0 L 11.117188 7.347656 L 5.957031 0 L 0 0 L 7.808594 11.113281 L 0 19.988281 L 1.765625 19.988281 L 8.589844 12.226562 L 14.042969 19.988281 L 20 19.988281 Z M 9.484375 11.210938 L 8.695312 10.105469 L 2.398438 1.300781 L 5.109375 1.300781 L 10.191406 8.40625 L 10.980469 9.511719 L 17.585938 18.75 L 14.875 18.75 Z M 9.484375 11.210938 "/>
            <path id="webSvg__socialYouTube-24" fill="currentColor" d="m19.801 4.267c1.371.078 2.045.281 2.665 1.384.647 1.103.973 3.002.973 6.346v.012c0 3.328-.326 5.241-.971 6.332-.621 1.104-1.294 1.304-2.665 1.397-1.372.079-4.816.126-7.8.126-2.99 0-6.436-.047-7.806-.127-1.368-.093-2.042-.293-2.668-1.397-.639-1.091-.968-3.004-.968-6.333v-.011c0-3.343.329-5.242.968-6.345.626-1.105 1.301-1.306 2.67-1.385 1.368-.092 4.814-.13 7.804-.13 2.984 0 6.428.038 7.798.131zm-3.508 7.733-7.15-4.289v8.579z"/>
            <path id="webSvg__trash-24" fill="currentColor" d="m13.06884,1.31187c0,-0.59029 -0.47852,-1.06881 -1.06881,-1.06881c-0.59029,0 -1.06881,0.47852 -1.06881,1.06881l0,1.78135l-7.48169,0c-0.59029,0 -1.06881,0.47853 -1.06881,1.06881c0,0.59029 0.47852,1.06881 1.06881,1.06881l8.55051,0l8.55045,0c0.59032,0 1.06882,-0.47852 1.06882,-1.06881c0,-0.59028 -0.47851,-1.06881 -1.06882,-1.06881l-7.48164,0l0,-1.78135zm-9.01827,6.44474c0.20304,-0.2458 0.50522,-0.38813 0.82404,-0.38813l14.2508,0c0.31191,0 0.60819,0.13619 0.81123,0.37287c0.20304,0.23668 0.29263,0.55024 0.24525,0.85846l-2.19243,14.25079c-0.0803,0.52142 -0.52894,0.90634 -1.05648,0.90634l-9.31782,0c-0.51246,0 -0.95281,-0.36376 -1.04958,-0.86696l-2.74054,-14.25085c-0.06021,-0.31309 0.02249,-0.63671 0.22553,-0.88252zm2.11798,1.74949l2.32947,12.11319l7.51803,0l1.86359,-12.11319l-11.7111,0z"/>
            <path id="webSvg__user-18" fill="currentColor" d="M11.9439 9.49085C13.217 9.93013 14.3837 10.6558 15.364 11.636C17.0638 13.3359 18 15.596 18 18H16.5938C16.5938 13.8128 13.1872 10.4062 9 10.4062C4.81279 10.4062 1.40625 13.8128 1.40625 18H0C0 15.596 0.936176 13.3359 2.63605 11.636C3.61631 10.6558 4.78308 9.93013 6.05609 9.49085C4.69262 8.55179 3.79688 6.9802 3.79688 5.20312C3.79688 2.33413 6.131 0 9 0C11.869 0 14.2031 2.33413 14.2031 5.20312C14.2031 6.9802 13.3074 8.55179 11.9439 9.49085ZM5.20312 5.20312C5.20312 7.29675 6.90641 9 9 9C11.0936 9 12.7969 7.29675 12.7969 5.20312C12.7969 3.1095 11.0936 1.40625 9 1.40625C6.90641 1.40625 5.20312 3.1095 5.20312 5.20312Z"/>
    				<path id="webSvg__user-24" fill="currentColor" d="m15.442 12.491c1.273.439 2.439 1.165 3.42 2.145 1.699 1.7 2.636 3.96 2.636 6.364h-1.407c0-4.187-3.406-7.594-7.593-7.594-4.188 0-7.594 3.407-7.594 7.594h-1.406c0-2.404.936-4.664 2.636-6.364.98-.98 2.147-1.706 3.42-2.145-1.364-.939-2.26-2.511-2.26-4.288 0-2.869 2.335-5.203 5.204-5.203s5.203 2.334 5.203 5.203c0 1.777-.896 3.349-2.259 4.288zm-6.741-4.288c0 2.094 1.703 3.797 3.797 3.797 2.093 0 3.796-1.703 3.796-3.797 0-2.093-1.703-3.797-3.796-3.797-2.094 0-3.797 1.704-3.797 3.797z"/>
            <path id="webSvg__world-24" fill="currentColor" d="m18.364 5.636c-1.7-1.7-3.96-2.636-6.364-2.636s-4.664.936-6.364 2.636-2.636 3.96-2.636 6.364.936 4.664 2.636 6.364 3.96 2.636 6.364 2.636 4.664-.936 6.364-2.636 2.636-3.96 2.636-6.364-.936-4.664-2.636-6.364zm-8.071-1.396c-.302.294-.628.644-.954 1.051-.565.704-1.035 1.462-1.403 2.26h-2.516c1.124-1.657 2.856-2.868 4.873-3.311zm-5.476 4.365h2.701c-.307.919-.486 1.879-.533 2.868h-2.912c.067-1.02.327-1.988.744-2.868zm-.744 3.922h2.912c.047.989.226 1.949.533 2.868h-2.701c-.417-.88-.677-1.848-.744-2.868zm1.347 3.922h2.516c.368.797.838 1.556 1.403 2.26.326.407.652.757.954 1.051-2.017-.443-3.749-1.654-4.873-3.311zm6.053 2.962c-.376-.324-.843-.777-1.311-1.361-.41-.512-.761-1.046-1.054-1.601h2.365zm0-4.016h-2.84c-.343-.908-.541-1.866-.592-2.868h3.432zm0-3.922h-3.432c.051-1.002.249-1.959.592-2.868h2.84zm0-3.922h-2.365c.287-.544.629-1.067 1.028-1.569.475-.597.953-1.061 1.337-1.392zm8.454 3.922h-2.912c-.047-.989-.226-1.949-.533-2.868h2.701c.417.88.677 1.848.744 2.868zm-1.347-3.922h-2.516c-.368-.798-.838-1.556-1.402-2.26-.327-.407-.653-.757-.955-1.051 2.017.443 3.749 1.654 4.873 3.311zm-6.053-2.962c.376.324.843.777 1.312 1.361.409.512.76 1.046 1.053 1.601h-2.365zm0 4.016h2.84c.343.908.541 1.866.592 2.868h-3.432zm0 3.922h3.432c-.051 1.002-.249 1.96-.592 2.868h-2.84zm0 6.883v-2.961h2.365c-.287.544-.629 1.067-1.028 1.569-.475.597-.953 1.061-1.337 1.392zm1.18.35c.302-.294.628-.644.955-1.051.564-.704 1.034-1.462 1.402-2.26h2.516c-1.124 1.657-2.856 2.868-4.873 3.311zm5.476-4.365h-2.701c.307-.919.486-1.879.533-2.868h2.912c-.067 1.02-.327 1.988-.744 2.868z"/>
            <path id="webSvg__zoom-48" fill="currentColor" d="m36.221 10.667c.301 0 .562.11.781.329.221.22.33.48.33.782v7.777c0 .301-.11.562-.33.782s-.48.33-.781.33-.561-.11-.782-.33l-2.499-2.5-5.764 5.764c-.116.115-.249.173-.4.173-.15 0-.283-.058-.399-.173l-1.979-1.979c-.116-.116-.174-.249-.174-.4 0-.15.058-.283.174-.399l5.764-5.764-2.5-2.5c-.22-.22-.33-.48-.33-.781s.11-.562.33-.782c.22-.219.48-.329.781-.329zm-14.998 13.559c.15 0 .283.058.399.173l1.979 1.979c.116.116.174.249.174.4 0 .15-.058.283-.174.399l-5.764 5.764 2.5 2.5c.22.22.33.48.33.781s-.11.562-.33.782c-.22.219-.48.329-.781.329h-7.778c-.301 0-.561-.11-.781-.329-.22-.221-.33-.481-.33-.782v-7.778c0-.3.11-.561.33-.781s.48-.33.781-.33.561.11.782.33l2.5 2.5 5.763-5.764c.116-.116.249-.173.4-.173z"/>
          </defs>

		<use href="#webSvg__logo-46">
	</svg>


						<svg class="weHeader__barLogoName" width="89" height="27">
							<use href="#webSvg__logoTypo" />
						</svg>
					</a>
				</div>

				













































































	
	
		
	







	
	
	



	<form class="weHeaderSearch weHeaderSearch--incremental" method="get" action="https://www.we-online.com/en/system/search">
		
			<div id="hiddenInputFields">
				
			</div>
		
		<label class="weHeaderSearch__label" for="weHeaderSearchInput">
			<svg width="24" height="24">
				<use href="#webSvg__search-24" />
			</svg>
		</label>

		<input
			autocomplete="off"
			class="weHeaderSearch__input"
			id="weHeaderSearchInput"
			name="sword"
			placeholder="Enter a search term..."
			type="text"
		/>

		<button class="weHeaderSearch__submit" type="submit" aria-label="Search">
			<svg width="24" height="24">
				<use href="#webSvg__arrowRight-24" />
			</svg>
		</button>

		<button class="weHeaderSearch__close" type="button" data-menu-action="hideSearch">
			<svg width="24" height="24">
			  <use href="#webSvg__closeLg-24"></use>
			</svg>
		</button>
	</form>


				


	
		












  <nav class="weHeader__barNav">
    <ul class="weHeader__barNavList">
      
        <li class="weHeader__barNavItem">
          <a class="weHeader__barNavLink" href="https://www.we-online.com/en/products" data-menu-id="14">Products & Services</a>
        </li>
      
        <li class="weHeader__barNavItem">
          <a class="weHeader__barNavLink" href="https://www.we-online.com/en/support" data-menu-id="369">Support</a>
        </li>
      
        <li class="weHeader__barNavItem">
          <a class="weHeader__barNavLink weHeader__barNavLink--current" href="https://www.we-online.com/en/company" data-menu-id="381">Company</a>
        </li>
      
        <li class="weHeader__barNavItem">
          <a class="weHeader__barNavLink" href="https://www.we-online.com/en/career" data-menu-id="593">Career</a>
        </li>
      
        <li class="weHeader__barNavItem">
          <a class="weHeader__barNavLink" href="https://www.we-online.com/en/news-center" data-menu-id="455">News Center</a>
        </li>
      
    </ul>
  </nav>


	






				














































































	<div class="weHeader__barTools">
		














    



	<button href="false" data-menu-action="showSearch" class="weHeader__barToolsButton weUtils__dNone--md">
		
			<svg class="weHeader__barToolsIcon" width="24" height="24">
				<use href="#webSvg__search-24" />
			</svg>
		
		
	</button>



		
		
			
				

					<div class="weHeaderAuth">
						
						<template class="weHeaderLogin__template">
							<a class="weHeaderLogin weHeader__barToolsButton weHeader__barToolsButton--thin weUtils__dNone weUtils__dFlex--lg" href='https://auth01.we-online.com/as/authorization.oauth2?code_challenge=B_W20Qpmx_TVdqdmL2YIKQWrK7MkbtlaCREm6ix7xnU&client_id=we.online.prod&redirect_uri=https://www.we-online.com/en/dashboard&code_challenge_method=S256&response_type=code '>
								<svg class="weHeader__barToolsIcon" width="24" height="24">
									<use href="#webSvg__user-24" />
								</svg>
								<span class="weHeaderLogin__label weHeader__barToolsLabel">Login </span>
							</a>
						</template>

						
						<template class="weHeaderUser__template">
							<div class="weHeaderUser">
								<span class="weHeaderUser__initials" tabindex="0"></span>
								<div class="weHeaderUser__panel">
									<div class="weHeaderUser__panelHandle"></div>
									<ul class="weHeader__barFlyoutMenu">
										<li class="weHeaderUser__panelItem">
											<svg width="18" height="18">
												<use href="#webSvg__user-18"></use>
											</svg>
											<span class="weHeaderUser__panelName"></span>
										</li>
										<li class="weHeader__barFlyoutMenuItem">
											<a class="weHeader__barFlyoutMenuLink" href="https://auth01.we-online.com/idp/startSLO.ping?TargetResource=https://www.we-online.com">
												<svg width="18" height="18">
													<use href="#webSvg__logout-18"></use>
												</svg>
												Logout
											</a>
										</li>
									</ul>
								</div>
							</div>
						</template>
					</div>

					<script>
						document.addEventListener('DOMContentLoaded', () => {

							function updateOAuthUrlsForLanguage() {
								const currentPath = window.location.pathname;
								if (currentPath.includes('/de/') || currentPath.endsWith('/de')) {
									const oauthLinks = document.querySelectorAll('a[href*="auth01.we-online.com"]');
									oauthLinks.forEach(link => {
										const originalHref = link.href;
										if (originalHref.includes('redirect_uri=') && originalHref.includes('/en/')) {
											link.href = originalHref.replace(/(redirect_uri=[^&]*?)\/en\//g, '$1/de/');
										}
									});
								}
							}

							// Update OAuth URLs on page load
							updateOAuthUrlsForLanguage();

							// Function to handle auth initialization
							function initializeAuth() {
								const userData = { firstName: 'Login', lastName: 'Login' };
								const el = document.querySelector('.weHeaderAuth');
								const view = el && el.__weViews && el.__weViews.weHeaderAuth;
								console.log(userData);
								if (view && typeof view.setUserAuthorized === 'function' && userData.firstName != 'Login') {
									view.setUserAuthorized(userData);
								}

								// Update OAuth URLs again after auth component is ready
								updateOAuthUrlsForLanguage();
							}

							// Listen for the auth ready event
							document.addEventListener('weHeaderAuthReady', initializeAuth);

							setTimeout(() => {
								const el = document.querySelector('.weHeaderAuth');
								if (el && el.__weViews && el.__weViews.weHeaderAuth) {
									initializeAuth();
								}
							}, 100);
						});
					</script>

				
				







  <div class="weHeader__barToolsDivider weUtils__dNone weUtils__dFlex--xxl"></div>


			
		


		














    



	<button href="false" data-menu-action="showMenu" class="weHeader__barToolsButton weUtils__dNone--xxl">
		
			<svg class="weHeader__barToolsIcon" width="24" height="24">
				<use href="#webSvg__menu-24" />
			</svg>
		
		
			<span class="weHeader__barToolsLabel">Menu</span>
		
	</button>


		




	
		<dl class="weUtilsFlyout weHeader__barFlyout" tabindex="-1">
			<dt class="weUtilsFlyout__label weHeader__barFlyoutLabel">
				














    



	<button href="false" data-menu-action="false" class="weHeader__barToolsButton weUtils__dNone weUtils__dFlex--xxl weHeader__barToolsButton--thin">
		
			<svg class="weHeader__barToolsIcon" width="24" height="24">
				<use href="#webSvg__world-24" />
			</svg>
		
		
			<span class="weHeader__barToolsLabel">EN</span>
		
	</button>


			</dt>
			<dd class="weUtilsFlyout__panel">
				


		









	



	<ul class="weHeader__barFlyoutMenu weUtils__languageNavigation">
		
			<li class="weHeader__barFlyoutMenuItem">
				<a class="weHeader__barFlyoutMenuLink" href="https://www.we-online.com/en/company/quality/overview-certificates" hreflang="en" lang="en">English</a>
			</li>
		
			<li class="weHeader__barFlyoutMenuItem">
				<a class="weHeader__barFlyoutMenuLink" href="https://www.we-online.com/de/unternehmen/qualitaet/ueberblick-zertifikate" hreflang="de" lang="de">Deutsch</a>
			</li>
		
	</ul>


		




	
			</dd>
		</dl>
	


	</div>


			</div>
		</div>
	</header>
	
	



	
		
		
	










		
	


	






	
	<div class="weStage weStage--base" id="i4029" >
		<div class="weStage__visual">
			<div class="weStage__visualMedia weMediaCover weMediaCover--darken">
				
				
					
				
				
				
					
				
				
				
					<img class="weMediaImage weMediaImage--desktop" data-srcset="https://www.we-online.com/apps/services/image.cfm?source=jpg1/qualitycheck.jpg&crop=0x314x1500x469&resize=1500x469&dt=20230124100140973" width="1500" height="469" />
				
				
			</div>

			<div class="weStage__visualContent weGrid__container">
				<div class="weGrid__row">
					<div class="weGrid__width8--lg weGrid__width6--xl">
						<h1 CLASS="weStage__headline weTypo__h1">Certificate Overview</h1>
					</div>
				</div>
			</div>

			
				<button class="weStage__icon" aria-label="Scroll down to more details">
					<svg width="100%" height="100%" viewBox="0 0 64 64">
						<use href="#webSvg__arrowDown-64" />
					</svg>
				</button>
			
		</div>

		
	</div>





	<div class="weGrid__main" >
		
			
			
			
			
			
			
			
		<a id="i6243"></a>







	
		
		

		
		<div class="weHeaderPath">
			
			
			
			<div class="weHeaderPath__content weGrid__container" style="margin-top: 20px;">
				
				
					
					
						
					
				


				<ul class="weHeaderPath__breadcrumbs">
					
						
							
							
							
							
							
							
							
							<li class="weHeaderPath__item">
								
								
									
										<a class="weHeaderPath__itemLabel" href="../../index" target="_top">Würth Elektronik</a>
									
								
							</li>
							
								
									<li class="weHeaderPath__itemArrow">
								
							
								<svg width="20" height="20" viewBox="0 0 24 24"><use href="#webSvg__chevronLeft-24"></use></svg>
							</li>
							
						
						
					
						
							
							
							
							
							
							
							
							<li class="weHeaderPath__item">
								
								
									
										<a class="weHeaderPath__itemLabel" href="../../company" target="_top">Company</a>
									
								
							</li>
							
								
									<li class="weHeaderPath__itemArrow">
								
							
								<svg width="20" height="20" viewBox="0 0 24 24"><use href="#webSvg__chevronLeft-24"></use></svg>
							</li>
							
						
						
					
						
							
							
							
							
							
							
							
							<li class="weHeaderPath__item">
								
								
									
										<a class="weHeaderPath__itemLabel" href="../../company/quality" target="_top">Quality</a>
									
								
							</li>
							
								
									<li class="weHeaderPath__itemArrow">
								
							
								<svg width="20" height="20" viewBox="0 0 24 24"><use href="#webSvg__chevronLeft-24"></use></svg>
							</li>
							
						
						
					

					<li class="weHeaderPath__item">
						
							
								<a class="weHeaderPath__itemLabel weHeaderPath__itemLabel--current">Overview Certificates</a>
							
						
						
						
						
						
						
					</li>
				</ul>
			</div>
			
			




	
		
			<script type="application/ld+json">
				{"itemListElement":[{"item":{"@id":"https://www.we-online.com/en/company/quality/overview-certificates","name":"Back to overview"},"position":1,"@type":"ListItem","name":"Back to overview"},{"item":{"@id":"https://www.we-online.com/en/index","name":"Würth Elektronik"},"position":2,"@type":"ListItem","name":"Würth Elektronik"},{"item":{"@id":"https://www.we-online.com/en/company","name":"Company"},"position":3,"@type":"ListItem","name":"Company"},{"item":{"@id":"https://www.we-online.com/en/company/quality","name":"Quality"},"position":4,"@type":"ListItem","name":"Quality"},{"position":5,"@type":"ListItem","name":"Overview Certificates"}],"@context":"https://schema.org","@type":"BreadcrumbList"}
			</script>
		
	
	



		</div>


<a id="i4030"></a>




	
	
	
	
	
	
		
			




	

	
	

    <div class="weCollection weCollection--collapse weCollection--downloads">
        
    <a id="ldc"></a>

    <form class="weCollectionHeader" action="overview-certificates?instance_ID=4030" method="get" data-collectionSwap="weCollectionSwap-downloads1">
        
        
		<input type="hidden" name="page" value="1" />
	
        <div class="weGrid__section weGrid__sectionPanel weGrid__sectionPanel--light">
            <div class="weGrid__container">
                <div class="weTypo__hgroup weMargin__bottomMdLg weTypo__hgroup--bottomBorder">
                    <h1 class="weTypo__hgroupHeading weTypo__h2">Important Documents and Files</h1>
                </div>
                <div class="weCollectionHeader__row weCollectionHeader__row--toolbar">
                    <div class="weCollectionHeaderSearch">
                        <label class="weCollectionHeaderSearch__label" for="7C49C89E-97F8-094D-9309A2292B8E5C29">
                            <svg width="24" height="24" viewBox="0 0 24 24">
                                <use href="#webSvg__search-24" />
                            </svg>
                        </label>
                        <input autocomplete="off" class="weCollectionHeaderSearch__input" id="7C49C89E-97F8-094D-9309A2292B8E5C29" name="search" placeholder="Enter search term..." type="input" value=""></input>
                    </div>

                    <div class="weCollectionHeaderFilter">
                        <label class="weCollectionHeaderFilter__label" for="7C49C89F-09D3-27B9-79EEC7D3D77F0D95">
                            <span class="weCollectionHeaderFilter__labelCaption">Category</span>
                            <svg class="weCollectionHeaderFilter__labelHandle" width="16" height="16" viewBox="0 0 16 16">
                                <use href="#webSvg__arrowDown-16" />
                            </svg>
                        </label>
                        <select class="weCollectionHeaderFilter__input" id="7C49C89F-09D3-27B9-79EEC7D3D77F0D95" name="categories" multiple>
                            
                                    <option value="1" >Business Conditions</option>
                                
                                    <option value="3" >Certificates</option>
                                
                                    <option value="5" >Declaration of Conformity</option>
                                
                                    <option value="4" >Information Material</option>
                                
                                    <option value="2" >Material Compliance</option>
                                
                        </select>
                    </div>
                    <div class="weCollectionHeaderFilter">
                        <label class="weCollectionHeaderFilter__label" for="7C49C8A1-C4CD-2ABA-8999F90D3CEB7EBD">
                            <span class="weCollectionHeaderFilter__labelCaption">Business Unit</span>
                            <svg class="weCollectionHeaderFilter__labelHandle" width="16" height="16" viewBox="0 0 16 16">
                                <use href="#webSvg__arrowDown-16" />
                            </svg>
                        </label>
                        <select class="weCollectionHeaderFilter__input" id="7C49C8A1-C4CD-2ABA-8999F90D3CEB7EBD" name="areas" multiple>
                            
                                    <option value="5" >Career</option>
                                
                                    <option value="2" >Components</option>
                                
                                    <option value="3" >Intelligent Systems</option>
                                
                                    <option value="1" >Printed Circuit Boards</option>
                                
                        </select>
                    </div>

                    <div class="weCollectionHeaderFilter">
                        <label class="weCollectionHeaderFilter__label" for="7C49C8A0-EF30-9DE6-AD134747489F1090">
                            <span class="weCollectionHeaderFilter__labelCaption">File Type</span>
                            <svg class="weCollectionHeaderFilter__labelHandle" width="16" height="16" viewBox="0 0 16 16">
                                <use href="#webSvg__arrowDown-16" />
                            </svg>
                        </label>

                        <select class="weCollectionHeaderFilter__input" id="7C49C8A0-EF30-9DE6-AD134747489F1090" name="mimetypes" multiple>
                            
                                        <option value="pdf" >PDF</option>
                                    
                                        <option value="png" >PNG</option>
                                    
                                        <option value="xlsx" >XLSX</option>
                                    
                        </select>
                    </div>

                    <button class="weCollectionHeader__widget" type="button" data-collection-filter>
                        <svg width="24" height="24" viewBox="0 0 24 24">
                            <use href="#webSvg__filter-24" />
                        </svg>
                    </button>
                </div>

                <div class="weCollectionSummary weFormTags__root"></div>
            </div>
        </div>

        <div class="weGrid__section">

            <div class="weGrid__container">
                <div class="weCollectionOptions">
                    <dl class="weCollectionOptions__toggle" style="">
                        <dt class="weCollectionOptions__toggleFront">
                            <div class="weCollectionOptions__results">
                                <span>192 Downloads</span>
                            </div>

                            <button class="weCollectionOptions__toggleButton" type="button">
                                <span>Anzahl & Sortierung</span>
                                <svg width="16" height="16" viewBox="0 0 16 16">
                                <use href="#webSvg__arrowDown-16"/></svg>
                            </button>
                        </dt>
                        <dd class="weCollectionOptions__toggleBack">
                            <div class="weCollectionOptionsSelect">
                                <label class="weCollectionOptionsSelect__label" for="7C49C8A2-C900-1C0A-B71D944B35383F7D">
                                    <span class="weCollectionOptionsSelect__labelCaption">Downloads per page:</span>
                                </label>
								
	<select class="weCollectionOptionsSelect__input" id="7C49C8A2-C900-1C0A-B71D944B35383F7D" name="resultsPerPage" >
		
			<option value="20" >20</option>
		
			<option value="15" selected>15</option>
		
			<option value="10" >10</option>
		
	</select>

                            </div>
                            
                            <button class="weCollectionOptions__toggleButton">
                                <svg width="16" height="16" viewBox="0 0 16 16"><use href="#webSvg__close-16"></use></svg>
                            </button>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </form>

    <div class="weGrid__section">
        <div class="weGrid__container">
            <div class="weCollectionSwap" id="weCollectionSwap-downloads1">
                <div class="weCollectionSwap__page weCollectionSwap__page--selected" data-results-label="192">
                    
    <ul class="weCollectionList weCollectionList--downloads">
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">XLSX</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/xlsx1/rmi_emrt_we_eisos-v1.xlsx" target="_blank">Extended Minerals Reporting Template (EMRT) 1.3 Würth Elektronik eiSos</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">XLSX</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Components</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Material Compliance</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 1 MB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/ics-v2025.pdf" target="_blank">ICS General Terms and Conditions Wurth Elektronik ICS (Shenyang) Co. Ltd. CN</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Intelligent Systems</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Business Conditions</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 2 MB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">XLSX</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/xlsx1/we-cbt-conflict-minerals-reporting-template-6-5-tin-en.xlsx" target="_blank">Conflict Minerals Reporting Template (CMRT 6.5) PCB Surface Tin Würth Elektronik Circuit Board Technology</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">XLSX</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Printed Circuit Boards</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Material Compliance</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 2 MB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">XLSX</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/xlsx1/we-cbt-conflict-minerals-reporting-template-6-5-gold-en.xlsx" target="_blank">Conflict Minerals Reporting Template (CMRT 6.5) PCB Surface Gold Würth Elektronik Circuit Board Technology</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">XLSX</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Printed Circuit Boards</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Material Compliance</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 2 MB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">XLSX</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/xlsx1/we-cbt-extended-minerals-reporting-template-2-0-en.xlsx" target="_blank">Extended Minerals Reporting Template (EMRT 2.0) Würth Elektronik Circuit Board Technology</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">XLSX</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Printed Circuit Boards</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Material Compliance</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 2 MB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/company-policy-wuerth-elektronik-circuit-board-technology-2025-cbt-en.pdf" target="_blank">Company policy Würth Elektronik GmbH & Co. KG Circuit Board Technology</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Printed Circuit Boards</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Information Material</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 140 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/environmental-policy-wuerth-elektronik-circuit-board-technology-2025-cbt-en.pdf" target="_blank">Environmental policy Würth Elektronik GmbH & Co. KG Circuit Board Technology</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Printed Circuit Boards</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Information Material</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 142 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/agb_we-za-v1.pdf" target="_blank">General Terms and Conditions Wurth Electronics South Africa Ltd</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Components</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Business Conditions</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 140 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/information-aux-substances-conformement-au-reach-art-33-svhc-cbt-fr-v1.pdf" target="_blank">Information aux substances conformément au REACH article 33 (SVHC) Würth Elektronik GmbH & Co. KG Circuit Board Technology</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Printed Circuit Boards</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Material Compliance, Declaration of Conformity</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 155 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/ics-rohs-declaration-of-conformity-2025_04.pdf" target="_blank">Würth Elektronik ICS 2025 RoHS Declaration of Conformity</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Intelligent Systems</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Material Compliance, Declaration of Conformity</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 454 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/08_tsca_section6h_5a2_20250422_en8.pdf" target="_blank">Statement in light of the Toxic Substance Control Act (TSCA) Section 6(h) and Section 5(a) (2)</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Components</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Material Compliance</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 117 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/-07_information-on-deforestation-fre-products_eudr_en.pdf" target="_blank">Information on deforestation-free products (EUDR)</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Components</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Material Compliance, Information Material</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 53 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/we-ics-india-certificate-iso-9001-en.pdf" target="_blank">DIN EN ISO 9001:2015 Certificate Wuerth Elektronik ICS India 2025-2028</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Intelligent Systems</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Certificates</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 147 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/we-ics-india-certificate-iso-14001-en.pdf" target="_blank">DIN EN ISO 14001:2015 Certificate Wuerth Elektronik ICS India 2025-2028</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Intelligent Systems</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Certificates</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 182 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
            <li class="weCollectionList__item weCollectionList__item--link">
                <div class="weCollectionList__itemFile">
                    <span class="weCollectionList__itemFileIcon">PDF</span>
                </div>
                <div class="weCollectionList__itemContent">
                    <div class="weCollectionList__itemTitle">
                        
                        <h3>
                            
                                <a class="weCollectionList__itemLink" href="/files/pdf1/we-eisos_unternehmenspolitik_a4_en_web.pdf" target="_blank">Würth Elektronik eiSos GmbH & Co. KG company policy</a>
                            
                        </h3>
                    </div>
                    <dl class="weCollectionList__itemMeta">
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone">File Type</dt>
                        <dd class="weCollectionList__itemMetaValue weUtils__dInlineBlock--sm weUtils__dNone--md">PDF</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Business Unit</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Components</dd>
                        
                            <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Category</dt>
                            <dd class="weCollectionList__itemMetaValue weUtils__dNone weUtils__dInlineBlock--md">Information Material</dd>
                        
                        <dt class="weCollectionList__itemMetaLabel weUtils__dNone weUtils__dInlineBlock--md">Size</dt>
                        
                        <dd class="weCollectionList__itemMetaValue"> 37 KB </dd>
                    </dl>
                </div>
                <svg class="weCollectionList__itemIcon weCollectionList__itemIcon--download" width="48" height="48" viewBox="0 0 48 48"><use href="#webSvg__download-48"></use></svg>
            </li>
        
    </ul>
    <div class="weCollectionFooter">
        <form>
            <div class="weCollectionOptionsSelect">
                <label class="weCollectionOptionsSelect__label" for="7C49C8A3-A3CF-26FC-D534A24FA302B9D2">
                    <span class="weCollectionOptionsSelect__labelCaption">Downloads per page:</span>
                </label>
                
				
	<select class="weCollectionOptionsSelect__input" id="7C49C8A3-A3CF-26FC-D534A24FA302B9D2" name="resultsPerPage_bottom" data-sync-to="resultsPerPage">
		
			<option value="20" >20</option>
		
			<option value="15" selected>15</option>
		
			<option value="10" >10</option>
		
	</select>

            </div>
        </form>
        
            <ul class="weCollectionPagination">
                <li class="weCollectionPagination__page weCollectionPagination__page--disabled">
                    <a class="weCollectionPagination__pageLink weCollectionPagination__pageLink--disabled" data-page="1" title="Previous page">
                        <svg width="16" height="16" viewBox="0 0 16 16"><use href="#webSvg__chevronLeft-16"/></svg>
                    </a>
                </li>
                
                <li class="weCollectionPagination__page">
                    <a class="weCollectionPagination__pageLink weCollectionPagination__pageLink--active" data-page="1" title="Page 1" active>
                        1
                    </a>
                </li>
                
                
                        <li class="weCollectionPagination__page">
                            <a class="weCollectionPagination__pageLink" data-page="2" title="Page 2" >
                                2
                            </a>
                        </li>
                    
                        <li class="weCollectionPagination__page">
                            <a class="weCollectionPagination__pageLink" data-page="3" title="Page 3" >
                                3
                            </a>
                        </li>
                    
                        <li class="weCollectionPagination__page">
                            <a class="weCollectionPagination__pageLink" data-page="4" title="Page 4" >
                                4
                            </a>
                        </li>
                    
                        <li class="weCollectionPagination__page">
                            <a class="weCollectionPagination__pageLink" data-page="5" title="Page 5" >
                                5
                            </a>
                        </li>
                    
                    <li class="weCollectionPagination__ellipsis"><span>&hellip;</span></li>
                
                
                <li class="weCollectionPagination__page">
                    <a class="weCollectionPagination__pageLink " data-page="13" title="Page 13" >
                        13
                    </a>
                </li>
                <li class="weCollectionPagination__page ">
                    <a class="weCollectionPagination__pageLink " data-page="2" title="Next page">
                        <svg width="16" height="16" viewBox="0 0 16 16"><use href="#webSvg__chevronRight-16"/></svg>
                    </a>
                </li>
            </ul>

            
            
        
    </div>

                </div>
            </div>
        </div>
    </div>

    </div>

	
	<script>
		document.addEventListener("DOMContentLoaded", function(event) {
			try {
				var lang = 1;
				var currentUrl = window.location.href;
				// Throttle function to limit the frequency of updateLinks
				function throttle(func, limit) {
					let inThrottle;
					return function() {
						const args = arguments;
						const context = this;
						if (!inThrottle) {
							func.apply(context, args);
							inThrottle = true;
							setTimeout(() => inThrottle = false, limit);
						}
					};
				}

				function updateLinks() {
					var links1, links2;

					if (lang === 1) {
						links1 = document.querySelectorAll('a.weHeader__barFlyoutMenuLink[hreflang="en"]');
						links2 = document.querySelectorAll('a.weHeader__barFlyoutMenuLink[hreflang="de"]');
					} else if (lang === 2) {
						links1 = document.querySelectorAll('a.weHeader__barFlyoutMenuLink[hreflang="de"]');
						links2 = document.querySelectorAll('a.weHeader__barFlyoutMenuLink[hreflang="en"]');
					}

					var currentParams = new URLSearchParams(window.location.search);
					function updateLinkHref(link) {
						var url = new URL(link.href);
						// Keep the current filter parameters in the URL
						currentParams.forEach(function(value, key) {
							url.searchParams.set(key, value);
						});

						link.href = url.href;
					}

					if (links1.length > 0) {
						links1.forEach(function(link) {
							updateLinkHref(link);
						});
					}
					if (links2.length > 0) {
						links2.forEach(function(link) {
							updateLinkHref(link);
						});
					}
				}

				var throttledUpdateLinks = throttle(updateLinks, 100);
				throttledUpdateLinks();

				var observer = new MutationObserver(function(mutationsList) {
					mutationsList.forEach(function(mutation) {
						if (mutation.addedNodes.length > 0) {
							throttledUpdateLinks();
						}
					});
				});

				observer.observe(document.body, { childList: true, subtree: true });
			} catch (error) {
				console.error('Error updating links:', error);
			}
		});
	</script>





		
	
	
	


		
	</div>

	






	















	

	<footer class="weFooter">
		<div class="weFooter__brand">
			
				<a class="weFooter__brandLogo" href="https://www.we-online.com/en" aria-label="Würth Elektronik Logo">
					<svg class="weFooter__brandLogoSvg" width="100%" height="100%" viewBox="0 0 46 46">
						<use href="#webSvg__logo-46" />
					</svg>
				</a>
			
		</div>

		<div class="weGrid__container">
			<div class="weFooter__grid" >
				
					
				



	





	
		<dl class="weFooterAccordion">
			<dt class="weFooterAccordion__header">
				
					<a id="i860"></a>
				
				<span>Products & More</span>
				<svg class="weFooterAccordion__headerIcon" width="24" height="24">
					<use href="#webSvg__chevronDown-24"/>
				</svg>
			</dt>
			<dd class="weFooterAccordion__body">
				


		<ul class="weFooter__menu">
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/products/components">Components</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/products/printed-circuit-boards/overview">PCBs</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/products/intelligent-systems">Intelligent Systems</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/news-center/newsletter">Newsletter</a>
				</li>
			
		</ul>
	





	
			</dd>
		</dl>
	







	





	
		<dl class="weFooterAccordion">
			<dt class="weFooterAccordion__header">
				
					<a id="i861"></a>
				
				<span>Interesting</span>
				<svg class="weFooterAccordion__headerIcon" width="24" height="24">
					<use href="#webSvg__chevronDown-24"/>
				</svg>
			</dt>
			<dd class="weFooterAccordion__body">
				


		<ul class="weFooter__menu">
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/company/sustainability">Sustainability</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/company">Company</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/news-center">News Center</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/news-center/events">Events</a>
				</li>
			
		</ul>
	





	
			</dd>
		</dl>
	







	





	
		<dl class="weFooterAccordion">
			<dt class="weFooterAccordion__header">
				
					<a id="i862"></a>
				
				<span>Important</span>
				<svg class="weFooterAccordion__headerIcon" width="24" height="24">
					<use href="#webSvg__chevronDown-24"/>
				</svg>
			</dt>
			<dd class="weFooterAccordion__body">
				


		<ul class="weFooter__menu">
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/support">Support</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/company/quality">Quality</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/company/legalinformation/terms-and-conditions">Terms and Conditions</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/company/legalinformation/compliance">Compliance</a>
				</li>
			
		</ul>
	





	
			</dd>
		</dl>
	







	





	
		<dl class="weFooterAccordion">
			<dt class="weFooterAccordion__header">
				
					<a id="i6545"></a>
				
				<span>Career</span>
				<svg class="weFooterAccordion__headerIcon" width="24" height="24">
					<use href="#webSvg__chevronDown-24"/>
				</svg>
			</dt>
			<dd class="weFooterAccordion__body">
				


		<ul class="weFooter__menu">
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/career">Career Area</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://jobs.we-online.com/eng">Open Positions</a>
				</li>
			
				<li class="weFooter__menuItem">
					<a class="weFooter__menuLink" href="https://www.we-online.com/en/career/we-employer">Würth Elektronik as an employer</a>
				</li>
			
		</ul>
	





	
			</dd>
		</dl>
	




				




	<ul class="weFooter__social">
		
			<li class="weFooter__socialItem">
				<a class="weFooter__socialLink" href="https://www.we-online.com/facebook-we-group" target="_blank" aria-label="Würth Elektronik Facebook">
					<svg width="24" height="24"><use href="#webSvg__socialFacebook-24"/></svg>
				</a>
			</li>
		
			<li class="weFooter__socialItem">
				<a class="weFooter__socialLink" href="https://www.we-online.com/twitter" target="_blank" aria-label="Würth Elektronik Twitter">
					<svg width="24" height="24"><use href="#webSvg__socialTwitter-24"/></svg>
				</a>
			</li>
		
			<li class="weFooter__socialItem">
				<a class="weFooter__socialLink" href="https://www.we-online.com/linkedin" target="_blank" aria-label="Würth Elektronik LinkedIn">
					<svg width="24" height="24"><use href="#webSvg__socialLinkedIn-24"/></svg>
				</a>
			</li>
		
			<li class="weFooter__socialItem">
				<a class="weFooter__socialLink" href="https://www.we-online.com/youtube" target="_blank" aria-label="Würth Elektronik YouTube">
					<svg width="24" height="24"><use href="#webSvg__socialYouTube-24"/></svg>
				</a>
			</li>
		
			<li class="weFooter__socialItem">
				<a class="weFooter__socialLink" href="https://www.we-online.com/instagram-we-group" target="_blank" aria-label="Würth Elektronik Instagram">
					<svg width="24" height="24"><use href="#webSvg__socialInstagram-24"/></svg>
				</a>
			</li>
		
			<li class="weFooter__socialItem">
				<a class="weFooter__socialLink" href="https://www.we-online.com/tiktok" target="_blank" aria-label="Würth Elektronik TikTok">
					<svg width="24" height="24"><use href="#webSvg__socialTikTok-24"/></svg>
				</a>
			</li>
		
	</ul>


			</div>
			<div class="weFooter__meta">
				<div class="weFooter__copyright">© 2025 Würth Elektronik eiSos GmbH &amp; Co. KG, Deutschland</div>
				









	<ul class="weFooter__legals">
		
			<li class="weFooter__legalsItem">
				<a class="weFooter__legalsLink" href="https://www.we-online.com/en/service/contact">Contact</a>
			</li>
    	
			<li class="weFooter__legalsItem">
				<a class="weFooter__legalsLink" href="https://www.we-online.com/en/service/imprint">Imprint</a>
			</li>
    	
			<li class="weFooter__legalsItem">
				<a class="weFooter__legalsLink" href="https://www.we-online.com/en/service/data-privacy">Data Privacy</a>
			</li>
    	
		<li class="weFooter__legalsItem">
			<a class="weFooter__legalsLink" href="#" onclick="CCM.openWidget();return false;">Cookies</a>
		</li>
	</ul>


				












	
		













	
		
			<dl class="weUtilsFlyout weFooter__dropDown" tabindex="-1">
				<dt class="weUtilsFlyout__label weUtilsFlyout__label--arrow">
					ENGLISH
				</dt>
				<dd class="weUtilsFlyout__panel weFooter__dropDownPanel">
					
						<a class="weFooter__dropDownLink" href="https://www.we-online.com/en/company/quality/overview-certificates" hreflang="en" lang="en">English</a>
					
						<a class="weFooter__dropDownLink" href="https://www.we-online.com/de/unternehmen/qualitaet/ueberblick-zertifikate" hreflang="de" lang="de">Deutsch</a>
					
				</dd>
			</dl>
		
	








	


			</div>

		</div>
	</footer>







		
			
				<script>
	(function() {
		// Silent OAuth authentication system
		const OAUTH_PROVIDERS = ['auth01.we-online.com', 'code_challenge'];
		const CHECK_AUTH_TIMEOUT = 3000;
		let silentAuthInProgress = false;

		// Configuration: Enable/disable redirect_uri language replacement
		const ENABLE_LANGUAGE_REDIRECT = false; // Set to false to disable replacement

		// Configuration for dynamic link detection
		const RETRY_DELAYS = [0, 1000, 2000, 3000, 5000]; // Check immediately, then after 1s, 2s, 3s, 5s
		const MAX_RETRIES = RETRY_DELAYS.length;
		let currentRetry = 0;
		let authCheckCompleted = false;

		function isOAuthLink(href) {
			return href && OAUTH_PROVIDERS.some(provider => href.includes(provider));
		}

		function checkSilentAuth(authUrl, callback) {
			if (silentAuthInProgress) {
				callback(false);
				return;
			}

			silentAuthInProgress = true;

			const iframe = document.createElement('iframe');
			iframe.style.display = 'none';
			iframe.src = authUrl + '&prompt=none';

			let timeoutId;
			let resolved = false;

			function cleanup() {
				if (iframe.parentNode) iframe.parentNode.removeChild(iframe);
				if (timeoutId) clearTimeout(timeoutId);
				silentAuthInProgress = false;
			}

			iframe.onload = function() {
				try {
					const iframeUrl = iframe.contentWindow.location.href;
					if (iframeUrl.includes('code=') && !resolved) {
						resolved = true;
						cleanup();
						callback(true, authUrl);
					}
				} catch (e) {
					// Cross-origin error expected
				}
			};

			iframe.onerror = function() {
				if (!resolved) {
					resolved = true;
					cleanup();
					callback(false);
				}
			};

			timeoutId = setTimeout(function() {
				if (!resolved) {
					resolved = true;
					cleanup();
					callback(false);
				}
			}, CHECK_AUTH_TIMEOUT);

			document.body.appendChild(iframe);
		}

		function autoCheckAuthenticationForLogins(loginLinks) {
			const loginLink = loginLinks[0];
			let authUrl = loginLink.href;

			// Replace /en/ with /de/ in redirect_uri parameter if enabled and on a German page
			const currentPath = window.location.pathname;
			if (ENABLE_LANGUAGE_REDIRECT && (currentPath.includes('/de/') || currentPath.endsWith('/de')) && authUrl.includes('redirect_uri=') && authUrl.includes('/en/')) {
				authUrl = authUrl.replace(
					/(redirect_uri=[^&]*?)\/en\//g,
					'$1/de/'
				);
			}

			checkSilentAuth(authUrl, function(success, redirectUrl) {
				if (success) {
					window.location.reload();
				}
			});
		}


		function initializeOAuthHandlers() {
			if (authCheckCompleted) {
				return;
			}

			const allLinks = Array.from(document.querySelectorAll('a[href]'));
			const oauthLinks = allLinks.filter(link => isOAuthLink(link.href));
			const loginLinks = oauthLinks
				.filter(link => link.href.includes('authorization.oauth2') && !link.href.includes('startSLO.ping'))
				.map(link => ({ href: link.href, element: link }));

			if (loginLinks.length > 0) {
				authCheckCompleted = true;
				autoCheckAuthenticationForLogins(loginLinks);
			} else {
				// Try again after a delay if we haven't exhausted all retries
				if (currentRetry < MAX_RETRIES - 1) {
					currentRetry++;
					const nextDelay = RETRY_DELAYS[currentRetry];
					setTimeout(initializeOAuthHandlers, nextDelay);
				}
			}
		}

		function scheduleInitialization() {
			// Reset retry counter
			currentRetry = 0;
			authCheckCompleted = false;

			// Start with the first attempt (immediate)
			const firstDelay = RETRY_DELAYS[0];
			if (firstDelay === 0) {
				initializeOAuthHandlers();
			} else {
				setTimeout(initializeOAuthHandlers, firstDelay);
			}
		}

		// Initialize when DOM is ready
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', scheduleInitialization);
		} else {
			scheduleInitialization();
		}
	})();
</script>

			
		
		<script type="text/javascript" async src="https://www.we-online.com/assets/main.js" id="weRootScript"></script>
		<script type="text/javascript" async src="https://www.we-online.com/assets/collections.js" id="weRootScript"></script>
		<script type="text/javascript" async src="https://www.we-online.com/assets/accordion.js" id="weRootScript"></script>
		
	</body>
	</html>


