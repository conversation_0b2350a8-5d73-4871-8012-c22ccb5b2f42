#!/usr/bin/env python3
"""
TEST SCREEN 1 ONLY
==================
Just test finding and entering the part number in UltraLibrarian search box.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_chrome():
    """Setup Chrome"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    return webdriver.Chrome(options=chrome_options)

def test_screen1():
    """Test Screen 1: Find search box and enter part number"""
    print("🧪 TESTING SCREEN 1: Search Box")
    print("=" * 40)
    
    driver = setup_chrome()
    
    try:
        # Go to UltraLibrarian
        print("📍 Opening https://app.ultralibrarian.com...")
        driver.get('https://app.ultralibrarian.com')
        
        # Wait for page to load
        print("⏳ Waiting for page to load...")
        time.sleep(10)  # Give it plenty of time
        
        print(f"✅ Page loaded. Title: {driver.title}")
        print(f"✅ Current URL: {driver.current_url}")
        
        # Take a screenshot for debugging
        driver.save_screenshot('screen1_debug.png')
        print("📸 Screenshot saved as screen1_debug.png")
        
        # Find ALL input elements
        print("\n🔍 Finding ALL input elements on page:")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(inputs)} input elements:")
        
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute('type') or 'none'
                placeholder = inp.get_attribute('placeholder') or 'none'
                name = inp.get_attribute('name') or 'none'
                id_attr = inp.get_attribute('id') or 'none'
                class_attr = inp.get_attribute('class') or 'none'
                visible = inp.is_displayed()
                enabled = inp.is_enabled()
                
                print(f"  Input {i}:")
                print(f"    type='{input_type}'")
                print(f"    placeholder='{placeholder}'")
                print(f"    name='{name}'")
                print(f"    id='{id_attr}'")
                print(f"    class='{class_attr}'")
                print(f"    visible={visible}, enabled={enabled}")
                print()
                
            except Exception as e:
                print(f"  Input {i}: Error getting attributes - {e}")
        
        # Try to find the search box
        print("🎯 Trying to find search box...")
        
        search_candidates = []
        for i, inp in enumerate(inputs):
            try:
                if inp.is_displayed() and inp.is_enabled():
                    input_type = inp.get_attribute('type') or ''
                    placeholder = inp.get_attribute('placeholder') or ''
                    
                    # Look for search-like inputs
                    if (input_type.lower() in ['search', 'text'] or 
                        'search' in placeholder.lower()):
                        search_candidates.append((i, inp))
                        print(f"  ✅ Search candidate {i}: type='{input_type}', placeholder='{placeholder}'")
            except:
                continue
        
        if not search_candidates:
            print("❌ No search box candidates found!")
            print("Trying first visible text input...")
            for i, inp in enumerate(inputs):
                try:
                    if inp.is_displayed() and inp.is_enabled():
                        input_type = inp.get_attribute('type') or ''
                        if input_type.lower() in ['text', '']:
                            search_candidates.append((i, inp))
                            print(f"  📝 Using text input {i} as search box")
                            break
                except:
                    continue
        
        if not search_candidates:
            print("❌ FAILED: No usable input found!")
            return False
        
        # Try the first candidate
        candidate_index, search_box = search_candidates[0]
        print(f"\n⌨️ Trying to enter 'LM358N' in input {candidate_index}...")
        
        try:
            search_box.clear()
            search_box.send_keys("LM358N")
            print("✅ Successfully entered 'LM358N'")
            
            # Try to submit
            print("📤 Trying to submit search...")
            search_box.send_keys(Keys.RETURN)
            
            # Wait for results
            print("⏳ Waiting 5 seconds for search results...")
            time.sleep(5)
            
            # Check if URL changed or page updated
            new_url = driver.current_url
            print(f"🔗 New URL: {new_url}")
            
            if new_url != 'https://app.ultralibrarian.com':
                print("✅ SUCCESS: URL changed, search likely worked!")
                return True
            else:
                print("⚠️ URL didn't change, but search may have worked")
                return True
                
        except Exception as e:
            print(f"❌ Error entering search term: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    success = test_screen1()
    if success:
        print("\n🎉 SCREEN 1 TEST PASSED!")
    else:
        print("\n❌ SCREEN 1 TEST FAILED!")
