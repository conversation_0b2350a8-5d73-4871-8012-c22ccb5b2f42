#!/usr/bin/env python3
"""
Get <PERSON>gi<PERSON> homepage to see how search works
"""

import requests

def get_digikey_homepage():
    print("🔍 GETTING DIGIKEY HOMEPAGE")
    print("=" * 30)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        response = session.get('https://www.digikey.com/', timeout=30)
        print(f"Status: {response.status_code}")
        
        # Save the homepage
        with open('digikey_homepage.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("📄 Saved homepage")
        
        print(f"Response length: {len(response.text)} characters")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    get_digikey_homepage()
