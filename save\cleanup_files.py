#!/usr/bin/env python3
"""
Clean up and rename files according to proper naming conventions
"""

import os
import shutil

def cleanup_and_rename():
    print("🧹 CLEANING UP AND RENAMING FILES")
    print("=" * 40)
    
    # 1. Remove wrong package STEP files
    wrong_files = [
        '3d/SOT25_Diodes.step',
        '3d/SOT323_Diodes.step'
    ]
    
    print("1. Removing wrong package files...")
    for file in wrong_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"   ❌ Removed: {file}")
        else:
            print(f"   ⚠️  Not found: {file}")
    
    # 2. Rename datasheet to proper format
    print("\n2. Renaming datasheet...")
    old_datasheet = 'files-download/APX803L20-30SA-7_datasheet.pdf'
    new_datasheet = 'files-download/Diodes Inc-APX803L20-30SA-7.pdf'
    
    if os.path.exists(old_datasheet):
        shutil.move(old_datasheet, new_datasheet)
        print(f"   ✅ Renamed datasheet:")
        print(f"      From: {old_datasheet}")
        print(f"      To:   {new_datasheet}")
    else:
        print(f"   ❌ Datasheet not found: {old_datasheet}")
    
    # 3. Rename STEP file to proper format
    print("\n3. Renaming STEP file...")
    old_step = '3d/APX803L20-30SA-7_SOT23_Diodes.step'
    new_step = '3d/Diodes-APX803L20-30SA-7.step'
    
    if os.path.exists(old_step):
        shutil.move(old_step, new_step)
        print(f"   ✅ Renamed STEP file:")
        print(f"      From: {old_step}")
        print(f"      To:   {new_step}")
    else:
        print(f"   ❌ STEP file not found: {old_step}")
    
    # 4. Verify final results
    print("\n4. Final verification...")
    
    final_files = [
        ('files-download/Diodes Inc-APX803L20-30SA-7.pdf', 'Datasheet'),
        ('3d/Diodes-APX803L20-30SA-7.step', 'STEP file')
    ]
    
    all_good = True
    for filepath, filetype in final_files:
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"   ✅ {filetype}: {os.path.basename(filepath)} ({size:,} bytes)")
        else:
            print(f"   ❌ Missing {filetype}: {filepath}")
            all_good = False
    
    return all_good

def main():
    print("🚀 FILE CLEANUP AND RENAMING")
    print("Fixing naming conventions:")
    print("- Datasheet: Manufacturer Name-Full Part Number")
    print("- STEP file: Website Name-Part Number")
    print("=" * 50)
    
    success = cleanup_and_rename()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ SUCCESS: Files cleaned up and renamed correctly!")
        print("\n📁 FINAL RESULTS:")
        print("   📄 Datasheet: Diodes Inc-APX803L20-30SA-7.pdf")
        print("   🎯 3D Model: Diodes-APX803L20-30SA-7.step (SOT23 package)")
        print("\n🎉 PERFECT: Only the correct files for the specific part!")
    else:
        print("❌ FAILED: Some files could not be processed")

if __name__ == "__main__":
    main()
