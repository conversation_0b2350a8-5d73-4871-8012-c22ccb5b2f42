#!/usr/bin/env python3
"""
Extract TI detail URL from HTML and access it to find the datasheet
"""

import json
import requests
import os

def get_ti_detail_page():
    print("🎯 GETTING TI DETAIL PAGE FROM HTML")
    print("=" * 50)
    
    # Read the fresh search results
    with open('digikey_fresh_search_LM358N.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Find the JSON data
    start = html_content.find('{"props":')
    end = html_content.find('</script>', start)
    json_str = html_content[start:end]
    data = json.loads(json_str)
    
    # Get exact matches
    exact_matches = data.get('props', {}).get('pageProps', {}).get('envelope', {}).get('data', {}).get('exactMatch', [])
    
    # Find Texas Instruments
    ti_match = None
    for match in exact_matches:
        if 'texas instruments' in match.get('mfr', '').lower():
            ti_match = match
            break
    
    if not ti_match:
        print("❌ Texas Instruments not found")
        return False
    
    print(f"✅ Found Texas Instruments match:")
    print(f"   Manufacturer: {ti_match['mfr']}")
    print(f"   Part: {ti_match['mfrProduct']}")
    print(f"   Detail URL: {ti_match['detailUrl']}")
    
    # Access the TI detail page
    detail_url = f"https://www.digikey.com{ti_match['detailUrl']}"
    
    print(f"\n📄 Accessing TI detail page...")
    print(f"   URL: {detail_url}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        response = session.get(detail_url, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the detail page
            with open('ti_detail_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"   ✅ Saved TI detail page to ti_detail_page.html")
            
            # Look for the TI datasheet URL pattern you provided
            if 'suppproductinfo' in response.text:
                print(f"   🎯 Found 'suppproductinfo' in page!")
                
                # Search for the specific pattern
                import re
                pattern = r'https://www\.ti\.com/general/docs/suppproductinfo\.tsp[^"\']*'
                matches = re.findall(pattern, response.text)
                
                if matches:
                    datasheet_url = matches[0]
                    print(f"   📄 Found TI datasheet URL: {datasheet_url}")
                    
                    # Now download the datasheet
                    return download_ti_datasheet(session, datasheet_url)
                else:
                    print(f"   ❌ Could not extract datasheet URL")
                    return False
            else:
                print(f"   ❌ 'suppproductinfo' not found in page")
                return False
        else:
            print(f"   ❌ Failed to access detail page: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def download_ti_datasheet(session, datasheet_url):
    """Download the TI datasheet"""
    print(f"\n📥 Downloading TI datasheet...")
    print(f"   URL: {datasheet_url}")
    
    try:
        response = session.get(datasheet_url, timeout=60, allow_redirects=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            print(f"   Content-Type: {content_type}")
            
            # Check if it's a PDF
            if 'application/pdf' in content_type:
                print("   ✅ Direct PDF download!")
                
                os.makedirs('datasheets', exist_ok=True)
                filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                
                with open(filename, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                file_size = os.path.getsize(filename)
                print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                
                if file_size > 10000:
                    print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                    return True
                else:
                    print(f"   ⚠️  File too small")
                    return False
                    
            else:
                print(f"   📄 Not a direct PDF, content type: {content_type}")
                
                # Save the response to see what we got
                with open('ti_datasheet_response.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                print(f"   💾 Saved response to ti_datasheet_response.html")
                
                # Look for PDF links in the response
                import re
                pdf_matches = re.findall(r'href="([^"]*\.pdf[^"]*)"', response.text)
                
                if pdf_matches:
                    print(f"   🎯 Found PDF links:")
                    for i, pdf_url in enumerate(pdf_matches[:3], 1):
                        print(f"      {i}. {pdf_url}")
                    
                    # Try the first PDF link
                    pdf_url = pdf_matches[0]
                    if not pdf_url.startswith('http'):
                        pdf_url = f"https://www.ti.com{pdf_url}"
                    
                    print(f"\n   📥 Downloading PDF from: {pdf_url}")
                    
                    pdf_response = session.get(pdf_url, timeout=60, stream=True)
                    print(f"   PDF Status: {pdf_response.status_code}")
                    
                    if pdf_response.status_code == 200:
                        os.makedirs('datasheets', exist_ok=True)
                        filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                        
                        with open(filename, 'wb') as f:
                            for chunk in pdf_response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        
                        file_size = os.path.getsize(filename)
                        print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                        
                        if file_size > 10000:
                            print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                            return True
                        else:
                            print(f"   ⚠️  File too small")
                            return False
                    else:
                        print(f"   ❌ PDF download failed: {pdf_response.status_code}")
                        return False
                else:
                    print(f"   ❌ No PDF links found in response")
                    return False
        else:
            print(f"   ❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = get_ti_detail_page()
    if success:
        print("\n🎉 TEXAS INSTRUMENTS DATASHEET SUCCESSFULLY DOWNLOADED!")
    else:
        print("\n❌ FAILED TO DOWNLOAD TI DATASHEET")
