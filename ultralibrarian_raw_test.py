#!/usr/bin/env python3
"""
Raw test - exactly replicate what browser sends
"""

import requests
from urllib.parse import urlencode

def test_raw_login():
    print("🔍 RAW ULTRALIBRARIAN LOGIN TEST")
    print("=" * 50)
    
    session = requests.Session()
    
    # Exact browser headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-<PERSON>tch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
    }
    
    session.headers.update(headers)
    
    # Step 1: Get login page
    print("1. Getting login page...")
    login_url = 'https://www.ultralibrarian.com/wp-login.php'
    
    response = session.get(login_url)
    print(f"   Status: {response.status_code}")
    
    if response.status_code != 200:
        print("   ❌ Failed to get login page")
        return False
    
    # Step 2: Submit with EXACT form data that browser would send
    print("2. Submitting with exact browser form data...")
    
    # This is EXACTLY what a browser would send
    form_data = {
        'log': '<EMAIL>',
        'pwd': 'Lennyai123#',
        'wp-submit': 'Log In',
        'redirect_to': 'https://www.ultralibrarian.com/wp-admin/',
        'testcookie': '1'
    }
    
    # Update headers for POST
    session.headers.update({
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://www.ultralibrarian.com',
        'Referer': 'https://www.ultralibrarian.com/wp-login.php',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
    })
    
    print(f"   Form data: {list(form_data.keys())}")
    print(f"   URL: {login_url}")
    
    # Submit
    login_response = session.post(login_url, data=form_data, allow_redirects=True)
    
    print(f"   Status: {login_response.status_code}")
    print(f"   Final URL: {login_response.url}")
    print(f"   Response length: {len(login_response.text)}")
    
    # Save response
    with open('ultralibrarian_raw_test_response.html', 'w', encoding='utf-8') as f:
        f.write(login_response.text)
    
    # Check for error
    if 'login_error' in login_response.text:
        print("   ❌ Login error found")
        # Find the error message
        start = login_response.text.find('<div id="login_error">')
        if start != -1:
            end = login_response.text.find('</div>', start)
            if end != -1:
                error_section = login_response.text[start:end+6]
                print(f"   Error section: {error_section}")
        return False
    
    # Check for success
    if 'wp-admin' in login_response.url:
        print("   ✅ SUCCESS: Redirected to wp-admin!")
        return True
    elif 'logout' in login_response.text.lower():
        print("   ✅ SUCCESS: Found logout link!")
        return True
    else:
        print("   ❌ No success indicators found")
        return False

def test_different_credentials():
    """Test if the issue is with the credentials themselves"""
    print("\n🔍 TESTING DIFFERENT CREDENTIAL FORMATS")
    print("=" * 50)
    
    # Test with different encodings/formats
    test_cases = [
        ('<EMAIL>', 'Lennyai123#'),
        ('<EMAIL>', 'Lennyai123%23'),  # URL encoded #
        ('lennyalexman%40gmail.com', 'Lennyai123#'),  # URL encoded @
        ('lennyalexman%40gmail.com', 'Lennyai123%23'), # Both encoded
    ]
    
    for i, (username, password) in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {username} / {password}")
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Get login page
        response = session.get('https://www.ultralibrarian.com/wp-login.php')
        if response.status_code != 200:
            print("   ❌ Failed to get login page")
            continue
        
        # Submit
        form_data = {
            'log': username,
            'pwd': password,
            'wp-submit': 'Log In',
            'redirect_to': 'https://www.ultralibrarian.com/wp-admin/',
            'testcookie': '1'
        }
        
        session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://www.ultralibrarian.com',
            'Referer': 'https://www.ultralibrarian.com/wp-login.php',
        })
        
        login_response = session.post(
            'https://www.ultralibrarian.com/wp-login.php', 
            data=form_data, 
            allow_redirects=True
        )
        
        if 'login_error' in login_response.text:
            print("   ❌ Login error")
        elif 'wp-admin' in login_response.url:
            print("   ✅ SUCCESS!")
            return True
        elif 'logout' in login_response.text.lower():
            print("   ✅ SUCCESS!")
            return True
        else:
            print("   ❓ Unclear result")
    
    return False

def main():
    print("🚀 ULTRALIBRARIAN RAW LOGIN TEST")
    print("=" * 50)
    
    # Test 1: Exact browser replication
    success1 = test_raw_login()
    
    # Test 2: Different credential formats
    success2 = test_different_credentials()
    
    print("\n" + "=" * 50)
    print("📋 RESULTS:")
    print(f"   Raw test: {'✅ SUCCESS' if success1 else '❌ FAILED'}")
    print(f"   Credential test: {'✅ SUCCESS' if success2 else '❌ FAILED'}")
    
    if not success1 and not success2:
        print("\n❌ ALL TESTS FAILED")
        print("   The credentials might be incorrect, or there's additional security")
    else:
        print("\n🎉 AT LEAST ONE TEST SUCCEEDED!")

if __name__ == "__main__":
    main()
