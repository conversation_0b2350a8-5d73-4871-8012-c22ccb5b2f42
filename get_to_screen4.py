#!/usr/bin/env python3
"""
GET TO SCREEN 4
===============
Go through all screens to reach Screen 4 (3D STEP select with login).
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def get_to_screen4():
    print("🎯 GET TO SCREEN 4")
    print("=" * 25)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # SCREEN 1: Load UltraLibrarian
        print("🔸 Screen 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print("✅ Screen 1 loaded")
        
        # SCREEN 2: Search for LM358N
        print("🔸 Screen 2: Searching for LM358N...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        print("✅ Screen 2: Search results loaded")
        
        # SCREEN 3: Click TI LM358N
        print("🔸 Screen 3: Clicking TI LM358N...")
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    link.click()
                    break
            except:
                continue
        time.sleep(8)
        print("✅ Screen 3: Part details loaded")
        
        # SCREEN 4: Click Download Now
        print("🔸 Screen 4: Clicking Download Now...")
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            driver.execute_script("arguments[0].click();", download_btns[0])
            time.sleep(5)
            print("✅ Clicked Download Now")
        
        # Click 3D CAD Model
        print("🔸 Screen 4: Clicking 3D CAD Model...")
        time.sleep(5)
        cad_btns = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
        if cad_btns:
            driver.execute_script("arguments[0].click();", cad_btns[0])
            time.sleep(5)
            print("✅ Clicked 3D CAD Model")
        
        # Check for new window
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        print("✅ REACHED SCREEN 4: 3D STEP select screen with login")
        print(f"Current URL: {driver.current_url}")
        
        # Stay open
        print("\n🔒 BROWSER STAYING OPEN")
        print("You should now see Screen 4 - 3D STEP select with login")
        
        while True:
            time.sleep(10)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Browser staying open...")
        while True:
            time.sleep(10)

if __name__ == "__main__":
    get_to_screen4()
