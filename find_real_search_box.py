#!/usr/bin/env python3
"""
Find the real search box on TI.com and use it properly
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time

def find_real_search_box():
    print("FINDING REAL SEARCH BOX ON TI.COM")
    print("=" * 40)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        print("1. Loading ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(20)  # Wait for everything to load
        
        print("2. Looking for search elements...")
        
        # Try clicking on anything that looks like search
        search_elements = driver.find_elements(By.XPATH, "//*[contains(@class, 'search') or contains(text(), 'Search') or contains(@placeholder, 'search')]")
        
        print(f"Found {len(search_elements)} search-related elements")
        
        for i, elem in enumerate(search_elements):
            try:
                if elem.is_displayed():
                    print(f"Element {i+1}: {elem.tag_name} - '{elem.text}' - class: {elem.get_attribute('class')}")
                    
                    # Try clicking it
                    print(f"Clicking element {i+1}...")
                    elem.click()
                    time.sleep(5)
                    
                    # Look for input fields after clicking
                    inputs = driver.find_elements(By.TAG_NAME, "input")
                    print(f"After clicking: Found {len(inputs)} input elements")
                    
                    for inp in inputs:
                        try:
                            if inp.is_displayed() and inp.is_enabled():
                                print("Found active input - trying to type LM358N...")
                                inp.clear()
                                inp.send_keys("LM358N")
                                print("Typed LM358N - pressing Enter...")
                                inp.send_keys(Keys.RETURN)
                                time.sleep(10)
                                
                                if "LM358N" in driver.page_source.upper():
                                    print("✅ SUCCESS! Found LM358N results!")
                                    print(f"Final URL: {driver.current_url}")
                                    input("Press Enter to close...")
                                    return True
                        except Exception as e:
                            print(f"Input error: {e}")
                            continue
            except Exception as e:
                print(f"Element {i+1} error: {e}")
                continue
        
        print("❌ Could not find working search method")
        input("Press Enter to close...")
        return False
        
    finally:
        driver.quit()

if __name__ == "__main__":
    find_real_search_box()
