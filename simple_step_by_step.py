#!/usr/bin/env python3
"""
SIMPLE STEP BY STEP
===================
Simple version with immediate output.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def simple_step_by_step():
    print("🎯 SIMPLE STEP BY STEP - ULTRALIBRARIAN")
    print("=" * 50)
    print("Starting Chrome...")
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    print("✅ Chrome started")
    
    try:
        # SCREEN 1
        print("\n📺 SCREEN 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        print("✅ Navigation started, waiting...")
        
        time.sleep(10)
        print(f"Current title: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        response = input("\n❓ SCREEN 1 - Is UltraLibrarian loaded? (y/n): ")
        
        if response.lower() != 'y':
            print("❌ Stopping")
            return
        
        # SCREEN 2
        print("\n📺 SCREEN 2: Searching for LM358N...")
        
        # Find search box
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(inputs)} input elements")
        
        search_box = None
        for i, inp in enumerate(inputs):
            try:
                placeholder = inp.get_attribute('placeholder') or ''
                visible = inp.is_displayed()
                print(f"  Input {i}: '{placeholder}' (visible={visible})")
                
                if inp.is_displayed() and 'search' in placeholder.lower():
                    search_box = inp
                    print(f"  ✅ Using input {i} as search box")
                    break
            except Exception as e:
                print(f"  Input {i}: Error - {e}")
        
        if not search_box:
            print("❌ No search box found!")
            return
        
        # Enter search term
        print("Entering LM358N...")
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        print("✅ Search submitted")
        
        time.sleep(8)
        print(f"New URL: {driver.current_url}")
        
        response = input("\n❓ SCREEN 2 - Do you see search results? (y/n): ")
        
        if response.lower() != 'y':
            print("❌ Stopping")
            return
        
        # SCREEN 3
        print("\n📺 SCREEN 3: Looking for Texas Instruments LM358N...")
        
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"Found {len(links)} links")
        
        lm358_links = []
        for i, link in enumerate(links):
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                
                if ('lm358' in text.lower() or 'lm358' in href.lower()) and link.is_displayed():
                    lm358_links.append((i, text, href, link))
                    print(f"  LM358 Link {len(lm358_links)}: '{text}'")
                    
                    if len(lm358_links) >= 5:  # Limit output
                        break
            except:
                continue
        
        # Find TI specific
        ti_link = None
        for i, text, href, link_element in lm358_links:
            if (('lm358n/nopb' in text.lower() or 
                 ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                'details' in href.lower()):
                ti_link = (i, text, href, link_element)
                print(f"  ✅ Texas Instruments: '{text}'")
                break
        
        if not ti_link:
            print("❌ No TI LM358N found!")
            return
        
        response = input("\n❓ SCREEN 3 - Should I click the TI LM358N? (y/n): ")
        
        if response.lower() != 'y':
            print("❌ Stopping")
            return
        
        # Click TI part
        print("Clicking TI LM358N...")
        i, text, href, link_element = ti_link
        link_element.click()
        time.sleep(6)
        
        print(f"✅ Clicked: {text}")
        print(f"New URL: {driver.current_url}")
        
        response = input("\n❓ SCREEN 3 - Are you on the part details page? (y/n): ")
        
        if response.lower() != 'y':
            print("❌ Stopping")
            return
        
        # SCREEN 4
        print("\n📺 SCREEN 4: Looking for Download Now...")
        
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print("Available buttons:")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if text:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            print(f"✅ Found Download Now: '{download_btns[0].text}'")
        else:
            print("❌ No Download Now found!")
            return
        
        response = input("\n❓ SCREEN 4 - Should I click Download Now? (y/n): ")
        
        if response.lower() != 'y':
            print("❌ Stopping")
            return
        
        # Click Download Now
        print("Clicking Download Now...")
        driver.execute_script("arguments[0].click();", download_btns[0])
        time.sleep(4)
        print("✅ Clicked Download Now")
        
        response = input("\n❓ SCREEN 4 - What do you see after clicking Download Now? (describe): ")
        print(f"You said: {response}")
        
        print("\n🔍 INVESTIGATION POINT")
        print("Browser will stay open for inspection...")
        input("Press Enter to close...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        driver.quit()
        print("✅ Browser closed")

if __name__ == "__main__":
    simple_step_by_step()
