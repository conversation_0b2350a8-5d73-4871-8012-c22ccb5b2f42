#!/usr/bin/env python3
"""
Proper 3D Verification - Use our actual working methods to verify 3D availability
"""

import time
import os
import subprocess
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def verify_snapeda_3d(part_number):
    """Verify 3D availability using our working SnapEDA method"""
    print(f"🔍 VERIFYING SNAPEDA: {part_number}")
    print("-" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Use our proven working method
        print("📱 Step 1: Opening SnapEDA...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        # Find and click login
        print("📱 Step 2: Finding login...")
        login_elements = driver.find_elements(By.XPATH, "//a[contains(text(), 'Log In') or contains(text(), 'Login')]")
        
        if login_elements:
            login_elements[0].click()
            time.sleep(5)
            print("✅ Login page opened")
            
            # Check if we can access the login form
            username_field = driver.find_elements(By.XPATH, "//input[@name='username']")
            password_field = driver.find_elements(By.XPATH, "//input[@name='password']")
            
            if username_field and password_field:
                print("✅ Login form accessible")
                print("⚠️ Credentials needed for actual download")
                
                # Try to go directly to part page to see if it exists
                part_url = f"https://www.snapeda.com/parts/{part_number}/Texas%20Instruments/view-part/"
                print(f"📱 Step 3: Testing part page: {part_url}")
                
                driver.get(part_url)
                time.sleep(8)
                
                if "404" not in driver.title and part_number.upper() in driver.page_source.upper():
                    print("✅ Part page exists")
                    
                    # Look for 3D Model tab
                    model_tabs = driver.find_elements(By.XPATH, "//a[contains(text(), '3D Model') or contains(text(), '3D')]")
                    
                    if model_tabs:
                        print("✅ 3D Model tab found")
                        
                        # Click tab to see if download button exists
                        try:
                            model_tabs[0].click()
                            time.sleep(5)
                            
                            download_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), 'Download 3D Model')] | //button[contains(text(), 'Download 3D Model')]")
                            
                            if download_buttons:
                                print("🎉 VERIFIED: 3D model download available (login required)")
                                return True, "3D model available with login"
                            else:
                                print("❌ No download button found")
                                return False, "3D tab exists but no download button"
                        except:
                            print("⚠️ Could not click 3D tab")
                            return True, "3D tab exists (likely has model)"
                    else:
                        print("❌ No 3D Model tab found")
                        return False, "Part exists but no 3D model tab"
                else:
                    print("❌ Part page not found")
                    return False, "Part not found on SnapEDA"
            else:
                print("❌ Login form not accessible")
                return False, "Cannot access login form"
        else:
            print("❌ Login link not found")
            return False, "Cannot find login"
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, f"Verification error: {e}"
    finally:
        driver.quit()

def verify_ultralibrarian_3d(part_number):
    """Verify 3D availability using UltraLibrarian method"""
    print(f"🔍 VERIFYING ULTRALIBRARIAN: {part_number}")
    print("-" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Go to UltraLibrarian search
        search_url = f"https://www.ultralibrarian.com/search?q={part_number}"
        print(f"📱 Testing: {search_url}")
        
        driver.get(search_url)
        time.sleep(8)
        
        # Handle cookies
        cookie_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Accept')] | //button[contains(text(), 'OK')]")
        if cookie_buttons:
            cookie_buttons[0].click()
            time.sleep(2)
        
        # Look for part results
        if part_number.upper() in driver.page_source.upper():
            print("✅ Part found on UltraLibrarian")
            
            # Look for download or model indicators
            model_indicators = driver.find_elements(By.XPATH, "//*[contains(text(), '3D') or contains(text(), 'Model') or contains(text(), 'Download')]")
            
            if model_indicators:
                print(f"✅ Found {len(model_indicators)} model indicators")
                return True, "3D model likely available"
            else:
                print("⚠️ Part found but no clear 3D indicators")
                return False, "Part found but no 3D model indicators"
        else:
            print("❌ Part not found")
            return False, "Part not found on UltraLibrarian"
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, f"Verification error: {e}"
    finally:
        driver.quit()

def test_multiple_parts():
    """Test 3D verification with multiple parts"""
    print("🎯 PROPER 3D MODEL VERIFICATION TEST")
    print("=" * 70)
    print("Using our actual working methods (not public search)")
    
    # Test parts - including ones we know work
    test_parts = [
        "LM358N",           # We know this works
        "LM358N/NOPB",      # We know this works  
        "AD8065",           # Analog Devices part
        "IRF540N"           # Power MOSFET
    ]
    
    results = []
    
    for part in test_parts:
        print(f"\n{'='*70}")
        print(f"🧪 TESTING PART: {part}")
        print(f"{'='*70}")
        
        # Test SnapEDA
        snapeda_available, snapeda_msg = verify_snapeda_3d(part)
        
        # Brief pause
        time.sleep(3)
        
        # Test UltraLibrarian  
        ultra_available, ultra_msg = verify_ultralibrarian_3d(part)
        
        # Store results
        result = {
            'part': part,
            'snapeda': {'available': snapeda_available, 'message': snapeda_msg},
            'ultralibrarian': {'available': ultra_available, 'message': ultra_msg}
        }
        
        results.append(result)
        
        # Brief pause between parts
        time.sleep(5)
    
    # Summary
    print(f"\n{'='*70}")
    print("📋 VERIFICATION SUMMARY")
    print(f"{'='*70}")
    
    for result in results:
        part = result['part']
        print(f"\n📦 {part}:")
        
        # SnapEDA result
        snapeda_status = "✅" if result['snapeda']['available'] else "❌"
        print(f"  🔸 SnapEDA: {snapeda_status} {result['snapeda']['message']}")
        
        # UltraLibrarian result
        ultra_status = "✅" if result['ultralibrarian']['available'] else "❌"
        print(f"  🔸 UltraLibrarian: {ultra_status} {result['ultralibrarian']['message']}")
        
        # Overall assessment
        total_available = sum([result['snapeda']['available'], result['ultralibrarian']['available']])
        
        if total_available >= 1:
            print(f"  🎉 OVERALL: 3D model available from {total_available} source(s)")
        else:
            print(f"  ❌ OVERALL: No 3D models found")
    
    # Final count
    parts_with_3d = sum(1 for r in results if r['snapeda']['available'] or r['ultralibrarian']['available'])
    
    print(f"\n🎯 FINAL RESULT:")
    print(f"📊 {parts_with_3d}/{len(results)} parts have verified 3D model availability")
    
    if parts_with_3d > 0:
        print(f"🎉 SUCCESS: Found working 3D model sources!")
        print(f"💡 Use our working automation scripts to download them")
    else:
        print(f"❌ No 3D models verified")
        print(f"💡 May need login credentials or different parts")

if __name__ == "__main__":
    test_multiple_parts()
