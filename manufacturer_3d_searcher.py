#!/usr/bin/env python3
"""
Search manufacturer websites for 3D STEP models
Based on the successful Diodes Inc method
"""

import requests
from bs4 import BeautifulSoup
import os
import sys

class Manufacturer3DSearcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def search_manufacturer_3d(self, manufacturer_website, part_number, package_type=None):
        """Search manufacturer website for 3D models"""
        print(f"🔍 SEARCHING {manufacturer_website} FOR 3D MODELS")
        print(f"   Part: {part_number}")
        if package_type:
            print(f"   Package: {package_type}")
        
        try:
            # Try common search patterns
            search_patterns = [
                f"{manufacturer_website}/search?q={part_number}",
                f"{manufacturer_website}/part/search/{part_number}/",
                f"{manufacturer_website}/products/search?part={part_number}",
                f"{manufacturer_website}/part/{part_number}",
            ]
            
            for i, url in enumerate(search_patterns, 1):
                try:
                    print(f"   Trying pattern {i}: {url}")
                    response = self.session.get(url, timeout=30)
                    print(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        filename = f'manufacturer_search_pattern_{i}.html'
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(response.text)
                        
                        if part_number in response.text:
                            print(f"   ✅ FOUND PART in pattern {i}!")
                            return self.extract_3d_models(response.text, manufacturer_website, part_number, package_type)
                        else:
                            print(f"   ❌ Part not found in response")
                    
                except Exception as e:
                    print(f"   ❌ Error with pattern {i}: {e}")
            
            return None
            
        except Exception as e:
            print(f"   ❌ SEARCH ERROR: {e}")
            return None
    
    def extract_3d_models(self, html_content, website, part_number, package_type):
        """Extract 3D model links from manufacturer page"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for STEP file links
            step_files = []
            
            # Search for links containing STEP-related keywords
            for link in soup.find_all('a', href=True):
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                title = link.get('title', '').lower()
                
                # Check for STEP file indicators
                if any(keyword in href or keyword in text or keyword in title 
                       for keyword in ['step', '.stp', '3d', 'cad', 'model']):
                    
                    # If we have a package type, prioritize matching files
                    if package_type and package_type.lower() in href:
                        step_files.insert(0, {
                            'url': link.get('href'),
                            'text': link.get_text(strip=True),
                            'title': title,
                            'priority': 'high'
                        })
                    else:
                        step_files.append({
                            'url': link.get('href'),
                            'text': link.get_text(strip=True),
                            'title': title,
                            'priority': 'normal'
                        })
            
            if step_files:
                print(f"   🎯 Found {len(step_files)} potential 3D model links")
                for i, model in enumerate(step_files[:3], 1):  # Show first 3
                    print(f"   {i}. {model['text']} ({model['priority']})")
                    print(f"      URL: {model['url']}")
                
                return step_files
            else:
                print(f"   ❌ No 3D model links found")
                return None
                
        except Exception as e:
            print(f"   ❌ EXTRACTION ERROR: {e}")
            return None
    
    def download_step_files(self, step_files, website, part_number):
        """Download STEP files from the manufacturer"""
        if not step_files:
            return []
        
        downloaded_files = []
        
        for i, step_file in enumerate(step_files[:3], 1):  # Try first 3
            try:
                url = step_file['url']
                
                # Make URL absolute
                if not url.startswith('http'):
                    if url.startswith('/'):
                        url = f"{website}{url}"
                    else:
                        url = f"{website}/{url}"
                
                print(f"📥 Downloading STEP file {i}...")
                print(f"   URL: {url}")
                
                response = self.session.get(url, timeout=60, stream=True)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    # Create 3d directory
                    os.makedirs('3d', exist_ok=True)
                    
                    # Generate filename
                    website_name = website.replace('https://www.', '').replace('https://', '').replace('.com', '').replace('.org', '').replace('.net', '')
                    filename = f"{website_name}-{part_number}.step"
                    filepath = os.path.join('3d', filename)
                    
                    with open(filepath, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    
                    file_size = os.path.getsize(filepath)
                    print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                    
                    if file_size > 1000:  # At least 1KB
                        # Verify it's a STEP file
                        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                            first_lines = f.read(200)
                        
                        if 'ISO-10303' in first_lines or 'STEP' in first_lines:
                            print(f"   🎉 SUCCESS: Valid STEP file!")
                            downloaded_files.append(filename)
                        else:
                            print(f"   ⚠️  File doesn't appear to be STEP format")
                    else:
                        print(f"   ⚠️  File too small")
                else:
                    print(f"   ❌ Download failed: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Download error: {e}")
        
        return downloaded_files

def main():
    """Standalone usage"""
    if len(sys.argv) < 3:
        print("Usage: python manufacturer_3d_searcher.py <manufacturer_website> <part_number> [package_type]")
        print("Example: python manufacturer_3d_searcher.py https://www.diodes.com APX803L20-30SA-7 SOT23")
        return
    
    manufacturer_website = sys.argv[1]
    part_number = sys.argv[2]
    package_type = sys.argv[3] if len(sys.argv) > 3 else None
    
    print("🚀 MANUFACTURER 3D MODEL SEARCHER")
    print("=" * 50)
    
    searcher = Manufacturer3DSearcher()
    
    # Search for 3D models
    step_files = searcher.search_manufacturer_3d(manufacturer_website, part_number, package_type)
    
    if step_files:
        # Download STEP files
        downloaded = searcher.download_step_files(step_files, manufacturer_website, part_number)
        
        print("\n" + "=" * 50)
        if downloaded:
            print(f"✅ SUCCESS: Downloaded {len(downloaded)} STEP files!")
            for file in downloaded:
                print(f"   🎯 {file}")
        else:
            print("⚠️  PARTIAL SUCCESS: Found links but download failed")
    else:
        print("\n" + "=" * 50)
        print("❌ FAILED: Could not find 3D models on manufacturer website")

if __name__ == "__main__":
    main()
