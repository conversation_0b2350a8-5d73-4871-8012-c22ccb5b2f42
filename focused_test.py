#!/usr/bin/env python3
"""
FOCUSED TEST
============
Get to the 3D CAD Model step and show what's available.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def focused_test():
    print("🎯 FOCUSED ULTRALIBRARIAN TEST")
    print("=" * 40)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick run through steps 1-7
        print("🔸 Loading and navigating to 3D CAD Model step...")
        
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        # Search
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        # Click TI LM358N
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed()):
                    link.click()
                    break
            except:
                continue
        time.sleep(8)
        
        # Click Download Now
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            download_btns[0].click()
            time.sleep(5)
        
        # Click 3D CAD Model
        model_btns = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
        if model_btns:
            model_btns[0].click()
            time.sleep(5)
        
        print("✅ Reached 3D CAD Model step")
        print(f"Current URL: {driver.current_url}")
        
        # Check for new windows
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
            print(f"New window URL: {driver.current_url}")
        
        # Show what's on the page
        print("\n🔍 CURRENT PAGE ANALYSIS:")
        
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\nButtons ({len(buttons)} total):")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if text:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"\nLinks (first 15 of {len(links)}):")
        for i, link in enumerate(links[:15]):
            try:
                text = link.text.strip()
                visible = link.is_displayed()
                if text and len(text) < 60:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        # Look specifically for STEP
        print(f"\n🔍 STEP-related elements:")
        step_found = False
        for btn in buttons:
            try:
                if 'step' in btn.text.lower() and btn.is_displayed():
                    print(f"  STEP Button: '{btn.text}'")
                    step_found = True
            except:
                continue
        
        for link in links:
            try:
                if 'step' in link.text.lower() and link.is_displayed():
                    print(f"  STEP Link: '{link.text}'")
                    step_found = True
            except:
                continue
        
        if not step_found:
            print("  No STEP elements found")
        
        # Look for Download Now
        print(f"\n🔍 Download-related elements:")
        download_found = False
        for btn in buttons:
            try:
                if 'download' in btn.text.lower() and btn.is_displayed():
                    print(f"  Download Button: '{btn.text}'")
                    download_found = True
            except:
                continue
        
        if not download_found:
            print("  No Download elements found")
        
        # Look for login
        print(f"\n🔍 Login-related elements:")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        login_found = False
        for inp in inputs:
            try:
                input_type = inp.get_attribute('type') or ''
                if input_type.lower() in ['email', 'password'] and inp.is_displayed():
                    print(f"  Login Input: type='{input_type}'")
                    login_found = True
            except:
                continue
        
        if not login_found:
            print("  No Login elements found")
        
        print(f"\n🎯 WHAT DO YOU SEE?")
        print("Please look at the browser and tell me what the next step should be.")
        
        input("Press Enter to close...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    focused_test()
