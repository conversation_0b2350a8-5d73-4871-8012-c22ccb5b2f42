#!/usr/bin/env python3
"""
Focused SamacSys/Component Search Engine search
"""

import requests
from bs4 import BeautifulSoup
import json
import time

def search_samacsys(part_number):
    print(f"🔍 FOCUSED SAMACSYS SEARCH FOR: {part_number}")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    # SamacSys/Component Search Engine URLs to try
    search_urls = [
        {
            'name': 'Component Search Engine',
            'url': 'https://componentsearchengine.com/search',
            'params': {'term': part_number}
        },
        {
            'name': 'SamacSys Library',
            'url': 'https://library.samacsys.com/search',
            'params': {'term': part_number}
        },
        {
            'name': 'SamacSys Main',
            'url': 'https://www.samacsys.com/search',
            'params': {'q': part_number}
        }
    ]
    
    for i, search_config in enumerate(search_urls, 1):
        print(f"\n{i}. Testing {search_config['name']}...")
        print(f"   URL: {search_config['url']}")
        print(f"   Params: {search_config['params']}")
        
        try:
            time.sleep(2)  # Be polite
            response = session.get(search_config['url'], params=search_config['params'], timeout=30)
            print(f"   Status: {response.status_code}")
            print(f"   Final URL: {response.url}")
            
            if response.status_code == 200:
                # Save response
                filename = f"samacsys_{search_config['name'].lower().replace(' ', '_')}_search.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"   📄 Saved to {filename}")
                
                # Check if we found the part
                response_text = response.text.lower()
                if part_number.lower() in response_text:
                    print(f"   ✅ Found {part_number} in response!")
                    
                    # Parse for more details
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Look for part links
                    part_links = []
                    for link in soup.find_all('a', href=True):
                        href = link.get('href', '')
                        text = link.get_text(strip=True)
                        
                        if part_number.upper() in text.upper() or part_number.lower() in href.lower():
                            part_links.append({
                                'url': href,
                                'text': text
                            })
                    
                    if part_links:
                        print(f"   🎯 Found {len(part_links)} part links:")
                        for j, link in enumerate(part_links[:3], 1):
                            print(f"   {j}. {link['text']}")
                            print(f"      URL: {link['url']}")
                        
                        # Try to access the first part page
                        if part_links:
                            return access_part_page(session, part_links[0], search_config['name'])
                    
                    # Look for download/3D model links
                    model_links = []
                    for link in soup.find_all('a', href=True):
                        href = link.get('href', '').lower()
                        text = link.get_text(strip=True).lower()
                        
                        if any(keyword in href or keyword in text for keyword in ['step', '3d', 'model', 'cad', 'download']):
                            model_links.append({
                                'url': link.get('href'),
                                'text': link.get_text(strip=True)
                            })
                    
                    if model_links:
                        print(f"   📥 Found {len(model_links)} potential model links:")
                        for j, link in enumerate(model_links[:3], 1):
                            print(f"   {j}. {link['text']}")
                            print(f"      URL: {link['url']}")
                        return True
                    
                else:
                    print(f"   ❌ {part_number} not found in response")
                    
                    # Check if we got search results
                    if 'search' in response.url.lower() or 'results' in response_text:
                        print(f"   📋 Got search results page, but no matches")
                        
                        # Look for similar parts
                        soup = BeautifulSoup(response.text, 'html.parser')
                        similar_parts = []
                        
                        # Look for part numbers in the response
                        import re
                        part_pattern = r'APX\d+[A-Z0-9\-]*'
                        matches = re.findall(part_pattern, response.text, re.IGNORECASE)
                        
                        if matches:
                            print(f"   🔍 Found similar parts: {matches[:5]}")
            
            elif response.status_code == 404:
                print(f"   ❌ Not found (404)")
            else:
                print(f"   ❌ Failed with status {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            continue
    
    return False

def access_part_page(session, part_link, source_name):
    """Access a specific part page to look for 3D models"""
    print(f"\n🔍 Accessing part page from {source_name}...")
    
    try:
        # Make URL absolute if needed
        url = part_link['url']
        if not url.startswith('http'):
            if 'componentsearchengine.com' in source_name.lower():
                url = f"https://componentsearchengine.com{url}"
            elif 'library.samacsys.com' in source_name.lower():
                url = f"https://library.samacsys.com{url}"
            else:
                url = f"https://www.samacsys.com{url}"
        
        print(f"   URL: {url}")
        
        response = session.get(url, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save part page
            with open('samacsys_part_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"   📄 Saved part page")
            
            # Look for 3D model downloads
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for STEP download links
            step_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                
                if any(ext in href for ext in ['.step', '.stp']) or \
                   any(keyword in text for keyword in ['step', '3d model', 'cad model']):
                    step_links.append({
                        'url': link.get('href'),
                        'text': link.get_text(strip=True)
                    })
            
            if step_links:
                print(f"   🎯 Found {len(step_links)} STEP download links:")
                for i, link in enumerate(step_links, 1):
                    print(f"   {i}. {link['text']}")
                    print(f"      URL: {link['url']}")
                
                # Try to download the first STEP file
                if step_links:
                    return download_step_file(session, step_links[0], url)
            else:
                print(f"   ❌ No STEP download links found")
                
                # Look for any download buttons/links
                download_elements = soup.find_all(['a', 'button'], string=lambda text: text and 'download' in text.lower())
                if download_elements:
                    print(f"   📥 Found {len(download_elements)} download elements:")
                    for elem in download_elements[:3]:
                        print(f"   - {elem.get_text(strip=True)}")
        
        return False
        
    except Exception as e:
        print(f"   ❌ Error accessing part page: {e}")
        return False

def download_step_file(session, step_link, base_url):
    """Try to download a STEP file"""
    print(f"\n📥 Attempting to download STEP file...")
    
    try:
        # Make URL absolute
        url = step_link['url']
        if not url.startswith('http'):
            from urllib.parse import urljoin
            url = urljoin(base_url, url)
        
        print(f"   Download URL: {url}")
        
        response = session.get(url, timeout=60, stream=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Create 3d directory
            import os
            os.makedirs('3d', exist_ok=True)
            
            # Save file
            filename = f"APX803L20-30SA-7_SamacSys.step"
            filepath = os.path.join('3d', filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                # Verify it's a STEP file
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    first_lines = f.read(200)
                
                if 'ISO-10303' in first_lines or 'STEP' in first_lines:
                    print(f"   🎉 SUCCESS: Valid STEP file downloaded!")
                    return True
                else:
                    print(f"   ⚠️  File doesn't appear to be STEP format")
            else:
                print(f"   ⚠️  File too small, might be error page")
        
        return False
        
    except Exception as e:
        print(f"   ❌ Download error: {e}")
        return False

def main():
    part_number = "APX803L20-30SA-7"
    
    print("🚀 SAMACSYS/COMPONENT SEARCH ENGINE TEST")
    print("=" * 60)
    
    success = search_samacsys(part_number)
    
    print("\n" + "=" * 60)
    if success:
        print("✅ SUCCESS: Found and downloaded STEP file from SamacSys!")
    else:
        print("❌ FAILED: No STEP file found on SamacSys")

if __name__ == "__main__":
    main()
