ISO-10303-21;
HEADER;
/* C_0603_1608Metric.step 3D STEP model for use in ECAD systems
 * Copyright (C) 2017, kicad StepUp
 * 
 * This work is licensed under the [Creative Commons CC-BY-SA 4.0 License](https://creativecommons.org/licenses/by-sa/4.0/legalcode), 
 * with the following exception:
 * To the extent that the creation of electronic designs that use 'Licensed Material' can be considered to be 'Adapted Material', 
 * then the copyright holder waives article 3 of the license with respect to these designs and any generated files which use data provided 
 * as part of the 'Licensed Material'.
 * You are free to use the library data in your own projects without the obligation to share your project files under this or any other license agreement.
 * However, if you wish to redistribute these libraries, or parts thereof (including in modified form) as a collection then the exception above does not apply. 
 * Please refer to https://github.com/KiCad/kicad-packages3D/blob/master/LICENSE.md for further clarification of the exception.
 * Disclaimer of Warranties and Limitation of Liability.
 * These libraries are provided in the hope that they will be useful, but are provided without warranty of any kind, express or implied.
 * *USE 3D CAD DATA AT YOUR OWN RISK*
 * *DO NOT RELY UPON ANY INFORMATION FOUND HERE WITHOUT INDEPENDENT VERIFICATION.*
 * 
 */

FILE_DESCRIPTION(
/* description */ ('model of C_0603_1608Metric'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'C_0603_1608Metric.step',
/* time_stamp */ '2017-12-10T01:08:40',
/* author */ ('kicad StepUp','ksu'),
/* organization */ ('FreeCAD'),
/* preprocessor_version */ 'OCC',
/* originating_system */ 'kicad StepUp',
/* authorisation */ '');

FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;

DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('C_0603_1608Metric','C_0603_1608Metric','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#869);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#93,#124,#149,#174,#198,#222,#247,#272,#289,
#375,#407,#431,#456,#480,#505,#529,#554,#571,#657,#688,#713,#738,
#762,#786,#811,#836,#853));
#17 = ADVANCED_FACE('',(#18),#88,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#30,#39,#47,#56,#64,#73,#81));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(-0.8,-0.4,3.E-02));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(-0.8,-0.4,0.77));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(-0.8,-0.4,0.));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(0.,0.,1.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#22,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(-0.8,-0.37,0.));
#34 = CIRCLE('',#35,3.E-02);
#35 = AXIS2_PLACEMENT_3D('',#36,#37,#38);
#36 = CARTESIAN_POINT('',(-0.8,-0.37,3.E-02));
#37 = DIRECTION('',(1.,0.,-0.));
#38 = DIRECTION('',(0.,0.,1.));
#39 = ORIENTED_EDGE('',*,*,#40,.T.);
#40 = EDGE_CURVE('',#32,#41,#43,.T.);
#41 = VERTEX_POINT('',#42);
#42 = CARTESIAN_POINT('',(-0.8,0.37,0.));
#43 = LINE('',#44,#45);
#44 = CARTESIAN_POINT('',(-0.8,-0.4,0.));
#45 = VECTOR('',#46,1.);
#46 = DIRECTION('',(0.,1.,0.));
#47 = ORIENTED_EDGE('',*,*,#48,.F.);
#48 = EDGE_CURVE('',#49,#41,#51,.T.);
#49 = VERTEX_POINT('',#50);
#50 = CARTESIAN_POINT('',(-0.8,0.4,3.E-02));
#51 = CIRCLE('',#52,3.E-02);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(-0.8,0.37,3.E-02));
#54 = DIRECTION('',(-1.,0.,0.));
#55 = DIRECTION('',(0.,0.,1.));
#56 = ORIENTED_EDGE('',*,*,#57,.T.);
#57 = EDGE_CURVE('',#49,#58,#60,.T.);
#58 = VERTEX_POINT('',#59);
#59 = CARTESIAN_POINT('',(-0.8,0.4,0.77));
#60 = LINE('',#61,#62);
#61 = CARTESIAN_POINT('',(-0.8,0.4,0.));
#62 = VECTOR('',#63,1.);
#63 = DIRECTION('',(0.,0.,1.));
#64 = ORIENTED_EDGE('',*,*,#65,.T.);
#65 = EDGE_CURVE('',#58,#66,#68,.T.);
#66 = VERTEX_POINT('',#67);
#67 = CARTESIAN_POINT('',(-0.8,0.37,0.8));
#68 = CIRCLE('',#69,3.E-02);
#69 = AXIS2_PLACEMENT_3D('',#70,#71,#72);
#70 = CARTESIAN_POINT('',(-0.8,0.37,0.77));
#71 = DIRECTION('',(1.,0.,-0.));
#72 = DIRECTION('',(0.,0.,1.));
#73 = ORIENTED_EDGE('',*,*,#74,.F.);
#74 = EDGE_CURVE('',#75,#66,#77,.T.);
#75 = VERTEX_POINT('',#76);
#76 = CARTESIAN_POINT('',(-0.8,-0.37,0.8));
#77 = LINE('',#78,#79);
#78 = CARTESIAN_POINT('',(-0.8,-0.4,0.8));
#79 = VECTOR('',#80,1.);
#80 = DIRECTION('',(0.,1.,0.));
#81 = ORIENTED_EDGE('',*,*,#82,.F.);
#82 = EDGE_CURVE('',#24,#75,#83,.T.);
#83 = CIRCLE('',#84,3.E-02);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(-0.8,-0.37,0.77));
#86 = DIRECTION('',(-1.,0.,0.));
#87 = DIRECTION('',(0.,0.,1.));
#88 = PLANE('',#89);
#89 = AXIS2_PLACEMENT_3D('',#90,#91,#92);
#90 = CARTESIAN_POINT('',(-0.8,-0.4,0.));
#91 = DIRECTION('',(1.,0.,0.));
#92 = DIRECTION('',(0.,0.,1.));
#93 = ADVANCED_FACE('',(#94),#119,.F.);
#94 = FACE_BOUND('',#95,.F.);
#95 = EDGE_LOOP('',(#96,#106,#112,#113));
#96 = ORIENTED_EDGE('',*,*,#97,.F.);
#97 = EDGE_CURVE('',#98,#100,#102,.T.);
#98 = VERTEX_POINT('',#99);
#99 = CARTESIAN_POINT('',(-0.5,-0.4,3.E-02));
#100 = VERTEX_POINT('',#101);
#101 = CARTESIAN_POINT('',(-0.5,-0.4,0.77));
#102 = LINE('',#103,#104);
#103 = CARTESIAN_POINT('',(-0.5,-0.4,0.));
#104 = VECTOR('',#105,1.);
#105 = DIRECTION('',(0.,0.,1.));
#106 = ORIENTED_EDGE('',*,*,#107,.F.);
#107 = EDGE_CURVE('',#22,#98,#108,.T.);
#108 = LINE('',#109,#110);
#109 = CARTESIAN_POINT('',(-0.8,-0.4,3.E-02));
#110 = VECTOR('',#111,1.);
#111 = DIRECTION('',(1.,0.,0.));
#112 = ORIENTED_EDGE('',*,*,#21,.T.);
#113 = ORIENTED_EDGE('',*,*,#114,.T.);
#114 = EDGE_CURVE('',#24,#100,#115,.T.);
#115 = LINE('',#116,#117);
#116 = CARTESIAN_POINT('',(-0.8,-0.4,0.77));
#117 = VECTOR('',#118,1.);
#118 = DIRECTION('',(1.,0.,0.));
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(-0.8,-0.4,0.));
#122 = DIRECTION('',(0.,1.,0.));
#123 = DIRECTION('',(0.,0.,1.));
#124 = ADVANCED_FACE('',(#125),#144,.T.);
#125 = FACE_BOUND('',#126,.T.);
#126 = EDGE_LOOP('',(#127,#128,#136,#143));
#127 = ORIENTED_EDGE('',*,*,#31,.T.);
#128 = ORIENTED_EDGE('',*,*,#129,.T.);
#129 = EDGE_CURVE('',#32,#130,#132,.T.);
#130 = VERTEX_POINT('',#131);
#131 = CARTESIAN_POINT('',(-0.5,-0.37,0.));
#132 = LINE('',#133,#134);
#133 = CARTESIAN_POINT('',(-0.8,-0.37,0.));
#134 = VECTOR('',#135,1.);
#135 = DIRECTION('',(1.,0.,0.));
#136 = ORIENTED_EDGE('',*,*,#137,.F.);
#137 = EDGE_CURVE('',#98,#130,#138,.T.);
#138 = CIRCLE('',#139,3.E-02);
#139 = AXIS2_PLACEMENT_3D('',#140,#141,#142);
#140 = CARTESIAN_POINT('',(-0.5,-0.37,3.E-02));
#141 = DIRECTION('',(1.,0.,-0.));
#142 = DIRECTION('',(0.,0.,1.));
#143 = ORIENTED_EDGE('',*,*,#107,.F.);
#144 = CYLINDRICAL_SURFACE('',#145,3.E-02);
#145 = AXIS2_PLACEMENT_3D('',#146,#147,#148);
#146 = CARTESIAN_POINT('',(-0.8,-0.37,3.E-02));
#147 = DIRECTION('',(1.,0.,0.));
#148 = DIRECTION('',(0.,-1.,0.));
#149 = ADVANCED_FACE('',(#150),#169,.T.);
#150 = FACE_BOUND('',#151,.F.);
#151 = EDGE_LOOP('',(#152,#153,#161,#168));
#152 = ORIENTED_EDGE('',*,*,#82,.T.);
#153 = ORIENTED_EDGE('',*,*,#154,.T.);
#154 = EDGE_CURVE('',#75,#155,#157,.T.);
#155 = VERTEX_POINT('',#156);
#156 = CARTESIAN_POINT('',(-0.5,-0.37,0.8));
#157 = LINE('',#158,#159);
#158 = CARTESIAN_POINT('',(-0.8,-0.37,0.8));
#159 = VECTOR('',#160,1.);
#160 = DIRECTION('',(1.,0.,0.));
#161 = ORIENTED_EDGE('',*,*,#162,.F.);
#162 = EDGE_CURVE('',#100,#155,#163,.T.);
#163 = CIRCLE('',#164,3.E-02);
#164 = AXIS2_PLACEMENT_3D('',#165,#166,#167);
#165 = CARTESIAN_POINT('',(-0.5,-0.37,0.77));
#166 = DIRECTION('',(-1.,0.,0.));
#167 = DIRECTION('',(0.,0.,1.));
#168 = ORIENTED_EDGE('',*,*,#114,.F.);
#169 = CYLINDRICAL_SURFACE('',#170,3.E-02);
#170 = AXIS2_PLACEMENT_3D('',#171,#172,#173);
#171 = CARTESIAN_POINT('',(-0.8,-0.37,0.77));
#172 = DIRECTION('',(1.,0.,0.));
#173 = DIRECTION('',(0.,-1.,0.));
#174 = ADVANCED_FACE('',(#175),#193,.F.);
#175 = FACE_BOUND('',#176,.F.);
#176 = EDGE_LOOP('',(#177,#178,#179,#187));
#177 = ORIENTED_EDGE('',*,*,#40,.F.);
#178 = ORIENTED_EDGE('',*,*,#129,.T.);
#179 = ORIENTED_EDGE('',*,*,#180,.T.);
#180 = EDGE_CURVE('',#130,#181,#183,.T.);
#181 = VERTEX_POINT('',#182);
#182 = CARTESIAN_POINT('',(-0.5,0.37,0.));
#183 = LINE('',#184,#185);
#184 = CARTESIAN_POINT('',(-0.5,-0.4,0.));
#185 = VECTOR('',#186,1.);
#186 = DIRECTION('',(0.,1.,0.));
#187 = ORIENTED_EDGE('',*,*,#188,.F.);
#188 = EDGE_CURVE('',#41,#181,#189,.T.);
#189 = LINE('',#190,#191);
#190 = CARTESIAN_POINT('',(-0.8,0.37,0.));
#191 = VECTOR('',#192,1.);
#192 = DIRECTION('',(1.,0.,0.));
#193 = PLANE('',#194);
#194 = AXIS2_PLACEMENT_3D('',#195,#196,#197);
#195 = CARTESIAN_POINT('',(-0.8,-0.4,0.));
#196 = DIRECTION('',(0.,0.,1.));
#197 = DIRECTION('',(1.,0.,0.));
#198 = ADVANCED_FACE('',(#199),#217,.T.);
#199 = FACE_BOUND('',#200,.T.);
#200 = EDGE_LOOP('',(#201,#202,#203,#211));
#201 = ORIENTED_EDGE('',*,*,#74,.F.);
#202 = ORIENTED_EDGE('',*,*,#154,.T.);
#203 = ORIENTED_EDGE('',*,*,#204,.T.);
#204 = EDGE_CURVE('',#155,#205,#207,.T.);
#205 = VERTEX_POINT('',#206);
#206 = CARTESIAN_POINT('',(-0.5,0.37,0.8));
#207 = LINE('',#208,#209);
#208 = CARTESIAN_POINT('',(-0.5,-0.4,0.8));
#209 = VECTOR('',#210,1.);
#210 = DIRECTION('',(0.,1.,0.));
#211 = ORIENTED_EDGE('',*,*,#212,.F.);
#212 = EDGE_CURVE('',#66,#205,#213,.T.);
#213 = LINE('',#214,#215);
#214 = CARTESIAN_POINT('',(-0.8,0.37,0.8));
#215 = VECTOR('',#216,1.);
#216 = DIRECTION('',(1.,0.,0.));
#217 = PLANE('',#218);
#218 = AXIS2_PLACEMENT_3D('',#219,#220,#221);
#219 = CARTESIAN_POINT('',(-0.8,-0.4,0.8));
#220 = DIRECTION('',(0.,0.,1.));
#221 = DIRECTION('',(1.,0.,0.));
#222 = ADVANCED_FACE('',(#223),#242,.T.);
#223 = FACE_BOUND('',#224,.F.);
#224 = EDGE_LOOP('',(#225,#226,#227,#236));
#225 = ORIENTED_EDGE('',*,*,#48,.T.);
#226 = ORIENTED_EDGE('',*,*,#188,.T.);
#227 = ORIENTED_EDGE('',*,*,#228,.F.);
#228 = EDGE_CURVE('',#229,#181,#231,.T.);
#229 = VERTEX_POINT('',#230);
#230 = CARTESIAN_POINT('',(-0.5,0.4,3.E-02));
#231 = CIRCLE('',#232,3.E-02);
#232 = AXIS2_PLACEMENT_3D('',#233,#234,#235);
#233 = CARTESIAN_POINT('',(-0.5,0.37,3.E-02));
#234 = DIRECTION('',(-1.,0.,0.));
#235 = DIRECTION('',(0.,0.,1.));
#236 = ORIENTED_EDGE('',*,*,#237,.F.);
#237 = EDGE_CURVE('',#49,#229,#238,.T.);
#238 = LINE('',#239,#240);
#239 = CARTESIAN_POINT('',(-0.8,0.4,3.E-02));
#240 = VECTOR('',#241,1.);
#241 = DIRECTION('',(1.,0.,0.));
#242 = CYLINDRICAL_SURFACE('',#243,3.E-02);
#243 = AXIS2_PLACEMENT_3D('',#244,#245,#246);
#244 = CARTESIAN_POINT('',(-0.8,0.37,3.E-02));
#245 = DIRECTION('',(1.,0.,0.));
#246 = DIRECTION('',(0.,1.,0.));
#247 = ADVANCED_FACE('',(#248),#267,.T.);
#248 = FACE_BOUND('',#249,.T.);
#249 = EDGE_LOOP('',(#250,#251,#252,#261));
#250 = ORIENTED_EDGE('',*,*,#65,.T.);
#251 = ORIENTED_EDGE('',*,*,#212,.T.);
#252 = ORIENTED_EDGE('',*,*,#253,.F.);
#253 = EDGE_CURVE('',#254,#205,#256,.T.);
#254 = VERTEX_POINT('',#255);
#255 = CARTESIAN_POINT('',(-0.5,0.4,0.77));
#256 = CIRCLE('',#257,3.E-02);
#257 = AXIS2_PLACEMENT_3D('',#258,#259,#260);
#258 = CARTESIAN_POINT('',(-0.5,0.37,0.77));
#259 = DIRECTION('',(1.,0.,-0.));
#260 = DIRECTION('',(0.,0.,1.));
#261 = ORIENTED_EDGE('',*,*,#262,.F.);
#262 = EDGE_CURVE('',#58,#254,#263,.T.);
#263 = LINE('',#264,#265);
#264 = CARTESIAN_POINT('',(-0.8,0.4,0.77));
#265 = VECTOR('',#266,1.);
#266 = DIRECTION('',(1.,0.,0.));
#267 = CYLINDRICAL_SURFACE('',#268,3.E-02);
#268 = AXIS2_PLACEMENT_3D('',#269,#270,#271);
#269 = CARTESIAN_POINT('',(-0.8,0.37,0.77));
#270 = DIRECTION('',(1.,0.,0.));
#271 = DIRECTION('',(0.,1.,0.));
#272 = ADVANCED_FACE('',(#273),#284,.T.);
#273 = FACE_BOUND('',#274,.T.);
#274 = EDGE_LOOP('',(#275,#281,#282,#283));
#275 = ORIENTED_EDGE('',*,*,#276,.F.);
#276 = EDGE_CURVE('',#229,#254,#277,.T.);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(-0.5,0.4,0.));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(0.,0.,1.));
#281 = ORIENTED_EDGE('',*,*,#237,.F.);
#282 = ORIENTED_EDGE('',*,*,#57,.T.);
#283 = ORIENTED_EDGE('',*,*,#262,.T.);
#284 = PLANE('',#285);
#285 = AXIS2_PLACEMENT_3D('',#286,#287,#288);
#286 = CARTESIAN_POINT('',(-0.8,0.4,0.));
#287 = DIRECTION('',(0.,1.,0.));
#288 = DIRECTION('',(0.,0.,1.));
#289 = ADVANCED_FACE('',(#290,#300),#370,.T.);
#290 = FACE_BOUND('',#291,.T.);
#291 = EDGE_LOOP('',(#292,#293,#294,#295,#296,#297,#298,#299));
#292 = ORIENTED_EDGE('',*,*,#97,.F.);
#293 = ORIENTED_EDGE('',*,*,#137,.T.);
#294 = ORIENTED_EDGE('',*,*,#180,.T.);
#295 = ORIENTED_EDGE('',*,*,#228,.F.);
#296 = ORIENTED_EDGE('',*,*,#276,.T.);
#297 = ORIENTED_EDGE('',*,*,#253,.T.);
#298 = ORIENTED_EDGE('',*,*,#204,.F.);
#299 = ORIENTED_EDGE('',*,*,#162,.F.);
#300 = FACE_BOUND('',#301,.T.);
#301 = EDGE_LOOP('',(#302,#313,#321,#330,#338,#347,#355,#364));
#302 = ORIENTED_EDGE('',*,*,#303,.F.);
#303 = EDGE_CURVE('',#304,#306,#308,.T.);
#304 = VERTEX_POINT('',#305);
#305 = CARTESIAN_POINT('',(-0.5,-0.37,6.E-02));
#306 = VERTEX_POINT('',#307);
#307 = CARTESIAN_POINT('',(-0.5,-0.34,3.E-02));
#308 = CIRCLE('',#309,3.E-02);
#309 = AXIS2_PLACEMENT_3D('',#310,#311,#312);
#310 = CARTESIAN_POINT('',(-0.5,-0.34,6.E-02));
#311 = DIRECTION('',(1.,0.,-0.));
#312 = DIRECTION('',(0.,0.,1.));
#313 = ORIENTED_EDGE('',*,*,#314,.T.);
#314 = EDGE_CURVE('',#304,#315,#317,.T.);
#315 = VERTEX_POINT('',#316);
#316 = CARTESIAN_POINT('',(-0.5,-0.37,0.74));
#317 = LINE('',#318,#319);
#318 = CARTESIAN_POINT('',(-0.5,-0.37,3.E-02));
#319 = VECTOR('',#320,1.);
#320 = DIRECTION('',(0.,0.,1.));
#321 = ORIENTED_EDGE('',*,*,#322,.T.);
#322 = EDGE_CURVE('',#315,#323,#325,.T.);
#323 = VERTEX_POINT('',#324);
#324 = CARTESIAN_POINT('',(-0.5,-0.34,0.77));
#325 = CIRCLE('',#326,3.E-02);
#326 = AXIS2_PLACEMENT_3D('',#327,#328,#329);
#327 = CARTESIAN_POINT('',(-0.5,-0.34,0.74));
#328 = DIRECTION('',(-1.,0.,0.));
#329 = DIRECTION('',(0.,0.,1.));
#330 = ORIENTED_EDGE('',*,*,#331,.T.);
#331 = EDGE_CURVE('',#323,#332,#334,.T.);
#332 = VERTEX_POINT('',#333);
#333 = CARTESIAN_POINT('',(-0.5,0.34,0.77));
#334 = LINE('',#335,#336);
#335 = CARTESIAN_POINT('',(-0.5,-0.37,0.77));
#336 = VECTOR('',#337,1.);
#337 = DIRECTION('',(0.,1.,0.));
#338 = ORIENTED_EDGE('',*,*,#339,.F.);
#339 = EDGE_CURVE('',#340,#332,#342,.T.);
#340 = VERTEX_POINT('',#341);
#341 = CARTESIAN_POINT('',(-0.5,0.37,0.74));
#342 = CIRCLE('',#343,3.E-02);
#343 = AXIS2_PLACEMENT_3D('',#344,#345,#346);
#344 = CARTESIAN_POINT('',(-0.5,0.34,0.74));
#345 = DIRECTION('',(1.,0.,-0.));
#346 = DIRECTION('',(0.,0.,1.));
#347 = ORIENTED_EDGE('',*,*,#348,.F.);
#348 = EDGE_CURVE('',#349,#340,#351,.T.);
#349 = VERTEX_POINT('',#350);
#350 = CARTESIAN_POINT('',(-0.5,0.37,6.E-02));
#351 = LINE('',#352,#353);
#352 = CARTESIAN_POINT('',(-0.5,0.37,3.E-02));
#353 = VECTOR('',#354,1.);
#354 = DIRECTION('',(0.,0.,1.));
#355 = ORIENTED_EDGE('',*,*,#356,.T.);
#356 = EDGE_CURVE('',#349,#357,#359,.T.);
#357 = VERTEX_POINT('',#358);
#358 = CARTESIAN_POINT('',(-0.5,0.34,3.E-02));
#359 = CIRCLE('',#360,3.E-02);
#360 = AXIS2_PLACEMENT_3D('',#361,#362,#363);
#361 = CARTESIAN_POINT('',(-0.5,0.34,6.E-02));
#362 = DIRECTION('',(-1.,0.,0.));
#363 = DIRECTION('',(0.,0.,1.));
#364 = ORIENTED_EDGE('',*,*,#365,.F.);
#365 = EDGE_CURVE('',#306,#357,#366,.T.);
#366 = LINE('',#367,#368);
#367 = CARTESIAN_POINT('',(-0.5,-0.37,3.E-02));
#368 = VECTOR('',#369,1.);
#369 = DIRECTION('',(0.,1.,0.));
#370 = PLANE('',#371);
#371 = AXIS2_PLACEMENT_3D('',#372,#373,#374);
#372 = CARTESIAN_POINT('',(-0.5,-0.4,0.));
#373 = DIRECTION('',(1.,0.,0.));
#374 = DIRECTION('',(0.,0.,1.));
#375 = ADVANCED_FACE('',(#376),#402,.T.);
#376 = FACE_BOUND('',#377,.T.);
#377 = EDGE_LOOP('',(#378,#379,#387,#396));
#378 = ORIENTED_EDGE('',*,*,#303,.T.);
#379 = ORIENTED_EDGE('',*,*,#380,.T.);
#380 = EDGE_CURVE('',#306,#381,#383,.T.);
#381 = VERTEX_POINT('',#382);
#382 = CARTESIAN_POINT('',(0.5,-0.34,3.E-02));
#383 = LINE('',#384,#385);
#384 = CARTESIAN_POINT('',(-0.5,-0.34,3.E-02));
#385 = VECTOR('',#386,1.);
#386 = DIRECTION('',(1.,0.,0.));
#387 = ORIENTED_EDGE('',*,*,#388,.F.);
#388 = EDGE_CURVE('',#389,#381,#391,.T.);
#389 = VERTEX_POINT('',#390);
#390 = CARTESIAN_POINT('',(0.5,-0.37,6.E-02));
#391 = CIRCLE('',#392,3.E-02);
#392 = AXIS2_PLACEMENT_3D('',#393,#394,#395);
#393 = CARTESIAN_POINT('',(0.5,-0.34,6.E-02));
#394 = DIRECTION('',(1.,0.,-0.));
#395 = DIRECTION('',(0.,0.,1.));
#396 = ORIENTED_EDGE('',*,*,#397,.F.);
#397 = EDGE_CURVE('',#304,#389,#398,.T.);
#398 = LINE('',#399,#400);
#399 = CARTESIAN_POINT('',(-0.5,-0.37,6.E-02));
#400 = VECTOR('',#401,1.);
#401 = DIRECTION('',(1.,0.,0.));
#402 = CYLINDRICAL_SURFACE('',#403,3.E-02);
#403 = AXIS2_PLACEMENT_3D('',#404,#405,#406);
#404 = CARTESIAN_POINT('',(-0.5,-0.34,6.E-02));
#405 = DIRECTION('',(1.,0.,0.));
#406 = DIRECTION('',(0.,-1.,0.));
#407 = ADVANCED_FACE('',(#408),#426,.F.);
#408 = FACE_BOUND('',#409,.F.);
#409 = EDGE_LOOP('',(#410,#411,#412,#420));
#410 = ORIENTED_EDGE('',*,*,#365,.F.);
#411 = ORIENTED_EDGE('',*,*,#380,.T.);
#412 = ORIENTED_EDGE('',*,*,#413,.T.);
#413 = EDGE_CURVE('',#381,#414,#416,.T.);
#414 = VERTEX_POINT('',#415);
#415 = CARTESIAN_POINT('',(0.5,0.34,3.E-02));
#416 = LINE('',#417,#418);
#417 = CARTESIAN_POINT('',(0.5,-0.37,3.E-02));
#418 = VECTOR('',#419,1.);
#419 = DIRECTION('',(0.,1.,0.));
#420 = ORIENTED_EDGE('',*,*,#421,.F.);
#421 = EDGE_CURVE('',#357,#414,#422,.T.);
#422 = LINE('',#423,#424);
#423 = CARTESIAN_POINT('',(-0.5,0.34,3.E-02));
#424 = VECTOR('',#425,1.);
#425 = DIRECTION('',(1.,0.,0.));
#426 = PLANE('',#427);
#427 = AXIS2_PLACEMENT_3D('',#428,#429,#430);
#428 = CARTESIAN_POINT('',(-0.5,-0.37,3.E-02));
#429 = DIRECTION('',(0.,0.,1.));
#430 = DIRECTION('',(1.,0.,0.));
#431 = ADVANCED_FACE('',(#432),#451,.T.);
#432 = FACE_BOUND('',#433,.F.);
#433 = EDGE_LOOP('',(#434,#435,#436,#445));
#434 = ORIENTED_EDGE('',*,*,#356,.T.);
#435 = ORIENTED_EDGE('',*,*,#421,.T.);
#436 = ORIENTED_EDGE('',*,*,#437,.F.);
#437 = EDGE_CURVE('',#438,#414,#440,.T.);
#438 = VERTEX_POINT('',#439);
#439 = CARTESIAN_POINT('',(0.5,0.37,6.E-02));
#440 = CIRCLE('',#441,3.E-02);
#441 = AXIS2_PLACEMENT_3D('',#442,#443,#444);
#442 = CARTESIAN_POINT('',(0.5,0.34,6.E-02));
#443 = DIRECTION('',(-1.,0.,0.));
#444 = DIRECTION('',(0.,0.,1.));
#445 = ORIENTED_EDGE('',*,*,#446,.F.);
#446 = EDGE_CURVE('',#349,#438,#447,.T.);
#447 = LINE('',#448,#449);
#448 = CARTESIAN_POINT('',(-0.5,0.37,6.E-02));
#449 = VECTOR('',#450,1.);
#450 = DIRECTION('',(1.,0.,0.));
#451 = CYLINDRICAL_SURFACE('',#452,3.E-02);
#452 = AXIS2_PLACEMENT_3D('',#453,#454,#455);
#453 = CARTESIAN_POINT('',(-0.5,0.34,6.E-02));
#454 = DIRECTION('',(1.,0.,0.));
#455 = DIRECTION('',(0.,1.,0.));
#456 = ADVANCED_FACE('',(#457),#475,.T.);
#457 = FACE_BOUND('',#458,.T.);
#458 = EDGE_LOOP('',(#459,#467,#468,#469));
#459 = ORIENTED_EDGE('',*,*,#460,.F.);
#460 = EDGE_CURVE('',#438,#461,#463,.T.);
#461 = VERTEX_POINT('',#462);
#462 = CARTESIAN_POINT('',(0.5,0.37,0.74));
#463 = LINE('',#464,#465);
#464 = CARTESIAN_POINT('',(0.5,0.37,3.E-02));
#465 = VECTOR('',#466,1.);
#466 = DIRECTION('',(0.,0.,1.));
#467 = ORIENTED_EDGE('',*,*,#446,.F.);
#468 = ORIENTED_EDGE('',*,*,#348,.T.);
#469 = ORIENTED_EDGE('',*,*,#470,.T.);
#470 = EDGE_CURVE('',#340,#461,#471,.T.);
#471 = LINE('',#472,#473);
#472 = CARTESIAN_POINT('',(-0.5,0.37,0.74));
#473 = VECTOR('',#474,1.);
#474 = DIRECTION('',(1.,0.,0.));
#475 = PLANE('',#476);
#476 = AXIS2_PLACEMENT_3D('',#477,#478,#479);
#477 = CARTESIAN_POINT('',(-0.5,0.37,3.E-02));
#478 = DIRECTION('',(0.,1.,0.));
#479 = DIRECTION('',(0.,0.,1.));
#480 = ADVANCED_FACE('',(#481),#500,.T.);
#481 = FACE_BOUND('',#482,.T.);
#482 = EDGE_LOOP('',(#483,#484,#492,#499));
#483 = ORIENTED_EDGE('',*,*,#339,.T.);
#484 = ORIENTED_EDGE('',*,*,#485,.T.);
#485 = EDGE_CURVE('',#332,#486,#488,.T.);
#486 = VERTEX_POINT('',#487);
#487 = CARTESIAN_POINT('',(0.5,0.34,0.77));
#488 = LINE('',#489,#490);
#489 = CARTESIAN_POINT('',(-0.5,0.34,0.77));
#490 = VECTOR('',#491,1.);
#491 = DIRECTION('',(1.,0.,0.));
#492 = ORIENTED_EDGE('',*,*,#493,.F.);
#493 = EDGE_CURVE('',#461,#486,#494,.T.);
#494 = CIRCLE('',#495,3.E-02);
#495 = AXIS2_PLACEMENT_3D('',#496,#497,#498);
#496 = CARTESIAN_POINT('',(0.5,0.34,0.74));
#497 = DIRECTION('',(1.,0.,-0.));
#498 = DIRECTION('',(0.,0.,1.));
#499 = ORIENTED_EDGE('',*,*,#470,.F.);
#500 = CYLINDRICAL_SURFACE('',#501,3.E-02);
#501 = AXIS2_PLACEMENT_3D('',#502,#503,#504);
#502 = CARTESIAN_POINT('',(-0.5,0.34,0.74));
#503 = DIRECTION('',(1.,0.,0.));
#504 = DIRECTION('',(0.,1.,0.));
#505 = ADVANCED_FACE('',(#506),#524,.T.);
#506 = FACE_BOUND('',#507,.T.);
#507 = EDGE_LOOP('',(#508,#509,#517,#523));
#508 = ORIENTED_EDGE('',*,*,#331,.F.);
#509 = ORIENTED_EDGE('',*,*,#510,.T.);
#510 = EDGE_CURVE('',#323,#511,#513,.T.);
#511 = VERTEX_POINT('',#512);
#512 = CARTESIAN_POINT('',(0.5,-0.34,0.77));
#513 = LINE('',#514,#515);
#514 = CARTESIAN_POINT('',(-0.5,-0.34,0.77));
#515 = VECTOR('',#516,1.);
#516 = DIRECTION('',(1.,0.,0.));
#517 = ORIENTED_EDGE('',*,*,#518,.T.);
#518 = EDGE_CURVE('',#511,#486,#519,.T.);
#519 = LINE('',#520,#521);
#520 = CARTESIAN_POINT('',(0.5,-0.37,0.77));
#521 = VECTOR('',#522,1.);
#522 = DIRECTION('',(0.,1.,0.));
#523 = ORIENTED_EDGE('',*,*,#485,.F.);
#524 = PLANE('',#525);
#525 = AXIS2_PLACEMENT_3D('',#526,#527,#528);
#526 = CARTESIAN_POINT('',(-0.5,-0.37,0.77));
#527 = DIRECTION('',(0.,0.,1.));
#528 = DIRECTION('',(1.,0.,0.));
#529 = ADVANCED_FACE('',(#530),#549,.T.);
#530 = FACE_BOUND('',#531,.F.);
#531 = EDGE_LOOP('',(#532,#533,#534,#543));
#532 = ORIENTED_EDGE('',*,*,#322,.T.);
#533 = ORIENTED_EDGE('',*,*,#510,.T.);
#534 = ORIENTED_EDGE('',*,*,#535,.F.);
#535 = EDGE_CURVE('',#536,#511,#538,.T.);
#536 = VERTEX_POINT('',#537);
#537 = CARTESIAN_POINT('',(0.5,-0.37,0.74));
#538 = CIRCLE('',#539,3.E-02);
#539 = AXIS2_PLACEMENT_3D('',#540,#541,#542);
#540 = CARTESIAN_POINT('',(0.5,-0.34,0.74));
#541 = DIRECTION('',(-1.,0.,0.));
#542 = DIRECTION('',(0.,0.,1.));
#543 = ORIENTED_EDGE('',*,*,#544,.F.);
#544 = EDGE_CURVE('',#315,#536,#545,.T.);
#545 = LINE('',#546,#547);
#546 = CARTESIAN_POINT('',(-0.5,-0.37,0.74));
#547 = VECTOR('',#548,1.);
#548 = DIRECTION('',(1.,0.,0.));
#549 = CYLINDRICAL_SURFACE('',#550,3.E-02);
#550 = AXIS2_PLACEMENT_3D('',#551,#552,#553);
#551 = CARTESIAN_POINT('',(-0.5,-0.34,0.74));
#552 = DIRECTION('',(1.,0.,0.));
#553 = DIRECTION('',(0.,-1.,0.));
#554 = ADVANCED_FACE('',(#555),#566,.F.);
#555 = FACE_BOUND('',#556,.F.);
#556 = EDGE_LOOP('',(#557,#563,#564,#565));
#557 = ORIENTED_EDGE('',*,*,#558,.F.);
#558 = EDGE_CURVE('',#389,#536,#559,.T.);
#559 = LINE('',#560,#561);
#560 = CARTESIAN_POINT('',(0.5,-0.37,3.E-02));
#561 = VECTOR('',#562,1.);
#562 = DIRECTION('',(0.,0.,1.));
#563 = ORIENTED_EDGE('',*,*,#397,.F.);
#564 = ORIENTED_EDGE('',*,*,#314,.T.);
#565 = ORIENTED_EDGE('',*,*,#544,.T.);
#566 = PLANE('',#567);
#567 = AXIS2_PLACEMENT_3D('',#568,#569,#570);
#568 = CARTESIAN_POINT('',(-0.5,-0.37,3.E-02));
#569 = DIRECTION('',(0.,1.,0.));
#570 = DIRECTION('',(0.,0.,1.));
#571 = ADVANCED_FACE('',(#572,#642),#652,.F.);
#572 = FACE_BOUND('',#573,.F.);
#573 = EDGE_LOOP('',(#574,#584,#593,#601,#610,#618,#627,#635));
#574 = ORIENTED_EDGE('',*,*,#575,.F.);
#575 = EDGE_CURVE('',#576,#578,#580,.T.);
#576 = VERTEX_POINT('',#577);
#577 = CARTESIAN_POINT('',(0.5,-0.4,3.E-02));
#578 = VERTEX_POINT('',#579);
#579 = CARTESIAN_POINT('',(0.5,-0.4,0.77));
#580 = LINE('',#581,#582);
#581 = CARTESIAN_POINT('',(0.5,-0.4,0.));
#582 = VECTOR('',#583,1.);
#583 = DIRECTION('',(0.,0.,1.));
#584 = ORIENTED_EDGE('',*,*,#585,.T.);
#585 = EDGE_CURVE('',#576,#586,#588,.T.);
#586 = VERTEX_POINT('',#587);
#587 = CARTESIAN_POINT('',(0.5,-0.37,0.));
#588 = CIRCLE('',#589,3.E-02);
#589 = AXIS2_PLACEMENT_3D('',#590,#591,#592);
#590 = CARTESIAN_POINT('',(0.5,-0.37,3.E-02));
#591 = DIRECTION('',(1.,0.,-0.));
#592 = DIRECTION('',(0.,0.,1.));
#593 = ORIENTED_EDGE('',*,*,#594,.T.);
#594 = EDGE_CURVE('',#586,#595,#597,.T.);
#595 = VERTEX_POINT('',#596);
#596 = CARTESIAN_POINT('',(0.5,0.37,0.));
#597 = LINE('',#598,#599);
#598 = CARTESIAN_POINT('',(0.5,-0.4,0.));
#599 = VECTOR('',#600,1.);
#600 = DIRECTION('',(0.,1.,0.));
#601 = ORIENTED_EDGE('',*,*,#602,.F.);
#602 = EDGE_CURVE('',#603,#595,#605,.T.);
#603 = VERTEX_POINT('',#604);
#604 = CARTESIAN_POINT('',(0.5,0.4,3.E-02));
#605 = CIRCLE('',#606,3.E-02);
#606 = AXIS2_PLACEMENT_3D('',#607,#608,#609);
#607 = CARTESIAN_POINT('',(0.5,0.37,3.E-02));
#608 = DIRECTION('',(-1.,0.,0.));
#609 = DIRECTION('',(0.,0.,1.));
#610 = ORIENTED_EDGE('',*,*,#611,.T.);
#611 = EDGE_CURVE('',#603,#612,#614,.T.);
#612 = VERTEX_POINT('',#613);
#613 = CARTESIAN_POINT('',(0.5,0.4,0.77));
#614 = LINE('',#615,#616);
#615 = CARTESIAN_POINT('',(0.5,0.4,0.));
#616 = VECTOR('',#617,1.);
#617 = DIRECTION('',(0.,0.,1.));
#618 = ORIENTED_EDGE('',*,*,#619,.T.);
#619 = EDGE_CURVE('',#612,#620,#622,.T.);
#620 = VERTEX_POINT('',#621);
#621 = CARTESIAN_POINT('',(0.5,0.37,0.8));
#622 = CIRCLE('',#623,3.E-02);
#623 = AXIS2_PLACEMENT_3D('',#624,#625,#626);
#624 = CARTESIAN_POINT('',(0.5,0.37,0.77));
#625 = DIRECTION('',(1.,0.,-0.));
#626 = DIRECTION('',(0.,0.,1.));
#627 = ORIENTED_EDGE('',*,*,#628,.F.);
#628 = EDGE_CURVE('',#629,#620,#631,.T.);
#629 = VERTEX_POINT('',#630);
#630 = CARTESIAN_POINT('',(0.5,-0.37,0.8));
#631 = LINE('',#632,#633);
#632 = CARTESIAN_POINT('',(0.5,-0.4,0.8));
#633 = VECTOR('',#634,1.);
#634 = DIRECTION('',(0.,1.,0.));
#635 = ORIENTED_EDGE('',*,*,#636,.F.);
#636 = EDGE_CURVE('',#578,#629,#637,.T.);
#637 = CIRCLE('',#638,3.E-02);
#638 = AXIS2_PLACEMENT_3D('',#639,#640,#641);
#639 = CARTESIAN_POINT('',(0.5,-0.37,0.77));
#640 = DIRECTION('',(-1.,0.,0.));
#641 = DIRECTION('',(0.,0.,1.));
#642 = FACE_BOUND('',#643,.F.);
#643 = EDGE_LOOP('',(#644,#645,#646,#647,#648,#649,#650,#651));
#644 = ORIENTED_EDGE('',*,*,#388,.F.);
#645 = ORIENTED_EDGE('',*,*,#558,.T.);
#646 = ORIENTED_EDGE('',*,*,#535,.T.);
#647 = ORIENTED_EDGE('',*,*,#518,.T.);
#648 = ORIENTED_EDGE('',*,*,#493,.F.);
#649 = ORIENTED_EDGE('',*,*,#460,.F.);
#650 = ORIENTED_EDGE('',*,*,#437,.T.);
#651 = ORIENTED_EDGE('',*,*,#413,.F.);
#652 = PLANE('',#653);
#653 = AXIS2_PLACEMENT_3D('',#654,#655,#656);
#654 = CARTESIAN_POINT('',(0.5,-0.4,0.));
#655 = DIRECTION('',(1.,0.,0.));
#656 = DIRECTION('',(0.,0.,1.));
#657 = ADVANCED_FACE('',(#658),#683,.F.);
#658 = FACE_BOUND('',#659,.F.);
#659 = EDGE_LOOP('',(#660,#670,#676,#677));
#660 = ORIENTED_EDGE('',*,*,#661,.F.);
#661 = EDGE_CURVE('',#662,#664,#666,.T.);
#662 = VERTEX_POINT('',#663);
#663 = CARTESIAN_POINT('',(0.8,-0.4,3.E-02));
#664 = VERTEX_POINT('',#665);
#665 = CARTESIAN_POINT('',(0.8,-0.4,0.77));
#666 = LINE('',#667,#668);
#667 = CARTESIAN_POINT('',(0.8,-0.4,0.));
#668 = VECTOR('',#669,1.);
#669 = DIRECTION('',(0.,0.,1.));
#670 = ORIENTED_EDGE('',*,*,#671,.F.);
#671 = EDGE_CURVE('',#576,#662,#672,.T.);
#672 = LINE('',#673,#674);
#673 = CARTESIAN_POINT('',(0.5,-0.4,3.E-02));
#674 = VECTOR('',#675,1.);
#675 = DIRECTION('',(1.,0.,0.));
#676 = ORIENTED_EDGE('',*,*,#575,.T.);
#677 = ORIENTED_EDGE('',*,*,#678,.T.);
#678 = EDGE_CURVE('',#578,#664,#679,.T.);
#679 = LINE('',#680,#681);
#680 = CARTESIAN_POINT('',(0.5,-0.4,0.77));
#681 = VECTOR('',#682,1.);
#682 = DIRECTION('',(1.,0.,0.));
#683 = PLANE('',#684);
#684 = AXIS2_PLACEMENT_3D('',#685,#686,#687);
#685 = CARTESIAN_POINT('',(0.5,-0.4,0.));
#686 = DIRECTION('',(0.,1.,0.));
#687 = DIRECTION('',(0.,0.,1.));
#688 = ADVANCED_FACE('',(#689),#708,.T.);
#689 = FACE_BOUND('',#690,.T.);
#690 = EDGE_LOOP('',(#691,#692,#700,#707));
#691 = ORIENTED_EDGE('',*,*,#585,.T.);
#692 = ORIENTED_EDGE('',*,*,#693,.T.);
#693 = EDGE_CURVE('',#586,#694,#696,.T.);
#694 = VERTEX_POINT('',#695);
#695 = CARTESIAN_POINT('',(0.8,-0.37,0.));
#696 = LINE('',#697,#698);
#697 = CARTESIAN_POINT('',(0.5,-0.37,0.));
#698 = VECTOR('',#699,1.);
#699 = DIRECTION('',(1.,0.,0.));
#700 = ORIENTED_EDGE('',*,*,#701,.F.);
#701 = EDGE_CURVE('',#662,#694,#702,.T.);
#702 = CIRCLE('',#703,3.E-02);
#703 = AXIS2_PLACEMENT_3D('',#704,#705,#706);
#704 = CARTESIAN_POINT('',(0.8,-0.37,3.E-02));
#705 = DIRECTION('',(1.,0.,-0.));
#706 = DIRECTION('',(0.,0.,1.));
#707 = ORIENTED_EDGE('',*,*,#671,.F.);
#708 = CYLINDRICAL_SURFACE('',#709,3.E-02);
#709 = AXIS2_PLACEMENT_3D('',#710,#711,#712);
#710 = CARTESIAN_POINT('',(0.5,-0.37,3.E-02));
#711 = DIRECTION('',(1.,0.,0.));
#712 = DIRECTION('',(0.,-1.,0.));
#713 = ADVANCED_FACE('',(#714),#733,.T.);
#714 = FACE_BOUND('',#715,.F.);
#715 = EDGE_LOOP('',(#716,#717,#725,#732));
#716 = ORIENTED_EDGE('',*,*,#636,.T.);
#717 = ORIENTED_EDGE('',*,*,#718,.T.);
#718 = EDGE_CURVE('',#629,#719,#721,.T.);
#719 = VERTEX_POINT('',#720);
#720 = CARTESIAN_POINT('',(0.8,-0.37,0.8));
#721 = LINE('',#722,#723);
#722 = CARTESIAN_POINT('',(0.5,-0.37,0.8));
#723 = VECTOR('',#724,1.);
#724 = DIRECTION('',(1.,0.,0.));
#725 = ORIENTED_EDGE('',*,*,#726,.F.);
#726 = EDGE_CURVE('',#664,#719,#727,.T.);
#727 = CIRCLE('',#728,3.E-02);
#728 = AXIS2_PLACEMENT_3D('',#729,#730,#731);
#729 = CARTESIAN_POINT('',(0.8,-0.37,0.77));
#730 = DIRECTION('',(-1.,0.,0.));
#731 = DIRECTION('',(0.,0.,1.));
#732 = ORIENTED_EDGE('',*,*,#678,.F.);
#733 = CYLINDRICAL_SURFACE('',#734,3.E-02);
#734 = AXIS2_PLACEMENT_3D('',#735,#736,#737);
#735 = CARTESIAN_POINT('',(0.5,-0.37,0.77));
#736 = DIRECTION('',(1.,0.,0.));
#737 = DIRECTION('',(0.,-1.,0.));
#738 = ADVANCED_FACE('',(#739),#757,.F.);
#739 = FACE_BOUND('',#740,.F.);
#740 = EDGE_LOOP('',(#741,#742,#743,#751));
#741 = ORIENTED_EDGE('',*,*,#594,.F.);
#742 = ORIENTED_EDGE('',*,*,#693,.T.);
#743 = ORIENTED_EDGE('',*,*,#744,.T.);
#744 = EDGE_CURVE('',#694,#745,#747,.T.);
#745 = VERTEX_POINT('',#746);
#746 = CARTESIAN_POINT('',(0.8,0.37,0.));
#747 = LINE('',#748,#749);
#748 = CARTESIAN_POINT('',(0.8,-0.4,0.));
#749 = VECTOR('',#750,1.);
#750 = DIRECTION('',(0.,1.,0.));
#751 = ORIENTED_EDGE('',*,*,#752,.F.);
#752 = EDGE_CURVE('',#595,#745,#753,.T.);
#753 = LINE('',#754,#755);
#754 = CARTESIAN_POINT('',(0.5,0.37,0.));
#755 = VECTOR('',#756,1.);
#756 = DIRECTION('',(1.,0.,0.));
#757 = PLANE('',#758);
#758 = AXIS2_PLACEMENT_3D('',#759,#760,#761);
#759 = CARTESIAN_POINT('',(0.5,-0.4,0.));
#760 = DIRECTION('',(0.,0.,1.));
#761 = DIRECTION('',(1.,0.,0.));
#762 = ADVANCED_FACE('',(#763),#781,.T.);
#763 = FACE_BOUND('',#764,.T.);
#764 = EDGE_LOOP('',(#765,#766,#767,#775));
#765 = ORIENTED_EDGE('',*,*,#628,.F.);
#766 = ORIENTED_EDGE('',*,*,#718,.T.);
#767 = ORIENTED_EDGE('',*,*,#768,.T.);
#768 = EDGE_CURVE('',#719,#769,#771,.T.);
#769 = VERTEX_POINT('',#770);
#770 = CARTESIAN_POINT('',(0.8,0.37,0.8));
#771 = LINE('',#772,#773);
#772 = CARTESIAN_POINT('',(0.8,-0.4,0.8));
#773 = VECTOR('',#774,1.);
#774 = DIRECTION('',(0.,1.,0.));
#775 = ORIENTED_EDGE('',*,*,#776,.F.);
#776 = EDGE_CURVE('',#620,#769,#777,.T.);
#777 = LINE('',#778,#779);
#778 = CARTESIAN_POINT('',(0.5,0.37,0.8));
#779 = VECTOR('',#780,1.);
#780 = DIRECTION('',(1.,0.,0.));
#781 = PLANE('',#782);
#782 = AXIS2_PLACEMENT_3D('',#783,#784,#785);
#783 = CARTESIAN_POINT('',(0.5,-0.4,0.8));
#784 = DIRECTION('',(0.,0.,1.));
#785 = DIRECTION('',(1.,0.,0.));
#786 = ADVANCED_FACE('',(#787),#806,.T.);
#787 = FACE_BOUND('',#788,.F.);
#788 = EDGE_LOOP('',(#789,#790,#791,#800));
#789 = ORIENTED_EDGE('',*,*,#602,.T.);
#790 = ORIENTED_EDGE('',*,*,#752,.T.);
#791 = ORIENTED_EDGE('',*,*,#792,.F.);
#792 = EDGE_CURVE('',#793,#745,#795,.T.);
#793 = VERTEX_POINT('',#794);
#794 = CARTESIAN_POINT('',(0.8,0.4,3.E-02));
#795 = CIRCLE('',#796,3.E-02);
#796 = AXIS2_PLACEMENT_3D('',#797,#798,#799);
#797 = CARTESIAN_POINT('',(0.8,0.37,3.E-02));
#798 = DIRECTION('',(-1.,0.,0.));
#799 = DIRECTION('',(0.,0.,1.));
#800 = ORIENTED_EDGE('',*,*,#801,.F.);
#801 = EDGE_CURVE('',#603,#793,#802,.T.);
#802 = LINE('',#803,#804);
#803 = CARTESIAN_POINT('',(0.5,0.4,3.E-02));
#804 = VECTOR('',#805,1.);
#805 = DIRECTION('',(1.,0.,0.));
#806 = CYLINDRICAL_SURFACE('',#807,3.E-02);
#807 = AXIS2_PLACEMENT_3D('',#808,#809,#810);
#808 = CARTESIAN_POINT('',(0.5,0.37,3.E-02));
#809 = DIRECTION('',(1.,0.,0.));
#810 = DIRECTION('',(0.,1.,0.));
#811 = ADVANCED_FACE('',(#812),#831,.T.);
#812 = FACE_BOUND('',#813,.T.);
#813 = EDGE_LOOP('',(#814,#815,#816,#825));
#814 = ORIENTED_EDGE('',*,*,#619,.T.);
#815 = ORIENTED_EDGE('',*,*,#776,.T.);
#816 = ORIENTED_EDGE('',*,*,#817,.F.);
#817 = EDGE_CURVE('',#818,#769,#820,.T.);
#818 = VERTEX_POINT('',#819);
#819 = CARTESIAN_POINT('',(0.8,0.4,0.77));
#820 = CIRCLE('',#821,3.E-02);
#821 = AXIS2_PLACEMENT_3D('',#822,#823,#824);
#822 = CARTESIAN_POINT('',(0.8,0.37,0.77));
#823 = DIRECTION('',(1.,0.,-0.));
#824 = DIRECTION('',(0.,0.,1.));
#825 = ORIENTED_EDGE('',*,*,#826,.F.);
#826 = EDGE_CURVE('',#612,#818,#827,.T.);
#827 = LINE('',#828,#829);
#828 = CARTESIAN_POINT('',(0.5,0.4,0.77));
#829 = VECTOR('',#830,1.);
#830 = DIRECTION('',(1.,0.,0.));
#831 = CYLINDRICAL_SURFACE('',#832,3.E-02);
#832 = AXIS2_PLACEMENT_3D('',#833,#834,#835);
#833 = CARTESIAN_POINT('',(0.5,0.37,0.77));
#834 = DIRECTION('',(1.,0.,0.));
#835 = DIRECTION('',(0.,1.,0.));
#836 = ADVANCED_FACE('',(#837),#848,.T.);
#837 = FACE_BOUND('',#838,.T.);
#838 = EDGE_LOOP('',(#839,#845,#846,#847));
#839 = ORIENTED_EDGE('',*,*,#840,.F.);
#840 = EDGE_CURVE('',#793,#818,#841,.T.);
#841 = LINE('',#842,#843);
#842 = CARTESIAN_POINT('',(0.8,0.4,0.));
#843 = VECTOR('',#844,1.);
#844 = DIRECTION('',(0.,0.,1.));
#845 = ORIENTED_EDGE('',*,*,#801,.F.);
#846 = ORIENTED_EDGE('',*,*,#611,.T.);
#847 = ORIENTED_EDGE('',*,*,#826,.T.);
#848 = PLANE('',#849);
#849 = AXIS2_PLACEMENT_3D('',#850,#851,#852);
#850 = CARTESIAN_POINT('',(0.5,0.4,0.));
#851 = DIRECTION('',(0.,1.,0.));
#852 = DIRECTION('',(0.,0.,1.));
#853 = ADVANCED_FACE('',(#854),#864,.T.);
#854 = FACE_BOUND('',#855,.T.);
#855 = EDGE_LOOP('',(#856,#857,#858,#859,#860,#861,#862,#863));
#856 = ORIENTED_EDGE('',*,*,#661,.F.);
#857 = ORIENTED_EDGE('',*,*,#701,.T.);
#858 = ORIENTED_EDGE('',*,*,#744,.T.);
#859 = ORIENTED_EDGE('',*,*,#792,.F.);
#860 = ORIENTED_EDGE('',*,*,#840,.T.);
#861 = ORIENTED_EDGE('',*,*,#817,.T.);
#862 = ORIENTED_EDGE('',*,*,#768,.F.);
#863 = ORIENTED_EDGE('',*,*,#726,.F.);
#864 = PLANE('',#865);
#865 = AXIS2_PLACEMENT_3D('',#866,#867,#868);
#866 = CARTESIAN_POINT('',(0.8,-0.4,0.));
#867 = DIRECTION('',(1.,0.,0.));
#868 = DIRECTION('',(0.,0.,1.));
#869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#873)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#870,#871,#872)) REPRESENTATION_CONTEXT('Context #1',
'3D Context with UNIT and UNCERTAINTY') );
#870 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#871 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#872 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#873 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#870,
'distance_accuracy_value','confusion accuracy');
#874 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#875 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#876,
#884,#891,#898,#905,#912,#919,#926,#933,#940,#947,#955,#962,#969,
#976,#983,#990,#997,#1004,#1011,#1018,#1025,#1032,#1039,#1046,#1053,
#1060,#1067),#869);
#876 = STYLED_ITEM('color',(#877),#17);
#877 = PRESENTATION_STYLE_ASSIGNMENT((#878));
#878 = SURFACE_STYLE_USAGE(.BOTH.,#879);
#879 = SURFACE_SIDE_STYLE('',(#880));
#880 = SURFACE_STYLE_FILL_AREA(#881);
#881 = FILL_AREA_STYLE('',(#882));
#882 = FILL_AREA_STYLE_COLOUR('',#883);
#883 = COLOUR_RGB('',0.824000000954,0.819999992847,0.78100001812);
#884 = STYLED_ITEM('color',(#885),#93);
#885 = PRESENTATION_STYLE_ASSIGNMENT((#886));
#886 = SURFACE_STYLE_USAGE(.BOTH.,#887);
#887 = SURFACE_SIDE_STYLE('',(#888));
#888 = SURFACE_STYLE_FILL_AREA(#889);
#889 = FILL_AREA_STYLE('',(#890));
#890 = FILL_AREA_STYLE_COLOUR('',#883);
#891 = STYLED_ITEM('color',(#892),#124);
#892 = PRESENTATION_STYLE_ASSIGNMENT((#893));
#893 = SURFACE_STYLE_USAGE(.BOTH.,#894);
#894 = SURFACE_SIDE_STYLE('',(#895));
#895 = SURFACE_STYLE_FILL_AREA(#896);
#896 = FILL_AREA_STYLE('',(#897));
#897 = FILL_AREA_STYLE_COLOUR('',#883);
#898 = STYLED_ITEM('color',(#899),#149);
#899 = PRESENTATION_STYLE_ASSIGNMENT((#900));
#900 = SURFACE_STYLE_USAGE(.BOTH.,#901);
#901 = SURFACE_SIDE_STYLE('',(#902));
#902 = SURFACE_STYLE_FILL_AREA(#903);
#903 = FILL_AREA_STYLE('',(#904));
#904 = FILL_AREA_STYLE_COLOUR('',#883);
#905 = STYLED_ITEM('color',(#906),#174);
#906 = PRESENTATION_STYLE_ASSIGNMENT((#907));
#907 = SURFACE_STYLE_USAGE(.BOTH.,#908);
#908 = SURFACE_SIDE_STYLE('',(#909));
#909 = SURFACE_STYLE_FILL_AREA(#910);
#910 = FILL_AREA_STYLE('',(#911));
#911 = FILL_AREA_STYLE_COLOUR('',#883);
#912 = STYLED_ITEM('color',(#913),#198);
#913 = PRESENTATION_STYLE_ASSIGNMENT((#914));
#914 = SURFACE_STYLE_USAGE(.BOTH.,#915);
#915 = SURFACE_SIDE_STYLE('',(#916));
#916 = SURFACE_STYLE_FILL_AREA(#917);
#917 = FILL_AREA_STYLE('',(#918));
#918 = FILL_AREA_STYLE_COLOUR('',#883);
#919 = STYLED_ITEM('color',(#920),#222);
#920 = PRESENTATION_STYLE_ASSIGNMENT((#921));
#921 = SURFACE_STYLE_USAGE(.BOTH.,#922);
#922 = SURFACE_SIDE_STYLE('',(#923));
#923 = SURFACE_STYLE_FILL_AREA(#924);
#924 = FILL_AREA_STYLE('',(#925));
#925 = FILL_AREA_STYLE_COLOUR('',#883);
#926 = STYLED_ITEM('color',(#927),#247);
#927 = PRESENTATION_STYLE_ASSIGNMENT((#928));
#928 = SURFACE_STYLE_USAGE(.BOTH.,#929);
#929 = SURFACE_SIDE_STYLE('',(#930));
#930 = SURFACE_STYLE_FILL_AREA(#931);
#931 = FILL_AREA_STYLE('',(#932));
#932 = FILL_AREA_STYLE_COLOUR('',#883);
#933 = STYLED_ITEM('color',(#934),#272);
#934 = PRESENTATION_STYLE_ASSIGNMENT((#935));
#935 = SURFACE_STYLE_USAGE(.BOTH.,#936);
#936 = SURFACE_SIDE_STYLE('',(#937));
#937 = SURFACE_STYLE_FILL_AREA(#938);
#938 = FILL_AREA_STYLE('',(#939));
#939 = FILL_AREA_STYLE_COLOUR('',#883);
#940 = STYLED_ITEM('color',(#941),#289);
#941 = PRESENTATION_STYLE_ASSIGNMENT((#942));
#942 = SURFACE_STYLE_USAGE(.BOTH.,#943);
#943 = SURFACE_SIDE_STYLE('',(#944));
#944 = SURFACE_STYLE_FILL_AREA(#945);
#945 = FILL_AREA_STYLE('',(#946));
#946 = FILL_AREA_STYLE_COLOUR('',#883);
#947 = STYLED_ITEM('color',(#948),#375);
#948 = PRESENTATION_STYLE_ASSIGNMENT((#949));
#949 = SURFACE_STYLE_USAGE(.BOTH.,#950);
#950 = SURFACE_SIDE_STYLE('',(#951));
#951 = SURFACE_STYLE_FILL_AREA(#952);
#952 = FILL_AREA_STYLE('',(#953));
#953 = FILL_AREA_STYLE_COLOUR('',#954);
#954 = COLOUR_RGB('',0.379000008106,0.270000010729,0.215000003576);
#955 = STYLED_ITEM('color',(#956),#407);
#956 = PRESENTATION_STYLE_ASSIGNMENT((#957));
#957 = SURFACE_STYLE_USAGE(.BOTH.,#958);
#958 = SURFACE_SIDE_STYLE('',(#959));
#959 = SURFACE_STYLE_FILL_AREA(#960);
#960 = FILL_AREA_STYLE('',(#961));
#961 = FILL_AREA_STYLE_COLOUR('',#954);
#962 = STYLED_ITEM('color',(#963),#431);
#963 = PRESENTATION_STYLE_ASSIGNMENT((#964));
#964 = SURFACE_STYLE_USAGE(.BOTH.,#965);
#965 = SURFACE_SIDE_STYLE('',(#966));
#966 = SURFACE_STYLE_FILL_AREA(#967);
#967 = FILL_AREA_STYLE('',(#968));
#968 = FILL_AREA_STYLE_COLOUR('',#954);
#969 = STYLED_ITEM('color',(#970),#456);
#970 = PRESENTATION_STYLE_ASSIGNMENT((#971));
#971 = SURFACE_STYLE_USAGE(.BOTH.,#972);
#972 = SURFACE_SIDE_STYLE('',(#973));
#973 = SURFACE_STYLE_FILL_AREA(#974);
#974 = FILL_AREA_STYLE('',(#975));
#975 = FILL_AREA_STYLE_COLOUR('',#954);
#976 = STYLED_ITEM('color',(#977),#480);
#977 = PRESENTATION_STYLE_ASSIGNMENT((#978));
#978 = SURFACE_STYLE_USAGE(.BOTH.,#979);
#979 = SURFACE_SIDE_STYLE('',(#980));
#980 = SURFACE_STYLE_FILL_AREA(#981);
#981 = FILL_AREA_STYLE('',(#982));
#982 = FILL_AREA_STYLE_COLOUR('',#954);
#983 = STYLED_ITEM('color',(#984),#505);
#984 = PRESENTATION_STYLE_ASSIGNMENT((#985));
#985 = SURFACE_STYLE_USAGE(.BOTH.,#986);
#986 = SURFACE_SIDE_STYLE('',(#987));
#987 = SURFACE_STYLE_FILL_AREA(#988);
#988 = FILL_AREA_STYLE('',(#989));
#989 = FILL_AREA_STYLE_COLOUR('',#954);
#990 = STYLED_ITEM('color',(#991),#529);
#991 = PRESENTATION_STYLE_ASSIGNMENT((#992));
#992 = SURFACE_STYLE_USAGE(.BOTH.,#993);
#993 = SURFACE_SIDE_STYLE('',(#994));
#994 = SURFACE_STYLE_FILL_AREA(#995);
#995 = FILL_AREA_STYLE('',(#996));
#996 = FILL_AREA_STYLE_COLOUR('',#954);
#997 = STYLED_ITEM('color',(#998),#554);
#998 = PRESENTATION_STYLE_ASSIGNMENT((#999));
#999 = SURFACE_STYLE_USAGE(.BOTH.,#1000);
#1000 = SURFACE_SIDE_STYLE('',(#1001));
#1001 = SURFACE_STYLE_FILL_AREA(#1002);
#1002 = FILL_AREA_STYLE('',(#1003));
#1003 = FILL_AREA_STYLE_COLOUR('',#954);
#1004 = STYLED_ITEM('color',(#1005),#571);
#1005 = PRESENTATION_STYLE_ASSIGNMENT((#1006));
#1006 = SURFACE_STYLE_USAGE(.BOTH.,#1007);
#1007 = SURFACE_SIDE_STYLE('',(#1008));
#1008 = SURFACE_STYLE_FILL_AREA(#1009);
#1009 = FILL_AREA_STYLE('',(#1010));
#1010 = FILL_AREA_STYLE_COLOUR('',#883);
#1011 = STYLED_ITEM('color',(#1012),#657);
#1012 = PRESENTATION_STYLE_ASSIGNMENT((#1013));
#1013 = SURFACE_STYLE_USAGE(.BOTH.,#1014);
#1014 = SURFACE_SIDE_STYLE('',(#1015));
#1015 = SURFACE_STYLE_FILL_AREA(#1016);
#1016 = FILL_AREA_STYLE('',(#1017));
#1017 = FILL_AREA_STYLE_COLOUR('',#883);
#1018 = STYLED_ITEM('color',(#1019),#688);
#1019 = PRESENTATION_STYLE_ASSIGNMENT((#1020));
#1020 = SURFACE_STYLE_USAGE(.BOTH.,#1021);
#1021 = SURFACE_SIDE_STYLE('',(#1022));
#1022 = SURFACE_STYLE_FILL_AREA(#1023);
#1023 = FILL_AREA_STYLE('',(#1024));
#1024 = FILL_AREA_STYLE_COLOUR('',#883);
#1025 = STYLED_ITEM('color',(#1026),#713);
#1026 = PRESENTATION_STYLE_ASSIGNMENT((#1027));
#1027 = SURFACE_STYLE_USAGE(.BOTH.,#1028);
#1028 = SURFACE_SIDE_STYLE('',(#1029));
#1029 = SURFACE_STYLE_FILL_AREA(#1030);
#1030 = FILL_AREA_STYLE('',(#1031));
#1031 = FILL_AREA_STYLE_COLOUR('',#883);
#1032 = STYLED_ITEM('color',(#1033),#738);
#1033 = PRESENTATION_STYLE_ASSIGNMENT((#1034));
#1034 = SURFACE_STYLE_USAGE(.BOTH.,#1035);
#1035 = SURFACE_SIDE_STYLE('',(#1036));
#1036 = SURFACE_STYLE_FILL_AREA(#1037);
#1037 = FILL_AREA_STYLE('',(#1038));
#1038 = FILL_AREA_STYLE_COLOUR('',#883);
#1039 = STYLED_ITEM('color',(#1040),#762);
#1040 = PRESENTATION_STYLE_ASSIGNMENT((#1041));
#1041 = SURFACE_STYLE_USAGE(.BOTH.,#1042);
#1042 = SURFACE_SIDE_STYLE('',(#1043));
#1043 = SURFACE_STYLE_FILL_AREA(#1044);
#1044 = FILL_AREA_STYLE('',(#1045));
#1045 = FILL_AREA_STYLE_COLOUR('',#883);
#1046 = STYLED_ITEM('color',(#1047),#786);
#1047 = PRESENTATION_STYLE_ASSIGNMENT((#1048));
#1048 = SURFACE_STYLE_USAGE(.BOTH.,#1049);
#1049 = SURFACE_SIDE_STYLE('',(#1050));
#1050 = SURFACE_STYLE_FILL_AREA(#1051);
#1051 = FILL_AREA_STYLE('',(#1052));
#1052 = FILL_AREA_STYLE_COLOUR('',#883);
#1053 = STYLED_ITEM('color',(#1054),#811);
#1054 = PRESENTATION_STYLE_ASSIGNMENT((#1055));
#1055 = SURFACE_STYLE_USAGE(.BOTH.,#1056);
#1056 = SURFACE_SIDE_STYLE('',(#1057));
#1057 = SURFACE_STYLE_FILL_AREA(#1058);
#1058 = FILL_AREA_STYLE('',(#1059));
#1059 = FILL_AREA_STYLE_COLOUR('',#883);
#1060 = STYLED_ITEM('color',(#1061),#836);
#1061 = PRESENTATION_STYLE_ASSIGNMENT((#1062));
#1062 = SURFACE_STYLE_USAGE(.BOTH.,#1063);
#1063 = SURFACE_SIDE_STYLE('',(#1064));
#1064 = SURFACE_STYLE_FILL_AREA(#1065);
#1065 = FILL_AREA_STYLE('',(#1066));
#1066 = FILL_AREA_STYLE_COLOUR('',#883);
#1067 = STYLED_ITEM('color',(#1068),#853);
#1068 = PRESENTATION_STYLE_ASSIGNMENT((#1069));
#1069 = SURFACE_STYLE_USAGE(.BOTH.,#1070);
#1070 = SURFACE_SIDE_STYLE('',(#1071));
#1071 = SURFACE_STYLE_FILL_AREA(#1072);
#1072 = FILL_AREA_STYLE('',(#1073));
#1073 = FILL_AREA_STYLE_COLOUR('',#883);
ENDSEC;
END-ISO-10303-21;
