#!/usr/bin/env python3

import requests
from bs4 import BeautifulSoup
import os
import sys

def test_basic_functionality():
    """Test basic functionality"""
    print("🔍 Testing basic functionality...")
    
    # Test 1: Check if we can make HTTP requests
    try:
        response = requests.get("https://httpbin.org/get", timeout=10)
        print(f"✅ HTTP request test: {response.status_code}")
    except Exception as e:
        print(f"❌ HTTP request failed: {e}")
        return False
    
    # Test 2: Check if we can parse HTML
    try:
        html = "<html><body><a href='test.pdf'>Datasheet</a></body></html>"
        soup = BeautifulSoup(html, 'html.parser')
        links = soup.find_all('a')
        print(f"✅ HTML parsing test: Found {len(links)} links")
    except Exception as e:
        print(f"❌ HTML parsing failed: {e}")
        return False
    
    # Test 3: Check if we can create directories
    try:
        test_dir = "files-download"
        os.makedirs(test_dir, exist_ok=True)
        print(f"✅ Directory creation test: {test_dir}")
    except Exception as e:
        print(f"❌ Directory creation failed: {e}")
        return False
    
    return True

def test_diodes_search():
    """Test searching Diodes Inc website"""
    print("\n🔍 Testing Diodes Inc search...")
    
    part_number = "APX803L20-30SA-7"
    base_part = part_number.split('-')[0]  # APX803L20
    
    url = f"https://www.diodes.com/part/view/{base_part}"
    print(f"🌐 Trying URL: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ Successfully fetched page ({len(response.text)} characters)")
            
            # Look for package information
            html_lower = response.text.lower()
            packages = ['sot-23', 'sot23', 'sc-59', 'sc59', 'sot-323', 'sot323']
            found_packages = [pkg for pkg in packages if pkg in html_lower]
            
            if found_packages:
                print(f"📦 Found packages: {found_packages}")
            else:
                print("⚠️ No package information found")
            
            # Look for PDF links
            import re
            pdf_pattern = r'href="([^"]*\.pdf[^"]*)"'
            pdf_matches = re.findall(pdf_pattern, response.text, re.IGNORECASE)
            
            if pdf_matches:
                print(f"📄 Found {len(pdf_matches)} PDF links:")
                for i, link in enumerate(pdf_matches[:3]):  # Show first 3
                    print(f"  {i+1}. {link}")
            else:
                print("⚠️ No PDF links found")
            
            return True
            
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Component Search Test Suite")
    print("=" * 50)
    
    # Run basic tests
    if not test_basic_functionality():
        print("❌ Basic functionality tests failed")
        sys.exit(1)
    
    # Run Diodes search test
    if not test_diodes_search():
        print("❌ Diodes search test failed")
        sys.exit(1)
    
    print("\n🎉 All tests passed!")
