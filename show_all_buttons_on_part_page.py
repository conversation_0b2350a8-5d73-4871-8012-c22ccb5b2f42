#!/usr/bin/env python3
"""
Show ALL buttons and links on the part details page
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def show_all_elements():
    print("🔍 SHOWING ALL BUTTONS AND LINKS ON PART PAGE")
    print("=" * 60)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick navigation to part page
        print("🔸 Navigating to part page...")
        
        # Homepage
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(3)
        
        # LOGIN
        login_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='Login']")
        if login_links:
            try:
                login_links[0].click()
            except:
                driver.execute_script("arguments[0].click();", login_links[0])
            time.sleep(3)
        
        # Fill login
        with open('component_site_credentials.json', 'r') as f:
            credentials = json.load(f)
        
        email_inputs = driver.find_elements(By.XPATH, "//input[contains(@placeholder, 'Email')]")
        if not email_inputs:
            email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[name='Username']")
        
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        if email_inputs and password_inputs:
            email_inputs[0].send_keys(credentials['UltraLibrarian']['email'])
            password_inputs[0].send_keys(credentials['UltraLibrarian']['password'])
            
            login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')]")
            if login_buttons:
                login_buttons[0].click()
                time.sleep(8)
        
        # Search
        search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i], input[type='text'], input[type='search']")
        search_input = None
        for inp in search_inputs:
            if inp.is_displayed() and inp.is_enabled():
                search_input = inp
                break
        
        if search_input:
            search_input.send_keys("LM358N")
            search_input.send_keys(Keys.RETURN)
            time.sleep(5)
        
        # Click part
        ti_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'LM358N')]")
        if ti_links:
            ti_links[0].click()
            time.sleep(8)
        
        print(f"✅ On part page: {driver.current_url}")
        
        # NOW SHOW ALL ELEMENTS
        print("\n" + "=" * 60)
        print("📋 ALL BUTTONS ON PAGE:")
        print("=" * 60)
        
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        for i, button in enumerate(all_buttons, 1):
            try:
                if button.is_displayed():
                    text = button.text.strip()
                    classes = button.get_attribute('class') or 'no-class'
                    id_attr = button.get_attribute('id') or 'no-id'
                    onclick = button.get_attribute('onclick') or 'no-onclick'
                    
                    print(f"{i:2d}. TEXT: '{text}'")
                    print(f"    CLASS: '{classes}'")
                    print(f"    ID: '{id_attr}'")
                    print(f"    ONCLICK: '{onclick[:60]}'")
                    print(f"    VISIBLE: {button.is_displayed()} | ENABLED: {button.is_enabled()}")
                    print("-" * 40)
            except Exception as e:
                print(f"{i:2d}. ERROR reading button: {e}")
        
        print("\n" + "=" * 60)
        print("📋 ALL LINKS ON PAGE:")
        print("=" * 60)
        
        all_links = driver.find_elements(By.TAG_NAME, "a")
        for i, link in enumerate(all_links, 1):
            try:
                if link.is_displayed():
                    text = link.text.strip()
                    href = link.get_attribute('href') or 'no-href'
                    classes = link.get_attribute('class') or 'no-class'
                    
                    if text or 'download' in href.lower() or 'download' in classes.lower():
                        print(f"{i:2d}. TEXT: '{text}'")
                        print(f"    HREF: '{href[:80]}'")
                        print(f"    CLASS: '{classes}'")
                        print(f"    VISIBLE: {link.is_displayed()} | ENABLED: {link.is_enabled()}")
                        print("-" * 40)
            except Exception as e:
                print(f"{i:2d}. ERROR reading link: {e}")
        
        print("\n" + "=" * 60)
        print("📋 ALL INPUT ELEMENTS:")
        print("=" * 60)
        
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        for i, inp in enumerate(all_inputs, 1):
            try:
                if inp.is_displayed():
                    input_type = inp.get_attribute('type') or 'text'
                    value = inp.get_attribute('value') or 'no-value'
                    classes = inp.get_attribute('class') or 'no-class'
                    
                    if 'download' in value.lower() or 'download' in classes.lower() or input_type in ['submit', 'button']:
                        print(f"{i:2d}. TYPE: '{input_type}'")
                        print(f"    VALUE: '{value}'")
                        print(f"    CLASS: '{classes}'")
                        print(f"    VISIBLE: {inp.is_displayed()} | ENABLED: {inp.is_enabled()}")
                        print("-" * 40)
            except Exception as e:
                print(f"{i:2d}. ERROR reading input: {e}")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n⏸️ Browser staying open - check the page manually...")
        input("Press Enter to close: ")
        driver.quit()

if __name__ == "__main__":
    show_all_elements()
