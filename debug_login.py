#!/usr/bin/env python3
"""
DEBUG LOGIN
===========
Debug the login process and show what elements are found.
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def debug_login():
    print("🎯 DEBUG LOGIN PROCESS")
    print("=" * 40)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick run to login screen
        print("🔸 Running to login screen...")
        
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        # Search
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        # Click TI part
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    link.click()
                    break
            except:
                continue
        time.sleep(8)
        
        # Click Download Now
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            driver.execute_script("arguments[0].click();", download_btns[0])
            time.sleep(5)
        
        # Click 3D CAD Model
        cad_btns = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
        if cad_btns:
            driver.execute_script("arguments[0].click();", cad_btns[0])
            time.sleep(5)
        
        # Check for new window
        if len(driver.window_handles) > 1:
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        # Try to select STEP (simplified)
        step_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP')]")
        if step_elements:
            for elem in step_elements:
                if elem.is_displayed():
                    try:
                        driver.execute_script("arguments[0].click();", elem)
                        print("✅ Clicked STEP element")
                        time.sleep(3)
                        break
                    except:
                        continue
        
        # Try to click download
        download_elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')]")
        if download_elements:
            for elem in download_elements:
                if elem.is_displayed():
                    try:
                        driver.execute_script("arguments[0].click();", elem)
                        print("✅ Clicked download element")
                        time.sleep(5)
                        break
                    except:
                        continue
        
        print("✅ Reached login screen")
        print(f"Current URL: {driver.current_url}")
        
        # DEBUG: Show all input elements
        print("\n🔍 ALL INPUT ELEMENTS:")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute('type') or 'text'
                name = inp.get_attribute('name') or ''
                placeholder = inp.get_attribute('placeholder') or ''
                id_attr = inp.get_attribute('id') or ''
                visible = inp.is_displayed()
                enabled = inp.is_enabled()
                
                print(f"  Input {i}: type='{input_type}', name='{name}', placeholder='{placeholder}', id='{id_attr}' (visible={visible}, enabled={enabled})")
            except Exception as e:
                print(f"  Input {i}: Error - {e}")
        
        # DEBUG: Show all buttons
        print("\n🔍 ALL BUTTONS:")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                btn_type = btn.get_attribute('type') or ''
                visible = btn.is_displayed()
                enabled = btn.is_enabled()
                
                if visible:
                    print(f"  Button {i}: '{text}' type='{btn_type}' (enabled={enabled})")
            except Exception as e:
                print(f"  Button {i}: Error - {e}")
        
        # Try to find email input with multiple methods
        print("\n🔍 LOOKING FOR EMAIL INPUT:")
        
        email_selectors = [
            "input[type='email']",
            "input[name*='email']",
            "input[placeholder*='email']",
            "input[id*='email']",
            "input[name*='Email']",
            "input[placeholder*='Email']"
        ]
        
        email_input = None
        for selector in email_selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for elem in elements:
                if elem.is_displayed() and elem.is_enabled():
                    email_input = elem
                    print(f"✅ Found email input with selector: {selector}")
                    break
            if email_input:
                break
        
        if not email_input:
            print("❌ No email input found with standard selectors")
            # Try any visible text input
            text_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='text']")
            for inp in text_inputs:
                if inp.is_displayed() and inp.is_enabled():
                    email_input = inp
                    print("✅ Using first visible text input as email field")
                    break
        
        # Try to find password input
        print("\n🔍 LOOKING FOR PASSWORD INPUT:")
        password_input = None
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        for inp in password_inputs:
            if inp.is_displayed() and inp.is_enabled():
                password_input = inp
                print("✅ Found password input")
                break
        
        if not password_input:
            print("❌ No password input found")
        
        # Load credentials and attempt login
        print("\n🔍 ATTEMPTING LOGIN:")
        try:
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            
            print(f"Email: {email}")
            print(f"Password: {'*' * len(password)}")
            
            if email_input:
                email_input.clear()
                email_input.send_keys(email)
                print("✅ Entered email")
                time.sleep(2)
            else:
                print("❌ Cannot enter email - no input found")
            
            if password_input:
                password_input.clear()
                password_input.send_keys(password)
                print("✅ Entered password")
                time.sleep(2)
            else:
                print("❌ Cannot enter password - no input found")
            
            # Try to submit
            if email_input and password_input:
                # Method 1: Look for submit button
                submit_buttons = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
                if submit_buttons:
                    for btn in submit_buttons:
                        if btn.is_displayed() and btn.is_enabled():
                            driver.execute_script("arguments[0].click();", btn)
                            print("✅ Clicked submit button")
                            break
                else:
                    # Method 2: Press Enter on password field
                    password_input.send_keys(Keys.RETURN)
                    print("✅ Pressed Enter on password field")
                
                time.sleep(10)
                print(f"After login URL: {driver.current_url}")
            
        except Exception as e:
            print(f"❌ Login error: {e}")
        
        print("\n🔍 LOGIN DEBUG COMPLETE")
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_login()
