#!/usr/bin/env python3
"""
DEBUG ULTRALIBRARIAN AUTOMATION
===============================
Debug version that shows exactly what's happening at each step.
"""

import os
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from webdriver_manager.chrome import ChromeDriverManager

class DebugUltraLibrarian:
    def __init__(self):
        self.base_url = 'https://www.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("🔍 DEBUG ULTRALIBRARIAN AUTOMATION", flush=True)

    def setup_driver(self):
        """Setup Chrome driver"""
        print("Setting up Chrome driver...", flush=True)
        
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            print("✅ Chrome driver ready", flush=True)
            return driver
        except Exception as e:
            print(f"❌ Chrome setup failed: {e}", flush=True)
            return None

    def debug_screen_1(self, driver, manufacturer, part_number):
        """DEBUG Screen 1: Search"""
        print(f"\n🔍 DEBUG SCREEN 1: Search", flush=True)
        
        try:
            print("   Loading homepage...", flush=True)
            driver.get(self.base_url)
            time.sleep(5)
            
            print(f"   Current URL: {driver.current_url}", flush=True)
            print(f"   Page title: {driver.title}", flush=True)
            
            # Look for ALL input elements
            print("   Finding ALL input elements...", flush=True)
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"   Found {len(all_inputs)} input elements:", flush=True)
            
            for i, inp in enumerate(all_inputs):
                try:
                    input_type = inp.get_attribute('type') or 'no-type'
                    input_name = inp.get_attribute('name') or 'no-name'
                    input_id = inp.get_attribute('id') or 'no-id'
                    input_class = inp.get_attribute('class') or 'no-class'
                    input_placeholder = inp.get_attribute('placeholder') or 'no-placeholder'
                    is_visible = inp.is_displayed()
                    
                    print(f"      {i+1}. Type:{input_type} Name:{input_name} ID:{input_id} Visible:{is_visible}", flush=True)
                    print(f"          Class:{input_class[:50]}...", flush=True)
                    print(f"          Placeholder:{input_placeholder}", flush=True)
                    print("", flush=True)
                except Exception as e:
                    print(f"      {i+1}. Error reading input: {e}", flush=True)
            
            # Try to find the search box
            search_input = None
            for inp in all_inputs:
                try:
                    if inp.is_displayed():
                        input_type = inp.get_attribute('type') or ''
                        input_placeholder = inp.get_attribute('placeholder') or ''
                        
                        if ('search' in input_type.lower() or 
                            'search' in input_placeholder.lower() or
                            input_type.lower() == 'text'):
                            search_input = inp
                            print(f"   ✅ Selected search input: type={input_type}, placeholder={input_placeholder}", flush=True)
                            break
                except:
                    continue
            
            if not search_input:
                print("   ❌ No suitable search input found", flush=True)
                print("   MANUAL INSPECTION NEEDED", flush=True)
                input("   Press Enter to continue for manual inspection...")
                return False
            
            # Enter search term
            search_term = f"{manufacturer} {part_number}"
            print(f"   Entering search term: '{search_term}'", flush=True)
            
            search_input.clear()
            search_input.send_keys(search_term)
            
            # Submit search
            print("   Submitting search (pressing Enter)...", flush=True)
            search_input.send_keys(Keys.RETURN)
            
            print("   Waiting 10 seconds for results...", flush=True)
            time.sleep(10)
            
            new_url = driver.current_url
            new_title = driver.title
            print(f"   New URL: {new_url}", flush=True)
            print(f"   New title: {new_title}", flush=True)
            
            return True
            
        except Exception as e:
            print(f"   ❌ Screen 1 debug failed: {e}", flush=True)
            return False

    def debug_screen_2(self, driver, part_number):
        """DEBUG Screen 2: Look for results"""
        print(f"\n🔍 DEBUG SCREEN 2: Look for results", flush=True)
        
        try:
            # Look for ALL links
            print("   Finding ALL links on page...", flush=True)
            all_links = driver.find_elements(By.TAG_NAME, "a")
            print(f"   Found {len(all_links)} total links", flush=True)
            
            # Filter for relevant links
            relevant_links = []
            for i, link in enumerate(all_links):
                try:
                    href = link.get_attribute('href') or ''
                    text = link.text.strip()
                    
                    if text and len(text) > 2:
                        # Check if link might be relevant
                        is_relevant = (
                            part_number.lower() in text.lower() or
                            'lm358' in text.lower() or
                            'part' in href.lower() or
                            'component' in href.lower()
                        )
                        
                        if is_relevant:
                            relevant_links.append({
                                'index': i+1,
                                'text': text,
                                'href': href,
                                'element': link
                            })
                except:
                    continue
            
            print(f"   Found {len(relevant_links)} relevant links:", flush=True)
            for link in relevant_links[:10]:  # Show first 10
                print(f"      {link['index']}. {link['text'][:60]}...", flush=True)
                print(f"          URL: {link['href'][:80]}...", flush=True)
                print("", flush=True)
            
            if not relevant_links:
                print("   ❌ No relevant links found", flush=True)
                print("   MANUAL INSPECTION NEEDED", flush=True)
                input("   Press Enter to continue for manual inspection...")
                return False
            
            # Try to click the most relevant link
            best_link = relevant_links[0]
            print(f"   Clicking best link: {best_link['text'][:50]}...", flush=True)
            
            driver.execute_script("arguments[0].scrollIntoView(true);", best_link['element'])
            time.sleep(2)
            best_link['element'].click()
            time.sleep(5)
            
            new_url = driver.current_url
            new_title = driver.title
            print(f"   New URL: {new_url}", flush=True)
            print(f"   New title: {new_title}", flush=True)
            
            return True
            
        except Exception as e:
            print(f"   ❌ Screen 2 debug failed: {e}", flush=True)
            return False

    def debug_current_page(self, driver):
        """Debug what's on the current page"""
        print(f"\n🔍 DEBUG CURRENT PAGE", flush=True)
        
        try:
            print(f"   URL: {driver.current_url}", flush=True)
            print(f"   Title: {driver.title}", flush=True)
            
            # Look for buttons
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print(f"   Found {len(buttons)} buttons:", flush=True)
            for i, btn in enumerate(buttons[:10]):
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"      {i+1}. {text}", flush=True)
                except:
                    continue
            
            # Look for links with download-related text
            links = driver.find_elements(By.TAG_NAME, "a")
            download_links = []
            for link in links:
                try:
                    text = link.text.strip().lower()
                    if any(word in text for word in ['download', '3d', 'model', 'step', 'cad']):
                        download_links.append(link.text.strip())
                except:
                    continue
            
            if download_links:
                print(f"   Found {len(download_links)} download-related links:", flush=True)
                for i, text in enumerate(download_links[:10]):
                    print(f"      {i+1}. {text}", flush=True)
            else:
                print("   No download-related links found", flush=True)
            
        except Exception as e:
            print(f"   ❌ Page debug failed: {e}", flush=True)

    def run_debug(self, manufacturer, part_number):
        """Run debug automation"""
        print(f"\n🔍 RUNNING DEBUG AUTOMATION", flush=True)
        print(f"Target: {manufacturer} {part_number}", flush=True)
        print("=" * 50, flush=True)
        
        driver = self.setup_driver()
        if not driver:
            return False
        
        try:
            # Debug Screen 1
            if not self.debug_screen_1(driver, manufacturer, part_number):
                print("\n❌ Screen 1 failed - keeping browser open for inspection", flush=True)
                input("Press Enter to continue...")
            
            # Debug current page
            self.debug_current_page(driver)
            
            # Debug Screen 2
            if not self.debug_screen_2(driver, part_number):
                print("\n❌ Screen 2 failed - keeping browser open for inspection", flush=True)
                input("Press Enter to continue...")
            
            # Debug current page again
            self.debug_current_page(driver)
            
            print(f"\n🔍 DEBUG COMPLETE", flush=True)
            print(f"   Browser is open for manual inspection", flush=True)
            print(f"   Look for the next steps in the process", flush=True)
            print(f"   Tell me what you see!", flush=True)
            
            input("Press Enter to close browser: ")
            
            return True
            
        except Exception as e:
            print(f"❌ Debug failed: {e}", flush=True)
            return False
        finally:
            driver.quit()

def main():
    if len(sys.argv) < 3:
        print("Usage: python debug_ultralibrarian.py \"Manufacturer\" \"Part Number\"")
        print("\nExample: python debug_ultralibrarian.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = sys.argv[1]
    part_number = sys.argv[2]
    
    debug = DebugUltraLibrarian()
    debug.run_debug(manufacturer, part_number)

if __name__ == "__main__":
    main()
