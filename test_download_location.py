#!/usr/bin/env python3
"""
Test exactly where Chrome downloads files when we set the download directory
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

def test_download_location():
    print("🧪 TESTING CHROME DOWNLOAD LOCATION")
    print("=" * 50)
    
    # Create 3d folder
    local_3d_dir = os.path.abspath('3d')
    os.makedirs(local_3d_dir, exist_ok=True)
    
    print(f"📁 Target download directory: {local_3d_dir}")
    
    # Setup Chrome with download preferences
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    # Download preferences - EXACTLY like in our main script
    prefs = {
        "download.default_directory": local_3d_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    print(f"⚙️ Chrome download setting: {prefs['download.default_directory']}")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Go to a site with a simple download link
        print("🌐 Going to a test download site...")
        driver.get("https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf")
        time.sleep(5)
        
        print("⏳ Download should start automatically...")
        time.sleep(10)
        
        # Check where the file actually went
        print("\n🔍 CHECKING DOWNLOAD LOCATIONS:")
        
        # Check 3d folder
        if os.path.exists(local_3d_dir):
            files_3d = os.listdir(local_3d_dir)
            print(f"📁 3d folder ({local_3d_dir}):")
            if files_3d:
                for f in files_3d:
                    print(f"   ✅ {f}")
            else:
                print("   ❌ Empty")
        
        # Check Downloads folder
        downloads_dir = os.path.expanduser("~/Downloads")
        if os.path.exists(downloads_dir):
            files_downloads = [f for f in os.listdir(downloads_dir) if 'dummy' in f.lower()]
            print(f"📁 Downloads folder ({downloads_dir}):")
            if files_downloads:
                for f in files_downloads:
                    print(f"   ✅ {f}")
            else:
                print("   ❌ No dummy files")
        
        # Check Chrome's actual download directory
        print("\n🔍 CHECKING CHROME'S ACTUAL DOWNLOAD SETTING:")
        try:
            # Navigate to Chrome downloads page
            driver.get("chrome://downloads/")
            time.sleep(3)
            print("   📄 Opened chrome://downloads/ page")
            
            # Try to find download location info
            # This is tricky because chrome://downloads/ uses shadow DOM
            print("   ⚠️ Chrome downloads page opened (check manually)")
            
        except Exception as e:
            print(f"   ❌ Could not check Chrome downloads: {e}")
        
        print(f"\n⏸️ MANUAL CHECK:")
        print(f"1. Look at the Chrome browser window")
        print(f"2. Check if you see the PDF downloaded")
        print(f"3. Check chrome://settings/downloads for download location")
        
        input("Press Enter when done checking...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_download_location()
