#!/usr/bin/env python3
"""
Modular Digikey searcher for datasheets and manufacturer info
Can be used by main program or run standalone
"""

import requests
from bs4 import BeautifulSoup
import json
import os
import sys

class DigikeySearcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.last_request_time = 0
        self.results = {}
    
    def search_part(self, part_number, manufacturer=None):
        """Search for a part on Digikey"""
        print(f"🔍 SEARCHING DIGIKEY FOR: {part_number}")
        if manufacturer:
            print(f"   Manufacturer: {manufacturer}")
        
        try:
            # Add delay to prevent rate limiting
            import time
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            if time_since_last < 10:  # Wait at least 10 seconds between requests
                wait_time = 10 - time_since_last
                print(f"   ⏳ Waiting {wait_time:.1f} seconds to avoid rate limiting...")
                time.sleep(wait_time)

            # Use Digikey's search URL with manufacturer if provided
            if manufacturer:
                search_url = f"https://www.digikey.com/en/products/result?keywords={part_number}+{manufacturer.replace(' ', '+')}"
                print(f"   Searching with manufacturer: {manufacturer}")
            else:
                search_url = f"https://www.digikey.com/en/products/result?keywords={part_number}"

            self.last_request_time = time.time()
            response = self.session.get(search_url, timeout=30)
            print(f"   Status: {response.status_code}")

            if response.status_code == 200:
                # Save search results
                filename = f"digikey_search_{part_number.replace('/', '_')}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)

                # Check if part found
                if part_number in response.text:
                    print(f"   ✅ FOUND: {part_number}")
                    return self.extract_part_info(response.text, part_number, manufacturer)
                else:
                    print(f"   ❌ NOT FOUND: {part_number}")
                    return None
            elif response.status_code == 429:
                print(f"   ⚠️  Rate limited, trying fallback approach...")
                return self.try_fallback_search(part_number, manufacturer)
            else:
                print(f"   ❌ SEARCH FAILED: {response.status_code}")
                return self.try_fallback_search(part_number, manufacturer)
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            return None
    
    def extract_part_info(self, html_content, part_number, target_manufacturer=None):
        """Extract datasheet URL and manufacturer info from Digikey page"""
        try:
            # First check if this is a category page with exact matches
            if '"exactMatch":' in html_content:
                return self.handle_exact_matches(html_content, part_number, target_manufacturer)

            # Look for JSON data in the HTML for direct part pages
            if '"datasheetUrl":' in html_content:
                # Find the JSON data section
                start = html_content.find('"datasheetUrl":"')
                if start != -1:
                    start += len('"datasheetUrl":"')
                    end = html_content.find('"', start)
                    datasheet_url = html_content[start:end]
                    
                    # Also look for manufacturer info
                    manufacturer = None
                    if '"manufacturer":"' in html_content:
                        mfr_start = html_content.find('"manufacturer":"')
                        if mfr_start != -1:
                            mfr_start += len('"manufacturer":"')
                            mfr_end = html_content.find('"', mfr_start)
                            manufacturer = html_content[mfr_start:mfr_end]
                    
                    # Look for manufacturer URL
                    manufacturer_url = None
                    if '"manufacturerUrl":"' in html_content:
                        url_start = html_content.find('"manufacturerUrl":"')
                        if url_start != -1:
                            url_start += len('"manufacturerUrl":"')
                            url_end = html_content.find('"', url_start)
                            manufacturer_url = html_content[url_start:url_end]
                            if manufacturer_url.startswith('/'):
                                manufacturer_url = f"https://www.digikey.com{manufacturer_url}"
                    
                    part_info = {
                        'part_number': part_number,
                        'datasheet_url': datasheet_url,
                        'manufacturer': manufacturer,
                        'manufacturer_url': manufacturer_url,
                        'source': 'Digikey'
                    }
                    
                    print(f"   📄 Datasheet: {datasheet_url}")
                    if manufacturer:
                        print(f"   🏭 Manufacturer: {manufacturer}")
                    if manufacturer_url:
                        print(f"   🌐 Manufacturer URL: {manufacturer_url}")
                    
                    return part_info
            
            print(f"   ⚠️  Could not extract part info from page")
            return None
            
        except Exception as e:
            print(f"   ❌ EXTRACTION ERROR: {e}")
            return None

    def handle_exact_matches(self, html_content, part_number, target_manufacturer=None):
        """Handle category pages with exact matches - pick the best one"""
        try:
            import json

            # Find the JSON data in the script tag
            start = html_content.find('{"props":')
            if start == -1:
                print(f"   ❌ Could not find JSON data")
                return None

            end = html_content.find('</script>', start)
            if end == -1:
                print(f"   ❌ Could not find end of JSON data")
                return None

            json_str = html_content[start:end]
            data = json.loads(json_str)

            # Look for exact matches
            exact_matches = data.get('props', {}).get('pageProps', {}).get('envelope', {}).get('data', {}).get('exactMatch', [])

            if not exact_matches:
                print(f"   ❌ No exact matches found")
                return None

            print(f"   🎯 Found {len(exact_matches)} exact matches")

            # Show all matches
            for i, match in enumerate(exact_matches, 1):
                mfr = match.get('mfr', 'Unknown')
                price = match.get('unitPrice', 'No price')
                print(f"   {i}. {mfr} - {price}")

            # Pick the best match (prefer major manufacturers)
            preferred_manufacturers = [
                'Texas Instruments', 'STMicroelectronics', 'onsemi',
                'Analog Devices', 'Microchip', 'NXP', 'Infineon'
            ]

            best_match = None

            # First try to find the target manufacturer if specified
            if target_manufacturer:
                for match in exact_matches:
                    mfr = match.get('mfr', '')
                    # More flexible matching for manufacturer names
                    target_lower = target_manufacturer.lower().replace(' ', '').replace('inc', '').replace('incorporated', '').replace('.', '')
                    mfr_lower = mfr.lower().replace(' ', '').replace('inc', '').replace('incorporated', '').replace('.', '')

                    if target_lower in mfr_lower or mfr_lower in target_lower:
                        best_match = match
                        print(f"   🎯 Found target manufacturer: {mfr}")
                        break

                    # Also try exact word matching
                    target_words = target_manufacturer.lower().split()
                    mfr_words = mfr.lower().split()
                    if any(word in mfr_words for word in target_words if len(word) > 2):
                        best_match = match
                        print(f"   🎯 Found target manufacturer (word match): {mfr}")
                        break

            # If no target manufacturer match, try preferred manufacturers
            if not best_match:
                for match in exact_matches:
                    mfr = match.get('mfr', '')
                    if any(pref_mfr in mfr for pref_mfr in preferred_manufacturers):
                        best_match = match
                        print(f"   ✅ Selected preferred manufacturer: {mfr}")
                        break

            # If no preferred manufacturer found, pick the first one with a price
            if not best_match:
                for match in exact_matches:
                    if match.get('unitPrice') and match.get('unitPrice') != '':
                        best_match = match
                        print(f"   ✅ Selected first match with price: {match.get('mfr', 'Unknown')}")
                        break

            # If still no match, just pick the first one
            if not best_match:
                best_match = exact_matches[0]
                print(f"   ⚠️  Using first match: {best_match.get('mfr', 'Unknown')}")

            first_match = best_match
            detail_url = first_match.get('detailUrl', '')
            manufacturer = first_match.get('mfr', '')

            if detail_url:
                print(f"   📄 Accessing detail page: {detail_url}")

                # Access the detail page to get datasheet with retry logic
                full_url = f"https://www.digikey.com{detail_url}"

                # Try multiple times with increasing delays
                for attempt in range(3):
                    if attempt > 0:
                        wait_time = 60 * (attempt + 1)  # 60, 120, 180 seconds
                        print(f"   ⏳ Waiting {wait_time} seconds before retry {attempt + 1}...")
                        import time
                        time.sleep(wait_time)

                    response = self.session.get(full_url, timeout=30)
                    print(f"   Attempt {attempt + 1} status: {response.status_code}")

                    if response.status_code == 200:
                        # Now extract from the detail page
                        return self.extract_part_info(response.text, part_number, target_manufacturer)
                    elif response.status_code == 429:
                        print(f"   ⚠️  Rate limited, will retry...")
                        continue
                    else:
                        print(f"   ❌ Failed to access detail page: {response.status_code}")
                        return None

                print(f"   ❌ All retry attempts failed")

                # Try fallback method for known manufacturers
                if target_manufacturer and 'texas instruments' in target_manufacturer.lower():
                    print(f"   🔄 Trying TI datasheet fallback method...")
                    return self.try_ti_datasheet_fallback(part_number)

                return None
            else:
                print(f"   ❌ No detail URL found")
                return None

        except Exception as e:
            print(f"   ❌ EXACT MATCH ERROR: {e}")
            return None

    def try_fallback_search(self, part_number, manufacturer):
        """Fallback search methods when primary search fails"""
        try:
            import time

            print(f"   🔄 Trying fallback approaches...")

            # Wait a bit to avoid rate limiting
            time.sleep(2)

            # Try different search patterns
            search_patterns = []

            if manufacturer:
                # Try manufacturer-specific searches
                search_patterns.extend([
                    f"https://www.digikey.com/en/products/result?keywords={part_number}%20{manufacturer.replace(' ', '%20')}",
                    f"https://www.digikey.com/en/products/result?keywords={manufacturer.replace(' ', '%20')}%20{part_number}",
                    f"https://www.digikey.com/en/products/result?keywords={part_number}"
                ])
            else:
                search_patterns.append(f"https://www.digikey.com/en/products/result?keywords={part_number}")

            for i, search_url in enumerate(search_patterns, 1):
                try:
                    print(f"   Fallback {i}: Trying different search pattern...")

                    response = self.session.get(search_url, timeout=30)
                    print(f"   Fallback {i} status: {response.status_code}")

                    if response.status_code == 200:
                        # Save fallback results
                        filename = f"digikey_fallback_{i}_{part_number.replace('/', '_')}.html"
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(response.text)

                        if part_number in response.text:
                            print(f"   ✅ Fallback {i} found part!")
                            return self.extract_part_info(response.text, part_number, manufacturer)

                    # Wait between attempts
                    time.sleep(1)

                except Exception as e:
                    print(f"   ❌ Fallback {i} error: {e}")
                    continue

            print(f"   ❌ All fallback methods failed")
            return None

        except Exception as e:
            print(f"   ❌ FALLBACK ERROR: {e}")
            return None

    def try_ti_datasheet_fallback(self, part_number):
        """Try to get TI datasheet directly from TI website"""
        try:
            print(f"   🎯 Attempting direct TI datasheet access...")

            # TI datasheet URLs typically follow pattern: https://www.ti.com/lit/gpn/partnumber
            base_part = part_number.lower().replace('-', '').replace('/', '')
            ti_datasheet_url = f"https://www.ti.com/lit/gpn/{base_part}"

            print(f"   Trying TI URL: {ti_datasheet_url}")

            response = self.session.get(ti_datasheet_url, timeout=30)
            print(f"   TI direct status: {response.status_code}")

            if response.status_code == 200:
                # Look for PDF download link in the TI page
                soup = BeautifulSoup(response.text, 'html.parser')

                pdf_links = []
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    if href and '.pdf' in href.lower():
                        if not href.startswith('http'):
                            href = f"https://www.ti.com{href}"
                        pdf_links.append(href)

                if pdf_links:
                    datasheet_url = pdf_links[0]  # Take first PDF link
                    print(f"   🎯 Found TI PDF: {datasheet_url}")

                    return {
                        'part_number': part_number,
                        'datasheet_url': datasheet_url,
                        'manufacturer': 'Texas Instruments',
                        'manufacturer_url': 'https://www.ti.com',
                        'source': 'TI Direct'
                    }
                else:
                    print(f"   ❌ No PDF links found on TI page")
                    return None
            else:
                print(f"   ❌ TI direct access failed: {response.status_code}")
                return None

        except Exception as e:
            print(f"   ❌ TI FALLBACK ERROR: {e}")
            return None
    
    def download_datasheet(self, part_info):
        """Download datasheet from the URL"""
        if not part_info or not part_info.get('datasheet_url'):
            print("   ❌ No datasheet URL available")
            return False
        
        try:
            datasheet_url = part_info['datasheet_url']
            part_number = part_info['part_number']
            manufacturer = part_info.get('manufacturer', 'Unknown')

            # Fix URL scheme if missing
            if datasheet_url.startswith('//'):
                datasheet_url = f"https:{datasheet_url}"
            elif not datasheet_url.startswith('http'):
                datasheet_url = f"https://{datasheet_url}"

            print(f"📥 DOWNLOADING DATASHEET...")
            print(f"   URL: {datasheet_url}")

            response = self.session.get(datasheet_url, timeout=60, stream=True)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                # Create datasheets directory
                os.makedirs('datasheets', exist_ok=True)
                
                # Create filename: Manufacturer-PartNumber.pdf
                filename = f"{manufacturer}-{part_number}.pdf"
                filepath = os.path.join('datasheets', filename)
                
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                file_size = os.path.getsize(filepath)
                print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                
                if file_size > 10000:  # At least 10KB
                    print(f"   🎉 SUCCESS: Datasheet downloaded!")
                    return True
                else:
                    print(f"   ⚠️  File too small, might be error page")
                    return False
            else:
                print(f"   ❌ Download failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ DOWNLOAD ERROR: {e}")
            return False
    
    def get_manufacturer_website(self, part_info):
        """Extract manufacturer website from manufacturer URL"""
        if not part_info or not part_info.get('manufacturer_url'):
            return None
        
        manufacturer_url = part_info['manufacturer_url']
        
        # Try to get the actual manufacturer website
        try:
            response = self.session.get(manufacturer_url, timeout=30)
            if response.status_code == 200:
                # Look for links to the actual manufacturer website
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Look for external links that might be the manufacturer website
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '').lower()
                    if any(domain in href for domain in ['.com', '.org', '.net']) and 'digikey' not in href:
                        if part_info.get('manufacturer', '').lower().replace(' ', '') in href.replace('-', '').replace('_', ''):
                            return href
                
                # If we can't find it, try to guess from manufacturer name
                manufacturer = part_info.get('manufacturer', '')
                if manufacturer:
                    # Common manufacturer website patterns
                    website_guess = f"https://www.{manufacturer.lower().replace(' ', '').replace('incorporated', '').replace('inc', '').replace('.', '')}.com"
                    return website_guess
            
        except Exception as e:
            print(f"   ⚠️  Could not determine manufacturer website: {e}")
        
        return None

def main():
    """Standalone usage"""
    if len(sys.argv) < 2:
        print("Usage: python digikey_searcher.py <part_number> [manufacturer]")
        print("Example: python digikey_searcher.py APX803L20-30SA-7 'Diodes Inc'")
        return
    
    part_number = sys.argv[1]
    manufacturer = sys.argv[2] if len(sys.argv) > 2 else None
    
    print("🚀 DIGIKEY SEARCHER")
    print("=" * 40)
    
    searcher = DigikeySearcher()
    
    # Search for part
    part_info = searcher.search_part(part_number, manufacturer)
    
    if part_info:
        # Download datasheet
        datasheet_success = searcher.download_datasheet(part_info)
        
        # Get manufacturer website
        manufacturer_website = searcher.get_manufacturer_website(part_info)
        if manufacturer_website:
            print(f"🌐 Manufacturer website: {manufacturer_website}")
        
        print("\n" + "=" * 40)
        if datasheet_success:
            print("✅ SUCCESS: Found and downloaded datasheet")
        else:
            print("⚠️  PARTIAL SUCCESS: Found part but datasheet download failed")
    else:
        print("\n" + "=" * 40)
        print("❌ FAILED: Could not find part on Digikey")

if __name__ == "__main__":
    main()
