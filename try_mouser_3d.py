#!/usr/bin/env python3
"""
Try Mouser Electronics for APX803L20-30SA-7 STEP files
Mouser often has manufacturer 3D models available
"""

import requests
from bs4 import BeautifulSoup
import os
import re
from datetime import datetime

def log_message(message):
    """Write message to log file"""
    print(message)
    with open('mouser_3d_log.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {message}\n")

def search_mouser_for_3d(part_number):
    log_message(f"🔍 SEARCHING MOUSER FOR 3D MODELS: {part_number}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        # Mouser search URL
        search_url = "https://www.mouser.com/ProductDetail/"
        params = {'qs': part_number}
        
        log_message(f"   Searching Mouser...")
        response = session.get(search_url, params=params, timeout=30)
        log_message(f"   Status: {response.status_code}")
        log_message(f"   Final URL: {response.url}")
        
        if response.status_code == 200:
            # Save the page
            with open('mouser_search_result.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            log_message(f"   📄 Saved Mouser page")
            
            # Look for 3D model indicators
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for CAD/3D model sections
            cad_indicators = ['3d', 'cad', 'step', 'model', 'download', 'mechanical']
            found_cad_content = []
            
            # Check all text for CAD indicators
            page_text = response.text.lower()
            for indicator in cad_indicators:
                if indicator in page_text:
                    found_cad_content.append(indicator)
            
            log_message(f"   CAD indicators found: {found_cad_content}")
            
            # Look for specific download links
            download_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text(strip=True).lower()
                
                if any(indicator in text for indicator in ['3d', 'cad', 'step', 'download']):
                    download_links.append({'url': href, 'text': text})
                elif any(ext in href.lower() for ext in ['.step', '.stp', '.zip']):
                    download_links.append({'url': href, 'text': text})
            
            log_message(f"   Download links found: {len(download_links)}")
            for i, link in enumerate(download_links[:3], 1):
                log_message(f"   {i}. {link['text']}")
                log_message(f"      URL: {link['url']}")
            
            # Try to download any STEP files found
            for link in download_links:
                if '.step' in link['url'].lower() or '.stp' in link['url'].lower():
                    return try_download_from_mouser(session, link, part_number)
            
            return len(found_cad_content) > 0
            
        else:
            log_message(f"   ❌ Mouser search failed: {response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Mouser error: {e}")
        return False

def try_download_from_mouser(session, download_link, part_number):
    log_message(f"   🔽 Attempting download from Mouser...")
    
    try:
        # Make sure URL is complete
        url = download_link['url']
        if not url.startswith('http'):
            url = f"https://www.mouser.com{url}"
        
        log_message(f"   Download URL: {url}")
        
        download_response = session.get(url, timeout=60, stream=True)
        log_message(f"   Download status: {download_response.status_code}")
        
        if download_response.status_code == 200:
            # Create 3d directory
            os.makedirs('3d', exist_ok=True)
            
            # Determine filename
            filename = f"{part_number}_Mouser.step"
            filepath = os.path.join('3d', filename)
            
            # Save file
            with open(filepath, 'wb') as f:
                for chunk in download_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            log_message(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                log_message(f"   🎉 SUCCESS: Got STEP file from Mouser!")
                return True
            else:
                log_message(f"   ⚠️  File too small, might be error page")
                return False
        else:
            log_message(f"   ❌ Download failed: {download_response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Download error: {e}")
        return False

def try_arrow_electronics(part_number):
    log_message(f"🔍 TRYING ARROW ELECTRONICS: {part_number}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        # Arrow search URL
        search_url = "https://www.arrow.com/en/products/search"
        params = {'q': part_number}
        
        log_message(f"   Searching Arrow...")
        response = session.get(search_url, params=params, timeout=30)
        log_message(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            with open('arrow_search_result.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            log_message(f"   📄 Saved Arrow page")
            
            # Check for CAD content
            if any(indicator in response.text.lower() for indicator in ['3d', 'cad', 'step', 'model']):
                log_message(f"   ✅ Found CAD content indicators on Arrow!")
                return True
            else:
                log_message(f"   ❌ No CAD content found on Arrow")
                return False
        else:
            log_message(f"   ❌ Arrow search failed: {response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Arrow error: {e}")
        return False

def try_newark_farnell(part_number):
    log_message(f"🔍 TRYING NEWARK/FARNELL: {part_number}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        # Newark search URL
        search_url = "https://www.newark.com/search"
        params = {'st': part_number}
        
        log_message(f"   Searching Newark...")
        response = session.get(search_url, params=params, timeout=30)
        log_message(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            with open('newark_search_result.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            log_message(f"   📄 Saved Newark page")
            
            # Check for CAD content
            if any(indicator in response.text.lower() for indicator in ['3d', 'cad', 'step', 'model']):
                log_message(f"   ✅ Found CAD content indicators on Newark!")
                return True
            else:
                log_message(f"   ❌ No CAD content found on Newark")
                return False
        else:
            log_message(f"   ❌ Newark search failed: {response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Newark error: {e}")
        return False

def main():
    # Clear log
    with open('mouser_3d_log.txt', 'w') as f:
        f.write(f"NEW DISTRIBUTOR 3D SEARCH LOG - {datetime.now()}\n")
        f.write("=" * 60 + "\n")
    
    part_number = "APX803L20-30SA-7"
    
    log_message("🚀 TRYING NEW DISTRIBUTOR SOURCES FOR STEP FILES")
    log_message("=" * 60)
    
    results = []
    
    # 1. Try Mouser
    log_message("\n1. MOUSER ELECTRONICS")
    if search_mouser_for_3d(part_number):
        results.append("Mouser - SUCCESS")
        log_message("✅ Mouser has CAD content!")
    else:
        results.append("Mouser - No CAD content")
        log_message("❌ No CAD content on Mouser")
    
    # 2. Try Arrow
    log_message("\n2. ARROW ELECTRONICS")
    if try_arrow_electronics(part_number):
        results.append("Arrow - SUCCESS")
        log_message("✅ Arrow has CAD content!")
    else:
        results.append("Arrow - No CAD content")
        log_message("❌ No CAD content on Arrow")
    
    # 3. Try Newark
    log_message("\n3. NEWARK/FARNELL")
    if try_newark_farnell(part_number):
        results.append("Newark - SUCCESS")
        log_message("✅ Newark has CAD content!")
    else:
        results.append("Newark - No CAD content")
        log_message("❌ No CAD content on Newark")
    
    # Summary
    log_message("\n" + "=" * 60)
    log_message("📋 NEW DISTRIBUTOR RESULTS:")
    for result in results:
        log_message(f"   {result}")
    
    # Check for actual STEP files
    step_files = []
    if os.path.exists('3d'):
        for file in os.listdir('3d'):
            if file.endswith('.step') and not file.startswith('KiCad_'):
                step_files.append(file)
    
    if step_files:
        log_message(f"\n🎉 ACTUAL STEP FILES FOUND:")
        for file in step_files:
            filepath = os.path.join('3d', file)
            size = os.path.getsize(filepath)
            log_message(f"   ✅ {file} ({size:,} bytes)")
    else:
        log_message(f"\n❌ NO NEW STEP FILES DOWNLOADED")
        log_message(f"   Check saved HTML files for manual inspection")
    
    log_message(f"\n📄 Full log saved to mouser_3d_log.txt")

if __name__ == "__main__":
    main()
