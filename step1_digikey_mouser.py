#!/usr/bin/env python3
"""
Step 1: Search Digikey and Mouser for APX803L20-30SA-7
Goal: Find datasheets and manufacturer website links
"""

import requests
from bs4 import BeautifulSoup
import time

def search_digikey(part_number):
    print(f"🔍 STEP 1A: SEARCHING DIGIKEY FOR {part_number}")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        # Search Digikey
        search_url = f"https://www.digikey.com/en/products/filter"
        params = {'keywords': part_number}
        
        print(f"   Searching: {search_url}")
        response = session.get(search_url, params=params, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the search results
            with open('digikey_search_results.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"   📄 Saved search results")
            
            # Look for the part and manufacturer info
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check if part found
            if part_number.upper() in response.text.upper():
                print(f"   ✅ Found {part_number} on Digikey")
                return True
            else:
                print(f"   ❌ {part_number} not found on Digikey")
                return False
        else:
            print(f"   ❌ Search failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def search_mouser(part_number):
    print(f"\n🔍 STEP 1B: SEARCHING MOUSER FOR {part_number}")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        # Search Mouser
        search_url = f"https://www.mouser.com/ProductDetail/{part_number}"
        
        print(f"   Searching: {search_url}")
        response = session.get(search_url, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the search results
            with open('mouser_search_results.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"   📄 Saved search results")
            
            # Check if part found
            if part_number.upper() in response.text.upper():
                print(f"   ✅ Found {part_number} on Mouser")
                return True
            else:
                print(f"   ❌ {part_number} not found on Mouser")
                return False
        else:
            print(f"   ❌ Search failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    part_number = "APX803L20-30SA-7"
    manufacturer = "Diodes Inc"
    
    print("🚀 STEP 1: SEARCH DIGIKEY AND MOUSER")
    print("Goal: Find datasheets and manufacturer website links")
    print("=" * 60)
    
    # Search both sites
    digikey_found = search_digikey(part_number)
    time.sleep(2)  # Be polite
    mouser_found = search_mouser(part_number)
    
    # Summary
    print(f"\n" + "=" * 60)
    print("📋 STEP 1 RESULTS:")
    print(f"   Digikey: {'✅ FOUND' if digikey_found else '❌ NOT FOUND'}")
    print(f"   Mouser: {'✅ FOUND' if mouser_found else '❌ NOT FOUND'}")
    
    if digikey_found or mouser_found:
        print(f"\n✅ SUCCESS: Found part on at least one distributor")
        print(f"📄 Next: Analyze the saved HTML files to find:")
        print(f"   - Datasheet links")
        print(f"   - Manufacturer website links")
        print(f"   - Any 3D model availability")
    else:
        print(f"\n❌ FAILED: Part not found on either distributor")

if __name__ == "__main__":
    main()
