#!/usr/bin/env python3
"""
Test SnapMagic/SnapEDA search directly for APX803L20-30SA-7
"""

import requests
from bs4 import BeautifulSoup
import os
import time
from datetime import datetime

def log_message(message):
    """Write message to log file"""
    print(message)
    with open('snapmagic_test_log.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {message}\n")

def test_snapmagic_search(part_number):
    log_message(f"🔍 TESTING SNAPMAGIC/SNAPEDA FOR: {part_number}")
    log_message("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    # Try both SnapEDA and SnapMagic
    search_urls = [
        f"https://www.snapeda.com/search?q={part_number}",
        f"https://www.snapmagic.com/search?q={part_number}"
    ]
    
    results = []
    
    for search_url in search_urls:
        site_name = "SnapEDA" if "snapeda" in search_url else "SnapMagic"
        log_message(f"\n🔍 Testing {site_name}...")
        log_message(f"   URL: {search_url}")
        
        try:
            time.sleep(2)  # Be polite
            response = session.get(search_url, timeout=30)
            log_message(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                # Save the response
                filename = f"{site_name.lower()}_search_result.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                log_message(f"   📄 Saved: {filename}")
                
                # Parse the response
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()
                
                # Check if part is found
                if part_number.lower() in page_text:
                    log_message(f"   ✅ Found {part_number} on {site_name}!")
                    
                    # Look for download links
                    download_links = find_download_links(soup, site_name)
                    log_message(f"   Found {len(download_links)} potential download links:")
                    
                    for i, link in enumerate(download_links[:5], 1):
                        log_message(f"   {i}. {link['text']}")
                        log_message(f"      URL: {link['url']}")
                    
                    # Try to download STEP files
                    for link in download_links:
                        if any(ext in link['url'].lower() for ext in ['.step', '.stp']):
                            success = try_download_step(session, link, part_number, site_name)
                            if success:
                                results.append(f"{site_name} - Downloaded STEP file")
                                break
                    else:
                        results.append(f"{site_name} - Found part, no direct STEP download")
                else:
                    log_message(f"   ❌ {part_number} not found on {site_name}")
                    results.append(f"{site_name} - Part not found")
            else:
                log_message(f"   ❌ {site_name} returned status {response.status_code}")
                results.append(f"{site_name} - HTTP error {response.status_code}")
                
        except Exception as e:
            log_message(f"   ❌ {site_name} error: {e}")
            results.append(f"{site_name} - Error: {str(e)[:50]}")
    
    return results

def find_download_links(soup, site_name):
    """Find potential download links on the page"""
    download_links = []
    
    # Enhanced selectors for SnapEDA/SnapMagic
    selectors = [
        'a[href*="download"]',
        'a[href*=".step"]', 
        'a[href*=".stp"]',
        '.download-btn',
        '.cad-download',
        '.model-download',
        'a[href*="3d"]',
        'a[href*="cad"]'
    ]
    
    for selector in selectors:
        elements = soup.select(selector)
        for element in elements:
            href = element.get('href')
            text = element.get_text(strip=True)
            
            if href and text:
                # Make URL absolute if needed
                if not href.startswith('http'):
                    if site_name == "SnapEDA":
                        href = f"https://www.snapeda.com{href}"
                    else:
                        href = f"https://www.snapmagic.com{href}"
                
                download_links.append({
                    'url': href,
                    'text': text,
                    'selector': selector
                })
    
    # Also look for any links containing STEP-related keywords
    for link in soup.find_all('a', href=True):
        href = link.get('href')
        text = link.get_text(strip=True).lower()
        
        if any(keyword in text for keyword in ['step', 'stp', '3d', 'cad', 'download', 'model']):
            if not href.startswith('http'):
                if site_name == "SnapEDA":
                    href = f"https://www.snapeda.com{href}"
                else:
                    href = f"https://www.snapmagic.com{href}"
            
            # Avoid duplicates
            if not any(existing['url'] == href for existing in download_links):
                download_links.append({
                    'url': href,
                    'text': text,
                    'selector': 'text_search'
                })
    
    return download_links

def try_download_step(session, link, part_number, site_name):
    """Try to download a STEP file"""
    log_message(f"   🔽 Attempting download from {site_name}...")
    log_message(f"   Download URL: {link['url']}")
    
    try:
        download_response = session.get(link['url'], timeout=60, stream=True)
        log_message(f"   Download status: {download_response.status_code}")
        
        if download_response.status_code == 200:
            # Create 3d directory
            os.makedirs('3d', exist_ok=True)
            
            # Determine filename
            filename = f"{part_number}_{site_name}.step"
            filepath = os.path.join('3d', filename)
            
            # Save file
            with open(filepath, 'wb') as f:
                for chunk in download_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            log_message(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                log_message(f"   🎉 SUCCESS: Got STEP file from {site_name}!")
                return True
            else:
                log_message(f"   ⚠️  File too small, might be error page")
                return False
        else:
            log_message(f"   ❌ Download failed: {download_response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Download error: {e}")
        return False

def main():
    # Clear log
    with open('snapmagic_test_log.txt', 'w') as f:
        f.write(f"SNAPMAGIC/SNAPEDA TEST LOG - {datetime.now()}\n")
        f.write("=" * 60 + "\n")
    
    part_number = "APX803L20-30SA-7"
    
    log_message("🚀 TESTING SNAPMAGIC/SNAPEDA - NEXT SOURCE AFTER ULTRALIBRARIAN")
    log_message("=" * 60)
    
    results = test_snapmagic_search(part_number)
    
    # Summary
    log_message("\n" + "=" * 60)
    log_message("📋 SNAPMAGIC/SNAPEDA TEST RESULTS:")
    for result in results:
        log_message(f"   {result}")
    
    # Check for actual STEP files
    step_files = []
    if os.path.exists('3d'):
        for file in os.listdir('3d'):
            if file.endswith('.step') and not file.startswith('KiCad_'):
                step_files.append(file)
    
    if step_files:
        log_message(f"\n🎉 ACTUAL STEP FILES DOWNLOADED:")
        for file in step_files:
            filepath = os.path.join('3d', file)
            size = os.path.getsize(filepath)
            log_message(f"   ✅ {file} ({size:,} bytes)")
        log_message(f"\n🎯 SUCCESS: We now have real manufacturer STEP files!")
    else:
        log_message(f"\n❌ NO NEW STEP FILES DOWNLOADED")
        log_message(f"   Still only have generic KiCad models")
        log_message(f"   Need to try SamacSys next")
    
    log_message(f"\n📄 Full log saved to snapmagic_test_log.txt")
    log_message(f"📄 Check HTML files for manual inspection")

if __name__ == "__main__":
    main()
