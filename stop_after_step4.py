#!/usr/bin/env python3
"""
STOP AFTER STEP 4
=================
Do steps 1-4 then stop. No login, no monitoring.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def stop_after_step4():
    print("🎯 STOP AFTER STEP 4")
    print("=" * 30)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Navigate to Screen 4 quickly
        print("🔸 Navigating to Screen 4...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        # Search
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        # Click TI part
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    link.click()
                    break
            except:
                continue
        time.sleep(8)
        
        # Click Download Now
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            driver.execute_script("arguments[0].click();", download_btns[0])
            time.sleep(5)
        
        print("✅ At Screen 4")
        
        # STEP 1: Click 3D CAD Model
        print("\n🔸 STEP 1: Clicking '3D CAD Model'...")
        time.sleep(5)
        
        cad_selectors = [
            "//button[contains(text(), '3D CAD Model')]",
            "//a[contains(text(), '3D CAD Model')]",
            "//button[contains(text(), '3D Model')]",
            "//a[contains(text(), '3D Model')]"
        ]
        
        cad_element = None
        for selector in cad_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    cad_element = elem
                    print(f"✅ Found 3D CAD Model: '{elem.text}'")
                    break
            if cad_element:
                break
        
        if not cad_element:
            print("❌ No 3D CAD Model button found!")
            print("Browser staying open for inspection...")
            while True:
                time.sleep(10)
            return
        
        driver.execute_script("arguments[0].click();", cad_element)
        time.sleep(5)
        print("✅ STEP 1 COMPLETE: Clicked 3D CAD Model")
        
        # STEP 2: Handle new screen
        print("\n🔸 STEP 2: Handling new screen...")
        
        # Check for new window
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        print(f"Current URL: {driver.current_url}")
        print("✅ STEP 2 COMPLETE: Handled new screen")
        
        # STEP 3: Select STEP format
        print("\n🔸 STEP 3: Selecting STEP format...")
        
        step_selectors = [
            "//button[contains(text(), 'STEP')]",
            "//a[contains(text(), 'STEP')]",
            "//input[@value='STEP']",
            "//option[contains(text(), 'STEP')]",
            "//div[contains(text(), 'STEP') and (@onclick or @click)]",
            "//label[contains(text(), 'STEP')]",
            "//span[contains(text(), 'STEP')]"
        ]
        
        step_element = None
        for selector in step_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                try:
                    if elem.is_displayed():
                        step_element = elem
                        print(f"✅ Found STEP option: '{elem.text or elem.get_attribute('value')}'")
                        break
                except:
                    continue
            if step_element:
                break
        
        if step_element:
            driver.execute_script("arguments[0].click();", step_element)
            time.sleep(3)
            print("✅ STEP 3 COMPLETE: Selected STEP format")
        else:
            print("⚠️ STEP 3: No STEP format found, continuing...")
        
        # STEP 4: Click download button
        print("\n🔸 STEP 4: Looking for download button...")
        
        download_selectors = [
            "//button[contains(text(), 'Download')]",
            "//a[contains(text(), 'Download')]",
            "//input[@type='submit']"
        ]
        
        download_element = None
        for selector in download_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    download_element = elem
                    print(f"✅ Found download button: '{elem.text or elem.get_attribute('value')}'")
                    break
            if download_element:
                break
        
        if download_element:
            driver.execute_script("arguments[0].click();", download_element)
            time.sleep(5)
            print("✅ STEP 4 COMPLETE: Clicked download button")
        else:
            print("⚠️ STEP 4: No download button found")
        
        # STOP HERE
        print("\n🛑 AUTOMATION COMPLETE")
        print("Steps 1-4 completed. Login and monitoring skipped as requested.")
        print(f"Current URL: {driver.current_url}")
        
        # Check if login form appeared
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email']")
        if email_inputs:
            print("🔐 Login form is now visible - ready for manual login")
        
        # Keep browser open
        print("\n🔒 BROWSER STAYING OPEN")
        print("Ready for manual login when needed")
        
        while True:
            time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Browser staying open...")
        while True:
            time.sleep(10)

if __name__ == "__main__":
    stop_after_step4()
