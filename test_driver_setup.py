#!/usr/bin/env python3
print('Testing driver setup...')

import working_ultralibrarian_3d_finder

finder = working_ultralibrarian_3d_finder.ScreenAwareUltraLibrarian()
print('Class created')

driver = finder.setup_driver()
print(f'Driver: {driver}')

if driver:
    print('Driver created successfully')
    driver.quit()
    print('Driver closed')
else:
    print('Driver creation failed')

print('Driver test complete')
