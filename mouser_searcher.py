#!/usr/bin/env python3
"""
Modular Mouser searcher for datasheets and manufacturer info
Can be used by main program or run standalone
"""

import requests
from bs4 import BeautifulSoup
import json
import os
import sys

class MouserSearcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.results = {}
    
    def search_part(self, part_number, manufacturer=None):
        """Search for a part on Mouser"""
        print(f"🔍 SEARCHING MOUSER FOR: {part_number}")
        if manufacturer:
            print(f"   Manufacturer: {manufacturer}")
        
        try:
            # Try direct product detail URL first
            search_url = f"https://www.mouser.com/ProductDetail/{part_number}"
            
            response = self.session.get(search_url, timeout=30)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                # Save search results
                filename = f"mouser_search_{part_number.replace('/', '_')}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                # Check if part found
                if part_number in response.text or 'ProductDetail' in response.text:
                    print(f"   ✅ FOUND: {part_number}")
                    return self.extract_part_info(response.text, part_number)
                else:
                    print(f"   ❌ NOT FOUND: {part_number}")
                    # Try search instead
                    return self.try_search_fallback(part_number, manufacturer)
            else:
                print(f"   ❌ DIRECT ACCESS FAILED: {response.status_code}")
                return self.try_search_fallback(part_number, manufacturer)
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            return None
    
    def try_search_fallback(self, part_number, manufacturer):
        """Try Mouser search if direct access fails"""
        try:
            print(f"   🔄 Trying search fallback...")
            search_url = f"https://www.mouser.com/c/?q={part_number}"
            
            response = self.session.get(search_url, timeout=30)
            print(f"   Search status: {response.status_code}")
            
            if response.status_code == 200:
                filename = f"mouser_search_fallback_{part_number.replace('/', '_')}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                if part_number in response.text:
                    print(f"   ✅ FOUND in search: {part_number}")
                    return self.extract_part_info(response.text, part_number)
            
            return None
            
        except Exception as e:
            print(f"   ❌ SEARCH FALLBACK ERROR: {e}")
            return None
    
    def extract_part_info(self, html_content, part_number):
        """Extract datasheet URL and manufacturer info from Mouser page"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for datasheet links
            datasheet_url = None
            datasheet_links = soup.find_all('a', href=True)
            
            for link in datasheet_links:
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                
                if 'datasheet' in text or 'datasheet' in href or '.pdf' in href:
                    if any(keyword in href for keyword in ['datasheet', 'pdf']):
                        datasheet_url = link.get('href')
                        if not datasheet_url.startswith('http'):
                            datasheet_url = f"https://www.mouser.com{datasheet_url}"
                        break
            
            # Look for manufacturer info
            manufacturer = None
            manufacturer_elements = soup.find_all(['span', 'div', 'td'], string=lambda text: text and 'manufacturer' in text.lower())
            
            # Also look in meta tags or structured data
            for meta in soup.find_all('meta'):
                if meta.get('property') == 'product:brand':
                    manufacturer = meta.get('content')
                    break
            
            # Look for manufacturer website links
            manufacturer_url = None
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'manufacturer' in href.lower() or 'brand' in href.lower():
                    manufacturer_url = href
                    if not manufacturer_url.startswith('http'):
                        manufacturer_url = f"https://www.mouser.com{manufacturer_url}"
                    break
            
            if datasheet_url or manufacturer:
                part_info = {
                    'part_number': part_number,
                    'datasheet_url': datasheet_url,
                    'manufacturer': manufacturer,
                    'manufacturer_url': manufacturer_url,
                    'source': 'Mouser'
                }
                
                print(f"   📄 Datasheet: {datasheet_url}")
                if manufacturer:
                    print(f"   🏭 Manufacturer: {manufacturer}")
                if manufacturer_url:
                    print(f"   🌐 Manufacturer URL: {manufacturer_url}")
                
                return part_info
            
            print(f"   ⚠️  Could not extract part info from page")
            return None
            
        except Exception as e:
            print(f"   ❌ EXTRACTION ERROR: {e}")
            return None
    
    def download_datasheet(self, part_info):
        """Download datasheet from the URL"""
        if not part_info or not part_info.get('datasheet_url'):
            print("   ❌ No datasheet URL available")
            return False
        
        try:
            datasheet_url = part_info['datasheet_url']
            part_number = part_info['part_number']
            manufacturer = part_info.get('manufacturer', 'Unknown')
            
            print(f"📥 DOWNLOADING DATASHEET...")
            print(f"   URL: {datasheet_url}")
            
            response = self.session.get(datasheet_url, timeout=60, stream=True)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                # Create datasheets directory
                os.makedirs('datasheets', exist_ok=True)
                
                # Create filename: Manufacturer-PartNumber.pdf
                filename = f"{manufacturer}-{part_number}.pdf"
                filepath = os.path.join('datasheets', filename)
                
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                file_size = os.path.getsize(filepath)
                print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                
                if file_size > 10000:  # At least 10KB
                    print(f"   🎉 SUCCESS: Datasheet downloaded!")
                    return True
                else:
                    print(f"   ⚠️  File too small, might be error page")
                    return False
            else:
                print(f"   ❌ Download failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ DOWNLOAD ERROR: {e}")
            return False
    
    def get_manufacturer_website(self, part_info):
        """Extract manufacturer website"""
        if not part_info:
            return None
        
        manufacturer = part_info.get('manufacturer', '')
        if manufacturer:
            # Try to guess manufacturer website
            website_guess = f"https://www.{manufacturer.lower().replace(' ', '').replace('incorporated', '').replace('inc', '').replace('.', '')}.com"
            return website_guess
        
        return None

def main():
    """Standalone usage"""
    if len(sys.argv) < 2:
        print("Usage: python mouser_searcher.py <part_number> [manufacturer]")
        print("Example: python mouser_searcher.py APX803L20-30SA-7 'Diodes Inc'")
        return
    
    part_number = sys.argv[1]
    manufacturer = sys.argv[2] if len(sys.argv) > 2 else None
    
    print("🚀 MOUSER SEARCHER")
    print("=" * 40)
    
    searcher = MouserSearcher()
    
    # Search for part
    part_info = searcher.search_part(part_number, manufacturer)
    
    if part_info:
        # Download datasheet
        datasheet_success = searcher.download_datasheet(part_info)
        
        # Get manufacturer website
        manufacturer_website = searcher.get_manufacturer_website(part_info)
        if manufacturer_website:
            print(f"🌐 Manufacturer website: {manufacturer_website}")
        
        print("\n" + "=" * 40)
        if datasheet_success:
            print("✅ SUCCESS: Found and downloaded datasheet")
        else:
            print("⚠️  PARTIAL SUCCESS: Found part but datasheet download failed")
    else:
        print("\n" + "=" * 40)
        print("❌ FAILED: Could not find part on Mouser")

if __name__ == "__main__":
    main()
