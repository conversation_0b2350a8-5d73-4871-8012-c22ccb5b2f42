#!/usr/bin/env python3
"""
STABLE STEP BY STEP
===================
More stable version with better error handling.
"""

import time
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def stable_step_by_step():
    print("🎯 STABLE STEP BY STEP - ULTRALIBRARIAN")
    print("=" * 50)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = None
    
    try:
        print("Starting Chrome...")
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Chrome started successfully")
        
        # SCREEN 1: Load UltraLibrarian
        print("\n📺 SCREEN 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        print("✅ Navigation command sent")
        
        # Wait and show progress
        for i in range(15):
            time.sleep(1)
            try:
                title = driver.title
                if title and "ultra" in title.lower():
                    print(f"✅ Page loaded: {title}")
                    break
                print(f"  Loading... ({i+1}/15)")
            except:
                print(f"  Loading... ({i+1}/15)")
        
        print(f"Current URL: {driver.current_url}")
        
        # Get user confirmation for Screen 1
        while True:
            try:
                response = input("\n❓ SCREEN 1 - Is UltraLibrarian loaded correctly? (y/n): ").strip().lower()
                if response in ['y', 'n']:
                    break
                print("Please enter 'y' or 'n'")
            except KeyboardInterrupt:
                print("\n❌ User cancelled")
                return
            except Exception as e:
                print(f"Input error: {e}")
                return
        
        if response != 'y':
            print("❌ User said Screen 1 not correct")
            return
        
        # SCREEN 2: Search
        print("\n📺 SCREEN 2: Searching for LM358N...")
        
        try:
            inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"Found {len(inputs)} input elements")
            
            search_box = None
            for i, inp in enumerate(inputs):
                try:
                    if inp.is_displayed() and inp.is_enabled():
                        placeholder = inp.get_attribute('placeholder') or ''
                        if 'search' in placeholder.lower():
                            search_box = inp
                            print(f"✅ Found search box: '{placeholder}'")
                            break
                except:
                    continue
            
            if not search_box:
                print("❌ No search box found!")
                return
            
            print("Entering LM358N...")
            search_box.clear()
            search_box.send_keys("LM358N")
            search_box.send_keys(Keys.RETURN)
            print("✅ Search submitted")
            
            # Wait for results
            for i in range(10):
                time.sleep(1)
                print(f"  Waiting for results... ({i+1}/10)")
            
            print(f"New URL: {driver.current_url}")
            
        except Exception as e:
            print(f"❌ Error in search: {e}")
            return
        
        # Get user confirmation for Screen 2
        while True:
            try:
                response = input("\n❓ SCREEN 2 - Do you see LM358N search results? (y/n): ").strip().lower()
                if response in ['y', 'n']:
                    break
                print("Please enter 'y' or 'n'")
            except KeyboardInterrupt:
                print("\n❌ User cancelled")
                return
            except Exception as e:
                print(f"Input error: {e}")
                return
        
        if response != 'y':
            print("❌ User said Screen 2 not correct")
            return
        
        # SCREEN 3: Find and click TI LM358N
        print("\n📺 SCREEN 3: Looking for Texas Instruments LM358N...")
        
        try:
            links = driver.find_elements(By.TAG_NAME, "a")
            print(f"Found {len(links)} links total")
            
            lm358_links = []
            for link in links:
                try:
                    text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    
                    if ('lm358' in text.lower() or 'lm358' in href.lower()) and link.is_displayed():
                        lm358_links.append((text, href, link))
                        print(f"  LM358 option: '{text}'")
                        
                        if len(lm358_links) >= 5:  # Limit output
                            break
                except:
                    continue
            
            # Find TI specific
            ti_link = None
            for text, href, link_element in lm358_links:
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower()):
                    ti_link = (text, href, link_element)
                    print(f"✅ Found Texas Instruments: '{text}'")
                    break
            
            if not ti_link:
                print("❌ No Texas Instruments LM358N found!")
                return
            
        except Exception as e:
            print(f"❌ Error finding links: {e}")
            return
        
        # Get user confirmation for Screen 3
        while True:
            try:
                response = input("\n❓ SCREEN 3 - Should I click the Texas Instruments LM358N? (y/n): ").strip().lower()
                if response in ['y', 'n']:
                    break
                print("Please enter 'y' or 'n'")
            except KeyboardInterrupt:
                print("\n❌ User cancelled")
                return
            except Exception as e:
                print(f"Input error: {e}")
                return
        
        if response != 'y':
            print("❌ User said don't click TI part")
            return
        
        # Click TI part
        try:
            print("Clicking Texas Instruments LM358N...")
            text, href, link_element = ti_link
            link_element.click()
            print(f"✅ Clicked: {text}")
            
            # Wait for page load
            for i in range(8):
                time.sleep(1)
                print(f"  Loading part page... ({i+1}/8)")
            
            print(f"New URL: {driver.current_url}")
            
        except Exception as e:
            print(f"❌ Error clicking TI part: {e}")
            return
        
        # SCREEN 4: Part details page
        print("\n📺 SCREEN 4: Part details page loaded")
        
        try:
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons:")
            for i, btn in enumerate(buttons):
                try:
                    text = btn.text.strip()
                    visible = btn.is_displayed()
                    if text:
                        print(f"  {i}: '{text}' (visible={visible})")
                except:
                    continue
            
            # Look for Download Now
            download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
            if download_btns:
                print(f"✅ Found Download Now button: '{download_btns[0].text}'")
            else:
                print("❌ No Download Now button found!")
                
        except Exception as e:
            print(f"❌ Error analyzing page: {e}")
        
        print("\n🔍 INVESTIGATION POINT")
        print("What do you see on the part details page?")
        print("What should be the next step?")
        
        # Keep browser open for investigation
        while True:
            try:
                response = input("\nPress Enter to close browser, or type 'keep' to keep it open: ").strip().lower()
                if response == 'keep':
                    print("Browser staying open...")
                    time.sleep(30)  # Keep open for 30 more seconds
                else:
                    break
            except KeyboardInterrupt:
                print("\n❌ User cancelled")
                break
            except Exception as e:
                print(f"Input error: {e}")
                break
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if driver:
            try:
                driver.quit()
                print("✅ Browser closed")
            except:
                print("⚠️ Error closing browser")

if __name__ == "__main__":
    stable_step_by_step()
