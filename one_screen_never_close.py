#!/usr/bin/env python3
"""
ONE SCREEN AT A TIME - NEVER CLOSE
==================================
Go one screen at a time and never close browser.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def one_screen_never_close():
    print("🎯 ONE SCREEN AT A TIME - NEVER CLOSE")
    print("=" * 50)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    print("✅ Chrome started")
    
    # SCREEN 1: Load UltraLibrarian
    print("\n📺 SCREEN 1: Loading UltraLibrarian...")
    driver.get('https://app.ultralibrarian.com')
    time.sleep(15)
    print(f"✅ Loaded: {driver.title}")
    
    response = input("\n❓ SCREEN 1 OK? (y to continue): ")
    if response.lower() != 'y':
        input("<PERSON><PERSON><PERSON> staying open. Press Enter when ready to close...")
        driver.quit()
        return
    
    # SCREEN 2: Search
    print("\n📺 SCREEN 2: Searching for LM358N...")
    inputs = driver.find_elements(By.TAG_NAME, "input")
    for inp in inputs:
        if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
            inp.clear()
            inp.send_keys("LM358N")
            inp.send_keys(Keys.RETURN)
            break
    time.sleep(10)
    print("✅ Search completed")
    
    response = input("\n❓ SCREEN 2 OK? (y to continue): ")
    if response.lower() != 'y':
        input("Browser staying open. Press Enter when ready to close...")
        driver.quit()
        return
    
    # SCREEN 3: Click TI part
    print("\n📺 SCREEN 3: Clicking TI LM358N...")
    links = driver.find_elements(By.TAG_NAME, "a")
    for link in links:
        try:
            text = link.text.strip()
            href = link.get_attribute('href') or ''
            if (('lm358n/nopb' in text.lower() or 
                 ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                'details' in href.lower() and link.is_displayed()):
                link.click()
                break
        except:
            continue
    time.sleep(8)
    print("✅ Clicked TI part")
    
    response = input("\n❓ SCREEN 3 OK? (y to continue): ")
    if response.lower() != 'y':
        input("Browser staying open. Press Enter when ready to close...")
        driver.quit()
        return
    
    # SCREEN 4: Click Download Now
    print("\n📺 SCREEN 4: Clicking Download Now...")
    download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
    if download_btns:
        driver.execute_script("arguments[0].click();", download_btns[0])
        time.sleep(5)
        print("✅ Clicked Download Now")
    else:
        print("❌ No Download Now found")
    
    response = input("\n❓ SCREEN 4 OK? (y to continue): ")
    if response.lower() != 'y':
        input("Browser staying open. Press Enter when ready to close...")
        driver.quit()
        return
    
    # SCREEN 5: Click 3D CAD Model
    print("\n📺 SCREEN 5: Clicking 3D CAD Model...")
    time.sleep(5)  # Wait for page to load
    
    cad_btns = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
    if cad_btns:
        driver.execute_script("arguments[0].click();", cad_btns[0])
        time.sleep(5)
        print("✅ Clicked 3D CAD Model")
    else:
        print("❌ No 3D CAD Model found")
    
    # Check for new window
    if len(driver.window_handles) > 1:
        print("✅ New window detected, switching...")
        driver.switch_to.window(driver.window_handles[-1])
        time.sleep(3)
    
    response = input("\n❓ SCREEN 5 OK? (y to continue): ")
    if response.lower() != 'y':
        input("Browser staying open. Press Enter when ready to close...")
        driver.quit()
        return
    
    # SCREEN 6: Select STEP
    print("\n📺 SCREEN 6: Looking for STEP selection...")
    
    # Show what's available
    buttons = driver.find_elements(By.TAG_NAME, "button")
    print("Available buttons:")
    for i, btn in enumerate(buttons[:10]):
        try:
            text = btn.text.strip()
            visible = btn.is_displayed()
            if text:
                print(f"  {i}: '{text}' (visible={visible})")
        except:
            continue
    
    # Look for STEP
    step_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP')]")
    if step_elements:
        for elem in step_elements:
            if elem.is_displayed():
                print(f"Found STEP element: {elem.text}")
                driver.execute_script("arguments[0].click();", elem)
                time.sleep(3)
                print("✅ Clicked STEP")
                break
    else:
        print("❌ No STEP elements found")
    
    response = input("\n❓ SCREEN 6 OK? Selected STEP? (y to continue): ")
    if response.lower() != 'y':
        input("Browser staying open. Press Enter when ready to close...")
        driver.quit()
        return
    
    # SCREEN 7: Download
    print("\n📺 SCREEN 7: Looking for download button...")
    
    download_elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')]")
    if download_elements:
        for elem in download_elements:
            if elem.is_displayed():
                print(f"Found download: {elem.text}")
                driver.execute_script("arguments[0].click();", elem)
                time.sleep(5)
                print("✅ Clicked download")
                break
    else:
        print("❌ No download button found")
    
    print(f"\nCurrent URL: {driver.current_url}")
    
    # Keep browser open
    print("\n✅ WORKFLOW COMPLETE")
    print("Browser will stay open until you close it.")
    
    while True:
        response = input("\nType 'close' to close browser, or Enter to keep it open: ")
        if response.lower() == 'close':
            driver.quit()
            print("✅ Browser closed")
            break
        else:
            print("Browser staying open...")

if __name__ == "__main__":
    one_screen_never_close()
