#!/usr/bin/env python3
"""
Debug what's on the part details page to find the download button
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def debug_part_page():
    print("🔍 DEBUGGING PART DETAILS PAGE")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    prefs = {
        "download.default_directory": os.path.abspath('3d'),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick login and navigation to part page
        print("🔸 STEP 1: Login and navigate to LM358N...")
        
        # Homepage
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(3)
        
        # Click LOGIN
        login_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='Login']")
        if login_links:
            login_links[0].click()
            time.sleep(3)
        
        # Fill login
        email_inputs = driver.find_elements(By.XPATH, "//input[contains(@placeholder, 'Email')]")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        if email_inputs and password_inputs:
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            
            email_inputs[0].send_keys(credentials['UltraLibrarian']['email'])
            password_inputs[0].send_keys(credentials['UltraLibrarian']['password'])
            
            login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')]")
            if login_buttons:
                login_buttons[0].click()
                time.sleep(5)
        
        # Search
        search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i], input[type='text'], input[type='search']")
        if search_inputs:
            search_input = None
            for inp in search_inputs:
                if inp.is_displayed() and inp.is_enabled():
                    search_input = inp
                    break
            
            if search_input:
                search_input.send_keys("LM358N")
                search_input.send_keys(Keys.RETURN)
                time.sleep(5)
        
        # Click part
        part_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'LM358N')]")
        if part_links:
            part_links[0].click()
            time.sleep(8)
        
        print(f"✅ On part page: {driver.current_url}")
        
        # NOW DEBUG THE PART PAGE
        print("\n🔍 ANALYZING PART DETAILS PAGE...")
        
        # Take screenshot
        driver.save_screenshot("debug_part_details.png")
        print("📸 Screenshot: debug_part_details.png")
        
        # Look for ALL buttons
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\n📋 Found {len(all_buttons)} buttons:")
        
        for i, button in enumerate(all_buttons, 1):
            try:
                if button.is_displayed():
                    text = button.text.strip()
                    classes = button.get_attribute('class') or ''
                    onclick = button.get_attribute('onclick') or ''
                    print(f"{i}. Text: '{text}' | Class: '{classes}' | OnClick: '{onclick[:50]}'")
            except:
                print(f"{i}. [Error reading button]")
        
        # Look for ALL links
        all_links = driver.find_elements(By.TAG_NAME, "a")
        download_links = []
        
        for link in all_links:
            try:
                if link.is_displayed():
                    text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    if any(word in text.lower() for word in ['download', 'get', 'save']) or \
                       any(word in href.lower() for word in ['download', 'get']):
                        download_links.append({
                            'text': text,
                            'href': href
                        })
            except:
                continue
        
        if download_links:
            print(f"\n✅ Found {len(download_links)} download-related links:")
            for i, link in enumerate(download_links, 1):
                print(f"{i}. Text: '{link['text']}' | Href: '{link['href'][:80]}'")
        else:
            print("\n❌ No download-related links found")
        
        # Look for specific download selectors
        print(f"\n🔍 Testing specific download selectors...")
        
        selectors = [
            ("xpath", "//button[contains(text(), 'Download')]"),
            ("xpath", "//a[contains(text(), 'Download')]"),
            ("xpath", "//button[contains(text(), 'Get')]"),
            ("xpath", "//a[contains(text(), 'Get')]"),
            ("css", "button[class*='download']"),
            ("css", "a[class*='download']"),
            ("css", "[data-action*='download']"),
        ]
        
        for selector_type, selector in selectors:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    print(f"✅ {selector}: Found {len(elements)} elements")
                    for i, elem in enumerate(elements[:3], 1):
                        if elem.is_displayed():
                            print(f"   {i}. Text: '{elem.text}' | Visible: True")
                else:
                    print(f"❌ {selector}: No elements found")
            except Exception as e:
                print(f"❌ {selector}: Error - {e}")
        
        print(f"\n⏸️ MANUAL CHECK:")
        print(f"1. Look at the browser window")
        print(f"2. Can you see a Download button or link?")
        print(f"3. What does the page look like?")
        
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_part_page()
