ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */
/* OPTION: strings as raw bytes, not using required /X/ escapes */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'SOT-23',
/* time_stamp */ '2020-07-15T16:23:13+08:00',
/* author */ ('Wei Wu'),
/* organization */ ('Diodes'),
/* preprocessor_version */ 'ST-DEVELOPER v16.7',
/* originating_system */ 'DEX',
/* authorisation */ $);

FILE_SCHEMA (('AUTOMOTIVE_DESIGN {1 0 10303 214 3 1 1}'));
ENDSEC;

DATA;
#10=PROPERTY_DEFINITION_REPRESENTATION(#14,#12);
#11=PROPERTY_DEFINITION_REPRESENTATION(#15,#13);
#12=REPRESENTATION('',(#16),#2556);
#13=REPRESENTATION('',(#17),#2556);
#14=PROPERTY_DEFINITION('pmi validation property','',#2569);
#15=PROPERTY_DEFINITION('pmi validation property','',#2569);
#16=VALUE_REPRESENTATION_ITEM('number of annotations',COUNT_MEASURE(0.));
#17=VALUE_REPRESENTATION_ITEM('number of views',COUNT_MEASURE(0.));
#18=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#26,#2572);
#19=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#27,#2574);
#20=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#28,#2575);
#21=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#29,#2577);
#22=NEXT_ASSEMBLY_USAGE_OCCURRENCE('CP01','CP01','CP01',#2579,#2580,'');
#23=NEXT_ASSEMBLY_USAGE_OCCURRENCE('CP02','CP02','CP02',#2579,#2581,'');
#24=NEXT_ASSEMBLY_USAGE_OCCURRENCE('COMPOUND','COMPOUND','COMPOUND',#2578,
#2579,'');
#25=NEXT_ASSEMBLY_USAGE_OCCURRENCE('LDF','LDF','LDF',#2578,#2582,'');
#26=(
REPRESENTATION_RELATIONSHIP(' ',' ',#1634,#1635)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#30)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#27=(
REPRESENTATION_RELATIONSHIP(' ',' ',#1636,#1635)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#31)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#28=(
REPRESENTATION_RELATIONSHIP(' ',' ',#1635,#1637)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#32)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#29=(
REPRESENTATION_RELATIONSHIP(' ',' ',#1638,#1637)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#33)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#30=ITEM_DEFINED_TRANSFORMATION(' ',' ',#1639,#1674);
#31=ITEM_DEFINED_TRANSFORMATION(' ',' ',#1639,#1709);
#32=ITEM_DEFINED_TRANSFORMATION(' ',' ',#1639,#1710);
#33=ITEM_DEFINED_TRANSFORMATION(' ',' ',#1639,#1777);
#34=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1634,#37);
#35=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1636,#38);
#36=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1638,#39);
#37=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1624),#2558);
#38=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1625),#2559);
#39=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1626,#1627,#1628),#2560);
#40=SPHERICAL_SURFACE('',#1652,0.1);
#41=SPHERICAL_SURFACE('',#1663,0.1);
#42=SPHERICAL_SURFACE('',#1665,0.1);
#43=SPHERICAL_SURFACE('',#1671,0.1);
#44=SPHERICAL_SURFACE('',#1687,0.1);
#45=SPHERICAL_SURFACE('',#1698,0.1);
#46=SPHERICAL_SURFACE('',#1700,0.1);
#47=SPHERICAL_SURFACE('',#1706,0.1);
#48=CIRCLE('',#1651,0.1);
#49=CIRCLE('',#1653,0.1);
#50=CIRCLE('',#1654,0.1);
#51=CIRCLE('',#1656,0.1);
#52=CIRCLE('',#1658,0.1);
#53=CIRCLE('',#1660,0.1);
#54=CIRCLE('',#1662,0.1);
#55=CIRCLE('',#1664,0.1);
#56=CIRCLE('',#1666,0.1);
#57=CIRCLE('',#1668,0.1);
#58=CIRCLE('',#1670,0.1);
#59=CIRCLE('',#1672,0.1);
#60=CIRCLE('',#1686,0.1);
#61=CIRCLE('',#1688,0.1);
#62=CIRCLE('',#1689,0.1);
#63=CIRCLE('',#1691,0.1);
#64=CIRCLE('',#1693,0.1);
#65=CIRCLE('',#1695,0.1);
#66=CIRCLE('',#1697,0.1);
#67=CIRCLE('',#1699,0.1);
#68=CIRCLE('',#1701,0.1);
#69=CIRCLE('',#1703,0.1);
#70=CIRCLE('',#1705,0.1);
#71=CIRCLE('',#1707,0.1);
#72=CIRCLE('',#1712,0.100000000000027);
#73=CIRCLE('',#1713,0.210000000000014);
#74=CIRCLE('',#1714,0.0999999999999946);
#75=CIRCLE('',#1715,0.21000000000003);
#76=CIRCLE('',#1718,0.100000000000027);
#77=CIRCLE('',#1721,0.210000000000014);
#78=CIRCLE('',#1726,0.0999999999999946);
#79=CIRCLE('',#1729,0.21000000000003);
#80=CIRCLE('',#1734,0.21000000000003);
#81=CIRCLE('',#1735,0.0999999999999946);
#82=CIRCLE('',#1736,0.210000000000014);
#83=CIRCLE('',#1737,0.100000000000027);
#84=CIRCLE('',#1740,0.100000000000027);
#85=CIRCLE('',#1743,0.210000000000014);
#86=CIRCLE('',#1748,0.0999999999999946);
#87=CIRCLE('',#1751,0.21000000000003);
#88=CIRCLE('',#1757,0.210000000000026);
#89=CIRCLE('',#1758,0.210000000000026);
#90=CIRCLE('',#1763,0.100000000000022);
#91=CIRCLE('',#1764,0.100000000000022);
#92=CIRCLE('',#1767,0.20999999999995);
#93=CIRCLE('',#1768,0.20999999999995);
#94=CIRCLE('',#1772,0.0999999999999751);
#95=CIRCLE('',#1773,0.0999999999999751);
#96=CYLINDRICAL_SURFACE('',#1650,0.1);
#97=CYLINDRICAL_SURFACE('',#1655,0.1);
#98=CYLINDRICAL_SURFACE('',#1657,0.1);
#99=CYLINDRICAL_SURFACE('',#1659,0.1);
#100=CYLINDRICAL_SURFACE('',#1661,0.1);
#101=CYLINDRICAL_SURFACE('',#1667,0.1);
#102=CYLINDRICAL_SURFACE('',#1669,0.1);
#103=CYLINDRICAL_SURFACE('',#1673,0.1);
#104=CYLINDRICAL_SURFACE('',#1685,0.1);
#105=CYLINDRICAL_SURFACE('',#1690,0.1);
#106=CYLINDRICAL_SURFACE('',#1692,0.1);
#107=CYLINDRICAL_SURFACE('',#1694,0.1);
#108=CYLINDRICAL_SURFACE('',#1696,0.1);
#109=CYLINDRICAL_SURFACE('',#1702,0.1);
#110=CYLINDRICAL_SURFACE('',#1704,0.1);
#111=CYLINDRICAL_SURFACE('',#1708,0.1);
#112=CYLINDRICAL_SURFACE('',#1717,0.100000000000027);
#113=CYLINDRICAL_SURFACE('',#1720,0.210000000000014);
#114=CYLINDRICAL_SURFACE('',#1725,0.0999999999999946);
#115=CYLINDRICAL_SURFACE('',#1728,0.21000000000003);
#116=CYLINDRICAL_SURFACE('',#1739,0.100000000000027);
#117=CYLINDRICAL_SURFACE('',#1742,0.210000000000014);
#118=CYLINDRICAL_SURFACE('',#1747,0.0999999999999946);
#119=CYLINDRICAL_SURFACE('',#1750,0.21000000000003);
#120=CYLINDRICAL_SURFACE('',#1756,0.210000000000026);
#121=CYLINDRICAL_SURFACE('',#1762,0.100000000000022);
#122=CYLINDRICAL_SURFACE('',#1766,0.20999999999995);
#123=CYLINDRICAL_SURFACE('',#1771,0.0999999999999751);
#124=ELLIPSE('',#1644,0.101496408645441,0.1);
#125=ELLIPSE('',#1645,0.101496408645441,0.1);
#126=ELLIPSE('',#1646,0.101496408645441,0.1);
#127=ELLIPSE('',#1647,0.101496408645441,0.1);
#128=ELLIPSE('',#1677,0.10149640864544,0.1);
#129=ELLIPSE('',#1678,0.101496408645439,0.1);
#130=ELLIPSE('',#1679,0.101496408645439,0.1);
#131=ELLIPSE('',#1680,0.10149640864544,0.1);
#132=ORIENTED_EDGE('',*,*,#492,.T.);
#133=ORIENTED_EDGE('',*,*,#493,.T.);
#134=ORIENTED_EDGE('',*,*,#494,.T.);
#135=ORIENTED_EDGE('',*,*,#495,.T.);
#136=ORIENTED_EDGE('',*,*,#496,.T.);
#137=ORIENTED_EDGE('',*,*,#497,.T.);
#138=ORIENTED_EDGE('',*,*,#498,.T.);
#139=ORIENTED_EDGE('',*,*,#499,.T.);
#140=ORIENTED_EDGE('',*,*,#500,.T.);
#141=ORIENTED_EDGE('',*,*,#501,.T.);
#142=ORIENTED_EDGE('',*,*,#502,.F.);
#143=ORIENTED_EDGE('',*,*,#503,.T.);
#144=ORIENTED_EDGE('',*,*,#504,.T.);
#145=ORIENTED_EDGE('',*,*,#505,.T.);
#146=ORIENTED_EDGE('',*,*,#492,.F.);
#147=ORIENTED_EDGE('',*,*,#506,.T.);
#148=ORIENTED_EDGE('',*,*,#507,.F.);
#149=ORIENTED_EDGE('',*,*,#508,.T.);
#150=ORIENTED_EDGE('',*,*,#502,.T.);
#151=ORIENTED_EDGE('',*,*,#509,.T.);
#152=ORIENTED_EDGE('',*,*,#510,.T.);
#153=ORIENTED_EDGE('',*,*,#511,.T.);
#154=ORIENTED_EDGE('',*,*,#504,.F.);
#155=ORIENTED_EDGE('',*,*,#512,.T.);
#156=ORIENTED_EDGE('',*,*,#507,.T.);
#157=ORIENTED_EDGE('',*,*,#513,.T.);
#158=ORIENTED_EDGE('',*,*,#514,.T.);
#159=ORIENTED_EDGE('',*,*,#515,.T.);
#160=ORIENTED_EDGE('',*,*,#516,.F.);
#161=ORIENTED_EDGE('',*,*,#503,.F.);
#162=ORIENTED_EDGE('',*,*,#508,.F.);
#163=ORIENTED_EDGE('',*,*,#515,.F.);
#164=ORIENTED_EDGE('',*,*,#517,.F.);
#165=ORIENTED_EDGE('',*,*,#516,.T.);
#166=ORIENTED_EDGE('',*,*,#518,.F.);
#167=ORIENTED_EDGE('',*,*,#506,.F.);
#168=ORIENTED_EDGE('',*,*,#495,.F.);
#169=ORIENTED_EDGE('',*,*,#519,.F.);
#170=ORIENTED_EDGE('',*,*,#513,.F.);
#171=ORIENTED_EDGE('',*,*,#518,.T.);
#172=ORIENTED_EDGE('',*,*,#514,.F.);
#173=ORIENTED_EDGE('',*,*,#520,.F.);
#174=ORIENTED_EDGE('',*,*,#496,.F.);
#175=ORIENTED_EDGE('',*,*,#517,.T.);
#176=ORIENTED_EDGE('',*,*,#499,.F.);
#177=ORIENTED_EDGE('',*,*,#521,.F.);
#178=ORIENTED_EDGE('',*,*,#500,.F.);
#179=ORIENTED_EDGE('',*,*,#509,.F.);
#180=ORIENTED_EDGE('',*,*,#501,.F.);
#181=ORIENTED_EDGE('',*,*,#522,.F.);
#182=ORIENTED_EDGE('',*,*,#512,.F.);
#183=ORIENTED_EDGE('',*,*,#520,.T.);
#184=ORIENTED_EDGE('',*,*,#519,.T.);
#185=ORIENTED_EDGE('',*,*,#523,.F.);
#186=ORIENTED_EDGE('',*,*,#522,.T.);
#187=ORIENTED_EDGE('',*,*,#521,.T.);
#188=ORIENTED_EDGE('',*,*,#524,.F.);
#189=ORIENTED_EDGE('',*,*,#523,.T.);
#190=ORIENTED_EDGE('',*,*,#494,.F.);
#191=ORIENTED_EDGE('',*,*,#525,.F.);
#192=ORIENTED_EDGE('',*,*,#497,.F.);
#193=ORIENTED_EDGE('',*,*,#524,.T.);
#194=ORIENTED_EDGE('',*,*,#498,.F.);
#195=ORIENTED_EDGE('',*,*,#526,.F.);
#196=ORIENTED_EDGE('',*,*,#510,.F.);
#197=ORIENTED_EDGE('',*,*,#526,.T.);
#198=ORIENTED_EDGE('',*,*,#525,.T.);
#199=ORIENTED_EDGE('',*,*,#527,.F.);
#200=ORIENTED_EDGE('',*,*,#505,.F.);
#201=ORIENTED_EDGE('',*,*,#511,.F.);
#202=ORIENTED_EDGE('',*,*,#527,.T.);
#203=ORIENTED_EDGE('',*,*,#493,.F.);
#204=ORIENTED_EDGE('',*,*,#528,.T.);
#205=ORIENTED_EDGE('',*,*,#529,.T.);
#206=ORIENTED_EDGE('',*,*,#530,.T.);
#207=ORIENTED_EDGE('',*,*,#531,.T.);
#208=ORIENTED_EDGE('',*,*,#532,.T.);
#209=ORIENTED_EDGE('',*,*,#533,.T.);
#210=ORIENTED_EDGE('',*,*,#534,.F.);
#211=ORIENTED_EDGE('',*,*,#535,.T.);
#212=ORIENTED_EDGE('',*,*,#528,.F.);
#213=ORIENTED_EDGE('',*,*,#536,.T.);
#214=ORIENTED_EDGE('',*,*,#537,.T.);
#215=ORIENTED_EDGE('',*,*,#538,.T.);
#216=ORIENTED_EDGE('',*,*,#539,.T.);
#217=ORIENTED_EDGE('',*,*,#540,.T.);
#218=ORIENTED_EDGE('',*,*,#532,.F.);
#219=ORIENTED_EDGE('',*,*,#541,.T.);
#220=ORIENTED_EDGE('',*,*,#542,.T.);
#221=ORIENTED_EDGE('',*,*,#543,.T.);
#222=ORIENTED_EDGE('',*,*,#544,.T.);
#223=ORIENTED_EDGE('',*,*,#545,.T.);
#224=ORIENTED_EDGE('',*,*,#534,.T.);
#225=ORIENTED_EDGE('',*,*,#546,.T.);
#226=ORIENTED_EDGE('',*,*,#547,.T.);
#227=ORIENTED_EDGE('',*,*,#548,.T.);
#228=ORIENTED_EDGE('',*,*,#549,.T.);
#229=ORIENTED_EDGE('',*,*,#550,.T.);
#230=ORIENTED_EDGE('',*,*,#537,.F.);
#231=ORIENTED_EDGE('',*,*,#551,.T.);
#232=ORIENTED_EDGE('',*,*,#552,.F.);
#233=ORIENTED_EDGE('',*,*,#551,.F.);
#234=ORIENTED_EDGE('',*,*,#536,.F.);
#235=ORIENTED_EDGE('',*,*,#531,.F.);
#236=ORIENTED_EDGE('',*,*,#553,.F.);
#237=ORIENTED_EDGE('',*,*,#552,.T.);
#238=ORIENTED_EDGE('',*,*,#554,.F.);
#239=ORIENTED_EDGE('',*,*,#535,.F.);
#240=ORIENTED_EDGE('',*,*,#548,.F.);
#241=ORIENTED_EDGE('',*,*,#555,.F.);
#242=ORIENTED_EDGE('',*,*,#529,.F.);
#243=ORIENTED_EDGE('',*,*,#554,.T.);
#244=ORIENTED_EDGE('',*,*,#530,.F.);
#245=ORIENTED_EDGE('',*,*,#556,.F.);
#246=ORIENTED_EDGE('',*,*,#545,.F.);
#247=ORIENTED_EDGE('',*,*,#553,.T.);
#248=ORIENTED_EDGE('',*,*,#544,.F.);
#249=ORIENTED_EDGE('',*,*,#557,.F.);
#250=ORIENTED_EDGE('',*,*,#549,.F.);
#251=ORIENTED_EDGE('',*,*,#538,.F.);
#252=ORIENTED_EDGE('',*,*,#550,.F.);
#253=ORIENTED_EDGE('',*,*,#558,.F.);
#254=ORIENTED_EDGE('',*,*,#541,.F.);
#255=ORIENTED_EDGE('',*,*,#556,.T.);
#256=ORIENTED_EDGE('',*,*,#555,.T.);
#257=ORIENTED_EDGE('',*,*,#559,.F.);
#258=ORIENTED_EDGE('',*,*,#558,.T.);
#259=ORIENTED_EDGE('',*,*,#557,.T.);
#260=ORIENTED_EDGE('',*,*,#560,.F.);
#261=ORIENTED_EDGE('',*,*,#559,.T.);
#262=ORIENTED_EDGE('',*,*,#547,.F.);
#263=ORIENTED_EDGE('',*,*,#561,.F.);
#264=ORIENTED_EDGE('',*,*,#542,.F.);
#265=ORIENTED_EDGE('',*,*,#560,.T.);
#266=ORIENTED_EDGE('',*,*,#543,.F.);
#267=ORIENTED_EDGE('',*,*,#562,.F.);
#268=ORIENTED_EDGE('',*,*,#539,.F.);
#269=ORIENTED_EDGE('',*,*,#562,.T.);
#270=ORIENTED_EDGE('',*,*,#561,.T.);
#271=ORIENTED_EDGE('',*,*,#563,.F.);
#272=ORIENTED_EDGE('',*,*,#563,.T.);
#273=ORIENTED_EDGE('',*,*,#546,.F.);
#274=ORIENTED_EDGE('',*,*,#533,.F.);
#275=ORIENTED_EDGE('',*,*,#540,.F.);
#276=ORIENTED_EDGE('',*,*,#564,.F.);
#277=ORIENTED_EDGE('',*,*,#565,.F.);
#278=ORIENTED_EDGE('',*,*,#566,.F.);
#279=ORIENTED_EDGE('',*,*,#567,.F.);
#280=ORIENTED_EDGE('',*,*,#568,.F.);
#281=ORIENTED_EDGE('',*,*,#569,.F.);
#282=ORIENTED_EDGE('',*,*,#570,.F.);
#283=ORIENTED_EDGE('',*,*,#571,.F.);
#284=ORIENTED_EDGE('',*,*,#572,.F.);
#285=ORIENTED_EDGE('',*,*,#573,.F.);
#286=ORIENTED_EDGE('',*,*,#574,.F.);
#287=ORIENTED_EDGE('',*,*,#575,.F.);
#288=ORIENTED_EDGE('',*,*,#576,.F.);
#289=ORIENTED_EDGE('',*,*,#577,.F.);
#290=ORIENTED_EDGE('',*,*,#578,.F.);
#291=ORIENTED_EDGE('',*,*,#566,.T.);
#292=ORIENTED_EDGE('',*,*,#579,.F.);
#293=ORIENTED_EDGE('',*,*,#580,.F.);
#294=ORIENTED_EDGE('',*,*,#567,.T.);
#295=ORIENTED_EDGE('',*,*,#578,.T.);
#296=ORIENTED_EDGE('',*,*,#581,.F.);
#297=ORIENTED_EDGE('',*,*,#582,.F.);
#298=ORIENTED_EDGE('',*,*,#568,.T.);
#299=ORIENTED_EDGE('',*,*,#580,.T.);
#300=ORIENTED_EDGE('',*,*,#583,.F.);
#301=ORIENTED_EDGE('',*,*,#584,.F.);
#302=ORIENTED_EDGE('',*,*,#569,.T.);
#303=ORIENTED_EDGE('',*,*,#582,.T.);
#304=ORIENTED_EDGE('',*,*,#585,.F.);
#305=ORIENTED_EDGE('',*,*,#586,.F.);
#306=ORIENTED_EDGE('',*,*,#570,.T.);
#307=ORIENTED_EDGE('',*,*,#584,.T.);
#308=ORIENTED_EDGE('',*,*,#587,.F.);
#309=ORIENTED_EDGE('',*,*,#588,.F.);
#310=ORIENTED_EDGE('',*,*,#571,.T.);
#311=ORIENTED_EDGE('',*,*,#586,.T.);
#312=ORIENTED_EDGE('',*,*,#589,.F.);
#313=ORIENTED_EDGE('',*,*,#590,.F.);
#314=ORIENTED_EDGE('',*,*,#572,.T.);
#315=ORIENTED_EDGE('',*,*,#588,.T.);
#316=ORIENTED_EDGE('',*,*,#591,.F.);
#317=ORIENTED_EDGE('',*,*,#592,.F.);
#318=ORIENTED_EDGE('',*,*,#573,.T.);
#319=ORIENTED_EDGE('',*,*,#590,.T.);
#320=ORIENTED_EDGE('',*,*,#593,.F.);
#321=ORIENTED_EDGE('',*,*,#594,.F.);
#322=ORIENTED_EDGE('',*,*,#574,.T.);
#323=ORIENTED_EDGE('',*,*,#592,.T.);
#324=ORIENTED_EDGE('',*,*,#595,.F.);
#325=ORIENTED_EDGE('',*,*,#596,.F.);
#326=ORIENTED_EDGE('',*,*,#575,.T.);
#327=ORIENTED_EDGE('',*,*,#594,.T.);
#328=ORIENTED_EDGE('',*,*,#597,.F.);
#329=ORIENTED_EDGE('',*,*,#564,.T.);
#330=ORIENTED_EDGE('',*,*,#596,.T.);
#331=ORIENTED_EDGE('',*,*,#598,.F.);
#332=ORIENTED_EDGE('',*,*,#576,.T.);
#333=ORIENTED_EDGE('',*,*,#565,.T.);
#334=ORIENTED_EDGE('',*,*,#597,.T.);
#335=ORIENTED_EDGE('',*,*,#599,.F.);
#336=ORIENTED_EDGE('',*,*,#595,.T.);
#337=ORIENTED_EDGE('',*,*,#593,.T.);
#338=ORIENTED_EDGE('',*,*,#591,.T.);
#339=ORIENTED_EDGE('',*,*,#589,.T.);
#340=ORIENTED_EDGE('',*,*,#587,.T.);
#341=ORIENTED_EDGE('',*,*,#585,.T.);
#342=ORIENTED_EDGE('',*,*,#583,.T.);
#343=ORIENTED_EDGE('',*,*,#581,.T.);
#344=ORIENTED_EDGE('',*,*,#579,.T.);
#345=ORIENTED_EDGE('',*,*,#577,.T.);
#346=ORIENTED_EDGE('',*,*,#599,.T.);
#347=ORIENTED_EDGE('',*,*,#598,.T.);
#348=ORIENTED_EDGE('',*,*,#600,.T.);
#349=ORIENTED_EDGE('',*,*,#601,.T.);
#350=ORIENTED_EDGE('',*,*,#602,.T.);
#351=ORIENTED_EDGE('',*,*,#603,.T.);
#352=ORIENTED_EDGE('',*,*,#604,.T.);
#353=ORIENTED_EDGE('',*,*,#605,.T.);
#354=ORIENTED_EDGE('',*,*,#606,.T.);
#355=ORIENTED_EDGE('',*,*,#607,.T.);
#356=ORIENTED_EDGE('',*,*,#608,.T.);
#357=ORIENTED_EDGE('',*,*,#609,.T.);
#358=ORIENTED_EDGE('',*,*,#610,.T.);
#359=ORIENTED_EDGE('',*,*,#611,.T.);
#360=ORIENTED_EDGE('',*,*,#612,.F.);
#361=ORIENTED_EDGE('',*,*,#613,.F.);
#362=ORIENTED_EDGE('',*,*,#611,.F.);
#363=ORIENTED_EDGE('',*,*,#614,.F.);
#364=ORIENTED_EDGE('',*,*,#615,.F.);
#365=ORIENTED_EDGE('',*,*,#614,.T.);
#366=ORIENTED_EDGE('',*,*,#610,.F.);
#367=ORIENTED_EDGE('',*,*,#616,.F.);
#368=ORIENTED_EDGE('',*,*,#617,.F.);
#369=ORIENTED_EDGE('',*,*,#616,.T.);
#370=ORIENTED_EDGE('',*,*,#609,.F.);
#371=ORIENTED_EDGE('',*,*,#618,.F.);
#372=ORIENTED_EDGE('',*,*,#619,.F.);
#373=ORIENTED_EDGE('',*,*,#618,.T.);
#374=ORIENTED_EDGE('',*,*,#608,.F.);
#375=ORIENTED_EDGE('',*,*,#620,.F.);
#376=ORIENTED_EDGE('',*,*,#621,.F.);
#377=ORIENTED_EDGE('',*,*,#620,.T.);
#378=ORIENTED_EDGE('',*,*,#607,.F.);
#379=ORIENTED_EDGE('',*,*,#622,.F.);
#380=ORIENTED_EDGE('',*,*,#623,.F.);
#381=ORIENTED_EDGE('',*,*,#622,.T.);
#382=ORIENTED_EDGE('',*,*,#606,.F.);
#383=ORIENTED_EDGE('',*,*,#624,.F.);
#384=ORIENTED_EDGE('',*,*,#625,.F.);
#385=ORIENTED_EDGE('',*,*,#624,.T.);
#386=ORIENTED_EDGE('',*,*,#605,.F.);
#387=ORIENTED_EDGE('',*,*,#626,.F.);
#388=ORIENTED_EDGE('',*,*,#627,.F.);
#389=ORIENTED_EDGE('',*,*,#626,.T.);
#390=ORIENTED_EDGE('',*,*,#604,.F.);
#391=ORIENTED_EDGE('',*,*,#628,.F.);
#392=ORIENTED_EDGE('',*,*,#629,.F.);
#393=ORIENTED_EDGE('',*,*,#628,.T.);
#394=ORIENTED_EDGE('',*,*,#603,.F.);
#395=ORIENTED_EDGE('',*,*,#630,.F.);
#396=ORIENTED_EDGE('',*,*,#631,.F.);
#397=ORIENTED_EDGE('',*,*,#630,.T.);
#398=ORIENTED_EDGE('',*,*,#602,.F.);
#399=ORIENTED_EDGE('',*,*,#632,.F.);
#400=ORIENTED_EDGE('',*,*,#633,.F.);
#401=ORIENTED_EDGE('',*,*,#632,.T.);
#402=ORIENTED_EDGE('',*,*,#601,.F.);
#403=ORIENTED_EDGE('',*,*,#634,.F.);
#404=ORIENTED_EDGE('',*,*,#613,.T.);
#405=ORIENTED_EDGE('',*,*,#635,.T.);
#406=ORIENTED_EDGE('',*,*,#634,.T.);
#407=ORIENTED_EDGE('',*,*,#600,.F.);
#408=ORIENTED_EDGE('',*,*,#631,.T.);
#409=ORIENTED_EDGE('',*,*,#633,.T.);
#410=ORIENTED_EDGE('',*,*,#635,.F.);
#411=ORIENTED_EDGE('',*,*,#612,.T.);
#412=ORIENTED_EDGE('',*,*,#615,.T.);
#413=ORIENTED_EDGE('',*,*,#617,.T.);
#414=ORIENTED_EDGE('',*,*,#619,.T.);
#415=ORIENTED_EDGE('',*,*,#621,.T.);
#416=ORIENTED_EDGE('',*,*,#623,.T.);
#417=ORIENTED_EDGE('',*,*,#625,.T.);
#418=ORIENTED_EDGE('',*,*,#627,.T.);
#419=ORIENTED_EDGE('',*,*,#629,.T.);
#420=ORIENTED_EDGE('',*,*,#636,.F.);
#421=ORIENTED_EDGE('',*,*,#637,.T.);
#422=ORIENTED_EDGE('',*,*,#638,.F.);
#423=ORIENTED_EDGE('',*,*,#639,.F.);
#424=ORIENTED_EDGE('',*,*,#640,.F.);
#425=ORIENTED_EDGE('',*,*,#641,.T.);
#426=ORIENTED_EDGE('',*,*,#642,.F.);
#427=ORIENTED_EDGE('',*,*,#637,.F.);
#428=ORIENTED_EDGE('',*,*,#643,.F.);
#429=ORIENTED_EDGE('',*,*,#644,.T.);
#430=ORIENTED_EDGE('',*,*,#645,.F.);
#431=ORIENTED_EDGE('',*,*,#641,.F.);
#432=ORIENTED_EDGE('',*,*,#646,.F.);
#433=ORIENTED_EDGE('',*,*,#647,.T.);
#434=ORIENTED_EDGE('',*,*,#648,.F.);
#435=ORIENTED_EDGE('',*,*,#644,.F.);
#436=ORIENTED_EDGE('',*,*,#649,.F.);
#437=ORIENTED_EDGE('',*,*,#650,.T.);
#438=ORIENTED_EDGE('',*,*,#651,.F.);
#439=ORIENTED_EDGE('',*,*,#647,.F.);
#440=ORIENTED_EDGE('',*,*,#652,.F.);
#441=ORIENTED_EDGE('',*,*,#653,.T.);
#442=ORIENTED_EDGE('',*,*,#654,.F.);
#443=ORIENTED_EDGE('',*,*,#650,.F.);
#444=ORIENTED_EDGE('',*,*,#655,.F.);
#445=ORIENTED_EDGE('',*,*,#656,.T.);
#446=ORIENTED_EDGE('',*,*,#657,.F.);
#447=ORIENTED_EDGE('',*,*,#653,.F.);
#448=ORIENTED_EDGE('',*,*,#658,.F.);
#449=ORIENTED_EDGE('',*,*,#659,.T.);
#450=ORIENTED_EDGE('',*,*,#660,.F.);
#451=ORIENTED_EDGE('',*,*,#656,.F.);
#452=ORIENTED_EDGE('',*,*,#661,.F.);
#453=ORIENTED_EDGE('',*,*,#662,.F.);
#454=ORIENTED_EDGE('',*,*,#663,.F.);
#455=ORIENTED_EDGE('',*,*,#659,.F.);
#456=ORIENTED_EDGE('',*,*,#664,.F.);
#457=ORIENTED_EDGE('',*,*,#665,.T.);
#458=ORIENTED_EDGE('',*,*,#666,.F.);
#459=ORIENTED_EDGE('',*,*,#667,.F.);
#460=ORIENTED_EDGE('',*,*,#668,.F.);
#461=ORIENTED_EDGE('',*,*,#639,.T.);
#462=ORIENTED_EDGE('',*,*,#669,.F.);
#463=ORIENTED_EDGE('',*,*,#665,.F.);
#464=ORIENTED_EDGE('',*,*,#658,.T.);
#465=ORIENTED_EDGE('',*,*,#655,.T.);
#466=ORIENTED_EDGE('',*,*,#652,.T.);
#467=ORIENTED_EDGE('',*,*,#649,.T.);
#468=ORIENTED_EDGE('',*,*,#646,.T.);
#469=ORIENTED_EDGE('',*,*,#643,.T.);
#470=ORIENTED_EDGE('',*,*,#640,.T.);
#471=ORIENTED_EDGE('',*,*,#636,.T.);
#472=ORIENTED_EDGE('',*,*,#668,.T.);
#473=ORIENTED_EDGE('',*,*,#664,.T.);
#474=ORIENTED_EDGE('',*,*,#670,.T.);
#475=ORIENTED_EDGE('',*,*,#661,.T.);
#476=ORIENTED_EDGE('',*,*,#660,.T.);
#477=ORIENTED_EDGE('',*,*,#663,.T.);
#478=ORIENTED_EDGE('',*,*,#671,.F.);
#479=ORIENTED_EDGE('',*,*,#666,.T.);
#480=ORIENTED_EDGE('',*,*,#669,.T.);
#481=ORIENTED_EDGE('',*,*,#638,.T.);
#482=ORIENTED_EDGE('',*,*,#642,.T.);
#483=ORIENTED_EDGE('',*,*,#645,.T.);
#484=ORIENTED_EDGE('',*,*,#648,.T.);
#485=ORIENTED_EDGE('',*,*,#651,.T.);
#486=ORIENTED_EDGE('',*,*,#654,.T.);
#487=ORIENTED_EDGE('',*,*,#657,.T.);
#488=ORIENTED_EDGE('',*,*,#667,.T.);
#489=ORIENTED_EDGE('',*,*,#671,.T.);
#490=ORIENTED_EDGE('',*,*,#662,.T.);
#491=ORIENTED_EDGE('',*,*,#670,.F.);
#492=EDGE_CURVE('',#672,#673,#784,.T.);
#493=EDGE_CURVE('',#673,#674,#785,.F.);
#494=EDGE_CURVE('',#674,#675,#786,.F.);
#495=EDGE_CURVE('',#675,#672,#787,.T.);
#496=EDGE_CURVE('',#676,#677,#788,.T.);
#497=EDGE_CURVE('',#677,#678,#789,.T.);
#498=EDGE_CURVE('',#678,#679,#790,.F.);
#499=EDGE_CURVE('',#679,#676,#791,.F.);
#500=EDGE_CURVE('',#680,#681,#792,.T.);
#501=EDGE_CURVE('',#681,#682,#793,.F.);
#502=EDGE_CURVE('',#683,#682,#794,.T.);
#503=EDGE_CURVE('',#683,#680,#795,.T.);
#504=EDGE_CURVE('',#684,#685,#796,.T.);
#505=EDGE_CURVE('',#685,#673,#124,.F.);
#506=EDGE_CURVE('',#672,#686,#125,.F.);
#507=EDGE_CURVE('',#687,#686,#797,.T.);
#508=EDGE_CURVE('',#687,#683,#126,.F.);
#509=EDGE_CURVE('',#682,#684,#127,.F.);
#510=EDGE_CURVE('',#688,#689,#798,.F.);
#511=EDGE_CURVE('',#689,#685,#799,.F.);
#512=EDGE_CURVE('',#684,#688,#800,.F.);
#513=EDGE_CURVE('',#686,#690,#801,.F.);
#514=EDGE_CURVE('',#690,#691,#802,.F.);
#515=EDGE_CURVE('',#691,#687,#803,.F.);
#516=EDGE_CURVE('',#680,#691,#48,.T.);
#517=EDGE_CURVE('',#680,#676,#49,.F.);
#518=EDGE_CURVE('',#676,#691,#50,.F.);
#519=EDGE_CURVE('',#690,#675,#51,.T.);
#520=EDGE_CURVE('',#677,#690,#52,.T.);
#521=EDGE_CURVE('',#681,#679,#53,.T.);
#522=EDGE_CURVE('',#688,#681,#54,.T.);
#523=EDGE_CURVE('',#677,#675,#55,.F.);
#524=EDGE_CURVE('',#688,#679,#56,.F.);
#525=EDGE_CURVE('',#678,#674,#57,.T.);
#526=EDGE_CURVE('',#689,#678,#58,.T.);
#527=EDGE_CURVE('',#689,#674,#59,.F.);
#528=EDGE_CURVE('',#692,#693,#804,.T.);
#529=EDGE_CURVE('',#693,#694,#805,.F.);
#530=EDGE_CURVE('',#694,#695,#806,.F.);
#531=EDGE_CURVE('',#695,#692,#807,.T.);
#532=EDGE_CURVE('',#696,#697,#808,.T.);
#533=EDGE_CURVE('',#697,#698,#128,.T.);
#534=EDGE_CURVE('',#699,#698,#809,.T.);
#535=EDGE_CURVE('',#699,#693,#129,.T.);
#536=EDGE_CURVE('',#692,#700,#130,.T.);
#537=EDGE_CURVE('',#700,#701,#810,.T.);
#538=EDGE_CURVE('',#701,#696,#131,.T.);
#539=EDGE_CURVE('',#702,#703,#811,.T.);
#540=EDGE_CURVE('',#703,#697,#812,.F.);
#541=EDGE_CURVE('',#696,#702,#813,.T.);
#542=EDGE_CURVE('',#704,#705,#814,.F.);
#543=EDGE_CURVE('',#705,#706,#815,.F.);
#544=EDGE_CURVE('',#706,#707,#816,.T.);
#545=EDGE_CURVE('',#707,#704,#817,.T.);
#546=EDGE_CURVE('',#698,#708,#818,.F.);
#547=EDGE_CURVE('',#708,#709,#819,.F.);
#548=EDGE_CURVE('',#709,#699,#820,.F.);
#549=EDGE_CURVE('',#710,#711,#821,.F.);
#550=EDGE_CURVE('',#711,#701,#822,.F.);
#551=EDGE_CURVE('',#700,#710,#823,.F.);
#552=EDGE_CURVE('',#710,#695,#60,.T.);
#553=EDGE_CURVE('',#710,#707,#61,.F.);
#554=EDGE_CURVE('',#707,#695,#62,.F.);
#555=EDGE_CURVE('',#694,#709,#63,.T.);
#556=EDGE_CURVE('',#704,#694,#64,.T.);
#557=EDGE_CURVE('',#711,#706,#65,.T.);
#558=EDGE_CURVE('',#702,#711,#66,.T.);
#559=EDGE_CURVE('',#704,#709,#67,.F.);
#560=EDGE_CURVE('',#702,#706,#68,.F.);
#561=EDGE_CURVE('',#705,#708,#69,.T.);
#562=EDGE_CURVE('',#703,#705,#70,.T.);
#563=EDGE_CURVE('',#703,#708,#71,.F.);
#564=EDGE_CURVE('',#712,#713,#824,.T.);
#565=EDGE_CURVE('',#714,#712,#825,.T.);
#566=EDGE_CURVE('',#715,#714,#826,.T.);
#567=EDGE_CURVE('',#716,#715,#72,.T.);
#568=EDGE_CURVE('',#717,#716,#827,.T.);
#569=EDGE_CURVE('',#718,#717,#73,.T.);
#570=EDGE_CURVE('',#719,#718,#828,.T.);
#571=EDGE_CURVE('',#720,#719,#829,.T.);
#572=EDGE_CURVE('',#721,#720,#830,.T.);
#573=EDGE_CURVE('',#722,#721,#74,.T.);
#574=EDGE_CURVE('',#723,#722,#831,.T.);
#575=EDGE_CURVE('',#713,#723,#75,.T.);
#576=EDGE_CURVE('',#724,#714,#832,.T.);
#577=EDGE_CURVE('',#725,#724,#833,.T.);
#578=EDGE_CURVE('',#715,#725,#834,.T.);
#579=EDGE_CURVE('',#726,#725,#76,.T.);
#580=EDGE_CURVE('',#716,#726,#835,.T.);
#581=EDGE_CURVE('',#727,#726,#836,.T.);
#582=EDGE_CURVE('',#717,#727,#837,.T.);
#583=EDGE_CURVE('',#728,#727,#77,.F.);
#584=EDGE_CURVE('',#718,#728,#838,.T.);
#585=EDGE_CURVE('',#729,#728,#839,.T.);
#586=EDGE_CURVE('',#719,#729,#840,.T.);
#587=EDGE_CURVE('',#730,#729,#841,.T.);
#588=EDGE_CURVE('',#720,#730,#842,.T.);
#589=EDGE_CURVE('',#731,#730,#843,.T.);
#590=EDGE_CURVE('',#721,#731,#844,.T.);
#591=EDGE_CURVE('',#732,#731,#78,.T.);
#592=EDGE_CURVE('',#722,#732,#845,.T.);
#593=EDGE_CURVE('',#733,#732,#846,.T.);
#594=EDGE_CURVE('',#723,#733,#847,.T.);
#595=EDGE_CURVE('',#734,#733,#79,.F.);
#596=EDGE_CURVE('',#713,#734,#848,.T.);
#597=EDGE_CURVE('',#712,#735,#849,.T.);
#598=EDGE_CURVE('',#735,#734,#850,.T.);
#599=EDGE_CURVE('',#724,#735,#851,.T.);
#600=EDGE_CURVE('',#736,#737,#852,.T.);
#601=EDGE_CURVE('',#737,#738,#853,.T.);
#602=EDGE_CURVE('',#738,#739,#80,.T.);
#603=EDGE_CURVE('',#739,#740,#854,.T.);
#604=EDGE_CURVE('',#740,#741,#81,.T.);
#605=EDGE_CURVE('',#741,#742,#855,.T.);
#606=EDGE_CURVE('',#742,#743,#856,.T.);
#607=EDGE_CURVE('',#743,#744,#857,.T.);
#608=EDGE_CURVE('',#744,#745,#82,.T.);
#609=EDGE_CURVE('',#745,#746,#858,.T.);
#610=EDGE_CURVE('',#746,#747,#83,.T.);
#611=EDGE_CURVE('',#747,#736,#859,.T.);
#612=EDGE_CURVE('',#748,#749,#860,.T.);
#613=EDGE_CURVE('',#736,#748,#861,.T.);
#614=EDGE_CURVE('',#749,#747,#862,.T.);
#615=EDGE_CURVE('',#749,#750,#84,.T.);
#616=EDGE_CURVE('',#750,#746,#863,.T.);
#617=EDGE_CURVE('',#750,#751,#864,.T.);
#618=EDGE_CURVE('',#751,#745,#865,.T.);
#619=EDGE_CURVE('',#751,#752,#85,.F.);
#620=EDGE_CURVE('',#752,#744,#866,.T.);
#621=EDGE_CURVE('',#752,#753,#867,.T.);
#622=EDGE_CURVE('',#753,#743,#868,.T.);
#623=EDGE_CURVE('',#753,#754,#869,.T.);
#624=EDGE_CURVE('',#754,#742,#870,.T.);
#625=EDGE_CURVE('',#754,#755,#871,.T.);
#626=EDGE_CURVE('',#755,#741,#872,.T.);
#627=EDGE_CURVE('',#755,#756,#86,.T.);
#628=EDGE_CURVE('',#756,#740,#873,.T.);
#629=EDGE_CURVE('',#756,#757,#874,.T.);
#630=EDGE_CURVE('',#757,#739,#875,.T.);
#631=EDGE_CURVE('',#757,#758,#87,.F.);
#632=EDGE_CURVE('',#758,#738,#876,.T.);
#633=EDGE_CURVE('',#758,#759,#877,.T.);
#634=EDGE_CURVE('',#759,#737,#878,.T.);
#635=EDGE_CURVE('',#748,#759,#879,.T.);
#636=EDGE_CURVE('',#760,#761,#880,.T.);
#637=EDGE_CURVE('',#760,#762,#881,.T.);
#638=EDGE_CURVE('',#763,#762,#882,.T.);
#639=EDGE_CURVE('',#761,#763,#883,.T.);
#640=EDGE_CURVE('',#764,#760,#88,.F.);
#641=EDGE_CURVE('',#764,#765,#884,.T.);
#642=EDGE_CURVE('',#762,#765,#89,.F.);
#643=EDGE_CURVE('',#766,#764,#885,.T.);
#644=EDGE_CURVE('',#766,#767,#886,.T.);
#645=EDGE_CURVE('',#765,#767,#887,.T.);
#646=EDGE_CURVE('',#768,#766,#888,.T.);
#647=EDGE_CURVE('',#768,#769,#889,.T.);
#648=EDGE_CURVE('',#767,#769,#890,.T.);
#649=EDGE_CURVE('',#770,#768,#891,.T.);
#650=EDGE_CURVE('',#770,#771,#892,.T.);
#651=EDGE_CURVE('',#769,#771,#893,.T.);
#652=EDGE_CURVE('',#772,#770,#90,.T.);
#653=EDGE_CURVE('',#772,#773,#894,.T.);
#654=EDGE_CURVE('',#771,#773,#91,.T.);
#655=EDGE_CURVE('',#774,#772,#895,.T.);
#656=EDGE_CURVE('',#774,#775,#896,.T.);
#657=EDGE_CURVE('',#773,#775,#897,.T.);
#658=EDGE_CURVE('',#776,#774,#92,.F.);
#659=EDGE_CURVE('',#776,#777,#898,.T.);
#660=EDGE_CURVE('',#775,#777,#93,.F.);
#661=EDGE_CURVE('',#778,#776,#899,.T.);
#662=EDGE_CURVE('',#779,#778,#900,.T.);
#663=EDGE_CURVE('',#777,#779,#901,.T.);
#664=EDGE_CURVE('',#780,#781,#902,.T.);
#665=EDGE_CURVE('',#780,#782,#903,.T.);
#666=EDGE_CURVE('',#783,#782,#904,.T.);
#667=EDGE_CURVE('',#781,#783,#905,.T.);
#668=EDGE_CURVE('',#761,#780,#94,.T.);
#669=EDGE_CURVE('',#782,#763,#95,.T.);
#670=EDGE_CURVE('',#781,#778,#906,.T.);
#671=EDGE_CURVE('',#783,#779,#907,.T.);
#672=VERTEX_POINT('',#2183);
#673=VERTEX_POINT('',#2184);
#674=VERTEX_POINT('',#2186);
#675=VERTEX_POINT('',#2188);
#676=VERTEX_POINT('',#2192);
#677=VERTEX_POINT('',#2193);
#678=VERTEX_POINT('',#2195);
#679=VERTEX_POINT('',#2197);
#680=VERTEX_POINT('',#2201);
#681=VERTEX_POINT('',#2202);
#682=VERTEX_POINT('',#2204);
#683=VERTEX_POINT('',#2206);
#684=VERTEX_POINT('',#2210);
#685=VERTEX_POINT('',#2211);
#686=VERTEX_POINT('',#2214);
#687=VERTEX_POINT('',#2216);
#688=VERTEX_POINT('',#2221);
#689=VERTEX_POINT('',#2222);
#690=VERTEX_POINT('',#2227);
#691=VERTEX_POINT('',#2229);
#692=VERTEX_POINT('',#2258);
#693=VERTEX_POINT('',#2259);
#694=VERTEX_POINT('',#2261);
#695=VERTEX_POINT('',#2263);
#696=VERTEX_POINT('',#2267);
#697=VERTEX_POINT('',#2268);
#698=VERTEX_POINT('',#2270);
#699=VERTEX_POINT('',#2272);
#700=VERTEX_POINT('',#2275);
#701=VERTEX_POINT('',#2277);
#702=VERTEX_POINT('',#2281);
#703=VERTEX_POINT('',#2282);
#704=VERTEX_POINT('',#2287);
#705=VERTEX_POINT('',#2288);
#706=VERTEX_POINT('',#2290);
#707=VERTEX_POINT('',#2292);
#708=VERTEX_POINT('',#2296);
#709=VERTEX_POINT('',#2298);
#710=VERTEX_POINT('',#2302);
#711=VERTEX_POINT('',#2303);
#712=VERTEX_POINT('',#2334);
#713=VERTEX_POINT('',#2335);
#714=VERTEX_POINT('',#2337);
#715=VERTEX_POINT('',#2339);
#716=VERTEX_POINT('',#2341);
#717=VERTEX_POINT('',#2343);
#718=VERTEX_POINT('',#2345);
#719=VERTEX_POINT('',#2347);
#720=VERTEX_POINT('',#2349);
#721=VERTEX_POINT('',#2351);
#722=VERTEX_POINT('',#2353);
#723=VERTEX_POINT('',#2355);
#724=VERTEX_POINT('',#2359);
#725=VERTEX_POINT('',#2361);
#726=VERTEX_POINT('',#2365);
#727=VERTEX_POINT('',#2369);
#728=VERTEX_POINT('',#2373);
#729=VERTEX_POINT('',#2377);
#730=VERTEX_POINT('',#2381);
#731=VERTEX_POINT('',#2385);
#732=VERTEX_POINT('',#2389);
#733=VERTEX_POINT('',#2393);
#734=VERTEX_POINT('',#2397);
#735=VERTEX_POINT('',#2401);
#736=VERTEX_POINT('',#2408);
#737=VERTEX_POINT('',#2409);
#738=VERTEX_POINT('',#2411);
#739=VERTEX_POINT('',#2413);
#740=VERTEX_POINT('',#2415);
#741=VERTEX_POINT('',#2417);
#742=VERTEX_POINT('',#2419);
#743=VERTEX_POINT('',#2421);
#744=VERTEX_POINT('',#2423);
#745=VERTEX_POINT('',#2425);
#746=VERTEX_POINT('',#2427);
#747=VERTEX_POINT('',#2429);
#748=VERTEX_POINT('',#2433);
#749=VERTEX_POINT('',#2434);
#750=VERTEX_POINT('',#2439);
#751=VERTEX_POINT('',#2443);
#752=VERTEX_POINT('',#2447);
#753=VERTEX_POINT('',#2451);
#754=VERTEX_POINT('',#2455);
#755=VERTEX_POINT('',#2459);
#756=VERTEX_POINT('',#2463);
#757=VERTEX_POINT('',#2467);
#758=VERTEX_POINT('',#2471);
#759=VERTEX_POINT('',#2475);
#760=VERTEX_POINT('',#2482);
#761=VERTEX_POINT('',#2483);
#762=VERTEX_POINT('',#2485);
#763=VERTEX_POINT('',#2487);
#764=VERTEX_POINT('',#2491);
#765=VERTEX_POINT('',#2493);
#766=VERTEX_POINT('',#2497);
#767=VERTEX_POINT('',#2499);
#768=VERTEX_POINT('',#2503);
#769=VERTEX_POINT('',#2505);
#770=VERTEX_POINT('',#2509);
#771=VERTEX_POINT('',#2511);
#772=VERTEX_POINT('',#2515);
#773=VERTEX_POINT('',#2517);
#774=VERTEX_POINT('',#2521);
#775=VERTEX_POINT('',#2523);
#776=VERTEX_POINT('',#2527);
#777=VERTEX_POINT('',#2529);
#778=VERTEX_POINT('',#2533);
#779=VERTEX_POINT('',#2535);
#780=VERTEX_POINT('',#2539);
#781=VERTEX_POINT('',#2540);
#782=VERTEX_POINT('',#2542);
#783=VERTEX_POINT('',#2544);
#784=LINE('',#2182,#908);
#785=LINE('',#2185,#909);
#786=LINE('',#2187,#910);
#787=LINE('',#2189,#911);
#788=LINE('',#2191,#912);
#789=LINE('',#2194,#913);
#790=LINE('',#2196,#914);
#791=LINE('',#2198,#915);
#792=LINE('',#2200,#916);
#793=LINE('',#2203,#917);
#794=LINE('',#2205,#918);
#795=LINE('',#2207,#919);
#796=LINE('',#2209,#920);
#797=LINE('',#2215,#921);
#798=LINE('',#2220,#922);
#799=LINE('',#2223,#923);
#800=LINE('',#2224,#924);
#801=LINE('',#2226,#925);
#802=LINE('',#2228,#926);
#803=LINE('',#2230,#927);
#804=LINE('',#2257,#928);
#805=LINE('',#2260,#929);
#806=LINE('',#2262,#930);
#807=LINE('',#2264,#931);
#808=LINE('',#2266,#932);
#809=LINE('',#2271,#933);
#810=LINE('',#2276,#934);
#811=LINE('',#2280,#935);
#812=LINE('',#2283,#936);
#813=LINE('',#2284,#937);
#814=LINE('',#2286,#938);
#815=LINE('',#2289,#939);
#816=LINE('',#2291,#940);
#817=LINE('',#2293,#941);
#818=LINE('',#2295,#942);
#819=LINE('',#2297,#943);
#820=LINE('',#2299,#944);
#821=LINE('',#2301,#945);
#822=LINE('',#2304,#946);
#823=LINE('',#2305,#947);
#824=LINE('',#2333,#948);
#825=LINE('',#2336,#949);
#826=LINE('',#2338,#950);
#827=LINE('',#2342,#951);
#828=LINE('',#2346,#952);
#829=LINE('',#2348,#953);
#830=LINE('',#2350,#954);
#831=LINE('',#2354,#955);
#832=LINE('',#2358,#956);
#833=LINE('',#2360,#957);
#834=LINE('',#2362,#958);
#835=LINE('',#2366,#959);
#836=LINE('',#2368,#960);
#837=LINE('',#2370,#961);
#838=LINE('',#2374,#962);
#839=LINE('',#2376,#963);
#840=LINE('',#2378,#964);
#841=LINE('',#2380,#965);
#842=LINE('',#2382,#966);
#843=LINE('',#2384,#967);
#844=LINE('',#2386,#968);
#845=LINE('',#2390,#969);
#846=LINE('',#2392,#970);
#847=LINE('',#2394,#971);
#848=LINE('',#2398,#972);
#849=LINE('',#2400,#973);
#850=LINE('',#2402,#974);
#851=LINE('',#2404,#975);
#852=LINE('',#2407,#976);
#853=LINE('',#2410,#977);
#854=LINE('',#2414,#978);
#855=LINE('',#2418,#979);
#856=LINE('',#2420,#980);
#857=LINE('',#2422,#981);
#858=LINE('',#2426,#982);
#859=LINE('',#2430,#983);
#860=LINE('',#2432,#984);
#861=LINE('',#2435,#985);
#862=LINE('',#2436,#986);
#863=LINE('',#2440,#987);
#864=LINE('',#2442,#988);
#865=LINE('',#2444,#989);
#866=LINE('',#2448,#990);
#867=LINE('',#2450,#991);
#868=LINE('',#2452,#992);
#869=LINE('',#2454,#993);
#870=LINE('',#2456,#994);
#871=LINE('',#2458,#995);
#872=LINE('',#2460,#996);
#873=LINE('',#2464,#997);
#874=LINE('',#2466,#998);
#875=LINE('',#2468,#999);
#876=LINE('',#2472,#1000);
#877=LINE('',#2474,#1001);
#878=LINE('',#2476,#1002);
#879=LINE('',#2478,#1003);
#880=LINE('',#2481,#1004);
#881=LINE('',#2484,#1005);
#882=LINE('',#2486,#1006);
#883=LINE('',#2488,#1007);
#884=LINE('',#2492,#1008);
#885=LINE('',#2496,#1009);
#886=LINE('',#2498,#1010);
#887=LINE('',#2500,#1011);
#888=LINE('',#2502,#1012);
#889=LINE('',#2504,#1013);
#890=LINE('',#2506,#1014);
#891=LINE('',#2508,#1015);
#892=LINE('',#2510,#1016);
#893=LINE('',#2512,#1017);
#894=LINE('',#2516,#1018);
#895=LINE('',#2520,#1019);
#896=LINE('',#2522,#1020);
#897=LINE('',#2524,#1021);
#898=LINE('',#2528,#1022);
#899=LINE('',#2532,#1023);
#900=LINE('',#2534,#1024);
#901=LINE('',#2536,#1025);
#902=LINE('',#2538,#1026);
#903=LINE('',#2541,#1027);
#904=LINE('',#2543,#1028);
#905=LINE('',#2545,#1029);
#906=LINE('',#2550,#1030);
#907=LINE('',#2552,#1031);
#908=VECTOR('',#1782,1000.);
#909=VECTOR('',#1783,1000.);
#910=VECTOR('',#1784,1000.);
#911=VECTOR('',#1785,1000.);
#912=VECTOR('',#1788,1000.);
#913=VECTOR('',#1789,1000.);
#914=VECTOR('',#1790,1000.);
#915=VECTOR('',#1791,1000.);
#916=VECTOR('',#1794,1000.);
#917=VECTOR('',#1795,1000.);
#918=VECTOR('',#1796,1000.);
#919=VECTOR('',#1797,1000.);
#920=VECTOR('',#1800,1000.);
#921=VECTOR('',#1805,1000.);
#922=VECTOR('',#1812,1000.);
#923=VECTOR('',#1813,1000.);
#924=VECTOR('',#1814,1000.);
#925=VECTOR('',#1817,1000.);
#926=VECTOR('',#1818,1000.);
#927=VECTOR('',#1819,1000.);
#928=VECTOR('',#1872,1000.);
#929=VECTOR('',#1873,1000.);
#930=VECTOR('',#1874,1000.);
#931=VECTOR('',#1875,1000.);
#932=VECTOR('',#1878,1000.);
#933=VECTOR('',#1881,1000.);
#934=VECTOR('',#1886,1000.);
#935=VECTOR('',#1891,1000.);
#936=VECTOR('',#1892,1000.);
#937=VECTOR('',#1893,1000.);
#938=VECTOR('',#1896,1000.);
#939=VECTOR('',#1897,1000.);
#940=VECTOR('',#1898,1000.);
#941=VECTOR('',#1899,1000.);
#942=VECTOR('',#1902,1000.);
#943=VECTOR('',#1903,1000.);
#944=VECTOR('',#1904,1000.);
#945=VECTOR('',#1907,1000.);
#946=VECTOR('',#1908,1000.);
#947=VECTOR('',#1909,1000.);
#948=VECTOR('',#1964,1000.);
#949=VECTOR('',#1965,1000.);
#950=VECTOR('',#1966,1000.);
#951=VECTOR('',#1969,1000.);
#952=VECTOR('',#1972,1000.);
#953=VECTOR('',#1973,1000.);
#954=VECTOR('',#1974,1000.);
#955=VECTOR('',#1977,1000.);
#956=VECTOR('',#1982,1000.);
#957=VECTOR('',#1983,1000.);
#958=VECTOR('',#1984,1000.);
#959=VECTOR('',#1989,1000.);
#960=VECTOR('',#1992,1000.);
#961=VECTOR('',#1993,1000.);
#962=VECTOR('',#1998,1000.);
#963=VECTOR('',#2001,1000.);
#964=VECTOR('',#2002,1000.);
#965=VECTOR('',#2005,1000.);
#966=VECTOR('',#2006,1000.);
#967=VECTOR('',#2009,1000.);
#968=VECTOR('',#2010,1000.);
#969=VECTOR('',#2015,1000.);
#970=VECTOR('',#2018,1000.);
#971=VECTOR('',#2019,1000.);
#972=VECTOR('',#2024,1000.);
#973=VECTOR('',#2027,1000.);
#974=VECTOR('',#2028,1000.);
#975=VECTOR('',#2031,1000.);
#976=VECTOR('',#2036,1000.);
#977=VECTOR('',#2037,1000.);
#978=VECTOR('',#2040,1000.);
#979=VECTOR('',#2043,1000.);
#980=VECTOR('',#2044,1000.);
#981=VECTOR('',#2045,1000.);
#982=VECTOR('',#2048,1000.);
#983=VECTOR('',#2051,1000.);
#984=VECTOR('',#2054,1000.);
#985=VECTOR('',#2055,1000.);
#986=VECTOR('',#2056,1000.);
#987=VECTOR('',#2061,1000.);
#988=VECTOR('',#2064,1000.);
#989=VECTOR('',#2065,1000.);
#990=VECTOR('',#2070,1000.);
#991=VECTOR('',#2073,1000.);
#992=VECTOR('',#2074,1000.);
#993=VECTOR('',#2077,1000.);
#994=VECTOR('',#2078,1000.);
#995=VECTOR('',#2081,1000.);
#996=VECTOR('',#2082,1000.);
#997=VECTOR('',#2087,1000.);
#998=VECTOR('',#2090,1000.);
#999=VECTOR('',#2091,1000.);
#1000=VECTOR('',#2096,1000.);
#1001=VECTOR('',#2099,1000.);
#1002=VECTOR('',#2100,1000.);
#1003=VECTOR('',#2103,1000.);
#1004=VECTOR('',#2108,1000.);
#1005=VECTOR('',#2109,1000.);
#1006=VECTOR('',#2110,1000.);
#1007=VECTOR('',#2111,1000.);
#1008=VECTOR('',#2116,1000.);
#1009=VECTOR('',#2121,1000.);
#1010=VECTOR('',#2122,1000.);
#1011=VECTOR('',#2123,1000.);
#1012=VECTOR('',#2126,1000.);
#1013=VECTOR('',#2127,1000.);
#1014=VECTOR('',#2128,1000.);
#1015=VECTOR('',#2131,1000.);
#1016=VECTOR('',#2132,1000.);
#1017=VECTOR('',#2133,1000.);
#1018=VECTOR('',#2138,1000.);
#1019=VECTOR('',#2143,1000.);
#1020=VECTOR('',#2144,1000.);
#1021=VECTOR('',#2145,1000.);
#1022=VECTOR('',#2150,1000.);
#1023=VECTOR('',#2155,1000.);
#1024=VECTOR('',#2156,1000.);
#1025=VECTOR('',#2157,1000.);
#1026=VECTOR('',#2160,1000.);
#1027=VECTOR('',#2161,1000.);
#1028=VECTOR('',#2162,1000.);
#1029=VECTOR('',#2163,1000.);
#1030=VECTOR('',#2172,1000.);
#1031=VECTOR('',#2175,1000.);
#1032=EDGE_LOOP('',(#132,#133,#134,#135));
#1033=EDGE_LOOP('',(#136,#137,#138,#139));
#1034=EDGE_LOOP('',(#140,#141,#142,#143));
#1035=EDGE_LOOP('',(#144,#145,#146,#147,#148,#149,#150,#151));
#1036=EDGE_LOOP('',(#152,#153,#154,#155));
#1037=EDGE_LOOP('',(#156,#157,#158,#159));
#1038=EDGE_LOOP('',(#160,#161,#162,#163));
#1039=EDGE_LOOP('',(#164,#165,#166));
#1040=EDGE_LOOP('',(#167,#168,#169,#170));
#1041=EDGE_LOOP('',(#171,#172,#173,#174));
#1042=EDGE_LOOP('',(#175,#176,#177,#178));
#1043=EDGE_LOOP('',(#179,#180,#181,#182));
#1044=EDGE_LOOP('',(#183,#184,#185));
#1045=EDGE_LOOP('',(#186,#187,#188));
#1046=EDGE_LOOP('',(#189,#190,#191,#192));
#1047=EDGE_LOOP('',(#193,#194,#195,#196));
#1048=EDGE_LOOP('',(#197,#198,#199));
#1049=EDGE_LOOP('',(#200,#201,#202,#203));
#1050=EDGE_LOOP('',(#204,#205,#206,#207));
#1051=EDGE_LOOP('',(#208,#209,#210,#211,#212,#213,#214,#215));
#1052=EDGE_LOOP('',(#216,#217,#218,#219));
#1053=EDGE_LOOP('',(#220,#221,#222,#223));
#1054=EDGE_LOOP('',(#224,#225,#226,#227));
#1055=EDGE_LOOP('',(#228,#229,#230,#231));
#1056=EDGE_LOOP('',(#232,#233,#234,#235));
#1057=EDGE_LOOP('',(#236,#237,#238));
#1058=EDGE_LOOP('',(#239,#240,#241,#242));
#1059=EDGE_LOOP('',(#243,#244,#245,#246));
#1060=EDGE_LOOP('',(#247,#248,#249,#250));
#1061=EDGE_LOOP('',(#251,#252,#253,#254));
#1062=EDGE_LOOP('',(#255,#256,#257));
#1063=EDGE_LOOP('',(#258,#259,#260));
#1064=EDGE_LOOP('',(#261,#262,#263,#264));
#1065=EDGE_LOOP('',(#265,#266,#267,#268));
#1066=EDGE_LOOP('',(#269,#270,#271));
#1067=EDGE_LOOP('',(#272,#273,#274,#275));
#1068=EDGE_LOOP('',(#276,#277,#278,#279,#280,#281,#282,#283,#284,#285,#286,
#287));
#1069=EDGE_LOOP('',(#288,#289,#290,#291));
#1070=EDGE_LOOP('',(#292,#293,#294,#295));
#1071=EDGE_LOOP('',(#296,#297,#298,#299));
#1072=EDGE_LOOP('',(#300,#301,#302,#303));
#1073=EDGE_LOOP('',(#304,#305,#306,#307));
#1074=EDGE_LOOP('',(#308,#309,#310,#311));
#1075=EDGE_LOOP('',(#312,#313,#314,#315));
#1076=EDGE_LOOP('',(#316,#317,#318,#319));
#1077=EDGE_LOOP('',(#320,#321,#322,#323));
#1078=EDGE_LOOP('',(#324,#325,#326,#327));
#1079=EDGE_LOOP('',(#328,#329,#330,#331));
#1080=EDGE_LOOP('',(#332,#333,#334,#335));
#1081=EDGE_LOOP('',(#336,#337,#338,#339,#340,#341,#342,#343,#344,#345,#346,
#347));
#1082=EDGE_LOOP('',(#348,#349,#350,#351,#352,#353,#354,#355,#356,#357,#358,
#359));
#1083=EDGE_LOOP('',(#360,#361,#362,#363));
#1084=EDGE_LOOP('',(#364,#365,#366,#367));
#1085=EDGE_LOOP('',(#368,#369,#370,#371));
#1086=EDGE_LOOP('',(#372,#373,#374,#375));
#1087=EDGE_LOOP('',(#376,#377,#378,#379));
#1088=EDGE_LOOP('',(#380,#381,#382,#383));
#1089=EDGE_LOOP('',(#384,#385,#386,#387));
#1090=EDGE_LOOP('',(#388,#389,#390,#391));
#1091=EDGE_LOOP('',(#392,#393,#394,#395));
#1092=EDGE_LOOP('',(#396,#397,#398,#399));
#1093=EDGE_LOOP('',(#400,#401,#402,#403));
#1094=EDGE_LOOP('',(#404,#405,#406,#407));
#1095=EDGE_LOOP('',(#408,#409,#410,#411,#412,#413,#414,#415,#416,#417,#418,
#419));
#1096=EDGE_LOOP('',(#420,#421,#422,#423));
#1097=EDGE_LOOP('',(#424,#425,#426,#427));
#1098=EDGE_LOOP('',(#428,#429,#430,#431));
#1099=EDGE_LOOP('',(#432,#433,#434,#435));
#1100=EDGE_LOOP('',(#436,#437,#438,#439));
#1101=EDGE_LOOP('',(#440,#441,#442,#443));
#1102=EDGE_LOOP('',(#444,#445,#446,#447));
#1103=EDGE_LOOP('',(#448,#449,#450,#451));
#1104=EDGE_LOOP('',(#452,#453,#454,#455));
#1105=EDGE_LOOP('',(#456,#457,#458,#459));
#1106=EDGE_LOOP('',(#460,#461,#462,#463));
#1107=EDGE_LOOP('',(#464,#465,#466,#467,#468,#469,#470,#471,#472,#473,#474,
#475));
#1108=EDGE_LOOP('',(#476,#477,#478,#479,#480,#481,#482,#483,#484,#485,#486,
#487));
#1109=EDGE_LOOP('',(#488,#489,#490,#491));
#1110=FACE_BOUND('',#1032,.T.);
#1111=FACE_BOUND('',#1033,.T.);
#1112=FACE_BOUND('',#1034,.T.);
#1113=FACE_BOUND('',#1035,.T.);
#1114=FACE_BOUND('',#1036,.T.);
#1115=FACE_BOUND('',#1037,.T.);
#1116=FACE_BOUND('',#1038,.T.);
#1117=FACE_BOUND('',#1039,.T.);
#1118=FACE_BOUND('',#1040,.T.);
#1119=FACE_BOUND('',#1041,.T.);
#1120=FACE_BOUND('',#1042,.T.);
#1121=FACE_BOUND('',#1043,.T.);
#1122=FACE_BOUND('',#1044,.T.);
#1123=FACE_BOUND('',#1045,.T.);
#1124=FACE_BOUND('',#1046,.T.);
#1125=FACE_BOUND('',#1047,.T.);
#1126=FACE_BOUND('',#1048,.T.);
#1127=FACE_BOUND('',#1049,.T.);
#1128=FACE_BOUND('',#1050,.T.);
#1129=FACE_BOUND('',#1051,.T.);
#1130=FACE_BOUND('',#1052,.T.);
#1131=FACE_BOUND('',#1053,.T.);
#1132=FACE_BOUND('',#1054,.T.);
#1133=FACE_BOUND('',#1055,.T.);
#1134=FACE_BOUND('',#1056,.T.);
#1135=FACE_BOUND('',#1057,.T.);
#1136=FACE_BOUND('',#1058,.T.);
#1137=FACE_BOUND('',#1059,.T.);
#1138=FACE_BOUND('',#1060,.T.);
#1139=FACE_BOUND('',#1061,.T.);
#1140=FACE_BOUND('',#1062,.T.);
#1141=FACE_BOUND('',#1063,.T.);
#1142=FACE_BOUND('',#1064,.T.);
#1143=FACE_BOUND('',#1065,.T.);
#1144=FACE_BOUND('',#1066,.T.);
#1145=FACE_BOUND('',#1067,.T.);
#1146=FACE_BOUND('',#1068,.T.);
#1147=FACE_BOUND('',#1069,.T.);
#1148=FACE_BOUND('',#1070,.T.);
#1149=FACE_BOUND('',#1071,.T.);
#1150=FACE_BOUND('',#1072,.T.);
#1151=FACE_BOUND('',#1073,.T.);
#1152=FACE_BOUND('',#1074,.T.);
#1153=FACE_BOUND('',#1075,.T.);
#1154=FACE_BOUND('',#1076,.T.);
#1155=FACE_BOUND('',#1077,.T.);
#1156=FACE_BOUND('',#1078,.T.);
#1157=FACE_BOUND('',#1079,.T.);
#1158=FACE_BOUND('',#1080,.T.);
#1159=FACE_BOUND('',#1081,.T.);
#1160=FACE_BOUND('',#1082,.T.);
#1161=FACE_BOUND('',#1083,.T.);
#1162=FACE_BOUND('',#1084,.T.);
#1163=FACE_BOUND('',#1085,.T.);
#1164=FACE_BOUND('',#1086,.T.);
#1165=FACE_BOUND('',#1087,.T.);
#1166=FACE_BOUND('',#1088,.T.);
#1167=FACE_BOUND('',#1089,.T.);
#1168=FACE_BOUND('',#1090,.T.);
#1169=FACE_BOUND('',#1091,.T.);
#1170=FACE_BOUND('',#1092,.T.);
#1171=FACE_BOUND('',#1093,.T.);
#1172=FACE_BOUND('',#1094,.T.);
#1173=FACE_BOUND('',#1095,.T.);
#1174=FACE_BOUND('',#1096,.T.);
#1175=FACE_BOUND('',#1097,.T.);
#1176=FACE_BOUND('',#1098,.T.);
#1177=FACE_BOUND('',#1099,.T.);
#1178=FACE_BOUND('',#1100,.T.);
#1179=FACE_BOUND('',#1101,.T.);
#1180=FACE_BOUND('',#1102,.T.);
#1181=FACE_BOUND('',#1103,.T.);
#1182=FACE_BOUND('',#1104,.T.);
#1183=FACE_BOUND('',#1105,.T.);
#1184=FACE_BOUND('',#1106,.T.);
#1185=FACE_BOUND('',#1107,.T.);
#1186=FACE_BOUND('',#1108,.T.);
#1187=FACE_BOUND('',#1109,.T.);
#1188=PLANE('',#1640);
#1189=PLANE('',#1641);
#1190=PLANE('',#1642);
#1191=PLANE('',#1643);
#1192=PLANE('',#1648);
#1193=PLANE('',#1649);
#1194=PLANE('',#1675);
#1195=PLANE('',#1676);
#1196=PLANE('',#1681);
#1197=PLANE('',#1682);
#1198=PLANE('',#1683);
#1199=PLANE('',#1684);
#1200=PLANE('',#1711);
#1201=PLANE('',#1716);
#1202=PLANE('',#1719);
#1203=PLANE('',#1722);
#1204=PLANE('',#1723);
#1205=PLANE('',#1724);
#1206=PLANE('',#1727);
#1207=PLANE('',#1730);
#1208=PLANE('',#1731);
#1209=PLANE('',#1732);
#1210=PLANE('',#1733);
#1211=PLANE('',#1738);
#1212=PLANE('',#1741);
#1213=PLANE('',#1744);
#1214=PLANE('',#1745);
#1215=PLANE('',#1746);
#1216=PLANE('',#1749);
#1217=PLANE('',#1752);
#1218=PLANE('',#1753);
#1219=PLANE('',#1754);
#1220=PLANE('',#1755);
#1221=PLANE('',#1759);
#1222=PLANE('',#1760);
#1223=PLANE('',#1761);
#1224=PLANE('',#1765);
#1225=PLANE('',#1769);
#1226=PLANE('',#1770);
#1227=PLANE('',#1774);
#1228=PLANE('',#1775);
#1229=PLANE('',#1776);
#1230=ADVANCED_FACE('',(#1110),#1188,.F.);
#1231=ADVANCED_FACE('',(#1111),#1189,.F.);
#1232=ADVANCED_FACE('',(#1112),#1190,.F.);
#1233=ADVANCED_FACE('',(#1113),#1191,.F.);
#1234=ADVANCED_FACE('',(#1114),#1192,.F.);
#1235=ADVANCED_FACE('',(#1115),#1193,.T.);
#1236=ADVANCED_FACE('',(#1116),#96,.T.);
#1237=ADVANCED_FACE('',(#1117),#40,.T.);
#1238=ADVANCED_FACE('',(#1118),#97,.T.);
#1239=ADVANCED_FACE('',(#1119),#98,.T.);
#1240=ADVANCED_FACE('',(#1120),#99,.T.);
#1241=ADVANCED_FACE('',(#1121),#100,.T.);
#1242=ADVANCED_FACE('',(#1122),#41,.T.);
#1243=ADVANCED_FACE('',(#1123),#42,.T.);
#1244=ADVANCED_FACE('',(#1124),#101,.T.);
#1245=ADVANCED_FACE('',(#1125),#102,.T.);
#1246=ADVANCED_FACE('',(#1126),#43,.T.);
#1247=ADVANCED_FACE('',(#1127),#103,.T.);
#1248=ADVANCED_FACE('',(#1128),#1194,.T.);
#1249=ADVANCED_FACE('',(#1129),#1195,.T.);
#1250=ADVANCED_FACE('',(#1130),#1196,.T.);
#1251=ADVANCED_FACE('',(#1131),#1197,.T.);
#1252=ADVANCED_FACE('',(#1132),#1198,.F.);
#1253=ADVANCED_FACE('',(#1133),#1199,.T.);
#1254=ADVANCED_FACE('',(#1134),#104,.T.);
#1255=ADVANCED_FACE('',(#1135),#44,.T.);
#1256=ADVANCED_FACE('',(#1136),#105,.T.);
#1257=ADVANCED_FACE('',(#1137),#106,.T.);
#1258=ADVANCED_FACE('',(#1138),#107,.T.);
#1259=ADVANCED_FACE('',(#1139),#108,.T.);
#1260=ADVANCED_FACE('',(#1140),#45,.T.);
#1261=ADVANCED_FACE('',(#1141),#46,.T.);
#1262=ADVANCED_FACE('',(#1142),#109,.T.);
#1263=ADVANCED_FACE('',(#1143),#110,.T.);
#1264=ADVANCED_FACE('',(#1144),#47,.T.);
#1265=ADVANCED_FACE('',(#1145),#111,.T.);
#1266=ADVANCED_FACE('',(#1146),#1200,.F.);
#1267=ADVANCED_FACE('',(#1147),#1201,.T.);
#1268=ADVANCED_FACE('',(#1148),#112,.F.);
#1269=ADVANCED_FACE('',(#1149),#1202,.T.);
#1270=ADVANCED_FACE('',(#1150),#113,.T.);
#1271=ADVANCED_FACE('',(#1151),#1203,.T.);
#1272=ADVANCED_FACE('',(#1152),#1204,.T.);
#1273=ADVANCED_FACE('',(#1153),#1205,.T.);
#1274=ADVANCED_FACE('',(#1154),#114,.F.);
#1275=ADVANCED_FACE('',(#1155),#1206,.T.);
#1276=ADVANCED_FACE('',(#1156),#115,.T.);
#1277=ADVANCED_FACE('',(#1157),#1207,.T.);
#1278=ADVANCED_FACE('',(#1158),#1208,.F.);
#1279=ADVANCED_FACE('',(#1159),#1209,.F.);
#1280=ADVANCED_FACE('',(#1160),#1210,.T.);
#1281=ADVANCED_FACE('',(#1161),#1211,.T.);
#1282=ADVANCED_FACE('',(#1162),#116,.F.);
#1283=ADVANCED_FACE('',(#1163),#1212,.T.);
#1284=ADVANCED_FACE('',(#1164),#117,.T.);
#1285=ADVANCED_FACE('',(#1165),#1213,.T.);
#1286=ADVANCED_FACE('',(#1166),#1214,.T.);
#1287=ADVANCED_FACE('',(#1167),#1215,.T.);
#1288=ADVANCED_FACE('',(#1168),#118,.F.);
#1289=ADVANCED_FACE('',(#1169),#1216,.T.);
#1290=ADVANCED_FACE('',(#1170),#119,.T.);
#1291=ADVANCED_FACE('',(#1171),#1217,.T.);
#1292=ADVANCED_FACE('',(#1172),#1218,.F.);
#1293=ADVANCED_FACE('',(#1173),#1219,.F.);
#1294=ADVANCED_FACE('',(#1174),#1220,.T.);
#1295=ADVANCED_FACE('',(#1175),#120,.T.);
#1296=ADVANCED_FACE('',(#1176),#1221,.T.);
#1297=ADVANCED_FACE('',(#1177),#1222,.T.);
#1298=ADVANCED_FACE('',(#1178),#1223,.T.);
#1299=ADVANCED_FACE('',(#1179),#121,.F.);
#1300=ADVANCED_FACE('',(#1180),#1224,.T.);
#1301=ADVANCED_FACE('',(#1181),#122,.T.);
#1302=ADVANCED_FACE('',(#1182),#1225,.T.);
#1303=ADVANCED_FACE('',(#1183),#1226,.T.);
#1304=ADVANCED_FACE('',(#1184),#123,.F.);
#1305=ADVANCED_FACE('',(#1185),#1227,.F.);
#1306=ADVANCED_FACE('',(#1186),#1228,.F.);
#1307=ADVANCED_FACE('',(#1187),#1229,.F.);
#1308=CLOSED_SHELL('',(#1230,#1231,#1232,#1233,#1234,#1235,#1236,#1237,
#1238,#1239,#1240,#1241,#1242,#1243,#1244,#1245,#1246,#1247));
#1309=CLOSED_SHELL('',(#1248,#1249,#1250,#1251,#1252,#1253,#1254,#1255,
#1256,#1257,#1258,#1259,#1260,#1261,#1262,#1263,#1264,#1265));
#1310=CLOSED_SHELL('',(#1266,#1267,#1268,#1269,#1270,#1271,#1272,#1273,
#1274,#1275,#1276,#1277,#1278,#1279));
#1311=CLOSED_SHELL('',(#1280,#1281,#1282,#1283,#1284,#1285,#1286,#1287,
#1288,#1289,#1290,#1291,#1292,#1293));
#1312=CLOSED_SHELL('',(#1294,#1295,#1296,#1297,#1298,#1299,#1300,#1301,
#1302,#1303,#1304,#1305,#1306,#1307));
#1313=STYLED_ITEM('',(#1357),#1624);
#1314=STYLED_ITEM('',(#1358),#1625);
#1315=STYLED_ITEM('',(#1359),#1266);
#1316=STYLED_ITEM('',(#1360),#1267);
#1317=STYLED_ITEM('',(#1361),#1268);
#1318=STYLED_ITEM('',(#1362),#1269);
#1319=STYLED_ITEM('',(#1363),#1270);
#1320=STYLED_ITEM('',(#1364),#1271);
#1321=STYLED_ITEM('',(#1365),#1272);
#1322=STYLED_ITEM('',(#1366),#1273);
#1323=STYLED_ITEM('',(#1367),#1274);
#1324=STYLED_ITEM('',(#1368),#1275);
#1325=STYLED_ITEM('',(#1369),#1276);
#1326=STYLED_ITEM('',(#1370),#1277);
#1327=STYLED_ITEM('',(#1371),#1278);
#1328=STYLED_ITEM('',(#1372),#1279);
#1329=STYLED_ITEM('',(#1373),#1280);
#1330=STYLED_ITEM('',(#1374),#1281);
#1331=STYLED_ITEM('',(#1375),#1282);
#1332=STYLED_ITEM('',(#1376),#1283);
#1333=STYLED_ITEM('',(#1377),#1284);
#1334=STYLED_ITEM('',(#1378),#1285);
#1335=STYLED_ITEM('',(#1379),#1286);
#1336=STYLED_ITEM('',(#1380),#1287);
#1337=STYLED_ITEM('',(#1381),#1288);
#1338=STYLED_ITEM('',(#1382),#1289);
#1339=STYLED_ITEM('',(#1383),#1290);
#1340=STYLED_ITEM('',(#1384),#1291);
#1341=STYLED_ITEM('',(#1385),#1292);
#1342=STYLED_ITEM('',(#1386),#1293);
#1343=STYLED_ITEM('',(#1387),#1294);
#1344=STYLED_ITEM('',(#1388),#1295);
#1345=STYLED_ITEM('',(#1389),#1296);
#1346=STYLED_ITEM('',(#1390),#1297);
#1347=STYLED_ITEM('',(#1391),#1298);
#1348=STYLED_ITEM('',(#1392),#1299);
#1349=STYLED_ITEM('',(#1393),#1300);
#1350=STYLED_ITEM('',(#1394),#1301);
#1351=STYLED_ITEM('',(#1395),#1302);
#1352=STYLED_ITEM('',(#1396),#1303);
#1353=STYLED_ITEM('',(#1397),#1304);
#1354=STYLED_ITEM('',(#1398),#1305);
#1355=STYLED_ITEM('',(#1399),#1306);
#1356=STYLED_ITEM('',(#1400),#1307);
#1357=PRESENTATION_STYLE_ASSIGNMENT((#1401));
#1358=PRESENTATION_STYLE_ASSIGNMENT((#1402));
#1359=PRESENTATION_STYLE_ASSIGNMENT((#1403));
#1360=PRESENTATION_STYLE_ASSIGNMENT((#1404));
#1361=PRESENTATION_STYLE_ASSIGNMENT((#1405));
#1362=PRESENTATION_STYLE_ASSIGNMENT((#1406));
#1363=PRESENTATION_STYLE_ASSIGNMENT((#1407));
#1364=PRESENTATION_STYLE_ASSIGNMENT((#1408));
#1365=PRESENTATION_STYLE_ASSIGNMENT((#1409));
#1366=PRESENTATION_STYLE_ASSIGNMENT((#1410));
#1367=PRESENTATION_STYLE_ASSIGNMENT((#1411));
#1368=PRESENTATION_STYLE_ASSIGNMENT((#1412));
#1369=PRESENTATION_STYLE_ASSIGNMENT((#1413));
#1370=PRESENTATION_STYLE_ASSIGNMENT((#1414));
#1371=PRESENTATION_STYLE_ASSIGNMENT((#1415));
#1372=PRESENTATION_STYLE_ASSIGNMENT((#1416));
#1373=PRESENTATION_STYLE_ASSIGNMENT((#1417));
#1374=PRESENTATION_STYLE_ASSIGNMENT((#1418));
#1375=PRESENTATION_STYLE_ASSIGNMENT((#1419));
#1376=PRESENTATION_STYLE_ASSIGNMENT((#1420));
#1377=PRESENTATION_STYLE_ASSIGNMENT((#1421));
#1378=PRESENTATION_STYLE_ASSIGNMENT((#1422));
#1379=PRESENTATION_STYLE_ASSIGNMENT((#1423));
#1380=PRESENTATION_STYLE_ASSIGNMENT((#1424));
#1381=PRESENTATION_STYLE_ASSIGNMENT((#1425));
#1382=PRESENTATION_STYLE_ASSIGNMENT((#1426));
#1383=PRESENTATION_STYLE_ASSIGNMENT((#1427));
#1384=PRESENTATION_STYLE_ASSIGNMENT((#1428));
#1385=PRESENTATION_STYLE_ASSIGNMENT((#1429));
#1386=PRESENTATION_STYLE_ASSIGNMENT((#1430));
#1387=PRESENTATION_STYLE_ASSIGNMENT((#1431));
#1388=PRESENTATION_STYLE_ASSIGNMENT((#1432));
#1389=PRESENTATION_STYLE_ASSIGNMENT((#1433));
#1390=PRESENTATION_STYLE_ASSIGNMENT((#1434));
#1391=PRESENTATION_STYLE_ASSIGNMENT((#1435));
#1392=PRESENTATION_STYLE_ASSIGNMENT((#1436));
#1393=PRESENTATION_STYLE_ASSIGNMENT((#1437));
#1394=PRESENTATION_STYLE_ASSIGNMENT((#1438));
#1395=PRESENTATION_STYLE_ASSIGNMENT((#1439));
#1396=PRESENTATION_STYLE_ASSIGNMENT((#1440));
#1397=PRESENTATION_STYLE_ASSIGNMENT((#1441));
#1398=PRESENTATION_STYLE_ASSIGNMENT((#1442));
#1399=PRESENTATION_STYLE_ASSIGNMENT((#1443));
#1400=PRESENTATION_STYLE_ASSIGNMENT((#1444));
#1401=SURFACE_STYLE_USAGE(.BOTH.,#1445);
#1402=SURFACE_STYLE_USAGE(.BOTH.,#1446);
#1403=SURFACE_STYLE_USAGE(.BOTH.,#1447);
#1404=SURFACE_STYLE_USAGE(.BOTH.,#1448);
#1405=SURFACE_STYLE_USAGE(.BOTH.,#1449);
#1406=SURFACE_STYLE_USAGE(.BOTH.,#1450);
#1407=SURFACE_STYLE_USAGE(.BOTH.,#1451);
#1408=SURFACE_STYLE_USAGE(.BOTH.,#1452);
#1409=SURFACE_STYLE_USAGE(.BOTH.,#1453);
#1410=SURFACE_STYLE_USAGE(.BOTH.,#1454);
#1411=SURFACE_STYLE_USAGE(.BOTH.,#1455);
#1412=SURFACE_STYLE_USAGE(.BOTH.,#1456);
#1413=SURFACE_STYLE_USAGE(.BOTH.,#1457);
#1414=SURFACE_STYLE_USAGE(.BOTH.,#1458);
#1415=SURFACE_STYLE_USAGE(.BOTH.,#1459);
#1416=SURFACE_STYLE_USAGE(.BOTH.,#1460);
#1417=SURFACE_STYLE_USAGE(.BOTH.,#1461);
#1418=SURFACE_STYLE_USAGE(.BOTH.,#1462);
#1419=SURFACE_STYLE_USAGE(.BOTH.,#1463);
#1420=SURFACE_STYLE_USAGE(.BOTH.,#1464);
#1421=SURFACE_STYLE_USAGE(.BOTH.,#1465);
#1422=SURFACE_STYLE_USAGE(.BOTH.,#1466);
#1423=SURFACE_STYLE_USAGE(.BOTH.,#1467);
#1424=SURFACE_STYLE_USAGE(.BOTH.,#1468);
#1425=SURFACE_STYLE_USAGE(.BOTH.,#1469);
#1426=SURFACE_STYLE_USAGE(.BOTH.,#1470);
#1427=SURFACE_STYLE_USAGE(.BOTH.,#1471);
#1428=SURFACE_STYLE_USAGE(.BOTH.,#1472);
#1429=SURFACE_STYLE_USAGE(.BOTH.,#1473);
#1430=SURFACE_STYLE_USAGE(.BOTH.,#1474);
#1431=SURFACE_STYLE_USAGE(.BOTH.,#1475);
#1432=SURFACE_STYLE_USAGE(.BOTH.,#1476);
#1433=SURFACE_STYLE_USAGE(.BOTH.,#1477);
#1434=SURFACE_STYLE_USAGE(.BOTH.,#1478);
#1435=SURFACE_STYLE_USAGE(.BOTH.,#1479);
#1436=SURFACE_STYLE_USAGE(.BOTH.,#1480);
#1437=SURFACE_STYLE_USAGE(.BOTH.,#1481);
#1438=SURFACE_STYLE_USAGE(.BOTH.,#1482);
#1439=SURFACE_STYLE_USAGE(.BOTH.,#1483);
#1440=SURFACE_STYLE_USAGE(.BOTH.,#1484);
#1441=SURFACE_STYLE_USAGE(.BOTH.,#1485);
#1442=SURFACE_STYLE_USAGE(.BOTH.,#1486);
#1443=SURFACE_STYLE_USAGE(.BOTH.,#1487);
#1444=SURFACE_STYLE_USAGE(.BOTH.,#1488);
#1445=SURFACE_SIDE_STYLE('',(#1489));
#1446=SURFACE_SIDE_STYLE('',(#1490));
#1447=SURFACE_SIDE_STYLE('',(#1491));
#1448=SURFACE_SIDE_STYLE('',(#1492));
#1449=SURFACE_SIDE_STYLE('',(#1493));
#1450=SURFACE_SIDE_STYLE('',(#1494));
#1451=SURFACE_SIDE_STYLE('',(#1495));
#1452=SURFACE_SIDE_STYLE('',(#1496));
#1453=SURFACE_SIDE_STYLE('',(#1497));
#1454=SURFACE_SIDE_STYLE('',(#1498));
#1455=SURFACE_SIDE_STYLE('',(#1499));
#1456=SURFACE_SIDE_STYLE('',(#1500));
#1457=SURFACE_SIDE_STYLE('',(#1501));
#1458=SURFACE_SIDE_STYLE('',(#1502));
#1459=SURFACE_SIDE_STYLE('',(#1503));
#1460=SURFACE_SIDE_STYLE('',(#1504));
#1461=SURFACE_SIDE_STYLE('',(#1505));
#1462=SURFACE_SIDE_STYLE('',(#1506));
#1463=SURFACE_SIDE_STYLE('',(#1507));
#1464=SURFACE_SIDE_STYLE('',(#1508));
#1465=SURFACE_SIDE_STYLE('',(#1509));
#1466=SURFACE_SIDE_STYLE('',(#1510));
#1467=SURFACE_SIDE_STYLE('',(#1511));
#1468=SURFACE_SIDE_STYLE('',(#1512));
#1469=SURFACE_SIDE_STYLE('',(#1513));
#1470=SURFACE_SIDE_STYLE('',(#1514));
#1471=SURFACE_SIDE_STYLE('',(#1515));
#1472=SURFACE_SIDE_STYLE('',(#1516));
#1473=SURFACE_SIDE_STYLE('',(#1517));
#1474=SURFACE_SIDE_STYLE('',(#1518));
#1475=SURFACE_SIDE_STYLE('',(#1519));
#1476=SURFACE_SIDE_STYLE('',(#1520));
#1477=SURFACE_SIDE_STYLE('',(#1521));
#1478=SURFACE_SIDE_STYLE('',(#1522));
#1479=SURFACE_SIDE_STYLE('',(#1523));
#1480=SURFACE_SIDE_STYLE('',(#1524));
#1481=SURFACE_SIDE_STYLE('',(#1525));
#1482=SURFACE_SIDE_STYLE('',(#1526));
#1483=SURFACE_SIDE_STYLE('',(#1527));
#1484=SURFACE_SIDE_STYLE('',(#1528));
#1485=SURFACE_SIDE_STYLE('',(#1529));
#1486=SURFACE_SIDE_STYLE('',(#1530));
#1487=SURFACE_SIDE_STYLE('',(#1531));
#1488=SURFACE_SIDE_STYLE('',(#1532));
#1489=SURFACE_STYLE_FILL_AREA(#1533);
#1490=SURFACE_STYLE_FILL_AREA(#1534);
#1491=SURFACE_STYLE_FILL_AREA(#1535);
#1492=SURFACE_STYLE_FILL_AREA(#1536);
#1493=SURFACE_STYLE_FILL_AREA(#1537);
#1494=SURFACE_STYLE_FILL_AREA(#1538);
#1495=SURFACE_STYLE_FILL_AREA(#1539);
#1496=SURFACE_STYLE_FILL_AREA(#1540);
#1497=SURFACE_STYLE_FILL_AREA(#1541);
#1498=SURFACE_STYLE_FILL_AREA(#1542);
#1499=SURFACE_STYLE_FILL_AREA(#1543);
#1500=SURFACE_STYLE_FILL_AREA(#1544);
#1501=SURFACE_STYLE_FILL_AREA(#1545);
#1502=SURFACE_STYLE_FILL_AREA(#1546);
#1503=SURFACE_STYLE_FILL_AREA(#1547);
#1504=SURFACE_STYLE_FILL_AREA(#1548);
#1505=SURFACE_STYLE_FILL_AREA(#1549);
#1506=SURFACE_STYLE_FILL_AREA(#1550);
#1507=SURFACE_STYLE_FILL_AREA(#1551);
#1508=SURFACE_STYLE_FILL_AREA(#1552);
#1509=SURFACE_STYLE_FILL_AREA(#1553);
#1510=SURFACE_STYLE_FILL_AREA(#1554);
#1511=SURFACE_STYLE_FILL_AREA(#1555);
#1512=SURFACE_STYLE_FILL_AREA(#1556);
#1513=SURFACE_STYLE_FILL_AREA(#1557);
#1514=SURFACE_STYLE_FILL_AREA(#1558);
#1515=SURFACE_STYLE_FILL_AREA(#1559);
#1516=SURFACE_STYLE_FILL_AREA(#1560);
#1517=SURFACE_STYLE_FILL_AREA(#1561);
#1518=SURFACE_STYLE_FILL_AREA(#1562);
#1519=SURFACE_STYLE_FILL_AREA(#1563);
#1520=SURFACE_STYLE_FILL_AREA(#1564);
#1521=SURFACE_STYLE_FILL_AREA(#1565);
#1522=SURFACE_STYLE_FILL_AREA(#1566);
#1523=SURFACE_STYLE_FILL_AREA(#1567);
#1524=SURFACE_STYLE_FILL_AREA(#1568);
#1525=SURFACE_STYLE_FILL_AREA(#1569);
#1526=SURFACE_STYLE_FILL_AREA(#1570);
#1527=SURFACE_STYLE_FILL_AREA(#1571);
#1528=SURFACE_STYLE_FILL_AREA(#1572);
#1529=SURFACE_STYLE_FILL_AREA(#1573);
#1530=SURFACE_STYLE_FILL_AREA(#1574);
#1531=SURFACE_STYLE_FILL_AREA(#1575);
#1532=SURFACE_STYLE_FILL_AREA(#1576);
#1533=FILL_AREA_STYLE('',(#1577));
#1534=FILL_AREA_STYLE('',(#1578));
#1535=FILL_AREA_STYLE('',(#1579));
#1536=FILL_AREA_STYLE('',(#1580));
#1537=FILL_AREA_STYLE('',(#1581));
#1538=FILL_AREA_STYLE('',(#1582));
#1539=FILL_AREA_STYLE('',(#1583));
#1540=FILL_AREA_STYLE('',(#1584));
#1541=FILL_AREA_STYLE('',(#1585));
#1542=FILL_AREA_STYLE('',(#1586));
#1543=FILL_AREA_STYLE('',(#1587));
#1544=FILL_AREA_STYLE('',(#1588));
#1545=FILL_AREA_STYLE('',(#1589));
#1546=FILL_AREA_STYLE('',(#1590));
#1547=FILL_AREA_STYLE('',(#1591));
#1548=FILL_AREA_STYLE('',(#1592));
#1549=FILL_AREA_STYLE('',(#1593));
#1550=FILL_AREA_STYLE('',(#1594));
#1551=FILL_AREA_STYLE('',(#1595));
#1552=FILL_AREA_STYLE('',(#1596));
#1553=FILL_AREA_STYLE('',(#1597));
#1554=FILL_AREA_STYLE('',(#1598));
#1555=FILL_AREA_STYLE('',(#1599));
#1556=FILL_AREA_STYLE('',(#1600));
#1557=FILL_AREA_STYLE('',(#1601));
#1558=FILL_AREA_STYLE('',(#1602));
#1559=FILL_AREA_STYLE('',(#1603));
#1560=FILL_AREA_STYLE('',(#1604));
#1561=FILL_AREA_STYLE('',(#1605));
#1562=FILL_AREA_STYLE('',(#1606));
#1563=FILL_AREA_STYLE('',(#1607));
#1564=FILL_AREA_STYLE('',(#1608));
#1565=FILL_AREA_STYLE('',(#1609));
#1566=FILL_AREA_STYLE('',(#1610));
#1567=FILL_AREA_STYLE('',(#1611));
#1568=FILL_AREA_STYLE('',(#1612));
#1569=FILL_AREA_STYLE('',(#1613));
#1570=FILL_AREA_STYLE('',(#1614));
#1571=FILL_AREA_STYLE('',(#1615));
#1572=FILL_AREA_STYLE('',(#1616));
#1573=FILL_AREA_STYLE('',(#1617));
#1574=FILL_AREA_STYLE('',(#1618));
#1575=FILL_AREA_STYLE('',(#1619));
#1576=FILL_AREA_STYLE('',(#1620));
#1577=FILL_AREA_STYLE_COLOUR('',#1621);
#1578=FILL_AREA_STYLE_COLOUR('',#1621);
#1579=FILL_AREA_STYLE_COLOUR('',#1622);
#1580=FILL_AREA_STYLE_COLOUR('',#1622);
#1581=FILL_AREA_STYLE_COLOUR('',#1622);
#1582=FILL_AREA_STYLE_COLOUR('',#1622);
#1583=FILL_AREA_STYLE_COLOUR('',#1622);
#1584=FILL_AREA_STYLE_COLOUR('',#1622);
#1585=FILL_AREA_STYLE_COLOUR('',#1623);
#1586=FILL_AREA_STYLE_COLOUR('',#1622);
#1587=FILL_AREA_STYLE_COLOUR('',#1622);
#1588=FILL_AREA_STYLE_COLOUR('',#1622);
#1589=FILL_AREA_STYLE_COLOUR('',#1622);
#1590=FILL_AREA_STYLE_COLOUR('',#1622);
#1591=FILL_AREA_STYLE_COLOUR('',#1622);
#1592=FILL_AREA_STYLE_COLOUR('',#1622);
#1593=FILL_AREA_STYLE_COLOUR('',#1622);
#1594=FILL_AREA_STYLE_COLOUR('',#1622);
#1595=FILL_AREA_STYLE_COLOUR('',#1622);
#1596=FILL_AREA_STYLE_COLOUR('',#1622);
#1597=FILL_AREA_STYLE_COLOUR('',#1622);
#1598=FILL_AREA_STYLE_COLOUR('',#1622);
#1599=FILL_AREA_STYLE_COLOUR('',#1623);
#1600=FILL_AREA_STYLE_COLOUR('',#1622);
#1601=FILL_AREA_STYLE_COLOUR('',#1622);
#1602=FILL_AREA_STYLE_COLOUR('',#1622);
#1603=FILL_AREA_STYLE_COLOUR('',#1622);
#1604=FILL_AREA_STYLE_COLOUR('',#1622);
#1605=FILL_AREA_STYLE_COLOUR('',#1622);
#1606=FILL_AREA_STYLE_COLOUR('',#1622);
#1607=FILL_AREA_STYLE_COLOUR('',#1622);
#1608=FILL_AREA_STYLE_COLOUR('',#1622);
#1609=FILL_AREA_STYLE_COLOUR('',#1622);
#1610=FILL_AREA_STYLE_COLOUR('',#1623);
#1611=FILL_AREA_STYLE_COLOUR('',#1622);
#1612=FILL_AREA_STYLE_COLOUR('',#1622);
#1613=FILL_AREA_STYLE_COLOUR('',#1622);
#1614=FILL_AREA_STYLE_COLOUR('',#1622);
#1615=FILL_AREA_STYLE_COLOUR('',#1622);
#1616=FILL_AREA_STYLE_COLOUR('',#1622);
#1617=FILL_AREA_STYLE_COLOUR('',#1622);
#1618=FILL_AREA_STYLE_COLOUR('',#1622);
#1619=FILL_AREA_STYLE_COLOUR('',#1622);
#1620=FILL_AREA_STYLE_COLOUR('',#1622);
#1621=COLOUR_RGB('',0.200000002980232,0.200000002980232,0.200000002980232);
#1622=COLOUR_RGB('',0.819999992847443,0.819999992847443,0.819999992847443);
#1623=COLOUR_RGB('',0.550000011920929,0.311243504285812,0.21841649711132);
#1624=MANIFOLD_SOLID_BREP('',#1308);
#1625=MANIFOLD_SOLID_BREP('',#1309);
#1626=MANIFOLD_SOLID_BREP('',#1310);
#1627=MANIFOLD_SOLID_BREP('',#1311);
#1628=MANIFOLD_SOLID_BREP('',#1312);
#1629=SHAPE_DEFINITION_REPRESENTATION(#2571,#1634);
#1630=SHAPE_DEFINITION_REPRESENTATION(#2570,#1635);
#1631=SHAPE_DEFINITION_REPRESENTATION(#2573,#1636);
#1632=SHAPE_DEFINITION_REPRESENTATION(#2569,#1637);
#1633=SHAPE_DEFINITION_REPRESENTATION(#2576,#1638);
#1634=SHAPE_REPRESENTATION('CP01',(#1639),#2558);
#1635=SHAPE_REPRESENTATION('COMPOUND',(#1639,#1674,#1709),#2557);
#1636=SHAPE_REPRESENTATION('CP02',(#1639),#2559);
#1637=SHAPE_REPRESENTATION('SOT-23',(#1639,#1710,#1777),#2556);
#1638=SHAPE_REPRESENTATION('LDF',(#1639),#2560);
#1639=AXIS2_PLACEMENT_3D('',#2180,#1778,#1779);
#1640=AXIS2_PLACEMENT_3D('',#2181,#1780,#1781);
#1641=AXIS2_PLACEMENT_3D('',#2190,#1786,#1787);
#1642=AXIS2_PLACEMENT_3D('',#2199,#1792,#1793);
#1643=AXIS2_PLACEMENT_3D('',#2208,#1798,#1799);
#1644=AXIS2_PLACEMENT_3D('',#2212,#1801,#1802);
#1645=AXIS2_PLACEMENT_3D('',#2213,#1803,#1804);
#1646=AXIS2_PLACEMENT_3D('',#2217,#1806,#1807);
#1647=AXIS2_PLACEMENT_3D('',#2218,#1808,#1809);
#1648=AXIS2_PLACEMENT_3D('',#2219,#1810,#1811);
#1649=AXIS2_PLACEMENT_3D('',#2225,#1815,#1816);
#1650=AXIS2_PLACEMENT_3D('',#2231,#1820,#1821);
#1651=AXIS2_PLACEMENT_3D('',#2232,#1822,#1823);
#1652=AXIS2_PLACEMENT_3D('',#2233,#1824,#1825);
#1653=AXIS2_PLACEMENT_3D('',#2234,#1826,#1827);
#1654=AXIS2_PLACEMENT_3D('',#2235,#1828,#1829);
#1655=AXIS2_PLACEMENT_3D('',#2236,#1830,#1831);
#1656=AXIS2_PLACEMENT_3D('',#2237,#1832,#1833);
#1657=AXIS2_PLACEMENT_3D('',#2238,#1834,#1835);
#1658=AXIS2_PLACEMENT_3D('',#2239,#1836,#1837);
#1659=AXIS2_PLACEMENT_3D('',#2240,#1838,#1839);
#1660=AXIS2_PLACEMENT_3D('',#2241,#1840,#1841);
#1661=AXIS2_PLACEMENT_3D('',#2242,#1842,#1843);
#1662=AXIS2_PLACEMENT_3D('',#2243,#1844,#1845);
#1663=AXIS2_PLACEMENT_3D('',#2244,#1846,#1847);
#1664=AXIS2_PLACEMENT_3D('',#2245,#1848,#1849);
#1665=AXIS2_PLACEMENT_3D('',#2246,#1850,#1851);
#1666=AXIS2_PLACEMENT_3D('',#2247,#1852,#1853);
#1667=AXIS2_PLACEMENT_3D('',#2248,#1854,#1855);
#1668=AXIS2_PLACEMENT_3D('',#2249,#1856,#1857);
#1669=AXIS2_PLACEMENT_3D('',#2250,#1858,#1859);
#1670=AXIS2_PLACEMENT_3D('',#2251,#1860,#1861);
#1671=AXIS2_PLACEMENT_3D('',#2252,#1862,#1863);
#1672=AXIS2_PLACEMENT_3D('',#2253,#1864,#1865);
#1673=AXIS2_PLACEMENT_3D('',#2254,#1866,#1867);
#1674=AXIS2_PLACEMENT_3D('',#2255,#1868,#1869);
#1675=AXIS2_PLACEMENT_3D('',#2256,#1870,#1871);
#1676=AXIS2_PLACEMENT_3D('',#2265,#1876,#1877);
#1677=AXIS2_PLACEMENT_3D('',#2269,#1879,#1880);
#1678=AXIS2_PLACEMENT_3D('',#2273,#1882,#1883);
#1679=AXIS2_PLACEMENT_3D('',#2274,#1884,#1885);
#1680=AXIS2_PLACEMENT_3D('',#2278,#1887,#1888);
#1681=AXIS2_PLACEMENT_3D('',#2279,#1889,#1890);
#1682=AXIS2_PLACEMENT_3D('',#2285,#1894,#1895);
#1683=AXIS2_PLACEMENT_3D('',#2294,#1900,#1901);
#1684=AXIS2_PLACEMENT_3D('',#2300,#1905,#1906);
#1685=AXIS2_PLACEMENT_3D('',#2306,#1910,#1911);
#1686=AXIS2_PLACEMENT_3D('',#2307,#1912,#1913);
#1687=AXIS2_PLACEMENT_3D('',#2308,#1914,#1915);
#1688=AXIS2_PLACEMENT_3D('',#2309,#1916,#1917);
#1689=AXIS2_PLACEMENT_3D('',#2310,#1918,#1919);
#1690=AXIS2_PLACEMENT_3D('',#2311,#1920,#1921);
#1691=AXIS2_PLACEMENT_3D('',#2312,#1922,#1923);
#1692=AXIS2_PLACEMENT_3D('',#2313,#1924,#1925);
#1693=AXIS2_PLACEMENT_3D('',#2314,#1926,#1927);
#1694=AXIS2_PLACEMENT_3D('',#2315,#1928,#1929);
#1695=AXIS2_PLACEMENT_3D('',#2316,#1930,#1931);
#1696=AXIS2_PLACEMENT_3D('',#2317,#1932,#1933);
#1697=AXIS2_PLACEMENT_3D('',#2318,#1934,#1935);
#1698=AXIS2_PLACEMENT_3D('',#2319,#1936,#1937);
#1699=AXIS2_PLACEMENT_3D('',#2320,#1938,#1939);
#1700=AXIS2_PLACEMENT_3D('',#2321,#1940,#1941);
#1701=AXIS2_PLACEMENT_3D('',#2322,#1942,#1943);
#1702=AXIS2_PLACEMENT_3D('',#2323,#1944,#1945);
#1703=AXIS2_PLACEMENT_3D('',#2324,#1946,#1947);
#1704=AXIS2_PLACEMENT_3D('',#2325,#1948,#1949);
#1705=AXIS2_PLACEMENT_3D('',#2326,#1950,#1951);
#1706=AXIS2_PLACEMENT_3D('',#2327,#1952,#1953);
#1707=AXIS2_PLACEMENT_3D('',#2328,#1954,#1955);
#1708=AXIS2_PLACEMENT_3D('',#2329,#1956,#1957);
#1709=AXIS2_PLACEMENT_3D('',#2330,#1958,#1959);
#1710=AXIS2_PLACEMENT_3D('',#2331,#1960,#1961);
#1711=AXIS2_PLACEMENT_3D('',#2332,#1962,#1963);
#1712=AXIS2_PLACEMENT_3D('',#2340,#1967,#1968);
#1713=AXIS2_PLACEMENT_3D('',#2344,#1970,#1971);
#1714=AXIS2_PLACEMENT_3D('',#2352,#1975,#1976);
#1715=AXIS2_PLACEMENT_3D('',#2356,#1978,#1979);
#1716=AXIS2_PLACEMENT_3D('',#2357,#1980,#1981);
#1717=AXIS2_PLACEMENT_3D('',#2363,#1985,#1986);
#1718=AXIS2_PLACEMENT_3D('',#2364,#1987,#1988);
#1719=AXIS2_PLACEMENT_3D('',#2367,#1990,#1991);
#1720=AXIS2_PLACEMENT_3D('',#2371,#1994,#1995);
#1721=AXIS2_PLACEMENT_3D('',#2372,#1996,#1997);
#1722=AXIS2_PLACEMENT_3D('',#2375,#1999,#2000);
#1723=AXIS2_PLACEMENT_3D('',#2379,#2003,#2004);
#1724=AXIS2_PLACEMENT_3D('',#2383,#2007,#2008);
#1725=AXIS2_PLACEMENT_3D('',#2387,#2011,#2012);
#1726=AXIS2_PLACEMENT_3D('',#2388,#2013,#2014);
#1727=AXIS2_PLACEMENT_3D('',#2391,#2016,#2017);
#1728=AXIS2_PLACEMENT_3D('',#2395,#2020,#2021);
#1729=AXIS2_PLACEMENT_3D('',#2396,#2022,#2023);
#1730=AXIS2_PLACEMENT_3D('',#2399,#2025,#2026);
#1731=AXIS2_PLACEMENT_3D('',#2403,#2029,#2030);
#1732=AXIS2_PLACEMENT_3D('',#2405,#2032,#2033);
#1733=AXIS2_PLACEMENT_3D('',#2406,#2034,#2035);
#1734=AXIS2_PLACEMENT_3D('',#2412,#2038,#2039);
#1735=AXIS2_PLACEMENT_3D('',#2416,#2041,#2042);
#1736=AXIS2_PLACEMENT_3D('',#2424,#2046,#2047);
#1737=AXIS2_PLACEMENT_3D('',#2428,#2049,#2050);
#1738=AXIS2_PLACEMENT_3D('',#2431,#2052,#2053);
#1739=AXIS2_PLACEMENT_3D('',#2437,#2057,#2058);
#1740=AXIS2_PLACEMENT_3D('',#2438,#2059,#2060);
#1741=AXIS2_PLACEMENT_3D('',#2441,#2062,#2063);
#1742=AXIS2_PLACEMENT_3D('',#2445,#2066,#2067);
#1743=AXIS2_PLACEMENT_3D('',#2446,#2068,#2069);
#1744=AXIS2_PLACEMENT_3D('',#2449,#2071,#2072);
#1745=AXIS2_PLACEMENT_3D('',#2453,#2075,#2076);
#1746=AXIS2_PLACEMENT_3D('',#2457,#2079,#2080);
#1747=AXIS2_PLACEMENT_3D('',#2461,#2083,#2084);
#1748=AXIS2_PLACEMENT_3D('',#2462,#2085,#2086);
#1749=AXIS2_PLACEMENT_3D('',#2465,#2088,#2089);
#1750=AXIS2_PLACEMENT_3D('',#2469,#2092,#2093);
#1751=AXIS2_PLACEMENT_3D('',#2470,#2094,#2095);
#1752=AXIS2_PLACEMENT_3D('',#2473,#2097,#2098);
#1753=AXIS2_PLACEMENT_3D('',#2477,#2101,#2102);
#1754=AXIS2_PLACEMENT_3D('',#2479,#2104,#2105);
#1755=AXIS2_PLACEMENT_3D('',#2480,#2106,#2107);
#1756=AXIS2_PLACEMENT_3D('',#2489,#2112,#2113);
#1757=AXIS2_PLACEMENT_3D('',#2490,#2114,#2115);
#1758=AXIS2_PLACEMENT_3D('',#2494,#2117,#2118);
#1759=AXIS2_PLACEMENT_3D('',#2495,#2119,#2120);
#1760=AXIS2_PLACEMENT_3D('',#2501,#2124,#2125);
#1761=AXIS2_PLACEMENT_3D('',#2507,#2129,#2130);
#1762=AXIS2_PLACEMENT_3D('',#2513,#2134,#2135);
#1763=AXIS2_PLACEMENT_3D('',#2514,#2136,#2137);
#1764=AXIS2_PLACEMENT_3D('',#2518,#2139,#2140);
#1765=AXIS2_PLACEMENT_3D('',#2519,#2141,#2142);
#1766=AXIS2_PLACEMENT_3D('',#2525,#2146,#2147);
#1767=AXIS2_PLACEMENT_3D('',#2526,#2148,#2149);
#1768=AXIS2_PLACEMENT_3D('',#2530,#2151,#2152);
#1769=AXIS2_PLACEMENT_3D('',#2531,#2153,#2154);
#1770=AXIS2_PLACEMENT_3D('',#2537,#2158,#2159);
#1771=AXIS2_PLACEMENT_3D('',#2546,#2164,#2165);
#1772=AXIS2_PLACEMENT_3D('',#2547,#2166,#2167);
#1773=AXIS2_PLACEMENT_3D('',#2548,#2168,#2169);
#1774=AXIS2_PLACEMENT_3D('',#2549,#2170,#2171);
#1775=AXIS2_PLACEMENT_3D('',#2551,#2173,#2174);
#1776=AXIS2_PLACEMENT_3D('',#2553,#2176,#2177);
#1777=AXIS2_PLACEMENT_3D('',#2554,#2178,#2179);
#1778=DIRECTION('',(0.,0.,1.));
#1779=DIRECTION('',(1.,0.,0.));
#1780=DIRECTION('',(-6.82382684464434E-17,0.992546151641323,-0.121869343405143));
#1781=DIRECTION('',(0.,0.121869343405143,0.992546151641323));
#1782=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573677E-17));
#1783=DIRECTION('',(0.120974291151355,-0.12097429115135,-0.985256536015294));
#1784=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573676E-17));
#1785=DIRECTION('',(-0.120974291151355,-0.12097429115135,-0.985256536015294));
#1786=DIRECTION('',(-6.12323399573666E-17,-1.79640335188696E-14,-1.));
#1787=DIRECTION('',(0.,1.,-1.79640335188696E-14));
#1788=DIRECTION('',(6.12323399573689E-17,-1.,1.79640335188696E-14));
#1789=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573676E-17));
#1790=DIRECTION('',(6.12323399573686E-17,-1.,1.79640335188696E-14));
#1791=DIRECTION('',(1.,6.12323399573676E-17,-6.12323399573676E-17));
#1792=DIRECTION('',(5.33135783149149E-17,-0.992546151641325,-0.121869343405124));
#1793=DIRECTION('',(0.,0.121869343405124,-0.992546151641325));
#1794=DIRECTION('',(1.,6.12323399573676E-17,-6.12323399573676E-17));
#1795=DIRECTION('',(-0.120974291151355,-0.120974291151331,0.985256536015296));
#1796=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573677E-17));
#1797=DIRECTION('',(0.120974291151355,-0.120974291151331,0.985256536015296));
#1798=DIRECTION('',(6.12323399573676E-17,8.34001671142725E-17,1.));
#1799=DIRECTION('',(0.,-1.,8.34001671142725E-17));
#1800=DIRECTION('',(6.12323399573677E-17,-1.,8.34001671142725E-17));
#1801=DIRECTION('',(6.12323399573676E-17,8.34001671142725E-17,1.));
#1802=DIRECTION('',(-0.707106781186562,0.707106781186533,-1.56750209068167E-17));
#1803=DIRECTION('',(6.12323399573676E-17,8.34001671142725E-17,1.));
#1804=DIRECTION('',(0.707106781186561,0.707106781186534,-1.02270626530368E-16));
#1805=DIRECTION('',(6.12323399573677E-17,-1.,8.34001671142725E-17));
#1806=DIRECTION('',(6.12323399573676E-17,8.34001671142725E-17,1.));
#1807=DIRECTION('',(-0.707106781186616,0.707106781186479,-1.56750209068089E-17));
#1808=DIRECTION('',(6.12323399573676E-17,8.34001671142725E-17,1.));
#1809=DIRECTION('',(0.707106781186616,0.707106781186479,-1.02270626530367E-16));
#1810=DIRECTION('',(-0.992546151641322,-2.25004089323338E-15,-0.121869343405148));
#1811=DIRECTION('',(-6.12323399573687E-17,1.,-1.79640335188696E-14));
#1812=DIRECTION('',(-6.12323399573684E-17,1.,-1.79640335188696E-14));
#1813=DIRECTION('',(-0.120974291151355,0.12097429115135,0.985256536015294));
#1814=DIRECTION('',(0.120974291151355,0.120974291151331,-0.985256536015296));
#1815=DIRECTION('',(-0.992546151641322,2.12848904647202E-15,0.121869343405147));
#1816=DIRECTION('',(6.12323399573687E-17,-1.,1.79640335188696E-14));
#1817=DIRECTION('',(-0.120974291151355,-0.12097429115135,-0.985256536015294));
#1818=DIRECTION('',(6.12323399573691E-17,-1.,1.79640335188696E-14));
#1819=DIRECTION('',(0.120974291151355,-0.120974291151331,0.985256536015296));
#1820=DIRECTION('',(-0.120974291151355,0.120974291151331,-0.985256536015296));
#1821=DIRECTION('',(-0.992546151641322,0.,0.121869343405147));
#1822=DIRECTION('',(0.120974291151355,-0.120974291151331,0.985256536015296));
#1823=DIRECTION('',(0.992546151641322,0.,-0.121869343405147));
#1824=DIRECTION('',(0.,0.,1.));
#1825=DIRECTION('',(1.,0.,0.));
#1826=DIRECTION('',(-1.,0.,0.));
#1827=DIRECTION('',(0.,0.,1.));
#1828=DIRECTION('',(-7.84072074103867E-17,1.,-1.84314369322536E-14));
#1829=DIRECTION('',(0.,1.84314369322536E-14,1.));
#1830=DIRECTION('',(0.120974291151355,0.12097429115135,0.985256536015294));
#1831=DIRECTION('',(0.992546151641322,0.,-0.121869343405148));
#1832=DIRECTION('',(0.120974291151355,0.120974291151351,0.985256536015294));
#1833=DIRECTION('',(0.992546151641322,0.,-0.121869343405148));
#1834=DIRECTION('',(-6.12323399573689E-17,1.,-1.79640335188696E-14));
#1835=DIRECTION('',(0.,1.79640335188696E-14,1.));
#1836=DIRECTION('',(7.84072074103811E-17,-1.,1.84314369322536E-14));
#1837=DIRECTION('',(0.,-1.84314369322536E-14,-1.));
#1838=DIRECTION('',(1.,6.12323399573676E-17,-6.12323399573676E-17));
#1839=DIRECTION('',(-6.12323399573676E-17,0.,-1.));
#1840=DIRECTION('',(1.,-3.66089041116101E-16,2.98155597433514E-15));
#1841=DIRECTION('',(2.98155597433514E-15,0.,-1.));
#1842=DIRECTION('',(0.120974291151355,0.120974291151331,-0.985256536015296));
#1843=DIRECTION('',(-0.992546151641322,0.,-0.121869343405147));
#1844=DIRECTION('',(-0.120974291151355,-0.120974291151331,0.985256536015296));
#1845=DIRECTION('',(0.992546151641322,0.,0.121869343405147));
#1846=DIRECTION('',(0.,0.,1.));
#1847=DIRECTION('',(1.,0.,0.));
#1848=DIRECTION('',(-1.,-5.32493150714411E-16,-4.33680868994201E-15));
#1849=DIRECTION('',(-4.33680868994201E-15,0.,1.));
#1850=DIRECTION('',(0.,0.,1.));
#1851=DIRECTION('',(1.,0.,0.));
#1852=DIRECTION('',(7.84072074103912E-17,1.,-1.84314369322536E-14));
#1853=DIRECTION('',(0.,1.84314369322536E-14,1.));
#1854=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573676E-17));
#1855=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1856=DIRECTION('',(1.,6.82715213476856E-15,-1.25834223998887E-28));
#1857=DIRECTION('',(-6.82715213476856E-15,1.,0.));
#1858=DIRECTION('',(-6.12323399573686E-17,1.,-1.79640335188696E-14));
#1859=DIRECTION('',(0.,1.79640335188696E-14,1.));
#1860=DIRECTION('',(-7.84072074103856E-17,-1.,1.84314369322536E-14));
#1861=DIRECTION('',(0.,-1.84314369322536E-14,-1.));
#1862=DIRECTION('',(0.,0.,1.));
#1863=DIRECTION('',(1.,0.,0.));
#1864=DIRECTION('',(-0.120974291151356,0.12097429115135,0.985256536015294));
#1865=DIRECTION('',(0.992546151641322,0.,0.121869343405149));
#1866=DIRECTION('',(-0.120974291151355,0.12097429115135,0.985256536015294));
#1867=DIRECTION('',(0.992546151641322,0.,0.121869343405148));
#1868=DIRECTION('',(0.,0.,1.));
#1869=DIRECTION('',(1.,0.,0.));
#1870=DIRECTION('',(-6.82382684464342E-17,0.992546151641344,-0.121869343404972));
#1871=DIRECTION('',(0.,0.121869343404972,0.992546151641344));
#1872=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573677E-17));
#1873=DIRECTION('',(0.120974291151357,0.120974291151181,0.985256536015314));
#1874=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573677E-17));
#1875=DIRECTION('',(-0.120974291151356,0.120974291151181,0.985256536015314));
#1876=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#1877=DIRECTION('',(1.,0.,-6.12323399573677E-17));
#1878=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573677E-17));
#1879=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#1880=DIRECTION('',(-0.707106781186789,0.707106781186306,4.32978028117894E-17));
#1881=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1882=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#1883=DIRECTION('',(0.707106781187062,0.707106781186033,-4.32978028118062E-17));
#1884=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#1885=DIRECTION('',(-0.707106781187062,0.707106781186033,4.32978028118061E-17));
#1886=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1887=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#1888=DIRECTION('',(0.707106781186788,0.707106781186307,-4.32978028117894E-17));
#1889=DIRECTION('',(5.33135783149189E-17,-0.992546151641332,-0.121869343405066));
#1890=DIRECTION('',(0.,0.121869343405066,-0.992546151641332));
#1891=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573676E-17));
#1892=DIRECTION('',(-0.120974291151356,0.120974291151273,-0.985256536015303));
#1893=DIRECTION('',(0.120974291151356,0.120974291151273,-0.985256536015303));
#1894=DIRECTION('',(-6.12323399573691E-17,2.30955351786212E-14,-1.));
#1895=DIRECTION('',(0.,1.,2.30955351786212E-14));
#1896=DIRECTION('',(-6.12323399573662E-17,1.,2.30955351786212E-14));
#1897=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573676E-17));
#1898=DIRECTION('',(-6.12323399573666E-17,1.,2.30955351786212E-14));
#1899=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573676E-17));
#1900=DIRECTION('',(-0.992546151641322,-2.87541363118973E-15,0.121869343405147));
#1901=DIRECTION('',(-6.12323399573662E-17,1.,2.30955351786212E-14));
#1902=DIRECTION('',(0.120974291151356,-0.120974291151273,0.985256536015303));
#1903=DIRECTION('',(6.1232339957366E-17,-1.,-2.30955351786212E-14));
#1904=DIRECTION('',(-0.120974291151357,-0.120974291151181,-0.985256536015314));
#1905=DIRECTION('',(-0.992546151641322,2.75386178442837E-15,-0.121869343405147));
#1906=DIRECTION('',(6.12323399573662E-17,-1.,-2.30955351786212E-14));
#1907=DIRECTION('',(-6.1232339957366E-17,1.,2.30955351786212E-14));
#1908=DIRECTION('',(0.120974291151356,0.120974291151273,-0.985256536015303));
#1909=DIRECTION('',(-0.120974291151356,0.120974291151181,0.985256536015314));
#1910=DIRECTION('',(-0.120974291151356,0.120974291151181,0.985256536015314));
#1911=DIRECTION('',(0.992546151641322,0.,0.121869343405147));
#1912=DIRECTION('',(0.120974291151356,-0.120974291151181,-0.985256536015314));
#1913=DIRECTION('',(-0.992546151641322,0.,-0.121869343405147));
#1914=DIRECTION('',(0.,0.,1.));
#1915=DIRECTION('',(1.,0.,0.));
#1916=DIRECTION('',(2.66613266656412E-15,1.,2.27682456221956E-14));
#1917=DIRECTION('',(0.,-2.27682456221956E-14,1.));
#1918=DIRECTION('',(-1.,0.,0.));
#1919=DIRECTION('',(0.,0.,1.));
#1920=DIRECTION('',(0.120974291151357,0.120974291151181,0.985256536015314));
#1921=DIRECTION('',(0.992546151641322,0.,-0.121869343405147));
#1922=DIRECTION('',(-0.120974291151357,-0.120974291151181,-0.985256536015314));
#1923=DIRECTION('',(-0.992546151641322,0.,0.121869343405147));
#1924=DIRECTION('',(1.,6.12323399573676E-17,-6.12323399573676E-17));
#1925=DIRECTION('',(-6.12323399573677E-17,0.,-1.));
#1926=DIRECTION('',(1.,-1.91160259773517E-15,-4.35238374772614E-29));
#1927=DIRECTION('',(1.91160259773517E-15,1.,0.));
#1928=DIRECTION('',(-6.12323399573666E-17,1.,2.30955351786212E-14));
#1929=DIRECTION('',(0.,-2.30955351786212E-14,1.));
#1930=DIRECTION('',(-4.81443983438166E-16,-1.,-2.27682456221956E-14));
#1931=DIRECTION('',(0.,2.27682456221956E-14,-1.));
#1932=DIRECTION('',(0.120974291151356,0.120974291151273,-0.985256536015303));
#1933=DIRECTION('',(-0.992546151641322,0.,-0.121869343405147));
#1934=DIRECTION('',(0.120974291151356,0.120974291151273,-0.985256536015303));
#1935=DIRECTION('',(-0.992546151641322,0.,-0.121869343405147));
#1936=DIRECTION('',(0.,0.,1.));
#1937=DIRECTION('',(1.,0.,0.));
#1938=DIRECTION('',(-6.14567271116767E-16,1.,2.16840434497101E-14));
#1939=DIRECTION('',(0.,-2.16840434497101E-14,1.));
#1940=DIRECTION('',(0.,0.,1.));
#1941=DIRECTION('',(1.,0.,0.));
#1942=DIRECTION('',(-1.,0.,0.));
#1943=DIRECTION('',(0.,0.,1.));
#1944=DIRECTION('',(-6.12323399573662E-17,1.,2.30955351786212E-14));
#1945=DIRECTION('',(0.,-2.30955351786212E-14,1.));
#1946=DIRECTION('',(-6.10900358124824E-16,-1.,-2.27682456221956E-14));
#1947=DIRECTION('',(0.,2.27682456221956E-14,-1.));
#1948=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573676E-17));
#1949=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1950=DIRECTION('',(1.,8.65301369910359E-16,-7.04731412115576E-15));
#1951=DIRECTION('',(-7.04731412115576E-15,0.,-1.));
#1952=DIRECTION('',(0.,0.,1.));
#1953=DIRECTION('',(1.,0.,0.));
#1954=DIRECTION('',(-0.120974291151356,0.120974291151273,-0.985256536015303));
#1955=DIRECTION('',(-0.992546151641322,0.,0.121869343405148));
#1956=DIRECTION('',(-0.120974291151356,0.120974291151273,-0.985256536015303));
#1957=DIRECTION('',(-0.992546151641322,0.,0.121869343405148));
#1958=DIRECTION('',(-5.55111512312578E-17,1.11022302462516E-16,1.));
#1959=DIRECTION('',(1.,5.55111512312578E-17,5.55111512312578E-17));
#1960=DIRECTION('',(0.,0.,1.));
#1961=DIRECTION('',(1.,0.,0.));
#1962=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#1963=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#1964=DIRECTION('',(-4.95297865625808E-14,-6.12323399573677E-17,-1.));
#1965=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#1966=DIRECTION('',(4.95297865625811E-14,6.12323399573677E-17,1.));
#1967=DIRECTION('',(0.,1.,-6.12323399573677E-17));
#1968=DIRECTION('',(1.,0.,0.));
#1969=DIRECTION('',(-0.992546151641392,7.46234506573018E-18,0.121869343404576));
#1970=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#1971=DIRECTION('',(1.,0.,0.));
#1972=DIRECTION('',(-0.0697564737438654,6.10831810547593E-17,0.997564050259842));
#1973=DIRECTION('',(0.997564050259827,4.27135211452509E-18,0.0697564737440865));
#1974=DIRECTION('',(0.0697564737438592,-6.10831810547593E-17,-0.997564050259843));
#1975=DIRECTION('',(0.,1.,-6.12323399573677E-17));
#1976=DIRECTION('',(1.,0.,0.));
#1977=DIRECTION('',(0.992546151641052,-7.46234506590008E-18,-0.121869343407351));
#1978=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#1979=DIRECTION('',(1.,0.,0.));
#1980=DIRECTION('',(1.,-3.03282472881583E-30,-4.95297865625811E-14));
#1981=DIRECTION('',(-4.95297865625811E-14,0.,-1.));
#1982=DIRECTION('',(3.03282472881583E-30,1.,-3.74939945665483E-33));
#1983=DIRECTION('',(4.95297865625811E-14,-1.85037170770842E-14,1.));
#1984=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#1985=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#1986=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#1987=DIRECTION('',(-6.12323399573665E-17,1.,1.85037170770842E-14));
#1988=DIRECTION('',(0.,-1.85037170770842E-14,1.));
#1989=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#1990=DIRECTION('',(0.121869343404576,6.07759233806827E-17,0.992546151641392));
#1991=DIRECTION('',(0.992546151641392,0.,-0.121869343404576));
#1992=DIRECTION('',(-0.992546151641392,-2.31581177410897E-15,0.121869343404576));
#1993=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#1994=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#1995=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#1996=DIRECTION('',(-6.12323399573665E-17,1.,1.85037170770842E-14));
#1997=DIRECTION('',(0.,-1.85037170770842E-14,1.));
#1998=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#1999=DIRECTION('',(0.997564050259842,4.27135211451156E-18,0.0697564737438654));
#2000=DIRECTION('',(0.0697564737438654,0.,-0.997564050259842));
#2001=DIRECTION('',(-0.0697564737438654,-1.84629143043929E-14,0.997564050259842));
#2002=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2003=DIRECTION('',(0.0697564737440865,-6.10831810547583E-17,-0.997564050259827));
#2004=DIRECTION('',(-0.997564050259827,0.,-0.0697564737440865));
#2005=DIRECTION('',(0.997564050259827,-1.22967087340087E-15,0.0697564737440865));
#2006=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2007=DIRECTION('',(-0.997564050259843,-4.27135211451118E-18,-0.0697564737438592));
#2008=DIRECTION('',(-0.0697564737438592,0.,0.997564050259843));
#2009=DIRECTION('',(0.0697564737438592,1.84629143043929E-14,-0.997564050259843));
#2010=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2011=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2012=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2013=DIRECTION('',(-6.12323399573665E-17,1.,1.85037170770842E-14));
#2014=DIRECTION('',(0.,-1.85037170770842E-14,1.));
#2015=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2016=DIRECTION('',(-0.121869343407351,-6.07759233806619E-17,-0.992546151641052));
#2017=DIRECTION('',(-0.992546151641052,0.,0.121869343407351));
#2018=DIRECTION('',(0.992546151641052,2.31581177416029E-15,-0.121869343407351));
#2019=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2020=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2021=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2022=DIRECTION('',(-6.12323399573665E-17,1.,1.85037170770842E-14));
#2023=DIRECTION('',(0.,-1.85037170770842E-14,1.));
#2024=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2025=DIRECTION('',(-1.,3.03282472881581E-30,4.95297865625808E-14));
#2026=DIRECTION('',(4.95297865625808E-14,0.,1.));
#2027=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2028=DIRECTION('',(-6.12323399573688E-17,1.85037170770842E-14,-1.));
#2029=DIRECTION('',(-6.12323399573677E-17,-3.74939945665464E-33,-1.));
#2030=DIRECTION('',(-1.,0.,6.12323399573677E-17));
#2031=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#2032=DIRECTION('',(-6.12323399573665E-17,1.,1.85037170770842E-14));
#2033=DIRECTION('',(0.,-1.85037170770842E-14,1.));
#2034=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2035=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2036=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#2037=DIRECTION('',(-4.95297865625808E-14,-6.12323399573677E-17,-1.));
#2038=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2039=DIRECTION('',(1.,0.,0.));
#2040=DIRECTION('',(0.992546151641052,-7.46234506590008E-18,-0.121869343407351));
#2041=DIRECTION('',(0.,1.,-6.12323399573677E-17));
#2042=DIRECTION('',(1.,0.,0.));
#2043=DIRECTION('',(0.0697564737438592,-6.10831810547593E-17,-0.997564050259843));
#2044=DIRECTION('',(0.997564050259827,4.27135211452509E-18,0.0697564737440865));
#2045=DIRECTION('',(-0.0697564737438654,6.10831810547593E-17,0.997564050259842));
#2046=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2047=DIRECTION('',(1.,0.,0.));
#2048=DIRECTION('',(-0.992546151641392,7.46234506573018E-18,0.121869343404576));
#2049=DIRECTION('',(0.,1.,-6.12323399573677E-17));
#2050=DIRECTION('',(1.,0.,0.));
#2051=DIRECTION('',(4.95297865625811E-14,6.12323399573677E-17,1.));
#2052=DIRECTION('',(1.,-3.03282472881583E-30,-4.95297865625811E-14));
#2053=DIRECTION('',(-4.95297865625811E-14,0.,-1.));
#2054=DIRECTION('',(-4.95297865625811E-14,-3.03282472881583E-30,-1.));
#2055=DIRECTION('',(8.62237659830913E-28,1.,1.7347234759764E-14));
#2056=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2057=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2058=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2059=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2060=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2061=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2062=DIRECTION('',(0.121869343404576,6.07759233806827E-17,0.992546151641392));
#2063=DIRECTION('',(0.992546151641392,0.,-0.121869343404576));
#2064=DIRECTION('',(0.992546151641392,6.07759233806827E-17,-0.121869343404576));
#2065=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2066=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2067=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2068=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2069=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2070=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2071=DIRECTION('',(0.997564050259842,4.27135211451156E-18,0.0697564737438654));
#2072=DIRECTION('',(0.0697564737438654,0.,-0.997564050259842));
#2073=DIRECTION('',(0.0697564737438654,4.27135211451156E-18,-0.997564050259842));
#2074=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2075=DIRECTION('',(0.0697564737440865,-6.10831810547583E-17,-0.997564050259827));
#2076=DIRECTION('',(-0.997564050259827,0.,-0.0697564737440865));
#2077=DIRECTION('',(-0.997564050259827,-6.10831810547583E-17,-0.0697564737440865));
#2078=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2079=DIRECTION('',(-0.997564050259843,-4.27135211451118E-18,-0.0697564737438592));
#2080=DIRECTION('',(-0.0697564737438592,0.,0.997564050259843));
#2081=DIRECTION('',(-0.0697564737438592,-4.27135211451118E-18,0.997564050259843));
#2082=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2083=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2084=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2085=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2086=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2087=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2088=DIRECTION('',(-0.121869343407351,-6.07759233806619E-17,-0.992546151641052));
#2089=DIRECTION('',(-0.992546151641052,0.,0.121869343407351));
#2090=DIRECTION('',(-0.992546151641052,-6.07759233806619E-17,0.121869343407351));
#2091=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2092=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2093=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2094=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2095=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2096=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2097=DIRECTION('',(-1.,3.03282472881581E-30,4.95297865625808E-14));
#2098=DIRECTION('',(4.95297865625808E-14,0.,1.));
#2099=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#2100=DIRECTION('',(6.12323399573666E-17,-1.,-1.7347234759764E-14));
#2101=DIRECTION('',(-6.12323399573687E-17,1.7347234759764E-14,-1.));
#2102=DIRECTION('',(0.,1.,1.7347234759764E-14));
#2103=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#2104=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2105=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2106=DIRECTION('',(0.12186934340747,-6.0775923380661E-17,-0.992546151641037));
#2107=DIRECTION('',(-0.992546151641037,0.,-0.12186934340747));
#2108=DIRECTION('',(-0.992546151641037,-1.18829384877159E-15,-0.12186934340747));
#2109=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2110=DIRECTION('',(0.992546151641037,1.18829384877159E-15,0.12186934340747));
#2111=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2112=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2113=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2114=DIRECTION('',(6.12323399573682E-17,-1.,9.25185853854219E-15));
#2115=DIRECTION('',(0.,-9.25185853854219E-15,-1.));
#2116=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2117=DIRECTION('',(-6.12323399573682E-17,1.,-9.25185853854219E-15));
#2118=DIRECTION('',(0.,9.25185853854219E-15,1.));
#2119=DIRECTION('',(0.997564050259852,-4.27135211450339E-18,-0.069756473743732));
#2120=DIRECTION('',(-0.069756473743732,0.,-0.997564050259852));
#2121=DIRECTION('',(-0.069756473743732,-9.23359282825385E-15,-0.997564050259852));
#2122=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2123=DIRECTION('',(0.069756473743732,9.23359282825385E-15,0.997564050259852));
#2124=DIRECTION('',(0.0697564737441493,6.10831810547581E-17,0.997564050259823));
#2125=DIRECTION('',(0.997564050259823,0.,-0.0697564737441493));
#2126=DIRECTION('',(0.997564050259823,-5.84293846173643E-16,-0.0697564737441493));
#2127=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2128=DIRECTION('',(-0.997564050259823,5.84293846173643E-16,0.0697564737441493));
#2129=DIRECTION('',(-0.997564050259837,4.27135211451608E-18,0.0697564737439392));
#2130=DIRECTION('',(0.0697564737439392,0.,0.997564050259837));
#2131=DIRECTION('',(0.0697564737439392,9.23359282825373E-15,0.997564050259837));
#2132=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2133=DIRECTION('',(-0.0697564737439392,-9.23359282825373E-15,-0.997564050259837));
#2134=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2135=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2136=DIRECTION('',(6.12323399573682E-17,-1.,9.25185853854219E-15));
#2137=DIRECTION('',(0.,-9.25185853854219E-15,-1.));
#2138=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2139=DIRECTION('',(-6.12323399573682E-17,1.,-9.25185853854219E-15));
#2140=DIRECTION('',(0.,9.25185853854219E-15,1.));
#2141=DIRECTION('',(-0.121869343407133,6.07759233806635E-17,0.992546151641078));
#2142=DIRECTION('',(0.992546151641078,0.,0.121869343407133));
#2143=DIRECTION('',(0.992546151641078,1.18829384876848E-15,0.121869343407133));
#2144=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2145=DIRECTION('',(-0.992546151641078,-1.18829384876848E-15,-0.121869343407133));
#2146=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2147=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2148=DIRECTION('',(6.12323399573682E-17,-1.,9.25185853854219E-15));
#2149=DIRECTION('',(0.,-9.25185853854219E-15,-1.));
#2150=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2151=DIRECTION('',(-6.12323399573682E-17,1.,-9.25185853854219E-15));
#2152=DIRECTION('',(0.,9.25185853854219E-15,1.));
#2153=DIRECTION('',(-1.,3.03282472881581E-30,4.95297865625808E-14));
#2154=DIRECTION('',(4.95297865625808E-14,0.,1.));
#2155=DIRECTION('',(6.12323399573671E-17,9.25185853854219E-15,1.));
#2156=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2157=DIRECTION('',(-6.12323399573671E-17,-9.25185853854219E-15,-1.));
#2158=DIRECTION('',(1.,-3.03282472881583E-30,-4.95297865625811E-14));
#2159=DIRECTION('',(-4.95297865625811E-14,0.,-1.));
#2160=DIRECTION('',(-4.95297865625811E-14,-9.2518585385422E-15,-1.));
#2161=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2162=DIRECTION('',(4.95297865625811E-14,9.2518585385422E-15,1.));
#2163=DIRECTION('',(-3.03282472881583E-30,-1.,3.74939945665483E-33));
#2164=DIRECTION('',(0.,-1.,6.12323399573677E-17));
#2165=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#2166=DIRECTION('',(6.12323399573682E-17,-1.,9.25185853854219E-15));
#2167=DIRECTION('',(0.,-9.25185853854219E-15,-1.));
#2168=DIRECTION('',(-6.12323399573682E-17,1.,-9.25185853854219E-15));
#2169=DIRECTION('',(0.,9.25185853854219E-15,1.));
#2170=DIRECTION('',(6.12323399573682E-17,-1.,9.25185853854219E-15));
#2171=DIRECTION('',(0.,-9.25185853854219E-15,-1.));
#2172=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#2173=DIRECTION('',(-6.12323399573682E-17,1.,-9.25185853854219E-15));
#2174=DIRECTION('',(0.,9.25185853854219E-15,1.));
#2175=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#2176=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#2177=DIRECTION('',(1.,0.,-6.12323399573677E-17));
#2178=DIRECTION('',(2.94448664910389E-17,-1.,6.74591256119308E-14));
#2179=DIRECTION('',(-3.34767643417359E-17,-6.74591256119308E-14,-1.));
#2180=CARTESIAN_POINT('',(0.,0.,0.));
#2181=CARTESIAN_POINT('',(-2.9,-0.649999999999984,-0.287499999999989));
#2182=CARTESIAN_POINT('',(-2.9,-0.649999999999984,-0.287499999999989));
#2183=CARTESIAN_POINT('',(-2.80074538483586,-0.649999999999984,-0.287499999999989));
#2184=CARTESIAN_POINT('',(-0.0992546151641345,-0.649999999999984,-0.287499999999989));
#2185=CARTESIAN_POINT('',(-0.168403168313777,-0.580851446850344,0.275669771843911));
#2186=CARTESIAN_POINT('',(-0.159073648974764,-0.590180966189357,0.199686934340502));
#2187=CARTESIAN_POINT('',(-2.9,-0.590180966189357,0.199686934340502));
#2188=CARTESIAN_POINT('',(-2.74092635102523,-0.590180966189357,0.199686934340502));
#2189=CARTESIAN_POINT('',(-2.80219795420539,-0.651452569369509,-0.299330228156068));
#2190=CARTESIAN_POINT('',(-2.9,-0.579398877480819,0.287499999999989));
#2191=CARTESIAN_POINT('',(-2.74092635102524,-0.579398877480819,0.287499999999989));
#2192=CARTESIAN_POINT('',(-2.74092635102524,0.490926351025224,0.28749999999997));
#2193=CARTESIAN_POINT('',(-2.74092635102524,-0.490926351025223,0.287499999999988));
#2194=CARTESIAN_POINT('',(-2.9,-0.490926351025223,0.287499999999988));
#2195=CARTESIAN_POINT('',(-0.159073648974765,-0.490926351025222,0.287499999999988));
#2196=CARTESIAN_POINT('',(-0.159073648974765,0.579398877480819,0.287499999999968));
#2197=CARTESIAN_POINT('',(-0.159073648974765,0.490926351025224,0.28749999999997));
#2198=CARTESIAN_POINT('',(-2.9,0.490926351025224,0.28749999999997));
#2199=CARTESIAN_POINT('',(-2.9,0.579398877480819,0.287499999999969));
#2200=CARTESIAN_POINT('',(-2.9,0.590180966189355,0.199686934340483));
#2201=CARTESIAN_POINT('',(-2.74092635102524,0.590180966189355,0.199686934340482));
#2202=CARTESIAN_POINT('',(-0.159073648974764,0.590180966189355,0.199686934340482));
#2203=CARTESIAN_POINT('',(-0.0978020457946121,0.651452569369495,-0.299330228156069));
#2204=CARTESIAN_POINT('',(-0.0992546151641375,0.64999999999997,-0.287499999999989));
#2205=CARTESIAN_POINT('',(-2.9,0.64999999999997,-0.287499999999989));
#2206=CARTESIAN_POINT('',(-2.80074538483586,0.64999999999997,-0.287499999999989));
#2207=CARTESIAN_POINT('',(-2.73159683168622,0.580851446850344,0.27566977184389));
#2208=CARTESIAN_POINT('',(-2.9,0.64999999999997,-0.287499999999989));
#2209=CARTESIAN_POINT('',(-5.74053187100297E-17,0.64999999999997,-0.287499999999989));
#2210=CARTESIAN_POINT('',(-5.13277263719619E-17,0.550745384835838,-0.287499999999989));
#2211=CARTESIAN_POINT('',(1.61191308964776E-17,-0.550745384835852,-0.287499999999989));
#2212=CARTESIAN_POINT('',(-0.100750982545887,-0.549249017454099,-0.287499999999989));
#2213=CARTESIAN_POINT('',(-2.79924901745411,-0.549249017454099,-0.287499999999989));
#2214=CARTESIAN_POINT('',(-2.9,-0.550745384835852,-0.287499999999989));
#2215=CARTESIAN_POINT('',(-2.9,0.64999999999997,-0.287499999999989));
#2216=CARTESIAN_POINT('',(-2.9,0.550745384835838,-0.287499999999989));
#2217=CARTESIAN_POINT('',(-2.79924901745411,0.549249017454085,-0.287499999999989));
#2218=CARTESIAN_POINT('',(-0.10075098254589,0.549249017454085,-0.287499999999989));
#2219=CARTESIAN_POINT('',(-0.0706011225191702,-0.579398877480819,0.287499999999989));
#2220=CARTESIAN_POINT('',(-0.0598190338106323,-0.579398877480821,0.199686934340504));
#2221=CARTESIAN_POINT('',(-0.0598190338106324,0.490926351025222,0.199686934340485));
#2222=CARTESIAN_POINT('',(-0.0598190338106323,-0.490926351025224,0.199686934340502));
#2223=CARTESIAN_POINT('',(0.00145256936952256,-0.552197954205377,-0.299330228156068));
#2224=CARTESIAN_POINT('',(-0.069148553149645,0.481596831686212,0.275669771843892));
#2225=CARTESIAN_POINT('',(-2.9,-5.34223342255175E-15,-0.287500000000021));
#2226=CARTESIAN_POINT('',(-2.83085144685035,-0.481596831686212,0.275669771843911));
#2227=CARTESIAN_POINT('',(-2.84018096618937,-0.490926351025224,0.199686934340502));
#2228=CARTESIAN_POINT('',(-2.84018096618937,0.579398877480817,0.199686934340483));
#2229=CARTESIAN_POINT('',(-2.84018096618937,0.490926351025222,0.199686934340485));
#2230=CARTESIAN_POINT('',(-2.90145256936952,0.552197954205363,-0.299330228156066));
#2231=CARTESIAN_POINT('',(-2.73263006351988,0.482630063519873,0.255067839509623));
#2232=CARTESIAN_POINT('',(-2.74092635102524,0.490926351025222,0.18749999999997));
#2233=CARTESIAN_POINT('',(-2.74092635102524,0.490926351025222,0.18749999999997));
#2234=CARTESIAN_POINT('',(-2.74092635102524,0.490926351025222,0.18749999999997));
#2235=CARTESIAN_POINT('',(-2.74092635102524,0.490926351025222,0.18749999999997));
#2236=CARTESIAN_POINT('',(-2.80219795420539,-0.552197954205377,-0.311517162496582));
#2237=CARTESIAN_POINT('',(-2.74092635102524,-0.490926351025225,0.187499999999988));
#2238=CARTESIAN_POINT('',(-2.74092635102524,-0.579398877480821,0.187499999999989));
#2239=CARTESIAN_POINT('',(-2.74092635102524,-0.490926351025225,0.187499999999988));
#2240=CARTESIAN_POINT('',(-2.9,0.490926351025222,0.18749999999997));
#2241=CARTESIAN_POINT('',(-0.159073648974764,0.490926351025222,0.18749999999997));
#2242=CARTESIAN_POINT('',(-0.209810795926876,0.440189204073121,0.600720901545023));
#2243=CARTESIAN_POINT('',(-0.159073648974765,0.490926351025222,0.18749999999997));
#2244=CARTESIAN_POINT('',(-2.74092635102524,-0.490926351025225,0.187499999999988));
#2245=CARTESIAN_POINT('',(-2.74092635102523,-0.490926351025225,0.187499999999988));
#2246=CARTESIAN_POINT('',(-0.159073648974765,0.490926351025222,0.18749999999997));
#2247=CARTESIAN_POINT('',(-0.159073648974765,0.490926351025222,0.18749999999997));
#2248=CARTESIAN_POINT('',(-2.9,-0.490926351025225,0.187499999999988));
#2249=CARTESIAN_POINT('',(-0.159073648974765,-0.490926351025224,0.187499999999988));
#2250=CARTESIAN_POINT('',(-0.159073648974765,-0.579398877480821,0.187499999999989));
#2251=CARTESIAN_POINT('',(-0.159073648974765,-0.490926351025224,0.187499999999988));
#2252=CARTESIAN_POINT('',(-0.159073648974765,-0.490926351025224,0.187499999999988));
#2253=CARTESIAN_POINT('',(-0.159073648974764,-0.490926351025224,0.187499999999988));
#2254=CARTESIAN_POINT('',(-0.14024290524137,-0.509757094758617,0.0341358995388176));
#2255=CARTESIAN_POINT('',(0.,0.,0.));
#2256=CARTESIAN_POINT('',(-2.9,0.600886175638873,-0.200000000000012));
#2257=CARTESIAN_POINT('',(-2.9,0.64999999999997,0.200000000000041));
#2258=CARTESIAN_POINT('',(-2.80074538483587,0.64999999999997,0.200000000000041));
#2259=CARTESIAN_POINT('',(-0.099254615164128,0.64999999999997,0.20000000000004));
#2260=CARTESIAN_POINT('',(-0.18863795963129,0.560616655532938,-0.527968922231516));
#2261=CARTESIAN_POINT('',(-0.137586350816756,0.611668264347397,-0.112186934340512));
#2262=CARTESIAN_POINT('',(-2.9,0.611668264347397,-0.112186934340512));
#2263=CARTESIAN_POINT('',(-2.76241364918324,0.611668264347397,-0.112186934340512));
#2264=CARTESIAN_POINT('',(-2.75380289981547,0.603057514979638,-0.182315860196104));
#2265=CARTESIAN_POINT('',(-2.9,0.64999999999997,0.200000000000041));
#2266=CARTESIAN_POINT('',(-2.9,-0.649999999999984,0.200000000000041));
#2267=CARTESIAN_POINT('',(-2.80074538483588,-0.649999999999984,0.200000000000041));
#2268=CARTESIAN_POINT('',(-0.0992546151641232,-0.649999999999984,0.20000000000004));
#2269=CARTESIAN_POINT('',(-0.100750982545875,-0.5492490174541,0.20000000000004));
#2270=CARTESIAN_POINT('',(4.59698966256961E-17,-0.550745384835851,0.20000000000004));
#2271=CARTESIAN_POINT('',(-2.75545529808111E-17,0.64999999999997,0.20000000000004));
#2272=CARTESIAN_POINT('',(-2.14769606427433E-17,0.550745384835838,0.20000000000004));
#2273=CARTESIAN_POINT('',(-0.100750982545878,0.549249017454087,0.20000000000004));
#2274=CARTESIAN_POINT('',(-2.79924901745412,0.549249017454087,0.200000000000041));
#2275=CARTESIAN_POINT('',(-2.9,0.550745384835838,0.200000000000041));
#2276=CARTESIAN_POINT('',(-2.9,0.64999999999997,0.200000000000041));
#2277=CARTESIAN_POINT('',(-2.9,-0.550745384835851,0.200000000000041));
#2278=CARTESIAN_POINT('',(-2.79924901745412,-0.5492490174541,0.200000000000041));
#2279=CARTESIAN_POINT('',(-2.9,-0.649999999999984,0.200000000000041));
#2280=CARTESIAN_POINT('',(-2.9,-0.611668264347378,-0.112186934340545));
#2281=CARTESIAN_POINT('',(-2.76241364918324,-0.611668264347378,-0.112186934340545));
#2282=CARTESIAN_POINT('',(-0.137586350816756,-0.611668264347377,-0.112186934340545));
#2283=CARTESIAN_POINT('',(-0.14024290524136,-0.609011709922775,-0.133822833879288));
#2284=CARTESIAN_POINT('',(-2.8021979542054,-0.651452569369508,0.211830228156118));
#2285=CARTESIAN_POINT('',(-2.9,-0.600886175638846,-0.20000000000004));
#2286=CARTESIAN_POINT('',(-0.137586350816757,-0.600886175638846,-0.20000000000004));
#2287=CARTESIAN_POINT('',(-0.137586350816757,0.512413649183265,-0.200000000000015));
#2288=CARTESIAN_POINT('',(-0.137586350816757,-0.512413649183242,-0.200000000000038));
#2289=CARTESIAN_POINT('',(-2.9,-0.512413649183242,-0.200000000000038));
#2290=CARTESIAN_POINT('',(-2.76241364918324,-0.512413649183242,-0.200000000000038));
#2291=CARTESIAN_POINT('',(-2.76241364918324,-0.600886175638846,-0.20000000000004));
#2292=CARTESIAN_POINT('',(-2.76241364918324,0.512413649183265,-0.200000000000015));
#2293=CARTESIAN_POINT('',(-2.9,0.512413649183265,-0.200000000000014));
#2294=CARTESIAN_POINT('',(1.22464679914722E-17,-4.61910703572363E-15,0.199999999999973));
#2295=CARTESIAN_POINT('',(-0.00806003705818967,-0.542685347777657,0.134356265975751));
#2296=CARTESIAN_POINT('',(-0.0383317356526239,-0.512413649183244,-0.112186934340553));
#2297=CARTESIAN_POINT('',(-0.038331735652624,2.588670146775E-15,-0.112186934340541));
#2298=CARTESIAN_POINT('',(-0.038331735652624,0.512413649183263,-0.112186934340529));
#2299=CARTESIAN_POINT('',(-0.00806003705818662,0.542685347777657,0.134356265975801));
#2300=CARTESIAN_POINT('',(-2.85088617563884,-0.600886175638846,-0.20000000000004));
#2301=CARTESIAN_POINT('',(-2.86166826434738,-0.600886175638848,-0.112186934340555));
#2302=CARTESIAN_POINT('',(-2.86166826434738,0.512413649183263,-0.112186934340529));
#2303=CARTESIAN_POINT('',(-2.86166826434738,-0.512413649183244,-0.112186934340553));
#2304=CARTESIAN_POINT('',(-2.85233874500836,-0.503084129844237,-0.18816977184397));
#2305=CARTESIAN_POINT('',(-2.83475107209542,0.485496456931351,-0.331409872908818));
#2306=CARTESIAN_POINT('',(-2.75380289981547,0.503802899815504,-0.170128925855607));
#2307=CARTESIAN_POINT('',(-2.76241364918324,0.512413649183263,-0.100000000000015));
#2308=CARTESIAN_POINT('',(-2.76241364918324,0.512413649183263,-0.100000000000015));
#2309=CARTESIAN_POINT('',(-2.76241364918324,0.512413649183263,-0.100000000000015));
#2310=CARTESIAN_POINT('',(-2.76241364918324,0.512413649183263,-0.100000000000015));
#2311=CARTESIAN_POINT('',(-0.18863795963129,0.461362040368804,-0.515781987891019));
#2312=CARTESIAN_POINT('',(-0.137586350816756,0.512413649183263,-0.100000000000015));
#2313=CARTESIAN_POINT('',(-2.9,0.512413649183263,-0.100000000000015));
#2314=CARTESIAN_POINT('',(-0.137586350816757,0.512413649183263,-0.100000000000015));
#2315=CARTESIAN_POINT('',(-2.76241364918324,-0.600886175638848,-0.10000000000004));
#2316=CARTESIAN_POINT('',(-2.76241364918324,-0.512413649183244,-0.100000000000038));
#2317=CARTESIAN_POINT('',(-2.8021979542054,-0.552197954205375,0.224017162496625));
#2318=CARTESIAN_POINT('',(-2.76241364918324,-0.512413649183244,-0.100000000000038));
#2319=CARTESIAN_POINT('',(-0.137586350816756,0.512413649183263,-0.100000000000015));
#2320=CARTESIAN_POINT('',(-0.137586350816756,0.512413649183263,-0.100000000000015));
#2321=CARTESIAN_POINT('',(-2.76241364918324,-0.512413649183244,-0.100000000000038));
#2322=CARTESIAN_POINT('',(-2.76241364918324,-0.512413649183244,-0.100000000000038));
#2323=CARTESIAN_POINT('',(-0.137586350816757,-0.600886175638848,-0.10000000000004));
#2324=CARTESIAN_POINT('',(-0.137586350816757,-0.512413649183244,-0.100000000000038));
#2325=CARTESIAN_POINT('',(-2.9,-0.512413649183244,-0.100000000000038));
#2326=CARTESIAN_POINT('',(-0.137586350816756,-0.512413649183244,-0.100000000000038));
#2327=CARTESIAN_POINT('',(-0.137586350816756,-0.512413649183244,-0.100000000000038));
#2328=CARTESIAN_POINT('',(-0.137586350816756,-0.512413649183244,-0.100000000000038));
#2329=CARTESIAN_POINT('',(-0.14024290524136,-0.509757094758642,-0.121635899538781));
#2330=CARTESIAN_POINT('',(0.,-1.0842021724855E-16,-0.48750000000003));
#2331=CARTESIAN_POINT('',(0.,0.,0.));
#2332=CARTESIAN_POINT('',(0.226539250368675,1.11499999999999,1.01483567354948));
#2333=CARTESIAN_POINT('',(7.63278329429795E-14,1.11499999999999,0.700476863062984));
#2334=CARTESIAN_POINT('',(-1.01951846029015E-16,1.11500000000001,-0.549999999999953));
#2335=CARTESIAN_POINT('',(6.93889390390723E-15,1.11499999999999,-0.700476863063002));
#2336=CARTESIAN_POINT('',(3.35298549331125,1.11500000000001,-0.549999999999954));
#2337=CARTESIAN_POINT('',(0.110000000000027,1.11499999999999,-0.549999999999953));
#2338=CARTESIAN_POINT('',(0.11000000000002,1.11499999999999,-0.700476863063043));
#2339=CARTESIAN_POINT('',(0.110000000000023,1.11499999999999,-0.700476863063043));
#2340=CARTESIAN_POINT('',(0.21000000000005,1.11499999999999,-0.700476863063022));
#2341=CARTESIAN_POINT('',(0.19781306565959,1.11499999999999,-0.799731478227189));
#2342=CARTESIAN_POINT('',(0.252131812483609,1.11499999999999,-0.80640098170474));
#2343=CARTESIAN_POINT('',(0.252131812483609,1.11499999999999,-0.806400981704738));
#2344=CARTESIAN_POINT('',(0.226539250368647,1.11499999999999,-1.01483567354944));
#2345=CARTESIAN_POINT('',(0.436027700923228,1.11499999999999,-1.00018681406323));
#2346=CARTESIAN_POINT('',(0.450000000000013,1.11499999999999,-1.20000000000005));
#2347=CARTESIAN_POINT('',(0.450000000000013,1.11499999999999,-1.20000000000005));
#2348=CARTESIAN_POINT('',(0.340267954471445,1.11499999999999,-1.2076732121119));
#2349=CARTESIAN_POINT('',(0.340267954471445,1.11499999999999,-1.2076732121119));
#2350=CARTESIAN_POINT('',(0.326295655394668,1.11499999999999,-1.00786002617519));
#2351=CARTESIAN_POINT('',(0.326295655394636,1.11499999999999,-1.00786002617519));
#2352=CARTESIAN_POINT('',(0.226539250368647,1.11499999999999,-1.01483567354944));
#2353=CARTESIAN_POINT('',(0.238726184709409,1.11499999999999,-0.915581058385476));
#2354=CARTESIAN_POINT('',(0.184407437884489,1.11499999999999,-0.90891155490766));
#2355=CARTESIAN_POINT('',(0.18440743788449,1.11499999999999,-0.908911554907652));
#2356=CARTESIAN_POINT('',(0.210000000000037,1.11499999999999,-0.700476863063002));
#2357=CARTESIAN_POINT('',(0.11000000000002,1.11499999999999,-0.700476863063043));
#2358=CARTESIAN_POINT('',(0.110000000000027,1.11499999999999,-0.549999999999953));
#2359=CARTESIAN_POINT('',(0.110000000000024,0.71499999999991,-0.549999999999953));
#2360=CARTESIAN_POINT('',(0.11000000000002,0.714999999999913,-0.700476863063051));
#2361=CARTESIAN_POINT('',(0.11000000000002,0.714999999999913,-0.700476863063043));
#2362=CARTESIAN_POINT('',(0.110000000000023,1.11499999999999,-0.700476863063043));
#2363=CARTESIAN_POINT('',(0.21000000000005,1.11499999999999,-0.700476863063022));
#2364=CARTESIAN_POINT('',(0.21000000000005,0.714999999999913,-0.700476863063022));
#2365=CARTESIAN_POINT('',(0.19781306565959,0.714999999999915,-0.799731478227189));
#2366=CARTESIAN_POINT('',(0.19781306565959,1.11499999999999,-0.799731478227189));
#2367=CARTESIAN_POINT('',(0.252131812483609,1.11499999999999,-0.80640098170474));
#2368=CARTESIAN_POINT('',(0.25213181248361,0.714999999999915,-0.80640098170474));
#2369=CARTESIAN_POINT('',(0.252131812483609,0.714999999999915,-0.80640098170474));
#2370=CARTESIAN_POINT('',(0.252131812483609,1.11499999999999,-0.806400981704738));
#2371=CARTESIAN_POINT('',(0.226539250368647,1.11499999999999,-1.01483567354944));
#2372=CARTESIAN_POINT('',(0.226539250368647,0.714999999999919,-1.01483567354944));
#2373=CARTESIAN_POINT('',(0.436027700923228,0.714999999999919,-1.00018681406323));
#2374=CARTESIAN_POINT('',(0.436027700923228,1.11499999999999,-1.00018681406323));
#2375=CARTESIAN_POINT('',(0.450000000000013,1.11499999999999,-1.20000000000005));
#2376=CARTESIAN_POINT('',(0.450000000000013,0.714999999999922,-1.20000000000006));
#2377=CARTESIAN_POINT('',(0.450000000000013,0.714999999999922,-1.20000000000005));
#2378=CARTESIAN_POINT('',(0.450000000000013,1.11499999999999,-1.20000000000005));
#2379=CARTESIAN_POINT('',(0.340267954471445,1.11499999999999,-1.2076732121119));
#2380=CARTESIAN_POINT('',(0.340267954471444,0.714999999999922,-1.2076732121119));
#2381=CARTESIAN_POINT('',(0.340267954471445,0.714999999999922,-1.2076732121119));
#2382=CARTESIAN_POINT('',(0.340267954471445,1.11499999999999,-1.2076732121119));
#2383=CARTESIAN_POINT('',(0.326295655394668,1.11499999999999,-1.00786002617519));
#2384=CARTESIAN_POINT('',(0.326295655394669,0.714999999999919,-1.0078600261752));
#2385=CARTESIAN_POINT('',(0.326295655394668,0.714999999999919,-1.00786002617519));
#2386=CARTESIAN_POINT('',(0.326295655394636,1.11499999999999,-1.00786002617519));
#2387=CARTESIAN_POINT('',(0.226539250368647,1.11499999999999,-1.01483567354944));
#2388=CARTESIAN_POINT('',(0.226539250368647,0.714999999999919,-1.01483567354944));
#2389=CARTESIAN_POINT('',(0.238726184709425,0.714999999999917,-0.91558105838535));
#2390=CARTESIAN_POINT('',(0.238726184709409,1.11499999999999,-0.915581058385476));
#2391=CARTESIAN_POINT('',(0.184407437884489,1.11499999999999,-0.90891155490766));
#2392=CARTESIAN_POINT('',(0.18440743788449,0.714999999999917,-0.90891155490766));
#2393=CARTESIAN_POINT('',(0.18440743788449,0.714999999999917,-0.90891155490766));
#2394=CARTESIAN_POINT('',(0.18440743788449,1.11499999999999,-0.908911554907652));
#2395=CARTESIAN_POINT('',(0.210000000000037,1.11499999999999,-0.700476863063002));
#2396=CARTESIAN_POINT('',(0.210000000000037,0.714999999999913,-0.700476863063002));
#2397=CARTESIAN_POINT('',(-8.66729604808567E-17,0.714999999999913,-0.700476863063001));
#2398=CARTESIAN_POINT('',(6.93889390390723E-15,1.11499999999999,-0.700476863063002));
#2399=CARTESIAN_POINT('',(7.63278329429795E-14,1.11499999999999,0.700476863062984));
#2400=CARTESIAN_POINT('',(-1.01951846029015E-16,1.11500000000001,-0.549999999999953));
#2401=CARTESIAN_POINT('',(-7.74589100460617E-17,0.71499999999991,-0.549999999999953));
#2402=CARTESIAN_POINT('',(-7.74589100460617E-17,0.71499999999991,-0.549999999999953));
#2403=CARTESIAN_POINT('',(3.35298549331125,1.11500000000001,-0.549999999999954));
#2404=CARTESIAN_POINT('',(3.35298549331125,0.71499999999991,-0.549999999999954));
#2405=CARTESIAN_POINT('',(3.35298549331125,0.71499999999991,-0.549999999999954));
#2406=CARTESIAN_POINT('',(0.226539250368675,-1.11500000000001,1.01483567354948));
#2407=CARTESIAN_POINT('',(3.35298549331125,-1.11500000000001,-0.54999999999996));
#2408=CARTESIAN_POINT('',(0.110000000000056,-1.11500000000001,-0.54999999999996));
#2409=CARTESIAN_POINT('',(1.43919808140265E-14,-1.11500000000001,-0.549999999999957));
#2410=CARTESIAN_POINT('',(7.63278329429795E-14,-1.11500000000001,0.700476863062984));
#2411=CARTESIAN_POINT('',(6.93889390390723E-15,-1.11500000000001,-0.700476863063001));
#2412=CARTESIAN_POINT('',(0.210000000000037,-1.11500000000001,-0.700476863063001));
#2413=CARTESIAN_POINT('',(0.18440743788449,-1.11500000000001,-0.908911554907652));
#2414=CARTESIAN_POINT('',(0.184407437884489,-1.11500000000001,-0.90891155490766));
#2415=CARTESIAN_POINT('',(0.238726184709409,-1.11500000000001,-0.915581058385476));
#2416=CARTESIAN_POINT('',(0.226539250368647,-1.11500000000001,-1.01483567354944));
#2417=CARTESIAN_POINT('',(0.326295655394636,-1.11500000000001,-1.00786002617519));
#2418=CARTESIAN_POINT('',(0.326295655394668,-1.11500000000001,-1.00786002617519));
#2419=CARTESIAN_POINT('',(0.340267954471445,-1.11500000000001,-1.2076732121119));
#2420=CARTESIAN_POINT('',(0.340267954471445,-1.11500000000001,-1.2076732121119));
#2421=CARTESIAN_POINT('',(0.450000000000013,-1.11500000000001,-1.20000000000005));
#2422=CARTESIAN_POINT('',(0.450000000000013,-1.11500000000001,-1.20000000000005));
#2423=CARTESIAN_POINT('',(0.436027700923228,-1.11500000000001,-1.00018681406323));
#2424=CARTESIAN_POINT('',(0.226539250368647,-1.11500000000001,-1.01483567354944));
#2425=CARTESIAN_POINT('',(0.252131812483609,-1.11500000000001,-0.806400981704738));
#2426=CARTESIAN_POINT('',(0.252131812483609,-1.11500000000001,-0.80640098170474));
#2427=CARTESIAN_POINT('',(0.19781306565959,-1.11500000000001,-0.799731478227189));
#2428=CARTESIAN_POINT('',(0.21000000000005,-1.11500000000001,-0.700476863063022));
#2429=CARTESIAN_POINT('',(0.110000000000023,-1.11500000000001,-0.700476863063043));
#2430=CARTESIAN_POINT('',(0.11000000000002,-1.11500000000001,-0.700476863063043));
#2431=CARTESIAN_POINT('',(0.11000000000002,1.11499999999999,-0.700476863063043));
#2432=CARTESIAN_POINT('',(0.11000000000002,-0.714999999999917,-0.700476863063043));
#2433=CARTESIAN_POINT('',(0.110000000000027,-0.714999999999917,-0.549999999999953));
#2434=CARTESIAN_POINT('',(0.11000000000002,-0.714999999999917,-0.700476863063043));
#2435=CARTESIAN_POINT('',(0.110000000000027,1.11499999999999,-0.549999999999922));
#2436=CARTESIAN_POINT('',(0.110000000000023,1.11499999999999,-0.700476863063043));
#2437=CARTESIAN_POINT('',(0.21000000000005,1.11499999999999,-0.700476863063022));
#2438=CARTESIAN_POINT('',(0.21000000000005,-0.714999999999917,-0.700476863063022));
#2439=CARTESIAN_POINT('',(0.19781306565959,-0.714999999999917,-0.799731478227189));
#2440=CARTESIAN_POINT('',(0.19781306565959,1.11499999999999,-0.799731478227189));
#2441=CARTESIAN_POINT('',(0.252131812483609,1.11499999999999,-0.80640098170474));
#2442=CARTESIAN_POINT('',(0.252131812483609,-0.714999999999917,-0.80640098170474));
#2443=CARTESIAN_POINT('',(0.252131812483609,-0.714999999999917,-0.80640098170474));
#2444=CARTESIAN_POINT('',(0.252131812483609,1.11499999999999,-0.806400981704738));
#2445=CARTESIAN_POINT('',(0.226539250368647,1.11499999999999,-1.01483567354944));
#2446=CARTESIAN_POINT('',(0.226539250368647,-0.714999999999917,-1.01483567354944));
#2447=CARTESIAN_POINT('',(0.436027700923228,-0.714999999999917,-1.00018681406323));
#2448=CARTESIAN_POINT('',(0.436027700923228,1.11499999999999,-1.00018681406323));
#2449=CARTESIAN_POINT('',(0.450000000000013,1.11499999999999,-1.20000000000005));
#2450=CARTESIAN_POINT('',(0.450000000000013,-0.714999999999917,-1.20000000000005));
#2451=CARTESIAN_POINT('',(0.450000000000013,-0.714999999999917,-1.20000000000005));
#2452=CARTESIAN_POINT('',(0.450000000000013,1.11499999999999,-1.20000000000005));
#2453=CARTESIAN_POINT('',(0.340267954471445,1.11499999999999,-1.2076732121119));
#2454=CARTESIAN_POINT('',(0.340267954471445,-0.714999999999917,-1.2076732121119));
#2455=CARTESIAN_POINT('',(0.340267954471445,-0.714999999999917,-1.2076732121119));
#2456=CARTESIAN_POINT('',(0.340267954471445,1.11499999999999,-1.2076732121119));
#2457=CARTESIAN_POINT('',(0.326295655394668,1.11499999999999,-1.00786002617519));
#2458=CARTESIAN_POINT('',(0.326295655394668,-0.714999999999917,-1.00786002617519));
#2459=CARTESIAN_POINT('',(0.326295655394668,-0.714999999999917,-1.00786002617519));
#2460=CARTESIAN_POINT('',(0.326295655394636,1.11499999999999,-1.00786002617519));
#2461=CARTESIAN_POINT('',(0.226539250368647,1.11499999999999,-1.01483567354944));
#2462=CARTESIAN_POINT('',(0.226539250368647,-0.714999999999917,-1.01483567354944));
#2463=CARTESIAN_POINT('',(0.238726184709425,-0.714999999999917,-0.915581058385349));
#2464=CARTESIAN_POINT('',(0.238726184709409,1.11499999999999,-0.915581058385476));
#2465=CARTESIAN_POINT('',(0.184407437884489,1.11499999999999,-0.90891155490766));
#2466=CARTESIAN_POINT('',(0.184407437884489,-0.714999999999917,-0.90891155490766));
#2467=CARTESIAN_POINT('',(0.18440743788449,-0.714999999999917,-0.90891155490766));
#2468=CARTESIAN_POINT('',(0.18440743788449,1.11499999999999,-0.908911554907652));
#2469=CARTESIAN_POINT('',(0.210000000000037,1.11499999999999,-0.700476863063002));
#2470=CARTESIAN_POINT('',(0.210000000000037,-0.714999999999917,-0.700476863063001));
#2471=CARTESIAN_POINT('',(8.8928565816862E-19,-0.714999999999917,-0.700476863063001));
#2472=CARTESIAN_POINT('',(6.93889390390723E-15,1.11499999999999,-0.700476863063002));
#2473=CARTESIAN_POINT('',(7.63278329429795E-14,1.11499999999999,0.700476863062984));
#2474=CARTESIAN_POINT('',(-3.58209188750666E-17,-0.714999999999917,-1.30000000000002));
#2475=CARTESIAN_POINT('',(1.01033360929634E-17,-0.714999999999917,-0.549999999999953));
#2476=CARTESIAN_POINT('',(1.01033360929634E-17,-0.714999999999917,-0.549999999999953));
#2477=CARTESIAN_POINT('',(3.35298549331125,-0.714999999999917,-0.549999999999954));
#2478=CARTESIAN_POINT('',(3.35298549331125,-0.714999999999917,-0.549999999999954));
#2479=CARTESIAN_POINT('',(3.35298549331125,-0.714999999999917,-1.30000000000002));
#2480=CARTESIAN_POINT('',(0.19781306565932,1.11499999999999,0.799731478227012));
#2481=CARTESIAN_POINT('',(0.197813065659321,0.20000000000005,0.799731478227012));
#2482=CARTESIAN_POINT('',(0.252131812484248,0.20000000000005,0.806400981704836));
#2483=CARTESIAN_POINT('',(0.19781306565932,0.20000000000005,0.799731478227011));
#2484=CARTESIAN_POINT('',(0.252131812484248,1.11499999999999,0.806400981704836));
#2485=CARTESIAN_POINT('',(0.252131812484248,-0.200000000000045,0.806400981704835));
#2486=CARTESIAN_POINT('',(0.197813065659321,-0.200000000000045,0.799731478227012));
#2487=CARTESIAN_POINT('',(0.19781306565932,-0.200000000000045,0.799731478227011));
#2488=CARTESIAN_POINT('',(0.19781306565932,1.11499999999999,0.799731478227011));
#2489=CARTESIAN_POINT('',(0.226539250368675,1.11499999999999,1.01483567354948));
#2490=CARTESIAN_POINT('',(0.226539250368675,0.200000000000052,1.01483567354948));
#2491=CARTESIAN_POINT('',(0.436027700923269,0.200000000000052,1.00018681406329));
#2492=CARTESIAN_POINT('',(0.436027700923269,1.11499999999999,1.00018681406329));
#2493=CARTESIAN_POINT('',(0.436027700923269,-0.200000000000043,1.00018681406329));
#2494=CARTESIAN_POINT('',(0.226539250368675,-0.200000000000043,1.01483567354948));
#2495=CARTESIAN_POINT('',(0.436027700923268,1.11499999999999,1.00018681406329));
#2496=CARTESIAN_POINT('',(0.436027700923268,0.200000000000051,1.0001868140633));
#2497=CARTESIAN_POINT('',(0.450000000000013,0.200000000000053,1.19999999999992));
#2498=CARTESIAN_POINT('',(0.450000000000013,1.11499999999999,1.19999999999992));
#2499=CARTESIAN_POINT('',(0.450000000000013,-0.200000000000041,1.19999999999992));
#2500=CARTESIAN_POINT('',(0.436027700923268,-0.200000000000043,1.00018681406331));
#2501=CARTESIAN_POINT('',(0.450000000000013,1.11499999999999,1.19999999999992));
#2502=CARTESIAN_POINT('',(0.450000000000012,0.200000000000053,1.19999999999992));
#2503=CARTESIAN_POINT('',(0.340267954471445,0.200000000000053,1.20767321211178));
#2504=CARTESIAN_POINT('',(0.340267954471445,1.11499999999999,1.20767321211178));
#2505=CARTESIAN_POINT('',(0.340267954471445,-0.200000000000041,1.20767321211178));
#2506=CARTESIAN_POINT('',(0.450000000000012,-0.200000000000041,1.19999999999992));
#2507=CARTESIAN_POINT('',(0.340267954471445,1.11499999999999,1.20767321211178));
#2508=CARTESIAN_POINT('',(0.340267954471445,0.200000000000053,1.20767321211179));
#2509=CARTESIAN_POINT('',(0.326295655394652,0.200000000000052,1.00786002617508));
#2510=CARTESIAN_POINT('',(0.326295655394652,1.11499999999999,1.00786002617508));
#2511=CARTESIAN_POINT('',(0.326295655394652,-0.200000000000043,1.00786002617508));
#2512=CARTESIAN_POINT('',(0.340267954471445,-0.200000000000041,1.20767321211179));
#2513=CARTESIAN_POINT('',(0.226539250368675,1.11499999999999,1.01483567354948));
#2514=CARTESIAN_POINT('',(0.226539250368675,0.200000000000052,1.01483567354948));
#2515=CARTESIAN_POINT('',(0.238726184709363,0.200000000000051,0.915581058385346));
#2516=CARTESIAN_POINT('',(0.238726184709363,1.11499999999999,0.915581058385346));
#2517=CARTESIAN_POINT('',(0.238726184709363,-0.200000000000044,0.915581058385346));
#2518=CARTESIAN_POINT('',(0.226539250368675,-0.200000000000043,1.01483567354948));
#2519=CARTESIAN_POINT('',(0.238726184709363,1.11499999999999,0.915581058385344));
#2520=CARTESIAN_POINT('',(0.238726184709364,0.200000000000051,0.915581058385344));
#2521=CARTESIAN_POINT('',(0.184407437884531,0.200000000000051,0.90891155490755));
#2522=CARTESIAN_POINT('',(0.184407437884531,1.11499999999999,0.90891155490755));
#2523=CARTESIAN_POINT('',(0.184407437884531,-0.200000000000044,0.90891155490755));
#2524=CARTESIAN_POINT('',(0.238726184709364,-0.200000000000044,0.915581058385344));
#2525=CARTESIAN_POINT('',(0.210000000000064,1.11499999999999,0.700476863062932));
#2526=CARTESIAN_POINT('',(0.210000000000064,0.200000000000049,0.700476863062932));
#2527=CARTESIAN_POINT('',(3.06453694198666E-17,0.200000000000049,0.700476863062984));
#2528=CARTESIAN_POINT('',(1.14437539305845E-13,1.11499999999999,0.700476863062984));
#2529=CARTESIAN_POINT('',(5.51383054028195E-17,-0.200000000000046,0.700476863062984));
#2530=CARTESIAN_POINT('',(0.210000000000064,-0.200000000000046,0.700476863062932));
#2531=CARTESIAN_POINT('',(7.63278329429795E-14,1.11499999999999,0.700476863062984));
#2532=CARTESIAN_POINT('',(2.14313189850733E-17,0.200000000000047,0.54999999999996));
#2533=CARTESIAN_POINT('',(2.14313189850734E-17,0.200000000000047,0.54999999999996));
#2534=CARTESIAN_POINT('',(4.59242549680262E-17,-0.200000000000047,0.54999999999996));
#2535=CARTESIAN_POINT('',(4.59242549680262E-17,-0.200000000000047,0.54999999999996));
#2536=CARTESIAN_POINT('',(9.18485099360554E-17,-0.20000000000004,1.30000000000002));
#2537=CARTESIAN_POINT('',(0.11000000000002,1.11499999999999,-0.700476863063043));
#2538=CARTESIAN_POINT('',(0.11000000000002,0.200000000000036,-0.700476863063035));
#2539=CARTESIAN_POINT('',(0.110000000000089,0.200000000000049,0.700476863062932));
#2540=CARTESIAN_POINT('',(0.110000000000082,0.200000000000047,0.54999999999996));
#2541=CARTESIAN_POINT('',(0.110000000000089,1.11499999999999,0.700476863062932));
#2542=CARTESIAN_POINT('',(0.110000000000089,-0.200000000000045,0.700476863062932));
#2543=CARTESIAN_POINT('',(0.11000000000002,-0.200000000000059,-0.700476863063031));
#2544=CARTESIAN_POINT('',(0.110000000000082,-0.200000000000047,0.54999999999996));
#2545=CARTESIAN_POINT('',(0.110000000000082,1.11499999999999,0.54999999999996));
#2546=CARTESIAN_POINT('',(0.210000000000064,1.11499999999999,0.700476863062932));
#2547=CARTESIAN_POINT('',(0.210000000000064,0.200000000000049,0.700476863062932));
#2548=CARTESIAN_POINT('',(0.210000000000064,-0.200000000000046,0.700476863062932));
#2549=CARTESIAN_POINT('',(3.35298549331125,0.200000000000048,0.54999999999996));
#2550=CARTESIAN_POINT('',(3.35298549331125,0.200000000000048,0.54999999999996));
#2551=CARTESIAN_POINT('',(3.35298549331125,-0.20000000000004,1.30000000000002));
#2552=CARTESIAN_POINT('',(3.35298549331125,-0.200000000000047,0.54999999999996));
#2553=CARTESIAN_POINT('',(3.35298549331125,-0.200000000000047,0.54999999999996));
#2554=CARTESIAN_POINT('',(-1.4547173240995,0.000476863062987465,-0.287500000000057));
#2555=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#1313,
#1314,#1315,#1316,#1317,#1318,#1319,#1320,#1321,#1322,#1323,#1324,#1325,
#1326,#1327,#1328,#1329,#1330,#1331,#1332,#1333,#1334,#1335,#1336,#1337,
#1338,#1339,#1340,#1341,#1342,#1343,#1344,#1345,#1346,#1347,#1348,#1349,
#1350,#1351,#1352,#1353,#1354,#1355,#1356),#2556);
#2556=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2561))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2568,#2567,#2566))
REPRESENTATION_CONTEXT('SOT-23','TOP_LEVEL_ASSEMBLY_PART')
);
#2557=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2562))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2568,#2567,#2566))
REPRESENTATION_CONTEXT('COMPOUND','COMPONENT_PART')
);
#2558=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2563))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2568,#2567,#2566))
REPRESENTATION_CONTEXT('CP01','COMPONENT_PART')
);
#2559=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2564))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2568,#2567,#2566))
REPRESENTATION_CONTEXT('CP02','COMPONENT_PART')
);
#2560=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2565))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2568,#2567,#2566))
REPRESENTATION_CONTEXT('LDF','COMPONENT_PART')
);
#2561=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#2568,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#2562=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#2568,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#2563=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#2568,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#2564=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#2568,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#2565=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#2568,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#2566=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#2567=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#2568=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#2569=PRODUCT_DEFINITION_SHAPE('','',#2578);
#2570=PRODUCT_DEFINITION_SHAPE('','',#2579);
#2571=PRODUCT_DEFINITION_SHAPE('','',#2580);
#2572=PRODUCT_DEFINITION_SHAPE(' ','NAUO PRDDFN',#22);
#2573=PRODUCT_DEFINITION_SHAPE('','',#2581);
#2574=PRODUCT_DEFINITION_SHAPE(' ','NAUO PRDDFN',#23);
#2575=PRODUCT_DEFINITION_SHAPE(' ','NAUO PRDDFN',#24);
#2576=PRODUCT_DEFINITION_SHAPE('','',#2582);
#2577=PRODUCT_DEFINITION_SHAPE(' ','NAUO PRDDFN',#25);
#2578=PRODUCT_DEFINITION('','',#2588,#2583);
#2579=PRODUCT_DEFINITION('','',#2589,#2584);
#2580=PRODUCT_DEFINITION('','',#2590,#2585);
#2581=PRODUCT_DEFINITION('','',#2591,#2586);
#2582=PRODUCT_DEFINITION('','',#2592,#2587);
#2583=PRODUCT_DEFINITION_CONTEXT('',#2614,'design');
#2584=PRODUCT_DEFINITION_CONTEXT('',#2614,'design');
#2585=PRODUCT_DEFINITION_CONTEXT('',#2614,'design');
#2586=PRODUCT_DEFINITION_CONTEXT('',#2614,'design');
#2587=PRODUCT_DEFINITION_CONTEXT('',#2614,'design');
#2588=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#2598,
 .NOT_KNOWN.);
#2589=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#2599,
 .NOT_KNOWN.);
#2590=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#2600,
 .NOT_KNOWN.);
#2591=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#2601,
 .NOT_KNOWN.);
#2592=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#2602,
 .NOT_KNOWN.);
#2593=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#2598));
#2594=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#2599));
#2595=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#2600));
#2596=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#2601));
#2597=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#2602));
#2598=PRODUCT('SOT-23','SOT-23','SOT-23',(#2608));
#2599=PRODUCT('COMPOUND','COMPOUND','COMPOUND',(#2609));
#2600=PRODUCT('CP01','CP01','CP01',(#2610));
#2601=PRODUCT('CP02','CP02','CP02',(#2611));
#2602=PRODUCT('LDF','LDF','LDF',(#2612));
#2603=PRODUCT_CATEGORY('','');
#2604=PRODUCT_CATEGORY('','');
#2605=PRODUCT_CATEGORY('','');
#2606=PRODUCT_CATEGORY('','');
#2607=PRODUCT_CATEGORY('','');
#2608=PRODUCT_CONTEXT('',#2614,'mechanical');
#2609=PRODUCT_CONTEXT('',#2614,'mechanical');
#2610=PRODUCT_CONTEXT('',#2614,'mechanical');
#2611=PRODUCT_CONTEXT('',#2614,'mechanical');
#2612=PRODUCT_CONTEXT('',#2614,'mechanical');
#2613=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2010,#2614);
#2614=APPLICATION_CONTEXT(
'core data for automotive mechanical design processes');
ENDSEC;
END-ISO-10303-21;
