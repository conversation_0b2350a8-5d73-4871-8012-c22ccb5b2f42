#!/usr/bin/env python3
"""
Simple TI search: Find search field, enter part number, hit return
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time

def simple_search():
    print("Simple TI search: Enter LM358N and hit return")
    print("=" * 50)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Load TI website
        print("Loading https://www.ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(20)  # Wait longer for page to fully load

        # Wait for search field to appear (check multiple times)
        print("Waiting for search field to appear...")
        search_field = None

        for attempt in range(10):  # Try 10 times
            print(f"Attempt {attempt + 1}/10...")
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"  Found {len(all_inputs)} input elements")

            if all_inputs:
                for inp in all_inputs:
                    try:
                        if inp.is_displayed() and inp.is_enabled():
                            search_field = inp
                            print(f"  Found usable input field!")
                            break
                    except:
                        continue

            if search_field:
                break

            time.sleep(3)  # Wait 3 seconds between attempts

        # Find search field and enter part number
        print("Looking for search field...")
        
        # Try common search field selectors
        search_field = None
        selectors = [
            "input[type='search']",
            "input[name*='search']", 
            "input[placeholder*='search']",
            "#searchboxheader input",
            ".search input"
        ]
        
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        search_field = element
                        print(f"Found search field: {selector}")
                        break
                if search_field:
                    break
            except:
                continue
        
        if search_field:
            print("Entering 'LM358N' and hitting return...")
            search_field.clear()
            search_field.send_keys("LM358N")
            search_field.send_keys(Keys.RETURN)
            time.sleep(10)

            print(f"Search completed! URL: {driver.current_url}")

            if "LM358N" in driver.page_source.upper():
                print("✅ SUCCESS: Found LM358N results!")
            else:
                print("❌ No LM358N found in results")
        else:
            print("❌ Could not find search field")

            # Debug: Show all input elements
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"DEBUG: Found {len(all_inputs)} total input elements:")
            for i, inp in enumerate(all_inputs):
                try:
                    displayed = inp.is_displayed()
                    enabled = inp.is_enabled()
                    inp_type = inp.get_attribute('type') or ''
                    inp_placeholder = inp.get_attribute('placeholder') or ''
                    print(f"  {i+1}: type='{inp_type}' placeholder='{inp_placeholder}' displayed={displayed} enabled={enabled}")
                except:
                    print(f"  {i+1}: Error reading element")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    simple_search()
