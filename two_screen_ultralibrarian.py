#!/usr/bin/env python3
"""
TWO SCREEN ULTRALIBRARIAN AUTOMATION
====================================
Automates the first two screens:
1. Enter part number and search
2. Select part from results
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

class TwoScreenUltraLibrarian:
    def __init__(self):
        self.base_url = 'https://www.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("Two-Screen UltraLibrarian Automation Ready!")

    def setup_driver(self):
        """Setup Chrome driver with stealth options"""
        chrome_options = Options()
        
        # Stealth options
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3D'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            
            # Hide automation
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return driver
        except Exception as e:
            print(f"Chrome driver setup failed: {e}")
            return None

    def screen_1_search(self, driver, manufacturer, part_number):
        """SCREEN 1: Enter part number and search"""
        print(f"\n🔸 SCREEN 1: Enter part number and search")
        
        # Load homepage
        print(f"   Loading homepage...")
        driver.get(self.base_url)
        time.sleep(3)
        
        # Find search box
        print(f"   Looking for search box...")
        search_selectors = [
            "input[type='search']",
            "input[name*='search']", 
            "input[placeholder*='search']",
            "input[placeholder*='Search']",
            "input[id*='search']",
            "input[class*='search']",
            "input[type='text']"
        ]
        
        search_input = None
        for selector in search_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements and elements[0].is_displayed():
                    search_input = elements[0]
                    print(f"   ✅ Found search box: {selector}")
                    break
            except:
                continue
        
        if not search_input:
            print(f"   ❌ No search box found!")
            return False
        
        # Enter search term
        search_term = f"{manufacturer} {part_number}"
        print(f"   Entering: '{search_term}'")
        
        search_input.clear()
        search_input.send_keys(search_term)
        
        # Submit search
        print(f"   Submitting search...")
        
        # Try submit button first
        submit_buttons = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
        if submit_buttons:
            submit_buttons[0].click()
            print(f"   ✅ Clicked submit button")
        else:
            # Press Enter
            search_input.send_keys(Keys.RETURN)
            print(f"   ✅ Pressed Enter")
        
        # Wait for results
        time.sleep(5)
        print(f"   ✅ Search submitted, waiting for results...")
        return True

    def screen_2_select_part(self, driver, part_number):
        """SCREEN 2: Select specific part from results"""
        print(f"\n🔸 SCREEN 2: Select part from results")
        
        # Look for part results
        print(f"   Looking for part results...")
        
        # Multiple strategies to find part links
        result_strategies = [
            # Strategy 1: Links containing part number
            f"//a[contains(text(), '{part_number}')]",
            f"//a[contains(translate(text(), 'abcdefghijklmnopqrstuvwxyz', 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'), '{part_number.upper()}')]",
            
            # Strategy 2: Table rows with part number
            f"//tr[contains(., '{part_number}')]//a",
            f"//td[contains(text(), '{part_number}')]/parent::tr//a",
            
            # Strategy 3: Generic part links
            "//a[contains(@href, 'part')]",
            "//a[contains(@href, 'component')]",
            
            # Strategy 4: Result containers
            "//div[contains(@class, 'result')]//a",
            "//div[contains(@class, 'search')]//a"
        ]
        
        found_results = []
        
        for strategy in result_strategies:
            try:
                elements = driver.find_elements(By.XPATH, strategy)
                for element in elements:
                    try:
                        text = element.text.strip()
                        href = element.get_attribute('href')
                        
                        if text and href and len(text) > 2:
                            # Prioritize results containing our part number
                            priority = 0
                            if part_number.upper() in text.upper():
                                priority = 10
                            elif part_number.lower() in text.lower():
                                priority = 5
                            
                            found_results.append({
                                'element': element,
                                'text': text,
                                'href': href,
                                'priority': priority,
                                'strategy': strategy
                            })
                    except:
                        continue
            except:
                continue
        
        if not found_results:
            print(f"   ❌ No part results found!")
            return False
        
        # Sort by priority (highest first)
        found_results.sort(key=lambda x: x['priority'], reverse=True)
        
        print(f"   ✅ Found {len(found_results)} potential results:")
        for i, result in enumerate(found_results[:5]):  # Show top 5
            print(f"      {i+1}. {result['text'][:60]}... (Priority: {result['priority']})")
        
        # Click the best result
        best_result = found_results[0]
        print(f"   Clicking best result: {best_result['text'][:50]}...")
        
        try:
            # Scroll to element and click
            driver.execute_script("arguments[0].scrollIntoView(true);", best_result['element'])
            time.sleep(1)
            best_result['element'].click()
            
            print(f"   ✅ Clicked part result!")
            time.sleep(5)  # Wait for next page
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to click result: {e}")
            return False

    def run_two_screens(self, manufacturer, part_number):
        """Run the two-screen automation"""
        print(f"\nTWO-SCREEN ULTRALIBRARIAN AUTOMATION")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("=" * 50)
        
        driver = self.setup_driver()
        if not driver:
            return False
        
        try:
            # SCREEN 1: Search
            if not self.screen_1_search(driver, manufacturer, part_number):
                print(f"\n❌ Screen 1 failed!")
                return False
            
            # SCREEN 2: Select part
            if not self.screen_2_select_part(driver, part_number):
                print(f"\n❌ Screen 2 failed!")
                return False
            
            # SUCCESS - Now on Screen 3
            print(f"\n🎉 SUCCESS! Reached Screen 3!")
            print(f"Current URL: {driver.current_url}")
            print(f"Page Title: {driver.title}")
            
            # Keep browser open for inspection
            print(f"\n🔍 SCREEN 3 INSPECTION:")
            print(f"   Browser is now on the part details page")
            print(f"   Look for download options, 3D models, STEP files")
            print(f"   Tell me what you see on this screen!")
            print(f"   Press Ctrl+C when done inspecting...")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print(f"\n👋 Closing browser...")
                
            return True
            
        except Exception as e:
            print(f"Error: {e}")
            return False
        finally:
            driver.quit()

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python two_screen_ultralibrarian.py \"Manufacturer\" \"Part Number\"")
        print("\nExample: python two_screen_ultralibrarian.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    scraper = TwoScreenUltraLibrarian()
    success = scraper.run_two_screens(manufacturer, part_number)
    
    if success:
        print(f"\n✅ Two-screen automation successful!")
        print(f"📋 Now tell me what you see on Screen 3!")
    else:
        print(f"\n❌ Two-screen automation failed!")

if __name__ == "__main__":
    main()
