#!/usr/bin/env python3
"""
Test Screen 6 - Select format and download
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_ultralibrarian_automation import CompleteUltraLibrarianAutomation

def test_screen6_download():
    print("🔸 Testing Screen 6 - Select format and download")
    print("=" * 50)
    
    automation = CompleteUltraLibrarianAutomation()
    driver = automation.setup_driver()
    
    if not driver:
        print("❌ Failed to setup driver")
        return
    
    try:
        # Run through Screens 1-5 to get to download options
        print("Running Screens 1-5...")
        
        # Screen 1
        if not automation.screen_1_search(driver, "Texas Instruments", "LM358N"):
            print("❌ Screen 1 failed")
            return
        print("✅ Screen 1 completed")
        
        # Screen 2  
        if not automation.screen_2_select_part(driver, "LM358N"):
            print("❌ Screen 2 failed")
            return
        print("✅ Screen 2 completed")
        
        # Screen 3
        if not automation.screen_3_find_right_variant(driver, "LM358N"):
            print("❌ Screen 3 failed")
            return
        print("✅ Screen 3 completed")
        
        # Screen 4
        if not automation.screen_4_download_now(driver):
            print("❌ Screen 4 failed")
            return
        print("✅ Screen 4 completed")
        
        # Screen 5
        if not automation.screen_5_3d_model(driver):
            print("❌ Screen 5 failed")
            return
        print("✅ Screen 5 completed")
        
        print("\n🔸 Now on Screen 6 - Download options page")
        print("Current URL:", driver.current_url)
        
        # Check what checkboxes are available
        from selenium.webdriver.common.by import By
        checkboxes = driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
        print(f"\nFound {len(checkboxes)} checkboxes:")
        
        for i, cb in enumerate(checkboxes):
            try:
                cb_id = cb.get_attribute('id') or ''
                cb_name = cb.get_attribute('name') or ''
                visible = cb.is_displayed()
                selected = cb.is_selected()
                print(f"  {i}: id='{cb_id}' name='{cb_name}' visible={visible} selected={selected}")
            except:
                continue
        
        # Try to select MfrThreeDModel
        print(f"\nTrying to select MfrThreeDModel...")
        try:
            mfr_checkbox = driver.find_element(By.ID, "MfrThreeDModel")
            if not mfr_checkbox.is_selected():
                # Use JavaScript to click if regular click doesn't work
                driver.execute_script("arguments[0].click();", mfr_checkbox)
                print("✅ Selected MfrThreeDModel using JavaScript")
            else:
                print("✅ MfrThreeDModel already selected")
        except Exception as e:
            print(f"❌ Could not select MfrThreeDModel: {e}")
        
        # Look for download/submit button
        print(f"\nLooking for download/submit button...")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
        
        print(f"Found {len(buttons)} buttons and {len(inputs)} submit inputs:")
        
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if text and visible:
                    print(f"  Button {i}: '{text}' visible={visible}")
            except:
                continue
        
        for i, inp in enumerate(inputs):
            try:
                value = inp.get_attribute('value') or ''
                visible = inp.is_displayed()
                if visible:
                    print(f"  Submit {i}: value='{value}' visible={visible}")
            except:
                continue
        
        print("\nBrowser will stay open for 60 seconds for inspection...")
        import time
        time.sleep(60)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        driver.quit()

if __name__ == "__main__":
    test_screen6_download()
