#!/usr/bin/env python3
"""
SIMPLE ULTRALIBRARIAN TEST
=========================
Just open the page and show basic info.
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

def simple_test():
    print("🧪 SIMPLE ULTRALIBRARIAN TEST")
    print("=" * 40)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("📍 Opening UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        
        print("⏳ Waiting 15 seconds for page to load...")
        time.sleep(15)
        
        print(f"✅ Page loaded!")
        print(f"Title: {driver.title}")
        print(f"URL: {driver.current_url}")
        
        # Count elements
        inputs = driver.find_elements(By.TAG_NAME, "input")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        links = driver.find_elements(By.TAG_NAME, "a")
        
        print(f"Found {len(inputs)} input elements")
        print(f"Found {len(buttons)} button elements")
        print(f"Found {len(links)} link elements")
        
        # Show first few inputs
        print("\nFirst 5 input elements:")
        for i, inp in enumerate(inputs[:5]):
            try:
                input_type = inp.get_attribute('type') or 'none'
                placeholder = inp.get_attribute('placeholder') or 'none'
                visible = inp.is_displayed()
                print(f"  {i}: type='{input_type}', placeholder='{placeholder}', visible={visible}")
            except Exception as e:
                print(f"  {i}: Error - {e}")
        
        print("\n✅ Basic test complete!")
        print("Browser will stay open for 30 seconds...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        print("Closing browser...")
        driver.quit()

if __name__ == "__main__":
    simple_test()
