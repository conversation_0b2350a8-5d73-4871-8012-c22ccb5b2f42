#!/usr/bin/env python3
"""
Check what's on the TI LM358 page
"""

import requests

def check_ti_page():
    print("🔍 CHECKING TI LM358 PAGE")
    print("=" * 30)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    url = "https://www.ti.com/lit/gpn/lm358"
    
    try:
        response = session.get(url, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the page
            with open('ti_lm358_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"✅ Saved TI page to ti_lm358_page.html")
            
            # Look for common datasheet patterns
            content = response.text.lower()
            
            if 'datasheet' in content:
                print("✅ Found 'datasheet' in content")
            if '.pdf' in content:
                print("✅ Found '.pdf' in content")
            if 'download' in content:
                print("✅ Found 'download' in content")
            
            # Look for specific patterns
            import re
            pdf_matches = re.findall(r'["\']([^"\']*\.pdf[^"\']*)["\']', response.text)
            if pdf_matches:
                print(f"📄 Found PDF links:")
                for i, pdf in enumerate(pdf_matches[:5], 1):
                    print(f"   {i}. {pdf}")
            else:
                print("❌ No PDF links found")
                
        else:
            print(f"❌ Failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_ti_page()
