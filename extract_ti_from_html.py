#!/usr/bin/env python3
"""
Extract TI datasheet URL from the existing HTML data
"""

import json
import requests
import os

def extract_ti_from_html():
    print("🎯 EXTRACTING TI DATASHEET FROM HTML DATA")
    print("=" * 50)
    
    # Read the fresh search results
    with open('digikey_fresh_search_LM358N.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Find the JSON data
    start = html_content.find('{"props":')
    end = html_content.find('</script>', start)
    json_str = html_content[start:end]
    data = json.loads(json_str)
    
    # Get exact matches
    exact_matches = data.get('props', {}).get('pageProps', {}).get('envelope', {}).get('data', {}).get('exactMatch', [])
    
    # Find Texas Instruments
    ti_match = None
    for match in exact_matches:
        if 'texas instruments' in match.get('mfr', '').lower():
            ti_match = match
            break
    
    if not ti_match:
        print("❌ Texas Instruments not found")
        return False
    
    print(f"✅ Found Texas Instruments match:")
    print(f"   Manufacturer: {ti_match['mfr']}")
    print(f"   Part: {ti_match['mfrProduct']}")
    print(f"   Detail URL: {ti_match['detailUrl']}")
    
    # The TI datasheet URL you provided is:
    # https://www.ti.com/general/docs/suppproductinfo.tsp?distId=10&gotoUrl=https%3A%2F%2Fwww.ti.com%2Flit%2Fgpn%2Flm158-n
    
    # Let's use this URL directly since you found it
    ti_datasheet_url = "https://www.ti.com/general/docs/suppproductinfo.tsp?distId=10&gotoUrl=https%3A%2F%2Fwww.ti.com%2Flit%2Fgpn%2Flm158-n"
    
    print(f"\n📥 Downloading TI datasheet...")
    print(f"   URL: {ti_datasheet_url}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        response = session.get(ti_datasheet_url, timeout=60, allow_redirects=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            print(f"   Content-Type: {content_type}")
            
            # Check if it's a PDF
            if 'application/pdf' in content_type:
                print("   ✅ Direct PDF download!")
                
                os.makedirs('datasheets', exist_ok=True)
                filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                
                with open(filename, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                file_size = os.path.getsize(filename)
                print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                
                if file_size > 10000:
                    print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                    return True
                else:
                    print(f"   ⚠️  File too small")
                    return False
                    
            else:
                print(f"   📄 Not a direct PDF, content type: {content_type}")
                
                # Save the response to see what we got
                with open('ti_response.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                print(f"   💾 Saved response to ti_response.html")
                
                # Look for PDF links in the response
                import re
                pdf_matches = re.findall(r'href="([^"]*\.pdf[^"]*)"', response.text)
                
                if pdf_matches:
                    print(f"   🎯 Found PDF links:")
                    for i, pdf_url in enumerate(pdf_matches[:3], 1):
                        print(f"      {i}. {pdf_url}")
                    
                    # Try the first PDF link
                    pdf_url = pdf_matches[0]
                    if not pdf_url.startswith('http'):
                        pdf_url = f"https://www.ti.com{pdf_url}"
                    
                    print(f"\n   📥 Downloading PDF from: {pdf_url}")
                    
                    pdf_response = session.get(pdf_url, timeout=60, stream=True)
                    print(f"   PDF Status: {pdf_response.status_code}")
                    
                    if pdf_response.status_code == 200:
                        os.makedirs('datasheets', exist_ok=True)
                        filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                        
                        with open(filename, 'wb') as f:
                            for chunk in pdf_response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        
                        file_size = os.path.getsize(filename)
                        print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                        
                        if file_size > 10000:
                            print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                            return True
                        else:
                            print(f"   ⚠️  File too small")
                            return False
                    else:
                        print(f"   ❌ PDF download failed: {pdf_response.status_code}")
                        return False
                else:
                    print(f"   ❌ No PDF links found in response")
                    return False
        else:
            print(f"   ❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = extract_ti_from_html()
    if success:
        print("\n🎉 TEXAS INSTRUMENTS DATASHEET SUCCESSFULLY DOWNLOADED!")
    else:
        print("\n❌ FAILED TO DOWNLOAD TI DATASHEET")
