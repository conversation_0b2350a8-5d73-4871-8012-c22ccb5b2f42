#!/usr/bin/env python3
"""
Test UltraLibrarian login process step by step
"""

import sys
import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def test_login_process():
    print("🧪 TESTING ULTRALIBRARIAN LOGIN PROCESS")
    print("=" * 50)
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Chrome driver started")
    except Exception as e:
        print(f"❌ Chrome driver failed: {e}")
        return False
    
    try:
        # Step 1: Navigate to main page
        print("\n🔸 STEP 1: Navigate to UltraLibrarian")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(5)
        print(f"   Current URL: {driver.current_url}")
        print(f"   Page title: {driver.title}")
        
        # Step 2: Find LOGIN link
        print("\n🔸 STEP 2: Find LOGIN link")
        login_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'LOGIN')]")
        
        if not login_links:
            print("   ❌ No LOGIN link found")
            return False
        
        login_link = login_links[0]
        print(f"   ✅ Found LOGIN link: {login_link.text}")
        print(f"   LOGIN href: {login_link.get_attribute('href')}")
        
        # Step 3: Click LOGIN link
        print("\n🔸 STEP 3: Click LOGIN link")
        login_link.click()
        time.sleep(5)
        print(f"   Current URL after click: {driver.current_url}")
        
        # Step 4: Check for login form
        print("\n🔸 STEP 4: Check for login form")
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email']")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        print(f"   Found {len(email_inputs)} email inputs")
        print(f"   Found {len(password_inputs)} password inputs")
        
        if not email_inputs or not password_inputs:
            print("   ❌ Login form not found")
            return False
        
        # Step 5: Load credentials
        print("\n🔸 STEP 5: Load credentials")
        try:
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            print(f"   ✅ Credentials loaded for: {email}")
        except Exception as e:
            print(f"   ❌ Could not load credentials: {e}")
            return False
        
        # Step 6: Fill login form
        print("\n🔸 STEP 6: Fill login form")
        email_input = email_inputs[0]
        password_input = password_inputs[0]
        
        email_input.clear()
        email_input.send_keys(email)
        print(f"   ✅ Email entered")
        
        password_input.clear()
        password_input.send_keys(password)
        print(f"   ✅ Password entered")
        
        # Step 7: Find and click login button
        print("\n🔸 STEP 7: Find and click login button")
        login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')] | //input[@type='submit'] | //button[@type='submit']")
        
        if not login_buttons:
            print("   ❌ No login button found")
            return False
        
        login_button = login_buttons[0]
        print(f"   ✅ Found login button: {login_button.text or 'Submit'}")
        
        login_button.click()
        print(f"   ✅ Login button clicked")
        
        # Step 8: Wait for redirect
        print("\n🔸 STEP 8: Wait for login redirect")
        time.sleep(10)
        print(f"   Final URL: {driver.current_url}")
        print(f"   Final title: {driver.title}")
        
        # Check if we're logged in (look for search functionality)
        search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i]")
        if search_inputs:
            print(f"   ✅ Login successful - found search box")
            return True
        else:
            print(f"   ❌ Login may have failed - no search box found")
            return False
        
    except Exception as e:
        print(f"❌ Error during login test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print(f"\n🔧 Keeping browser open for inspection...")
        input("Press Enter to close browser: ")
        driver.quit()

if __name__ == "__main__":
    success = test_login_process()
    if success:
        print("\n🎉 LOGIN TEST SUCCESSFUL!")
    else:
        print("\n❌ LOGIN TEST FAILED!")
