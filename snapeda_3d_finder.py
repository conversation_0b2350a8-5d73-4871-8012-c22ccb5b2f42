#!/usr/bin/env python3
"""
SnapEDA Complete Flow - Open, click login, fill form, submit
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def main():
    print("🎯 SNAPEDA COMPLETE FLOW")
    print("=" * 40)
    print("1. Open SnapEDA")
    print("2. Click login link")
    print("3. Fill login form")
    print("4. Submit login")
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # STEP 1: Open SnapEDA
        print("\n📱 STEP 1: Opening SnapEDA...")
        driver.get("https://www.snapeda.com/")
        time.sleep(8)
        
        print(f"✅ Page opened: {driver.current_url}")
        
        # STEP 2: Click login link
        print("\n📱 STEP 2: Looking for login link...")

        # FIRST: Show all clickable elements for debugging
        print(f"\n🔍 DEBUG: All clickable elements:")
        all_clickable = driver.find_elements(By.CSS_SELECTOR, "a, button")
        displayed_count = 0

        for element in all_clickable:
            try:
                if element.is_displayed():
                    displayed_count += 1
                    text = element.text.strip()
                    href = element.get_attribute('href') or ''

                    if text or href:  # Only show elements with text or href
                        print(f"  {displayed_count}. TEXT: '{text}' HREF: '{href[:50]}'")

                        # Check for login-related content
                        if (any(word in text.lower() for word in ['login', 'sign', 'account']) or
                            any(word in href.lower() for word in ['login', 'signin', 'account'])):
                            print(f"      🎯 POTENTIAL LOGIN!")

                    if displayed_count >= 15:  # Show first 15
                        break
            except:
                continue

        login_found = False

        # Try text-based search with more variations
        login_texts = ["Login", "Log in", "Sign in", "Sign In", "LOG IN", "SIGN IN", "Account", "My Account"]

        for login_text in login_texts:
            if login_found:
                break

            print(f"  Trying text: '{login_text}'")
            elements = driver.find_elements(By.XPATH, f"//a[contains(text(), '{login_text}')] | //button[contains(text(), '{login_text}')]")

            for element in elements:
                try:
                    if element.is_displayed():
                        print(f"🎯 Found and clicking: '{element.text.strip()}'")
                        element.click()
                        time.sleep(8)

                        new_url = driver.current_url
                        print(f"📍 After click: {new_url}")

                        if new_url != "https://www.snapeda.com/":
                            print(f"✅ Moved to login page!")
                            login_found = True
                            break
                except Exception as e:
                    print(f"    ❌ Error clicking: {e}")
                    continue

        # Try href-based search if text search failed
        if not login_found:
            print("Text search failed, trying href-based search...")
            login_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'login')] | //a[contains(@href, 'signin')] | //a[contains(@href, 'account')]")

            for element in login_links:
                try:
                    if element.is_displayed():
                        href = element.get_attribute('href')
                        text = element.text.strip()
                        print(f"🎯 Found and clicking login link: '{text}' -> {href}")
                        element.click()
                        time.sleep(8)

                        new_url = driver.current_url
                        print(f"📍 After click: {new_url}")

                        if new_url != "https://www.snapeda.com/":
                            print(f"✅ Moved to login page!")
                            login_found = True
                            break
                except Exception as e:
                    print(f"    ❌ Error clicking: {e}")
                    continue
        
        if not login_found:
            print("❌ Could not find login link")
            return
        
        # STEP 3: Fill login form
        print("\n📱 STEP 3: Finding login form fields...")

        # FIRST: Show ALL input fields for debugging
        print(f"\n🔍 DEBUG: All input fields on login page:")
        all_inputs = driver.find_elements(By.CSS_SELECTOR, "input")
        for i, inp in enumerate(all_inputs):
            try:
                if inp.is_displayed():
                    inp_type = inp.get_attribute('type') or 'text'
                    name = inp.get_attribute('name') or ''
                    placeholder = inp.get_attribute('placeholder') or ''
                    id_attr = inp.get_attribute('id') or ''
                    print(f"  {i+1}. Type: {inp_type}, Name: '{name}', Placeholder: '{placeholder}', ID: '{id_attr}'")
            except:
                continue

        # Find email field (exact selectors from HTML)
        email_field = None
        email_selectors = [
            'input[name="username"]',  # Exact match from HTML
            'input[id="id_username"]',  # Exact ID from HTML
            'input[placeholder="Username or Email"]'  # Exact placeholder
        ]

        for selector in email_selectors:
            print(f"  Trying email selector: {selector}")
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        name = element.get_attribute('name') or ''
                        placeholder = element.get_attribute('placeholder') or ''
                        print(f"    ✅ Found potential email field: name='{name}', placeholder='{placeholder}'")
                        email_field = element
                        break
                if email_field:
                    break
            except Exception as e:
                print(f"    ❌ Error with {selector}: {e}")
                continue
        
        # Find password field (exact selectors from HTML)
        password_field = None
        password_selectors = [
            'input[name="password"]',  # Exact match from HTML
            'input[id="id_password"]',  # Exact ID from HTML
            'input[type="password"]'   # Fallback
        ]

        for selector in password_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        password_field = element
                        print(f"✅ Found password field with: {selector}")
                        break
                if password_field:
                    break
            except:
                continue
        
        # Find login button (exact selectors from HTML)
        login_button = None
        button_selectors = [
            'input[value="Log in"]',  # Exact match from HTML
            'input.btn-submit',       # Exact class from HTML
            'input[type="submit"]'    # Fallback
        ]
        
        for selector in button_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        login_button = element
                        text = element.text or element.get_attribute('value') or 'Submit'
                        print(f"✅ Found login button: '{text}' with {selector}")
                        break
                if login_button:
                    break
            except:
                continue
        
        # Check if all elements found
        if email_field and password_field and login_button:
            print(f"\n🎉 All login elements found!")

            # STEP 4: Use credentials directly
            print(f"\n📱 STEP 4: Using provided credentials...")

            email = "<EMAIL>"
            password = "Lennyai123#"

            if email and password:
                print(f"\n🔸 Filling form...")

                # Fill email
                try:
                    email_field.clear()
                    email_field.send_keys(email)
                    print(f"✅ Email entered")
                except Exception as e:
                    print(f"❌ Error entering email: {e}")

                # Fill password
                try:
                    password_field.clear()
                    password_field.send_keys(password)
                    print(f"✅ Password entered")
                except Exception as e:
                    print(f"❌ Error entering password: {e}")

                # Submit
                try:
                    print(f"🎯 Clicking login button...")
                    login_button.click()
                    time.sleep(10)

                    final_url = driver.current_url
                    print(f"📍 After login: {final_url}")

                    if "login" not in final_url.lower():
                        print(f"🎉 LOGIN SUCCESS!")
                        print(f"✅ You are now logged in!")

                        # STEP 5: Go to part page and download 3D model
                        print(f"\n📱 STEP 5: Going to part page for 3D download...")

                        part_number = "LM358N/NOPB"  # Can change this to any part
                        part_url = f"https://www.snapeda.com/parts/{part_number}/Texas%20Instruments/view-part/"

                        print(f"🔧 Going to part: {part_number}")
                        driver.get(part_url)
                        time.sleep(8)

                        print(f"📍 On part page: {driver.current_url}")

                        # Look for 3D Model tab
                        print(f"🔸 Looking for 3D Model tab...")
                        tab_elements = driver.find_elements(By.XPATH, "//li[contains(text(), '3D Model')] | //a[contains(text(), '3D Model')]")

                        if tab_elements:
                            for element in tab_elements:
                                if element.is_displayed():
                                    print(f"🎯 Clicking 3D Model tab...")
                                    element.click()
                                    time.sleep(10)
                                    break

                            # Look for download button
                            print(f"🔸 Looking for Download 3D Model button...")

                            # Show all download-related elements for debugging
                            all_downloads = driver.find_elements(By.XPATH, "//*[contains(text(), 'Download')]")
                            print(f"🔍 Found {len(all_downloads)} elements with 'Download' text:")
                            for i, elem in enumerate(all_downloads):
                                try:
                                    if elem.is_displayed():
                                        text = elem.text.strip()
                                        tag = elem.tag_name
                                        print(f"  {i+1}. '{text}' ({tag})")
                                except:
                                    continue

                            # Try multiple download button selectors
                            download_selectors = [
                                "//a[contains(text(), 'Download 3D Model')]",
                                "//button[contains(text(), 'Download 3D Model')]",
                                "//a[contains(text(), 'Download')]",
                                "//button[contains(text(), 'Download')]",
                                "//*[@class='download-btn']",
                                "//*[contains(@class, 'download')]"
                            ]

                            download_clicked = False
                            for selector in download_selectors:
                                if download_clicked:
                                    break

                                print(f"  Trying selector: {selector}")
                                try:
                                    elements = driver.find_elements(By.XPATH, selector)
                                    for element in elements:
                                        if element.is_displayed():
                                            text = element.text.strip()
                                            print(f"    Found: '{text}'")

                                            if any(word in text.lower() for word in ['3d', 'model', 'download']):
                                                print(f"🎯 Clicking: '{text}'")
                                                element.click()
                                                time.sleep(5)
                                                print(f"✅ Download clicked!")

                                                # Wait for download and move file
                                                print(f"🔸 Waiting for download to complete...")
                                                time.sleep(10)  # Wait for download

                                                # Move and rename file
                                                import os
                                                import shutil

                                                downloads_dir = os.path.expanduser("~/Downloads")

                                                # Create 3d directory if it doesn't exist
                                                target_dir = "3d"
                                                os.makedirs(target_dir, exist_ok=True)

                                                # New filename format: snapeda_manufacturer_partnumber.step
                                                manufacturer = "Texas_Instruments"
                                                clean_part = part_number.replace('/', '_')
                                                target_file = os.path.join(target_dir, f"snapeda_{manufacturer}_{clean_part}.step")

                                                # Try different possible source filenames
                                                possible_names = [
                                                    f"{clean_part}.STEP",
                                                    f"{clean_part}.step",
                                                    f"{part_number}.STEP",
                                                    f"{part_number}.step"
                                                ]

                                                moved = False
                                                for name in possible_names:
                                                    source_file = os.path.join(downloads_dir, name)
                                                    if os.path.exists(source_file):
                                                        shutil.move(source_file, target_file)
                                                        print(f"✅ File moved from {name} to: {target_file}")
                                                        moved = True
                                                        break

                                                if not moved:
                                                    print(f"⚠️ Could not find downloaded file in Downloads folder")

                                                # Close browser after successful download
                                                print(f"🔸 Download complete - closing browser...")
                                                driver.quit()
                                                return

                                                download_clicked = True
                                                break
                                except Exception as e:
                                    print(f"    Error with selector: {e}")
                                    continue

                            if not download_clicked:
                                print(f"❌ No download button could be clicked")
                        else:
                            print(f"❌ No 3D Model tab found")

                    else:
                        print(f"❌ Login failed - check credentials")
                except Exception as e:
                    print(f"❌ Error clicking login button: {e}")
            else:
                print(f"❌ No credentials provided")
        else:
            print(f"\n❌ Missing login elements:")
            print(f"  Email: {'✅' if email_field else '❌'}")
            print(f"  Password: {'✅' if password_field else '❌'}")
            print(f"  Button: {'✅' if login_button else '❌'}")

            # DEBUG: Show what we actually found
            print(f"\n🔍 DEBUG: All input elements on page:")
            all_inputs = driver.find_elements(By.CSS_SELECTOR, "input")
            for i, inp in enumerate(all_inputs):
                try:
                    if inp.is_displayed():
                        inp_type = inp.get_attribute('type') or 'text'
                        name = inp.get_attribute('name') or ''
                        placeholder = inp.get_attribute('placeholder') or ''
                        print(f"  {i+1}. Type: {inp_type}, Name: '{name}', Placeholder: '{placeholder}'")
                except:
                    continue
        
        # Keep browser open
        print(f"\n🔸 Browser staying open for 5 minutes...")
        time.sleep(300)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
