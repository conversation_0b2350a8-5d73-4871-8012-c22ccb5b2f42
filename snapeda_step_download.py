#!/usr/bin/env python3
"""
SnapEDA STEP file downloader - get the actual STEP file
"""

import requests
from bs4 import BeautifulSoup
import json
import os
import time

def download_snapeda_step(part_number):
    print(f"🔍 DOWNLOADING STEP FILE FROM SNAPEDA FOR: {part_number}")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        # Step 1: Get search results via API
        print("1. Getting search results via API...")
        api_url = 'https://www.snapeda.com/api/v1/search'
        api_params = {'q': part_number, 'page': 1}

        api_response = session.get(api_url, params=api_params, timeout=30)
        print(f"   API status: {api_response.status_code}")

        if api_response.status_code != 200:
            print(f"   ❌ API search failed")
            return False

        # Parse JSON response
        try:
            search_data = api_response.json()
            print(f"   ✅ Got JSON data")
        except:
            print(f"   ❌ Failed to parse JSON")
            return False

        # Step 2: Find parts in the response
        parts = search_data.get('parts', [])
        if not parts:
            print(f"   ❌ No parts found in API response")
            return False

        print(f"   ✅ Found {len(parts)} parts")

        # Find our specific part
        target_part = None
        for part in parts:
            if part_number.upper() in part.get('name', '').upper():
                target_part = part
                break

        if not target_part:
            print(f"   ❌ Target part not found in results")
            return False

        # Build part URL
        part_url = f"https://www.snapeda.com/parts/{target_part['urlname']}/{target_part['manufacturer']}/view-part/"
        print(f"   ✅ Found part URL: {part_url}")
        
        # Step 3: Access the part page
        print("2. Accessing part page...")
        part_response = session.get(part_url, timeout=30)
        print(f"   Part page status: {part_response.status_code}")
        
        if part_response.status_code != 200:
            print(f"   ❌ Failed to access part page")
            return False
        
        # Save part page for analysis
        with open('snapeda_part_page.html', 'w', encoding='utf-8') as f:
            f.write(part_response.text)
        print(f"   📄 Saved part page")
        
        # Step 4: Look for download links
        soup = BeautifulSoup(part_response.text, 'html.parser')
        
        # Look for download buttons/links
        download_links = []
        
        # Common download link patterns
        download_selectors = [
            'a[href*="download"]',
            'a[href*="step"]',
            'a[href*="3d"]',
            'a[href*="model"]',
            '.download',
            '.btn-download',
            'button[onclick*="download"]'
        ]
        
        for selector in download_selectors:
            elements = soup.select(selector)
            for elem in elements:
                href = elem.get('href', '')
                onclick = elem.get('onclick', '')
                text = elem.get_text(strip=True).lower()
                
                if any(keyword in text for keyword in ['download', 'step', '3d', 'model']):
                    download_links.append({
                        'element': elem,
                        'href': href,
                        'onclick': onclick,
                        'text': text
                    })
        
        if not download_links:
            print(f"   ❌ No download links found")
            return False
        
        print(f"   ✅ Found {len(download_links)} download links:")
        for i, link in enumerate(download_links[:3], 1):
            print(f"   {i}. {link['text']}")
            print(f"      href: {link['href']}")
        
        # Step 5: Try to download STEP file
        for link in download_links:
            if try_download_step(session, link, part_number):
                return True
        
        print(f"   ❌ No STEP files successfully downloaded")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def try_download_step(session, link, part_number):
    """Try to download STEP file from a link"""
    print(f"\n📥 Trying to download from: {link['text']}")
    
    try:
        href = link['href']
        
        # Skip empty or javascript links
        if not href or href.startswith('javascript:') or href == '#':
            print(f"   ⚠️  Skipping invalid link")
            return False
        
        # Make URL absolute
        if not href.startswith('http'):
            href = f"https://www.snapeda.com{href}"
        
        print(f"   URL: {href}")
        
        # Try to download
        download_response = session.get(href, timeout=60, stream=True)
        print(f"   Status: {download_response.status_code}")
        
        if download_response.status_code == 200:
            # Check content type
            content_type = download_response.headers.get('content-type', '').lower()
            print(f"   Content-Type: {content_type}")
            
            # Check if it's a STEP file or download
            if any(ct in content_type for ct in ['application/octet-stream', 'application/step', 'model/step']):
                return save_step_file(download_response, part_number, "direct")
            
            # If it's HTML, might be a download page
            elif 'text/html' in content_type:
                # Parse the download page
                soup = BeautifulSoup(download_response.text, 'html.parser')
                
                # Look for direct STEP download links
                step_links = []
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    if any(ext in href.lower() for ext in ['.step', '.stp']):
                        step_links.append(href)
                
                if step_links:
                    print(f"   ✅ Found {len(step_links)} STEP links on download page")
                    
                    # Try to download the first STEP file
                    step_url = step_links[0]
                    if not step_url.startswith('http'):
                        step_url = f"https://www.snapeda.com{step_url}"
                    
                    print(f"   Downloading STEP: {step_url}")
                    step_response = session.get(step_url, timeout=60, stream=True)
                    
                    if step_response.status_code == 200:
                        return save_step_file(step_response, part_number, "indirect")
                
                print(f"   ❌ No STEP files found on download page")
                return False
            
            else:
                print(f"   ❌ Unexpected content type: {content_type}")
                return False
        
        else:
            print(f"   ❌ Download failed: {download_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Download error: {e}")
        return False

def save_step_file(response, part_number, method):
    """Save the STEP file"""
    try:
        # Create 3d directory
        os.makedirs('3d', exist_ok=True)
        
        # Generate filename
        filename = f"{part_number}_SnapEDA.step"
        filepath = os.path.join('3d', filename)
        
        # Save file
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        file_size = os.path.getsize(filepath)
        print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes) via {method}")
        
        if file_size > 1000:
            # Verify it's a STEP file
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                first_lines = f.read(200)
            
            if 'ISO-10303' in first_lines or 'STEP' in first_lines:
                print(f"   🎉 SUCCESS: Valid STEP file downloaded!")
                return True
            else:
                print(f"   ⚠️  File doesn't appear to be STEP format")
                # Show first few lines for debugging
                print(f"   First lines: {first_lines[:100]}...")
                return False
        else:
            print(f"   ⚠️  File too small, might be error page")
            return False
            
    except Exception as e:
        print(f"   ❌ Save error: {e}")
        return False

def main():
    part_number = "APX803L20-30SA-7"
    
    print("🚀 SNAPEDA STEP FILE DOWNLOADER")
    print("=" * 60)
    
    success = download_snapeda_step(part_number)
    
    print("\n" + "=" * 60)
    if success:
        print("✅ SUCCESS: Downloaded STEP file from SnapEDA!")
        
        # Check what we got
        step_files = []
        if os.path.exists('3d'):
            for file in os.listdir('3d'):
                if file.endswith('.step') and 'SnapEDA' in file:
                    step_files.append(file)
        
        if step_files:
            print(f"\n🎯 STEP FILES DOWNLOADED:")
            for file in step_files:
                filepath = os.path.join('3d', file)
                size = os.path.getsize(filepath)
                print(f"   ✅ {file} ({size:,} bytes)")
        
    else:
        print("❌ FAILED: Could not download STEP file from SnapEDA")
        print("\nThis might be because:")
        print("1. SnapEDA requires login for downloads")
        print("2. The part doesn't have a 3D model")
        print("3. Additional steps are needed to access the download")

if __name__ == "__main__":
    main()
