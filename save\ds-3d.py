import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from bs4 import BeautifulSoup
import os
import re
import json
import time
from urllib.parse import urljoin, urlparse
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComponentSearcher:
    def __init__(self, download_dir="files-download"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = self._create_session()

        # Known manufacturer websites and their search patterns
        self.manufacturer_sites = {
            "diodes inc": {
                "base_url": "https://www.diodes.com",
                "search_method": "form_search",  # Use form-based search
                "search_url": "https://www.diodes.com/search/",
                "search_urls": [
                    "https://www.diodes.com/part/view/{}",
                    "https://www.diodes.com/products/catalog/part/{}",
                    "https://www.diodes.com/products/{}",
                ],
                "datasheet_urls": [
                    "https://www.diodes.com/assets/Datasheets/{}.pdf",
                    "https://www.diodes.com/datasheet/download/{}.pdf"
                ],
                "datasheet_patterns": [r'href="([^"]*\.pdf[^"]*)"[^>]*>.*?datasheet',
                                     r'href="([^"]*datasheet[^"]*\.pdf)"'],
                "3d_model_patterns": [r'href="([^"]*\.step[^"]*)"', r'href="([^"]*\.stp[^"]*)"']
            },
            "texas instruments": {
                "base_url": "https://www.ti.com",
                "search_urls": ["https://www.ti.com/product/{}"],
                "datasheet_patterns": [r'href="([^"]*\.pdf[^"]*)"[^>]*>.*?datasheet'],
                "3d_model_patterns": [r'href="([^"]*\.step[^"]*)"', r'href="([^"]*\.stp[^"]*)"']
            },
            "analog devices": {
                "base_url": "https://www.analog.com",
                "search_urls": ["https://www.analog.com/en/products/{}.html"],
                "datasheet_patterns": [r'href="([^"]*\.pdf[^"]*)"[^>]*>.*?datasheet'],
                "3d_model_patterns": [r'href="([^"]*\.step[^"]*)"', r'href="([^"]*\.stp[^"]*)"']
            }
        }

    def _create_session(self):
        """Create a requests session with retry strategy"""
        session = requests.Session()
        retries = Retry(
            total=3,
            backoff_factor=2,
            status_forcelist=[429, 502, 503, 504],
            allowed_methods=["GET"]
        )
        session.mount("https://", HTTPAdapter(max_retries=retries))
        session.mount("http://", HTTPAdapter(max_retries=retries))

        # Set user agent to avoid blocking
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        return session

    def search_manufacturer_website(self, manufacturer, part_number):
        """Search for a part on the manufacturer's website"""
        manufacturer_lower = manufacturer.lower()

        # Special handling for Diodes Inc
        if manufacturer_lower == "diodes inc":
            return self.search_diodes_website(part_number)

        if manufacturer_lower not in self.manufacturer_sites:
            logger.warning(f"Manufacturer '{manufacturer}' not in known sites. Attempting generic search...")
            return self._generic_web_search(manufacturer, part_number)

        site_info = self.manufacturer_sites[manufacturer_lower]

        # Try different URL formats for the part number
        base_part = part_number.split('-')[0]  # APX803L20-30SA-7 -> APX803L20
        part_variations = [
            part_number,  # Full part number
            base_part,    # Base part without suffix
            base_part[:-2] if len(base_part) > 2 else base_part,  # Remove last 2 chars (APX803L20 -> APX803L)
            base_part[:-1] if len(base_part) > 1 else base_part,  # Remove last char (APX803L20 -> APX803L2)
            part_number.replace('-', ''),  # Remove dashes
            part_number.upper(),
            part_number.lower(),
            base_part.upper(),
            base_part.lower()
        ]

        for part_var in part_variations:
            for search_url_template in site_info["search_urls"]:
                try:
                    url = search_url_template.format(part_var)
                    logger.info(f"🔍 Searching: {url}")

                    response = self.session.get(url, timeout=30)
                    if response.status_code == 200:
                        return response.text, url

                except Exception as e:
                    logger.error(f"Error searching for {part_var} at {url}: {e}")
                    continue

        return None, None

    def _generic_web_search(self, manufacturer, part_number):
        """Fallback method using web search"""
        try:
            from .web_search import web_search  # Import web search if available
            query = f'"{part_number}" {manufacturer} datasheet filetype:pdf'
            results = web_search(query, num_results=3)

            if results:
                logger.info(f"Found {len(results)} web search results")
                # Return the first result's URL for further processing
                return None, results[0]['url']  # Return URL for datasheet download

        except ImportError:
            logger.info(f"Web search not available, manual intervention needed for {manufacturer}")
        except Exception as e:
            logger.error(f"Web search failed: {e}")

        return None, None

    def try_direct_datasheet_urls(self, manufacturer, part_number):
        """Try direct datasheet URLs for known manufacturers"""
        manufacturer_lower = manufacturer.lower()

        if manufacturer_lower not in self.manufacturer_sites:
            return []

        site_info = self.manufacturer_sites[manufacturer_lower]
        if "datasheet_urls" not in site_info:
            return []

        # Generate part number variations
        part_variations = [
            part_number,
            part_number.split('-')[0],  # Remove suffix (APX803L20-30SA-7 -> APX803L20)
            part_number.split('-')[0][:-2] if len(part_number.split('-')[0]) > 2 else part_number.split('-')[0],  # Remove last 2 chars (APX803L20 -> APX803L)
            part_number.replace('-', ''),  # Remove dashes
            part_number.upper(),
            part_number.lower()
        ]

        found_datasheets = []

        for part_var in part_variations:
            for datasheet_url_template in site_info["datasheet_urls"]:
                try:
                    url = datasheet_url_template.format(part_var)
                    logger.info(f"🔍 Trying direct datasheet: {url}")

                    # Use HEAD request to check if file exists without downloading
                    response = self.session.head(url, timeout=15)
                    if response.status_code == 200:
                        content_type = response.headers.get('Content-Type', '')
                        if 'pdf' in content_type.lower():
                            logger.info(f"✅ Found datasheet: {url}")
                            found_datasheets.append(url)

                except Exception as e:
                    logger.debug(f"Direct datasheet URL failed {url}: {e}")
                    continue

        return found_datasheets

    def search_diodes_website(self, part_number):
        """Search Diodes Inc website using their search functionality"""
        try:
            # First, get the main page to understand the search form
            main_page = self.session.get("https://www.diodes.com", timeout=30)
            if main_page.status_code != 200:
                return None, None

            # Try the search functionality - Diodes uses a search form
            search_data = {
                'q': part_number,
                'search_type': 'part'
            }

            # Try different search approaches
            search_urls = [
                "https://www.diodes.com/search/",
                "https://www.diodes.com/products/search/",
                f"https://www.diodes.com/search/?q={part_number}",
                f"https://www.diodes.com/products/search/?q={part_number}"
            ]

            for search_url in search_urls:
                try:
                    logger.info(f"🔍 Trying search: {search_url}")

                    if '?' in search_url:
                        # GET request with query parameter
                        response = self.session.get(search_url, timeout=30)
                    else:
                        # POST request with form data
                        response = self.session.post(search_url, data=search_data, timeout=30)

                    if response.status_code == 200 and len(response.text) > 1000:
                        # Check if we got search results
                        if part_number.lower() in response.text.lower():
                            logger.info(f"✅ Found search results for {part_number}")
                            return response.text, search_url

                except Exception as e:
                    logger.debug(f"Search attempt failed for {search_url}: {e}")
                    continue

            # If search doesn't work, try direct part URLs with better variations
            part_base = part_number.split('-')[0]  # APX803L20-30SA-7 -> APX803L20

            # For Diodes, try removing numbers from the end to get the family
            part_family = part_base
            if part_base[-2:].isdigit():  # If ends with 2 digits
                part_family = part_base[:-2]  # APX803L20 -> APX803L
            elif part_base[-1:].isdigit():  # If ends with 1 digit
                part_family = part_base[:-1]  # APX803L2 -> APX803L

            direct_urls = [
                f"https://www.diodes.com/part/view/{part_family}",
                f"https://www.diodes.com/part/view/{part_base}",
                f"https://www.diodes.com/part/view/{part_number}",
                f"https://www.diodes.com/products/{part_family}",
                f"https://www.diodes.com/products/{part_base}",
            ]

            for url in direct_urls:
                try:
                    logger.info(f"🔍 Trying direct URL: {url}")
                    response = self.session.get(url, timeout=30)
                    if response.status_code == 200 and len(response.text) > 1000:
                        # Check if this looks like a valid product page
                        content_lower = response.text.lower()
                        if (part_number.lower() in content_lower or
                            part_base.lower() in content_lower or
                            part_family.lower() in content_lower):
                            logger.info(f"✅ Found product page: {url}")
                            return response.text, url

                except Exception as e:
                    logger.debug(f"Direct URL failed {url}: {e}")
                    continue

            return None, None

        except Exception as e:
            logger.error(f"Diodes search failed: {e}")
            return None, None

    def extract_datasheet_links(self, html, base_url):
        """Extract datasheet download links from HTML"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')
        datasheet_links = []

        # Look for PDF links that might be datasheets
        pdf_patterns = [
            r'href="([^"]*\.pdf[^"]*)"[^>]*>.*?datasheet',
            r'href="([^"]*datasheet[^"]*\.pdf)"',
            r'href="([^"]*\.pdf)"[^>]*>.*?(?:data\s*sheet|specification|spec\s*sheet)',
            r'href="([^"]*(?:datasheet|spec|specification)[^"]*\.pdf)"'
        ]

        html_lower = html.lower()

        for pattern in pdf_patterns:
            matches = re.finditer(pattern, html_lower, re.IGNORECASE)
            for match in matches:
                link = match.group(1)
                full_url = urljoin(base_url, link)
                if full_url not in datasheet_links:
                    datasheet_links.append(full_url)

        # Also look for direct PDF links in anchor tags
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().lower()

            if (href.endswith('.pdf') and
                any(keyword in text for keyword in ['datasheet', 'data sheet', 'specification', 'spec sheet'])):
                full_url = urljoin(base_url, href)
                if full_url not in datasheet_links:
                    datasheet_links.append(full_url)

        return datasheet_links

    def extract_package_info(self, html, datasheet_content=None):
        """Extract package information from HTML or datasheet content"""
        content = html
        if datasheet_content:
            content += " " + datasheet_content

        if not content:
            return None

        content_lower = content.lower()

        # Common package types for electronic components
        package_patterns = {
            'SOT-23': [r'sot-?23', r'sot23'],
            'SOT-323': [r'sot-?323', r'sot323'],
            'SOT-25': [r'sot-?25', r'sot25'],
            'SC-59': [r'sc-?59', r'sc59'],
            'SC-70': [r'sc-?70', r'sc70'],
            'SOIC-8': [r'soic-?8', r'so-?8'],
            'SOIC-14': [r'soic-?14', r'so-?14'],
            'SOIC-16': [r'soic-?16', r'so-?16'],
            'TSSOP-8': [r'tssop-?8'],
            'TSSOP-14': [r'tssop-?14'],
            'TSSOP-16': [r'tssop-?16'],
            'QFN-16': [r'qfn-?16'],
            'QFN-20': [r'qfn-?20'],
            'QFN-24': [r'qfn-?24'],
            'BGA': [r'bga'],
            'DIP-8': [r'dip-?8', r'pdip-?8'],
            'DIP-14': [r'dip-?14', r'pdip-?14'],
            'DIP-16': [r'dip-?16', r'pdip-?16'],
            'TO-220': [r'to-?220'],
            'TO-252': [r'to-?252'],
            'TO-263': [r'to-?263']
        }

        found_packages = []
        for package_name, patterns in package_patterns.items():
            for pattern in patterns:
                if re.search(pattern, content_lower):
                    found_packages.append(package_name)
                    break

        return found_packages[0] if found_packages else None

    def search_3d_models(self, html, base_url, package_type=None):
        """Search for 3D model files (STEP, STP files)"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')
        model_links = []

        # Look for STEP/STP files
        model_patterns = [
            r'href="([^"]*\.step[^"]*)"',
            r'href="([^"]*\.stp[^"]*)"',
            r'href="([^"]*3d[^"]*\.(?:step|stp))"',
            r'href="([^"]*model[^"]*\.(?:step|stp))"'
        ]

        html_lower = html.lower()

        for pattern in model_patterns:
            matches = re.finditer(pattern, html_lower, re.IGNORECASE)
            for match in matches:
                link = match.group(1)
                full_url = urljoin(base_url, link)
                if full_url not in model_links:
                    model_links.append(full_url)

        # Also look for links with 3D model keywords
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().lower()

            if (any(ext in href.lower() for ext in ['.step', '.stp']) or
                any(keyword in text for keyword in ['3d model', '3d', 'step', 'cad'])):
                if href.endswith(('.step', '.stp')):
                    full_url = urljoin(base_url, href)
                    if full_url not in model_links:
                        model_links.append(full_url)

        return model_links

    def download_file(self, url, filename=None):
        """Download a file from URL"""
        try:
            logger.info(f"📥 Downloading: {url}")
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()

            if not filename:
                # Extract filename from URL or Content-Disposition header
                if 'Content-Disposition' in response.headers:
                    cd = response.headers['Content-Disposition']
                    filename_match = re.search(r'filename="?([^"]+)"?', cd)
                    if filename_match:
                        filename = filename_match.group(1)

                if not filename:
                    filename = os.path.basename(urlparse(url).path)
                    if not filename or '.' not in filename:
                        # Generate a filename based on content type
                        content_type = response.headers.get('Content-Type', '')
                        if 'pdf' in content_type:
                            filename = f"datasheet_{int(time.time())}.pdf"
                        elif 'step' in content_type or url.endswith(('.step', '.stp')):
                            filename = f"3d_model_{int(time.time())}.step"
                        else:
                            filename = f"download_{int(time.time())}"

            filepath = self.download_dir / filename

            # Avoid overwriting existing files
            counter = 1
            original_filepath = filepath
            while filepath.exists():
                name, ext = original_filepath.stem, original_filepath.suffix
                filepath = self.download_dir / f"{name}_{counter}{ext}"
                counter += 1

            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"✅ Downloaded: {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"❌ Download failed for {url}: {e}")
            return None

    def validate_datasheet(self, filepath, part_number):
        """Basic validation to check if the downloaded file contains the part number"""
        try:
            # For now, just check if file exists and has reasonable size
            if not os.path.exists(filepath):
                return False

            file_size = os.path.getsize(filepath)
            if file_size < 1000:  # Less than 1KB is probably not a real datasheet
                logger.warning(f"Downloaded file {filepath} is very small ({file_size} bytes)")
                return False

            # TODO: Add PDF text extraction to verify part number is mentioned
            logger.info(f"✅ Datasheet validation passed for {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error validating datasheet {filepath}: {e}")
            return False

    def search_component(self, manufacturer, part_number):
        """Main method to search for component datasheet and 3D model"""
        results = {
            'manufacturer': manufacturer,
            'part_number': part_number,
            'datasheet_url': None,
            'datasheet_file': None,
            'package_type': None,
            '3d_model_url': None,
            '3d_model_file': None,
            'success': False
        }

        logger.info(f"🔍 Searching for {manufacturer} {part_number}")

        # Step 1: Search manufacturer website
        html, page_url = self.search_manufacturer_website(manufacturer, part_number)
        if not html:
            logger.error(f"❌ Could not find part page for {part_number}")
            return results

        logger.info(f"✅ Found part page: {page_url}")

        # Step 2: Extract package information
        package_type = self.extract_package_info(html)
        results['package_type'] = package_type
        if package_type:
            logger.info(f"📦 Package type: {package_type}")
        else:
            logger.warning("⚠️ Could not determine package type")

        # Step 3: Find and download datasheet
        datasheet_links = self.extract_datasheet_links(html, page_url) if html else []

        # Also try direct datasheet URLs
        direct_datasheet_links = self.try_direct_datasheet_urls(manufacturer, part_number)
        all_datasheet_links = direct_datasheet_links + datasheet_links

        if all_datasheet_links:
            logger.info(f"📄 Found {len(all_datasheet_links)} potential datasheet(s)")
            for i, link in enumerate(all_datasheet_links):
                logger.info(f"  {i+1}. {link}")

                # Try to download the first datasheet
                if i == 0:  # Download the first one
                    datasheet_file = self.download_file(link)
                    if datasheet_file and self.validate_datasheet(datasheet_file, part_number):
                        results['datasheet_url'] = link
                        results['datasheet_file'] = datasheet_file
                        logger.info(f"✅ Datasheet downloaded: {datasheet_file}")
                        break
        else:
            logger.warning("⚠️ No datasheet links found")

        # Step 4: Search for 3D models
        model_links = self.search_3d_models(html, page_url, package_type)
        if model_links:
            logger.info(f"🎯 Found {len(model_links)} potential 3D model(s)")
            for i, link in enumerate(model_links):
                logger.info(f"  {i+1}. {link}")

                # Try to download the first 3D model
                if i == 0:  # Download the first one
                    model_file = self.download_file(link)
                    if model_file:
                        results['3d_model_url'] = link
                        results['3d_model_file'] = model_file
                        logger.info(f"✅ 3D model downloaded: {model_file}")
                        break
        else:
            logger.warning("⚠️ No 3D model links found")

        # Determine overall success
        results['success'] = bool(results['datasheet_file'] or results['3d_model_file'])

        return results


def main():
    """Command line interface"""
    import argparse

    parser = argparse.ArgumentParser(description='Search and download component datasheets and 3D models')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Diodes Inc")')
    parser.add_argument('part_number', help='Part number (e.g., "APX803L20-30SA-7")')
    parser.add_argument('--download-dir', default='files-download',
                       help='Directory to save downloaded files (default: files-download)')

    args = parser.parse_args()

    # Create searcher instance
    searcher = ComponentSearcher(download_dir=args.download_dir)

    # Search for component
    results = searcher.search_component(args.manufacturer, args.part_number)

    # Print results
    print("\n" + "="*60)
    print("SEARCH RESULTS")
    print("="*60)
    print(f"Manufacturer: {results['manufacturer']}")
    print(f"Part Number: {results['part_number']}")
    print(f"Package Type: {results['package_type'] or 'Unknown'}")
    print()

    if results['datasheet_file']:
        print(f"✅ Datasheet: {results['datasheet_file']}")
        print(f"   URL: {results['datasheet_url']}")
    else:
        print("❌ Datasheet: Not found")

    if results['3d_model_file']:
        print(f"✅ 3D Model: {results['3d_model_file']}")
        print(f"   URL: {results['3d_model_url']}")
    else:
        print("❌ 3D Model: Not found")

    print()
    if results['success']:
        print("🎉 Search completed successfully!")
    else:
        print("⚠️ Search completed with limited results")

    return results


if __name__ == "__main__":
    import sys
    # Test with the example part
    if len(sys.argv) == 1:
        # No command line arguments, run test
        searcher = ComponentSearcher()
        results = searcher.search_component("Diodes Inc", "APX803L20-30SA-7")
        print(json.dumps(results, indent=2))
    else:
        main()
