#!/usr/bin/env python3
"""
Debug which step fails before Download Now
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def debug_steps():
    print("🔍 DEBUGGING STEPS BEFORE DOWNLOAD NOW")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Load homepage
        print("\n🔸 STEP 1: Loading homepage...")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(5)
        print(f"   ✅ URL: {driver.current_url}")
        print(f"   ✅ Title: {driver.title}")
        
        # Step 2: Click LOGIN
        print("\n🔸 STEP 2: Clicking LOGIN...")
        login_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='Login']")
        
        if not login_links:
            print("   ❌ FAILED: No LOGIN link found!")
            return False
        
        login_link = login_links[0]
        print(f"   ✅ Found LOGIN link: {login_link.text}")
        
        try:
            login_link.click()
        except Exception as e:
            print(f"   ⚠️ Regular click failed, trying JavaScript...")
            driver.execute_script("arguments[0].click();", login_link)
        
        time.sleep(5)
        print(f"   ✅ After LOGIN click: {driver.current_url}")
        
        # Step 3: Fill login form
        print("\n🔸 STEP 3: Filling login form...")
        
        # Load credentials
        try:
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            print(f"   ✅ Credentials loaded: {email}")
        except Exception as e:
            print(f"   ❌ FAILED: Could not load credentials: {e}")
            return False
        
        # Find email input
        email_inputs = driver.find_elements(By.XPATH, "//input[contains(@placeholder, 'Email')]")
        if not email_inputs:
            email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[name='Username']")
        
        if not email_inputs:
            print("   ❌ FAILED: No email input found!")
            return False
        
        email_input = email_inputs[0]
        email_input.clear()
        email_input.send_keys(email)
        print("   ✅ Email entered")
        
        # Find password input
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        if not password_inputs:
            print("   ❌ FAILED: No password input found!")
            return False
        
        password_input = password_inputs[0]
        password_input.clear()
        password_input.send_keys(password)
        print("   ✅ Password entered")
        
        # Find login button
        login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')]")
        if not login_buttons:
            print("   ❌ FAILED: No login button found!")
            return False
        
        login_button = login_buttons[0]
        print(f"   ✅ Found login button: {login_button.text}")
        login_button.click()
        time.sleep(10)
        
        print(f"   ✅ After login: {driver.current_url}")
        
        # Step 4: Search
        print("\n🔸 STEP 4: Searching for LM358N...")
        
        search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i], input[type='text'], input[type='search']")
        
        search_input = None
        for inp in search_inputs:
            if inp.is_displayed() and inp.is_enabled():
                search_input = inp
                break
        
        if not search_input:
            print("   ❌ FAILED: No search box found!")
            return False
        
        print("   ✅ Found search box")
        search_input.clear()
        search_input.send_keys("LM358N")
        search_input.send_keys(Keys.RETURN)
        time.sleep(8)
        
        print(f"   ✅ After search: {driver.current_url}")
        
        # Step 5: Click on part
        print("\n🔸 STEP 5: Clicking on LM358N part...")
        
        # Look for TI LM358N links
        ti_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'LM358N') and contains(text(), 'TI')]")
        
        if not ti_links:
            print("   ⚠️ No TI LM358N links found, looking for any LM358N...")
            ti_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'LM358N')]")
        
        if not ti_links:
            print("   ❌ FAILED: No LM358N links found!")
            return False
        
        ti_link = ti_links[0]
        print(f"   ✅ Found part link: {ti_link.text}")
        ti_link.click()
        time.sleep(8)
        
        print(f"   ✅ After part click: {driver.current_url}")
        
        # Step 6: Look for Download Now button
        print("\n🔸 STEP 6: Looking for 'Download Now' button...")
        
        download_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')] | //a[contains(text(), 'Download Now')]")
        
        if not download_buttons:
            print("   ❌ FAILED: No 'Download Now' button found!")
            
            # Show what buttons ARE available
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            all_links = driver.find_elements(By.TAG_NAME, "a")
            
            print("   📋 Available buttons:")
            for i, btn in enumerate(all_buttons[:10], 1):
                try:
                    if btn.is_displayed():
                        text = btn.text.strip()[:50]
                        print(f"      {i}. '{text}'")
                except:
                    continue
            
            print("   📋 Available links:")
            for i, link in enumerate(all_links[:10], 1):
                try:
                    if link.is_displayed():
                        text = link.text.strip()[:50]
                        if text:
                            print(f"      {i}. '{text}'")
                except:
                    continue
            
            return False
        
        download_button = download_buttons[0]
        print(f"   ✅ SUCCESS: Found 'Download Now' button: {download_button.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print(f"\n⏸️ Keeping browser open for inspection...")
        input("Press Enter to close: ")
        driver.quit()

if __name__ == "__main__":
    success = debug_steps()
    if success:
        print("\n🎉 REACHED DOWNLOAD NOW BUTTON!")
    else:
        print("\n❌ FAILED BEFORE DOWNLOAD NOW!")
