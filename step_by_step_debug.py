#!/usr/bin/env python3
"""
STEP BY STEP DEBUG
==================
Show exactly where the workflow is failing.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def step_by_step_debug():
    print("🎯 STEP BY STEP DEBUG")
    print("=" * 40)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # STEP 1: Load UltraLibrarian
        print("\n🔸 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print(f"✅ Step 1 complete: {driver.title}")
        print(f"URL: {driver.current_url}")
        
        # STEP 2: Search
        print("\n🔸 STEP 2: Searching for LM358N...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_box = None
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                search_box = inp
                break
        
        if not search_box:
            print("❌ FAILED at Step 2: No search box found!")
            return
        
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        time.sleep(10)
        print(f"✅ Step 2 complete: Search submitted")
        print(f"URL: {driver.current_url}")
        
        # STEP 3: Click TI part
        print("\n🔸 STEP 3: Finding Texas Instruments LM358N...")
        links = driver.find_elements(By.TAG_NAME, "a")
        ti_link = None
        
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    ti_link = link
                    print(f"Found TI part: {text}")
                    break
            except:
                continue
        
        if not ti_link:
            print("❌ FAILED at Step 3: No TI LM358N found!")
            return
        
        ti_link.click()
        time.sleep(8)
        print(f"✅ Step 3 complete: Clicked TI part")
        print(f"URL: {driver.current_url}")
        
        # STEP 4: Download Now
        print("\n🔸 STEP 4: Looking for Download Now...")
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        
        if not download_btns:
            print("❌ FAILED at Step 4: No Download Now button found!")
            # Show available buttons
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons:")
            for btn in buttons[:10]:
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"  - '{text}'")
                except:
                    continue
            return
        
        print(f"Found Download Now: {download_btns[0].text}")
        driver.execute_script("arguments[0].click();", download_btns[0])
        time.sleep(5)
        print(f"✅ Step 4 complete: Clicked Download Now")
        print(f"URL: {driver.current_url}")
        
        # STEP 5: 3D CAD Model
        print("\n🔸 STEP 5: Looking for 3D CAD Model...")
        cad_btns = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
        
        if not cad_btns:
            print("❌ FAILED at Step 5: No 3D CAD Model button found!")
            # Show available buttons
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons after Download Now:")
            for btn in buttons[:15]:
                try:
                    text = btn.text.strip()
                    visible = btn.is_displayed()
                    if text:
                        print(f"  - '{text}' (visible={visible})")
                except:
                    continue
            return
        
        print(f"Found 3D CAD Model: {cad_btns[0].text}")
        driver.execute_script("arguments[0].click();", cad_btns[0])
        time.sleep(5)
        print(f"✅ Step 5 complete: Clicked 3D CAD Model")
        print(f"URL: {driver.current_url}")
        
        # Check for new window
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
            print(f"New window URL: {driver.current_url}")
        
        # STEP 6: STEP selection
        print("\n🔸 STEP 6: Looking for STEP format...")
        step_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP')]")
        
        if not step_elements:
            print("❌ FAILED at Step 6: No STEP elements found!")
            # Show what's available
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons:")
            for btn in buttons[:10]:
                try:
                    text = btn.text.strip()
                    visible = btn.is_displayed()
                    if text:
                        print(f"  - '{text}' (visible={visible})")
                except:
                    continue
            return
        
        step_element = None
        for elem in step_elements:
            if elem.is_displayed():
                step_element = elem
                print(f"Found STEP element: {elem.text}")
                break
        
        if not step_element:
            print("❌ FAILED at Step 6: No visible STEP elements!")
            return
        
        driver.execute_script("arguments[0].click();", step_element)
        time.sleep(3)
        print(f"✅ Step 6 complete: Selected STEP")
        
        # STEP 7: Download button
        print("\n🔸 STEP 7: Looking for download button...")
        download_elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')]")
        
        if not download_elements:
            print("❌ FAILED at Step 7: No download button found!")
            return
        
        download_element = None
        for elem in download_elements:
            if elem.is_displayed():
                download_element = elem
                print(f"Found download button: {elem.text}")
                break
        
        if not download_element:
            print("❌ FAILED at Step 7: No visible download button!")
            return
        
        driver.execute_script("arguments[0].click();", download_element)
        time.sleep(5)
        print(f"✅ Step 7 complete: Clicked download")
        print(f"URL: {driver.current_url}")
        
        # Check if we reached login
        if 'login' in driver.current_url.lower() or 'sso' in driver.current_url.lower():
            print("✅ SUCCESS: Reached login screen!")
        else:
            print("❌ Did not reach login screen")
            print(f"Current URL: {driver.current_url}")
        
        print("\n🔍 WORKFLOW DEBUG COMPLETE")
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error at current step: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    step_by_step_debug()
