#!/usr/bin/env python3
"""
Analog Devices Part Page - Get to actual AD8065 part page and look for 3D models
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def handle_cookie_popups(driver):
    """Handle cookie consent popups"""
    print("🍪 Handling cookie popups...")

    cookie_button_texts = [
        "Accept", "Accept All", "Accept Cookies", "OK", "Got it",
        "I Agree", "Agree", "Continue", "Allow", "Allow All"
    ]

    for text in cookie_button_texts:
        try:
            elements = driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')] | //a[contains(text(), '{text}')]")
            for element in elements:
                if element.is_displayed():
                    print(f"  🎯 Clicking cookie button: '{text}'")
                    element.click()
                    time.sleep(2)
                    return True
        except:
            continue

    return False

def main():
    print("🎯 ANALOG DEVICES PART PAGE ANALYSIS")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Go directly to part page if we can construct the URL
        print("\n📱 STEP 1: Trying direct part page access...")
        
        # Try common ADI part page URL patterns
        possible_urls = [
            "https://www.analog.com/en/products/ad8065.html",
            "https://www.analog.com/en/products/ad8065",
            "https://www.analog.com/media/en/technical-documentation/data-sheets/AD8065_8066.pdf"
        ]
        
        part_page_found = False
        
        for url in possible_urls:
            print(f"  Trying: {url}")
            try:
                driver.get(url)
                time.sleep(5)

                # Handle cookie popup immediately
                handle_cookie_popups(driver)
                
                if driver.current_url != url and "404" not in driver.title.lower():
                    print(f"  ✅ Found part page: {driver.current_url}")
                    part_page_found = True
                    break
                elif "404" not in driver.title.lower() and "AD8065" in driver.page_source:
                    print(f"  ✅ Found part page: {driver.current_url}")
                    part_page_found = True
                    break
                else:
                    print(f"  ❌ Not found")
            except:
                print(f"  ❌ Error accessing")
                continue
        
        # If direct access failed, use search
        if not part_page_found:
            print(f"\n📱 STEP 2: Using search to find part page...")
            
            driver.get("https://www.analog.com/")
            time.sleep(5)
            
            # Find and use search
            search_box = driver.find_element(By.ID, "autocomplete-0-input")
            search_box.clear()
            search_box.send_keys("AD8065")
            search_box.send_keys(Keys.RETURN)
            time.sleep(8)
            
            print(f"📍 Search results: {driver.current_url}")
            
            # Look for the actual part page link (not datasheet or model)
            part_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'products') and contains(text(), 'AD8065')]")
            
            if not part_links:
                # Try broader search
                part_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'AD8065') and not(contains(text(), 'Data Sheet')) and not(contains(text(), 'Model'))]")
            
            print(f"Found {len(part_links)} potential part page links:")
            for i, link in enumerate(part_links[:5]):
                try:
                    text = link.text.strip()
                    href = link.get_attribute('href')
                    print(f"  {i+1}. '{text}' -> {href}")
                except:
                    continue
            
            # Click the most likely part page link
            if part_links:
                for link in part_links:
                    try:
                        href = link.get_attribute('href')
                        text = link.text.strip()
                        
                        # Look for product page URLs
                        if 'products' in href and 'AD8065' in text and len(text) < 100:
                            print(f"🎯 Clicking part page link: '{text}'")
                            link.click()
                            time.sleep(8)
                            part_page_found = True
                            break
                    except:
                        continue
        
        if part_page_found:
            print(f"\n📱 STEP 3: Analyzing part page for 3D models...")
            print(f"📍 Part page URL: {driver.current_url}")
            print(f"📄 Title: {driver.title}")
            
            # Look for 3D model sections
            print(f"\n🔸 Looking for 3D/CAD model sections...")
            
            # Check for common 3D model indicators
            model_indicators = [
                "3D Model", "3d model", "STEP", "step", "CAD", "cad",
                "Mechanical", "Package", "Footprint", "Symbol"
            ]
            
            found_3d_elements = []
            
            for indicator in model_indicators:
                elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{indicator}')]")
                for elem in elements:
                    try:
                        if elem.is_displayed():
                            text = elem.text.strip()
                            tag = elem.tag_name
                            found_3d_elements.append((indicator, text, tag, elem))
                            print(f"  ✅ '{indicator}': '{text[:60]}...' ({tag})")
                    except:
                        continue
            
            # Look for download links
            print(f"\n🔸 Looking for download links...")
            
            download_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'download') or contains(text(), 'Download')]")
            
            print(f"Found {len(download_links)} download links:")
            for i, link in enumerate(download_links[:10]):
                try:
                    if link.is_displayed():
                        text = link.text.strip()
                        href = link.get_attribute('href')
                        print(f"  {i+1}. '{text}' -> {href[:60]}...")
                        
                        if any(word in text.lower() for word in ['3d', 'step', 'cad', 'model']):
                            print(f"      🎯 POTENTIAL 3D DOWNLOAD!")
                except:
                    continue
            
            # Look for tabs or sections
            print(f"\n🔸 Looking for tabs/sections...")
            
            tabs = driver.find_elements(By.CSS_SELECTOR, ".tab, [role='tab'], .nav-item, .section-header")
            
            for i, tab in enumerate(tabs[:15]):
                try:
                    if tab.is_displayed():
                        text = tab.text.strip()
                        if text and len(text) < 50:
                            print(f"  Tab {i+1}: '{text}'")
                            
                            if any(word in text.lower() for word in ['3d', 'cad', 'model', 'design', 'package']):
                                print(f"      🎯 POTENTIAL 3D SECTION!")
                                
                                # Try clicking this tab
                                try:
                                    print(f"      🎯 Clicking tab: '{text}'")
                                    tab.click()
                                    time.sleep(5)
                                    
                                    # Look for 3D content after clicking
                                    new_3d_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP') or contains(text(), '3D')]")
                                    if new_3d_elements:
                                        print(f"      ✅ Found {len(new_3d_elements)} 3D elements after clicking!")
                                except:
                                    print(f"      ❌ Could not click tab")
                except:
                    continue
            
            # Final summary
            print(f"\n📋 PART PAGE ANALYSIS SUMMARY:")
            print(f"🌐 Site: analog.com")
            print(f"📄 Part: AD8065")
            print(f"🎯 3D indicators: {len(found_3d_elements)} found")
            print(f"🔗 Download links: {len(download_links)} found")
            
            if found_3d_elements:
                print(f"✅ POTENTIAL 3D MODEL AVAILABILITY!")
            else:
                print(f"❌ No clear 3D model indicators found")
        
        else:
            print(f"❌ Could not reach part page")
        
        # Keep browser open
        print(f"\n🔸 Browser staying open for 5 minutes...")
        print(f"👀 Manual inspection time!")
        time.sleep(300)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
