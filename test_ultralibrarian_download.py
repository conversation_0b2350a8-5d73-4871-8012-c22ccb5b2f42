#!/usr/bin/env python3
"""
TEST ULTRALIBRARIAN DOWNLOAD
============================
Simple test to verify UltraLibrarian automation works and downloads actual STEP file.
Based on the exact 7-screen workflow we mapped together.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_chrome():
    """Setup Chrome with download preferences"""
    chrome_options = Options()
    
    # Download preferences
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    # Basic options
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def test_ultralibrarian_download():
    """Test the complete UltraLibrarian download workflow"""
    print("🧪 TESTING ULTRALIBRARIAN DOWNLOAD")
    print("=" * 50)
    
    # Check initial files
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    print(f"Initial files in 3D/: {len(initial_files)}")
    
    driver = setup_chrome()
    
    try:
        # Step 1: Go to UltraLibrarian
        print("\n📍 Step 1: Opening UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(5)
        
        print(f"Current URL: {driver.current_url}")
        print(f"Page title: {driver.title}")
        
        # Step 2: Search for LM358N
        print("\n🔍 Step 2: Searching for LM358N...")
        
        # Wait for page to load and find search box
        wait = WebDriverWait(driver, 20)
        
        # Try different search box selectors
        search_selectors = [
            "input[type='search']",
            "input[placeholder*='search']",
            "input[placeholder*='Search']",
            "input[name*='search']",
            ".search-input",
            "#search",
            "input[type='text']"
        ]
        
        search_box = None
        for selector in search_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        search_box = element
                        print(f"✅ Found search box with selector: {selector}")
                        break
                if search_box:
                    break
            except:
                continue
        
        if not search_box:
            print("❌ Could not find search box")
            print("Available input elements:")
            inputs = driver.find_elements(By.TAG_NAME, "input")
            for i, inp in enumerate(inputs[:10]):  # Show first 10
                try:
                    print(f"  {i}: type='{inp.get_attribute('type')}' placeholder='{inp.get_attribute('placeholder')}' visible={inp.is_displayed()}")
                except:
                    pass
            return False
        
        # Search for LM358N
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        
        print("✅ Search submitted, waiting for results...")
        time.sleep(5)
        
        # Step 3: Look for Texas Instruments LM358N in results
        print("\n📋 Step 3: Looking for Texas Instruments LM358N...")
        
        # Wait a bit more for results to load
        time.sleep(3)
        
        # Look for part links or results
        result_selectors = [
            "a[href*='LM358']",
            "a[href*='texas-instruments']",
            ".search-result",
            ".part-result",
            "a[href*='details']"
        ]
        
        found_part = False
        for selector in result_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.lower()
                    if 'lm358' in text and ('texas' in text or 'ti' in text):
                        print(f"✅ Found matching part: {element.text[:100]}")
                        element.click()
                        found_part = True
                        break
                if found_part:
                    break
            except Exception as e:
                print(f"Error with selector {selector}: {e}")
                continue
        
        if not found_part:
            print("❌ Could not find Texas Instruments LM358N in results")
            print("Available links:")
            links = driver.find_elements(By.TAG_NAME, "a")
            for i, link in enumerate(links[:20]):  # Show first 20
                try:
                    href = link.get_attribute('href')
                    text = link.text.strip()
                    if text and len(text) < 100:
                        print(f"  {i}: {text} -> {href}")
                except:
                    pass
            return False
        
        print("✅ Clicked on part, waiting for part details page...")
        time.sleep(5)
        
        # Step 4: Look for Download Now button
        print("\n⬇️ Step 4: Looking for Download Now button...")
        
        download_selectors = [
            "button:contains('Download Now')",
            "a:contains('Download Now')",
            ".download-button",
            "button[class*='download']",
            "a[class*='download']"
        ]
        
        download_button = None
        for selector in download_selectors:
            try:
                if ':contains(' in selector:
                    # Use XPath for text content
                    xpath = f"//button[contains(text(), 'Download Now')] | //a[contains(text(), 'Download Now')]"
                    elements = driver.find_elements(By.XPATH, xpath)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        download_button = element
                        print(f"✅ Found download button: {element.text}")
                        break
                if download_button:
                    break
            except:
                continue
        
        if not download_button:
            print("❌ Could not find Download Now button")
            print("Available buttons:")
            buttons = driver.find_elements(By.TAG_NAME, "button")
            for i, btn in enumerate(buttons[:10]):
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"  {i}: {text}")
                except:
                    pass
            return False
        
        download_button.click()
        print("✅ Clicked Download Now, waiting for next screen...")
        time.sleep(3)
        
        # Step 5: Look for 3D CAD Model option
        print("\n🎯 Step 5: Looking for 3D CAD Model option...")
        
        # This is where we need to continue the workflow...
        # For now, let's wait and see if any files were downloaded
        time.sleep(10)
        
        # Check for downloaded files
        current_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
        new_files = current_files - initial_files
        
        print(f"\n📁 Files check:")
        print(f"Initial files: {len(initial_files)}")
        print(f"Current files: {len(current_files)}")
        print(f"New files: {list(new_files)}")
        
        # Look for STEP files specifically
        step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
        
        if step_files:
            print(f"🎉 SUCCESS! Downloaded STEP files: {step_files}")
            return True
        else:
            print("❌ No STEP files downloaded")
            return False
        
    except Exception as e:
        print(f"❌ Error during automation: {e}")
        return False
    
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    success = test_ultralibrarian_download()
    if success:
        print("\n🎉 TEST PASSED: STEP file downloaded successfully!")
    else:
        print("\n❌ TEST FAILED: No STEP file downloaded")
