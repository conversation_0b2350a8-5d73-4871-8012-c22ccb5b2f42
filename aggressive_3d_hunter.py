#!/usr/bin/env python3
"""
Aggressive 3D Hunter - Find 3D models by any means necessary
"""

import time
import os
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def handle_cookie_popups(driver):
    """Universal cookie popup handler"""
    cookie_texts = ["Accept", "Accept All", "OK", "Got it", "I Agree", "Continue", "Allow"]
    
    for text in cookie_texts:
        try:
            elements = driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')] | //a[contains(text(), '{text}')]")
            for element in elements:
                if element.is_displayed():
                    element.click()
                    time.sleep(2)
                    return True
        except:
            continue
    return False

def hunt_external_3d_providers(driver, part_number):
    """Look for external 3D model providers embedded in the page"""
    print("🎯 Hunting for external 3D model providers...")
    
    # Common 3D model provider domains
    providers = [
        'ultralibrarian.com',
        'samacsys.com', 
        'snapeda.com',
        'componentsearchengine.com',
        'accelerated-designs.com',
        'library.io'
    ]
    
    found_providers = []
    
    # Method 1: Look for iframes from 3D providers
    print("  🔍 Checking for embedded 3D provider iframes...")
    iframes = driver.find_elements(By.CSS_SELECTOR, "iframe")
    
    for iframe in iframes:
        try:
            src = iframe.get_attribute('src') or ''
            for provider in providers:
                if provider in src:
                    print(f"    ✅ Found {provider} iframe: {src}")
                    found_providers.append({
                        'provider': provider,
                        'type': 'iframe',
                        'url': src,
                        'element': iframe
                    })
        except:
            continue
    
    # Method 2: Look for links to 3D providers
    print("  🔍 Checking for links to 3D providers...")
    all_links = driver.find_elements(By.CSS_SELECTOR, "a[href]")
    
    for link in all_links:
        try:
            href = link.get_attribute('href') or ''
            text = link.text.strip()
            
            for provider in providers:
                if provider in href:
                    print(f"    ✅ Found {provider} link: '{text}' -> {href}")
                    found_providers.append({
                        'provider': provider,
                        'type': 'link',
                        'url': href,
                        'text': text,
                        'element': link
                    })
        except:
            continue
    
    # Method 3: Look for JavaScript references to 3D providers
    print("  🔍 Checking page source for 3D provider references...")
    page_source = driver.page_source.lower()
    
    for provider in providers:
        if provider in page_source:
            print(f"    ✅ Found {provider} reference in page source")
            found_providers.append({
                'provider': provider,
                'type': 'reference',
                'url': f"https://{provider}"
            })
    
    return found_providers

def try_direct_3d_urls(part_number, manufacturer):
    """Try direct URLs to known 3D model providers"""
    print("🎯 Trying direct URLs to 3D model providers...")
    
    # Clean part number for URLs
    clean_part = part_number.replace('/', '%2F')
    
    # Direct URL patterns for various providers
    direct_urls = [
        f"https://www.ultralibrarian.com/search?q={clean_part}",
        f"https://componentsearchengine.com/search?term={clean_part}",
        f"https://www.snapeda.com/search?q={clean_part}",
        f"https://www.samacsys.com/search?q={clean_part}"
    ]
    
    found_models = []
    
    for url in direct_urls:
        try:
            print(f"  🔍 Checking: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                content = response.text.lower()
                
                # Look for 3D model indicators in the response
                if any(indicator in content for indicator in ['3d model', 'step', '.step', 'cad model']):
                    print(f"    ✅ Found 3D model indicators at: {url}")
                    found_models.append(url)
                else:
                    print(f"    ❌ No 3D indicators found")
            else:
                print(f"    ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
            continue
    
    return found_models

def deep_dive_3d_section(driver, section_element):
    """Deep dive into a 3D section to find actual download links"""
    print("  🔍 Deep diving into 3D section...")
    
    try:
        # Scroll to the section
        driver.execute_script("arguments[0].scrollIntoView();", section_element)
        time.sleep(2)
        
        # Try clicking the section
        section_element.click()
        time.sleep(5)
        
        # Look for any new download links that appeared
        download_links = driver.find_elements(By.XPATH, "//a[contains(@href, '.step') or contains(@href, '.stp') or contains(text(), 'Download')]")
        
        found_downloads = []
        for link in download_links:
            try:
                if link.is_displayed():
                    text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    
                    if any(ext in href.lower() for ext in ['.step', '.stp']) or 'download' in text.lower():
                        found_downloads.append({
                            'text': text,
                            'href': href,
                            'element': link
                        })
                        print(f"    ✅ Found download: '{text}' -> {href}")
            except:
                continue
        
        return found_downloads
        
    except Exception as e:
        print(f"    ❌ Error deep diving: {e}")
        return []

def aggressive_3d_hunt_test():
    """Aggressive test for 3D models on multiple approaches"""
    print("🎯 AGGRESSIVE 3D MODEL HUNT")
    print("=" * 50)
    
    part_number = "AD8065"
    manufacturer = "Analog_Devices"
    
    print(f"📦 Hunting for: {part_number} ({manufacturer})")
    
    # Method 1: Try direct provider URLs first (no browser needed)
    print(f"\n🔸 METHOD 1: Direct provider URLs")
    direct_models = try_direct_3d_urls(part_number, manufacturer)
    
    if direct_models:
        print(f"✅ Found {len(direct_models)} direct 3D model sources!")
        for url in direct_models:
            print(f"  - {url}")
    
    # Method 2: Browser-based hunting
    print(f"\n🔸 METHOD 2: Browser-based hunting")
    
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Go to manufacturer part page
        url = f"https://www.analog.com/en/products/{part_number}.html"
        driver.get(url)
        time.sleep(5)
        handle_cookie_popups(driver)
        
        # Hunt for external providers
        external_providers = hunt_external_3d_providers(driver, part_number)
        
        if external_providers:
            print(f"✅ Found {len(external_providers)} external 3D providers!")
            
            for provider in external_providers:
                print(f"  - {provider['provider']}: {provider['type']}")
                
                # Try to access the provider
                if provider['type'] == 'link':
                    try:
                        print(f"    🎯 Clicking {provider['provider']} link...")
                        provider['element'].click()
                        time.sleep(10)
                        
                        # Check if we're now on the provider site
                        if provider['provider'] in driver.current_url:
                            print(f"    ✅ Successfully navigated to {provider['provider']}")
                            
                            # Look for 3D downloads on provider site
                            step_links = driver.find_elements(By.XPATH, "//a[contains(@href, '.step') or contains(@href, '.stp')]")
                            
                            if step_links:
                                print(f"    🎉 Found {len(step_links)} STEP file links!")
                                
                                # Try downloading first STEP file
                                try:
                                    step_links[0].click()
                                    time.sleep(5)
                                    print(f"    ✅ Download attempted!")
                                except:
                                    print(f"    ⚠️ Could not click download")
                        
                        # Go back to original page
                        driver.back()
                        time.sleep(5)
                        
                    except Exception as e:
                        print(f"    ❌ Error accessing provider: {e}")
        
        # Method 3: Navigate to Component Resources tab and deep dive
        print(f"\n🔸 METHOD 3: Deep dive into Component Resources")
        
        try:
            # Click Component Resources tab
            comp_resources = driver.find_element(By.XPATH, "//a[contains(text(), 'Component Resources')]")
            comp_resources.click()
            time.sleep(5)
            
            # Look for 3D sections
            cad_sections = driver.find_elements(By.XPATH, "//*[contains(text(), 'CAD') or contains(text(), '3D')]")
            
            for section in cad_sections:
                if section.is_displayed():
                    downloads = deep_dive_3d_section(driver, section)
                    if downloads:
                        print(f"    🎉 Found downloads in section!")
                        
                        # Try first download
                        try:
                            downloads[0]['element'].click()
                            time.sleep(5)
                            print(f"    ✅ Download attempted!")
                        except:
                            print(f"    ⚠️ Could not click download")
                        break
        
        except Exception as e:
            print(f"❌ Error in deep dive: {e}")
        
        print(f"\n📋 HUNT SUMMARY:")
        print(f"🌐 Direct providers: {len(direct_models)} found")
        print(f"🔗 External providers: {len(external_providers)} found")
        print(f"💡 Multiple approaches attempted")
        
        # Keep browser open
        print(f"\n🔸 Browser staying open for manual inspection...")
        time.sleep(120)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    aggressive_3d_hunt_test()
