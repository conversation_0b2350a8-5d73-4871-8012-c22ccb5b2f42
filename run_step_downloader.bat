@echo off
REM ========================================
REM MANUFACTURER STEP FILE DOWNLOADER
REM Easy launcher for downloading STEP files
REM ========================================

echo.
echo ========================================
echo MANUFACTURER STEP FILE DOWNLOADER
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python first.
    pause
    exit /b 1
)

REM Check if the downloader script exists
if not exist "manufacturer_step_downloader.py" (
    echo ❌ manufacturer_step_downloader.py not found!
    echo Make sure you're in the correct directory.
    pause
    exit /b 1
)

echo Choose your option:
echo.
echo 1. Launch GUI (Recommended)
echo 2. Command Line Mode
echo 3. Quick Test (TI LM358N)
echo 4. Quick Test (Diodes Inc APX803L20-30SA-7)
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🚀 Launching GUI mode...
    python manufacturer_step_downloader.py --gui
) else if "%choice%"=="2" (
    echo.
    set /p manufacturer="Enter manufacturer name: "
    set /p part_number="Enter part number: "
    echo.
    echo 🔍 Searching for STEP files...
    python manufacturer_step_downloader.py "%manufacturer%" "%part_number%"
) else if "%choice%"=="3" (
    echo.
    echo 🧪 Testing with TI LM358N...
    python manufacturer_step_downloader.py "TI" "LM358N"
) else if "%choice%"=="4" (
    echo.
    echo 🧪 Testing with Diodes Inc APX803L20-30SA-7...
    python manufacturer_step_downloader.py "Diodes Inc" "APX803L20-30SA-7"
) else (
    echo.
    echo ❌ Invalid choice. Please run again and select 1-4.
)

echo.
echo ========================================
echo.
if exist "step_downloads" (
    echo 📁 Downloaded files are in: step_downloads\
    echo.
    echo Contents:
    dir step_downloads /b 2>nul
    if errorlevel 1 (
        echo    (No files downloaded yet)
    )
) else (
    echo 📁 No downloads folder created yet
)

echo.
pause
