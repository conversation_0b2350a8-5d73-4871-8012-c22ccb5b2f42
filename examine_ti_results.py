#!/usr/bin/env python3
"""
Baby step: Examine TI search results page for 3D model content
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

def examine_ti_results():
    print("EXAMINING TI SEARCH RESULTS")
    print("=" * 40)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Go directly to TI search results
        search_url = "https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm=LM358N"
        print(f"Going to: {search_url}")
        driver.get(search_url)
        time.sleep(10)
        
        print(f"Page title: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        # Look for 3D/STEP related content
        page_text = driver.page_source.lower()
        
        keywords = ['3d', 'step', 'model', 'cad', 'mechanical', 'package', 'download']
        print("\nSearching for 3D-related keywords:")
        for keyword in keywords:
            count = page_text.count(keyword)
            print(f"  '{keyword}': {count} occurrences")
        
        # Look for clickable elements with 3D/STEP content
        print("\nLooking for clickable 3D elements:")
        
        # Find all links
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"Found {len(links)} total links")
        
        step_links = []
        for link in links:
            try:
                text = link.text.strip().lower()
                href = link.get_attribute('href') or ''
                
                if any(word in text for word in ['3d', 'step', 'model', 'cad', 'mechanical']) or \
                   any(word in href.lower() for word in ['step', '3d', 'model']):
                    step_links.append((text, href))
                    print(f"  Found: '{text}' -> {href[:60]}...")
            except:
                continue
        
        if not step_links:
            print("  No 3D/STEP links found in main results")
            
            # Look for product detail links to click into
            print("\nLooking for product detail links:")
            product_links = []
            for link in links:
                try:
                    text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    
                    if 'LM358' in text.upper() or 'lm358' in href.lower():
                        product_links.append((text, href))
                        print(f"  Product: '{text}' -> {href[:60]}...")
                except:
                    continue
            
            # Click on first product link if found
            if product_links:
                print(f"\nClicking on first product link...")
                first_link = driver.find_element(By.XPATH, f"//a[contains(@href, '{product_links[0][1].split('/')[-1]}')]")
                first_link.click()
                time.sleep(10)
                
                print(f"Product page URL: {driver.current_url}")
                
                # Look for 3D content on product page
                product_page = driver.page_source.lower()
                print("\n3D keywords on product page:")
                for keyword in keywords:
                    count = product_page.count(keyword)
                    print(f"  '{keyword}': {count} occurrences")
        
    finally:
        input("Press Enter to close...")
        driver.quit()

if __name__ == "__main__":
    examine_ti_results()
