#!/usr/bin/env python3
"""
ROBUST ULTRALIBRARIAN SCRAPER
=============================
Handles multi-screen navigation and dynamic content loading.
Solves stale element references and buried download paths.
"""

import requests
import os
import time
import json
from urllib.parse import urljoin, quote
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException

class RobustUltraLibrarianScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.base_url = 'https://www.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("Robust UltraLibrarian Scraper Ready!")

    def setup_driver(self):
        """Setup Chrome driver with robust settings"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3D'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            driver.implicitly_wait(10)  # Global wait
            return driver
        except Exception as e:
            print(f"Chrome driver failed: {e}")
            return None

    def safe_click(self, driver, element_locator, description="element", max_retries=3):
        """Safely click an element with retries for stale references"""
        for attempt in range(max_retries):
            try:
                if isinstance(element_locator, tuple):
                    # It's a (By.X, "selector") tuple
                    element = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable(element_locator)
                    )
                else:
                    # It's already an element
                    element = element_locator
                
                # Scroll to element and click
                driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)
                driver.execute_script("arguments[0].click();", element)
                print(f"   Successfully clicked {description}")
                return True
                
            except StaleElementReferenceException:
                print(f"   Stale element on attempt {attempt + 1}, retrying...")
                time.sleep(2)
                continue
            except Exception as e:
                print(f"   Click attempt {attempt + 1} failed: {e}")
                time.sleep(2)
                continue
        
        print(f"   Failed to click {description} after {max_retries} attempts")
        return False

    def safe_find_elements(self, driver, by, selector, description="elements"):
        """Safely find elements with retry logic"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                elements = driver.find_elements(by, selector)
                if elements:
                    print(f"   Found {len(elements)} {description}")
                    return elements
                else:
                    print(f"   No {description} found on attempt {attempt + 1}")
                    time.sleep(2)
            except Exception as e:
                print(f"   Error finding {description} on attempt {attempt + 1}: {e}")
                time.sleep(2)
        
        return []

    def navigate_to_part_details(self, driver, manufacturer, part_number):
        """Navigate through UltraLibrarian's multi-screen process"""
        print(f"Navigating UltraLibrarian multi-screen process for {manufacturer} {part_number}...")
        
        # Search strategies
        search_terms = [
            f"{manufacturer} {part_number}",
            part_number,
            f"{part_number} {manufacturer}"
        ]
        
        for search_term in search_terms:
            print(f"   Trying search: '{search_term}'")
            
            # Step 1: Search
            search_url = f"{self.base_url}/search?q={quote(search_term)}"
            print(f"   Loading search: {search_url}")
            
            try:
                driver.get(search_url)
                WebDriverWait(driver, 15).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                time.sleep(5)  # Let dynamic content load
                
                # Step 2: Find and click part results
                part_results = self.find_part_results(driver)
                
                if not part_results:
                    print(f"   No part results for '{search_term}'")
                    continue
                
                # Step 3: Try each part result
                for i, part_info in enumerate(part_results[:5]):  # Try first 5
                    print(f"   Trying part result {i+1}: {part_info['text'][:50]}...")
                    
                    result = self.explore_part_result(driver, part_info, manufacturer, part_number)
                    if result:
                        return result
                    
                    # Go back to search results
                    driver.back()
                    time.sleep(3)
                
            except Exception as e:
                print(f"   Search '{search_term}' failed: {e}")
                continue
        
        return None

    def find_part_results(self, driver):
        """Find clickable part results with robust selectors"""
        part_results = []
        
        # Multiple strategies to find part links
        selectors = [
            (By.CSS_SELECTOR, "a[href*='/part/']"),
            (By.CSS_SELECTOR, "a[href*='/component/']"),
            (By.CSS_SELECTOR, ".search-result a"),
            (By.CSS_SELECTOR, ".part-result a"),
            (By.XPATH, "//a[contains(@href, 'part') or contains(@href, 'component')]"),
            (By.XPATH, "//div[contains(@class, 'result')]//a"),
            (By.XPATH, "//tr//a[contains(@href, 'ultralibrarian')]")
        ]
        
        for by, selector in selectors:
            elements = self.safe_find_elements(driver, by, selector, f"part links ({selector})")
            
            for element in elements:
                try:
                    href = element.get_attribute('href')
                    text = element.text.strip()
                    
                    if href and text and len(text) > 2:
                        # Filter out navigation/footer links
                        if any(skip in href.lower() for skip in ['login', 'register', 'about', 'contact', 'privacy']):
                            continue
                        
                        part_results.append({
                            'element': element,
                            'href': href,
                            'text': text,
                            'selector': selector
                        })
                        
                except StaleElementReferenceException:
                    continue
                except Exception as e:
                    continue
        
        # Remove duplicates by href
        seen_hrefs = set()
        unique_results = []
        for result in part_results:
            if result['href'] not in seen_hrefs:
                seen_hrefs.add(result['href'])
                unique_results.append(result)
        
        print(f"   Found {len(unique_results)} unique part results")
        return unique_results

    def explore_part_result(self, driver, part_info, manufacturer, part_number):
        """Explore a specific part result through multiple screens"""
        try:
            print(f"      Exploring: {part_info['text'][:50]}...")
            
            # Click the part link
            if not self.safe_click(driver, part_info['element'], f"part link: {part_info['text'][:30]}"):
                return None
            
            time.sleep(5)  # Wait for page load
            
            # Screen 1: Look for immediate downloads
            downloads = self.find_download_options(driver)
            if downloads:
                print(f"      Found {len(downloads)} immediate download options")
                result = self.try_downloads(driver, downloads, manufacturer, part_number)
                if result:
                    return result
            
            # Screen 2: Look for "more details" or "3D models" links
            detail_links = self.find_detail_navigation_links(driver)
            
            for detail_link in detail_links[:3]:  # Try first 3 detail links
                print(f"      Following detail link: {detail_link['text'][:30]}...")
                
                if not self.safe_click(driver, detail_link['element'], f"detail link: {detail_link['text'][:30]}"):
                    continue
                
                time.sleep(5)  # Wait for new page
                
                # Screen 3: Look for downloads on detail page
                detail_downloads = self.find_download_options(driver)
                if detail_downloads:
                    print(f"      Found {len(detail_downloads)} downloads on detail page")
                    result = self.try_downloads(driver, detail_downloads, manufacturer, part_number)
                    if result:
                        return result
                
                # Screen 4: Look for even deeper links (CAD models, STEP files, etc.)
                deeper_links = self.find_cad_model_links(driver)
                
                for deeper_link in deeper_links[:2]:  # Try first 2 deeper links
                    print(f"      Following CAD link: {deeper_link['text'][:30]}...")
                    
                    if not self.safe_click(driver, deeper_link['element'], f"CAD link: {deeper_link['text'][:30]}"):
                        continue
                    
                    time.sleep(5)
                    
                    # Screen 5: Final download attempt
                    final_downloads = self.find_download_options(driver)
                    if final_downloads:
                        print(f"      Found {len(final_downloads)} final downloads")
                        result = self.try_downloads(driver, final_downloads, manufacturer, part_number)
                        if result:
                            return result
                    
                    # Go back one level
                    driver.back()
                    time.sleep(3)
                
                # Go back to part page
                driver.back()
                time.sleep(3)
            
            return None
            
        except Exception as e:
            print(f"      Part exploration failed: {e}")
            return None

    def find_download_options(self, driver):
        """Find all download options on current page"""
        downloads = []
        
        # Comprehensive download selectors
        selectors = [
            (By.CSS_SELECTOR, "button[onclick*='download']"),
            (By.CSS_SELECTOR, "a[href*='download']"),
            (By.CSS_SELECTOR, "a[href*='.step']"),
            (By.CSS_SELECTOR, "a[href*='.stp']"),
            (By.CSS_SELECTOR, "a[href*='.zip']"),
            (By.CSS_SELECTOR, ".download-btn"),
            (By.CSS_SELECTOR, ".btn-download"),
            (By.XPATH, "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download')]"),
            (By.XPATH, "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download')]"),
            (By.XPATH, "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')]"),
            (By.XPATH, "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')]"),
            (By.XPATH, "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'step')]"),
            (By.XPATH, "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'step')]")
        ]
        
        for by, selector in selectors:
            elements = self.safe_find_elements(driver, by, selector, f"downloads ({selector})")
            
            for element in elements:
                try:
                    text = element.text.strip()
                    if not text:
                        text = element.get_attribute('title') or element.get_attribute('value') or 'Download'
                    
                    if text and any(keyword in text.lower() for keyword in ['download', '3d', 'step', 'cad', 'model']):
                        downloads.append({
                            'element': element,
                            'text': text,
                            'selector': selector
                        })
                        
                except StaleElementReferenceException:
                    continue
                except Exception:
                    continue
        
        return downloads

    def find_detail_navigation_links(self, driver):
        """Find links that lead to more detailed pages"""
        detail_links = []
        
        # Look for navigation to detail pages
        keywords = ['detail', 'more', 'info', 'spec', 'datasheet', 'model', '3d', 'cad', 'step']
        
        for keyword in keywords:
            xpath = f"//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]"
            elements = self.safe_find_elements(driver, By.XPATH, xpath, f"detail links ({keyword})")
            
            for element in elements:
                try:
                    text = element.text.strip()
                    href = element.get_attribute('href')
                    
                    if text and href and len(text) < 100:  # Reasonable link text
                        detail_links.append({
                            'element': element,
                            'text': text,
                            'href': href
                        })
                        
                except StaleElementReferenceException:
                    continue
                except Exception:
                    continue
        
        return detail_links

    def find_cad_model_links(self, driver):
        """Find specific CAD/3D model links"""
        cad_links = []
        
        # Specific CAD-related selectors
        selectors = [
            (By.XPATH, "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'step')]"),
            (By.XPATH, "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')]"),
            (By.XPATH, "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'cad')]"),
            (By.XPATH, "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'model')]"),
            (By.CSS_SELECTOR, "a[href*='3d']"),
            (By.CSS_SELECTOR, "a[href*='step']"),
            (By.CSS_SELECTOR, "a[href*='cad']")
        ]
        
        for by, selector in selectors:
            elements = self.safe_find_elements(driver, by, selector, f"CAD links ({selector})")
            
            for element in elements:
                try:
                    text = element.text.strip()
                    href = element.get_attribute('href')
                    
                    if text and href:
                        cad_links.append({
                            'element': element,
                            'text': text,
                            'href': href
                        })
                        
                except StaleElementReferenceException:
                    continue
                except Exception:
                    continue
        
        return cad_links

    def try_downloads(self, driver, downloads, manufacturer, part_number):
        """Try each download option"""
        for download in downloads:
            try:
                print(f"         Trying download: {download['text'][:40]}...")
                
                # Get initial file count
                initial_files = set(os.listdir('3D'))
                
                # Click download
                if not self.safe_click(driver, download['element'], f"download: {download['text'][:30]}"):
                    continue
                
                time.sleep(10)  # Wait for download
                
                # Check for new files
                current_files = set(os.listdir('3D'))
                new_files = current_files - initial_files
                
                for new_file in new_files:
                    if any(ext in new_file.lower() for ext in ['.step', '.stp', '.zip']):
                        print(f"         SUCCESS: Downloaded {new_file}")
                        
                        # Process the file
                        result = self.process_downloaded_file(new_file, manufacturer, part_number)
                        if result:
                            return result
                
            except Exception as e:
                print(f"         Download failed: {e}")
                continue
        
        return None

    def process_downloaded_file(self, filename, manufacturer, part_number):
        """Process and rename downloaded file"""
        try:
            old_path = os.path.join('3D', filename)
            
            if filename.lower().endswith('.zip'):
                # Extract STEP from ZIP
                extracted = self.extract_step_from_zip(old_path, manufacturer, part_number)
                if extracted:
                    os.remove(old_path)  # Remove ZIP
                    self.create_log_file(extracted, manufacturer, part_number)
                    return extracted
            else:
                # Rename STEP file
                new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                new_path = os.path.join('3D', new_name)
                
                try:
                    os.rename(old_path, new_path)
                    self.create_log_file(new_name, manufacturer, part_number)
                    return new_name
                except:
                    # If rename fails, keep original
                    self.create_log_file(filename, manufacturer, part_number)
                    return filename
        
        except Exception as e:
            print(f"         File processing failed: {e}")
        
        return None

    def extract_step_from_zip(self, zip_path, manufacturer, part_number):
        """Extract STEP files from ZIP"""
        try:
            import zipfile
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                step_files = [f for f in zip_ref.namelist() if f.lower().endswith(('.step', '.stp'))]
                
                if step_files:
                    step_file = step_files[0]
                    zip_ref.extract(step_file, '3D')
                    
                    extracted_path = os.path.join('3D', step_file)
                    new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_UL.step"
                    new_path = os.path.join('3D', new_name)
                    
                    os.rename(extracted_path, new_path)
                    print(f"         Extracted STEP from ZIP: {new_name}")
                    return new_name
            
        except Exception as e:
            print(f"         ZIP extraction failed: {e}")
        
        return None

    def create_log_file(self, filename, manufacturer, part_number):
        """Create log file for successful download"""
        log_name = filename.replace('.step', '.txt')
        log_path = os.path.join('3D', log_name)
        
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(f"{manufacturer.upper()} {part_number} STEP FILE DOWNLOAD LOG\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Manufacturer: {manufacturer}\n")
            f.write(f"Part Number: {part_number}\n")
            f.write(f"Source: UltraLibrarian (Robust Multi-Screen Scraping)\n")
            f.write(f"Downloaded File: {filename}\n")
            f.write(f"Location: 3D/{filename}\n")
            f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("DOWNLOAD DETAILS:\n")
            f.write("================\n")
            f.write("Method: Robust multi-screen navigation\n")
            f.write("Source: UltraLibrarian website\n")
            f.write("Process: Automated navigation through 5+ screens\n")
            f.write("Stale Element Handling: Implemented\n")
            f.write("Status: SUCCESS\n\n")
            f.write("NOTES:\n")
            f.write("======\n")
            f.write("- Downloaded via robust multi-screen scraping\n")
            f.write("- Handled dynamic page changes and stale elements\n")
            f.write("- Navigated through buried download paths\n")
            f.write("- Third-party CAD model library\n")
            f.write("- Verify dimensions against datasheet\n")

    def get_step_file(self, manufacturer, part_number):
        """Main method to get STEP file"""
        print(f"\nROBUST ULTRALIBRARIAN SCRAPING")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("-" * 50)
        
        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            result = self.navigate_to_part_details(driver, manufacturer, part_number)
            return result
            
        except Exception as e:
            print(f"Scraping error: {e}")
            return None
        finally:
            driver.quit()

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python robust_ultralibrarian_scraper.py \"Manufacturer\" \"Part Number\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    scraper = RobustUltraLibrarianScraper()
    result = scraper.get_step_file(manufacturer, part_number)
    
    if result:
        print(f"\nSUCCESS: Downloaded {result}")
        print(f"Location: 3D/{result}")
    else:
        print(f"\nFAILED: Could not download STEP file for {part_number}")

if __name__ == "__main__":
    main()
