#!/usr/bin/env python3
"""
DIAGNOSTIC ULTRALIBRARIAN AUTOMATION
====================================
Pauses at each screen to show exactly what's happening.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome"""
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def diagnostic_run():
    """Run diagnostic automation with pauses"""
    print("🔍 DIAGNOSTIC ULTRALIBRARIAN AUTOMATION")
    print("=" * 50)
    
    driver = setup_driver()
    
    try:
        # Screen 1: Search
        print("\n🔸 SCREEN 1: Opening UltraLibrarian and searching...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(10)
        
        print(f"Current URL: {driver.current_url}")
        print(f"Page title: {driver.title}")
        
        # Find search box
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(inputs)} input elements")
        
        search_box = None
        for i, inp in enumerate(inputs):
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    print(f"  Input {i}: placeholder='{placeholder}'")
                    if 'search' in placeholder.lower():
                        search_box = inp
                        print(f"  ✅ Using input {i} as search box")
                        break
            except:
                continue
        
        if not search_box:
            print("❌ No search box found!")
            return
        
        # Search
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        time.sleep(8)
        
        print("✅ Screen 1 complete - search submitted")
        input("Press Enter to continue to Screen 2...")
        
        # Screen 2: Select part
        print("\n🔸 SCREEN 2: Selecting LM358N part...")
        print(f"Current URL: {driver.current_url}")
        
        # Look for results
        links = driver.find_elements(By.TAG_NAME, "a")
        lm358_links = []
        
        for i, link in enumerate(links):
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if 'lm358' in text.lower() or 'lm358' in href.lower():
                    lm358_links.append((i, text, href))
                    print(f"  Link {i}: '{text}' -> {href}")
            except:
                continue
        
        if not lm358_links:
            print("❌ No LM358N links found!")
            return
        
        # Click first LM358N link
        first_link = driver.find_elements(By.TAG_NAME, "a")[lm358_links[0][0]]
        print(f"Clicking: {lm358_links[0][1]}")
        first_link.click()
        time.sleep(5)
        
        print("✅ Screen 2 complete - part selected")
        print(f"New URL: {driver.current_url}")
        input("Press Enter to continue to Screen 3...")
        
        # Screen 3: Download Now
        print("\n🔸 SCREEN 3: Looking for Download Now button...")
        print(f"Current URL: {driver.current_url}")
        
        # Find all buttons and links
        buttons = driver.find_elements(By.TAG_NAME, "button")
        links = driver.find_elements(By.TAG_NAME, "a")
        
        download_elements = []
        
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip().lower()
                if 'download' in text:
                    download_elements.append(('button', i, btn.text, btn))
                    print(f"  Button {i}: '{btn.text}'")
            except:
                continue
        
        for i, link in enumerate(links):
            try:
                text = link.text.strip().lower()
                if 'download' in text:
                    download_elements.append(('link', i, link.text, link))
                    print(f"  Link {i}: '{link.text}'")
            except:
                continue
        
        if not download_elements:
            print("❌ No download elements found!")
            return
        
        # Click first download element
        element_type, index, text, element = download_elements[0]
        print(f"Clicking {element_type}: '{text}'")
        element.click()
        time.sleep(3)
        
        print("✅ Screen 3 complete - Download Now clicked")
        print(f"New URL: {driver.current_url}")
        input("Press Enter to continue to Screen 4...")
        
        # Screen 4: 3D CAD Model
        print("\n🔸 SCREEN 4: Looking for 3D CAD Model option...")
        print(f"Current URL: {driver.current_url}")
        
        # Find 3D model elements
        buttons = driver.find_elements(By.TAG_NAME, "button")
        links = driver.find_elements(By.TAG_NAME, "a")
        
        model_elements = []
        
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip().lower()
                if '3d' in text or 'model' in text:
                    model_elements.append(('button', i, btn.text, btn))
                    print(f"  Button {i}: '{btn.text}'")
            except:
                continue
        
        for i, link in enumerate(links):
            try:
                text = link.text.strip().lower()
                if '3d' in text or 'model' in text:
                    model_elements.append(('link', i, link.text, link))
                    print(f"  Link {i}: '{link.text}'")
            except:
                continue
        
        if not model_elements:
            print("❌ No 3D model elements found!")
            return
        
        # Click first 3D model element
        element_type, index, text, element = model_elements[0]
        print(f"Clicking {element_type}: '{text}'")
        element.click()
        time.sleep(5)
        
        print("✅ Screen 4 complete - 3D CAD Model clicked")
        print(f"New URL: {driver.current_url}")
        input("Press Enter to continue to Screen 5...")
        
        # Screen 5: Final download/login
        print("\n🔸 SCREEN 5: Final download screen...")
        print(f"Current URL: {driver.current_url}")
        print(f"Page title: {driver.title}")
        
        # Check for login form
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email']")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        if email_inputs and password_inputs:
            print("🔐 Login form detected!")
            print("Email inputs:", len(email_inputs))
            print("Password inputs:", len(password_inputs))
        else:
            print("ℹ️ No login form detected")
        
        # Check for any download buttons
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        all_links = driver.find_elements(By.TAG_NAME, "a")
        
        print(f"\nAll buttons on page:")
        for i, btn in enumerate(all_buttons[:10]):  # Show first 10
            try:
                text = btn.text.strip()
                if text:
                    print(f"  Button {i}: '{text}'")
            except:
                continue
        
        print(f"\nAll links on page:")
        for i, link in enumerate(all_links[:10]):  # Show first 10
            try:
                text = link.text.strip()
                if text:
                    print(f"  Link {i}: '{text}'")
            except:
                continue
        
        print("\n🔍 DIAGNOSTIC COMPLETE")
        print("This is where the automation typically fails to download.")
        print("Please manually complete any remaining steps.")
        print("Check if files download to 3D/ folder or Downloads folder.")
        
        input("Press Enter when done (browser will close)...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    diagnostic_run()
