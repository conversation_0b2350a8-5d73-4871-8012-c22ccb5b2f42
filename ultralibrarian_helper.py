#!/usr/bin/env python3
"""
ULTRALIBRARIAN MANUAL HELPER
============================
Opens the correct UltraLibrarian search page for manual download.
Creates a log file to track what you need to download.
"""

import os
import webbrowser
import time
from urllib.parse import quote

class UltraLibrarianHelper:
    def __init__(self):
        self.base_url = 'https://app.ultralibrarian.com'
        os.makedirs('3D', exist_ok=True)
        print("UltraLibrarian Manual Helper Ready!")

    def open_search_page(self, manufacturer, part_number):
        """Open UltraLibrarian search page and create helper files"""
        print(f"\nOPENING ULTRALIBRARIAN FOR MANUAL DOWNLOAD")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("-" * 50)
        
        # Try different search strategies
        search_terms = [
            f"{manufacturer} {part_number}",
            part_number,
            f"{part_number} {manufacturer}",
            f"TI {part_number}" if manufacturer.upper() == "TI" else f"{manufacturer} {part_number}"
        ]
        
        # Create instruction file
        instruction_file = f"3D/{manufacturer.replace(' ', '_')}_{part_number}_INSTRUCTIONS.txt"
        self.create_instruction_file(instruction_file, manufacturer, part_number, search_terms)
        
        # Open the main UltraLibrarian app page for manual search
        primary_search = search_terms[0]
        search_url = f"https://app.ultralibrarian.com"
        
        print(f"Opening: {search_url}")
        print(f"Instructions saved to: {instruction_file}")
        
        try:
            webbrowser.open(search_url)
            print("\n✅ Browser opened with UltraLibrarian search")
            print("📋 Follow the instructions in the text file to download")
            return True
        except Exception as e:
            print(f"❌ Failed to open browser: {e}")
            return False

    def create_instruction_file(self, filename, manufacturer, part_number, search_terms):
        """Create detailed instructions for manual download"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"ULTRALIBRARIAN MANUAL DOWNLOAD INSTRUCTIONS\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"TARGET PART:\n")
            f.write(f"Manufacturer: {manufacturer}\n")
            f.write(f"Part Number: {part_number}\n")
            f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("SEARCH STRATEGIES:\n")
            f.write("==================\n")
            f.write("1. Open UltraLibrarian app: https://app.ultralibrarian.com\n")
            f.write("2. Use the search box in the app to search for:\n")
            for i, term in enumerate(search_terms, 1):
                f.write(f"   - '{term}'\n")
            f.write("\n")
            
            f.write("DOWNLOAD STEPS:\n")
            f.write("===============\n")
            f.write("1. Browser opened to UltraLibrarian app: https://app.ultralibrarian.com\n")
            f.write("2. Wait for the page to fully load (it's a JavaScript application)\n")
            f.write("3. Use the search box at the top to search for your part number\n")
            f.write("4. Try these search terms in order:\n")
            for i, term in enumerate(search_terms, 1):
                f.write(f"   {i}. '{term}'\n")
            f.write("5. Click on the matching part from the search results\n")
            f.write("6. On the part details page, look for download options:\n")
            f.write("   - 'Download Now' button\n")
            f.write("   - '3D CAD Model' section\n")
            f.write("   - 'STEP' file format option\n")
            f.write("7. You may need to:\n")
            f.write("   - Create a free UltraLibrarian account\n")
            f.write("   - Login with your credentials\n")
            f.write("   - Select STEP file format\n")
            f.write("   - Choose the correct package type if multiple options\n")
            f.write("8. Download the ZIP file containing the STEP model\n")
            f.write("9. Extract the STEP file from the ZIP\n")
            f.write("10. Save it to this folder: 3D/\n")
            f.write(f"11. Rename it to: {manufacturer.replace(' ', '_')}_{part_number}_UL.step\n\n")
            
            f.write("ALTERNATIVE SEARCHES:\n")
            f.write("====================\n")
            f.write("If no results found, try:\n")
            f.write("- Different manufacturer names (TI vs Texas Instruments)\n")
            f.write("- Part number without suffixes (-N, -SA, etc.)\n")
            f.write("- Generic part number (LM358 instead of LM358N)\n")
            f.write("- Package type + part number (DIP LM358)\n\n")
            
            f.write("TROUBLESHOOTING:\n")
            f.write("================\n")
            f.write("- No results? Try the alternative search URLs above\n")
            f.write("- Need account? Registration is usually free\n")
            f.write("- No STEP file? Look for other 3D formats (.stp, .3d)\n")
            f.write("- Multiple packages? Choose the one you need (DIP-8, SOIC-8)\n\n")
            
            f.write("WHEN COMPLETE:\n")
            f.write("==============\n")
            f.write("[✓] File downloaded and renamed\n")
            f.write("[✓] Saved in 3D/ folder\n")
            f.write("[✓] Ready for CAD import\n\n")
            
            f.write("OTHER 3D MODEL SOURCES:\n")
            f.write("=======================\n")
            f.write("If UltraLibrarian doesn't have it, try:\n")
            f.write("- SnapEDA: https://www.snapeda.com/\n")
            f.write("- SamacSys: https://www.samacsys.com/\n")
            f.write("- Component Search Plus: https://componentsearchengine.com/\n")
            f.write("- Manufacturer website directly\n")

    def create_completion_log(self, manufacturer, part_number, success=True, filename=None):
        """Create a log when user completes the download"""
        log_file = f"3D/{manufacturer.replace(' ', '_')}_{part_number}_UL.txt"
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"{manufacturer.upper()} {part_number} STEP FILE DOWNLOAD LOG\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Manufacturer: {manufacturer}\n")
            f.write(f"Part Number: {part_number}\n")
            f.write(f"Source: UltraLibrarian (Manual Download)\n")
            f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            if success and filename:
                f.write(f"Downloaded File: {filename}\n")
                f.write(f"Location: 3D/{filename}\n")
                f.write("Status: SUCCESS\n\n")
            else:
                f.write("Status: MANUAL PROCESS INITIATED\n\n")
            
            f.write("DOWNLOAD DETAILS:\n")
            f.write("================\n")
            f.write("Method: Manual download via browser\n")
            f.write("Source: UltraLibrarian website\n")
            f.write("Process: User-guided download\n\n")
            
            f.write("NOTES:\n")
            f.write("======\n")
            f.write("- Downloaded manually from UltraLibrarian\n")
            f.write("- Third-party CAD model library\n")
            f.write("- Verify dimensions against datasheet\n")
            f.write("- May require free account registration\n")

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python ultralibrarian_helper.py \"Manufacturer\" \"Part Number\"")
        print("\nExample: python ultralibrarian_helper.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    helper = UltraLibrarianHelper()
    success = helper.open_search_page(manufacturer, part_number)
    
    # Create initial log
    helper.create_completion_log(manufacturer, part_number, success=False)
    
    if success:
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. Browser opened to UltraLibrarian search")
        print(f"2. Follow instructions in: 3D/{manufacturer.replace(' ', '_')}_{part_number}_INSTRUCTIONS.txt")
        print(f"3. Download the STEP file manually")
        print(f"4. Save it as: {manufacturer.replace(' ', '_')}_{part_number}_UL.step")
        print(f"5. Put it in the 3D/ folder")
        
        print(f"\n📋 Files created:")
        print(f"- Instructions: 3D/{manufacturer.replace(' ', '_')}_{part_number}_INSTRUCTIONS.txt")
        print(f"- Log file: 3D/{manufacturer.replace(' ', '_')}_{part_number}_UL.txt")
    else:
        print(f"\n❌ Failed to open browser automatically")
        print(f"📋 Manual URL: https://www.ultralibrarian.com/search?q={quote(f'{manufacturer} {part_number}')}")

if __name__ == "__main__":
    main()
