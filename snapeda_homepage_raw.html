






<!DOCTYPE html>
<html lang="en">
<head>
    <!--[if IE]><style>div { zoom: 1; /* trigger hasLayout */ }</style><![endif]-->
    <!-- META -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta charset="UTF-8"><script type="text/javascript">(window.NREUM||(NREUM={})).init={ajax:{deny_list:["bam.nr-data.net"]}};(window.NREUM||(NREUM={})).loader_config={licenseKey:"40b3e098a7",applicationID:"29832197"};;/*! For license information please see nr-loader-rum-1.295.0.min.js.LICENSE.txt */
(()=>{var e,t,r={122:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});var n=r(944);function i(e,t){try{if(!e||"object"!=typeof e)return(0,n.R)(3);if(!t||"object"!=typeof t)return(0,n.R)(4);const r=Object.create(Object.getPrototypeOf(t),Object.getOwnPropertyDescriptors(t)),a=0===Object.keys(r).length?e:r;for(let s in a)if(void 0!==e[s])try{if(null===e[s]){r[s]=null;continue}Array.isArray(e[s])&&Array.isArray(t[s])?r[s]=Array.from(new Set([...e[s],...t[s]])):"object"==typeof e[s]&&"object"==typeof t[s]?r[s]=i(e[s],t[s]):r[s]=e[s]}catch(e){r[s]||(0,n.R)(1,e)}return r}catch(e){(0,n.R)(2,e)}}},555:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,f:()=>s});var n=r(384),i=r(122);const a={beacon:n.NT.beacon,errorBeacon:n.NT.errorBeacon,licenseKey:void 0,applicationID:void 0,sa:void 0,queueTime:void 0,applicationTime:void 0,ttGuid:void 0,user:void 0,account:void 0,product:void 0,extra:void 0,jsAttributes:{},userAttributes:void 0,atts:void 0,transactionName:void 0,tNamePlain:void 0};function s(e){try{return!!e.licenseKey&&!!e.errorBeacon&&!!e.applicationID}catch(e){return!1}}const o=e=>(0,i.a)(e,a)},324:(e,t,r)=>{"use strict";r.d(t,{F3:()=>i,Xs:()=>a,xv:()=>n});const n="1.295.0",i="PROD",a="CDN"},154:(e,t,r)=>{"use strict";r.d(t,{OF:()=>c,RI:()=>i,WN:()=>d,bv:()=>a,gm:()=>s,mw:()=>o,sb:()=>u});var n=r(863);const i="undefined"!=typeof window&&!!window.document,a="undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self.navigator instanceof WorkerNavigator||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis.navigator instanceof WorkerNavigator),s=i?window:"undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis),o=Boolean("hidden"===s?.document?.visibilityState),c=/iPad|iPhone|iPod/.test(s.navigator?.userAgent),u=c&&"undefined"==typeof SharedWorker,d=((()=>{const e=s.navigator?.userAgent?.match(/Firefox[/\s](\d+\.\d+)/);Array.isArray(e)&&e.length>=2&&e[1]})(),Date.now()-(0,n.t)())},241:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});var n=r(154);const i="newrelic";function a(e={}){try{n.gm.dispatchEvent(new CustomEvent(i,{detail:e}))}catch(e){}}},687:(e,t,r)=>{"use strict";r.d(t,{Ak:()=>u,Ze:()=>f,x3:()=>d});var n=r(241),i=r(836),a=r(606),s=r(860),o=r(646);const c={};function u(e,t){const r={staged:!1,priority:s.P3[t]||0};l(e),c[e].get(t)||c[e].set(t,r)}function d(e,t){e&&c[e]&&(c[e].get(t)&&c[e].delete(t),p(e,t,!1),c[e].size&&g(e))}function l(e){if(!e)throw new Error("agentIdentifier required");c[e]||(c[e]=new Map)}function f(e="",t="feature",r=!1){if(l(e),!e||!c[e].get(t)||r)return p(e,t);c[e].get(t).staged=!0,g(e)}function g(e){const t=Array.from(c[e]);t.every((([e,t])=>t.staged))&&(t.sort(((e,t)=>e[1].priority-t[1].priority)),t.forEach((([t])=>{c[e].delete(t),p(e,t)})))}function p(e,t,r=!0){const s=e?i.ee.get(e):i.ee,c=a.i.handlers;if(!s.aborted&&s.backlog&&c){if((0,n.W)({agentIdentifier:e,type:"lifecycle",name:"drain",feature:t}),r){const e=s.backlog[t],r=c[t];if(r){for(let t=0;e&&t<e.length;++t)m(e[t],r);Object.entries(r).forEach((([e,t])=>{Object.values(t||{}).forEach((t=>{t[0]?.on&&t[0]?.context()instanceof o.y&&t[0].on(e,t[1])}))}))}}s.isolatedBacklog||delete c[t],s.backlog[t]=null,s.emit("drain-"+t,[])}}function m(e,t){var r=e[1];Object.values(t[r]||{}).forEach((t=>{var r=e[0];if(t[0]===r){var n=t[1],i=e[3],a=e[2];n.apply(i,a)}}))}},836:(e,t,r)=>{"use strict";r.d(t,{P:()=>o,ee:()=>c});var n=r(384),i=r(990),a=r(646),s=r(607);const o="nr@context:".concat(s.W),c=function e(t,r){var n={},s={},d={},l=!1;try{l=16===r.length&&u.initializedAgents?.[r]?.runtime.isolatedBacklog}catch(e){}var f={on:p,addEventListener:p,removeEventListener:function(e,t){var r=n[e];if(!r)return;for(var i=0;i<r.length;i++)r[i]===t&&r.splice(i,1)},emit:function(e,r,n,i,a){!1!==a&&(a=!0);if(c.aborted&&!i)return;t&&a&&t.emit(e,r,n);var o=g(n);m(e).forEach((e=>{e.apply(o,r)}));var u=v()[s[e]];u&&u.push([f,e,r,o]);return o},get:h,listeners:m,context:g,buffer:function(e,t){const r=v();if(t=t||"feature",f.aborted)return;Object.entries(e||{}).forEach((([e,n])=>{s[n]=t,t in r||(r[t]=[])}))},abort:function(){f._aborted=!0,Object.keys(f.backlog).forEach((e=>{delete f.backlog[e]}))},isBuffering:function(e){return!!v()[s[e]]},debugId:r,backlog:l?{}:t&&"object"==typeof t.backlog?t.backlog:{},isolatedBacklog:l};return Object.defineProperty(f,"aborted",{get:()=>{let e=f._aborted||!1;return e||(t&&(e=t.aborted),e)}}),f;function g(e){return e&&e instanceof a.y?e:e?(0,i.I)(e,o,(()=>new a.y(o))):new a.y(o)}function p(e,t){n[e]=m(e).concat(t)}function m(e){return n[e]||[]}function h(t){return d[t]=d[t]||e(f,t)}function v(){return f.backlog}}(void 0,"globalEE"),u=(0,n.Zm)();u.ee||(u.ee=c)},646:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});class n{constructor(e){this.contextId=e}}},908:(e,t,r)=>{"use strict";r.d(t,{d:()=>n,p:()=>i});var n=r(836).ee.get("handle");function i(e,t,r,i,a){a?(a.buffer([e],i),a.emit(e,t,r)):(n.buffer([e],i),n.emit(e,t,r))}},606:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(908);a.on=s;var i=a.handlers={};function a(e,t,r,a){s(a||n.d,i,e,t,r)}function s(e,t,r,i,a){a||(a="feature"),e||(e=n.d);var s=t[a]=t[a]||{};(s[r]=s[r]||[]).push([e,i])}},878:(e,t,r)=>{"use strict";function n(e,t){return{capture:e,passive:!1,signal:t}}function i(e,t,r=!1,i){window.addEventListener(e,t,n(r,i))}function a(e,t,r=!1,i){document.addEventListener(e,t,n(r,i))}r.d(t,{DD:()=>a,jT:()=>n,sp:()=>i})},607:(e,t,r)=>{"use strict";r.d(t,{W:()=>n});const n=(0,r(566).bz)()},566:(e,t,r)=>{"use strict";r.d(t,{LA:()=>o,bz:()=>s});var n=r(154);const i="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";function a(e,t){return e?15&e[t]:16*Math.random()|0}function s(){const e=n.gm?.crypto||n.gm?.msCrypto;let t,r=0;return e&&e.getRandomValues&&(t=e.getRandomValues(new Uint8Array(30))),i.split("").map((e=>"x"===e?a(t,r++).toString(16):"y"===e?(3&a()|8).toString(16):e)).join("")}function o(e){const t=n.gm?.crypto||n.gm?.msCrypto;let r,i=0;t&&t.getRandomValues&&(r=t.getRandomValues(new Uint8Array(e)));const s=[];for(var o=0;o<e;o++)s.push(a(r,i++).toString(16));return s.join("")}},614:(e,t,r)=>{"use strict";r.d(t,{BB:()=>s,H3:()=>n,g:()=>u,iL:()=>c,tS:()=>o,uh:()=>i,wk:()=>a});const n="NRBA",i="SESSION",a=144e5,s=18e5,o={STARTED:"session-started",PAUSE:"session-pause",RESET:"session-reset",RESUME:"session-resume",UPDATE:"session-update"},c={SAME_TAB:"same-tab",CROSS_TAB:"cross-tab"},u={OFF:0,FULL:1,ERROR:2}},863:(e,t,r)=>{"use strict";function n(){return Math.floor(performance.now())}r.d(t,{t:()=>n})},944:(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var n=r(241);function i(e,t){"function"==typeof console.debug&&(console.debug("New Relic Warning: https://github.com/newrelic/newrelic-browser-agent/blob/main/docs/warning-codes.md#".concat(e),t),(0,n.W)({agentIdentifier:null,drained:null,type:"data",name:"warn",feature:"warn",data:{code:e,secondary:t}}))}},701:(e,t,r)=>{"use strict";r.d(t,{B:()=>a,t:()=>s});var n=r(241);const i=new Set,a={};function s(e,t){const r=t.agentIdentifier;a[r]??={},e&&"object"==typeof e&&(i.has(r)||(t.ee.emit("rumresp",[e]),a[r]=e,i.add(r),(0,n.W)({agentIdentifier:r,loaded:!0,drained:!0,type:"lifecycle",name:"load",feature:void 0,data:e})))}},990:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var n=Object.prototype.hasOwnProperty;function i(e,t,r){if(n.call(e,t))return e[t];var i=r();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(e,t,{value:i,writable:!0,enumerable:!1}),i}catch(e){}return e[t]=i,i}},389:(e,t,r)=>{"use strict";function n(e,t=500,r={}){const n=r?.leading||!1;let i;return(...r)=>{n&&void 0===i&&(e.apply(this,r),i=setTimeout((()=>{i=clearTimeout(i)}),t)),n||(clearTimeout(i),i=setTimeout((()=>{e.apply(this,r)}),t))}}function i(e){let t=!1;return(...r)=>{t||(t=!0,e.apply(this,r))}}r.d(t,{J:()=>i,s:()=>n})},910:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(944);const i=new Map;function a(...e){return e.every((e=>{if(i.has(e))return i.get(e);const t="function"==typeof e&&e.toString().includes("[native code]");return t||(0,n.R)(64,e?.name||e?.toString()),i.set(e,t),t}))}},289:(e,t,r)=>{"use strict";r.d(t,{GG:()=>a,Qr:()=>o,sB:()=>s});var n=r(878);function i(){return"undefined"==typeof document||"complete"===document.readyState}function a(e,t){if(i())return e();(0,n.sp)("load",e,t)}function s(e){if(i())return e();(0,n.DD)("DOMContentLoaded",e)}function o(e){if(i())return e();(0,n.sp)("popstate",e)}},384:(e,t,r)=>{"use strict";r.d(t,{NT:()=>s,US:()=>d,Zm:()=>o,bQ:()=>u,dV:()=>c,pV:()=>l});var n=r(154),i=r(863),a=r(910);const s={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net"};function o(){return n.gm.NREUM||(n.gm.NREUM={}),void 0===n.gm.newrelic&&(n.gm.newrelic=n.gm.NREUM),n.gm.NREUM}function c(){let e=o();return e.o||(e.o={ST:n.gm.setTimeout,SI:n.gm.setImmediate||n.gm.setInterval,CT:n.gm.clearTimeout,XHR:n.gm.XMLHttpRequest,REQ:n.gm.Request,EV:n.gm.Event,PR:n.gm.Promise,MO:n.gm.MutationObserver,FETCH:n.gm.fetch,WS:n.gm.WebSocket},(0,a.i)(...Object.values(e.o))),e}function u(e,t){let r=o();r.initializedAgents??={},t.initializedAt={ms:(0,i.t)(),date:new Date},r.initializedAgents[e]=t}function d(e,t){o()[e]=t}function l(){return function(){let e=o();const t=e.info||{};e.info={beacon:s.beacon,errorBeacon:s.errorBeacon,...t}}(),function(){let e=o();const t=e.init||{};e.init={...t}}(),c(),function(){let e=o();const t=e.loader_config||{};e.loader_config={...t}}(),o()}},843:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});var n=r(878);function i(e,t=!1,r,i){(0,n.DD)("visibilitychange",(function(){if(t)return void("hidden"===document.visibilityState&&e());e(document.visibilityState)}),r,i)}},773:(e,t,r)=>{"use strict";r.d(t,{z_:()=>a,XG:()=>o,TZ:()=>n,rs:()=>i,xV:()=>s});r(154),r(566),r(384);const n=r(860).K7.metrics,i="sm",a="cm",s="storeSupportabilityMetrics",o="storeEventMetrics"},630:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewEvent},782:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewTiming},234:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});var n=r(836),i=r(687);class a{constructor(e,t){this.agentIdentifier=e,this.ee=n.ee.get(e),this.featureName=t,this.blocked=!1}deregisterDrain(){(0,i.x3)(this.agentIdentifier,this.featureName)}}},741:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});var n=r(944),i=r(261);class a{#e(e,...t){if(this[e]!==a.prototype[e])return this[e](...t);(0,n.R)(35,e)}addPageAction(e,t){return this.#e(i.hG,e,t)}register(e){return this.#e(i.eY,e)}recordCustomEvent(e,t){return this.#e(i.fF,e,t)}setPageViewName(e,t){return this.#e(i.Fw,e,t)}setCustomAttribute(e,t,r){return this.#e(i.cD,e,t,r)}noticeError(e,t){return this.#e(i.o5,e,t)}setUserId(e){return this.#e(i.Dl,e)}setApplicationVersion(e){return this.#e(i.nb,e)}setErrorHandler(e){return this.#e(i.bt,e)}addRelease(e,t){return this.#e(i.k6,e,t)}log(e,t){return this.#e(i.$9,e,t)}start(){return this.#e(i.d3)}finished(e){return this.#e(i.BL,e)}recordReplay(){return this.#e(i.CH)}pauseReplay(){return this.#e(i.Tb)}addToTrace(e){return this.#e(i.U2,e)}setCurrentRouteName(e){return this.#e(i.PA,e)}interaction(){return this.#e(i.dT)}wrapLogger(e,t,r){return this.#e(i.Wb,e,t,r)}measure(e,t){return this.#e(i.V1,e,t)}}},261:(e,t,r)=>{"use strict";r.d(t,{$9:()=>u,BL:()=>o,CH:()=>g,Dl:()=>_,Fw:()=>y,PA:()=>h,Pl:()=>n,Tb:()=>l,U2:()=>a,V1:()=>k,Wb:()=>x,bt:()=>b,cD:()=>v,d3:()=>w,dT:()=>c,eY:()=>p,fF:()=>f,hG:()=>i,k6:()=>s,nb:()=>m,o5:()=>d});const n="api-",i="addPageAction",a="addToTrace",s="addRelease",o="finished",c="interaction",u="log",d="noticeError",l="pauseReplay",f="recordCustomEvent",g="recordReplay",p="register",m="setApplicationVersion",h="setCurrentRouteName",v="setCustomAttribute",b="setErrorHandler",y="setPageViewName",_="setUserId",w="start",x="wrapLogger",k="measure"},163:(e,t,r)=>{"use strict";r.d(t,{j:()=>E});var n=r(384),i=r(741);var a=r(555);r(860).K7.genericEvents;const s="experimental.marks",o="experimental.measures",c="experimental.resources",u=e=>{if(!e||"string"!=typeof e)return!1;try{document.createDocumentFragment().querySelector(e)}catch{return!1}return!0};var d=r(614),l=r(944),f=r(122);const g="[data-nr-mask]",p=e=>(0,f.a)(e,(()=>{const e={feature_flags:[],experimental:{marks:!1,measures:!1,resources:!1},mask_selector:"*",block_selector:"[data-nr-block]",mask_input_options:{color:!1,date:!1,"datetime-local":!1,email:!1,month:!1,number:!1,range:!1,search:!1,tel:!1,text:!1,time:!1,url:!1,week:!1,textarea:!1,select:!1,password:!0}};return{ajax:{deny_list:void 0,block_internal:!0,enabled:!0,autoStart:!0},api:{allow_registered_children:!0,duplicate_registered_data:!1},distributed_tracing:{enabled:void 0,exclude_newrelic_header:void 0,cors_use_newrelic_header:void 0,cors_use_tracecontext_headers:void 0,allowed_origins:void 0},get feature_flags(){return e.feature_flags},set feature_flags(t){e.feature_flags=t},generic_events:{enabled:!0,autoStart:!0},harvest:{interval:30},jserrors:{enabled:!0,autoStart:!0},logging:{enabled:!0,autoStart:!0},metrics:{enabled:!0,autoStart:!0},obfuscate:void 0,page_action:{enabled:!0},page_view_event:{enabled:!0,autoStart:!0},page_view_timing:{enabled:!0,autoStart:!0},performance:{get capture_marks(){return e.feature_flags.includes(s)||e.experimental.marks},set capture_marks(t){e.experimental.marks=t},get capture_measures(){return e.feature_flags.includes(o)||e.experimental.measures},set capture_measures(t){e.experimental.measures=t},capture_detail:!0,resources:{get enabled(){return e.feature_flags.includes(c)||e.experimental.resources},set enabled(t){e.experimental.resources=t},asset_types:[],first_party_domains:[],ignore_newrelic:!0}},privacy:{cookies_enabled:!0},proxy:{assets:void 0,beacon:void 0},session:{expiresMs:d.wk,inactiveMs:d.BB},session_replay:{autoStart:!0,enabled:!1,preload:!1,sampling_rate:10,error_sampling_rate:100,collect_fonts:!1,inline_images:!1,fix_stylesheets:!0,mask_all_inputs:!0,get mask_text_selector(){return e.mask_selector},set mask_text_selector(t){u(t)?e.mask_selector="".concat(t,",").concat(g):""===t||null===t?e.mask_selector=g:(0,l.R)(5,t)},get block_class(){return"nr-block"},get ignore_class(){return"nr-ignore"},get mask_text_class(){return"nr-mask"},get block_selector(){return e.block_selector},set block_selector(t){u(t)?e.block_selector+=",".concat(t):""!==t&&(0,l.R)(6,t)},get mask_input_options(){return e.mask_input_options},set mask_input_options(t){t&&"object"==typeof t?e.mask_input_options={...t,password:!0}:(0,l.R)(7,t)}},session_trace:{enabled:!0,autoStart:!0},soft_navigations:{enabled:!0,autoStart:!0},spa:{enabled:!0,autoStart:!0},ssl:void 0,user_actions:{enabled:!0,elementAttributes:["id","className","tagName","type"]}}})());var m=r(154),h=r(324);let v=0;const b={buildEnv:h.F3,distMethod:h.Xs,version:h.xv,originTime:m.WN},y={appMetadata:{},customTransaction:void 0,denyList:void 0,disabled:!1,entityManager:void 0,harvester:void 0,isolatedBacklog:!1,isRecording:!1,loaderType:void 0,maxBytes:3e4,obfuscator:void 0,onerror:void 0,ptid:void 0,releaseIds:{},session:void 0,timeKeeper:void 0,jsAttributesMetadata:{bytes:0},get harvestCount(){return++v}},_=e=>{const t=(0,f.a)(e,y),r=Object.keys(b).reduce(((e,t)=>(e[t]={value:b[t],writable:!1,configurable:!0,enumerable:!0},e)),{});return Object.defineProperties(t,r)};var w=r(701);const x=e=>{const t=e.startsWith("http");e+="/",r.p=t?e:"https://"+e};var k=r(836),A=r(241);const S={accountID:void 0,trustKey:void 0,agentID:void 0,licenseKey:void 0,applicationID:void 0,xpid:void 0},T=e=>(0,f.a)(e,S),R=new Set;function E(e,t={},r,s){let{init:o,info:c,loader_config:u,runtime:d={},exposed:l=!0}=t;if(!c){const e=(0,n.pV)();o=e.init,c=e.info,u=e.loader_config}e.init=p(o||{}),e.loader_config=T(u||{}),c.jsAttributes??={},m.bv&&(c.jsAttributes.isWorker=!0),e.info=(0,a.D)(c);const f=e.init,g=[c.beacon,c.errorBeacon];R.has(e.agentIdentifier)||(f.proxy.assets&&(x(f.proxy.assets),g.push(f.proxy.assets)),f.proxy.beacon&&g.push(f.proxy.beacon),function(e){const t=(0,n.pV)();Object.getOwnPropertyNames(i.W.prototype).forEach((r=>{const n=i.W.prototype[r];if("function"!=typeof n||"constructor"===n)return;let a=t[r];e[r]&&!1!==e.exposed&&"micro-agent"!==e.runtime?.loaderType&&(t[r]=(...t)=>{const n=e[r](...t);return a?a(...t):n})}))}(e),(0,n.US)("activatedFeatures",w.B),e.runSoftNavOverSpa&&=!0===f.soft_navigations.enabled&&f.feature_flags.includes("soft_nav")),d.denyList=[...f.ajax.deny_list||[],...f.ajax.block_internal?g:[]],d.ptid=e.agentIdentifier,d.loaderType=r,e.runtime=_(d),R.has(e.agentIdentifier)||(e.ee=k.ee.get(e.agentIdentifier),e.exposed=l,(0,A.W)({agentIdentifier:e.agentIdentifier,drained:!!w.B?.[e.agentIdentifier],type:"lifecycle",name:"initialize",feature:void 0,data:e.config})),R.add(e.agentIdentifier)}},374:(e,t,r)=>{r.nc=(()=>{try{return document?.currentScript?.nonce}catch(e){}return""})()},860:(e,t,r)=>{"use strict";r.d(t,{$J:()=>d,K7:()=>c,P3:()=>u,XX:()=>i,Yy:()=>o,df:()=>a,qY:()=>n,v4:()=>s});const n="events",i="jserrors",a="browser/blobs",s="rum",o="browser/logs",c={ajax:"ajax",genericEvents:"generic_events",jserrors:i,logging:"logging",metrics:"metrics",pageAction:"page_action",pageViewEvent:"page_view_event",pageViewTiming:"page_view_timing",sessionReplay:"session_replay",sessionTrace:"session_trace",softNav:"soft_navigations",spa:"spa"},u={[c.pageViewEvent]:1,[c.pageViewTiming]:2,[c.metrics]:3,[c.jserrors]:4,[c.spa]:5,[c.ajax]:6,[c.sessionTrace]:7,[c.softNav]:8,[c.sessionReplay]:9,[c.logging]:10,[c.genericEvents]:11},d={[c.pageViewEvent]:s,[c.pageViewTiming]:n,[c.ajax]:n,[c.spa]:n,[c.softNav]:n,[c.metrics]:i,[c.jserrors]:i,[c.sessionTrace]:a,[c.sessionReplay]:a,[c.logging]:o,[c.genericEvents]:"ins"}}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var a=n[e]={exports:{}};return r[e](a,a.exports,i),a.exports}i.m=r,i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,r)=>(i.f[r](e,t),t)),[])),i.u=e=>"nr-rum-1.295.0.min.js",i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="NRBA-1.295.0.PROD:",i.l=(r,n,a,s)=>{if(e[r])e[r].push(n);else{var o,c;if(void 0!==a)for(var u=document.getElementsByTagName("script"),d=0;d<u.length;d++){var l=u[d];if(l.getAttribute("src")==r||l.getAttribute("data-webpack")==t+a){o=l;break}}if(!o){c=!0;var f={296:"sha512-5mjLs4V+vqmVCMY5tMI4S9zuik2ZQGpHyNyS3JjzoMIc9MrdQiFKTHsXuapkHWyDEDo06Z3nazEbjwKKn3drdA=="};(o=document.createElement("script")).charset="utf-8",o.timeout=120,i.nc&&o.setAttribute("nonce",i.nc),o.setAttribute("data-webpack",t+a),o.src=r,0!==o.src.indexOf(window.location.origin+"/")&&(o.crossOrigin="anonymous"),f[s]&&(o.integrity=f[s])}e[r]=[n];var g=(t,n)=>{o.onerror=o.onload=null,clearTimeout(p);var i=e[r];if(delete e[r],o.parentNode&&o.parentNode.removeChild(o),i&&i.forEach((e=>e(n))),t)return t(n)},p=setTimeout(g.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=g.bind(null,o.onerror),o.onload=g.bind(null,o.onload),c&&document.head.appendChild(o)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.p="https://js-agent.newrelic.com/",(()=>{var e={374:0,840:0};i.f.j=(t,r)=>{var n=i.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var a=new Promise(((r,i)=>n=e[t]=[r,i]));r.push(n[2]=a);var s=i.p+i.u(t),o=new Error;i.l(s,(r=>{if(i.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var a=r&&("load"===r.type?"missing":r.type),s=r&&r.target&&r.target.src;o.message="Loading chunk "+t+" failed.\n("+a+": "+s+")",o.name="ChunkLoadError",o.type=a,o.request=s,n[1](o)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,a,[s,o,c]=r,u=0;if(s.some((t=>0!==e[t]))){for(n in o)i.o(o,n)&&(i.m[n]=o[n]);if(c)c(i)}for(t&&t(r);u<s.length;u++)a=s[u],i.o(e,a)&&e[a]&&e[a][0](),e[a]=0},r=self["webpackChunk:NRBA-1.295.0.PROD"]=self["webpackChunk:NRBA-1.295.0.PROD"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";i(374);var e=i(566),t=i(741);class r extends t.W{agentIdentifier=(0,e.LA)(16)}var n=i(860);const a=Object.values(n.K7);var s=i(163);var o=i(908),c=i(863),u=i(261),d=i(241),l=i(944),f=i(701),g=i(773);function p(e,t,i,a){const s=a||i;!s||s[e]&&s[e]!==r.prototype[e]||(s[e]=function(){(0,o.p)(g.xV,["API/"+e+"/called"],void 0,n.K7.metrics,i.ee),(0,d.W)({agentIdentifier:i.agentIdentifier,drained:!!f.B?.[i.agentIdentifier],type:"data",name:"api",feature:u.Pl+e,data:{}});try{return t.apply(this,arguments)}catch(e){(0,l.R)(23,e)}})}function m(e,t,r,n,i){const a=e.info;null===r?delete a.jsAttributes[t]:a.jsAttributes[t]=r,(i||null===r)&&(0,o.p)(u.Pl+n,[(0,c.t)(),t,r],void 0,"session",e.ee)}var h=i(687),v=i(234),b=i(289),y=i(154),_=i(384);const w=e=>y.RI&&!0===e?.privacy.cookies_enabled;function x(e){return!!(0,_.dV)().o.MO&&w(e)&&!0===e?.session_trace.enabled}var k=i(389);class A extends v.W{constructor(e,t){super(e.agentIdentifier,t),this.abortHandler=void 0,this.featAggregate=void 0,this.onAggregateImported=void 0,this.deferred=Promise.resolve(),!1===e.init[this.featureName].autoStart?this.deferred=new Promise(((t,r)=>{this.ee.on("manual-start-all",(0,k.J)((()=>{(0,h.Ak)(e.agentIdentifier,this.featureName),t()})))})):(0,h.Ak)(e.agentIdentifier,t)}importAggregator(e,t,r={}){if(this.featAggregate)return;let a;this.onAggregateImported=new Promise((e=>{a=e}));const s=async()=>{let s;await this.deferred;try{if(w(e.init)){const{setupAgentSession:t}=await i.e(296).then(i.bind(i,305));s=t(e)}}catch(e){(0,l.R)(20,e),this.ee.emit("internal-error",[e]),this.featureName===n.K7.sessionReplay&&this.abortHandler?.()}try{if(!this.#t(this.featureName,s,e.init))return(0,h.Ze)(this.agentIdentifier,this.featureName),void a(!1);const{Aggregate:n}=await t();this.featAggregate=new n(e,r),e.runtime.harvester.initializedAggregates.push(this.featAggregate),a(!0)}catch(e){(0,l.R)(34,e),this.abortHandler?.(),(0,h.Ze)(this.agentIdentifier,this.featureName,!0),a(!1),this.ee&&this.ee.abort()}};y.RI?(0,b.GG)((()=>s()),!0):s()}#t(e,t,r){switch(e){case n.K7.sessionReplay:return x(r)&&!!t;case n.K7.sessionTrace:return!!t;default:return!0}}}var S=i(630),T=i(614);class R extends A{static featureName=S.T;constructor(e){var t;super(e,S.T),this.setupInspectionEvents(e.agentIdentifier),t=e,p(u.Fw,(function(e,r){"string"==typeof e&&("/"!==e.charAt(0)&&(e="/"+e),t.runtime.customTransaction=(r||"http://custom.transaction")+e,(0,o.p)(u.Pl+u.Fw,[(0,c.t)()],void 0,void 0,t.ee))}),t),this.ee.on("api-send-rum",((e,t)=>(0,o.p)("send-rum",[e,t],void 0,this.featureName,this.ee))),this.importAggregator(e,(()=>i.e(296).then(i.bind(i,108))))}setupInspectionEvents(e){const t=(t,r)=>{t&&(0,d.W)({agentIdentifier:e,timeStamp:t.timeStamp,loaded:"complete"===t.target.readyState,type:"window",name:r,data:t.target.location+""})};(0,b.sB)((e=>{t(e,"DOMContentLoaded")})),(0,b.GG)((e=>{t(e,"load")})),(0,b.Qr)((e=>{t(e,"navigate")})),this.ee.on(T.tS.UPDATE,((t,r)=>{(0,d.W)({agentIdentifier:e,type:"lifecycle",name:"session",data:r})}))}}var E=i(843),N=i(878),j=i(782);class I extends A{static featureName=j.T;constructor(e){super(e,j.T),y.RI&&((0,E.u)((()=>(0,o.p)("docHidden",[(0,c.t)()],void 0,j.T,this.ee)),!0),(0,N.sp)("pagehide",(()=>(0,o.p)("winPagehide",[(0,c.t)()],void 0,j.T,this.ee))),this.importAggregator(e,(()=>i.e(296).then(i.bind(i,350)))))}}class O extends A{static featureName=g.TZ;constructor(e){super(e,g.TZ),y.RI&&document.addEventListener("securitypolicyviolation",(e=>{(0,o.p)(g.xV,["Generic/CSPViolation/Detected"],void 0,this.featureName,this.ee)})),this.importAggregator(e,(()=>i.e(296).then(i.bind(i,373))))}}new class extends r{constructor(e){var t;(super(),y.gm)?(this.features={},(0,_.bQ)(this.agentIdentifier,this),this.desiredFeatures=new Set(e.features||[]),this.desiredFeatures.add(R),this.runSoftNavOverSpa=[...this.desiredFeatures].some((e=>e.featureName===n.K7.softNav)),(0,s.j)(this,e,e.loaderType||"agent"),t=this,p(u.cD,(function(e,r,n=!1){if("string"==typeof e){if(["string","number","boolean"].includes(typeof r)||null===r)return m(t,e,r,u.cD,n);(0,l.R)(40,typeof r)}else(0,l.R)(39,typeof e)}),t),function(e){p(u.Dl,(function(t){if("string"==typeof t||null===t)return m(e,"enduser.id",t,u.Dl,!0);(0,l.R)(41,typeof t)}),e)}(this),function(e){p(u.nb,(function(t){if("string"==typeof t||null===t)return m(e,"application.version",t,u.nb,!1);(0,l.R)(42,typeof t)}),e)}(this),function(e){p(u.d3,(function(){e.ee.emit("manual-start-all")}),e)}(this),this.run()):(0,l.R)(21)}get config(){return{info:this.info,init:this.init,loader_config:this.loader_config,runtime:this.runtime}}get api(){return this}run(){try{const e=function(e){const t={};return a.forEach((r=>{t[r]=!!e[r]?.enabled})),t}(this.init),t=[...this.desiredFeatures];t.sort(((e,t)=>n.P3[e.featureName]-n.P3[t.featureName])),t.forEach((t=>{if(!e[t.featureName]&&t.featureName!==n.K7.pageViewEvent)return;if(this.runSoftNavOverSpa&&t.featureName===n.K7.spa)return;if(!this.runSoftNavOverSpa&&t.featureName===n.K7.softNav)return;const r=function(e){switch(e){case n.K7.ajax:return[n.K7.jserrors];case n.K7.sessionTrace:return[n.K7.ajax,n.K7.pageViewEvent];case n.K7.sessionReplay:return[n.K7.sessionTrace];case n.K7.pageViewTiming:return[n.K7.pageViewEvent];default:return[]}}(t.featureName).filter((e=>!(e in this.features)));r.length>0&&(0,l.R)(36,{targetFeature:t.featureName,missingDependencies:r}),this.features[t.featureName]=new t(this)}))}catch(e){(0,l.R)(22,e);for(const e in this.features)this.features[e].abortHandler?.();const t=(0,_.Zm)();delete t.initializedAgents[this.agentIdentifier]?.features,delete this.sharedAggregator;return t.ee.get(this.agentIdentifier).abort(),!1}}}({features:[R,I,O],loaderType:"lite"})})()})();</script><script type="text/javascript">window.NREUM||(NREUM={});NREUM.info={"beacon":"bam.nr-data.net","queueTime":2,"licenseKey":"40b3e098a7","agent":"","transactionName":"NVYAbUpQVhIDW0JeWwwcJExWUkwIDVYZREQLUAdKTF5KBExbWVpZDV1MT1FUTxJYVFdZUAtdBWZIUF8E","applicationID":"29832197","errorBeacon":"bam.nr-data.net","applicationTime":33}</script>
    <style>.async-hide { opacity: 0 !important} </style>

    <!-- <script type="text/javascript" src="https://www.datadoghq-browser-agent.com/datadog-logs-v4.js"></script> -->
    <!--
    <script>
      window.DD_LOGS &&
        DD_LOGS.init({
          clientToken: 'pub45617fffc211c4500025c8335e62dfff',
          site: 'datadoghq.com',
          forwardErrorsToLogs: true,
          sampleRate: 100,
          service: 'SnapEDA'
        })
    </script>
    -->
    <script>(function(a,s,y,n,c,h,i,d,e){s.className+=' '+y;h.start=1*new Date;
    h.end=i=function(){s.className=s.className.replace(RegExp(' ?'+y),'')};
    (a[n]=a[n]||[]).hide=h;setTimeout(function(){i();h.end=null},c);h.timeout=c;
    })(window,document.documentElement,'async-hide','dataLayer',4000,
    {'GTM-WPWKB7R':true});</script>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-N2DMH7J');</script>
    <!-- End Google Tag Manager -->
    <script>
      (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
      ga('create', '***********-1', 'auto');
      ga('require', 'GTM-WPWKB7R');
      ga('send', 'pageview');
    </script>
        <script>
            !function(){var analytics=window.analytics=window.analytics||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else{analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","once","off","on","addSourceMiddleware","addIntegrationMiddleware","setAnonymousId","addDestinationMiddleware"];analytics.factory=function(e){return function(){var t=Array.prototype.slice.call(arguments);t.unshift(e);analytics.push(t);return analytics}};for(var e=0;e<analytics.methods.length;e++){var key=analytics.methods[e];analytics[key]=analytics.factory(key)}analytics.load=function(key,e){var t=document.createElement("script");t.type="text/javascript";t.async=!0;t.src="https://cdn.segment.com/analytics.js/v1/" + key + "/analytics.min.js";var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n);analytics._loadOptions=e};analytics._writeKey="R33QdDxhZ5YzaUts0oFQgxD5FNZqV3TG";;analytics.SNIPPET_VERSION="4.15.3";
            analytics.load("R33QdDxhZ5YzaUts0oFQgxD5FNZqV3TG");
            analytics.page();
            }}();
            </script>
                <!-- Start VWO Async SmartCode -->
    <script type='text/javascript'>
        window._vwo_code = window._vwo_code || (function(){
        var account_id=612614,
        settings_tolerance=2000,
        library_tolerance=2500,
        use_existing_jquery=false,
        is_spa=1,
        hide_element='body',
        
        
        f=false,d=document,code={use_existing_jquery:function(){return use_existing_jquery;},library_tolerance:function(){return library_tolerance;},finish:function(){if(!f){f=true;var a=d.getElementById('_vis_opt_path_hides');if(a)a.parentNode.removeChild(a);}},finished:function(){return f;},load:function(a){var b=d.createElement('script');b.src=a;b.type='text/javascript';b.innerText;b.onerror=function(){_vwo_code.finish();};d.getElementsByTagName('head')[0].appendChild(b);},init:function(){
        window.settings_timer=setTimeout(function () {_vwo_code.finish() },settings_tolerance);var a=d.createElement('style'),b=hide_element?hide_element+'{opacity:0 !important;filter:alpha(opacity=0) !important;background:none !important;}':'',h=d.getElementsByTagName('head')[0];a.setAttribute('id','_vis_opt_path_hides');a.setAttribute('type','text/css');if(a.styleSheet)a.styleSheet.cssText=b;else a.appendChild(d.createTextNode(b));h.appendChild(a);this.load('https://dev.visualwebsiteoptimizer.com/j.php?a='+account_id+'&u='+encodeURIComponent(d.URL)+'&f='+(+is_spa)+'&r='+Math.random());return settings_timer; }};window._vwo_settings_timer = code.init(); return code; }());
        </script>
        <!-- End VWO Async SmartCode -->
    <!-- Standard Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/img/icons/SnapEDA.ico"/>
    <!-- Google+ publisher -->
    <link href="https://plus.google.com/105383266825551010805" rel="publisher">

    <title>SnapMagic Search | Free PCB Footprints and Schematic Symbols</title>
    
<meta name="description" content="Design faster with SnapMagic Search. Download CAD models for millions of electronic components, including schematic symbols, PCB footprints, Eagle library, OrCAD library, KiCAD library, pinouts, datasheets, and 3D models.">
<meta name="keywords" content="PCB Footprint, PCB Libraries, SnapMagic Search, Footprint Library, Eagle library, OrCAD library, KiCAD library, schematic symbols, 3D part models, ECAD, electronic footprint, electronic symbol, pinouts, datasheet, CAD Libraries, download footprints, download symbols, download 3D models">
<meta property='og:title' content="Let's make your electronic design a snap."/>
<meta property='og:image' content="/static/img/SnapMagic-google-search.png"/>
<meta property='og:description' content="Design faster with SnapMagic Search. Download CAD models for millions of electronic components, including schematic symbols, PCB footprints, Eagle library, OrCAD library, KiCAD library and 3D models."/>
<meta property='og:url' content='https://www.snapeda.com' />
<link rel="canonical" href="https://www.snapeda.com"/>

<style>
	#header-left,
	#header-left a,
	.btn {}

	.navbar .nav>li>a,
	.s-btn,
	a {
		text-decoration: none
	}

	.container::after,
	.row-fluid::after {
		clear: both
	}

	a,
	body,
	div,
	form,
	h1,
	h2,
	h3,
	header,
	html,
	i,
	img,
	li,
	nav,
	p,
	section,
	span,
	ul {
		margin: 0;
		padding: 0;
		border: 0;
		font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
		font-size: inherit;
		font-style: inherit;
		font-variant: inherit;
		font-weight: inherit;
		line-height: inherit;
		vertical-align: baseline
	}

	h1,
	h2 {
		line-height: 36px
	}

	body,
	button,
	input,
	li,
	select {
		line-height: 18px
	}

	header,
	nav,
	section {
		display: block
	}

	html {
		font-size: 100%
	}

	img {
		max-width: 100%;
		vertical-align: middle;
		border: 0
	}

	button,
	input,
	select {
		margin: 0;
		vertical-align: middle
	}

	body {
		overflow-x: hidden;
		margin: 0;
		font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
		font-size: 13px;
		color: #333;
		background-color: #fff
	}

	a {
		color: #08c
	}

	[class*=span] {
		float: left;
		margin-left: 20px
	}

	.container {
		width: 940px
	}

	.span10 {
		width: 780px
	}

	.span3 {
		width: 220px
	}

	.span2 {
		width: 140px
	}

	.row-fluid {
		width: 100%
	}

	.row-fluid::after,
	.row-fluid::before {
		display: table;
		content: ''
	}

	.row-fluid [class*=span] {
		display: block;
		width: 100%;
		min-height: 28px;
		box-sizing: border-box;
		float: left;
		margin-left: 2.127659574%
	}

	.row-fluid [class*=span]:first-child {
		margin-left: 0
	}

	.row-fluid .span10 {
		width: 82.97872339599999%
	}

	.row-fluid .span3 {
		width: 23.404255317%
	}

	.row-fluid .span2 {
		width: 14.89361702%
	}

	.container {
		margin-right: auto;
		margin-left: auto
	}

	.container::after,
	.container::before {
		display: table;
		content: ''
	}

	p {
		margin: 0 0 9px
	}

	h1,
	h2,
	h3 {
		margin: 0;
		font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
		font-weight: 700;
		color: inherit;
		text-rendering: optimizelegibility
	}

	h1 {
		font-size: 30px
	}

	h2 {
		font-size: 24px
	}

	h3 {
		font-size: 18px;
		line-height: 27px
	}

	ul {
		padding: 0;
		margin: 0 0 9px 25px;
		list-style: disc
	}

	form {
		margin: 0 0 18px
	}

	.nav,
	input {
		margin-left: 0
	}

	button,
	input,
	select {
		font-size: 13px;
		font-weight: 400;
		font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
	}

	#s-navbar,
	.landing {
		font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
	}

	input[type=text],
	select {
		display: inline-block;
		height: 18px;
		padding: 4px;
		margin-bottom: 9px;
		font-size: 13px;
		line-height: 18px;
		color: #555
	}

	input[type=text] {
		background-color: #fff;
		border: 1px solid #ccc;
		-webkit-box-shadow: rgba(0, 0, 0, .0745098) 0 1px 1px inset;
		box-shadow: rgba(0, 0, 0, .0745098) 0 1px 1px inset;
		border-radius: 3px
	}

	select {
		height: 28px;
		line-height: 28px;
		width: 220px;
		border: 1px solid #bbb
	}

	::-webkit-input-placeholder {
		color: #999
	}

	.search-query {
		padding-right: 14px;
		padding-left: 14px;
		margin-bottom: 0;
		border-radius: 14px
	}

	.make-snap-detail-section .snap-card{
		cursor: pointer;
	}

	[class^=icon-] {
		display: inline-block;
		width: 14px;
		height: 14px;
		line-height: 14px;
		vertical-align: text-top;
		background-position: 14px 14px;
		background-repeat: no-repeat no-repeat
	}

	.btn {
		display: inline-block;
		padding: 4px 10px;
		margin-bottom: 0;
		font-size: 13px;
		line-height: 18px;
		color: #333;
		text-shadow: rgba(255, 255, 255, .74902) 0 1px 1px;
		vertical-align: middle;
		background-color: #f5f5f5;
		background-image: -webkit-linear-gradient(top, #fff, #e6e6e6);
		border-width: 1px;
		border-style: solid;
		border-color: #ccc #ccc #b3b3b3;
		-webkit-box-shadow: rgba(255, 255, 255, .2) 0 1px 0 inset, rgba(0, 0, 0, .0470588) 0 1px 2px;
		box-shadow: rgba(255, 255, 255, .2) 0 1px 0 inset, rgba(0, 0, 0, .0470588) 0 1px 2px;
		background-repeat: repeat no-repeat;
		border-radius: 4px;
		border-color: rgba(0, 0, 0, .0980392) rgba(0, 0, 0, .0980392) rgba(0, 0, 0, .247059)
	}

	.nav>li>a,
	.navbar .nav {
		display: block
	}

	.nav {
		margin-bottom: 18px;
		list-style: none
	}

	.navbar {
		overflow: visible;
		color: #fff
	}

	.navbar-innerhome {
		min-height: 65px;
		padding-left: 20px;
		padding-right: 20px;
		-webkit-box-shadow: rgba(0, 0, 0, .247059) 0 1px 3px, rgba(0, 0, 0, .0980392) 0 -1px 0 inset
	}

	.navbar .container {
		width: auto
	}

	.navbar .nav {
		position: relative;
		left: 0;
		float: left;
		margin: 0 10px 0 0
	}

	.navbar .nav.pull-right {
		float: right
	}

	.navbar .nav>li {
		display: block;
		float: left
	}

	.navbar .nav>li>a {
		float: none;
		padding: 21.5px 10px 0;
		line-height: 12px;
		color: #5a5a5a;
		font-size: 14px
	}

	.navbar .btn {
		display: inline-block;
		padding: 4px 10px;
		margin: 17.5px 5px 18.5px;
		line-height: 18px
	}

	.navbar .nav.pull-right {
		margin-left: 10px;
		margin-right: 0
	}

	.navbar .btn-navbar {
		display: none;
		float: right;
		padding: 7px 10px;
		margin-left: 5px;
		margin-right: 5px;
		background-color: #424242;
		background-image: -webkit-linear-gradient(top, #4d4d4d, #333);
		border-color: rgba(0, 0, 0, .0980392) rgba(0, 0, 0, .0980392) rgba(0, 0, 0, .247059);
		-webkit-box-shadow: rgba(255, 255, 255, .0980392) 0 1px 0 inset, rgba(255, 255, 255, .0745098) 0 1px 0;
		box-shadow: rgba(255, 255, 255, .0980392) 0 1px 0 inset, rgba(255, 255, 255, .0745098) 0 1px 0;
		background-repeat: repeat no-repeat
	}

	#navbar-search,
	button {
		background-repeat: initial initial;
		background-position: 0 50%
	}

	.navbar .btn-navbar .icon-bar {
		display: block;
		width: 18px;
		height: 2px;
		background-color: #f5f5f5;
		-webkit-box-shadow: rgba(0, 0, 0, .247059) 0 1px 0;
		box-shadow: rgba(0, 0, 0, .247059) 0 1px 0;
		border-radius: 1px
	}

	.btn-navbar .icon-bar+.icon-bar {
		margin-top: 3px
	} 

	.pull-right {
		float: right
	}

	@media (max-width:767px) {
		body {
			padding-left: 20px;
			padding-right: 20px
		}
		.container {
			width: auto
		}
		.row-fluid {
			width: 100%
		}
		.row-fluid [class*=span],
		[class*=span] {
			float: none;
			display: block;
			width: auto;
			margin-left: 0
		}
	}

	@media (max-width:979px) and (min-width:768px) {
		[class*=span] {
			float: left;
			margin-left: 20px
		}
		.container {
			width: 724px
		}
		.span10 {
			width: 600px
		}
		.span3 {
			width: 166px
		}
		.span2 {
			width: 104px
		}
		.row-fluid {
			width: 100%
		}
		.row-fluid::after,
		.row-fluid::before {
			display: table;
			content: ''
		}
		.row-fluid::after {
			clear: both
		}
		.row-fluid [class*=span] {
			display: block;
			width: 100%;
			min-height: 28px;
			box-sizing: border-box;
			float: left;
			margin-left: 2.762430939%
		}
		.row-fluid [class*=span]:first-child,
		input {
			margin-left: 0
		}
		.row-fluid .span10 {
			width: 82.87292817100001%
		}
		.row-fluid .span3 {
			width: 22.928176794%
		}
		.row-fluid .span2 {
			width: 14.364640883%
		}
	}

	@media (min-width:1200px) {
		[class*=span] {
			float: left;
			margin-left: 30px
		}
		.container {
			width: 1170px
		}
		.span10 {
			width: 970px
		}
		.span3 {
			width: 270px
		}
		.span2 {
			width: 170px
		}
		.row-fluid {
			width: 100%
		}
		.row-fluid::after,
		.row-fluid::before {
			display: table;
			content: ''
		}
		.row-fluid::after {
			clear: both
		}
		.row-fluid [class*=span] {
			display: block;
			width: 100%;
			min-height: 28px;
			box-sizing: border-box;
			float: left;
			margin-left: 2.564102564%
		}
		.row-fluid [class*=span]:first-child,
		input {
			margin-left: 0
		}
		.row-fluid .span10 {
			width: 82.905982906%
		}
		.row-fluid .span3 {
			width: 23.076923077%
		}
		.row-fluid .span2 {
			width: 14.529914530000001%
		}
	}

	@media (max-width:979px) {
		body {
			padding-top: 0
		}
		.navbar .container {
			width: auto;
			padding: 0
		}
		.nav-collapse {
			clear: both;
			overflow: hidden;
			height: 0
		}
		.nav-collapse .nav {
			float: none;
			margin: 0 0 9px
		}
		.nav-collapse .nav>li {
			float: none
		}
		.nav-collapse .nav>li>a {
			margin-bottom: 2px;
			padding: 6px 15px;
			font-weight: 700;
			color: #999;
			border-radius: 3px
		}
		.navbar .nav-collapse .nav.pull-right {
			float: none;
			margin-left: 0
		}
		.navbar .btn-navbar {
			display: block
		}
	}

	body,
	html {
		height: 100%
	}

	#base {
		overflow: auto;
		padding-bottom: 20px;
		width: 100%
	}

	@media (min-width:768px) {
		body,
		html {
			height: 100%
		}
		#sticky-header {
			min-height: 100%
		}
		#base {
			padding-bottom: 120px
		}
		body::before {
			content: '';
			height: 100%;
			float: left;
			width: 0;
			margin-top: -32767px
		}
	}

	@media (max-width:767px) {
		body {
			padding-left: 0!important;
			padding-right: 0!important
		}
	}

	.heading.heading-home {
		font-size: 33px;
		font-weight: 300;
		line-height: 50px;
		margin-top: 10px;
		margin-bottom: 10px
	}

	.subheading.subheading-home {
		color: #fff!important;
		line-height: 28px;
	}

	#header-left {
		padding: 40px 0px 20px 0px;
		width: 71%;
		float: left
	}

	#header-left a {
		display: block;
		margin: 30px auto 30px 0px;
		width: 120px;
		font-weight: 400 !important;
		letter-spacing: 0.5px;
		font-size: 14px;
		text-align: center;
	}

	#header-right {
		float: right;
		text-align: right;
		width: 220px;
		padding: 0
	}

	.btns-wrapper {
		list-style: none;
		display: inline-block;
		margin: 27% 0
	}

	.btns-wrapper li {
		margin: 8px 0px
	}

	#landing-search .center {
		min-height: 76px
	}

	.searchbox-resistor {
		height: 88px!important
	}

	#features-cards {
		margin: 0!important
	}

	#features-cards li {
		padding: 46px 26px 16px;
		min-height: 170px
	}

	#features-cards li div {
		display: inline-block;
		float: left
	}

	#features-cards li div img {
		margin-right: 16px;
		width: 120px
	}

	#features-cards h3 {
		font-size: 24px;
		margin-bottom: 16px
	}

	#features-cards .feature-desc {
		display: table;
		float: none
	}

	#sec-as-seen {
		margin-top: 36px;
		border: 1px solid #dbe2ed;
		-webkit-box-shadow: #eef1f5 0 0 20px 5px;
		box-shadow: #eef1f5 0 0 20px 5px
	}

	#sec-as-seen h2 {
		padding: 36px 16px 16px
	}

	#testemonials .testm-holder {
		max-width: 80%;
		margin: 22px auto
	}

	.testm-avatar img {
		border-radius: 50%
	}

	.testm-itself {
		font-size: 20px;
		line-height: 1.5em;
		max-width: 700px;
		margin: 16px auto
	}

	.testm-who {
		font-size: 18px
	}

	.testm-holder .testm-name {
		font-weight: 600;
		font-size: 16px
	}

	.testm-holder .testm-job {
		font-weight: 300
	}

	@media (max-width:885px) {
		#header-left,
		#header-right {
			float: none;
			margin: 0 auto
		}
		.center {
			max-width: none
		}
		#header-left {
			max-width: 800px;
			padding: 16px
		}
		#header-left h1 {
			line-height: 1.1em;
			padding-bottom: 12px
		}
		#header-right .btns-wrapper {
			margin: 0 0 30px
		}
		#landing-search .center {
			min-height: 150px
		}
		#landing-search .center p {
			width: 100%;
			text-align: center
		}
	}

	@media (max-width:940px) {
		.heading.heading-home {
			font-size: 28px
		}
		.subheading.subheading-home {
			font-size: 16px
		}
	}

	@media (max-width:480px) {
		.nav-collapse {
			-webkit-transform: translate3d(0, 0, 0)
		}
		#features-cards li {
			text-align: center
		}
		#features-cards li div {
			display: block;
			float: none
		}
		#features-cards li div img {
			display: block;
			margin: 0 auto
		}
	}

	.hidden {
		visibility: hidden;
		display: none!important
	}

	#s-navbar {
		font-size: 16px
	}

	.nav-collapse {
		z-index: 10;
		background-color: #fff;
		width: 100vw;
		margin-left: -20px;
		height: 60px
	}

	#nav-logo-link {
		float: left;
		margin: 11px 10px 0 0
	}

	#nav-logo-link img {
		float: left;
		margin: 0
	}

	.s-nav-list {
		padding-right: 10px;
		height: 60px;
		margin-right: 16px!important;
		margin-left: 10px!important
	}

	#navbar-search-form {
		display: inline-table;
		margin-bottom: 0
	}

	.s-nav-list li {
		display: block;
		float: left;
		height: 60px
	}

	.s-nav-list li a {
		float: none;
		padding: 18px 10px 0;
		font-size: 13px!important
	}

	.s-nav-list li a.nav-link {
		float: none;
		font-weight: 400;
		color: #333;
		height: 60px;
		line-height: 60px;
		padding: 0 13px;
		font-size: 15px!important
	}

	#s-btn-signup a,
	#s-btn-signup2 a {
		margin-top: 12px;
		font-weight: 600;
		text-shadow: none;
		padding: 8px 13px!important;
		background-color: #fff
	}

	#s-btn-signup,
	#s-btn-signup2 {
		margin: 3px 8px;
		max-width: 80px
	}

	#s-btn-signup a {
		color: #ff761a;
		border: 1px solid #ff761a;
		border-radius: 3px
	}

	#s-btn-signup2 a {
		color: #304e70;
		border: 1px solid #304e70;
		border-radius: 3px
	}

	.navbar-innerhome {
		background-color: #fff;
		box-shadow: none;
		border-bottom-width: 1px;
		border-bottom-style: solid;
		border-bottom-color: #eee;
		border-radius: 0
	}

	#snap-sandwich {
		background-color: #fff;
		background-image: none;
		border: 0;
		margin-top: 17px;
		margin-right: 0;
		margin-bottom: 0
	}

	#snap-sandwich .icon-bar {
		background-color: #ff761a;
		box-shadow: none;
		border-radius: 0
	}

	.navbar .btn {
		margin-top: 16px
	}

	.pull-right {
		margin: 0
	}

	#s-navbar-inner {
		min-height: 0
	}

	#s-navbar-inner .container {
		height: 60px
	}

	#navbar-search {
		margin-top: 14px;
		margin-bottom: 0;
		text-align: left;
		border: 0
	}

	#navbar-search .search-query {
		background-color: #fff;
		border: 1px solid #3a5c84;
		font-weight: 400;
		width: 140px;
		height: 32px;
		line-height: 32px;
		margin: 0;
		padding: 0 12px;
		color: #3a5c84!important;
		font-size: 14px!important;
		box-shadow: none!important;
		outline: #000!important;
		border-radius: 3px 0 0 3px
	}

	#navbar-search #navbar-search-submit {
		background-color: #3a5c84;
		color: #fff;
		font-size: 14px;
		height: 34px;
		line-height: 34px;
		padding: 0 10px;
		width: 50px;
		float: right;
		margin: 0;
		border-radius: 0 3px 3px 0
	}

	#navbar-search #navbar-search-submit span.icon {
		width: 14px;
		height: 34px;
		overflow: hidden
	}

	@media (max-width:3000px) and (min-width:1221px) {
		#navbar-search .search-query {
			width: 200px
		}
		#navbar-search #navbar-search-submit {
			width: 100px;
			min-width: 100px;
		}
		#navbar-search #navbar-search-submit span.icon {
			float: left
		}
	}

	@media (max-width:1220px) and (min-width:1045px) {
		#navbar-search .search-query {
			width: 130px
		}
		#navbar-search #navbar-search-submit {
			width: 60px
		}
		#navbar-search #navbar-search-submit span.icon {
			float: none
		}
		#navbar-search #navbar-search-submit .hide-medium {
			display: none!important
		}
	}

	@media (max-width:1220px) and (min-width:980px) {
		#navbar-search .search-query {
			width: 100px
		}
		#navbar-search #navbar-search-submit {
			width: 40px;
			min-width: 40px;
		}
		#navbar-search #navbar-search-submit span.icon {
			float: none
		}
		#navbar-search #navbar-search-submit .hide-medium {
			display: none!important
		}
	}

	@media (max-width:979px) {
		.s-nav-list {
			height: auto
		}
		.s-nav-list li {
			height: 40px;
			border-bottom-width: 1px;
			border-bottom-style: solid;
			border-bottom-color: #e3e3e3;
			padding-left: 8px
		}
		.hide-small-nav {
			height: 1px!important;
			border-bottom-width: 0!important
		}
		#navbar-search .search-query {
			width: 60%
		}
		.s-nav-list li a.nav-link {
			height: 40px;
			line-height: 40px
		}
		.nav-collapse {
			height: 1px
		}
		#navbar-search {
			margin: 10px 0 15px 30px
		}
		ul.pull-right {
			margin-top: 10px!important
		}
	}

	.heading,
	.subheading {
		color: #fff
	}

	.heading {
		font-size: 40px;
		font-weight: 300;
		line-height: 70px
	}

	.subheading {
		font-size: 18px;
		font-weight: 400;
		line-height: 1.3em;
		max-width: 800px;
		margin: 0 auto;
		color: rgba(255, 255, 255, .6)
	}

	.center {
		max-width: 1080px;
		padding: 5px;
		margin: 0 auto!important
	}

	.center-homepage {
		max-width: 920px;
		padding: 5px 0px;
		margin: 0 auto!important
	}

	.flex {
		-webkit-justify-content: center;
		-webkit-align-items: flex-start;
		display: -webkit-flex;
		-webkit-flex-wrap: wrap;
		list-style-type: none;
		margin: 0
	}

	.flex-item {
		-webkit-flex-basis: auto;
		margin: 6px
	}

	.sct-title {
		text-align: center
	}

	.s-btn-join,
	.s-btn-social,
	.search-example,
	.searchbox-big {
		text-align: left
	}

	h2.sct-title {
		padding: 66px 10px 20px 10px;
		font-weight: 300;
		font-size: 32px
	}

	@media (max-width:880px) {
		.center {
			max-width: 600px
		}
	}

	@media (max-width:580px) {
		.heading {
			line-height: 1.2em;
			font-size: 48px;
			margin-bottom: 5px
		}
	}

	@media (max-width:940px) {
		h1.heading {
			font-size: 38px
		}
	}

	.bg-blue {
		background-color: #304e70
	}

	.clr-orange {
		color: #ff761a
	}

	.bg-lighter {
		background-color: #f1f1f1
	}

	.bg-lightest {
		background-color: #fafafa
	}

	.bg-white {
		background-color: #fff
	}

	.clr-white {
		color: #fafafa
	}

	.s-btn {
		display: inline-block;
		font-weight: 600;
		border-radius: 3px
	}

	.s-btn-twttr {
		background-color: #55acee
	}

	.s-btn-lnkdn {
		background-color: #4377b2
	}

	.s-btn-social {
		color: #fff;
		border: 1px solid transparent
	}

	.s-btn-join i {
		margin-right: 27px!important
	}

	.s-btn-medium {
		font-size: 14px;
		padding: 8px 24px
	}

	.s-btn-large {
		font-size: 18px;
		padding: 10px 30px
	}

	.s-btn-medium i {
		margin-right: 10px;
		margin-left: -7px;
		width: 13px;
		height: 14px;
		float: left;
		line-height: 18px;
	}

	.s-btn.s-btn-medium.w-fixed {
		width: 164px!important
	}

	.s-btn.ghost {
		border: 1px solid #1a1a1a
	}

	.s-btn.outl-blue {
		border-color: #304e70;
		color: #304e70
	}

	.s-btn.outl-orange {
		border-color: #ff761a;
		color: #ff761a
	}

	.s-btn.blue {
		border: 1px solid #304e70;
		background-color: #304e70;
		color: #fff
	}

	button {
		border: 0;
		color: inherit;
		font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
		font-size: inherit;
		font-style: inherit;
		font-variant: inherit;
		font-weight: inherit;
		line-height: normal;
		overflow: visible;
		padding: 0;
		-webkit-appearance: button
	}

	.card {
		box-shadow: #cacaca 0 1px 3px 0;
		max-width: 400px;
		overflow: hidden;
		border-radius: 6px
	}

	.searchbox-big {
		max-width: 920px;
		height: 48px;
		margin: 15px auto 10px;
		border: 1px solid #fff;
		background-color: #fff;
		border-radius: 2px
	}

	.searchbox-resistor {
		border: 0;
		color: #fff;
		background-position: 0 50%;
		background-repeat: initial initial
	}

	.resistor-submit,
	.search-tab-blue.active {
		background-position: initial initial;
		background-repeat: initial initial
	}

	.no_ml {
		margin-left: 0!important
	}

	.search-tabs-blue {
		max-width: 920px;
		margin: 40px auto 10px;
		display: table;
		width: 920px;
		border-bottom-width: 2px;
		border-bottom-style: solid;
		border-bottom-color: rgba(255, 255, 255, .2)
	}

	.search-tab-blue {
		padding: 10px 15px;
		float: left;
		color: #fff;
		font-weight: 600;
		font-size: 14px;
		border-radius: 3px 3px 0 0
	}

	.search-tab-blue.active {
		background-color: rgba(255, 255, 255, .2)
	}

	.homepage-search .search-tabs-blue {
		margin-top: 20px
	}

	.search-input::-webkit-input-placeholder {
		color: #999;
		padding-top: 3px
	}

	.searchbox-big .search-input {
		background-color: #fff;
		border: 0;
		color: #304e70;
		font-size: 18px;
		font-weight: 400;
		font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
		margin: 10px;
		width: 91%;
		box-shadow: none!important;
		outline: #000!important
	}

	.search-resistor input,
	.select-style select {
		font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
	}

	form.home_search {
		clear: both;
		margin-bottom: 10px
	}

	.search-example {
		max-width: 920px;
		margin: 0 auto
	}

	.homepage-search .search-example {
		margin-bottom: 10px
	}

	.search-example span {
		color: rgba(255, 255, 255, .701961)
	}

	.search-example a {
		color: rgba(255, 255, 255, .701961)!important;
		text-decoration: underline!important
	}

	.searchbox-submit {
		color: #304e70;
		background: #ffffff;
		font-size: 18px;
		margin: 9px 9px 0 0;
		padding: 3px 4px 4px 5px;
		width: 30px;
		height: 30px;
		float: right;
		border-radius: 10px
	}

	.resistor-submit {
		display: block;
		text-align: center;
		width: 100%;
		height: 39px;
		background-color: #ff671a;
		line-height: 39px;
		font-size: 16px;
		font-weight: 600;
		margin-top: 24px;
		border-radius: 3px
	}

	.resistor-submit span {
		width: 18px;
		display: inline;
		font-size: 14px
	}

	.search-resistor p.input_label {
		font-size: 13px;
		font-weight: 400;
		line-height: 16px;
		color: #666
	}

	.searchbox-resistor p.input_label {
		color: #fff
	}

	.search-resistor input.resistor-input {
		display: inline;
		width: 92%;
		height: 37px;
		padding: 0 4%;
		font-weight: 600;
		color: #333
	}

	.search-resistor input.resistor-input::-webkit-input-placeholder {
		font-weight: 400
	}

	.select-style {
		border: 1px solid #ccc;
		width: 100%;
		margin-left: auto;
		margin-right: auto;
		overflow: hidden;
		background-image: url(data:image/png;base64,R0lGODlhDwAUAIABAAAAAP///yH5BAEAAAEALAAAAAAPABQAAAIXjI+py+0Po5wH2HsXzmw//lHiSJZmUAAAOw==);
		background-color: #fff;
		margin-bottom: 5px;
		background-position: 95% 50%;
		background-repeat: no-repeat no-repeat;
		border-radius: 3px
	}

	.select-style select {
		padding: 5px 10px 0;
		width: 100%;
		border: 0;
		box-shadow: none;
		background-color: transparent;
		background-image: none;
		-webkit-appearance: none;
		font-weight: 600;
		background-position: initial initial;
		background-repeat: initial initial
	}

	@media (max-width:885px) {
		.search-tabs-blue {
			width: auto;
			max-width: 800px
		}
	}

	@media (max-width:780px) {
		.searchbox-big {
			max-width: 80%
		}
		.search-example {
			visibility: hidden
		}

		.built-for-engineers {
			max-width: 90%;
		}
	}

	#landing-header {
		background-image: linear-gradient(150deg, #ea5d12 0, #F97D38 100%);
		color: white;
	}

	/**********************************************/

    .home-feed {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #ffffff;  
		height: 480px;
	}

	.home-feed-image {
		height: 440px;
		position: absolute;
		right: 0;
		top: 80px;
	}

	.section-search-part {
		background: #FFFFFF;
		border: 1px solid #E0E0E0;
		box-sizing: border-box;
		box-shadow: 10px 4px 10px rgba(0, 0, 0, 0.05), -10px -4px 20px rgba(0, 0, 0, 0.05);
		border-radius: 16px;
		height: 76px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0px .5rem 0px .5rem; 
	}
	
	.search-input { 
		width: 100%;
		border: none !important;
		font-family: Open Sans;
		font-style: normal;
		font-weight: normal;
		font-size: 20px !important;
		line-height: 27px !important;
		color: #696969 !important;
		box-shadow: none !important;
		margin-top: 0.5rem;
		height: auto !important;
	} 

	.search-btn {
		background: #FF761A;
		border: 1px solid #FF761A;
		box-sizing: border-box;
		box-shadow: 10px 0px 20px rgba(244, 127, 32, 0.3), -10px 4px 20px rgba(255, 118, 26, 0.2);
		border-radius: 12px;
		color: #fff;
		height: 45px;
	} 

	.example_search {
		background: #FFFFFF;
		border: 1px solid #C4C4C4;
		box-sizing: border-box;
		border-radius: 100px;
		display: flex;
		justify-content: center;
		margin-right: .5rem;
	}

	.example-section {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		width: 100%;
		margin-top: .5rem;
		margin-left: 2rem;
	}

	

    .marquee {
		width: 100%;
		overflow: hidden; 
		background: #FAFAFA; 
	}

	.marquee span {
		width: 132px;
		height: 132px;
		background: #FFFFFF;
		border: 1px solid #F1F1F1;
		box-sizing: border-box;
		border-radius: 50%;
		display: flex !important;
		justify-content: center;
		align-items: center;
		margin-left: 42px;
		padding: 0px !important;
	}

	.marquee span img {
		width: 75px;
	}

	.js-marquee {
		display: flex;
	}
 
	.img-span { 
		width: 158px;
		height: 164px;
		display: flex !important;
		justify-content: center !important;
		align-items: center !important;
	}

	#export-to {
		padding-left: 72px;
		padding-right: 72px;
		padding-top: 48px;
	}

	#export-to .img-span {
		background: #FFFFFF;
		border: 1px solid #F1F1F1;
		box-sizing: border-box;
		border-radius: 16px;
		width: 132px;
		height: 132px;
		margin-left: 0px; 
	}

	#export-to span img {
		width: 85px;
	}
	
	#export-to .format-div {
		padding-left: 88px;
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		align-items: center;
	}

	.export-item {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		margin-top: 1rem;
 	}

	.export-to-text {
		padding-left: 32px; 
		text-align: left !important;
		border-left: 3px solid #FF761B;
		font-family: Open Sans;
		font-style: normal;
		font-weight: 800;
		font-size: 32px;
		line-height: 48px;
		letter-spacing: 0.04em;
		color: #000000;
	}

	.span-text {
		margin-top: 1rem !important;
	}

	

	.make-snap-header {		
		font-family: Open Sans;
		font-style: normal;
		font-weight: 800;
		font-size: 32px;
		line-height: 64px;
		letter-spacing: 0.04em;
		color: #000000;
		background: #FFFFFF;
		box-shadow: 10px 4px 10px rgba(0, 0, 0, 0.05), -10px -4px 10px rgba(0, 0, 0, 0.05);
		border-top-left-radius: 24px;
		border-top-right-radius: 24px;
		width: 90%;
		border-bottom: 2px solid #FAFAFA;
	}
 
	.make-snap-body {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 32px;
		padding-bottom: 1.5rem;
		padding-top: 1.5rem; 
		background: #FFFFFF;
		border-bottom-left-radius: 24px;
		border-bottom-right-radius: 24px;
		width: 90%; 
		box-shadow: 10px 4px 10px rgba(0, 0, 0, 0.05), -10px -4px 10px rgba(0, 0, 0, 0.05);
		z-index: 100;
	}

	.make-snap-img {
		height: 392px;
	}
 
	.make-snap-img-section {
		display: flex;
		background: #F1F1F1;
		border: 1px solid #D8D8D8;
		box-sizing: border-box;
		border-radius: 16px;
		background: #fafafa;
		justify-content: center;
		align-items: center;
		padding: .5rem;
		height: 100%;
	}

	.make-snap-img-section img {
		height: auto;
    	border-radius: 8px;
		width: 100%;
	}

	.snap-card {
		background: #FFFFFF;
		border: 1px solid #F1F1F1;
		box-sizing: border-box;
		border-radius: 8px; 
		padding: 1.5rem;
		margin-top: .5rem;
	}

	.snap-card:nth-child(1) {
		margin-top: 0px;
	}

	.snap-card:hover {
		box-shadow: 4px 4px 4px rgba(0, 0, 0, 0.05), -4px -4px 10px rgba(0, 0, 0, 0.05);
	}

	.snap-card-heading {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 800;
		font-size: 18px;
		line-height: 25px; 
		letter-spacing: 0.04em;
	}

	.snap-card-text {
		font-family: Open Sans;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		line-height: 21px; 
		letter-spacing: 0.04em;
		color: #000000;
		display: none;
	}

	.snap-card img {
		width: 72px !important;
	}

	.be-card {
		background: #FFFFFF;
		border: 1px solid #E1E1E1;
		box-sizing: border-box;
		border-radius: 16px; 
		min-height: 300px;
	}

	.be-card-heading {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 800;
		font-size: 22px;
		line-height: 30px;
		color: #000000;
	}
	
	.be-card-text {
		font-family: Open Sans;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		line-height: 21px; 
		letter-spacing: 0.04em; 
		color: #000000;
	}

	.manufacturer-heading {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 600;
		font-size: 24px;
		line-height: 32px;
		letter-spacing: 0.04em;
		color: #000000;
		margin: 88px 0px 48px 48px;
	}

	.manufacturer-logo {
		width: 128px;
		height: 128px;
		background: #FFFFFF;
		border: 1px solid #F1F1F1;
		box-sizing: border-box;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0.2rem;
	}

	.manufacturer-logo img {
		width: 95px;
	} 

	.instant-manufacturer-text {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 600;
		font-size: 24px;
		line-height: 32px;
	}

	.f-column-center {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.insta-part-section {
		background: #FFFFFF;
		border: 1px solid #E5E5E5;
		box-sizing: border-box;
		border-radius: 16px;
		padding: 1rem !important;
		margin-left: 88px !important;
		margin-right: 88px !important;
		min-height: 360px
	}

	.lcs-header {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 800;
		font-size: 32px;
		line-height: 44px;
		letter-spacing: 0.04em;
		color: #000000;
	}

	.lcs-section {
		padding: 0px 88px 0px 88px;
	}

	.lcs-card {
		background: #FFFFFF;
		border: 1px solid #E1E1E1;
		box-sizing: border-box;
		box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
		border-radius: 8px;
		max-width: 475px;
		padding: 2rem 1rem 2rem 1rem !important;
		height: 100%;
	}

	.lcs-card img {
		width: 80px;
	}

	.lcs-card-number { 
		font-weight: 900;
		font-size: 40px;
		line-height: 50px; 
		letter-spacing: 0.04em;
		color: #3B6285;
	}

	.lcs-card-text {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 300;
		font-size: 16px;
		line-height: 22px; 
		letter-spacing: 0.03em; 
		color: #000000;
	}

	
	
	.carousel-item {
		width: 90% !important;
		height: 400px !important;
		text-align: left !important;
		margin-top: 75px;
		margin-left: 88px;
		margin-right: 48px !important;
		display: none !important;
		right: 0;
		background: #FFFFFF;
		border: 1px solid #F1F1F1;
		box-sizing: border-box;
		border-radius: 16px;
		padding: 3rem;
		padding-top: 0px;
	}

	.carousel .active {
		opacity: 1;
    	position: relative !important;
		display: block !important;
	}

	.carousel-indicators li {
		width: 10px !important;
		height: 10px !important;
		border-radius: 100%;
		background: #FFFFFF;
		border: 1px solid #3B6285 !important;
		color: #fff;
	}

	.carousel-indicators { 
		margin-right: 0px !important;
		margin-left: 0px !important;
		bottom: -45px !important; 
	}
	.carousel-item .person-img {
		width: 120px;
		height: 120px;
		position: absolute;
		z-index: 1;
		border-radius: 16px;
		left: 88px;
		top: -56px;
	}
	body {
		background-color: #fafafa;
	}
	.carousel-indicators li.active {
		background: #3B6285;
	}
	.carousel-item .recommendation-text {
		font-family: Open Sans;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		line-height: 33px;
		letter-spacing: 0.04em;
		text-align: justify;
		margin-top: 96px !important;
	}

	.carousel-item .recommendation-company {
		font-family: Open Sans;
		font-style: normal;
		font-size: 14px;
		line-height: 22px;
		letter-spacing: 0.04em;
		position: absolute;
		bottom: 30px; 
	}
	
	
	

	#join-snapeda { 
		font-weight: bolder;
		font-size: 32px;
		line-height: 44px;
	}

	.social-button {
		width: 300px;
		height: 54px;
		border-radius: 12px;
		font-family: Open Sans;
		font-style: normal;
		font-weight: bold;
		font-size: 18px;
		line-height: 24px;
		color: #FFFFFF !important;
		display: flex;
		justify-content: center;
		align-items: center;
		text-decoration: none !important;
	}

	.btn-email {
		background: #516C8C;
	}
	
	.btn-twitter {
		background: #56ACEE;
	}
	
	.btn-linkedin {
		background: #4377B1;
	}

	.social-button span {
		width: 150px;
		text-align: left;
	}

	.navigate-right {
		color: #C4C4C4;
		transform: scale(1,2.5);
		font-size: 18px;
	}

	.feed-item .part-name {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 800;
		font-size: 14px;
		line-height: 21px;  
		letter-spacing: 0.04em;
		color: #000000;
	}

	.feed-item .manufacturer-name {
		font-family: Open Sans;
		font-style: normal;
		font-weight: normal;
		font-size: 12px;
		line-height: 21px; 
		display: flex;
		align-items: center;
		justify-content: space-between;
		letter-spacing: 0.04em;
		color: #000000;
		margin-top: 10px;
		margin-bottom: 10px;
	}

	.feed-item .time-stamp {
		font-family: Open Sans;
		font-style: normal;
		font-size: 12px;
		line-height: 18px;
		letter-spacing: 0.04em;
		color: #696969;
	}

	#new-items-feed {
		height: 680px;
		padding: 0 !important;
		background: #FFFFFF;
		border: 1px solid #E1E1E1;
		box-sizing: border-box;
		border-radius: 24px;
		width: 432px;
		position: absolute;
		overflow: hidden;
		right: 248px;
		margin-top: -88px;
	}

	#new-items-feed .feed-heading {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 800;
		font-size: 20px;
		line-height: 27px;
		letter-spacing: 0.04em;
		color: #000000;
		text-align: center;
	}

	#new-items-feed .ul-feed {
		height: 568px;
		list-style: none;
		margin: 18px;
		overflow-y: scroll;
		width: 100%;
		padding-right: 24px;
		overflow-x: hidden;
		padding-left: 8px;
		margin-top: 0px;
		padding-top: 8px;
	}

	#base {
		padding-bottom: 40px !important;
	} 

	.manufacturers {
		flex-wrap: wrap;
		display: flex;
		justify-content: space-between;
		padding-left: 120px;
		padding-right: 120px;
		margin-bottom: 64px;
	}

	.instapart-info-section {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-left: 88px;
	}

	.instapart-info-section-right {
		margin-left: 0px;
		margin-right: 88px;
	}

	.instapart-info-section img {
		width: 242px;
		height: 48px;
	}

	.snap-btn {
		height: 46px;
		width: 200px !important;
		background: #FF761A;
		border: 1px solid #FF761A;
		box-sizing: border-box;
		border-radius: 12px;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 18px;
		font-family: Open Sans;
		font-style: normal;
		font-weight: bold;
		font-size: 15px;
		line-height: 24px;
		text-align: center;
		color: #FFFFFF !important;
		text-decoration: none !important;
	}

	.instapart-info-section .snap-btn {
		width: 75%;
	}

	.parts-se-details {
		display: flex;
		justify-content: center; 
		flex-direction: column;
		margin-top: 24px;
	}

	.parts-in-searchengine {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		margin-top: 24px
	}

	.instabuild-img {
		position: absolute;
	}

	.bfe-section {
		margin-top: 40px;
		margin-bottom: 40px;
		width: 40%;
	}
  
	.be-cards{
		padding-right: 48px;
		padding-left: 48px;
	}

	.section-bfe {
		padding: 72px 106px 72px 106px;	
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.bfe-heading {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 800;
		font-size: 32px;
		line-height: 44px;
		letter-spacing: 0.04em;
		color: #000000;
	}

	.bfe-text {
		font-family: Open Sans;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		line-height: 24px;
		letter-spacing: 0.04em;
		color: #000000;
		margin-top: 1.5rem !important;
	}

	.largest-community-heading {
		font-family: Open Sans;
		font-style: normal;
		font-weight: 800;
		font-size: 32px;
		line-height: 44px;
		display: flex;
		align-items: center;
		letter-spacing: 0.04em;
		color: #403826;
		justify-content: center;
		margin-top: 64px;
		margin-bottom: 48px;
	}

	.section-bfe-explore {
		margin-top: 160px;
	}

	.img-social-section {
		margin-top:10rem;
	}

	.join-snapeda-section {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 7rem;
		margin-left: 10rem;
	}

	.social-section-bg {
		background: url(/static/img/home/<USER>
    	background-size: cover;
		margin-top: 2rem;
		padding: 2rem;
	}

	.export-to-name {
		margin-top: 0.5rem;
	}

	.partners-info {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		letter-spacing: 0.04em;
		color: #696969;
		text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
		line-height: 16px;
	}

	.testimonials-section {
		padding: 0px 64px 0px 64px;
	}
	
	.ipf-mobile {
		display: none !important;
	}

	.ipf-desktop {
		display: flex !important;
	}

	.bfe-section .snap-btn {
		margin-top: 32px;
		margin-left: 1rem;
	}

	#instant-access {
		display: none;
		background: #fff;
	}

	.mob-gif {
		display: none;
	}

	@media (max-width:1024px) { 
		#new-items-feed {
			right: 80px;
		}

		.home-feed {
			height: 500px;
			padding-left: 0rem; 
		}


		#new-items-feed {
			position: initial;
			margin: 0 auto;
		}
		
		.bfe-section {
			width: 100% !important;
			padding: 0px;
			margin: 0 auto;
			max-width: 100% !important;
			margin-top: 10px !important;
		}
	}
	
	@media (max-width:1024px) { 
		
		.insta-part-section {
			margin-left: 0px;
			margin-right: 0px;
		}

		.instapart-info-section {
			margin-left: 64px;
		}

		.instapart-info-section-right {
			margin-left: 0px;
			margin-right: 64px;
		}

		.instapart-info-section .snap-btn {
			width: 50%;
		}

		.be-cards{
			padding-right: 8px;
			padding-left: 0px;
		} 

		.carousel-item {
			height: auto !important;
			margin-left: 0px;
		}

		.section-bfe .col-md {
			text-align: center !important;
		}
		 
	} 
 
	@media (max-width:1080px) { 
		
		.built-for-engineers {
			max-width: 90%;
		}

		.manufacturers {
			justify-content: center;
			padding-left: 88px;
			padding-right: 88px;
		}

		.instapart-info-section {
			margin-left: 0px !important;
			margin-right: 0px !important;
		}

		.instapart-info-section .snap-btn {
			width: auto;
		}

		.parts-se-details {
			align-items: center;
		} 

		.lcs-card {
			max-width: 100%;
		}

		.make-snap-img-section img {
			min-width: auto;
		}

		.instabuild-img {
			position: initial;
		}

		.new-items-feed {
			position: initial;
			margin: 0 auto;
		}

		.search-input {
			width: 80%;
		} 

		.carousel-item {
			height: auto !important;
			margin-left: 0px;
		}

		.join-snapeda-section {
			margin-left: 0px;
		}
	}

	@media (max-width:750px) {
		.social-section-bg {
			background: #FAFAFA;
			padding: 0px;
			margin: 0px;
			padding-bottom: 2rem;
			margin-top: 2rem; 
		}

		.img-social-section {
			display: none;
		}
	}

	@media (max-width:768px) {
		.home-feed {
			height: auto;
		}

		.home-feed-h1 {
			display: inline-block;
			margin-bottom: 40px;
			padding-top: 3rem;
			font-size: 32px !important;
			line-height: 36px !important;
			width: 50%;
		}

		.home-feed-description {
			padding-top: 32px;
			display: block;
		}

		.section-search-part {
			border: 0px;
			box-shadow: none;
			padding: 0px !important;
		}

		.ssp-input-section {
			padding: 0px !important;
		}

		.search-input {
			height: 54px !important;
			background: #FFFFFF;
			border: 1px solid #E0E0E0 !important;
			box-sizing: border-box;
			box-shadow: 10px 4px 10px rgba(0, 0, 0, 0.05), -10px -4px 20px rgba(0, 0, 0, 0.05) !important;
			border-radius: 8px !important;
			width: 100% !important;
			border-top-right-radius: 0px !important;
			border-bottom-right-radius: 0px !important;
			padding-left: 16px !important;
			font-size: 16px !important;
		}

		.ssp-input-btn-section {
			width: 20% !important;
			padding: 0px !important;
		}

		.ssp-input-btn-section .search-btn {
			height: 54px;
			margin-left: 0px !important;
			border-radius: 8px;
			border-top-left-radius: 0px;
			border-bottom-left-radius: 0px;
		}

		.ssp-input-btn-section .search-btn span {
			display: none;
		}

		.example-section { 
			display: block !important;
			margin-left: 0px !important;
		}

		.example_search {
			min-width: 80px;
			margin-left: 0px !important;
			margin-right: .5rem;
			margin-top: .5rem;
			margin-bottom: 1rem;
		}

		.home-feed-image {
			display: block;
			height: 200px;
		}

		.insta-part-section {
			margin-left: 0px;
			margin-right: 8px;
		}

		.make-snap-header {
			font-size: 16px;
			line-height: 22px;
			letter-spacing: 0.04em;
			color: #000000;
			border: none;
			box-shadow: none;
			width: 102%;
			border-radius: 0px;
			padding-bottom: 0px !important;
		}

		.make-snap-header img {
			display: none;
		}

		.make-snap-body {
			padding: 0px !important;
			border-radius: 0px;
			margin: 0px !important;
			box-shadow: none;
			overflow: auto;
		}

		.make-snap-img-section {
			display: none;
		}

		.make-snap-detail-section {
			display: flex;
			width: 100%;
			padding: 0px !important;
			margin: 0px !important;
		}

		.snap-card:nth-child(2) {
			background: #fafafa;
		} 

		.snap-card {
			display: flex;
			flex-direction: column;
			min-width: 144px;
			width: 33.33%;
			border-radius: 0px;
			justify-content: center;
			padding: 0px;
			padding-top: 1rem;
			padding-bottom: 1rem;
			margin: 0px !important;
		}

		.snap-card img {
			width: 48px !important;
		}

		.snap-card-heading { 
			font-size: 12px;
			line-height: 16px;
		}

		.snap-card-text {
			display: none !important;
		}

		#export-to .format-div {
			padding-left: 0px;
			justify-content: center;
    		align-items: center;
		}

		#export-to {
			padding-left: 0px;
			padding-right: 0px;
			padding-top: 48px;
		}

		.export-to-text {
			font-size: 16px;
			line-height: 24px;
			padding-right: 5rem;
			margin-left: 3rem;
		}

		.export-item { 
			display: inline-flex;
			margin-top: 1rem !important;
			max-width: 96px;
			margin-right: 1rem;
		}

		.section-bfe {
			padding: 48px 0px 48px 0px;
		}

		.bfe-heading {
			text-align: left;
			font-size: 18px;
			line-height: 25px;
		}

		.bfe-text {
			text-align: left;
			font-size: 14px;
			line-height: 22px;
		}

		.snap-btn {
			width: 100% !important; 
    		margin-bottom: 24px;
		}

		.largest-community-heading {
			font-size: 16px;
			line-height: 24px;
			margin-top: 24px;
			margin-bottom: 24px;
		}

		.lcs-card {
			display: flex;
		}

		.ipf-mobile {
			display: flex !important;
		}

		.ipf-desktop {
			display: none !important;
		}

		.section-bfe img {
			width: 44%;
		} 

		.section-bfe col-md {
			text-align: center !important;
		}

		.lced-mobile {
			margin: 0px 0px 1rem 0px!important;
    		padding: 0px 12px 0px 0px;
		}

		#new-items-feed {
			width: auto !important;
    		margin: 0px 20px 0px 20px !important;
		}

		#new-items-feed .ul-feed {
			margin-left: 10px;
		}

		.lcs-card-overlay {
			margin: 0px 0px 1rem 0px!important;
    		padding: 0px 12px 0px 0px !important;
		}

		.section-bfe-explore {
			margin-top: 48px;
			padding-top: 24px;
		}

		.bfe-section .snap-btn {
			margin: 0px 0px 32px 0px !important;
		}

		.manufacturers {
			justify-content: center;
			padding-left: 0px;
			padding-right: 0px;
		}

		.manufacturer-heading {
			margin-top: 32px;
		}

		#base {
			padding-bottom: 0px !important;
		}

		#site-footer {
			margin-top: 0px !important;
		}

		#join-snapeda-section {
			margin: 0px !important;
			padding: 0px !important;
			text-align: center;
		}

		.carousel-inner {
			padding-top: 32px;
		}

		.carousel-item {
			height: 840px !important;
			margin-left: 0px;
			padding: 0px !important;
		}

		.testimonials-section {
			padding: 0px !important;
		}

		.export-item-lt {
			width: 100px;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		#export-to .img-span {
			width: 80px;
			height: 80px;
		}

		#export-to span img {
			width: 64px;
		}

		#instant-access { 
			display: block;
		}

		.manufacturers {
			display: none;
		}

		.marquee .img-span {
			width: 96px !important;
			height: 96px !important;
			margin-left: 8px !important;
		}

		.marquee span img {
			width: 64px !important;
		}

		.mob-gif {
			display: block;
			margin-top: 16px;
			padding: 8px;
			display: block;
		}

		.mob-gif img {
			border-radius: 8px;
		}
	}

	.z-1 {
		z-index: 1;
	}

	#base {
		overflow: hidden !important;
	}

	#nav-searchbar-wrapper {
		display: none;
	}

	
	.row:before, .row:after {display: none !important;} 
</style>
<meta name="google-site-verification" content="eVobs3IVSEbdA0uoIP3UPWRBdmbWIJaxdk4fKLc08Mc" />
<meta name="msvalidate.01" content="823D3AAFA800CD08A585B4F69127E7FB" />

    





<!-- Facebook Pixel Code -->
<!-- <script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', '339053146617437');
  fbq('track', 'PageView');
</script>
<noscript><img height="1" width="1" style="display:none"
  src="https://www.facebook.com/tr?id=339053146617437&ev=PageView&noscript=1"
/></noscript> -->
<!-- End Facebook Pixel Code -->



    <!-- Convertize -->
    <script async src="//pixel.convertize.io/3693.js" charset="UTF-8"></script>


    <!--load FOMO script -->
    <script src='https://load.fomo.com/api/v1/VRrmLWeQ3uCcECxLw7VDjw/load.js' async></script>
    <style>

    .extended-search-input{
        width: 100%!important;
    }
    .extended-search-bar{
        max-width: 100%!important;
        /* width: calc( 100% - 953px ); */
        margin-right: 16px!important;
        width: 100%!important;
    }
    .extended-search-container{
        display: flex!important;
    }
        #seda-navbar {
            font-family: 'Open Sans';
            font-size: 16px;
            background: #FFFFFF;
            border: 1px solid #E5E5E5;
            box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            color: #3a3a3a;
            position: fixed;
            width: 100%;
            z-index: 1000;
        }

        #seda-navbar #nav-logo-link {
            margin: 0px;
            float: none;
            margin-top: auto;
            margin-bottom: auto;
        }

        #seda-navbar .nav-bar-items {
            display: flex;
            align-items: center;
        }

        #seda-navbar .nav-bar-item {
            font-family: Open Sans;
            font-style: normal;
            font-weight: normal;
            font-size: 16px;
            line-height: 22px;
            display: flex;
            align-items: center;
            color: #3A3A3A;
            margin-left: 32px;
            cursor: pointer;
            text-decoration: none !important;
        }

        #seda-navbar .nav-bar-item .symbol-down {
            transform: rotate(90deg) scale(1,1.75);
            margin-left: 10px;
        }
        .symbol-down-instabuild{
            transform: rotate(90deg) scale(.83333,1.4583333);;
            margin-left: 3px;
            display: inline-block;
        }

        #seda-navbar .nav-bar-item .nav-bar-dropdown {
            position: absolute;
            top: 50px;
            display: none;
            min-width: 200px;
            min-height: 50px;
            background: #FFFFFF;
            border: 1px solid #E5E5E5;
            box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
            border-top: 0px;
            padding-top: 8px;
            padding-bottom: 8px;
            cursor: pointer;
            font-size: 14px;
            z-index: 2;
        }

        #seda-navbar .nav-bar-item:hover .nav-bar-dropdown {
            display: block;
        }

        #seda-navbar .nav-bar-item .nav-bar-dropdown .dropdown-option {
            padding: 10px;
            text-decoration: none !important;
            display: block;
            color: #3a3a3a;
        }

        #seda-navbar .nav-bar-item .nav-bar-dropdown .dropdown-option:hover {
            background: #ff761a;
        }

        .ls-btn {
            width: 96px;
            height: 40px;
            box-sizing: border-box;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
            margin-right: 16px;
            text-decoration: none !important;
        }

        .login-btn {
            color: #3B5D83;
            border: 1px solid #3B5D83;
        }

        .login-btn:hover {
            color:#fff;
            background: #3B5D83;
        }

        .signup-btn {
            color: #ffffff;
            background: #FF761A;
            border: 1px solid #FF761A;
        }

        .signup-btn:hover {
            color: #FF761A;
            background: #FFFFFF;
        }

        .ls-section {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            position: absolute;
            right: 16px;
        }

        
        #nav-search-part-form {
            height: 48px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0px;
            margin: 0px;
            position: absolute;
            right: 256px;
        }

        #nav-searchbar-wrapper {
            margin: 0px;
            padding: 0px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #searchbar-input {
            height: 40px !important;
            border: 1px solid #3B6285 !important;
            box-sizing: border-box;
            border-radius: 8px !important;
            border-top-right-radius: 0px !important;
            border-bottom-right-radius: 0px !important;
            font-family: Open Sans;
            font-family: Open Sans;
            font-style: normal;
            font-weight: normal;
            font-size: 14px !important;
            line-height: 19px;
            margin: 0px;
            min-width: 240px;
            padding-left: 16px;
            padding-right: 16px;
        }

        #nav-search-btn {
            height: 40px;
            width: 112px;
            background: #3B6285;
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
            font-family: Open Sans;
            font-style: normal;
            font-weight: bold;
            font-size: 14px;
            line-height: 19px;
            color: #FFFFFF;
            border: 1px solid #3b6285;
        }

        .btn-search-icon {
            display: none  !important;
        }

        #seda-navbar .nav-logo-link {
            margin-top: -10px;
        }

        #seda-navbar .r-menu-btn {
            display: none;
            position: absolute;
            top: 20px;
            right: 22px;
            width: 32px;
            height: 32px;
            background: #3B6285;
            border: 1px solid #3B6285;
            box-sizing: border-box;
            border-radius: 8px;
            color: #fff;
            font-size: 22px;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            text-decoration: none !important;
        }

        @media (max-width:1200px) {
            #seda-navbar .nav-logo-link {
                margin-top: 0px;
            }

            .section-search-part {
                display: flex !important;
            }

            #seda-navbar .nav-bar-items {
                width: 100%;
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
                padding-left: 0px;
            }

            #seda-navbar .nav-bar-item-dd {
                padding: 0px !important;
            }

            #seda-navbar .nav-bar-item-dd {
                padding: 0px;
            }

            #seda-navbar .nav-bar-item .nav-item-text,
            #seda-navbar .nav-bar-item .symbol-down {
                display: none !important;
            }
            #seda-navbar .nav-bar-item {
                margin-left: 0px;
                padding: 15px;
                padding-left: 0px;
            }

            #seda-navbar .nav-bar-item .symbol-down {
                display: flex;
            }

            #seda-navbar .nav-bar-item .nav-bar-dropdown {
                position: relative;
                display: flex !important;
                top: 0px;
                margin-left: 0px;
                border: none;
                box-shadow: none;
                padding: 0px;
            }

            #seda-navbar .nav-bar-item .nav-bar-dropdown .dropdown-option {
                padding-left: 0px;
                width: 128px;
            }

            #seda-navbar .nav-bar-item .nav-bar-dropdown .dropdown-option:hover {
                background: #fff;
            }

            .ls-section {
                width: 100%;
                display: flex;
                position: initial;
            }

            .ls-btn {
                width: 50%;
                margin-right: 0px !important;
            }

            .login-btn {
                margin-right: 16px !important;
            }

            #nav-search-part-form {
                position: initial;
                display: block;
                margin-bottom: 10px;
                width: 100%;
            }

            #nav-searchbar-wrapper {
                margin-right: 0;
                width: 100%;
                max-width: 100%;
            }

            #searchbar-input {
                min-width: calc(100% - 112px) !important;
            }

            #seda-navbar .r-menu-btn {
                display: flex;
            }

            .nav-bar-items, #nav-search-part-form, .ls-section {
                display: none;
            }

        }

        @media (max-width:550px) {

            .btn-search-text {
                display: none;
            }

            .btn-search-icon {
                display: block !important;
                color: #fff;
                margin: 0 auto;
            }
        }

        @media (max-width:712px) {
            #seda-navbar .nav-bar-item .nav-bar-dropdown {
                flex-direction: column;
            }
        }

        
        @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
            

            #HW_badge {
                margin-left: 5px !important;
            }

            #recent_news span:first-child {
                margin-left: 20px;
            }

            #seda-navbar {
                padding-top: 24px;
                min-height: 72px;
            }

            #nav-search-part-form {
                top: 16px;
            }

            .ls-section {
                top: 18px;
            }

            .nav-bar-dropdown {
                margin-left: -120px;
                top: 36px;
            }

            .make-snap-header {
                height: 56px !important;
            }

            .snap-card img {
                width: 72px;
                height: 60px;
            }

            .feed-item img {
                width: 80px !important;
                height: 80px !important;
            }
        }

        

        /*the container must be positioned relative:*/
        .autocomplete {
            position: relative;
        }

        .autocomplete-items {
            position: absolute;
            border: 1px solid #d4d4d4;
            z-index: 10000000;
            top: 100%;
            left: 0;
            right: 0;
        }

        .autocomplete-items div {
            padding: 10px;
            cursor: pointer;
            background-color: #fff;
        }

        /*when hovering an item:*/
        .autocomplete-items div:hover {
            background-color: #FF761A;
        }

        /*when navigating through the items using the arrow keys:*/
        .autocomplete-active {
            background-color: #FF761A !important;
            color: #ffffff;
        }

        .search-part-package-section .autocomplete-items {
            top: 39px !important;
        }
        

        .flex-align-center {
            display: flex !important;
            justify-content: center;
            align-items: center;
        }

        #remaining-instaparts {
            background: #3a5c84;
            width: fit-content;
            height: 22px;
            color: #fff;
            min-width: 40px;
            display: none;
            justify-content: center;
            align-items: center;
            border-radius: 50px;
            margin-left: 8px;
        }
    
    @supports (-webkit-touch-callout: none) {
    
    #seda-navbar .r-menu-btn {
    
        padding-bottom: 6px;
    
         }
    }
    </style>

</head>
<body class="
  landing
">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N2DMH7J"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
	<div id="sticky-header">
        
        <nav class="navbar navbar-default" id="seda-navbar">
            <a id="nav-logo-link" href="/" >
                <img src="/static/img/snapeda_logo_260.png" width="200" alt="SnapMagic Search logo" title="SnapMagic Search">
            </a>
            <span class="r-menu-btn">☰</span>
            <div class="nav-bar-items" >
                <div>
                    <a href="/about" class="nav-bar-item" > About </a>
                </div>
                <div>
                    <div class="nav-bar-item nav-bar-item-dd">
                        <span class='nav-item-text'>For Engineers </span>
                        <span class='symbol-down'>
                            &gt;
                        </span>

                        
                        <div class='nav-bar-dropdown'>
                            <a class='dropdown-option' href='/instabuild/'>
                                Build Parts
                            </a>
                            <a class='dropdown-option' href='/instapart/'>
                                Request Parts
                            </a>
                            <a class='dropdown-option' href='/discover/'>
                                Browse Parts
                            </a>
                            <a class='dropdown-option' href='/pricing/'>
                                Pricing
                            </a>
                            <a class='dropdown-option' href='/questions/'>
                                Q &amp; A
                            </a>
                            <a class='dropdown-option' href='/pcb-manufacturing/'>
                                PCB Suppliers
                            </a>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="nav-bar-item nav-bar-item-dd">
                        <span class='nav-item-text'>For Part Vendors </span>
                        <span class='symbol-down'>
                            &gt;
                        </span>
                        
                        <div class='nav-bar-dropdown'>
                            <a class='dropdown-option' href='/instapublish/'>
                                Publish
                            </a>
                            <a class='dropdown-option' href='https://mediakit.snapeda.com/kit2023' target="_blank">
                                Media Kit
                            </a>
                            <a class='dropdown-option' href="https://insights.snapeda.com/">
                                SnapInsights
                            </a>
                            <a class='dropdown-option' href="/get-cad-models">
                                Get CAD Models
                            </a>
                            <a class='dropdown-option' href="https://insights.snapeda.com/syndication">
                                Syndication Program
                            </a>
                            <a class='dropdown-option' href="/about/#contact_us">
                                Contact Us
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <form id="nav-search-part-form" action="/search/?q=" method="GET">
                <div id="nav-searchbar-wrapper" class="search-wrapper searchbox-big">
                    <input id="searchbar-input" type="text" name="q" class="search-query search-input" placeholder="Search Parts" value="" required="">
                    <input class="search-type-top" type="hidden" name="search-type" value="parts" >
                    
                    <button id="nav-search-btn" type="submit" >
                        <i class="fa fa-search btn-search-icon"></i>
                        <span class="btn-search-text">Search Parts</span>
                    </button>
                </div>
            </form>
            <div class='ls-section'>
                <a href="/account/login?next=/" class="ls-btn login-btn">
                    Log In
                </a>
                <a href="/account/signup/?next=/" class="ls-btn signup-btn">
                    Sign Up
                </a>
            </div>
        </nav>
        <div style='padding-bottom:80px'></div>
        
	    <div id="base">
	        <div id="left_tabs">
	             
	        </div>
	        <div id="right_tabs" >
	             
	        </div>
	        
	            <div id="body">
	                <div class="row-fluid" id="subnav-outer">
	                    <div class="pull-right" id="subnav-inner">
                            
                        </div>
	                </div>
	                


<!-- Import CSS and Bootstrap 4.0 -->

<link rel="stylesheet" href="//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&amp;lang=en" />
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"> 
<link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Muli" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<style>

    .s-heading-1 {
        font-family: Open Sans;
        font-style: normal;
        font-weight: 800;
        font-size: 48px;
        line-height: 72px;   
        letter-spacing: 0.05em; 
        color: #000000;
    }

    .s-heading-1-md {
        font-weight: 800;
        font-size: 32px;
        line-height: 44px;
        letter-spacing: 0.04em;
    }

    .s-heading-description {
        font-family: Open Sans;
        font-style: normal;
        font-weight: normal;
        font-size: 18px;
        line-height: 24px;
        letter-spacing: 0.05em;
        color: #000000;
    }

    .font-12 {
        font-size: 12px;
        line-height: 16px;
    }

    input:-webkit-autofill,
    input:-webkit-autofill:hover, 
    input:-webkit-autofill:focus, 
    input:-webkit-autofill:active  {
        -webkit-box-shadow: 0 0 0 30px white inset !important;
        box-shadow: 0 0 0 30px white inset !important;
    }

    .s-heading-2 {
        font-family: Open Sans;
        font-style: normal;
        font-weight: bold;
        font-size: 24px;
        line-height: 33px;
        letter-spacing: 0.04em;
        color: #000000;

    }

    .bg-off-white {
        background: #FAFAFA;
    }

    .marquee span {
        display: flex;
        padding-left: 100%;
        animation: marquee 10s linear infinite;
    }

    .w-90 {
        width: 90% !important;
    }

    .snap-btn { 
		height: 46px;
		background: #FF761A;
		border: 1px solid #FF761A;
		box-sizing: border-box;
		border-radius: 12px;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 24px;
		font-family: Open Sans;
		font-style: normal;
		font-weight: bold;
		font-size: 16px;
		line-height: 24px; 
		text-align: center;
		color: #FFFFFF !important;
		text-decoration: none !important; 
	}

    @keyframes marquee {
        0% {
            transform: translate(0, 0);
        }
        100% {
            transform: translate(-100%, 0);
        }
    }

    .mt24 { margin-top: 24px !important;}
	.mb24 { margin-bottom: 24px !important;}
	.ml24 { margin-left: 24px !important;}
	.mr24 { margin-right: 24px !important;}

	.mt64 { margin-top: 64px !important;}
	.mb64 { margin-bottom: 64px !important;}
	.ml64 { margin-left: 64px !important;}
	.mr64 { margin-right: 64px !important;}

	.mt40 { margin-top: 40px !important;}
    .mb40 { margin-bottom: 40px !important;}
    .ml40 { margin-left: 40px !important;}
    .mr40 { margin-right: 40px !important;}

	.mt32 { margin-top: 32px !important;}
    .mb32 { margin-bottom: 32px !important;}
    .ml32 { margin-left: 32px !important;}
    .mr32 { margin-right: 32px !important;}

	.mt88 { margin-top: 88px !important;}
    .mb88 { margin-bottom: 88px !important;}
    .ml88 { margin-left: 88px !important;}
    .mr88 { margin-right: 88px !important;}

	.mt48 { margin-top: 48px !important;}
    .mb48 { margin-bottom: 48px !important;}
    .ml48 { margin-left: 48px !important;}
    .mr48 { margin-right: 48px !important;}

	.mt24 { margin-top: 24px !important;}
    .mb24 { margin-bottom: 24px !important;}
    .ml24 { margin-left: 24px !important;}
    .mr24 { margin-right: 24px !important;}

	.pt24 { padding-top: 24px !important;}
    .pb24 { padding-bottom: 24px !important;}
    .pl24 { padding-left: 24px !important;}
    .pr24 { padding-right: 24px !important;}

	.pt48 { padding-top: 48px !important;}
    .pb48 { padding-bottom: 48px !important;}
    .pl48 { padding-left: 48px !important;}
    .pr48 { padding-right: 48px !important;}

	.pt24 { padding-top: 24px !important;}
	.pb24 { padding-bottom: 24px !important;}
	.pl24 { padding-left: 24px !important;}
	.pr24 { padding-right: 24px !important;}

	.pt64 { padding-top: 64px !important;}
	.pb64 { padding-bottom: 64px !important;}
	.pl64 { padding-left: 64px !important;}
	.pr64 { padding-right: 64px !important;}

	.pt40 { padding-top: 40px !important;}
    .pb40 { padding-bottom: 40px !important;}
    .pl40 { padding-left: 40px !important;}
    .pr40 { padding-right: 40px !important;}

	.pt32 { padding-top: 32px !important;}
    .pb32 { padding-bottom: 32px !important;}
    .pl32 { padding-left: 32px !important;}
    .pr32 { padding-right: 32px !important;}

	.pt88 { padding-top: 88px !important;}
    .pb88 { padding-bottom: 88px !important;}
    .pl88 { padding-left: 88px !important;}
    .pr88 { padding-right: 88px !important;}
</style>

 
<div class='container'>
	<header class="home-feed" >
		<div class="d-flex align-items-center flex-column justify-content-center z-1"> 
			<div >
			<h1 class="s-heading-1 home-feed-h1"  >
				Design electronics in a snap.
			</h1>
			<span class="s-heading-description home-feed-description mt-3">
				Download free symbols, footprints, &amp; 3D models for millions of electronic components	
			</span>
			</div>
			<form class="explore_search w-100" action="/search/" method="GET" novalidate=""> 
			<div class="section-search-part mt-5 searchbox-parts col-md-12" >  
				<div class="col-md-9 ssp-input-section autocomplete">
					<input type="text" name="q" class="search-input explore-search" placeholder="Search by part number or keywords" required=""  id="search_autocomplete" autocomplete="off">
					<input class="search-type search-autocom-submit" type="hidden" name="search-type" value="parts">
				</div>
				<div class="col-md ssp-input-btn-section">
					<button type="submit" class="search-btn w-100 search-autocom-submit" >
					<i class="fa fa-search"></i> 
					<span> Search Parts </span>
				</button>
				</div>
			</div>
			</form>
			
			<div class="example-section">
				<div class="font-12 font-weight-bold mr-2">
					Or see examples:
				</div>
				<div class='d-flex'>
					<a class="example_search text-decoration-none text-dark pl-3 pr-3 pt-2 pb-2  font-12" data-term="USB Type C" href="/search/?q=USB+Type+C">
						USB Type C
					</a>
	
					<a class="example_search text-decoration-none text-dark pl-3 pr-3 pt-2 pb-2 font-12" data-term="SMA Connector" href="/search/?q=STM32">
						STM32
					</a> 
					
					<a class="example_search text-decoration-none text-dark pl-3 pr-3 pt-2 pb-2 font-12" data-term="Bluetooth Module" href="/search/?q=Bluetooth+Module">
						Bluetooth Module
					</a>              
				</div>		
			</div>
		</div> 
		<img src="/static/img/home/<USER>" class="home-feed-image" >  
	</header>  
</div>


<div class='s-heading-2 pt64 pb40 w-100 text-center bg-off-white'>
	<h3 class='s-heading-2 mb40 w-100 text-center'>Trusted by over 2 million engineers at these companies & more</h3>
	<div class='w-100 marquee' id='teac'> 
	</div>
</div>


<div class='d-flex bg-off-white justify-content-center'>
	<div class='make-snap-header d-flex justify-content-center align-items-center mt32 pt32 pb32 col-md-8 text-center row'>
		Let's make your design a Snap <img src='/static/img/home/<USER>' style='width: 32px;' class='ml-3'>
	</div>
</div>

<div class='d-flex w-100 justify-content-center'>
	<div class='d-flex justify-content-center w-100 overflow-hidden'> 
		<div class='make-snap-body col-md-8 row'> 
			<div class='make-snap-img-section col-md-8'>
				<img src="/static/img/home/<USER>">
			</div>
			<div class='col-md make-snap-detail-section'>
				<div class="snap-card bg-off-white">
					<div class="d-flex align-items-center">
						<img src="/static/img/home/<USER>">
						<span class="pl-3 snap-card-heading"  >
							Focus on Design
						</span>
					</div>
					<div class="pt-4 snap-card-text" style='display:block'>
						Download millions of free symbols & footprints instantly to design better products faster.
					</div>
				</div>

				<div class="snap-card" id='discover-parts'>
					<div class="d-flex align-items-center">
						<img src="/static/img/home/<USER>">
						<span class="pl-3 snap-card-heading"  >
							Discover new components
						</span>
					</div>
					<div class="pt-4 snap-card-text">
						Find the best components for your designs, complete with datasheets, specs, pricing, & more.
					</div>
				</div>
				
				<div class="snap-card" id="pcb-pair">
					<div class="d-flex align-items-center">
						<img src="/static/img/home/<USER>">
						<span class="pl-3 snap-card-heading"  >
							Pair with your PCB design tool
						</span>
					</div>
					<div class="pt-4 snap-card-text"> 
						Download from the web, or right from your PCB design tool using our <a class='text-decoration-none text-dark' href='/plugins/'>plugins</a>!
					</div>
				</div>
			</div>
		</div>	
	</div>
</div>

<div class="mob-gif" >
    <img src="/static/img/home/<USER>">
</div>


<div class='w-100 container'>
	<div class='d-flex justify-content-center row' id='export-to'>
		<div class='col-md-4 d-flex justify-content-center align-items-center'>	
			<h3 class='export-to-text'>
				Works with the PCB tool you already use	
			</h3>
		</div>
		<div class="w-100 bg-white col-md format-div" >
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/altium.png" alt="Altium"></span>
				<div class="export-to-name">
					Altium
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/companylogos/Fusion.svg" alt="Autodesk Fusion"></span>
				<div class="export-to-name">
					Autodesk Fusion
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/cadence.png" alt="Cadence"></span>
				<div class="export-to-name export-item-lt">
					Cadence Allegro
				</div>			
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/circuitstudio.png" alt="CircuitStudio"></span>
				<div class="export-to-name">
					CircuitStudio
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/CR5000.jpg" alt="CR5000"></span>
				<div class="export-to-name">
					CR-5000
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/CR8000.jfif" alt="CR8000"></span>
				<div class="export-to-name">
					CR-8000
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="https://snapeda.s3.amazonaws.com/static/img/designspark_logo.webp" alt="DesignSpark"></span>
				<div class="export-to-name">
					DesignSpark
				</div>
			</span><span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/diptrace.png" alt="DipTrace"></span>
				<div class="export-to-name">
					DipTrace
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/eagle.png" alt="Eagle"></span>
				<div class="export-to-name">
					Eagle
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/easy-pc.png" alt="Easy-PC"></span>
				<div class="export-to-name">
					Easy-PC
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/eCADSTAR.png" alt="eCADSTAR"></span>
				<div class="export-to-name">
					eCADSTAR
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/expressPCB-plus.png" alt="expressPCB"></span>
				<div class="export-to-name">
					ExpressPCB Plus
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/kicad.png" alt="KiCad" style="width: 100px;"></span>
				<div class="export-to-name">
					KiCad
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/orcad.png" alt="OrCAD"></span>
				<div class="export-to-name">
					OrCAD
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/pads.png" alt="PADS"></span>
				<div class="export-to-name">
					PADS
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/p-cad.png" alt="P-CAD!"></span>
				<div class="export-to-name">
					P-CAD
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/pcb123.png" alt="PCB123"></span>
				<div class="export-to-name">
					PCB123
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/proteuslogo.png" alt="Proteus"></span>
				<div class="export-to-name">
					Proteus
				</div>
			</span> 
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/pulsonix.png" alt="Pulsonix"></span>
				<div class="export-to-name">
					Pulsonix
				</div>
			</span>
			<span class="col-md-3 export-item"  >
				<span class="img-span"><img src="/static/img/eda/target3001.png" alt="TARGET 3001!"></span>
				<div class="export-to-name">
					TARGET 3001!
				</div>
			</span>
		</div>
	</div>
</div>



<div class='bg-off-white mt88'>
	<div class='container'>
		<div class='row section-bfe'>
			<div class='col-md'>
				<img src="/static/img/home/<USER>" />
			</div>
			<div class='col-md'>
				<div class='bfe-heading'> Take back your time </div>
				<div class='bfe-text'>
					Download millions of free symbols & footprints instantly to design better products faster.
				</div>
				<div>
					<a class="snap-btn mt24" href="/home">
						Learn More &nbsp;&nbsp; &gt;
					</a>
				</div>
			</div>
		</div>
	</div>
</div>
<div>
	<div class='container'>
		<div class='row section-bfe'>
			<div class="col-md justify-content-center flex-column align-items-center ipf-mobile mb-4">
				<img src="/static/img/home/<USER>">
				<div class="d-flex"> 
					<img src="/static/img/home/<USER>" class="mr-3">
					<img src="/static/img/home/<USER>">
				</div>			
			</div> 
			<div class='col-md'>
				<div class='bfe-heading'> Crush Deadlines </div>
				<div class='bfe-text'>
					Need a part that's not in our library yet? Request it, and our engineers will create it for you. 
					<br /> 
					<br />
					Or use InstaBuild, our computer-vision tool, to build them in minutes, all free.
				</div>
				<div>
					<a class="snap-btn mt24" href="/instapart/">
						Learn More &nbsp;&nbsp; &gt;
					</a>
				</div>
			</div>
			<div class="col-md justify-content-center flex-column align-items-center ipf-desktop">
				<img src="/static/img/home/<USER>">
				<div class="d-flex"> 
					<img src="/static/img/home/<USER>" class="mr-5">
					<img src="/static/img/home/<USER>">
				</div>			
			</div> 
		</div>
	</div>
</div>
<div class='bg-off-white'>
	<div class='container'>
		<div class='row section-bfe'>
			<div class='col-md'>
				<img src="/static/img/home/<USER>" />
			</div>
			<div class='col-md'>
				<div class='bfe-heading'> Prevent Errors </div>
				<div class='bfe-text'>
					See whether a footprint is manufacturable with real-time automated checks. Our patented verification technology will give you instant transparency.
				</div>
				<div>
					<a class="snap-btn mt24" href="https://blog.snapeda.com/2015/09/19/part-verification-on-snapeda/">
						Learn More &nbsp;&nbsp; &gt;
					</a>
				</div>
			</div>
		</div>
	</div>
</div>


<div>
	<div class='container'> 
		<div class='largest-community-heading'>
			The largest community of electronics designers
		</div>
		<div class='d-flex row justify-content-between'>
			<div class='col-md lcs-card-overlay'>
				<div class='lcs-card'>
					<img src='/static/img/home/<USER>' class="ml-3">
					<div class='ml-4'>
						<div class='lcs-card-number'>1.1M</div>
						<div class='lcs-card-text mt-2'>Engineers trust SnapMagic Search</div>
					</div>
				</div>
			</div>
			<div class='col-md lcs-card-overlay'>
				<div class='lcs-card'>
					<img src='/static/img/home/<USER>' class="ml-3">
					<div class='ml-4'>
						<div class='lcs-card-number'>10M</div>
						<div class='lcs-card-text mt-2'>Parts in our library</div>
					</div>
				</div>
			</div>
			<div class='col-md lcs-card-overlay'>
				<div class='lcs-card'>
					<img src='/static/img/home/<USER>' class="ml-3">
					<div class='ml-4'>
						<div class='lcs-card-number'>391K</div>
						<div class='lcs-card-text mt-2'>Products built with SnapMagic Search Parts</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class='bg-off-white section-bfe-explore'>
	
	<div id="new-items-feed">
		<div class="feed-heading mt32 mb32"> 
			New Parts Added

		</div>
		<div id="feed-newparts-hp" class="tab-content tab-content-active non-flex overflow-hidden" data-tab-id="0"> 
			<ul class="ul-feed"> 
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/10142344-207KLF/Amphenol%20ICC%20%28FCI%29/view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
                                <img src="https://snapeda.s3.amazonaws.com/partpics/Amphenol_ICC_-FCI-_10142344-207KLF_64372.png" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								10142344-207KLF
							</div>
							<div class="manufacturer-name w-100">
								by Amphenol ICC (FCI)
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								13 minutes
							</div> 
						</div>
					</a>
				</li>
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/FX23-100S-0.5SVB/Hirose/view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
                                <img src="https://snapeda.s3.amazonaws.com/partpics/Hirose_FX23-100S-0.5SVB_59727.jpg" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								FX23-100S-0.5SVB
							</div>
							<div class="manufacturer-name w-100">
								by Hirose
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								14 minutes
							</div> 
						</div>
					</a>
				</li>
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/FX23-100S-0.5SV10B/Hirose/view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
                                <img src="https://snapeda.s3.amazonaws.com/partpics/Hirose_FX23-100S-0.5SV10B_56846.jpg" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								FX23-100S-0.5SV10B
							</div>
							<div class="manufacturer-name w-100">
								by Hirose
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								15 minutes
							</div> 
						</div>
					</a>
				</li>
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/FX23-100S-0.5SV10%2820%29/Hirose%20Connector/view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
							<img src="/static/img/default_avatar/SnapMagic-logo-icon.svg" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								FX23-100S-0.5SV10(20)
							</div>
							<div class="manufacturer-name w-100">
								by Hirose Connector
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								15 minutes
							</div> 
						</div>
					</a>
				</li>
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/FX23-100S-0.5SV10%2801%29/Hirose%20Connector/view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
							<img src="/static/img/default_avatar/SnapMagic-logo-icon.svg" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								FX23-100S-0.5SV10(01)
							</div>
							<div class="manufacturer-name w-100">
								by Hirose Connector
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								15 minutes
							</div> 
						</div>
					</a>
				</li>
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/FX23-100S-0.5SV%2820%29/Hirose%20Electric%20Co%20Ltd/view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
                                <img src="https://snapeda.s3.amazonaws.com/partpics/fx23-100s-0_5sv_20__SPL_FIR.jpg" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								FX23-100S-0.5SV(20)
							</div>
							<div class="manufacturer-name w-100">
								by Hirose Electric Co Ltd
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								15 minutes
							</div> 
						</div>
					</a>
				</li>
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/FX23-100S-0.5SV%2820%29/Hirose/view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
                                <img src="https://snapeda.s3.amazonaws.com/partpics/Hirose_FX23-100S-0.5SV-20-_70033.jpg" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								FX23-100S-0.5SV(20)
							</div>
							<div class="manufacturer-name w-100">
								by Hirose
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								15 minutes
							</div> 
						</div>
					</a>
				</li>
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/FX23-100S-0.5SV/Hirose%20Electric%20Co%20Ltd/view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
                                <img src="https://snapeda.s3.amazonaws.com/partpics/Hirose_Electric_Co_Ltd_FX23-100S-0.5SV_78458.JPG" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								FX23-100S-0.5SV
							</div>
							<div class="manufacturer-name w-100">
								by Hirose Electric Co Ltd
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								15 minutes
							</div> 
						</div>
					</a>
				</li>
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/FX23-100S-0.5SV/Hirose/view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
                                <img src="https://snapeda.s3.amazonaws.com/partpics/Hirose_FX23-100S-0.5SV_49804.jpg" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								FX23-100S-0.5SV
							</div>
							<div class="manufacturer-name w-100">
								by Hirose
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								15 minutes
							</div> 
						</div>
					</a>
				</li>
				
				<li class="feed-item pr-4">
					<a class="text-decoration-none w-100 d-flex" href="/parts/ADG884BRMZ-REEL/Analog%20Devices%20Inc./view-part/"> 
						<div class='d-flex justify-content-center align-items-center'>
							
                                <img src="https://snapeda.s3.amazonaws.com/partpics/Analog_Devices_Inc._ADG884BRMZ-REEL_91696.jpg" alt="part_image" style="width: 80px;">
							

						</div>  
						<div class='w-100 pl-4'>
							<div class="part-name 2-100 "> 
								ADG884BRMZ-REEL
							</div>
							<div class="manufacturer-name w-100">
								by Analog Devices Inc.
								<i class="navigate-right"> &gt; </i>
							</div>
							<div class="time-stamp request-item">
								18 minutes
							</div> 
						</div>
					</a>
				</li>
				
			</ul>
		</div>
	</div>
	<div class='container'>
		<div class='row'>
			<div>
				<div class='col-sm-5 bfe-section pt32'>
					<h2 class='s-heading-1-medium pl-3'> Built for engineers of the future 🚀 </h2>
					<div class='mt32 pl-3 bfe-text'>
						At SnapMagic Search, we believe in a world where engineers can spend more time innovating. 
						<br />
						<br />
						We break down barriers so they can bring new products to life faster.
						<br />
						<br />
						By providing the building blocks needed to design new products, we aim to help improve the world with more impactful products. 
						<br />
						<br />
					</div>
					<a class="snap-btn col-md-6" href="/home/">
						Explore Now &nbsp;&nbsp;&gt;
					</a>
				</div>
			</div> 
		</div>
	</div> 
</div>


<div>
    <div class="container">
        <div class="col-md">
            <div class="ml48 mt88 mb48 manufacturer-heading">
                Instant access to millions of models from <br>
                over <strong>2,500 manufacturers </strong>😍
            </div>
        </div>
    </div>
    <div class="manufacturers">
		<span class='partners-info'>
			<span class="manufacturer-logo">
				<img src="https://snapeda.s3.amazonaws.com/media/homepage/newupdated/vishay.png">
			</span>
			<div>Vishay</div>
		</span>
		<span class='partners-info'>
			<span class="manufacturer-logo">
				<img src="https://snapeda.s3.amazonaws.com/media/homepage/newupdated/texas.png">
			</span>
			<div>Texas Instrument</div>
		</span>
		<span class='partners-info'>
			<span class="manufacturer-logo">
				<img src="https://snapeda.s3.amazonaws.com/media/homepage/newupdated/gan.png">
			</span>
			<div>Gan Systems</div>
		</span>
		<span class='partners-info'>
			<span class="manufacturer-logo">
				<img src="https://snapeda.s3.amazonaws.com/media/homepage/newupdated/Linear.jpg">
			</span>
			<div>Linear Technology</div>
		</span>
		<span class='partners-info'>
			<span class="manufacturer-logo">
				<img src="https://snapeda.s3.amazonaws.com/media/homepage/analog_devices.webp">
			</span>
			<div> Maxim Integrated </div>
		</span>
		<span class='partners-info'>
			<span class="manufacturer-logo">
				<img src="https://snapeda.s3.amazonaws.com/media/homepage/GCT.png">
			</span>
			<div>GCT</div>
		</span>
		<span class='partners-info'>
			<span class="manufacturer-logo">
				<img src="https://snapeda.s3.amazonaws.com/media/homepage/newupdated/TE.png">
			</span>
			<div>TE Connectivity</div>
		</span>
		<span class='partners-info'>
			<span class="manufacturer-logo">
				<img src="https://snapeda.s3.amazonaws.com/media/homepage/newupdated/CUI.png">
			</span>
			<div> CUI INC</div>
		</span>
		<span class='partners-info'>
			<span class="manufacturer-logo">
				<img src="https://snapeda.s3.amazonaws.com/media/homepage/newupdated/Recom.png">
			</span>
			<div> RECOM </div>
		</span>
	</div>
</div>
<div class='w-100 marquee' id='instant-access'> 
</div>
 

<div class='container'>
	<div class='row testimonials-section'>
		
		<div>
			<div id="slider" class="carousel slide" data-ride="carousel">
				<ol class="carousel-indicators">
				<li data-target="#slider" data-slide-to="0" class="active"></li>
				<li data-target="#slider" data-slide-to="1"></li>
				<li data-target="#slider" data-slide-to="2"></li>
				<li data-target="#slider" data-slide-to="3"></li>
				</ol>
				<div class="carousel-inner">
					<div class="carousel-item active">
						<img src="/static/img/home/<USER>" class='person-img'>
						<div class='m-5 recommendation-text'>
							“I used SnapMagic Search while working in Samsung Research America. I had to switch from one design tool to another and I used SnapMagic Search to get most of the footprints for my design. It helped save a lot of time before starting the real PCB design. I would highly recommend trying SnapMagic Search not only for footprints but also to find other product information.”
						</div>
						<div class='recommendation-company ml-5 mr-5'>
							<strong>Md. Nazmus Sahadat</strong>, Graduate Research Assistant GT-Bionics Lab, School of Electrical and Computer Engineering, <a href="http://www.gtbionics.org/" class='text-decoration-none text-dark'>Georgia Institute of Technology </a>
						</div>
					</div>
					<div class="carousel-item">
						<img src="/static/img/home/<USER>" class='person-img'>
						<div class='m-5 recommendation-text'>
							“SnapMagic Search was a lifesaver in our most recent board design project. We were working on a very tight deadline and didn't have footprints for several of the components that we needed to use. SnapMagic Search saved us many hours compared with creating footprints by hand. Using SnapMagic Search's data also gave us added confidence that the boards we were paying rush charges for could be processed quickly and assembled correctly by our PCBA vendor.”
						</div>
						<div class='recommendation-company ml-5 mr-5'>
							<strong>Kwindla Hultman Kramer</strong>, Founder - <a href="http://www.pluot.co" class='text-decoration-none text-dark'>Pluot Communications</a>
						</div>
					</div>
					<div class="carousel-item">
						<img src="/static/img/home/<USER>" class='person-img'>
						<div class='m-5 recommendation-text'>
							“I've used SnapMagic Search for footprints and symbols for complex microcontrollers and saved myself hours of CAD time per part. At Screaming Circuits, we build dozens of different board designs each day, and footprint errors are one of the most common design problems we see. I've started recommending SnapMagic Search to our customers to help them avoid those problems.”
						</div>
						<div class='recommendation-company ml-5 mr-5'>
							<strong>Duane Benson</strong>, Chief Technology Champion/Marketing Manager, <a href='https://www.screamingcircuits.com/' class='text-decoration-none text-dark'>Screaming Circuits</a>
						</div>
					</div>
					<div class="carousel-item">
						<img src="/static/img/home/<USER>" class='person-img'>
						<div class='m-5 recommendation-text'>
							“We use SnapMagic Search to design the circuits that power teaBOT. It's been a huge help to develop circuits faster. We don't have an EE in-house, so as a startup we're all doing many tasks. SnapMagic Search reduces time and cost.”
						</div>
						<div class='recommendation-company ml-5 mr-5'>
							<strong>Rehman Merali</strong>, CEO - <a href="http://www.teabot.com" class='text-decoration-none text-dark'>teaBOT</a>
						</div>
					</div> 
				</div> 
			</div>
		</div>
	</div>
</div>


<div class='d-flex social-section-bg' >
	<div class='container'>
		<div class='row d-flex'>
			<div class='col-md join-snapeda-section'>
				<div id='join-snapeda'> Join SnapMagic Search today</div>
				<div class='d-flex-column mt-4'>
					<a href="/account/signup/" class='social-button btn-email mt-3'>
						<i class="fa fa-envelope mr-2"></i>
						<span>Join by Email</span>
					</a>
					<a class='social-button btn-linkedin mt-3' href="/login/linkedin/?next=">
						<i class="fa fa-linkedin mr-2"></i>
						<span>Join by Linkedin</span>
					</a>
					<a class='social-button btn-twitter mt-3' href="https://www.snapeda.com/login/twitter/">
						<i class="fa fa-twitter mr-2"></i>
						<span>Join by Twitter</span>
					</a>
				</div> 
			</div>
			<div class='col-md'>
				<img src='/static/img/home/<USER>' alt="join_snapeda" class='img-social-section'>
			</div> 
		</div>
	</div>
</div> 



<!--------------------------------------------enddd------------------------------------------------------>


	            </div>
	        
	    </div>
	</div>
	<div id="sticky-footer">

        



<footer id="site-footer">
    <div class="container">
        <div class="footer-row links">
            <div class="footer-column">
                <h3>SnapMagic</h3>
                <ul>
                    <li><a href="/about/">About</a></li>
                    <li><a href="/about/#contact_us">Contact</a></li>
                    <li><a href="/pricing/">Pricing</a></li>
                    <li><a href="https://careers.snapeda.com" target="_blank">Careers</a></li>
                    <li><a href="https://changelog.snapeda.com/" target="_blank">💎 What's new</a></li>
                    <!--<li><a href="/jobs/">Jobs</a></li>!-->
                </ul>
            </div>
            <div class="footer-column">
                <h3>Community</h3>
                <ul>
                    <li><a href="/profiles/">Our Community</a></li>
                    <li><a href="/questions/">Q &amp; A</a></li>
                    <li><a href="https://blog.snapeda.com/">Blog</a></li>
                    <li><a href="/made-with-snapeda/">Made With SnapMagic Search</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>Product</h3>
                <ul>
                    <li><a href="/parts/">Parts Library</a></li>
                    <li><a href="/instapart/">InstaPart</a></li>
                    <li><a href="/instabuild/">InstaBuild</a></li>
                    <!-- <li><a href="/part-requests/">Recent Requests</a></li> -->
                    <li><a href="/plugins/">Plugins</a></li>
                    
                    <li><a href="/get-api/">API</a></li>
                    <!-- <li><a href="/feature/request/">Send Feature Request</a></li> -->
                    <li><a href="/pcb-manufacturing/">PCB Suppliers</a></li>
                    <li><a href="https://support.snapmagic.com/en/articles/5599920-snapeda-desktop-app">SnapMagic Search Desktop App</a></li>
                </ul>
            </div>
            <!--  Orcad and KiCad to be updated -->
            <div class="footer-column">
                <h3>Tools</h3>
                <ul>
                <li><a href="/allegro/">Allegro</a></li>
                <li><a href="/altium-libraries/">Altium</a></li>
                <li><a href="/autodeskfusion360">Autodesk Fusion</a></li>
                <li><a href="/circuitstudio/">CircuitStudio</a></li>
                <li><a href="/cr-5000">CR-8000/CR-5000</a></li>
                <li><a href="/designspark/">DesignSpark</a></li>
                <li><a href="/diptrace/">DipTrace</a></li>
                <li><a href="/eagle/">Eagle</a></li>
                <li><a href="/easy-pc">Easy-PC</a></li>
                <li><a href="/ecadstar">eCADSTAR</a></li>
                <li><a href="/expresspcbplus">ExpressPCB Plus</a></li>
                <li><a href="/kicad/">KiCad</a></li>
                <li><a href="/orcad/">OrCAD</a></li>
                <li><a href="/pads/">PADS &amp; DxDesigner</a></li>
                <li><a href="/pcb123-libraries/">PCB123</a></li>
                <li><a href="/p-cad/">P-CAD</a></li>
                <li><a href="/proteus/">Proteus</a></li>
                <li><a href="/pulsonix">Pulsonix</a></li>
                <li><a href="/target-3001/">Target 3001!</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>Support</h3>
                <ul>
                    <li><a href="https://support.snapeda.com/" target="_blank">FAQ</a></li>
                    <li><a href="/about/import/">How to Import</a></li>
                    <li><a href="/standards/">Standards</a></li>
                    <li><a href="/cdn-cgi/l/email-protection#d2bbbcb4bd92a1bcb3a2b7b6b3fcb1bdbf">Contact Us</a></li>
                    <li><a href="https://support.snapeda.com/en/articles/5948134-design-fundamentals">Design Resources</a></li>
                    <li><a href="/about/terms/">Terms Of Service</a></li>
                    <li><a href="/about/privacy/">Privacy</a></li>
                </ul>
            </div>

        </div>

        <div id="home-newsletter-signup" class="footer-column social">
            <div id="mc_embed_signup">
                <h3 style="display: none;" id="title-newsletter">Join Our Newsletter</h3>
                <form  style="display: none;" action="//092552f0.sibforms.com/serve/MUIEANXky0hiqpnWKig9cHQOdX7_HLNrniK4d-bmzT6cm7SqyaucLhG10uf5lupdoyWC7dfBaWvBmJJwVHZQ5gMV_1H74H9SDO-4ov-cvjDpCEhpJRc1E7rJD1iHtbmjPkY7c5X3WCQyK-DGmI9V9wcqkxTOAYjCNj_KI13-6LxhR6y1OBvWex7GZmQSjFyV7TaYr064RjBrhHvI"
                    method="post"
                    id="sib-form"
                    class="validate"
                    name="mc-embedded-subscribe-form"
                    target="_blank"
                    data-type="subscription">
                    <div id="sib-form-container" class="sib-form-container">
                        <div id="error-message" class="sib-form-message-panel" style="margin-bottom:10px;">
                            <div class="sib-form-message-panel__text sib-form-message-panel__text--center">
                                <svg style="display:none !important" viewBox="0 0 512 512" class="sib-icon sib-notification__icon">
                                    <path d="M256 40c118.621 0 216 96.075 216 216 0 119.291-96.61 216-216 216-119.244 0-216-96.562-216-216 0-119.203 96.602-216 216-216m0-32C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm-11.49 120h22.979c6.823 0 12.274 5.682 11.99 12.5l-7 168c-.268 6.428-5.556 11.5-11.99 11.5h-8.979c-6.433 0-11.722-5.073-11.99-11.5l-7-168c-.283-6.818 5.167-12.5 11.99-12.5zM256 340c-15.464 0-28 12.536-28 28s12.536 28 28 28 28-12.536 28-28-12.536-28-28-28z"
                                    />
                                </svg>
                                <span class="sib-form-message-panel__inner-text">Your subscription could not be saved. Please try again.</span>
                            </div>
                        </div>
                        <div></div>
                        <div id="success-message" class="sib-form-message-panel">
                          <div class="sib-form-message-panel__text sib-form-message-panel__text--center">
                            <svg style="display:none !important" viewBox="0 0 512 512" class="sib-icon sib-notification__icon">
                              <path d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 464c-118.664 0-216-96.055-216-216 0-118.663 96.055-216 216-216 118.664 0 216 96.055 216 216 0 118.663-96.055 216-216 216zm141.63-274.961L217.15 376.071c-4.705 4.667-12.303 4.637-16.97-.068l-85.878-86.572c-4.667-4.705-4.637-12.303.068-16.97l8.52-8.451c4.705-4.667 12.303-4.637 16.97.068l68.976 69.533 163.441-162.13c4.705-4.667 12.303-4.637 16.97.068l8.451 8.52c4.668 4.705 4.637 12.303-.068 16.97z"
                              />
                            </svg>
                            <span class="sib-form-message-panel__inner-text">Thanks for subscribing to the SnapMagic Search newsletter. We&#039;re excited to have you as part of our community.</span>
                          </div>
                        </div>
                        <div></div>
                        <!-- <label for="mce-EMAIL">Keep in Touch! Subscribe to Our Newsletter</label> -->
                        <div class="sib-input sib-form-block">
                            <div class="form__entry entry_block">
                                <div class="form__label-row ">
                                    <div class="entry__field">
                                        <input type="email" value="" name="EMAIL" class="email input" id="mce-EMAIL" placeholder="email address" data-required="true" required style="-webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box;">
                                    </div>
                                </div>
                                <label class="entry__error entry__error--primary" style="font-size: 12px;text-align:left;color:#661d1d;background-color:#ffeded;border-radius:3px;border-color:#ff4949;padding: 0 5px;"></label>
                            </div>
                        </div>
                        <!-- real people should not fill this in and expect good things - do not remove this or risk form bot signups-->
                        <div style="position: absolute; left: -5000px;">
                            <input type="text" name="b_59ee0d7ec121b9b37313fd775_adb224cc06" tabindex="-1" value="">
                        </div>
                        <div class="sib-captcha sib-form-block">
                            <div class="form__entry entry_block">
                                <div class="form__label-row ">
                                    <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script>
                                        function handleCaptchaResponse() {
                                        var event = new Event('captchaChange');
                                        document.getElementById('sib-captcha').dispatchEvent(event);
                                        }
                                    </script>
                                    <div class="g-recaptcha sib-visible-recaptcha" id="sib-captcha" 
                                        data-sitekey="6LfDwKcUAAAAAF3aAYWzseFo_OIJwQXdHv6EIcCK" 
                                        data-callback="handleCaptchaResponse" 
                                        style="transform: scale(0.626);transform-origin: 0 0; margin: 0 auto -25px auto;">
                                    </div>
                                </div>
                                <label class="entry__error entry__error--primary" style="font-size: 12px;text-align:left;color:#661d1d;background-color:#ffeded;border-radius:3px;border-color:#ff4949;padding: 0 5px;"></label>
                            </div>
                        </div>
                        <div class="clear">
                            <!-- <input type="submit" value="Subscribe" name="subscribe" id="mc-embedded-subscribe" class="button"> -->
                            <button class="sib-form-block__button sib-form-block__button-with-loader" 
                                form="sib-form"
                                type="submit"
                                id="mc-embedded-subscribe"
                                style="font-size: 13px;font-weight: 400;line-height: 18px;">
                                <svg style="display:none !important" class="icon clickable__icon progress-indicator__icon sib-hide-loader-icon" viewBox="0 0 512 512">
                                  <path d="M460.116 373.846l-20.823-12.022c-5.541-3.199-7.54-10.159-4.663-15.874 30.137-59.886 28.343-131.652-5.386-189.946-33.641-58.394-94.896-95.833-161.827-99.676C261.028 55.961 256 50.751 256 44.352V20.309c0-6.904 5.808-12.337 12.703-11.982 83.556 4.306 160.163 50.864 202.11 123.677 42.063 72.696 44.079 162.316 6.031 236.832-3.14 6.148-10.75 8.461-16.728 5.01z"
                                  />
                                </svg>
                                Subscribe
                            </button>
                        </div>
                    </div>
                    <input type="text" name="email_address_check" value="" class="input--hidden">
                    <input type="hidden" name="locale" value="en">
                </form>
                <div class="contact-icons">
                    <ul>
                        <li class="social-facebook"><a rel="me" href="https://www.facebook.com/pages/SnapEDA/393047664096498">Facebook</a></li>
                        <li class="social-linkedin"><a rel="me" href="https://www.linkedin.com/company/snapeda">LinkedIn</a></li>
                        <li class="social-twitter"><a rel="me" href="https://twitter.com/SnapEDA">Twitter</a></li>
                    </ul>
                </div>
                <div class="footer-row copyright">
                    <p><i class="fa fa-phone"></i> **************</p>
                    <p><span class="copyright-symbol">©</span> 2013 - 2025 SnapMagic</p>
                </div>
            </div>
        </div>
        <div id="request-ids" style="clear: both; text-align: center; opacity: 0.25; font-size:.75em;color: #232323;">32a790de9708448caf0cff897860fde9</div>
    </div>
</footer>

<script>
  window.REQUIRED_CODE_ERROR_MESSAGE = 'Please choose a country code';

  window.EMAIL_INVALID_MESSAGE = window.SMS_INVALID_MESSAGE = "The information provided is invalid. Please review the field format and try again.";

  window.REQUIRED_ERROR_MESSAGE = "This field cannot be left blank. ";

  window.GENERIC_INVALID_MESSAGE = "The information provided is invalid. Please review the field format and try again.";




  window.translation = {
    common: {
      selectedList: '{quantity} list selected',
      selectedLists: '{quantity} lists selected'
    }
  };

  var AUTOHIDE = Boolean(0);
</script>
<script src="https://sibforms.com/forms/end-form/build/main.js"></script>
<script src="https://www.recaptcha.net/recaptcha/api.js" async defer></script>
    </div>

    <link href='//fonts.googleapis.com/css?family=Open+Sans:300,400,600' rel='stylesheet' type='text/css'>
    <link href="/static/css/core.min.css" rel="stylesheet" type="text/css" media="all" />
    <link rel="stylesheet" type="text/css" href="/static/css/font-awesome.css">
    
    
    <style>
    .social-facebook a {background-image: url(/static/img/icons/face.png);}
    .social-linkedin a{background-image: url(/static/img/icons/linkedin.png);}
    .social-twitter a{background-image: url(/static/img/icons/twitter.png);}
    </style>
    <script type="text/javascript" src="/static/js/core.min.js" charset="utf-8"></script>
    

<script src="/static/js/jquery.unveil.js"></script>
<script>

$(document).ready(function() {
  $("img").unveil(200);
//   $('nav').hide();
});

$('.example_search').click(function(event) {
	term = $(this).attr('data-term');

	var Properties = {
	  'term': term,
	  'userid': '',
	}
	mixpanel.track('Clicked example search', Properties);
});

$('.search-tab-blue').click(function(event) {
    $(".search-tab-blue").removeClass('active');
    $(this).addClass('active');

    if ($(this).hasClass('search-parts')){
        $('.searchbox-parts').removeClass('hidden');
        $('.searchbox-resistor').addClass('hidden');
        $("input.search-type").val('parts');
        $('.search-example-part').removeClass('hidden');
        $('.search-example-package').addClass('hidden');
        $('.explore-search').attr('placeholder', 'Search Over 25 Million Parts');
        $('.home_search').attr('action', '/search')

    }else if ($(this).hasClass('search-packages')){
        $('.searchbox-parts').removeClass('hidden');
        $('.searchbox-resistor').addClass('hidden');
        $("input.search-type").val('packages');
        $('.explore-search').attr('placeholder', 'Search By Package or IPC Name');
        $('.search-example-part').addClass('hidden');
        $('.search-example-package').removeClass('hidden');
        $('.home_search').attr('action', '/search')

    }else if ($(this).hasClass('search-resistor')){
        $('.searchbox-parts').addClass('hidden');
        $('.searchbox-resistor').removeClass('hidden');
        $("input.search-type").val('resistor');
        $('.search-example-part').addClass('hidden');
        $('.search-example-package').addClass('hidden');
        $('.home_search').attr('action', '/search-resistor')

    }
});
</script>

<script>

	var MARQUEE_TEMPLATE = "<span>" +
				           "        <img src='__IMG__' __WIDTH__  __ALT__>" +
				           "</span>";

	var MARQUEE_TEMPLATE_LABEL = "<div class='d-flex justify-content-center align-items-center flex-column'> " + 
								 "    <span class='img-span'> " + 
								 "        <img src='__IMG__' __WIDTH__  __ALT__> " + 
								 "    </span> " + 
								//  "    <div class='mt-3 text-decoration-none text-deark'> __LABEL__ </div> " + 
								 "</div> ";
	
	populateTEAC(); 
	populateManufacturers(); 
	
    $(document).ready(function(){

		addAutoComplete('search_autocomplete');

    	// start marquees
    	$('.marquee').marquee({ 
			duplicated: true,
			startVisible: true,
			duration: 120000,
			gap: 1
		}); 

		
		$('.make-snap-body .snap-card').click(function(event) {
			if(event.currentTarget.id === 'pcb-pair') {
				$('.make-snap-img-section img').attr('src',"/static/img/home/<USER>");
			} else if(event.currentTarget.id === 'discover-parts') {
				$('.make-snap-img-section img').attr('src',"/static/img/home/<USER>");
			} else {
				$('.make-snap-img-section img').attr('src',"/static/img/home/<USER>");
			}
			var currentTarget = event.currentTarget.querySelector('.snap-card-text');
			if($(currentTarget).css('display') === 'none') {
				$('.make-snap-body .snap-card .snap-card-text').css('display', 'none'); 
				$('.make-snap-body .snap-card').removeClass('bg-off-white');
				$(event.currentTarget).addClass('bg-off-white');
				$(currentTarget).css('display','block');
			} else {
				$('.make-snap-body .snap-card .snap-card-text').css('display', 'none'); 
				$('.make-snap-body .snap-card').removeClass('bg-off-white');
			}
		});


		
		if ($('.section-search-part').isInViewport() && $(window).width() > 576) {
			$('#nav-searchbar-wrapper').hide();
		} else {
			$('#nav-searchbar-wrapper').show();
		}
		$(window).on('resize scroll', function() {
			if ($('.section-search-part').isInViewport() && $(window).width() > 576) {
				$('#nav-searchbar-wrapper').hide();
			} else {
				$('#nav-searchbar-wrapper').show();
			}
		});

    });

	
	function populateTEAC() {
		var elements = [
			{name: 'Microsoft'},
			{name: 'General Electric'},
			{name: 'Amazon', width: '90px'},
			{name: 'Google'},
			{name: 'Meditronic', width: '90px'},
			{name: 'Honeywell', width: '90px'},
			{name: 'HP'},
			{name: 'Fitbit', width: '90px'},
			{name: 'CISCO'},
			{name: 'Samsung', width: '90px'},
			{name: 'Addidas'},
			{name: 'Nike', width: '80px'}, 
			{name: 'Nest', width: '100px'}]; 
		var url = "/static/img/trusted-companies/"; 
		var result = addElementsToMarquee('teac', elements, MARQUEE_TEMPLATE, url);
		if(result === -1) {
			$('#teac').parent().hide();
		} 
	} 

	
	function populateManufacturers() {
		var elements = [
				{name: 'Vishay ', image: 'vishay'},
				{name: 'Texas Instrument ', image: 'texas-instrument'},
				{name: 'Gan Systems ', image: 'gan-systems'},
				{name: 'Linear Technology', image: 'Linear-Technology'},
				{name: 'Maxim Integrated ', image: 'maxim-integrated'},
				{name: 'GCT  ', image: 'gct'},
				{name: 'TE Connectivity ', image: 'te-connectivity'},
				{name: 'CUI INC', image: 'cui-inc'},
				{name: 'RECOM', image: '/static/img/recom-logo.png', fullImg: true},
				{name: 'Vishay ', image: 'vishay'},
				{name: 'Texas Instrument ', image: 'texas-instrument'},
				{name: 'Gan Systems ', image: 'gan-systems'},
				{name: 'Linear Technology', image: 'Linear-Technology'},
				{name: 'Maxim Integrated ', image: 'maxim-integrated'},
				{name: 'GCT  ', image: 'gct'},
				{name: 'TE Connectivity ', image: 'te-connectivity'},
				{name: 'CUI INC', image: 'cui-inc'},
				{name: 'RECOM', image: '/static/img/recom-logo.png', fullImg: true}
			]; 
		var url = "/static/img/manufacturer-logos/"; 
		var result = addElementsToMarquee('instant-access', elements, MARQUEE_TEMPLATE, url);
		if(result === -1) {
			$('#instant-access').parent().hide();
		} 
	} 

	function addElementsToMarquee(id, elements, TEMPLATE, imgUrl) {
		try {
			if ( elements.length > 0 ) {
				elements.forEach(function (element) {
					var image = '';
					if (element.fullImg) {
						image = element.image; 
					} else {
						image = imgUrl + (element.image ? element.image :  element.name.replace(' ', '-').toLowerCase()) + '.png';
					}
					var template = '';
					if(element.link) {
						template = TEMPLATE.replace('__IMG__', image);
						template = template.replace('__LINK__', element.link);
					} else {
						template = MARQUEE_TEMPLATE_LABEL.replace('__IMG__', image);
					}
					template = template.replace('__ALT__', ' alt="'+ element.name +'"');
					if(element.label) {
						template = template.replace('__LABEL__', element.label);
					}
					if(element.width) {
						var style = "style='width: " + element.width + ";'";
						template = template.replace('__WIDTH__', style);
					} else {
						template = template.replace('__WIDTH__', '');
					}
					$('#' + id).append(template);
				});
			}
			return 1;
		}
		catch (err) {
			return -1;
		}
	}
</script>
<script src="https://cdn.jsdelivr.net/jquery.marquee/1.4.0/jquery.marquee.min.js"></script> 



    <style>
        #base{

            -webkit-animation: fadein 0.3s; /* Safari, Chrome and Opera > 12.1 */
               -moz-animation: fadein 0.3s; /* Firefox < 16 */
                -ms-animation: fadein 0.3s; /* Internet Explorer */
                 -o-animation: fadein 0.3s; /* Opera < 12.1 */
                    animation: fadein 0.3s;
                    opacity: 1
        }

        

        #HW_badge {
            width: 45px !important;
            height: 45px !important;
            position: absolute !important;
            left: -25px !important;
            top: -5px !important;
            background: transparent !important;
            color: transparent !important;
        }

        .diamond {
            margin-left: 30px;
            font-size: 25px;
            margin-right: -20px;
            color: rgba(150, 200, 255, 1) !important;
        }

        .flex-center {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
        }

        .updates {
            margin-right: 15px;
            width: 40px;
            margin-left: -10px;
        }

        .asked_panel .profile img {
            width: 30px !important;
            height: 30px !important;
        }

    </style>

    <script>
        function containsAny(str, substrings) {
        for (var i = 0; i != substrings.length; i++) {
        var substring = substrings[i];
        if (str.indexOf(substring) != - 1) {
            return substring;
        }
        }
        return false; 
    }
    if(!containsAny(window.location.href,['home','view-part','search'])){
        $('#questions').show()
    }
    
          window.intercomSettings = {
            app_id: "owbpizd5"
          };
    
  </script>

    <script>(function(){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',intercomSettings);}else{var d=document;var i=function(){i.c(arguments)};i.q=[];i.c=function(args){i.q.push(args)};w.Intercom=i;function l(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://widget.intercom.io/widget/owbpizd5';var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s,x);}if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}})()</script>

    <script type="text/javascript">
        window.addEventListener('message', (event) => {
            const { type, payload } = event.data;
            if (type === 'notify intercom') {
                window.intercomSettings['used_web_copilot'] = true;
                window.Intercom('update', window.intercomSettings);
            }
        });
    </script>

    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "url": "https://www.snapeda.com/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://www.snapeda.com/search/?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="text/javascript">
        adroll_adv_id = "E5JOHRIMLRBBZHMVKSEUGT";
        adroll_pix_id = "OINORE6YJVH6XI52DJKCXZ";
        
        (function () {
            var _onload = function(){
                if (document.readyState && !/loaded|complete/.test(document.readyState)){setTimeout(_onload, 10);return}
                if (!window.__adroll_loaded){__adroll_loaded=true;setTimeout(_onload, 50);return}
                var scr = document.createElement("script");
                var host = (("https:" == document.location.protocol) ? "https://s.adroll.com" : "http://a.adroll.com");
                scr.setAttribute('async', 'true');
                scr.type = "text/javascript";
                scr.src = host + "/j/roundtrip.js";
                ((document.getElementsByTagName('head') || [null])[0] ||
                    document.getElementsByTagName('script')[0].parentNode).appendChild(scr);
            };
            if (window.addEventListener) {window.addEventListener('load', _onload, false);}
            else {window.attachEvent('onload', _onload)}
        }());

        $(document).ready(function() {
            

            
        });
    </script>



    <!-- Sentry integration -->
    <script src='https://js.sentry-cdn.com/d2019c20912d44c3b7ee0c0f42296c42.min.js' crossorigin="anonymous"></script>
    <script>
      Sentry.init({ dsn: 'https://<EMAIL>/1286597',
        tracesSampleRate: 0.2 });
    </script>

    <script>!function () {var reb2b = window.reb2b = window.reb2b || []; if (reb2b.invoked) return;reb2b.invoked = true;reb2b.methods = ["identify", "collect"]; reb2b.factory = function (method) {return function () {var args = Array.prototype.slice.call(arguments); args.unshift(method);reb2b.push(args);return reb2b;};}; for (var i = 0; i < reb2b.methods.length; i++) {var key = reb2b.methods[i];reb2b[key] = reb2b.factory(key);} reb2b.load = function (key) {var script = document.createElement("script");script.type = "text/javascript";script.async = true; script.src = "https://s3-us-west-2.amazonaws.com/b2bjsstore/b/" + key + "/reb2b.js.gz"; var first = document.getElementsByTagName("script")[0]; first.parentNode.insertBefore(script, first);}; reb2b.SNIPPET_VERSION = "1.0.1";reb2b.load("5NRP9HG3Z2O1");}();</script>

    <script>
        function getFallbackImage(partname, manufacturer, cb){
            const url = "/api/get_mouser_info_api?part_name=" + partname + "&manufacturer=" + manufacturer;
            $.getJSON(url, function(r){
                cb(r['ImagePath'])
            })
        }
        function fallbackImage(partname,manufacturer,t){
            t.onerror=null;
            t.style.display='none'
            $current = $(t)
            $current.hide()
            getFallbackImage(partname,manufacturer,function(imgUrl){
                $current.hide()
                $current.parent().append('<img src="' + imgUrl + '" alt="part image"/>');
            })
        }
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        };

        function trackSignup() {
            var queryString = window.location.search;
            var campaign = null;
            var referrer = document.referrer
            var query_params = {}

            // get value of campaign code if it exist
            window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, function (m, key, value) {
                if (key == 'welcome') {
                    campaign = value;
                }
                query_params[key] = value;
            });

            if (campaign != null
                & referrer.indexOf('account/signup') != -1) {
                var properties = {
                    'userId': '',
                    'email': '',
                    'company': query_params['company'],
                    'campaign': campaign,
                    'plugin': query_params['plugin']
                }
                var url = window.location.href.replace('welcome=' + campaign, '')

                // in case the welcome is in the middle of qs
                url = url.replace('&&', '&')
                // in case the welcome is at the beginning of qs
                url = url.replace('?&', '?')

                var mixpanelWait = setInterval(function () {
                    if (mixpanel) {
                        mixpanel.track('Signup', properties)
                        clearInterval(mixpanelWait);
                    }
                }, 100);
                window.history.pushState({}, document.title, url);

            }
        }

        window.onload = function(){

            trackSignup();


            $('.r-menu-btn').click(function(){
                $('.nav-bar-items, #nav-search-part-form, .ls-section').slideToggle();
            });

            if($(window).width() > 1200) {
                $('.nav-bar-items, #nav-search-part-form, .ls-section').show();
            } else {
                $('.nav-bar-items, #nav-search-part-form, .ls-section').hide();
            }
            $(window).on('resize', function() {
                if($(window).width() > 1200) {
                    $('.nav-bar-items, #nav-search-part-form, .ls-section').show();
                } else if($(window).width() > 576) {
                    $('.nav-bar-items, #nav-search-part-form, .ls-section').hide();
                }
            });

            
            var ua = window.navigator.userAgent;
            var msie = ua.indexOf("MSIE ");
            var urlList = ['home', 'instapart', 'parts', 'questions', 'pricing', 'discover', 'about', 'instapublish', 'login', 'signup', 'instabuild'];
            var cl = window.location.href;
            if( cl == (window.location.origin + "/") ) {
                $('.r-menu-btn').on('click', function() {
                    const body = document.querySelector("body");
                    var x = document.getElementById("seda-navbar");
                    if (x.style.display === "flex" || x.style.display === "") {
                        x.style.display = "block";
                        x.style.paddingTop = "16px";
                        body.style.overflow = "hidden";
                        $(".ls-section").css("display", "flex");
                    } else {
                        x.style.display = "flex";
                        x.style.paddingTop = "0px";
                        body.style.overflow = "auto";
                        $(".ls-section").css("display", "");
                    }
                });
                $('#seda-navbar').css('padding-left','16px');
                if($('#seda-navbar').css('min-height') === '72px') {
                    $('#seda-navbar').css('min-height','56px'); 
                } else {
                    $('#seda-navbar').css('min-height','78px'); 
                }
            } 
            urlList.forEach( function(element) {
                if( cl.indexOf(element) > 0 ) {
                    $('.r-menu-btn').on('click', function() {
                    const body = document.querySelector("body");
                    var x = document.getElementById("seda-navbar");
                    if (x.style.display === "flex" || x.style.display === "") {
                        x.style.display = "block";
                        x.style.paddingTop = "16px";
                        body.style.overflow = "hidden";
                        $(".ls-section").css("display", "flex");
                        $(".ls-section").css("marginBottom", "10px");
                        $("#nav-search-part-form,.ls-section").css("width", "96%");
                    } else {
                        x.style.display = "flex";
                        x.style.paddingTop = "0px";
                        $(".ls-section").css("display", "");
                        $(".ls-section").css("marginBottom", "");
                        $("#nav-search-part-form,.ls-section").css("width", "");
                        body.style.overflow = "auto";
                    }
                    });
                    $('#seda-navbar').css('width','calc(100% - 16px)');
                    $('#seda-navbar').css('padding-left','16px');
                    if($('#seda-navbar').css('min-height') === '72px') {
                        $('#seda-navbar').css('min-height','56px');
                    } else {
                        $('#seda-navbar').css('min-height','78px');
                    }
                }
            });

            try {
                var HW_config = {
                    selector: "#recent_news",
                    account: "x9ndd7",
                    translations: {
                        footer: "More Updates"
                    }
                };
                if(Headway) {
                    Headway.init(HW_config);
                }
            } catch (err) {
                console.log('Headway error occured');
            }
        };
    </script>
    <script async src="https://cdn.headwayapp.co/widget.js"></script>

    <!-- ShieldSquare Integration
    <script>
        (function(w, d, e, u, c, g, a, b){
        w["SSJSConnectorObj"] = w["SSJSConnectorObj"] || {ss_cid : c, domain_info: g};
        a = d.createElement(e);
        a.async = true;
        a.src = u;
        b = d.getElementsByTagName(e)[0];
        b.parentNode.insertBefore(a, b);

        })(window,document,"script","https://cdn.perfdrive.com/aperture/aperture.js","ad6a","auto");
    </script>
    -->

    <!-- <script src="https://posthog-analytics.herokuapp.com/static/array.js"></script>
    <script>
        posthog.init('LUXEDZEuoIJH3N6eecBJkyqiGL1oJNVDG8t3MaGeChY', {api_host: 'https://posthog-analytics.herokuapp.com'});
        var authenticated = false;
        
        posthog.identify(authenticated ? "" : "null");
        posthog.people.set({
            "email": authenticated ? "" : "null"
        });
    </script> -->

    <script>
        
        $.fn.isInViewport = function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();

            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();

            return elementBottom > viewportTop && elementTop < viewportBottom;
        };
    </script>


    <script>
        
        
        function addAutoComplete(id) {
          if (document.getElementById(id)){
            document.getElementById(id).addEventListener("keyup", function(event){
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function() {
                    if (this.readyState == 4 && this.status == 200) {
                        var response = JSON.parse(this.response);
                        autocomplete(document.getElementById(id), response);
                    }
                };
                xhttp.open("GET", "/api/v1/search_autocomplete?q=" + this.value, true);
                xhttp.send();
                if (event.keyCode === 13) {
                    $('.search-autocom-submit').click()
                }
            });
          }
        }

        function autocomplete(inp, arr) {
            var currentFocus;
            var a, b, i, val = inp.value;
            closeAllLists();
            if (!val) { return false;}
            currentFocus = -1;
            a = document.createElement("DIV");
            a.setAttribute("id", inp.id + "autocomplete-list");
            a.setAttribute("class", "autocomplete-items");
            inp.parentNode.appendChild(a);
            for (i = 0; i < arr.length; i++) {
                b = document.createElement("DIV");
                b.innerHTML = "<strong>" + arr[i].substr(0, val.length) + "</strong>";
                b.innerHTML += arr[i].substr(val.length);
                b.innerHTML += "<input type='hidden' value='" + arr[i] + "'>";
                b.addEventListener("click", function(e) {
                    inp.value = this.getElementsByTagName("input")[0].value;
                    closeAllLists();
                    $('.search-autocom-submit').click()
                });
                a.appendChild(b);
            }
            inp.addEventListener("keydown", function(e) {
                var x = document.getElementById(this.id + "autocomplete-list");
                if (x) x = x.getElementsByTagName("div");
                if (e.keyCode == 40) {
                    currentFocus++;
                    addActive(x);
                } else if (e.keyCode == 38) {
                    currentFocus--;
                    addActive(x);
                } else if (e.keyCode == 13) {
                    e.preventDefault();
                    if (currentFocus > -1) {
                    if (x) x[currentFocus].click();
                    }
                }
            });
            function addActive(x) {
                if (!x) return false;
                removeActive(x);
                if (currentFocus >= x.length) currentFocus = 0;
                if (currentFocus < 0) currentFocus = (x.length - 1);
                if(x[currentFocus])
                    x[currentFocus].classList.add("autocomplete-active");
            }
            function removeActive(x) {
                for (var i = 0; i < x.length; i++) {
                x[i].classList.remove("autocomplete-active");
                }
            }
            function closeAllLists(elmnt) {
                var x = document.getElementsByClassName("autocomplete-items");
                for (var i = 0; i < x.length; i++) {
                if (elmnt != x[i] && elmnt != inp) {
                    x[i].parentNode.removeChild(x[i]);
                }
                }
            }
            document.addEventListener("click", function (e) {
                closeAllLists(e.target);
            });
            }

        
    </script>

    
</body>
</html>
