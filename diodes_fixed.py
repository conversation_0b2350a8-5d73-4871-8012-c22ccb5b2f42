#!/usr/bin/env python3
"""
Diodes Fixed - Updated version based on diagnostic findings
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def diodes_fixed_automation():
    print("🎯 DIODES FIXED AUTOMATION")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # STEP 1: Load website
        print("📱 STEP 1: Loading Diodes website...")
        driver.get('https://www.diodes.com')
        time.sleep(8)
        
        print(f"✅ Loaded: {driver.title}")
        
        # STEP 2: Find search box (we know it's input[type='search'])
        print("📱 STEP 2: Finding search box...")
        
        try:
            search_box = driver.find_element(By.CSS_SELECTOR, "input[type='search']")
            print("✅ Found search box")
        except:
            print("❌ Search box not found")
            return False
        
        # STEP 3: Search for part
        print("📱 STEP 3: Searching for APX803L20-30SA-7...")
        search_box.clear()
        search_box.send_keys("APX803L20-30SA-7")
        search_box.send_keys(Keys.RETURN)
        time.sleep(10)
        
        print(f"✅ Search completed")
        print(f"📍 URL: {driver.current_url}")
        
        # STEP 4: Look for part in results with better selectors
        print("📱 STEP 4: Looking for part in results...")
        
        # Check if part is found in page
        if "APX803L20-30SA-7" in driver.page_source.upper():
            print("✅ Part found in page source")
            
            # Look for clickable part links with multiple strategies
            part_selectors = [
                "//a[contains(text(), 'APX803L20-30SA-7')]",
                "//a[contains(@href, 'APX803L20-30SA-7')]", 
                "//a[contains(text(), 'APX803L20')]",
                "//td[contains(text(), 'APX803L20-30SA-7')]/parent::tr//a",
                "//tr[contains(., 'APX803L20-30SA-7')]//a"
            ]
            
            part_link_found = False
            
            for selector in part_selectors:
                if part_link_found:
                    break
                    
                print(f"  Trying selector: {selector}")
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    print(f"    Found {len(elements)} elements")
                    
                    for i, elem in enumerate(elements):
                        try:
                            if elem.is_displayed() and elem.is_enabled():
                                text = elem.text.strip()
                                href = elem.get_attribute('href') or ''
                                print(f"    {i+1}. '{text}' -> {href[:50]}...")
                                
                                # Click the first good link
                                if text or 'APX803L20' in href:
                                    print(f"🎯 Clicking: '{text}'")
                                    elem.click()
                                    time.sleep(10)
                                    part_link_found = True
                                    break
                        except Exception as e:
                            print(f"    Error with element {i}: {e}")
                            continue
                            
                except Exception as e:
                    print(f"    Selector failed: {e}")
                    continue
            
            if not part_link_found:
                print("⚠️ No clickable part links found, but part exists in page")
                print("🔍 Showing all links on page:")
                
                all_links = driver.find_elements(By.TAG_NAME, "a")
                for i, link in enumerate(all_links[:10]):
                    try:
                        if link.is_displayed():
                            text = link.text.strip()
                            href = link.get_attribute('href') or ''
                            if text:
                                print(f"  {i+1}. '{text}' -> {href[:50]}...")
                    except:
                        continue
        else:
            print("❌ Part not found in search results")
            return False
        
        # STEP 5: Look for 3D models/downloads on part page
        print("📱 STEP 5: Looking for 3D models...")
        print(f"📍 Current URL: {driver.current_url}")
        
        # Look for download-related elements
        download_indicators = [
            "//a[contains(text(), '3D')]",
            "//a[contains(text(), 'STEP')]", 
            "//a[contains(text(), 'Download')]",
            "//a[contains(@href, '.step')]",
            "//a[contains(@href, '.stp')]",
            "//button[contains(text(), 'Download')]"
        ]
        
        downloads_found = []
        
        for indicator in download_indicators:
            try:
                elements = driver.find_elements(By.XPATH, indicator)
                for elem in elements:
                    if elem.is_displayed():
                        text = elem.text.strip()
                        href = elem.get_attribute('href') or ''
                        downloads_found.append((text, href, elem))
                        print(f"  ✅ Found: '{text}' -> {href[:50]}...")
            except:
                continue
        
        if downloads_found:
            print(f"🎉 Found {len(downloads_found)} potential downloads!")
            
            # Try to download the first one
            for text, href, elem in downloads_found:
                try:
                    if '3d' in text.lower() or 'step' in text.lower() or '.step' in href.lower():
                        print(f"🔽 Attempting download: '{text}'")
                        elem.click()
                        time.sleep(10)
                        
                        # Check if file was downloaded
                        downloads_dir = os.path.expanduser("~/Downloads")
                        import glob
                        step_files = glob.glob(os.path.join(downloads_dir, "*.step")) + glob.glob(os.path.join(downloads_dir, "*.stp"))
                        
                        if step_files:
                            latest_file = max(step_files, key=os.path.getmtime)
                            mod_time = os.path.getmtime(latest_file)
                            
                            if (time.time() - mod_time) < 60:  # Modified in last minute
                                print(f"🎉 SUCCESS: Downloaded {os.path.basename(latest_file)}")
                                
                                # Move to 3d folder
                                os.makedirs("3d", exist_ok=True)
                                target_file = f"3d/diodes_inc_APX803L20-30SA-7.step"
                                
                                import shutil
                                shutil.move(latest_file, target_file)
                                print(f"✅ Moved to: {target_file}")
                                return True
                        
                        print(f"⚠️ No STEP file downloaded")
                        break
                        
                except Exception as e:
                    print(f"❌ Download error: {e}")
                    continue
        else:
            print("❌ No download links found")
        
        # Keep browser open for inspection
        print(f"\n🔸 Browser staying open for 2 minutes...")
        time.sleep(120)
        
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    success = diodes_fixed_automation()
    
    if success:
        print(f"\n🎉 DIODES AUTOMATION SUCCESS!")
    else:
        print(f"\n❌ DIODES AUTOMATION FAILED")
        print(f"💡 Check browser window for manual inspection")
