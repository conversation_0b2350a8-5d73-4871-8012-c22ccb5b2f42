#!/usr/bin/env python3
"""
Test UltraLibrarian now that the block should be cleared
"""

import requests
from bs4 import BeautifulSoup
import os
import time
from datetime import datetime

def log_message(message):
    """Write message to log file"""
    print(message)
    with open('ultralibrarian_retry_log.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {message}\n")

def test_ultralibrarian_access():
    log_message("🔍 TESTING ULTRALIBRARIAN ACCESS - RETRY AFTER BLOCK")
    log_message("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    # Test basic access first
    login_url = 'https://www.ultralibrarian.com/wp-login.php'
    
    log_message(f"1. Testing basic access to UltraLibrarian...")
    log_message(f"   URL: {login_url}")
    
    try:
        response = session.get(login_url, timeout=30)
        log_message(f"   Status: {response.status_code}")
        
        if response.status_code == 503:
            log_message(f"   ❌ Still blocked by Wordfence (503 error)")
            if 'wordfence' in response.text.lower():
                log_message(f"   🔒 Confirmed: Wordfence security block still active")
            return False
            
        elif response.status_code == 200:
            log_message(f"   ✅ Successfully accessed login page!")
            
            # Save the response
            with open('ultralibrarian_retry_response.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            log_message(f"   📄 Saved response to ultralibrarian_retry_response.html")
            
            # Check if it's actually the login page
            if 'login' in response.text.lower() and 'password' in response.text.lower():
                log_message(f"   ✅ Confirmed: This is the login page")
                return True
            else:
                log_message(f"   ⚠️  Got 200 response but doesn't look like login page")
                return False
        else:
            log_message(f"   ⚠️  Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Error: {e}")
        return False

def test_ultralibrarian_login():
    log_message(f"\n2. Testing UltraLibrarian login...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        # Get login page
        login_url = 'https://www.ultralibrarian.com/wp-login.php'
        login_page = session.get(login_url, timeout=30)
        
        if login_page.status_code != 200:
            log_message(f"   ❌ Cannot access login page: {login_page.status_code}")
            return False
        
        # Parse form
        soup = BeautifulSoup(login_page.text, 'html.parser')
        form = soup.find('form', {'id': 'loginform'})
        
        if not form:
            log_message(f"   ❌ Cannot find login form")
            return False
        
        # Extract form data
        form_data = {}
        for inp in form.find_all('input'):
            name = inp.get('name')
            value = inp.get('value', '')
            if name:
                form_data[name] = value
        
        # Set credentials
        form_data['log'] = '<EMAIL>'
        form_data['pwd'] = 'Lennyai123#'
        
        log_message(f"   Form fields found: {list(form_data.keys())}")
        
        # Submit login
        session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://www.ultralibrarian.com',
            'Referer': login_url,
        })
        
        login_response = session.post(login_url, data=form_data, timeout=30, allow_redirects=True)
        
        log_message(f"   Login Status: {login_response.status_code}")
        log_message(f"   Final URL: {login_response.url}")
        
        # Save response
        with open('ultralibrarian_login_test_response.html', 'w', encoding='utf-8') as f:
            f.write(login_response.text)
        log_message(f"   📄 Saved login response")
        
        # Check for success indicators
        if 'wp-admin' in login_response.url:
            log_message(f"   🎉 SUCCESS: Redirected to wp-admin!")
            return session
        elif 'login_error' in login_response.text:
            log_message(f"   ❌ Login failed: Found error message")
            return False
        elif 'logout' in login_response.text.lower():
            log_message(f"   🎉 SUCCESS: Found logout link (likely logged in)")
            return session
        else:
            log_message(f"   ⚠️  Login status unclear")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Login test error: {e}")
        return False

def search_ultralibrarian_for_part(session, part_number):
    log_message(f"\n3. Searching UltraLibrarian for {part_number}...")
    
    try:
        # Try UltraLibrarian search
        search_url = f"https://www.ultralibrarian.com/search?q={part_number}"
        log_message(f"   Search URL: {search_url}")
        
        search_response = session.get(search_url, timeout=30)
        log_message(f"   Search Status: {search_response.status_code}")
        
        if search_response.status_code == 200:
            # Save search results
            with open('ultralibrarian_search_results.html', 'w', encoding='utf-8') as f:
                f.write(search_response.text)
            log_message(f"   📄 Saved search results")
            
            # Check if part is found
            if part_number.lower() in search_response.text.lower():
                log_message(f"   ✅ Found {part_number} in search results!")
                
                # Look for download links
                soup = BeautifulSoup(search_response.text, 'html.parser')
                download_links = []
                
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    text = link.get_text(strip=True).lower()
                    
                    if any(keyword in text for keyword in ['download', 'step', '3d', 'model']):
                        download_links.append({
                            'url': href,
                            'text': text
                        })
                
                log_message(f"   Found {len(download_links)} potential download links:")
                for i, link in enumerate(download_links[:5], 1):
                    log_message(f"   {i}. {link['text']}")
                    log_message(f"      URL: {link['url']}")
                
                # Try to download STEP files
                for link in download_links:
                    if '.step' in link['url'].lower() or '.stp' in link['url'].lower():
                        success = try_download_step(session, link, part_number)
                        if success:
                            return True
                
                return len(download_links) > 0
            else:
                log_message(f"   ❌ {part_number} not found in search results")
                return False
        else:
            log_message(f"   ❌ Search failed: {search_response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Search error: {e}")
        return False

def try_download_step(session, link, part_number):
    """Try to download a STEP file"""
    log_message(f"   🔽 Attempting download...")
    log_message(f"   Download URL: {link['url']}")
    
    try:
        # Make URL absolute if needed
        url = link['url']
        if not url.startswith('http'):
            url = f"https://www.ultralibrarian.com{url}"
        
        download_response = session.get(url, timeout=60, stream=True)
        log_message(f"   Download status: {download_response.status_code}")
        
        if download_response.status_code == 200:
            # Create 3d directory
            os.makedirs('3d', exist_ok=True)
            
            # Determine filename
            filename = f"{part_number}_UltraLibrarian.step"
            filepath = os.path.join('3d', filename)
            
            # Save file
            with open(filepath, 'wb') as f:
                for chunk in download_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            log_message(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                log_message(f"   🎉 SUCCESS: Got STEP file from UltraLibrarian!")
                return True
            else:
                log_message(f"   ⚠️  File too small, might be error page")
                return False
        else:
            log_message(f"   ❌ Download failed: {download_response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ Download error: {e}")
        return False

def main():
    # Clear log
    with open('ultralibrarian_retry_log.txt', 'w') as f:
        f.write(f"ULTRALIBRARIAN RETRY LOG - {datetime.now()}\n")
        f.write("=" * 60 + "\n")
    
    part_number = "APX803L20-30SA-7"
    
    log_message("🚀 ULTRALIBRARIAN RETRY - TESTING AFTER WORDFENCE BLOCK")
    log_message("=" * 60)
    
    # Test basic access
    if not test_ultralibrarian_access():
        log_message("\n❌ STILL BLOCKED - Cannot access UltraLibrarian")
        return
    
    # Test login
    session = test_ultralibrarian_login()
    if not session:
        log_message("\n❌ LOGIN FAILED - Cannot log into UltraLibrarian")
        return
    
    # Search for part
    found_part = search_ultralibrarian_for_part(session, part_number)
    
    # Summary
    log_message("\n" + "=" * 60)
    log_message("📋 ULTRALIBRARIAN RETRY RESULTS:")
    
    if found_part:
        log_message("   ✅ UltraLibrarian access restored!")
        log_message("   ✅ Login successful!")
        log_message("   ✅ Part found!")
    else:
        log_message("   ✅ UltraLibrarian access restored!")
        log_message("   ✅ Login successful!")
        log_message("   ❌ Part not found or no downloads available")
    
    # Check for actual STEP files
    step_files = []
    if os.path.exists('3d'):
        for file in os.listdir('3d'):
            if file.endswith('.step') and not file.startswith('KiCad_'):
                step_files.append(file)
    
    if step_files:
        log_message(f"\n🎉 ACTUAL STEP FILES DOWNLOADED:")
        for file in step_files:
            filepath = os.path.join('3d', file)
            size = os.path.getsize(filepath)
            log_message(f"   ✅ {file} ({size:,} bytes)")
        log_message(f"\n🎯 SUCCESS: We now have real manufacturer STEP files!")
    else:
        log_message(f"\n❌ NO STEP FILES DOWNLOADED")
        log_message(f"   UltraLibrarian may not have this specific part")
    
    log_message(f"\n📄 Full log saved to ultralibrarian_retry_log.txt")

if __name__ == "__main__":
    main()
