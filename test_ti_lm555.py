#!/usr/bin/env python3
"""
Test TI search with LM555 (definitely a TI part)
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def test_ti_lm555():
    print("TESTING TI SEARCH WITH LM555")
    print("=" * 40)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        print("1. Going to TI search for LM555...")
        search_url = "https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm=LM555"
        driver.get(search_url)
        time.sleep(15)
        
        print(f"Current URL: {driver.current_url}")
        
        # Check visible text
        body_text = driver.find_element("tag name", "body").text
        
        if "LM555" in body_text.upper():
            print("✅ SUCCESS: LM555 found on results page!")
        else:
            print("❌ FAILED: LM555 not found on results page")
            print("First 300 chars of page:")
            print(body_text[:300])
        
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_ti_lm555()
