#!/usr/bin/env python3
"""
KEEP BROWSER OPEN
=================
Version that keeps browser open even on errors.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def keep_browser_open():
    print("🎯 KEEP BROWSER OPEN DEBUG")
    print("=" * 40)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # STEP 1: Load UltraLibrarian
        print("\n🔸 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        print(f"✅ Step 1 complete: {driver.title}")
        
        # STEP 2: Search
        print("\n🔸 STEP 2: Searching for LM358N...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_box = None
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                search_box = inp
                break
        
        if not search_box:
            print("❌ FAILED at Step 2: No search box found!")
            print("Browser will stay open for inspection...")
            input("Press Enter to close browser...")
            return
        
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        time.sleep(10)
        print(f"✅ Step 2 complete")
        
        # STEP 3: Click TI part
        print("\n🔸 STEP 3: Finding Texas Instruments LM358N...")
        links = driver.find_elements(By.TAG_NAME, "a")
        ti_link = None
        
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    ti_link = link
                    break
            except:
                continue
        
        if not ti_link:
            print("❌ FAILED at Step 3: No TI LM358N found!")
            print("Browser will stay open for inspection...")
            input("Press Enter to close browser...")
            return
        
        ti_link.click()
        time.sleep(8)
        print(f"✅ Step 3 complete")
        
        # STEP 4: Download Now
        print("\n🔸 STEP 4: Looking for Download Now...")
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        
        if not download_btns:
            print("❌ FAILED at Step 4: No Download Now button found!")
            print("Browser will stay open for inspection...")
            input("Press Enter to close browser...")
            return
        
        driver.execute_script("arguments[0].click();", download_btns[0])
        time.sleep(5)
        print(f"✅ Step 4 complete: Clicked Download Now")
        
        # STEP 5: Look for 3D CAD Model with more time
        print("\n🔸 STEP 5: Looking for 3D CAD Model (waiting longer)...")
        
        # Wait longer for page to load
        time.sleep(10)
        
        # Try multiple selectors
        cad_selectors = [
            "//button[contains(text(), '3D CAD Model')]",
            "//button[contains(text(), '3D Model')]",
            "//button[contains(text(), 'CAD Model')]",
            "//a[contains(text(), '3D CAD Model')]",
            "//a[contains(text(), '3D Model')]",
            "//a[contains(text(), 'CAD Model')]"
        ]
        
        cad_element = None
        for selector in cad_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    cad_element = elem
                    print(f"✅ Found 3D CAD element: '{elem.text}' with selector: {selector}")
                    break
            if cad_element:
                break
        
        if not cad_element:
            print("❌ FAILED at Step 5: No 3D CAD Model button found!")
            
            # Show ALL buttons for debugging
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print(f"\nAll buttons on page ({len(buttons)} total):")
            for i, btn in enumerate(buttons):
                try:
                    text = btn.text.strip()
                    visible = btn.is_displayed()
                    enabled = btn.is_enabled()
                    if text or visible:
                        print(f"  {i}: '{text}' (visible={visible}, enabled={enabled})")
                except Exception as e:
                    print(f"  {i}: Error reading button - {e}")
            
            # Show ALL links for debugging
            links = driver.find_elements(By.TAG_NAME, "a")
            print(f"\nAll links on page (first 20 of {len(links)}):")
            for i, link in enumerate(links[:20]):
                try:
                    text = link.text.strip()
                    visible = link.is_displayed()
                    if text and visible:
                        print(f"  {i}: '{text}' (visible={visible})")
                except Exception as e:
                    print(f"  {i}: Error reading link - {e}")
            
            print(f"\nCurrent URL: {driver.current_url}")
            print("Browser will stay open for inspection...")
            input("Press Enter to close browser...")
            return
        
        # Continue if 3D CAD Model found
        driver.execute_script("arguments[0].click();", cad_element)
        time.sleep(5)
        print(f"✅ Step 5 complete: Clicked 3D CAD Model")
        
        print("\n✅ SUCCESS: Got past Step 5!")
        print("Browser will stay open for further inspection...")
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("Browser will stay open for inspection...")
        input("Press Enter to close browser...")
    
    finally:
        # Only close if user pressed Enter
        try:
            driver.quit()
            print("✅ Browser closed")
        except:
            print("⚠️ Error closing browser")

if __name__ == "__main__":
    keep_browser_open()
