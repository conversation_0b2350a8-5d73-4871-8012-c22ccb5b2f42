#!/usr/bin/env python3
"""
Test UltraLibrarian up to Screen 4 using the working automation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_ultralibrarian_automation import CompleteUltraLibrarianAutomation

def test_screen4():
    print("🔸 Testing UltraLibrarian up to Screen 4")
    print("=" * 40)
    
    automation = CompleteUltraLibrarianAutomation()
    driver = automation.setup_driver()
    
    if not driver:
        print("❌ Failed to setup driver")
        return
    
    try:
        # Screen 1
        print("\n🔸 SCREEN 1: Search")
        result1 = automation.screen_1_search(driver, "Texas Instruments", "LM358N")
        if not result1:
            print("❌ Screen 1 failed")
            return
        print("✅ Screen 1 completed")
        
        # Screen 2  
        print("\n🔸 SCREEN 2: Select part")
        result2 = automation.screen_2_select_part(driver, "LM358N")
        if not result2:
            print("❌ Screen 2 failed")
            return
        print("✅ Screen 2 completed")
        
        # Screen 3
        print("\n🔸 SCREEN 3: Find right variant")
        result3 = automation.screen_3_find_right_variant(driver, "LM358N")
        if not result3:
            print("❌ Screen 3 failed")
            return
        print("✅ Screen 3 completed")
        
        # Screen 4
        print("\n🔸 SCREEN 4: Download Now")
        result4 = automation.screen_4_download_now(driver)
        if not result4:
            print("❌ Screen 4 failed")
            return
        print("✅ Screen 4 completed")
        
        print(f"\n🎯 SCREENS 1-4 COMPLETE")
        print(f"Current URL: {driver.current_url}")
        print("Browser will stay open for 60 seconds")
        
        import time
        time.sleep(60)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        driver.quit()

if __name__ == "__main__":
    test_screen4()
