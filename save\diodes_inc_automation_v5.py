#!/usr/bin/env python3
"""
DIODES INC AUTOMATION
=====================
Download STEP files from Diodes Inc website.
Test part: APX803L20-30SA-7
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def diodes_inc_automation():
    print("🎯 DIODES INC AUTOMATION")
    print("=" * 40)
    
    # Setup Chrome with downloads
    chrome_options = Options()
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # STEP 1: Load Diodes Inc website
        print("\n🔸 STEP 1: Loading Diodes Inc website...")
        driver.get('https://www.diodes.com')
        time.sleep(10)
        
        print(f"✅ Loaded: {driver.title}")
        print(f"URL: {driver.current_url}")
        
        # STEP 2: Find search box
        print("\n🔸 STEP 2: Looking for search box...")
        
        # Try multiple search box selectors
        search_selectors = [
            "input[type='search']",
            "input[placeholder*='search']",
            "input[placeholder*='Search']",
            "input[name*='search']",
            "input[id*='search']",
            "#search",
            ".search-input",
            "input[type='text']"
        ]
        
        search_box = None
        for selector in search_selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for elem in elements:
                if elem.is_displayed() and elem.is_enabled():
                    search_box = elem
                    print(f"✅ Found search box with selector: {selector}")
                    break
            if search_box:
                break
        
        if not search_box:
            print("❌ No search box found!")
            print("Available input elements:")
            inputs = driver.find_elements(By.TAG_NAME, "input")
            for i, inp in enumerate(inputs[:10]):
                try:
                    input_type = inp.get_attribute('type') or 'text'
                    placeholder = inp.get_attribute('placeholder') or ''
                    name = inp.get_attribute('name') or ''
                    visible = inp.is_displayed()
                    print(f"  {i}: type='{input_type}', placeholder='{placeholder}', name='{name}' (visible={visible})")
                except:
                    continue
            
            print("\n🔒 BROWSER STAYING OPEN for manual search")
            while True:
                time.sleep(10)
            return
        
        # STEP 3: Search for APX803L20-30SA-7
        print("\n🔸 STEP 3: Searching for APX803L20-30SA-7...")
        search_box.clear()
        search_box.send_keys("APX803L20-30SA-7")
        search_box.send_keys(Keys.RETURN)
        
        print("✅ Search submitted")
        time.sleep(10)
        print(f"Search results URL: {driver.current_url}")
        
        # STEP 4: Look for the part in search results
        print("\n🔸 STEP 4: Looking for APX803L20-30SA-7 in results...")
        
        # Try to find links containing the part number
        part_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'APX803L20-30SA-7')] | //a[contains(@href, 'APX803L20-30SA-7')] | //a[contains(text(), 'APX803L20')]")
        
        if part_links:
            for i, link in enumerate(part_links):
                try:
                    text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    visible = link.is_displayed()
                    print(f"  Link {i}: '{text}' -> {href} (visible={visible})")
                    
                    if visible and link.is_enabled():
                        print(f"✅ Clicking on: '{text}'")
                        link.click()
                        time.sleep(8)
                        break
                except Exception as e:
                    print(f"  Link {i}: Error - {e}")
        else:
            print("❌ No part links found!")
            print("Available links:")
            all_links = driver.find_elements(By.TAG_NAME, "a")
            for i, link in enumerate(all_links[:20]):
                try:
                    text = link.text.strip()
                    visible = link.is_displayed()
                    if text and visible and len(text) < 100:
                        print(f"  {i}: '{text}'")
                except:
                    continue
            
            print("\n🔒 BROWSER STAYING OPEN for manual navigation")
            while True:
                time.sleep(10)
            return
        
        print(f"✅ On part details page: {driver.current_url}")
        
        # STEP 5: Look for 3D model or STEP file download options
        print("\n🔸 STEP 5: Looking for 3D model/STEP download options...")
        
        # Look for various download-related elements
        download_keywords = ['3d', 'step', 'model', 'cad', 'download', 'file']
        download_elements = []
        
        # Check buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        for btn in buttons:
            try:
                text = btn.text.strip().lower()
                if any(keyword in text for keyword in download_keywords) and btn.is_displayed():
                    download_elements.append(('button', btn.text.strip(), btn))
                    print(f"  Found button: '{btn.text.strip()}'")
            except:
                continue
        
        # Check links
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip().lower()
                href = (link.get_attribute('href') or '').lower()
                if (any(keyword in text for keyword in download_keywords) or 
                    any(keyword in href for keyword in download_keywords)) and link.is_displayed():
                    download_elements.append(('link', link.text.strip(), link))
                    print(f"  Found link: '{link.text.strip()}'")
            except:
                continue
        
        if download_elements:
            # Try to click the most relevant download element
            for element_type, text, element in download_elements:
                if any(keyword in text.lower() for keyword in ['3d', 'step', 'cad']):
                    print(f"✅ Clicking {element_type}: '{text}'")
                    try:
                        driver.execute_script("arguments[0].click();", element)
                        time.sleep(5)
                        break
                    except Exception as e:
                        print(f"⚠️ Click failed: {e}")
                        continue
        else:
            print("❌ No download options found!")
        
        # STEP 6: Monitor for downloads
        print("\n🔸 STEP 6: Monitoring for downloads...")
        
        for i in range(24):  # Monitor for 2 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 NEW FILES: {list(new_files)}")
                
                # Check for STEP files
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    original_file = step_files[0]
                    original_path = os.path.join('3D', original_file)
                    new_name = 'diodes-apx803l20-30sa-7.step'
                    new_path = os.path.join('3D', new_name)
                    
                    try:
                        os.rename(original_path, new_path)
                        print(f"✅ RENAMED: {original_file} -> {new_name}")
                        return new_name
                    except Exception as e:
                        print(f"⚠️ Rename failed: {e}")
                        return original_file
                
                # Check for ZIP files
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP FILE: {zip_files[0]}")
                    try:
                        import zipfile
                        with zipfile.ZipFile(os.path.join('3D', zip_files[0]), 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            original_file = step_files[0]
                            original_path = os.path.join('3D', original_file)
                            new_name = 'diodes-apx803l20-30sa-7.step'
                            new_path = os.path.join('3D', new_name)
                            
                            try:
                                os.rename(original_path, new_path)
                                print(f"✅ EXTRACTED AND RENAMED: {original_file} -> {new_name}")
                                return new_name
                            except Exception as e:
                                print(f"⚠️ Rename failed: {e}")
                                return original_file
                    except Exception as e:
                        print(f"Error extracting: {e}")
            
            print(f"  Checking... ({(i+1)*5}/120 seconds)")
        
        print("⏳ No files downloaded")
        
        # Keep browser open for manual inspection
        print("\n🔒 BROWSER STAYING OPEN for manual inspection")
        print("Current page elements:")
        
        # Show current page buttons and links
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\nButtons ({len(buttons)} total):")
        for i, btn in enumerate(buttons[:15]):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if text:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"\nLinks (first 15 of {len(links)}):")
        for i, link in enumerate(links[:15]):
            try:
                text = link.text.strip()
                visible = link.is_displayed()
                if text and len(text) < 60:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        while True:
            time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Browser staying open...")
        while True:
            time.sleep(10)

if __name__ == "__main__":
    result = diodes_inc_automation()
    if result:
        print(f"\n🎉 SUCCESS: {result}")
    else:
        print(f"\n⚠️ Process completed - check browser for manual steps")
