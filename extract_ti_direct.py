#!/usr/bin/env python3
"""
Extract TI datasheet directly from the existing search data
"""

import json
import requests
import os

def extract_ti_datasheet():
    print("🎯 EXTRACTING TI DATASHEET FROM EXISTING DATA")
    print("=" * 50)
    
    # Read the fresh search results
    with open('digikey_fresh_search_LM358N.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Find the JSON data
    start = html_content.find('{"props":')
    end = html_content.find('</script>', start)
    json_str = html_content[start:end]
    data = json.loads(json_str)
    
    # Get exact matches
    exact_matches = data.get('props', {}).get('pageProps', {}).get('envelope', {}).get('data', {}).get('exactMatch', [])
    
    print(f"Found {len(exact_matches)} exact matches:")
    for i, match in enumerate(exact_matches, 1):
        mfr = match.get('mfr', 'Unknown')
        price = match.get('unitPrice', 'No price')
        detail_url = match.get('detailUrl', '')
        print(f"   {i}. {mfr} - {price} - {detail_url}")
    
    # Find Texas Instruments
    ti_match = None
    for match in exact_matches:
        if 'texas instruments' in match.get('mfr', '').lower():
            ti_match = match
            break
    
    if not ti_match:
        print("❌ Texas Instruments not found")
        return
    
    print(f"\n🎯 Found Texas Instruments match:")
    print(f"   Detail URL: {ti_match['detailUrl']}")
    
    # The TI datasheet is likely at: https://www.ti.com/lit/gpn/lm358
    ti_datasheet_url = "https://www.ti.com/lit/gpn/lm358"
    
    print(f"\n📥 Downloading TI datasheet directly...")
    print(f"   URL: {ti_datasheet_url}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        response = session.get(ti_datasheet_url, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # This will be the TI page, look for PDF download
            if '.pdf' in response.text:
                # Find PDF links
                import re
                pdf_links = re.findall(r'href="([^"]*\.pdf[^"]*)"', response.text)
                
                if pdf_links:
                    pdf_url = pdf_links[0]
                    if not pdf_url.startswith('http'):
                        pdf_url = f"https://www.ti.com{pdf_url}"
                    
                    print(f"   Found PDF: {pdf_url}")
                    
                    # Download the PDF
                    pdf_response = session.get(pdf_url, timeout=60, stream=True)
                    print(f"   PDF Status: {pdf_response.status_code}")
                    
                    if pdf_response.status_code == 200:
                        os.makedirs('datasheets', exist_ok=True)
                        filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                        
                        with open(filename, 'wb') as f:
                            for chunk in pdf_response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        
                        file_size = os.path.getsize(filename)
                        print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                        
                        if file_size > 10000:
                            print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                            return True
                        else:
                            print(f"   ⚠️  File too small")
                            return False
                    else:
                        print(f"   ❌ PDF download failed: {pdf_response.status_code}")
                        return False
                else:
                    print(f"   ❌ No PDF links found")
                    return False
            else:
                print(f"   ❌ No PDF content found")
                return False
        else:
            print(f"   ❌ TI page access failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    extract_ti_datasheet()
