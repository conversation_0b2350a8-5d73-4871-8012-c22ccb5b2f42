#!/usr/bin/env python3
"""
Test Working LM358N - Use our proven SnapEDA method that actually worked
"""

import time
import os
import shutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def handle_cookie_popups(driver):
    """Universal cookie popup handler"""
    cookie_texts = ["Accept", "Accept All", "OK", "Got it", "I Agree", "Continue", "Allow"]
    
    for text in cookie_texts:
        try:
            elements = driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')] | //a[contains(text(), '{text}')]")
            for element in elements:
                if element.is_displayed():
                    element.click()
                    time.sleep(2)
                    return True
        except:
            continue
    return False

def test_snapeda_lm358n_working_method():
    """Test LM358N using our proven working SnapEDA method"""
    print("🎯 TESTING LM358N WITH PROVEN WORKING METHOD")
    print("=" * 60)
    
    part_number = "LM358N"  # The exact part that worked before
    
    print(f"📦 Testing: {part_number}")
    print(f"🎯 Using our proven SnapEDA login method")
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # STEP 1: Go to SnapEDA and login (like our working script)
        print(f"\n📱 STEP 1: Going to SnapEDA...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        # Handle cookies
        handle_cookie_popups(driver)
        
        # STEP 2: Click Login (like our working script)
        print(f"📱 STEP 2: Clicking Login...")
        try:
            login_link = driver.find_element(By.XPATH, "//a[contains(text(), 'Login') or contains(text(), 'Sign in')]")
            login_link.click()
            time.sleep(5)
            print(f"✅ Login page opened")
        except Exception as e:
            print(f"❌ Could not find login link: {e}")
            return False
        
        # STEP 3: Fill login form (using credentials from our working script)
        print(f"📱 STEP 3: Filling login form...")
        try:
            # Look for email field
            email_field = driver.find_element(By.XPATH, "//input[@type='email'] | //input[@name='email'] | //input[@id='email']")
            email_field.clear()
            email_field.send_keys("<EMAIL>")  # User would need to provide real credentials
            
            # Look for password field
            password_field = driver.find_element(By.XPATH, "//input[@type='password'] | //input[@name='password'] | //input[@id='password']")
            password_field.clear()
            password_field.send_keys("your_password")  # User would need to provide real credentials
            
            print(f"✅ Login form filled")
            
            # Submit login
            submit_button = driver.find_element(By.XPATH, "//button[@type='submit'] | //input[@type='submit'] | //button[contains(text(), 'Login')]")
            submit_button.click()
            time.sleep(8)
            
            print(f"✅ Login submitted")
            
        except Exception as e:
            print(f"⚠️ Login form not filled (credentials needed): {e}")
            print(f"🔸 Continuing without login to test search...")
        
        # STEP 4: Search for LM358N (like our working script)
        print(f"📱 STEP 4: Searching for {part_number}...")
        
        # Go back to main page if needed
        if "login" in driver.current_url.lower():
            driver.get("https://www.snapeda.com/")
            time.sleep(5)
        
        # Find search box
        try:
            search_box = driver.find_element(By.XPATH, "//input[@type='search'] | //input[@placeholder*='search' i] | //input[@name='q']")
            search_box.clear()
            search_box.send_keys(part_number)
            search_box.send_keys(Keys.RETURN)
            time.sleep(8)
            
            print(f"✅ Search completed")
            print(f"📍 Current URL: {driver.current_url}")
            
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return False
        
        # STEP 5: Look for the specific LM358N part (like our working script)
        print(f"📱 STEP 5: Looking for {part_number} part...")
        
        # Look for part links
        part_links = driver.find_elements(By.XPATH, f"//a[contains(text(), '{part_number}')]")
        
        print(f"Found {len(part_links)} potential part links:")
        for i, link in enumerate(part_links[:5]):
            try:
                text = link.text.strip()
                href = link.get_attribute('href')
                print(f"  {i+1}. '{text}' -> {href}")
            except:
                continue
        
        # Click on the first LM358N part link
        if part_links:
            try:
                first_link = part_links[0]
                text = first_link.text.strip()
                print(f"🎯 Clicking first part link: '{text}'")
                
                first_link.click()
                time.sleep(8)
                
                print(f"✅ Part page opened")
                print(f"📍 Current URL: {driver.current_url}")
                
            except Exception as e:
                print(f"❌ Could not click part link: {e}")
                return False
        else:
            print(f"❌ No part links found")
            return False
        
        # STEP 6: Look for 3D CAD tab (like our working script)
        print(f"📱 STEP 6: Looking for 3D CAD tab...")
        
        # Look for 3D/CAD related tabs
        cad_tabs = driver.find_elements(By.XPATH, "//a[contains(text(), '3D') or contains(text(), 'CAD') or contains(text(), 'Model')]")
        
        if cad_tabs:
            print(f"Found {len(cad_tabs)} CAD-related tabs:")
            for i, tab in enumerate(cad_tabs):
                try:
                    text = tab.text.strip()
                    print(f"  {i+1}. '{text}'")
                    
                    if '3d' in text.lower() or 'cad' in text.lower():
                        print(f"🎯 Clicking 3D/CAD tab: '{text}'")
                        tab.click()
                        time.sleep(8)
                        break
                except:
                    continue
        
        # STEP 7: Look for downloadable STEP files
        print(f"📱 STEP 7: Looking for downloadable STEP files...")
        
        # Look for STEP file download links
        step_links = driver.find_elements(By.XPATH, "//a[contains(@href, '.step') or contains(@href, '.stp') or contains(text(), 'STEP') or contains(text(), 'Download')]")
        
        if step_links:
            print(f"✅ Found {len(step_links)} potential STEP download links:")
            
            for i, link in enumerate(step_links):
                try:
                    text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    print(f"  {i+1}. '{text}' -> {href[:60]}...")
                    
                    # Try to download the first STEP file
                    if i == 0:
                        print(f"🔽 Attempting download: '{text}'")
                        
                        # Get downloads folder before click
                        downloads_dir = os.path.expanduser("~/Downloads")
                        before_files = set(os.listdir(downloads_dir))
                        
                        link.click()
                        time.sleep(10)
                        
                        # Check for new files
                        after_files = set(os.listdir(downloads_dir))
                        new_files = after_files - before_files
                        
                        step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            file_path = os.path.join(downloads_dir, step_files[0])
                            file_size = os.path.getsize(file_path)
                            print(f"🎉 DOWNLOAD SUCCESS: {step_files[0]} ({file_size} bytes)")
                            
                            # Move to 3d folder with proper naming
                            os.makedirs("3d", exist_ok=True)
                            target_file = f"3d/snapeda_Texas_Instruments_{part_number}.step"
                            shutil.move(file_path, target_file)
                            print(f"✅ Moved to: {target_file}")
                            
                            return True
                        else:
                            print(f"❌ No STEP files downloaded")
                
                except Exception as e:
                    print(f"❌ Error with download link {i+1}: {e}")
                    continue
        else:
            print(f"❌ No STEP download links found")
        
        # Keep browser open for manual inspection
        print(f"\n🔸 Browser staying open for 3 minutes for manual inspection...")
        print(f"👀 Check if you can manually find and download the 3D model!")
        time.sleep(180)
        
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    success = test_snapeda_lm358n_working_method()
    
    if success:
        print(f"\n🎉 SUCCESS: LM358N 3D model downloaded using working method!")
    else:
        print(f"\n❌ FAILED: Could not download LM358N 3D model")
        print(f"💡 This confirms that login credentials or specific navigation is required")
