#!/usr/bin/env python3
"""
FIXED ULTRALIBRARIAN AUTOMATION
===============================
Fixed to handle the URL redirect and use the correct search interface.
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome with download preferences"""
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def run_fixed_automation():
    """Run the fixed automation using the correct URL"""
    print("🔧 FIXED ULTRALIBRARIAN AUTOMATION")
    print("=" * 50)
    
    driver = setup_driver()
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # Step 1: Go to UltraLibrarian (it redirects to main site)
        print("\n🔸 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)  # Wait for redirect and page load
        
        print(f"✅ Page loaded: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        # Step 2: Find and use the search box
        print("\n🔸 STEP 2: Finding search box...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        
        search_box = None
        for i, inp in enumerate(inputs):
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower():
                        search_box = inp
                        print(f"✅ Found search box: '{placeholder}'")
                        break
            except:
                continue
        
        if not search_box:
            print("❌ No search box found!")
            return None
        
        # Step 3: Search for LM358N
        print("\n🔸 STEP 3: Searching for LM358N...")
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        
        print("✅ Search submitted, waiting for results...")
        time.sleep(10)
        
        print(f"New URL: {driver.current_url}")
        
        # Step 4: Look for LM358N results
        print("\n🔸 STEP 4: Looking for LM358N results...")
        
        # Wait a bit more for results to load
        time.sleep(5)
        
        # Look for links containing LM358N
        all_links = driver.find_elements(By.TAG_NAME, "a")
        lm358_links = []

        for link in all_links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''

                if (('lm358' in text.lower() or 'lm358' in href.lower()) and
                    link.is_displayed() and link.is_enabled()):
                    lm358_links.append((text, href, link))
                    print(f"  Found: '{text}' -> {href}")
            except:
                continue

        if not lm358_links:
            print("❌ No LM358N results found!")
            return None

        # Find the Texas Instruments LM358N/NOPB specifically
        ti_link = None
        for text, href, link_element in lm358_links:
            if ('lm358n/nopb' in text.lower() or
                'texas-instruments' in href.lower() or
                ('lm358n' in text.lower() and 'details' in href.lower() and 'login' not in href.lower())):
                ti_link = (text, href, link_element)
                break

        if not ti_link:
            print("❌ No Texas Instruments LM358N found!")
            print("Available options:")
            for text, href, _ in lm358_links[:5]:
                print(f"  - '{text}' -> {href}")
            return None

        # Click the Texas Instruments LM358N result
        text, href, link_element = ti_link
        print(f"\n🔸 STEP 5: Clicking on '{text}'...")
        
        driver.execute_script("arguments[0].scrollIntoView(true);", link_element)
        time.sleep(2)
        link_element.click()
        time.sleep(8)
        
        print(f"✅ Clicked on part, new URL: {driver.current_url}")
        
        # Step 6: Look for download options
        print("\n🔸 STEP 6: Looking for download options...")
        
        # Look for download buttons/links
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        all_links = driver.find_elements(By.TAG_NAME, "a")
        
        download_elements = []
        
        # Check buttons
        for btn in all_buttons:
            try:
                text = btn.text.strip().lower()
                if 'download' in text and btn.is_displayed() and btn.is_enabled():
                    download_elements.append(('button', btn.text, btn))
                    print(f"  Button: '{btn.text}'")
            except:
                continue
        
        # Check links
        for link in all_links:
            try:
                text = link.text.strip().lower()
                if 'download' in text and link.is_displayed() and link.is_enabled():
                    download_elements.append(('link', link.text, link))
                    print(f"  Link: '{link.text}'")
            except:
                continue
        
        if not download_elements:
            print("❌ No download options found!")
            print("Available buttons:")
            for btn in all_buttons[:10]:
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"  - '{text}'")
                except:
                    continue
            return None
        
        # Click first download option
        element_type, text, element = download_elements[0]
        print(f"\n🔸 STEP 7: Clicking {element_type} '{text}'...")
        
        driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(2)
        element.click()
        time.sleep(5)
        
        print(f"✅ Clicked download option")
        
        # Step 8: Monitor for downloads
        print("\n🔸 STEP 8: Monitoring for downloads...")
        
        for i in range(12):  # Monitor for 60 seconds
            time.sleep(5)
            current_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 New files detected: {list(new_files)}")
                
                # Check for STEP files
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    print(f"✅ STEP files found: {step_files}")
                    return step_files[0]
                
                # Check for ZIP files
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP file found: {zip_files[0]}")
                    # Try to extract
                    try:
                        import zipfile
                        zip_path = os.path.join('3D', zip_files[0])
                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        
                        # Check for extracted STEP files
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            print(f"✅ STEP file extracted: {step_files[0]}")
                            return step_files[0]
                    except Exception as e:
                        print(f"Error extracting ZIP: {e}")
            
            print(f"  Checking... ({(i+1)*5}/60 seconds)")
        
        print("❌ No files downloaded after 60 seconds")
        
        # Final manual step
        print("\n🔧 MANUAL COMPLETION:")
        print("The automation reached the download stage.")
        print("Please manually complete any remaining steps.")
        print("Browser will stay open for manual completion...")
        
        input("Press Enter when done: ")
        
        # Final check
        final_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
        final_new_files = final_files - initial_files
        
        if final_new_files:
            step_files = [f for f in final_new_files if f.lower().endswith(('.step', '.stp'))]
            if step_files:
                print(f"🎉 MANUAL SUCCESS: {step_files[0]}")
                return step_files[0]
        
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        driver.quit()

if __name__ == "__main__":
    result = run_fixed_automation()
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded to 3D/ folder!")
    else:
        print(f"\n❌ FAILED: No STEP file obtained")
