#!/usr/bin/env python3
"""
Go directly to LM358N product page and check for 3D models
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

def check_lm358n_product_page():
    print("CHECKING LM358N PRODUCT PAGE FOR 3D MODELS")
    print("=" * 50)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Go directly to LM358N product page
        product_url = "https://www.ti.com/product/LM358-N/part-details/LM358N/NOPB"
        print(f"1. Going to LM358N product page: {product_url}")
        
        driver.get(product_url)
        time.sleep(15)
        
        print(f"2. Current URL: {driver.current_url}")
        print(f"3. Page title: {driver.title}")
        
        # Get visible text
        body_text = driver.find_element(By.TAG_NAME, "body").text
        
        print("4. Looking for 3D model content...")
        
        # Check for 3D model indicators
        model_keywords = [
            '3d model', '3d', 'step file', 'step', '.step', '.stp',
            'mechanical', 'cad', 'download', 'model', 'package'
        ]
        
        found_keywords = {}
        for keyword in model_keywords:
            count = body_text.lower().count(keyword.lower())
            if count > 0:
                found_keywords[keyword] = count
        
        if found_keywords:
            print("✅ Found 3D-related keywords:")
            for keyword, count in found_keywords.items():
                print(f"  '{keyword}': {count} times")
        else:
            print("❌ No 3D-related keywords found")
        
        # Look for specific sections that might contain 3D models
        print("\n5. Looking for 3D model sections...")
        
        sections_to_check = [
            "//div[contains(@class, '3d')]",
            "//div[contains(@class, 'model')]", 
            "//div[contains(@class, 'cad')]",
            "//div[contains(@class, 'mechanical')]",
            "//div[contains(@class, 'package')]",
            "//section[contains(@class, 'download')]",
            "//a[contains(text(), '3D')]",
            "//a[contains(text(), 'STEP')]",
            "//a[contains(text(), 'Model')]",
            "//a[contains(@href, '.step')]",
            "//a[contains(@href, '.stp')]"
        ]
        
        found_sections = []
        for xpath in sections_to_check:
            try:
                elements = driver.find_elements(By.XPATH, xpath)
                if elements:
                    found_sections.append((xpath, len(elements)))
                    print(f"  Found {len(elements)} elements: {xpath}")
            except:
                continue
        
        if not found_sections:
            print("❌ No 3D model sections found")
        
        # Look for download links
        print("\n6. Looking for download links...")
        
        download_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'download') or contains(text(), 'Download') or contains(text(), 'download')]")
        
        if download_links:
            print(f"Found {len(download_links)} download links:")
            for i, link in enumerate(download_links[:5]):
                try:
                    text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    print(f"  {i+1}. '{text}' -> {href[:60]}...")
                except:
                    continue
        else:
            print("❌ No download links found")
        
        # Final determination
        print("\n7. FINAL DETERMINATION:")
        
        has_3d_indicators = any(keyword in ['3d', 'step', 'model', 'cad'] for keyword in found_keywords.keys())
        has_download_links = len(download_links) > 0
        
        if has_3d_indicators and has_download_links:
            print("✅ LIKELY: LM358N appears to have 3D model support")
            return True
        elif has_3d_indicators:
            print("⚠️ MAYBE: Found 3D keywords but no clear download links")
            return False
        else:
            print("❌ NO: No evidence of 3D models for LM358N")
            return False
        
    finally:
        input("Press Enter to close...")
        driver.quit()

if __name__ == "__main__":
    has_3d = check_lm358n_product_page()
    print(f"\nFINAL RESULT: LM358N 3D model available = {has_3d}")
