#!/usr/bin/env python3
"""
Component Finder - Account Creation Script
Creates accounts on component library sites for automated STEP file downloads
"""

import requests
import time
import json
import random
import string
from urllib.parse import urljoin, urlparse

class AccountCreator:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
    def generate_credentials(self, site_name):
        """Generate unique credentials for each site"""
        random_suffix = ''.join(random.choices(string.digits, k=6))
        
        credentials = {
            'email': f'component.finder.{random_suffix}@gmail.com',
            'password': f'CompFinder2024!{random_suffix}',
            'company': 'Component Research Labs',
            'first_name': 'Component',
            'last_name': 'Finder'
        }
        
        print(f"Generated credentials for {site_name}:")
        print(f"  Email: {credentials['email']}")
        print(f"  Password: {credentials['password']}")
        print(f"  Company: {credentials['company']}")
        
        return credentials
    
    def create_snapeda_account(self):
        """Create SnapEDA account"""
        print("\n🎯 CREATING SNAPEDA ACCOUNT")
        print("=" * 40)
        
        try:
            # Get signup page first
            signup_url = 'https://www.snapeda.com/account/signup'
            print(f"1. Getting signup page: {signup_url}")
            
            response = self.session.get(signup_url, timeout=30)
            if response.status_code != 200:
                print(f"   ❌ Failed to get signup page: {response.status_code}")
                return None
                
            print(f"   ✅ Got signup page")
            
            # Generate credentials
            creds = self.generate_credentials('SnapEDA')
            
            # Extract CSRF token if present
            csrf_token = None
            if 'csrfmiddlewaretoken' in response.text:
                import re
                csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', response.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
                    print(f"   ✅ Found CSRF token: {csrf_token[:20]}...")
            
            # Prepare signup data
            signup_data = {
                'email': creds['email'],
                'company': creds['company'],
                'password1': creds['password'],
                'password2': creds['password'],
                'plugin': '',
                'ref_email': ''
            }
            
            if csrf_token:
                signup_data['csrfmiddlewaretoken'] = csrf_token
            
            print(f"2. Submitting signup form...")
            
            # Submit signup form
            signup_response = self.session.post(
                signup_url,
                data=signup_data,
                timeout=30,
                allow_redirects=True
            )
            
            print(f"   Status: {signup_response.status_code}")
            print(f"   URL: {signup_response.url}")
            
            if signup_response.status_code == 200:
                if 'welcome' in signup_response.url.lower() or 'home' in signup_response.url.lower():
                    print(f"   ✅ Account created successfully!")
                    return creds
                elif 'recaptcha' in signup_response.text.lower():
                    print(f"   ❌ reCAPTCHA required - manual intervention needed")
                    return None
                else:
                    print(f"   ❌ Signup may have failed - check response")
                    # Save response for debugging
                    with open('snapeda_signup_response.html', 'w', encoding='utf-8') as f:
                        f.write(signup_response.text)
                    print(f"   📄 Saved response to snapeda_signup_response.html")
                    return None
            else:
                print(f"   ❌ Signup failed with status {signup_response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ Error creating SnapEDA account: {e}")
            return None
    
    def create_ultralibrarian_account(self):
        """Create UltraLibrarian account"""
        print("\n🎯 CREATING ULTRALIBRARIAN ACCOUNT")
        print("=" * 40)
        
        try:
            # Get signup page
            signup_url = 'https://www.ultralibrarian.com/register'
            print(f"1. Getting signup page: {signup_url}")
            
            response = self.session.get(signup_url, timeout=30)
            if response.status_code != 200:
                print(f"   ❌ Failed to get signup page: {response.status_code}")
                return None
                
            print(f"   ✅ Got signup page")
            
            # Generate credentials
            creds = self.generate_credentials('UltraLibrarian')
            
            # This would need to be implemented based on their actual form
            print(f"   ⚠️  UltraLibrarian signup needs manual implementation")
            print(f"   📄 Please create account manually at: {signup_url}")
            
            return None
            
        except Exception as e:
            print(f"   ❌ Error creating UltraLibrarian account: {e}")
            return None
    
    def create_samacsys_account(self):
        """Create SamacSys account"""
        print("\n🎯 CREATING SAMACSYS ACCOUNT")
        print("=" * 40)
        
        try:
            # Get signup page
            signup_url = 'https://componentsearchengine.com/register'
            print(f"1. Getting signup page: {signup_url}")
            
            response = self.session.get(signup_url, timeout=30)
            if response.status_code != 200:
                print(f"   ❌ Failed to get signup page: {response.status_code}")
                return None
                
            print(f"   ✅ Got signup page")
            
            # Generate credentials
            creds = self.generate_credentials('SamacSys')
            
            # This would need to be implemented based on their actual form
            print(f"   ⚠️  SamacSys signup needs manual implementation")
            print(f"   📄 Please create account manually at: {signup_url}")
            
            return None
            
        except Exception as e:
            print(f"   ❌ Error creating SamacSys account: {e}")
            return None
    
    def save_credentials(self, credentials_dict):
        """Save all credentials to file"""
        if not credentials_dict:
            print("No credentials to save")
            return
            
        print(f"\n💾 SAVING CREDENTIALS")
        print("=" * 30)
        
        try:
            with open('component_site_credentials.json', 'w') as f:
                json.dump(credentials_dict, f, indent=2)
            print(f"✅ Credentials saved to component_site_credentials.json")
            
            # Also create a readable text file
            with open('component_site_credentials.txt', 'w') as f:
                f.write("Component Site Credentials\n")
                f.write("=" * 30 + "\n\n")
                
                for site, creds in credentials_dict.items():
                    if creds:
                        f.write(f"{site}:\n")
                        f.write(f"  Email: {creds['email']}\n")
                        f.write(f"  Password: {creds['password']}\n")
                        f.write(f"  Company: {creds['company']}\n\n")
                        
            print(f"✅ Readable credentials saved to component_site_credentials.txt")
            
        except Exception as e:
            print(f"❌ Error saving credentials: {e}")

def main():
    print("🎯 COMPONENT FINDER - ACCOUNT CREATOR")
    print("=" * 50)
    
    creator = AccountCreator()
    all_credentials = {}
    
    # Create accounts on each site
    snapeda_creds = creator.create_snapeda_account()
    if snapeda_creds:
        all_credentials['SnapEDA'] = snapeda_creds
    
    time.sleep(2)  # Be nice to servers
    
    ultralibrarian_creds = creator.create_ultralibrarian_account()
    if ultralibrarian_creds:
        all_credentials['UltraLibrarian'] = ultralibrarian_creds
    
    time.sleep(2)
    
    samacsys_creds = creator.create_samacsys_account()
    if samacsys_creds:
        all_credentials['SamacSys'] = samacsys_creds
    
    # Save all credentials
    creator.save_credentials(all_credentials)
    
    print(f"\n🎯 ACCOUNT CREATION COMPLETE")
    print("=" * 40)
    print(f"Created {len(all_credentials)} accounts")
    
    if all_credentials:
        print("Next steps:")
        print("1. Verify email addresses if required")
        print("2. Test login on each site")
        print("3. Use credentials for automated STEP file downloads")

if __name__ == "__main__":
    main()
