#!/usr/bin/env python3
"""
STEP 4: Use JavaScript to search on TI website
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

def javascript_search():
    print("STEP 4: JavaScript search on TI website")
    print("=" * 50)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Load TI website
        print("Loading https://www.ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(15)
        
        # Try JavaScript approach
        print("Trying JavaScript search...")
        
        # Method 1: Try to trigger Coveo search directly
        js_commands = [
            # Try to find and use Coveo search
            "if (window.Coveo) { console.log('Coveo found'); window.Coveo.executeQuery('LM358N'); }",
            
            # Try to set search value and submit
            "var searchBox = document.querySelector('#searchboxheader'); if (searchBox) { searchBox.click(); }",
            
            # Wait and try to find input
            "setTimeout(function() { var input = document.querySelector('input[type=\"search\"], input[placeholder*=\"search\"]'); if (input) { input.value = 'LM358N'; input.dispatchEvent(new Event('input')); input.form.submit(); } }, 2000);",
            
            # Try direct navigation to search results
            "window.location.href = 'https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm=LM358N';"
        ]
        
        for i, js_cmd in enumerate(js_commands):
            try:
                print(f"Executing JavaScript command {i+1}...")
                result = driver.execute_script(js_cmd)
                print(f"Result: {result}")
                time.sleep(5)
                
                # Check if URL changed (indicating search worked)
                current_url = driver.current_url
                print(f"Current URL: {current_url}")
                
                if "search" in current_url.lower() or "LM358N" in current_url:
                    print("✅ SUCCESS: Search URL detected!")
                    
                    # Check for results
                    time.sleep(5)
                    if "LM358N" in driver.page_source.upper():
                        print("✅ SUCCESS: Found LM358N in page content!")
                        break
                    else:
                        print("⚠️ Search URL found but no LM358N content yet")
                        
            except Exception as e:
                print(f"Error with command {i+1}: {e}")
        
        print(f"Final URL: {driver.current_url}")
        print("JavaScript search test completed.")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    javascript_search()
