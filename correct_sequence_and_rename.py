#!/usr/bin/env python3
"""
CORRECT SEQUENCE AND RENAME
===========================
Login -> 3D CAD -> STEP -> Download -> Rename to ultra-ti-lm358n.step
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def correct_sequence_and_rename():
    print("🎯 CORRECT SEQUENCE AND RENAME")
    print("=" * 40)
    
    # Setup Chrome with downloads
    chrome_options = Options()
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # Quick navigation to login screen
        print("🔸 Navigating to login screen...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        # Quick steps to get to login
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    link.click()
                    break
            except:
                continue
        time.sleep(8)
        
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            driver.execute_script("arguments[0].click();", download_btns[0])
            time.sleep(5)
        
        cad_btns = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
        if cad_btns:
            driver.execute_script("arguments[0].click();", cad_btns[0])
            time.sleep(5)
        
        if len(driver.window_handles) > 1:
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        step_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP')]")
        for elem in step_elements:
            if elem.is_displayed():
                driver.execute_script("arguments[0].click();", elem)
                time.sleep(3)
                break
        
        download_elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')]")
        for elem in download_elements:
            if elem.is_displayed():
                driver.execute_script("arguments[0].click();", elem)
                time.sleep(5)
                break
        
        print("✅ At login screen")
        
        # Do login
        print("\n🔸 Doing login...")
        
        with open('component_site_credentials.json', 'r') as f:
            credentials = json.load(f)
        
        email = credentials['UltraLibrarian']['email']
        password = credentials['UltraLibrarian']['password']
        
        # Enter credentials and login (same as before)
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email'], input[placeholder*='email'], input[name*='Email'], input[name*='username'], input[name*='Username']")
        
        email_input = None
        for inp in email_inputs:
            if inp.is_displayed() and inp.is_enabled():
                email_input = inp
                break
        
        if not email_input:
            text_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='text']")
            for inp in text_inputs:
                if inp.is_displayed() and inp.is_enabled():
                    email_input = inp
                    break
        
        if email_input:
            email_input.clear()
            email_input.send_keys(email)
            print("✅ Entered email")
            time.sleep(2)
        
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        password_input = None
        for inp in password_inputs:
            if inp.is_displayed() and inp.is_enabled():
                password_input = inp
                break
        
        if password_input:
            password_input.clear()
            password_input.send_keys(password)
            print("✅ Entered password")
            time.sleep(2)
        
        # Click login
        login_selectors = [
            "button[type='submit']",
            "input[type='submit']",
            "//button[contains(text(), 'Login')]",
            "//button[contains(text(), 'Sign In')]",
            "//button[contains(text(), 'Log In')]"
        ]
        
        login_clicked = False
        for selector in login_selectors:
            try:
                if selector.startswith("//"):
                    buttons = driver.find_elements(By.XPATH, selector)
                else:
                    buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                
                for btn in buttons:
                    if btn.is_displayed() and btn.is_enabled():
                        driver.execute_script("arguments[0].click();", btn)
                        print("✅ Clicked login button")
                        login_clicked = True
                        break
                
                if login_clicked:
                    break
            except:
                continue
        
        if not login_clicked and password_input:
            password_input.send_keys(Keys.RETURN)
            print("✅ Pressed Enter to login")
        
        time.sleep(10)
        print(f"After login URL: {driver.current_url}")
        print("✅ Login completed - back at previous screen")
        
        # CORRECT SEQUENCE: 3D CAD -> STEP -> DOWNLOAD
        print("\n🔸 CORRECT SEQUENCE: 3D CAD -> STEP -> DOWNLOAD")
        
        # STEP 1: Click 3D CAD again
        print("\n🔸 STEP 1: Clicking 3D CAD again...")
        cad_btns_2 = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')] | //a[contains(text(), '3D CAD Model')] | //a[contains(text(), '3D Model')]")
        
        cad_clicked = False
        for btn in cad_btns_2:
            if btn.is_displayed() and btn.is_enabled():
                driver.execute_script("arguments[0].click();", btn)
                print("✅ Clicked 3D CAD Model again")
                cad_clicked = True
                time.sleep(5)
                break
        
        if not cad_clicked:
            print("⚠️ No 3D CAD button found, continuing...")
        
        # Check for new window again
        if len(driver.window_handles) > 1:
            print("✅ Switching to latest window...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        # STEP 2: Select STEP again
        print("\n🔸 STEP 2: Selecting STEP format again...")
        step_elements_2 = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP')]")
        
        step_clicked = False
        for elem in step_elements_2:
            if elem.is_displayed():
                driver.execute_script("arguments[0].click();", elem)
                print("✅ Selected STEP format again")
                step_clicked = True
                time.sleep(3)
                break
        
        if not step_clicked:
            print("⚠️ No STEP element found, continuing...")
        
        # STEP 3: Download
        print("\n🔸 STEP 3: Clicking Download...")
        download_elements_2 = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')] | //input[@type='submit']")
        
        download_clicked = False
        for elem in download_elements_2:
            if elem.is_displayed() and elem.is_enabled():
                driver.execute_script("arguments[0].click();", elem)
                print("✅ Clicked Download")
                download_clicked = True
                time.sleep(5)
                break
        
        if not download_clicked:
            print("⚠️ No download button found")
        
        # Monitor for downloads and rename
        print("\n🔸 MONITORING FOR DOWNLOADS AND RENAMING...")
        
        for i in range(30):  # Monitor for 2.5 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 NEW FILES: {list(new_files)}")
                
                # Check for STEP files
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    original_file = step_files[0]
                    original_path = os.path.join('3D', original_file)
                    new_name = 'ultra-ti-lm358n.step'
                    new_path = os.path.join('3D', new_name)
                    
                    try:
                        os.rename(original_path, new_path)
                        print(f"✅ RENAMED: {original_file} -> {new_name}")
                        return new_name
                    except Exception as e:
                        print(f"⚠️ Rename failed: {e}")
                        return original_file
                
                # Check for ZIP files
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP FILE: {zip_files[0]}")
                    try:
                        import zipfile
                        with zipfile.ZipFile(os.path.join('3D', zip_files[0]), 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            original_file = step_files[0]
                            original_path = os.path.join('3D', original_file)
                            new_name = 'ultra-ti-lm358n.step'
                            new_path = os.path.join('3D', new_name)
                            
                            try:
                                os.rename(original_path, new_path)
                                print(f"✅ EXTRACTED AND RENAMED: {original_file} -> {new_name}")
                                return new_name
                            except Exception as e:
                                print(f"⚠️ Rename failed: {e}")
                                return original_file
                    except Exception as e:
                        print(f"Error extracting: {e}")
            
            print(f"  Checking... ({(i+1)*5}/150 seconds)")
        
        print("⏳ No files downloaded")
        
        # Keep browser open
        print("\n🔒 BROWSER STAYING OPEN")
        while True:
            time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        while True:
            time.sleep(10)

if __name__ == "__main__":
    result = correct_sequence_and_rename()
    if result:
        print(f"\n🎉 SUCCESS: {result}")
    else:
        print(f"\n⚠️ Process completed")
