#!/usr/bin/env python3
"""
SnapEDA 3D model searcher
Can be used by main program or run standalone
"""

import requests
from bs4 import BeautifulSoup
import json
import os
import sys
import brotli

class SnapEDA3DSearcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
    
    def search_part_3d(self, part_number):
        """Search SnapEDA for 3D models"""
        print(f"🔍 SEARCHING SNAPEDA FOR: {part_number}")
        
        try:
            # Use SnapEDA's search API
            api_url = 'https://www.snapeda.com/api/v1/search_local'
            params = {'q': part_number, 'page': 1}
            
            response = self.session.get(api_url, params=params, timeout=30)
            print(f"   API Status: {response.status_code}")
            
            if response.status_code == 200:
                # Handle compressed response
                try:
                    data = response.json()
                except:
                    # Try manual decompression
                    content_encoding = response.headers.get('content-encoding', '')
                    if content_encoding == 'br':
                        decompressed = brotli.decompress(response.content).decode('utf-8')
                        data = json.loads(decompressed)
                    else:
                        print(f"   ❌ Could not parse API response")
                        return None
                
                # Save API response
                with open('snapeda_api_response.json', 'w') as f:
                    json.dump(data, f, indent=2)
                
                # Check for parts
                parts = data.get('parts', [])
                if not parts:
                    print(f"   ❌ No parts found in API response")
                    return None
                
                print(f"   ✅ Found {len(parts)} parts")
                
                # Look for our specific part
                target_part = None
                for part in parts:
                    part_name = part.get('name', '')
                    if part_number.upper() in part_name.upper():
                        target_part = part
                        break
                
                if not target_part:
                    print(f"   ❌ Target part not found")
                    return None
                
                # Check if 3D model is available
                has_3d = target_part.get('has_3d', 0)
                if has_3d == 1:
                    print(f"   🎯 3D MODEL AVAILABLE!")
                    return self.get_part_page_3d(target_part, part_number)
                else:
                    print(f"   ❌ No 3D model available for this part")
                    return None
                    
            else:
                print(f"   ❌ API failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ SEARCH ERROR: {e}")
            return None
    
    def get_part_page_3d(self, part_data, part_number):
        """Access the part page to get 3D model download links"""
        try:
            # Build part URL
            part_url = f"https://www.snapeda.com/parts/{part_data['urlname']}/{part_data['manufacturer']}/view-part/"
            print(f"   Accessing part page: {part_url}")
            
            response = self.session.get(part_url, timeout=30)
            print(f"   Part page status: {response.status_code}")
            
            if response.status_code == 200:
                # Save part page
                with open('snapeda_part_page.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                # Look for download links
                soup = BeautifulSoup(response.text, 'html.parser')
                
                download_links = []
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '').lower()
                    text = link.get_text(strip=True).lower()
                    
                    if any(keyword in href or keyword in text 
                           for keyword in ['download', 'step', '3d', 'model']):
                        download_links.append({
                            'url': link.get('href'),
                            'text': link.get_text(strip=True)
                        })
                
                if download_links:
                    print(f"   🎯 Found {len(download_links)} download links")
                    return self.download_3d_models(download_links, part_number)
                else:
                    print(f"   ❌ No download links found")
                    return None
            else:
                print(f"   ❌ Failed to access part page")
                return None
                
        except Exception as e:
            print(f"   ❌ PART PAGE ERROR: {e}")
            return None
    
    def download_3d_models(self, download_links, part_number):
        """Download 3D models from SnapEDA"""
        downloaded_files = []
        
        for i, link in enumerate(download_links[:3], 1):  # Try first 3
            try:
                url = link['url']
                
                # Make URL absolute
                if not url.startswith('http'):
                    url = f"https://www.snapeda.com{url}"
                
                print(f"📥 Trying download {i}: {link['text']}")
                print(f"   URL: {url}")
                
                response = self.session.get(url, timeout=60, stream=True)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    
                    # Check if it's a STEP file or download
                    if any(ct in content_type for ct in ['application/octet-stream', 'application/step', 'model/step']):
                        filename = f"SnapEDA-{part_number}.step"
                        if self.save_step_file(response, filename):
                            downloaded_files.append(filename)
                    
                    # If it's HTML, might be a download page
                    elif 'text/html' in content_type:
                        # Parse for direct STEP links
                        soup = BeautifulSoup(response.text, 'html.parser')
                        step_links = []
                        
                        for step_link in soup.find_all('a', href=True):
                            href = step_link.get('href')
                            if any(ext in href.lower() for ext in ['.step', '.stp']):
                                step_links.append(href)
                        
                        if step_links:
                            step_url = step_links[0]
                            if not step_url.startswith('http'):
                                step_url = f"https://www.snapeda.com{step_url}"
                            
                            print(f"   Found STEP link: {step_url}")
                            step_response = self.session.get(step_url, timeout=60, stream=True)
                            
                            if step_response.status_code == 200:
                                filename = f"SnapEDA-{part_number}.step"
                                if self.save_step_file(step_response, filename):
                                    downloaded_files.append(filename)
                
            except Exception as e:
                print(f"   ❌ Download {i} error: {e}")
        
        return downloaded_files
    
    def save_step_file(self, response, filename):
        """Save STEP file and verify"""
        try:
            # Create 3d directory
            os.makedirs('3d', exist_ok=True)
            filepath = os.path.join('3d', filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                # Verify STEP file
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    first_lines = f.read(200)
                
                if 'ISO-10303' in first_lines or 'STEP' in first_lines:
                    print(f"   🎉 SUCCESS: Valid STEP file!")
                    return True
                else:
                    print(f"   ⚠️  File doesn't appear to be STEP format")
                    return False
            else:
                print(f"   ⚠️  File too small")
                return False
                
        except Exception as e:
            print(f"   ❌ Save error: {e}")
            return False

def main():
    """Standalone usage"""
    if len(sys.argv) < 2:
        print("Usage: python snapeda_3d_searcher.py <part_number>")
        print("Example: python snapeda_3d_searcher.py APX803L20-30SA-7")
        return
    
    part_number = sys.argv[1]
    
    print("🚀 SNAPEDA 3D MODEL SEARCHER")
    print("=" * 40)
    
    searcher = SnapEDA3DSearcher()
    
    # Search for 3D models
    downloaded = searcher.search_part_3d(part_number)
    
    print("\n" + "=" * 40)
    if downloaded:
        print(f"✅ SUCCESS: Downloaded {len(downloaded)} STEP files from SnapEDA!")
        for file in downloaded:
            print(f"   🎯 {file}")
    else:
        print("❌ FAILED: Could not find or download 3D models from SnapEDA")

if __name__ == "__main__":
    main()
