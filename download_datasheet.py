#!/usr/bin/env python3
"""
Download the datasheet from the URL we found
"""

import requests
import os

def download_datasheet():
    print("📥 DOWNLOADING DATASHEET")
    print("=" * 30)
    
    # The datasheet URL we found in the Digikey JSON
    datasheet_url = "https://www.diodes.com/assets/Datasheets/APX803L.pdf"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        print(f"Downloading: {datasheet_url}")
        response = session.get(datasheet_url, timeout=60, stream=True)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            # Create files-download directory
            os.makedirs('files-download', exist_ok=True)
            
            # Save datasheet
            filename = "APX803L20-30SA-7_datasheet.pdf"
            filepath = os.path.join('files-download', filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 10000:  # At least 10KB
                print("🎉 SUCCESS: Datasheet downloaded!")
                return True
            else:
                print("⚠️  File too small, might be error page")
                return False
        else:
            print(f"❌ Download failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = download_datasheet()
    
    if success:
        print("\n✅ STEP 1 COMPLETE: Got datasheet from Digikey!")
        print("📄 Next: Look for manufacturer website and 3D models")
    else:
        print("\n❌ FAILED: Could not download datasheet")
