@echo off
echo.
echo ========================================
echo SIMPLE STEP FILE GETTER
echo ========================================
echo.

if [%1]==[] (
    echo Enter the component details:
    echo.
    set /p manufacturer="Manufacturer: "
    set /p part_number="Part Number: "
    echo.
    echo Searching for 3D model...
    python simple_step_getter.py "%manufacturer%" "%part_number%"
) else (
    if [%2]==[] (
        echo Usage: get_step_file.bat "Manufacturer" "Part Number"
        echo.
        echo Examples:
        echo   get_step_file.bat "Diodes Inc" "APX803L20-30SA-7"
        echo   get_step_file.bat "TI" "LM358N"
        pause
        exit /b 1
    )
    echo Searching for 3D model...
    python simple_step_getter.py %1 %2
)

echo.
echo ========================================
if exist "step_files" (
    echo Files saved to: step_files\
    dir step_files /b
) else (
    echo No files downloaded
)
echo.
pause
