#!/usr/bin/env python3
"""
STEP 2: Enter data in TI search bar like we did yesterday
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time

def test_search():
    print("STEP 2: Entering LM358N in TI search bar")
    print("=" * 50)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Load TI website
        print("Loading https://www.ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(15)
        
        # Find and click search box
        print("Finding search box...")
        search_box = driver.find_element(By.CSS_SELECTOR, "#searchboxheader")
        print(f"Found search box: {search_box.tag_name}")
        
        # Click on search box
        print("Clicking search box...")
        search_box.click()
        time.sleep(3)
        
        # Look for actual input field that appears
        print("Looking for input field...")
        input_selectors = [
            "#searchboxheader input",
            ".coveo-search-section input",
            "input[type='search']",
            "input[placeholder*='search' i]"
        ]
        
        search_input = None
        for selector in input_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements and elements[0].is_displayed():
                    search_input = elements[0]
                    print(f"Found input with selector: {selector}")
                    break
            except:
                continue
        
        if search_input:
            # Enter search term
            print("Entering 'LM358N'...")
            search_input.clear()
            search_input.send_keys("LM358N")
            search_input.send_keys(Keys.RETURN)
            time.sleep(10)
            
            print(f"Search completed! Current URL: {driver.current_url}")
            
            # Check if we got results
            if "LM358N" in driver.page_source.upper():
                print("✅ SUCCESS: Found LM358N in results!")
            else:
                print("❌ No LM358N found in results")
        else:
            print("❌ Could not find input field after clicking search box")
        
        print("Test completed.")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_search()
