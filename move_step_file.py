#!/usr/bin/env python3
"""
Move STEP file from Downloads to 3d folder with proper naming
"""

import os
import shutil

def move_step_file():
    downloads_dir = os.path.expanduser("~/Downloads")
    local_3d_dir = os.path.abspath('3d')
    
    # Ensure 3d directory exists
    os.makedirs(local_3d_dir, exist_ok=True)
    
    # Find LM358N STEP files
    files = os.listdir(downloads_dir)
    step_files = [f for f in files if 'LM358N' in f.upper() and f.lower().endswith(('.step', '.stp'))]
    
    if not step_files:
        print("❌ No LM358N STEP files found in Downloads")
        return False
    
    step_file = step_files[0]
    print(f"✅ Found STEP file: {step_file}")
    
    # Create proper filename
    manufacturer = "TI"
    base_name = step_file.replace(" (1)", "").replace(" ", "_")
    new_name = f"ultralibrarian_{manufacturer}_{base_name}"
    
    src_path = os.path.join(downloads_dir, step_file)
    dst_path = os.path.join(local_3d_dir, new_name)
    
    try:
        shutil.move(src_path, dst_path)
        print(f"✅ Moved to 3d folder: {new_name}")
        return True
    except Exception as e:
        print(f"❌ Error moving file: {e}")
        return False

if __name__ == "__main__":
    success = move_step_file()
    if success:
        print("\n🎉 SUCCESS: File moved to 3d folder!")
    else:
        print("\n❌ FAILED: Could not move file")
