#!/usr/bin/env python3
"""
SAMACSYS GENERIC AUTOMATION
===========================
Generic SamacSys automation for any electronic component.

Usage: python samacsys.py "PART_NUMBER"
Example: python samacsys.py "LM358N"
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome with download preferences"""
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def samacsys_automation(part_number):
    """
    Generic SamacSys automation for any part number
    
    Args:
        part_number (str): Electronic component part number
        
    Returns:
        str: Downloaded STEP filename or None if failed
    """
    print(f"🎯 SAMACSYS AUTOMATION: {part_number}")
    print("=" * 60)
    
    driver = setup_driver()
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # STEP 1: Load SamacSys Component Search Engine
        print("\n🔸 STEP 1: Loading SamacSys Component Search Engine...")
        driver.get('https://componentsearchengine.com')
        time.sleep(15)
        print(f"✅ Loaded: {driver.title}")
        
        # STEP 2: Search for part
        print(f"\n🔸 STEP 2: Searching for {part_number}...")
        
        # Try multiple search box selectors including SamacSys specific ones
        search_selectors = [
            "input[type='search']",
            "input[placeholder*='search']",
            "input[placeholder*='Search']",
            "input[placeholder*='part']",
            "input[placeholder*='Part']",
            "input[name*='search']",
            "input[name*='query']",
            "input[name*='q']",
            "input[id*='search']",
            "input[id*='query']",
            "#search",
            "#searchbox",
            "#query",
            ".search-input",
            ".search-box",
            "input[type='text']"
        ]

        search_box = None
        for selector in search_selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for elem in elements:
                if elem.is_displayed() and elem.is_enabled():
                    search_box = elem
                    print(f"✅ Found search box with selector: {selector}")
                    break
            if search_box:
                break
        
        if not search_box:
            print("❌ No search box found!")
            return None
        
        search_box.clear()
        search_box.send_keys(part_number)
        search_box.send_keys(Keys.RETURN)
        print("✅ Search submitted")
        time.sleep(10)
        
        # STEP 3: Find and click first relevant part
        print(f"\n🔸 STEP 3: Looking for {part_number} in results...")
        
        # Look for part links
        part_selectors = [
            f"//a[contains(text(), '{part_number}')]",
            f"//a[contains(@href, '{part_number}')]",
            f"//a[contains(text(), '{part_number.upper()}')]",
            f"//a[contains(text(), '{part_number.lower()}')]"
        ]
        
        part_link = None
        for selector in part_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed() and elem.is_enabled():
                    part_link = elem
                    print(f"✅ Found part: {elem.text}")
                    break
            if part_link:
                break
        
        if not part_link:
            print(f"❌ No {part_number} found in results!")
            return None
        
        part_link.click()
        print("✅ Clicked on part")
        time.sleep(8)
        
        # STEP 4: Look for 3D model or STEP download options
        print("\n🔸 STEP 4: Looking for 3D model/STEP download options...")
        
        # Look for various download-related elements
        download_keywords = ['3d', 'step', 'model', 'cad', 'download', 'file']
        download_elements = []
        
        # Check buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        for btn in buttons:
            try:
                text = btn.text.strip().lower()
                if any(keyword in text for keyword in download_keywords) and btn.is_displayed():
                    download_elements.append(('button', btn.text.strip(), btn))
                    print(f"  Found button: '{btn.text.strip()}'")
            except:
                continue
        
        # Check links
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip().lower()
                href = (link.get_attribute('href') or '').lower()
                if (any(keyword in text for keyword in download_keywords) or 
                    any(keyword in href for keyword in download_keywords)) and link.is_displayed():
                    download_elements.append(('link', link.text.strip(), link))
                    print(f"  Found link: '{link.text.strip()}'")
            except:
                continue
        
        if not download_elements:
            print("❌ No download options found!")
            return None
        
        # STEP 5: Click the most relevant download element
        print("\n🔸 STEP 5: Clicking download option...")
        
        # Prioritize 3D/STEP/CAD elements
        priority_keywords = ['3d', 'step', 'cad']
        clicked = False
        
        for element_type, text, element in download_elements:
            if any(keyword in text.lower() for keyword in priority_keywords):
                print(f"✅ Clicking {element_type}: '{text}'")
                try:
                    driver.execute_script("arguments[0].click();", element)
                    clicked = True
                    time.sleep(5)
                    break
                except Exception as e:
                    print(f"⚠️ Click failed: {e}")
                    continue
        
        if not clicked:
            # Try any download element
            for element_type, text, element in download_elements:
                print(f"✅ Trying {element_type}: '{text}'")
                try:
                    driver.execute_script("arguments[0].click();", element)
                    clicked = True
                    time.sleep(5)
                    break
                except Exception as e:
                    print(f"⚠️ Click failed: {e}")
                    continue
        
        if not clicked:
            print("❌ Could not click any download option!")
            return None
        
        # STEP 6: Handle login if needed
        print("\n🔸 STEP 6: Checking for login...")
        
        # Check if we're on login screen or login form appeared
        current_url = driver.current_url.lower()
        login_required = ('login' in current_url or 'signin' in current_url or 'sign-in' in current_url)
        
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email'], input[placeholder*='email']")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        if email_inputs or password_inputs or login_required:
            print("🔐 Login required, attempting login...")
            
            try:
                with open('component_site_credentials.json', 'r') as f:
                    credentials = json.load(f)
                
                if 'SamacSys' not in credentials:
                    print("❌ No SamacSys credentials found in credentials file!")
                    return None
                
                email = credentials['SamacSys']['email']
                password = credentials['SamacSys']['password']
                
                # Enter email
                email_input = None
                for inp in email_inputs:
                    if inp.is_displayed() and inp.is_enabled():
                        email_input = inp
                        break
                
                if not email_input:
                    text_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='text']")
                    for inp in text_inputs:
                        if inp.is_displayed() and inp.is_enabled():
                            email_input = inp
                            break
                
                if email_input:
                    email_input.clear()
                    email_input.send_keys(email)
                    print("✅ Entered email")
                    time.sleep(2)
                
                # Enter password
                password_input = None
                for inp in password_inputs:
                    if inp.is_displayed() and inp.is_enabled():
                        password_input = inp
                        break
                
                if password_input:
                    password_input.clear()
                    password_input.send_keys(password)
                    print("✅ Entered password")
                    time.sleep(2)
                
                # Submit login
                login_selectors = [
                    "button[type='submit']",
                    "input[type='submit']",
                    "//button[contains(text(), 'Login')]",
                    "//button[contains(text(), 'Sign In')]",
                    "//button[contains(text(), 'Log In')]"
                ]
                
                login_clicked = False
                for selector in login_selectors:
                    try:
                        if selector.startswith("//"):
                            buttons = driver.find_elements(By.XPATH, selector)
                        else:
                            buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                        
                        for btn in buttons:
                            if btn.is_displayed() and btn.is_enabled():
                                driver.execute_script("arguments[0].click();", btn)
                                print("✅ Clicked login button")
                                login_clicked = True
                                break
                        
                        if login_clicked:
                            break
                    except:
                        continue
                
                if not login_clicked:
                    if password_input:
                        password_input.send_keys(Keys.RETURN)
                        print("✅ Pressed Enter to submit login")
                
                time.sleep(10)
                print(f"After login URL: {driver.current_url}")
                
            except Exception as e:
                print(f"⚠️ Login failed: {e}")
        
        # STEP 7: Monitor for downloads
        print("\n🔸 STEP 7: Monitoring for downloads...")
        
        for i in range(30):  # Monitor for 2.5 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 NEW FILES: {list(new_files)}")
                
                # Check for STEP files
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    original_file = step_files[0]
                    clean_part = part_number.replace('/', '-').replace('\\', '-')
                    new_name = f'samacsys-{clean_part.lower()}.step'
                    
                    original_path = os.path.join('3D', original_file)
                    new_path = os.path.join('3D', new_name)
                    
                    try:
                        os.rename(original_path, new_path)
                        print(f"✅ RENAMED: {original_file} -> {new_name}")
                        return new_name
                    except Exception as e:
                        print(f"⚠️ Rename failed: {e}")
                        return original_file
                
                # Check for ZIP files
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP FILE: {zip_files[0]}")
                    try:
                        import zipfile
                        zip_path = os.path.join('3D', zip_files[0])
                        
                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        print(f"✅ Extracted ZIP file: {zip_files[0]}")
                        
                        time.sleep(2)
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files - {zip_files[0]}
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            original_file = step_files[0]
                            clean_part = part_number.replace('/', '-').replace('\\', '-')
                            new_name = f'samacsys-{clean_part.lower()}.step'
                            
                            original_path = os.path.join('3D', original_file)
                            new_path = os.path.join('3D', new_name)
                            
                            try:
                                os.rename(original_path, new_path)
                                print(f"✅ EXTRACTED AND RENAMED: {original_file} -> {new_name}")
                                return new_name
                            except Exception as e:
                                print(f"⚠️ Rename failed: {e}")
                                return original_file
                    except Exception as e:
                        print(f"❌ Error extracting ZIP: {e}")
            
            print(f"  Checking... ({(i+1)*5}/150 seconds)")
        
        print("⏳ No files downloaded")
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        driver.quit()

def main():
    """Main function to handle command line arguments"""
    if len(sys.argv) != 2:
        print("Usage: python samacsys.py \"PART_NUMBER\"")
        print("Example: python samacsys.py \"LM358N\"")
        sys.exit(1)
    
    part_number = sys.argv[1]
    result = samacsys_automation(part_number)
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded to 3D/ folder!")
        sys.exit(0)
    else:
        print(f"\n❌ FAILED: Could not download STEP file for {part_number}")
        sys.exit(1)

if __name__ == "__main__":
    main()
