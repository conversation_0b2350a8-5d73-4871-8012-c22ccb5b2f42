#!/usr/bin/env python3
"""
SIMPLE TEST
===========
Just test if we can open UltraLibrarian with immediate output.
"""

import sys
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

def simple_test():
    print("🔧 SIMPLE ULTRALIBRARIAN TEST", flush=True)
    print("=" * 30, flush=True)
    
    try:
        print("1. Setting up Chrome...", flush=True)
        
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("   ✅ Chrome driver created", flush=True)
        
        print("2. Loading UltraLibrarian...", flush=True)
        driver.get('https://www.ultralibrarian.com')
        time.sleep(5)
        
        title = driver.title
        url = driver.current_url
        
        print(f"   ✅ Page loaded", flush=True)
        print(f"   Title: {title}", flush=True)
        print(f"   URL: {url}", flush=True)
        
        print("3. Test complete - keeping browser open...", flush=True)
        print("   Press Enter to close browser...", flush=True)
        
        input()
        
        driver.quit()
        print("   ✅ Browser closed", flush=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}", flush=True)
        return False

if __name__ == "__main__":
    success = simple_test()
    if success:
        print("✅ SIMPLE TEST PASSED", flush=True)
    else:
        print("❌ SIMPLE TEST FAILED", flush=True)
