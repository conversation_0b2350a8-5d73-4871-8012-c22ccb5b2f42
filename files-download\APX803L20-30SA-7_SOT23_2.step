ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */
/* OPTION: strings as raw bytes, not using required /X/ escapes */

FILE_DESCRIPTION(
/* description */ ('Unknown'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'SOT-323',
/* time_stamp */ '2020-09-02T09:29:11+08:00',
/* author */ ('Unknown'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ST-DEVELOPER v16.7',
/* originating_system */ 'DEX',
/* authorisation */ $);

FILE_SCHEMA (('AUTOMOTIVE_DESIGN {1 0 10303 214 3 1 1}'));
ENDSEC;

DATA;
#10=PROPERTY_DEFINITION_REPRESENTATION(#14,#12);
#11=PROPERTY_DEFINITION_REPRESENTATION(#15,#13);
#12=REPRESENTATION('',(#16),#4602);
#13=REPRESENTATION('',(#17),#4602);
#14=PROPERTY_DEFINITION('pmi validation property','',#4611);
#15=PROPERTY_DEFINITION('pmi validation property','',#4611);
#16=VALUE_REPRESENTATION_ITEM('number of annotations',COUNT_MEASURE(0.));
#17=VALUE_REPRESENTATION_ITEM('number of views',COUNT_MEASURE(0.));
#18=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#22,#4613);
#19=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#23,#4615);
#20=NEXT_ASSEMBLY_USAGE_OCCURRENCE('Compound-New','Compound-New',
'Compound-New',#4616,#4617,'');
#21=NEXT_ASSEMBLY_USAGE_OCCURRENCE('LDF-New','LDF-New','LDF-New',#4616,
#4618,'');
#22=(
REPRESENTATION_RELATIONSHIP(' ',' ',#2756,#2757)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#24)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#23=(
REPRESENTATION_RELATIONSHIP(' ',' ',#2758,#2757)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#25)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#24=ITEM_DEFINED_TRANSFORMATION(' ',' ',#2759,#2895);
#25=ITEM_DEFINED_TRANSFORMATION(' ',' ',#2759,#3014);
#26=SHAPE_REPRESENTATION_RELATIONSHIP('','',#2756,#28);
#27=SHAPE_REPRESENTATION_RELATIONSHIP('','',#2758,#29);
#28=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#2749),#4603);
#29=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#2750,#2751,#2752),#4604);
#30=SPHERICAL_SURFACE('',#2849,0.0799999999999999);
#31=SPHERICAL_SURFACE('',#2859,0.0799999999999999);
#32=SPHERICAL_SURFACE('',#2877,0.0799999999999999);
#33=SPHERICAL_SURFACE('',#2878,0.0799999999999999);
#34=SPHERICAL_SURFACE('',#2884,0.0799999999999999);
#35=SPHERICAL_SURFACE('',#2885,0.0799999999999999);
#36=SPHERICAL_SURFACE('',#2891,0.0799999999999999);
#37=SPHERICAL_SURFACE('',#2892,0.0799999999999999);
#38=ELLIPSE('',#2847,0.0811971269163528,0.0799999999999999);
#39=ELLIPSE('',#2862,0.0811971269163528,0.0799999999999999);
#40=ELLIPSE('',#2881,0.0811971269163528,0.0799999999999999);
#41=ELLIPSE('',#2888,0.0811971269163528,0.0799999999999999);
#42=CYLINDRICAL_SURFACE('',#2766,0.0499999999999945);
#43=CYLINDRICAL_SURFACE('',#2770,0.0645672323584652);
#44=CYLINDRICAL_SURFACE('',#2775,0.0799999999999135);
#45=CYLINDRICAL_SURFACE('',#2779,0.0500000000003276);
#46=CYLINDRICAL_SURFACE('',#2782,0.0499999999999904);
#47=CYLINDRICAL_SURFACE('',#2786,0.0499999999999945);
#48=CYLINDRICAL_SURFACE('',#2790,0.0499999999999945);
#49=CYLINDRICAL_SURFACE('',#2793,0.0500000000003648);
#50=CYLINDRICAL_SURFACE('',#2797,0.079999999999969);
#51=CYLINDRICAL_SURFACE('',#2802,0.064567232358839);
#52=CYLINDRICAL_SURFACE('',#2806,0.0500000000000338);
#53=CYLINDRICAL_SURFACE('',#2813,0.0499999999999945);
#54=CYLINDRICAL_SURFACE('',#2817,0.079999999999969);
#55=CYLINDRICAL_SURFACE('',#2827,0.079999999999969);
#56=CYLINDRICAL_SURFACE('',#2843,0.08);
#57=CYLINDRICAL_SURFACE('',#2846,0.0799999999999999);
#58=CYLINDRICAL_SURFACE('',#2851,0.0799999999999999);
#59=CYLINDRICAL_SURFACE('',#2853,0.0799999999999999);
#60=CYLINDRICAL_SURFACE('',#2856,0.0799999999999999);
#61=CYLINDRICAL_SURFACE('',#2861,0.0799999999999999);
#62=CYLINDRICAL_SURFACE('',#2863,0.0799999999999999);
#63=CYLINDRICAL_SURFACE('',#2866,0.0799999999999999);
#64=CYLINDRICAL_SURFACE('',#2869,0.0799999999999999);
#65=CYLINDRICAL_SURFACE('',#2872,0.0799999999999999);
#66=CYLINDRICAL_SURFACE('',#2875,0.0799999999999999);
#67=CYLINDRICAL_SURFACE('',#2880,0.0799999999999999);
#68=CYLINDRICAL_SURFACE('',#2882,0.0799999999999999);
#69=CYLINDRICAL_SURFACE('',#2887,0.0799999999999999);
#70=CYLINDRICAL_SURFACE('',#2889,0.0799999999999999);
#71=CYLINDRICAL_SURFACE('',#2894,0.0799999999999999);
#72=CYLINDRICAL_SURFACE('',#2908,0.0499999999999945);
#73=CYLINDRICAL_SURFACE('',#2912,0.14999999999995);
#74=CYLINDRICAL_SURFACE('',#2918,0.0399999999999714);
#75=CYLINDRICAL_SURFACE('',#2922,0.160000000000049);
#76=CYLINDRICAL_SURFACE('',#2928,0.0500000000000338);
#77=CYLINDRICAL_SURFACE('',#2930,0.064567232358839);
#78=CYLINDRICAL_SURFACE('',#2933,0.079999999999969);
#79=CYLINDRICAL_SURFACE('',#2935,0.0500000000003648);
#80=CYLINDRICAL_SURFACE('',#2936,0.0499999999999945);
#81=CYLINDRICAL_SURFACE('',#2944,0.0499999999999945);
#82=CYLINDRICAL_SURFACE('',#2948,0.14999999999995);
#83=CYLINDRICAL_SURFACE('',#2954,0.0399999999999714);
#84=CYLINDRICAL_SURFACE('',#2964,0.160000000000049);
#85=CYLINDRICAL_SURFACE('',#2967,0.0499999999999904);
#86=CYLINDRICAL_SURFACE('',#2968,0.0500000000003276);
#87=CYLINDRICAL_SURFACE('',#2970,0.0799999999999135);
#88=CYLINDRICAL_SURFACE('',#2973,0.0645672323584652);
#89=CYLINDRICAL_SURFACE('',#2975,0.0499999999999945);
#90=CYLINDRICAL_SURFACE('',#2986,0.0500000000000143);
#91=CYLINDRICAL_SURFACE('',#2990,0.150000000000014);
#92=CYLINDRICAL_SURFACE('',#2996,0.0399999999999835);
#93=CYLINDRICAL_SURFACE('',#3000,0.159999999999989);
#94=CYLINDRICAL_SURFACE('',#3004,0.079999999999969);
#95=CYLINDRICAL_SURFACE('',#3012,0.079999999999969);
#96=CIRCLE('',#2761,0.0499999999999945);
#97=CIRCLE('',#2763,0.0499999999999945);
#98=CIRCLE('',#2767,0.0499999999999945);
#99=CIRCLE('',#2768,0.0499999999999945);
#100=CIRCLE('',#2771,0.0645672323584652);
#101=CIRCLE('',#2772,0.0645672323584652);
#102=CIRCLE('',#2776,0.0799999999999135);
#103=CIRCLE('',#2777,0.0799999999999135);
#104=CIRCLE('',#2780,0.0500000000003276);
#105=CIRCLE('',#2781,0.0500000000003276);
#106=CIRCLE('',#2783,0.0499999999999904);
#107=CIRCLE('',#2784,0.0499999999999904);
#108=CIRCLE('',#2789,0.0499999999999945);
#109=CIRCLE('',#2791,0.0499999999999945);
#110=CIRCLE('',#2792,0.0499999999999945);
#111=CIRCLE('',#2794,0.0500000000003648);
#112=CIRCLE('',#2795,0.0500000000003648);
#113=CIRCLE('',#2798,0.079999999999969);
#114=CIRCLE('',#2799,0.079999999999969);
#115=CIRCLE('',#2803,0.064567232358839);
#116=CIRCLE('',#2804,0.064567232358839);
#117=CIRCLE('',#2807,0.0500000000000338);
#118=CIRCLE('',#2808,0.0500000000000338);
#119=CIRCLE('',#2812,0.0499999999999945);
#120=CIRCLE('',#2818,0.079999999999969);
#121=CIRCLE('',#2819,0.079999999999969);
#122=CIRCLE('',#2828,0.079999999999969);
#123=CIRCLE('',#2829,0.079999999999969);
#124=CIRCLE('',#2844,0.08);
#125=CIRCLE('',#2845,0.08);
#126=CIRCLE('',#2848,0.0799999999999999);
#127=CIRCLE('',#2850,0.0799999999999999);
#128=CIRCLE('',#2852,0.0799999999999999);
#129=CIRCLE('',#2854,0.0799999999999999);
#130=CIRCLE('',#2855,0.0799999999999999);
#131=CIRCLE('',#2857,0.0799999999999999);
#132=CIRCLE('',#2858,0.0799999999999999);
#133=CIRCLE('',#2860,0.0799999999999999);
#134=CIRCLE('',#2864,0.0799999999999999);
#135=CIRCLE('',#2865,0.0799999999999999);
#136=CIRCLE('',#2867,0.0799999999999999);
#137=CIRCLE('',#2868,0.0799999999999999);
#138=CIRCLE('',#2870,0.0799999999999999);
#139=CIRCLE('',#2871,0.0799999999999999);
#140=CIRCLE('',#2873,0.0799999999999999);
#141=CIRCLE('',#2874,0.0799999999999999);
#142=CIRCLE('',#2876,0.0799999999999999);
#143=CIRCLE('',#2879,0.0799999999999999);
#144=CIRCLE('',#2883,0.0799999999999999);
#145=CIRCLE('',#2886,0.0799999999999999);
#146=CIRCLE('',#2890,0.0799999999999999);
#147=CIRCLE('',#2893,0.0799999999999999);
#148=CIRCLE('',#2897,0.0500000000000338);
#149=CIRCLE('',#2898,0.064567232358839);
#150=CIRCLE('',#2899,0.079999999999969);
#151=CIRCLE('',#2900,0.0500000000003648);
#152=CIRCLE('',#2901,0.0499999999999945);
#153=CIRCLE('',#2903,0.0499999999999945);
#154=CIRCLE('',#2904,0.0500000000003648);
#155=CIRCLE('',#2905,0.079999999999969);
#156=CIRCLE('',#2906,0.064567232358839);
#157=CIRCLE('',#2907,0.0500000000000338);
#158=CIRCLE('',#2909,0.0499999999999945);
#159=CIRCLE('',#2910,0.0499999999999945);
#160=CIRCLE('',#2913,0.14999999999995);
#161=CIRCLE('',#2914,0.14999999999995);
#162=CIRCLE('',#2919,0.0399999999999714);
#163=CIRCLE('',#2920,0.0399999999999714);
#164=CIRCLE('',#2923,0.160000000000049);
#165=CIRCLE('',#2924,0.160000000000049);
#166=CIRCLE('',#2939,0.0499999999999945);
#167=CIRCLE('',#2940,0.0645672323584652);
#168=CIRCLE('',#2941,0.0799999999999135);
#169=CIRCLE('',#2942,0.0500000000003276);
#170=CIRCLE('',#2943,0.0499999999999904);
#171=CIRCLE('',#2945,0.0499999999999945);
#172=CIRCLE('',#2946,0.0499999999999945);
#173=CIRCLE('',#2949,0.14999999999995);
#174=CIRCLE('',#2950,0.14999999999995);
#175=CIRCLE('',#2955,0.0399999999999714);
#176=CIRCLE('',#2956,0.0399999999999714);
#177=CIRCLE('',#2959,0.0499999999999904);
#178=CIRCLE('',#2960,0.0500000000003276);
#179=CIRCLE('',#2961,0.0799999999999135);
#180=CIRCLE('',#2962,0.0645672323584652);
#181=CIRCLE('',#2963,0.0499999999999945);
#182=CIRCLE('',#2965,0.160000000000049);
#183=CIRCLE('',#2966,0.160000000000049);
#184=CIRCLE('',#2981,0.079999999999969);
#185=CIRCLE('',#2982,0.079999999999969);
#186=CIRCLE('',#2984,0.079999999999969);
#187=CIRCLE('',#2985,0.079999999999969);
#188=CIRCLE('',#2987,0.0500000000000143);
#189=CIRCLE('',#2988,0.0500000000000143);
#190=CIRCLE('',#2991,0.150000000000014);
#191=CIRCLE('',#2992,0.150000000000014);
#192=CIRCLE('',#2997,0.0399999999999835);
#193=CIRCLE('',#2998,0.0399999999999835);
#194=CIRCLE('',#3001,0.159999999999989);
#195=CIRCLE('',#3002,0.159999999999989);
#196=ORIENTED_EDGE('',*,*,#974,.F.);
#197=ORIENTED_EDGE('',*,*,#975,.F.);
#198=ORIENTED_EDGE('',*,*,#976,.F.);
#199=ORIENTED_EDGE('',*,*,#977,.F.);
#200=ORIENTED_EDGE('',*,*,#978,.F.);
#201=ORIENTED_EDGE('',*,*,#979,.F.);
#202=ORIENTED_EDGE('',*,*,#980,.F.);
#203=ORIENTED_EDGE('',*,*,#981,.F.);
#204=ORIENTED_EDGE('',*,*,#982,.T.);
#205=ORIENTED_EDGE('',*,*,#983,.F.);
#206=ORIENTED_EDGE('',*,*,#984,.F.);
#207=ORIENTED_EDGE('',*,*,#985,.F.);
#208=ORIENTED_EDGE('',*,*,#986,.F.);
#209=ORIENTED_EDGE('',*,*,#987,.T.);
#210=ORIENTED_EDGE('',*,*,#988,.F.);
#211=ORIENTED_EDGE('',*,*,#982,.F.);
#212=ORIENTED_EDGE('',*,*,#989,.F.);
#213=ORIENTED_EDGE('',*,*,#990,.T.);
#214=ORIENTED_EDGE('',*,*,#991,.F.);
#215=ORIENTED_EDGE('',*,*,#987,.F.);
#216=ORIENTED_EDGE('',*,*,#992,.F.);
#217=ORIENTED_EDGE('',*,*,#993,.T.);
#218=ORIENTED_EDGE('',*,*,#994,.F.);
#219=ORIENTED_EDGE('',*,*,#990,.F.);
#220=ORIENTED_EDGE('',*,*,#995,.F.);
#221=ORIENTED_EDGE('',*,*,#996,.T.);
#222=ORIENTED_EDGE('',*,*,#997,.F.);
#223=ORIENTED_EDGE('',*,*,#993,.F.);
#224=ORIENTED_EDGE('',*,*,#998,.F.);
#225=ORIENTED_EDGE('',*,*,#999,.T.);
#226=ORIENTED_EDGE('',*,*,#1000,.F.);
#227=ORIENTED_EDGE('',*,*,#996,.F.);
#228=ORIENTED_EDGE('',*,*,#1001,.F.);
#229=ORIENTED_EDGE('',*,*,#1002,.T.);
#230=ORIENTED_EDGE('',*,*,#1003,.F.);
#231=ORIENTED_EDGE('',*,*,#999,.F.);
#232=ORIENTED_EDGE('',*,*,#1004,.F.);
#233=ORIENTED_EDGE('',*,*,#1005,.T.);
#234=ORIENTED_EDGE('',*,*,#1006,.F.);
#235=ORIENTED_EDGE('',*,*,#1002,.F.);
#236=ORIENTED_EDGE('',*,*,#1007,.F.);
#237=ORIENTED_EDGE('',*,*,#1008,.T.);
#238=ORIENTED_EDGE('',*,*,#1009,.F.);
#239=ORIENTED_EDGE('',*,*,#1005,.F.);
#240=ORIENTED_EDGE('',*,*,#1010,.F.);
#241=ORIENTED_EDGE('',*,*,#1011,.T.);
#242=ORIENTED_EDGE('',*,*,#1012,.F.);
#243=ORIENTED_EDGE('',*,*,#1008,.F.);
#244=ORIENTED_EDGE('',*,*,#1013,.F.);
#245=ORIENTED_EDGE('',*,*,#1014,.T.);
#246=ORIENTED_EDGE('',*,*,#1015,.F.);
#247=ORIENTED_EDGE('',*,*,#1011,.F.);
#248=ORIENTED_EDGE('',*,*,#1016,.F.);
#249=ORIENTED_EDGE('',*,*,#976,.T.);
#250=ORIENTED_EDGE('',*,*,#1017,.F.);
#251=ORIENTED_EDGE('',*,*,#1014,.F.);
#252=ORIENTED_EDGE('',*,*,#1018,.T.);
#253=ORIENTED_EDGE('',*,*,#983,.T.);
#254=ORIENTED_EDGE('',*,*,#988,.T.);
#255=ORIENTED_EDGE('',*,*,#991,.T.);
#256=ORIENTED_EDGE('',*,*,#994,.T.);
#257=ORIENTED_EDGE('',*,*,#997,.T.);
#258=ORIENTED_EDGE('',*,*,#1000,.T.);
#259=ORIENTED_EDGE('',*,*,#1003,.T.);
#260=ORIENTED_EDGE('',*,*,#1006,.T.);
#261=ORIENTED_EDGE('',*,*,#1009,.T.);
#262=ORIENTED_EDGE('',*,*,#1012,.T.);
#263=ORIENTED_EDGE('',*,*,#1015,.T.);
#264=ORIENTED_EDGE('',*,*,#1017,.T.);
#265=ORIENTED_EDGE('',*,*,#975,.T.);
#266=ORIENTED_EDGE('',*,*,#1019,.T.);
#267=ORIENTED_EDGE('',*,*,#978,.T.);
#268=ORIENTED_EDGE('',*,*,#1020,.F.);
#269=ORIENTED_EDGE('',*,*,#980,.T.);
#270=ORIENTED_EDGE('',*,*,#977,.T.);
#271=ORIENTED_EDGE('',*,*,#1016,.T.);
#272=ORIENTED_EDGE('',*,*,#1013,.T.);
#273=ORIENTED_EDGE('',*,*,#1010,.T.);
#274=ORIENTED_EDGE('',*,*,#1007,.T.);
#275=ORIENTED_EDGE('',*,*,#1004,.T.);
#276=ORIENTED_EDGE('',*,*,#1001,.T.);
#277=ORIENTED_EDGE('',*,*,#998,.T.);
#278=ORIENTED_EDGE('',*,*,#995,.T.);
#279=ORIENTED_EDGE('',*,*,#992,.T.);
#280=ORIENTED_EDGE('',*,*,#989,.T.);
#281=ORIENTED_EDGE('',*,*,#986,.T.);
#282=ORIENTED_EDGE('',*,*,#981,.T.);
#283=ORIENTED_EDGE('',*,*,#1020,.T.);
#284=ORIENTED_EDGE('',*,*,#1021,.F.);
#285=ORIENTED_EDGE('',*,*,#1022,.F.);
#286=ORIENTED_EDGE('',*,*,#1023,.T.);
#287=ORIENTED_EDGE('',*,*,#1024,.F.);
#288=ORIENTED_EDGE('',*,*,#1025,.F.);
#289=ORIENTED_EDGE('',*,*,#1026,.F.);
#290=ORIENTED_EDGE('',*,*,#1027,.F.);
#291=ORIENTED_EDGE('',*,*,#1028,.T.);
#292=ORIENTED_EDGE('',*,*,#1029,.F.);
#293=ORIENTED_EDGE('',*,*,#1023,.F.);
#294=ORIENTED_EDGE('',*,*,#1030,.F.);
#295=ORIENTED_EDGE('',*,*,#1031,.T.);
#296=ORIENTED_EDGE('',*,*,#1032,.F.);
#297=ORIENTED_EDGE('',*,*,#1028,.F.);
#298=ORIENTED_EDGE('',*,*,#1033,.F.);
#299=ORIENTED_EDGE('',*,*,#1034,.T.);
#300=ORIENTED_EDGE('',*,*,#1035,.F.);
#301=ORIENTED_EDGE('',*,*,#1031,.F.);
#302=ORIENTED_EDGE('',*,*,#1036,.F.);
#303=ORIENTED_EDGE('',*,*,#1037,.T.);
#304=ORIENTED_EDGE('',*,*,#1038,.F.);
#305=ORIENTED_EDGE('',*,*,#1034,.F.);
#306=ORIENTED_EDGE('',*,*,#1039,.F.);
#307=ORIENTED_EDGE('',*,*,#1040,.T.);
#308=ORIENTED_EDGE('',*,*,#1041,.F.);
#309=ORIENTED_EDGE('',*,*,#1037,.F.);
#310=ORIENTED_EDGE('',*,*,#1042,.F.);
#311=ORIENTED_EDGE('',*,*,#1043,.T.);
#312=ORIENTED_EDGE('',*,*,#1044,.F.);
#313=ORIENTED_EDGE('',*,*,#1040,.F.);
#314=ORIENTED_EDGE('',*,*,#1045,.F.);
#315=ORIENTED_EDGE('',*,*,#1046,.T.);
#316=ORIENTED_EDGE('',*,*,#1047,.F.);
#317=ORIENTED_EDGE('',*,*,#1043,.F.);
#318=ORIENTED_EDGE('',*,*,#1048,.F.);
#319=ORIENTED_EDGE('',*,*,#1049,.T.);
#320=ORIENTED_EDGE('',*,*,#1050,.F.);
#321=ORIENTED_EDGE('',*,*,#1046,.F.);
#322=ORIENTED_EDGE('',*,*,#1051,.F.);
#323=ORIENTED_EDGE('',*,*,#1052,.T.);
#324=ORIENTED_EDGE('',*,*,#1053,.F.);
#325=ORIENTED_EDGE('',*,*,#1049,.F.);
#326=ORIENTED_EDGE('',*,*,#1054,.F.);
#327=ORIENTED_EDGE('',*,*,#1055,.T.);
#328=ORIENTED_EDGE('',*,*,#1056,.F.);
#329=ORIENTED_EDGE('',*,*,#1052,.F.);
#330=ORIENTED_EDGE('',*,*,#1057,.F.);
#331=ORIENTED_EDGE('',*,*,#1058,.T.);
#332=ORIENTED_EDGE('',*,*,#1059,.F.);
#333=ORIENTED_EDGE('',*,*,#1055,.F.);
#334=ORIENTED_EDGE('',*,*,#1060,.F.);
#335=ORIENTED_EDGE('',*,*,#1061,.F.);
#336=ORIENTED_EDGE('',*,*,#1058,.F.);
#337=ORIENTED_EDGE('',*,*,#1062,.F.);
#338=ORIENTED_EDGE('',*,*,#1063,.F.);
#339=ORIENTED_EDGE('',*,*,#1064,.F.);
#340=ORIENTED_EDGE('',*,*,#1065,.T.);
#341=ORIENTED_EDGE('',*,*,#1063,.T.);
#342=ORIENTED_EDGE('',*,*,#1066,.F.);
#343=ORIENTED_EDGE('',*,*,#1021,.T.);
#344=ORIENTED_EDGE('',*,*,#1022,.T.);
#345=ORIENTED_EDGE('',*,*,#1066,.T.);
#346=ORIENTED_EDGE('',*,*,#1062,.T.);
#347=ORIENTED_EDGE('',*,*,#1057,.T.);
#348=ORIENTED_EDGE('',*,*,#1054,.T.);
#349=ORIENTED_EDGE('',*,*,#1051,.T.);
#350=ORIENTED_EDGE('',*,*,#1048,.T.);
#351=ORIENTED_EDGE('',*,*,#1045,.T.);
#352=ORIENTED_EDGE('',*,*,#1042,.T.);
#353=ORIENTED_EDGE('',*,*,#1039,.T.);
#354=ORIENTED_EDGE('',*,*,#1036,.T.);
#355=ORIENTED_EDGE('',*,*,#1033,.T.);
#356=ORIENTED_EDGE('',*,*,#1030,.T.);
#357=ORIENTED_EDGE('',*,*,#1027,.T.);
#358=ORIENTED_EDGE('',*,*,#1067,.T.);
#359=ORIENTED_EDGE('',*,*,#1024,.T.);
#360=ORIENTED_EDGE('',*,*,#1029,.T.);
#361=ORIENTED_EDGE('',*,*,#1032,.T.);
#362=ORIENTED_EDGE('',*,*,#1035,.T.);
#363=ORIENTED_EDGE('',*,*,#1038,.T.);
#364=ORIENTED_EDGE('',*,*,#1041,.T.);
#365=ORIENTED_EDGE('',*,*,#1044,.T.);
#366=ORIENTED_EDGE('',*,*,#1047,.T.);
#367=ORIENTED_EDGE('',*,*,#1050,.T.);
#368=ORIENTED_EDGE('',*,*,#1053,.T.);
#369=ORIENTED_EDGE('',*,*,#1056,.T.);
#370=ORIENTED_EDGE('',*,*,#1059,.T.);
#371=ORIENTED_EDGE('',*,*,#1061,.T.);
#372=ORIENTED_EDGE('',*,*,#1068,.F.);
#373=ORIENTED_EDGE('',*,*,#1069,.F.);
#374=ORIENTED_EDGE('',*,*,#1070,.F.);
#375=ORIENTED_EDGE('',*,*,#1071,.F.);
#376=ORIENTED_EDGE('',*,*,#1072,.F.);
#377=ORIENTED_EDGE('',*,*,#1069,.T.);
#378=ORIENTED_EDGE('',*,*,#1073,.F.);
#379=ORIENTED_EDGE('',*,*,#1074,.F.);
#380=ORIENTED_EDGE('',*,*,#1075,.F.);
#381=ORIENTED_EDGE('',*,*,#1074,.T.);
#382=ORIENTED_EDGE('',*,*,#1076,.F.);
#383=ORIENTED_EDGE('',*,*,#1077,.F.);
#384=ORIENTED_EDGE('',*,*,#1078,.F.);
#385=ORIENTED_EDGE('',*,*,#1077,.T.);
#386=ORIENTED_EDGE('',*,*,#1079,.F.);
#387=ORIENTED_EDGE('',*,*,#1080,.F.);
#388=ORIENTED_EDGE('',*,*,#1081,.F.);
#389=ORIENTED_EDGE('',*,*,#1080,.T.);
#390=ORIENTED_EDGE('',*,*,#1082,.F.);
#391=ORIENTED_EDGE('',*,*,#1083,.F.);
#392=ORIENTED_EDGE('',*,*,#1084,.F.);
#393=ORIENTED_EDGE('',*,*,#1083,.T.);
#394=ORIENTED_EDGE('',*,*,#1085,.F.);
#395=ORIENTED_EDGE('',*,*,#1086,.F.);
#396=ORIENTED_EDGE('',*,*,#1087,.F.);
#397=ORIENTED_EDGE('',*,*,#1086,.T.);
#398=ORIENTED_EDGE('',*,*,#1088,.F.);
#399=ORIENTED_EDGE('',*,*,#1089,.F.);
#400=ORIENTED_EDGE('',*,*,#1090,.F.);
#401=ORIENTED_EDGE('',*,*,#1089,.T.);
#402=ORIENTED_EDGE('',*,*,#1091,.F.);
#403=ORIENTED_EDGE('',*,*,#1092,.F.);
#404=ORIENTED_EDGE('',*,*,#1093,.F.);
#405=ORIENTED_EDGE('',*,*,#1092,.T.);
#406=ORIENTED_EDGE('',*,*,#1094,.F.);
#407=ORIENTED_EDGE('',*,*,#1095,.F.);
#408=ORIENTED_EDGE('',*,*,#1096,.F.);
#409=ORIENTED_EDGE('',*,*,#1095,.T.);
#410=ORIENTED_EDGE('',*,*,#1097,.F.);
#411=ORIENTED_EDGE('',*,*,#1098,.F.);
#412=ORIENTED_EDGE('',*,*,#1099,.F.);
#413=ORIENTED_EDGE('',*,*,#1098,.T.);
#414=ORIENTED_EDGE('',*,*,#1100,.F.);
#415=ORIENTED_EDGE('',*,*,#1101,.F.);
#416=ORIENTED_EDGE('',*,*,#1102,.T.);
#417=ORIENTED_EDGE('',*,*,#1100,.T.);
#418=ORIENTED_EDGE('',*,*,#1097,.T.);
#419=ORIENTED_EDGE('',*,*,#1094,.T.);
#420=ORIENTED_EDGE('',*,*,#1091,.T.);
#421=ORIENTED_EDGE('',*,*,#1088,.T.);
#422=ORIENTED_EDGE('',*,*,#1085,.T.);
#423=ORIENTED_EDGE('',*,*,#1082,.T.);
#424=ORIENTED_EDGE('',*,*,#1079,.T.);
#425=ORIENTED_EDGE('',*,*,#1076,.T.);
#426=ORIENTED_EDGE('',*,*,#1073,.T.);
#427=ORIENTED_EDGE('',*,*,#1068,.T.);
#428=ORIENTED_EDGE('',*,*,#1103,.F.);
#429=ORIENTED_EDGE('',*,*,#1070,.T.);
#430=ORIENTED_EDGE('',*,*,#1072,.T.);
#431=ORIENTED_EDGE('',*,*,#1075,.T.);
#432=ORIENTED_EDGE('',*,*,#1078,.T.);
#433=ORIENTED_EDGE('',*,*,#1081,.T.);
#434=ORIENTED_EDGE('',*,*,#1084,.T.);
#435=ORIENTED_EDGE('',*,*,#1087,.T.);
#436=ORIENTED_EDGE('',*,*,#1090,.T.);
#437=ORIENTED_EDGE('',*,*,#1093,.T.);
#438=ORIENTED_EDGE('',*,*,#1096,.T.);
#439=ORIENTED_EDGE('',*,*,#1099,.T.);
#440=ORIENTED_EDGE('',*,*,#1019,.F.);
#441=ORIENTED_EDGE('',*,*,#985,.T.);
#442=ORIENTED_EDGE('',*,*,#1104,.T.);
#443=ORIENTED_EDGE('',*,*,#1064,.T.);
#444=ORIENTED_EDGE('',*,*,#1065,.F.);
#445=ORIENTED_EDGE('',*,*,#1026,.T.);
#446=ORIENTED_EDGE('',*,*,#1105,.T.);
#447=ORIENTED_EDGE('',*,*,#1106,.T.);
#448=ORIENTED_EDGE('',*,*,#1107,.T.);
#449=ORIENTED_EDGE('',*,*,#1108,.T.);
#450=ORIENTED_EDGE('',*,*,#1109,.T.);
#451=ORIENTED_EDGE('',*,*,#979,.T.);
#452=ORIENTED_EDGE('',*,*,#1104,.F.);
#453=ORIENTED_EDGE('',*,*,#984,.T.);
#454=ORIENTED_EDGE('',*,*,#1018,.F.);
#455=ORIENTED_EDGE('',*,*,#974,.T.);
#456=ORIENTED_EDGE('',*,*,#1109,.F.);
#457=ORIENTED_EDGE('',*,*,#1110,.T.);
#458=ORIENTED_EDGE('',*,*,#1111,.T.);
#459=ORIENTED_EDGE('',*,*,#1112,.T.);
#460=ORIENTED_EDGE('',*,*,#1105,.F.);
#461=ORIENTED_EDGE('',*,*,#1025,.T.);
#462=ORIENTED_EDGE('',*,*,#1067,.F.);
#463=ORIENTED_EDGE('',*,*,#1060,.T.);
#464=ORIENTED_EDGE('',*,*,#1113,.F.);
#465=ORIENTED_EDGE('',*,*,#1101,.T.);
#466=ORIENTED_EDGE('',*,*,#1102,.F.);
#467=ORIENTED_EDGE('',*,*,#1071,.T.);
#468=ORIENTED_EDGE('',*,*,#1114,.F.);
#469=ORIENTED_EDGE('',*,*,#1115,.T.);
#470=ORIENTED_EDGE('',*,*,#1116,.T.);
#471=ORIENTED_EDGE('',*,*,#1117,.T.);
#472=ORIENTED_EDGE('',*,*,#1118,.T.);
#473=ORIENTED_EDGE('',*,*,#1119,.T.);
#474=ORIENTED_EDGE('',*,*,#1120,.T.);
#475=ORIENTED_EDGE('',*,*,#1121,.T.);
#476=ORIENTED_EDGE('',*,*,#1122,.T.);
#477=ORIENTED_EDGE('',*,*,#1123,.T.);
#478=ORIENTED_EDGE('',*,*,#1124,.T.);
#479=ORIENTED_EDGE('',*,*,#1125,.T.);
#480=ORIENTED_EDGE('',*,*,#1126,.T.);
#481=ORIENTED_EDGE('',*,*,#1127,.T.);
#482=ORIENTED_EDGE('',*,*,#1128,.T.);
#483=ORIENTED_EDGE('',*,*,#1129,.T.);
#484=ORIENTED_EDGE('',*,*,#1130,.T.);
#485=ORIENTED_EDGE('',*,*,#1131,.T.);
#486=ORIENTED_EDGE('',*,*,#1114,.T.);
#487=ORIENTED_EDGE('',*,*,#1103,.T.);
#488=ORIENTED_EDGE('',*,*,#1113,.T.);
#489=ORIENTED_EDGE('',*,*,#1132,.T.);
#490=ORIENTED_EDGE('',*,*,#1133,.T.);
#491=ORIENTED_EDGE('',*,*,#1134,.T.);
#492=ORIENTED_EDGE('',*,*,#1135,.T.);
#493=ORIENTED_EDGE('',*,*,#1136,.T.);
#494=ORIENTED_EDGE('',*,*,#1126,.F.);
#495=ORIENTED_EDGE('',*,*,#1137,.T.);
#496=ORIENTED_EDGE('',*,*,#1138,.T.);
#497=ORIENTED_EDGE('',*,*,#1139,.T.);
#498=ORIENTED_EDGE('',*,*,#1124,.F.);
#499=ORIENTED_EDGE('',*,*,#1140,.T.);
#500=ORIENTED_EDGE('',*,*,#1141,.T.);
#501=ORIENTED_EDGE('',*,*,#1142,.T.);
#502=ORIENTED_EDGE('',*,*,#1120,.F.);
#503=ORIENTED_EDGE('',*,*,#1143,.T.);
#504=ORIENTED_EDGE('',*,*,#1107,.F.);
#505=ORIENTED_EDGE('',*,*,#1144,.T.);
#506=ORIENTED_EDGE('',*,*,#1106,.F.);
#507=ORIENTED_EDGE('',*,*,#1145,.T.);
#508=ORIENTED_EDGE('',*,*,#1129,.F.);
#509=ORIENTED_EDGE('',*,*,#1146,.F.);
#510=ORIENTED_EDGE('',*,*,#1144,.F.);
#511=ORIENTED_EDGE('',*,*,#1146,.T.);
#512=ORIENTED_EDGE('',*,*,#1147,.F.);
#513=ORIENTED_EDGE('',*,*,#1128,.F.);
#514=ORIENTED_EDGE('',*,*,#1148,.T.);
#515=ORIENTED_EDGE('',*,*,#1121,.F.);
#516=ORIENTED_EDGE('',*,*,#1147,.T.);
#517=ORIENTED_EDGE('',*,*,#1118,.F.);
#518=ORIENTED_EDGE('',*,*,#1149,.T.);
#519=ORIENTED_EDGE('',*,*,#1130,.F.);
#520=ORIENTED_EDGE('',*,*,#1150,.T.);
#521=ORIENTED_EDGE('',*,*,#1119,.F.);
#522=ORIENTED_EDGE('',*,*,#1151,.T.);
#523=ORIENTED_EDGE('',*,*,#1122,.F.);
#524=ORIENTED_EDGE('',*,*,#1152,.F.);
#525=ORIENTED_EDGE('',*,*,#1143,.F.);
#526=ORIENTED_EDGE('',*,*,#1152,.T.);
#527=ORIENTED_EDGE('',*,*,#1153,.F.);
#528=ORIENTED_EDGE('',*,*,#1125,.F.);
#529=ORIENTED_EDGE('',*,*,#1154,.T.);
#530=ORIENTED_EDGE('',*,*,#1108,.F.);
#531=ORIENTED_EDGE('',*,*,#1153,.T.);
#532=ORIENTED_EDGE('',*,*,#1141,.F.);
#533=ORIENTED_EDGE('',*,*,#1155,.T.);
#534=ORIENTED_EDGE('',*,*,#1136,.F.);
#535=ORIENTED_EDGE('',*,*,#1156,.T.);
#536=ORIENTED_EDGE('',*,*,#1116,.F.);
#537=ORIENTED_EDGE('',*,*,#1157,.T.);
#538=ORIENTED_EDGE('',*,*,#1133,.F.);
#539=ORIENTED_EDGE('',*,*,#1158,.T.);
#540=ORIENTED_EDGE('',*,*,#1135,.F.);
#541=ORIENTED_EDGE('',*,*,#1159,.T.);
#542=ORIENTED_EDGE('',*,*,#1111,.F.);
#543=ORIENTED_EDGE('',*,*,#1160,.T.);
#544=ORIENTED_EDGE('',*,*,#1138,.F.);
#545=ORIENTED_EDGE('',*,*,#1161,.T.);
#546=ORIENTED_EDGE('',*,*,#1134,.F.);
#547=ORIENTED_EDGE('',*,*,#1162,.T.);
#548=ORIENTED_EDGE('',*,*,#1154,.F.);
#549=ORIENTED_EDGE('',*,*,#1142,.F.);
#550=ORIENTED_EDGE('',*,*,#1163,.F.);
#551=ORIENTED_EDGE('',*,*,#1110,.F.);
#552=ORIENTED_EDGE('',*,*,#1163,.T.);
#553=ORIENTED_EDGE('',*,*,#1156,.F.);
#554=ORIENTED_EDGE('',*,*,#1160,.F.);
#555=ORIENTED_EDGE('',*,*,#1151,.F.);
#556=ORIENTED_EDGE('',*,*,#1150,.F.);
#557=ORIENTED_EDGE('',*,*,#1164,.F.);
#558=ORIENTED_EDGE('',*,*,#1164,.T.);
#559=ORIENTED_EDGE('',*,*,#1132,.F.);
#560=ORIENTED_EDGE('',*,*,#1165,.F.);
#561=ORIENTED_EDGE('',*,*,#1123,.F.);
#562=ORIENTED_EDGE('',*,*,#1165,.T.);
#563=ORIENTED_EDGE('',*,*,#1117,.F.);
#564=ORIENTED_EDGE('',*,*,#1166,.F.);
#565=ORIENTED_EDGE('',*,*,#1140,.F.);
#566=ORIENTED_EDGE('',*,*,#1158,.F.);
#567=ORIENTED_EDGE('',*,*,#1155,.F.);
#568=ORIENTED_EDGE('',*,*,#1166,.T.);
#569=ORIENTED_EDGE('',*,*,#1149,.F.);
#570=ORIENTED_EDGE('',*,*,#1148,.F.);
#571=ORIENTED_EDGE('',*,*,#1167,.F.);
#572=ORIENTED_EDGE('',*,*,#1167,.T.);
#573=ORIENTED_EDGE('',*,*,#1127,.F.);
#574=ORIENTED_EDGE('',*,*,#1168,.F.);
#575=ORIENTED_EDGE('',*,*,#1131,.F.);
#576=ORIENTED_EDGE('',*,*,#1168,.T.);
#577=ORIENTED_EDGE('',*,*,#1139,.F.);
#578=ORIENTED_EDGE('',*,*,#1169,.F.);
#579=ORIENTED_EDGE('',*,*,#1115,.F.);
#580=ORIENTED_EDGE('',*,*,#1162,.F.);
#581=ORIENTED_EDGE('',*,*,#1157,.F.);
#582=ORIENTED_EDGE('',*,*,#1169,.T.);
#583=ORIENTED_EDGE('',*,*,#1159,.F.);
#584=ORIENTED_EDGE('',*,*,#1161,.F.);
#585=ORIENTED_EDGE('',*,*,#1170,.F.);
#586=ORIENTED_EDGE('',*,*,#1145,.F.);
#587=ORIENTED_EDGE('',*,*,#1112,.F.);
#588=ORIENTED_EDGE('',*,*,#1170,.T.);
#589=ORIENTED_EDGE('',*,*,#1137,.F.);
#590=ORIENTED_EDGE('',*,*,#1171,.F.);
#591=ORIENTED_EDGE('',*,*,#1172,.T.);
#592=ORIENTED_EDGE('',*,*,#1173,.F.);
#593=ORIENTED_EDGE('',*,*,#1174,.F.);
#594=ORIENTED_EDGE('',*,*,#1175,.F.);
#595=ORIENTED_EDGE('',*,*,#1176,.F.);
#596=ORIENTED_EDGE('',*,*,#1177,.F.);
#597=ORIENTED_EDGE('',*,*,#1178,.F.);
#598=ORIENTED_EDGE('',*,*,#1179,.F.);
#599=ORIENTED_EDGE('',*,*,#1180,.F.);
#600=ORIENTED_EDGE('',*,*,#1181,.F.);
#601=ORIENTED_EDGE('',*,*,#1182,.F.);
#602=ORIENTED_EDGE('',*,*,#1183,.F.);
#603=ORIENTED_EDGE('',*,*,#1184,.F.);
#604=ORIENTED_EDGE('',*,*,#1185,.F.);
#605=ORIENTED_EDGE('',*,*,#1186,.F.);
#606=ORIENTED_EDGE('',*,*,#1187,.F.);
#607=ORIENTED_EDGE('',*,*,#1188,.F.);
#608=ORIENTED_EDGE('',*,*,#1189,.F.);
#609=ORIENTED_EDGE('',*,*,#1190,.F.);
#610=ORIENTED_EDGE('',*,*,#1191,.F.);
#611=ORIENTED_EDGE('',*,*,#1192,.F.);
#612=ORIENTED_EDGE('',*,*,#1193,.F.);
#613=ORIENTED_EDGE('',*,*,#1194,.F.);
#614=ORIENTED_EDGE('',*,*,#1195,.F.);
#615=ORIENTED_EDGE('',*,*,#1196,.F.);
#616=ORIENTED_EDGE('',*,*,#1197,.F.);
#617=ORIENTED_EDGE('',*,*,#1198,.F.);
#618=ORIENTED_EDGE('',*,*,#1199,.F.);
#619=ORIENTED_EDGE('',*,*,#1198,.T.);
#620=ORIENTED_EDGE('',*,*,#1200,.F.);
#621=ORIENTED_EDGE('',*,*,#1201,.F.);
#622=ORIENTED_EDGE('',*,*,#1202,.F.);
#623=ORIENTED_EDGE('',*,*,#1201,.T.);
#624=ORIENTED_EDGE('',*,*,#1203,.F.);
#625=ORIENTED_EDGE('',*,*,#1204,.F.);
#626=ORIENTED_EDGE('',*,*,#1205,.F.);
#627=ORIENTED_EDGE('',*,*,#1204,.T.);
#628=ORIENTED_EDGE('',*,*,#1206,.F.);
#629=ORIENTED_EDGE('',*,*,#1207,.F.);
#630=ORIENTED_EDGE('',*,*,#1208,.F.);
#631=ORIENTED_EDGE('',*,*,#1207,.T.);
#632=ORIENTED_EDGE('',*,*,#1209,.F.);
#633=ORIENTED_EDGE('',*,*,#1210,.F.);
#634=ORIENTED_EDGE('',*,*,#1211,.F.);
#635=ORIENTED_EDGE('',*,*,#1210,.T.);
#636=ORIENTED_EDGE('',*,*,#1212,.F.);
#637=ORIENTED_EDGE('',*,*,#1213,.F.);
#638=ORIENTED_EDGE('',*,*,#1214,.F.);
#639=ORIENTED_EDGE('',*,*,#1213,.T.);
#640=ORIENTED_EDGE('',*,*,#1215,.F.);
#641=ORIENTED_EDGE('',*,*,#1216,.F.);
#642=ORIENTED_EDGE('',*,*,#1217,.F.);
#643=ORIENTED_EDGE('',*,*,#1216,.T.);
#644=ORIENTED_EDGE('',*,*,#1218,.F.);
#645=ORIENTED_EDGE('',*,*,#1219,.F.);
#646=ORIENTED_EDGE('',*,*,#1220,.F.);
#647=ORIENTED_EDGE('',*,*,#1219,.T.);
#648=ORIENTED_EDGE('',*,*,#1221,.F.);
#649=ORIENTED_EDGE('',*,*,#1222,.F.);
#650=ORIENTED_EDGE('',*,*,#1223,.F.);
#651=ORIENTED_EDGE('',*,*,#1222,.T.);
#652=ORIENTED_EDGE('',*,*,#1224,.F.);
#653=ORIENTED_EDGE('',*,*,#1172,.F.);
#654=ORIENTED_EDGE('',*,*,#1224,.T.);
#655=ORIENTED_EDGE('',*,*,#1221,.T.);
#656=ORIENTED_EDGE('',*,*,#1218,.T.);
#657=ORIENTED_EDGE('',*,*,#1215,.T.);
#658=ORIENTED_EDGE('',*,*,#1212,.T.);
#659=ORIENTED_EDGE('',*,*,#1209,.T.);
#660=ORIENTED_EDGE('',*,*,#1206,.T.);
#661=ORIENTED_EDGE('',*,*,#1203,.T.);
#662=ORIENTED_EDGE('',*,*,#1200,.T.);
#663=ORIENTED_EDGE('',*,*,#1197,.T.);
#664=ORIENTED_EDGE('',*,*,#1225,.T.);
#665=ORIENTED_EDGE('',*,*,#1173,.T.);
#666=ORIENTED_EDGE('',*,*,#1196,.T.);
#667=ORIENTED_EDGE('',*,*,#1226,.T.);
#668=ORIENTED_EDGE('',*,*,#1174,.T.);
#669=ORIENTED_EDGE('',*,*,#1225,.F.);
#670=ORIENTED_EDGE('',*,*,#1195,.T.);
#671=ORIENTED_EDGE('',*,*,#1227,.T.);
#672=ORIENTED_EDGE('',*,*,#1175,.T.);
#673=ORIENTED_EDGE('',*,*,#1226,.F.);
#674=ORIENTED_EDGE('',*,*,#1194,.T.);
#675=ORIENTED_EDGE('',*,*,#1228,.T.);
#676=ORIENTED_EDGE('',*,*,#1176,.T.);
#677=ORIENTED_EDGE('',*,*,#1227,.F.);
#678=ORIENTED_EDGE('',*,*,#1193,.T.);
#679=ORIENTED_EDGE('',*,*,#1229,.T.);
#680=ORIENTED_EDGE('',*,*,#1177,.T.);
#681=ORIENTED_EDGE('',*,*,#1228,.F.);
#682=ORIENTED_EDGE('',*,*,#1192,.T.);
#683=ORIENTED_EDGE('',*,*,#1230,.T.);
#684=ORIENTED_EDGE('',*,*,#1178,.T.);
#685=ORIENTED_EDGE('',*,*,#1229,.F.);
#686=ORIENTED_EDGE('',*,*,#1191,.T.);
#687=ORIENTED_EDGE('',*,*,#1231,.T.);
#688=ORIENTED_EDGE('',*,*,#1179,.T.);
#689=ORIENTED_EDGE('',*,*,#1230,.F.);
#690=ORIENTED_EDGE('',*,*,#1190,.T.);
#691=ORIENTED_EDGE('',*,*,#1232,.T.);
#692=ORIENTED_EDGE('',*,*,#1180,.T.);
#693=ORIENTED_EDGE('',*,*,#1231,.F.);
#694=ORIENTED_EDGE('',*,*,#1189,.T.);
#695=ORIENTED_EDGE('',*,*,#1233,.T.);
#696=ORIENTED_EDGE('',*,*,#1181,.T.);
#697=ORIENTED_EDGE('',*,*,#1232,.F.);
#698=ORIENTED_EDGE('',*,*,#1188,.T.);
#699=ORIENTED_EDGE('',*,*,#1234,.T.);
#700=ORIENTED_EDGE('',*,*,#1182,.T.);
#701=ORIENTED_EDGE('',*,*,#1233,.F.);
#702=ORIENTED_EDGE('',*,*,#1187,.T.);
#703=ORIENTED_EDGE('',*,*,#1235,.T.);
#704=ORIENTED_EDGE('',*,*,#1183,.T.);
#705=ORIENTED_EDGE('',*,*,#1234,.F.);
#706=ORIENTED_EDGE('',*,*,#1186,.T.);
#707=ORIENTED_EDGE('',*,*,#1236,.T.);
#708=ORIENTED_EDGE('',*,*,#1184,.T.);
#709=ORIENTED_EDGE('',*,*,#1235,.F.);
#710=ORIENTED_EDGE('',*,*,#1223,.T.);
#711=ORIENTED_EDGE('',*,*,#1171,.T.);
#712=ORIENTED_EDGE('',*,*,#1236,.F.);
#713=ORIENTED_EDGE('',*,*,#1185,.T.);
#714=ORIENTED_EDGE('',*,*,#1199,.T.);
#715=ORIENTED_EDGE('',*,*,#1202,.T.);
#716=ORIENTED_EDGE('',*,*,#1205,.T.);
#717=ORIENTED_EDGE('',*,*,#1208,.T.);
#718=ORIENTED_EDGE('',*,*,#1211,.T.);
#719=ORIENTED_EDGE('',*,*,#1214,.T.);
#720=ORIENTED_EDGE('',*,*,#1217,.T.);
#721=ORIENTED_EDGE('',*,*,#1220,.T.);
#722=ORIENTED_EDGE('',*,*,#1237,.F.);
#723=ORIENTED_EDGE('',*,*,#1238,.F.);
#724=ORIENTED_EDGE('',*,*,#1239,.F.);
#725=ORIENTED_EDGE('',*,*,#1240,.F.);
#726=ORIENTED_EDGE('',*,*,#1241,.F.);
#727=ORIENTED_EDGE('',*,*,#1242,.F.);
#728=ORIENTED_EDGE('',*,*,#1243,.F.);
#729=ORIENTED_EDGE('',*,*,#1244,.F.);
#730=ORIENTED_EDGE('',*,*,#1245,.F.);
#731=ORIENTED_EDGE('',*,*,#1246,.F.);
#732=ORIENTED_EDGE('',*,*,#1247,.F.);
#733=ORIENTED_EDGE('',*,*,#1248,.F.);
#734=ORIENTED_EDGE('',*,*,#1249,.F.);
#735=ORIENTED_EDGE('',*,*,#1250,.F.);
#736=ORIENTED_EDGE('',*,*,#1251,.F.);
#737=ORIENTED_EDGE('',*,*,#1252,.F.);
#738=ORIENTED_EDGE('',*,*,#1253,.F.);
#739=ORIENTED_EDGE('',*,*,#1238,.T.);
#740=ORIENTED_EDGE('',*,*,#1254,.F.);
#741=ORIENTED_EDGE('',*,*,#1255,.F.);
#742=ORIENTED_EDGE('',*,*,#1256,.F.);
#743=ORIENTED_EDGE('',*,*,#1252,.T.);
#744=ORIENTED_EDGE('',*,*,#1257,.F.);
#745=ORIENTED_EDGE('',*,*,#1258,.F.);
#746=ORIENTED_EDGE('',*,*,#1259,.F.);
#747=ORIENTED_EDGE('',*,*,#1255,.T.);
#748=ORIENTED_EDGE('',*,*,#1260,.F.);
#749=ORIENTED_EDGE('',*,*,#1261,.F.);
#750=ORIENTED_EDGE('',*,*,#1262,.F.);
#751=ORIENTED_EDGE('',*,*,#1258,.T.);
#752=ORIENTED_EDGE('',*,*,#1263,.F.);
#753=ORIENTED_EDGE('',*,*,#1264,.F.);
#754=ORIENTED_EDGE('',*,*,#1265,.F.);
#755=ORIENTED_EDGE('',*,*,#1261,.T.);
#756=ORIENTED_EDGE('',*,*,#1266,.F.);
#757=ORIENTED_EDGE('',*,*,#1267,.F.);
#758=ORIENTED_EDGE('',*,*,#1268,.F.);
#759=ORIENTED_EDGE('',*,*,#1264,.T.);
#760=ORIENTED_EDGE('',*,*,#1269,.F.);
#761=ORIENTED_EDGE('',*,*,#1270,.F.);
#762=ORIENTED_EDGE('',*,*,#1271,.F.);
#763=ORIENTED_EDGE('',*,*,#1267,.T.);
#764=ORIENTED_EDGE('',*,*,#1272,.F.);
#765=ORIENTED_EDGE('',*,*,#1273,.F.);
#766=ORIENTED_EDGE('',*,*,#1274,.F.);
#767=ORIENTED_EDGE('',*,*,#1270,.T.);
#768=ORIENTED_EDGE('',*,*,#1275,.F.);
#769=ORIENTED_EDGE('',*,*,#1276,.F.);
#770=ORIENTED_EDGE('',*,*,#1277,.F.);
#771=ORIENTED_EDGE('',*,*,#1278,.F.);
#772=ORIENTED_EDGE('',*,*,#1279,.F.);
#773=ORIENTED_EDGE('',*,*,#1280,.F.);
#774=ORIENTED_EDGE('',*,*,#1281,.F.);
#775=ORIENTED_EDGE('',*,*,#1282,.F.);
#776=ORIENTED_EDGE('',*,*,#1283,.F.);
#777=ORIENTED_EDGE('',*,*,#1284,.F.);
#778=ORIENTED_EDGE('',*,*,#1285,.F.);
#779=ORIENTED_EDGE('',*,*,#1286,.F.);
#780=ORIENTED_EDGE('',*,*,#1287,.F.);
#781=ORIENTED_EDGE('',*,*,#1288,.T.);
#782=ORIENTED_EDGE('',*,*,#1289,.F.);
#783=ORIENTED_EDGE('',*,*,#1288,.F.);
#784=ORIENTED_EDGE('',*,*,#1290,.F.);
#785=ORIENTED_EDGE('',*,*,#1273,.T.);
#786=ORIENTED_EDGE('',*,*,#1250,.T.);
#787=ORIENTED_EDGE('',*,*,#1291,.T.);
#788=ORIENTED_EDGE('',*,*,#1276,.T.);
#789=ORIENTED_EDGE('',*,*,#1292,.F.);
#790=ORIENTED_EDGE('',*,*,#1249,.T.);
#791=ORIENTED_EDGE('',*,*,#1293,.T.);
#792=ORIENTED_EDGE('',*,*,#1277,.T.);
#793=ORIENTED_EDGE('',*,*,#1291,.F.);
#794=ORIENTED_EDGE('',*,*,#1248,.T.);
#795=ORIENTED_EDGE('',*,*,#1294,.T.);
#796=ORIENTED_EDGE('',*,*,#1278,.T.);
#797=ORIENTED_EDGE('',*,*,#1293,.F.);
#798=ORIENTED_EDGE('',*,*,#1247,.T.);
#799=ORIENTED_EDGE('',*,*,#1295,.T.);
#800=ORIENTED_EDGE('',*,*,#1279,.T.);
#801=ORIENTED_EDGE('',*,*,#1294,.F.);
#802=ORIENTED_EDGE('',*,*,#1246,.T.);
#803=ORIENTED_EDGE('',*,*,#1296,.T.);
#804=ORIENTED_EDGE('',*,*,#1280,.T.);
#805=ORIENTED_EDGE('',*,*,#1295,.F.);
#806=ORIENTED_EDGE('',*,*,#1245,.T.);
#807=ORIENTED_EDGE('',*,*,#1297,.T.);
#808=ORIENTED_EDGE('',*,*,#1281,.T.);
#809=ORIENTED_EDGE('',*,*,#1296,.F.);
#810=ORIENTED_EDGE('',*,*,#1244,.T.);
#811=ORIENTED_EDGE('',*,*,#1298,.T.);
#812=ORIENTED_EDGE('',*,*,#1282,.T.);
#813=ORIENTED_EDGE('',*,*,#1297,.F.);
#814=ORIENTED_EDGE('',*,*,#1243,.T.);
#815=ORIENTED_EDGE('',*,*,#1299,.T.);
#816=ORIENTED_EDGE('',*,*,#1283,.T.);
#817=ORIENTED_EDGE('',*,*,#1298,.F.);
#818=ORIENTED_EDGE('',*,*,#1242,.T.);
#819=ORIENTED_EDGE('',*,*,#1300,.T.);
#820=ORIENTED_EDGE('',*,*,#1284,.T.);
#821=ORIENTED_EDGE('',*,*,#1299,.F.);
#822=ORIENTED_EDGE('',*,*,#1241,.T.);
#823=ORIENTED_EDGE('',*,*,#1301,.T.);
#824=ORIENTED_EDGE('',*,*,#1285,.T.);
#825=ORIENTED_EDGE('',*,*,#1300,.F.);
#826=ORIENTED_EDGE('',*,*,#1240,.T.);
#827=ORIENTED_EDGE('',*,*,#1302,.T.);
#828=ORIENTED_EDGE('',*,*,#1286,.T.);
#829=ORIENTED_EDGE('',*,*,#1301,.F.);
#830=ORIENTED_EDGE('',*,*,#1290,.T.);
#831=ORIENTED_EDGE('',*,*,#1287,.T.);
#832=ORIENTED_EDGE('',*,*,#1302,.F.);
#833=ORIENTED_EDGE('',*,*,#1239,.T.);
#834=ORIENTED_EDGE('',*,*,#1253,.T.);
#835=ORIENTED_EDGE('',*,*,#1256,.T.);
#836=ORIENTED_EDGE('',*,*,#1259,.T.);
#837=ORIENTED_EDGE('',*,*,#1262,.T.);
#838=ORIENTED_EDGE('',*,*,#1265,.T.);
#839=ORIENTED_EDGE('',*,*,#1268,.T.);
#840=ORIENTED_EDGE('',*,*,#1271,.T.);
#841=ORIENTED_EDGE('',*,*,#1274,.T.);
#842=ORIENTED_EDGE('',*,*,#1289,.T.);
#843=ORIENTED_EDGE('',*,*,#1272,.T.);
#844=ORIENTED_EDGE('',*,*,#1269,.T.);
#845=ORIENTED_EDGE('',*,*,#1266,.T.);
#846=ORIENTED_EDGE('',*,*,#1263,.T.);
#847=ORIENTED_EDGE('',*,*,#1260,.T.);
#848=ORIENTED_EDGE('',*,*,#1257,.T.);
#849=ORIENTED_EDGE('',*,*,#1254,.T.);
#850=ORIENTED_EDGE('',*,*,#1251,.T.);
#851=ORIENTED_EDGE('',*,*,#1237,.T.);
#852=ORIENTED_EDGE('',*,*,#1292,.T.);
#853=ORIENTED_EDGE('',*,*,#1275,.T.);
#854=ORIENTED_EDGE('',*,*,#1303,.F.);
#855=ORIENTED_EDGE('',*,*,#1304,.F.);
#856=ORIENTED_EDGE('',*,*,#1305,.F.);
#857=ORIENTED_EDGE('',*,*,#1306,.F.);
#858=ORIENTED_EDGE('',*,*,#1307,.F.);
#859=ORIENTED_EDGE('',*,*,#1308,.F.);
#860=ORIENTED_EDGE('',*,*,#1309,.F.);
#861=ORIENTED_EDGE('',*,*,#1310,.F.);
#862=ORIENTED_EDGE('',*,*,#1311,.F.);
#863=ORIENTED_EDGE('',*,*,#1312,.F.);
#864=ORIENTED_EDGE('',*,*,#1313,.F.);
#865=ORIENTED_EDGE('',*,*,#1314,.T.);
#866=ORIENTED_EDGE('',*,*,#1315,.F.);
#867=ORIENTED_EDGE('',*,*,#1316,.F.);
#868=ORIENTED_EDGE('',*,*,#1317,.F.);
#869=ORIENTED_EDGE('',*,*,#1318,.F.);
#870=ORIENTED_EDGE('',*,*,#1319,.F.);
#871=ORIENTED_EDGE('',*,*,#1320,.F.);
#872=ORIENTED_EDGE('',*,*,#1321,.F.);
#873=ORIENTED_EDGE('',*,*,#1322,.F.);
#874=ORIENTED_EDGE('',*,*,#1323,.F.);
#875=ORIENTED_EDGE('',*,*,#1324,.F.);
#876=ORIENTED_EDGE('',*,*,#1325,.F.);
#877=ORIENTED_EDGE('',*,*,#1326,.F.);
#878=ORIENTED_EDGE('',*,*,#1327,.F.);
#879=ORIENTED_EDGE('',*,*,#1314,.F.);
#880=ORIENTED_EDGE('',*,*,#1328,.F.);
#881=ORIENTED_EDGE('',*,*,#1329,.T.);
#882=ORIENTED_EDGE('',*,*,#1330,.F.);
#883=ORIENTED_EDGE('',*,*,#1329,.F.);
#884=ORIENTED_EDGE('',*,*,#1331,.F.);
#885=ORIENTED_EDGE('',*,*,#1332,.T.);
#886=ORIENTED_EDGE('',*,*,#1333,.F.);
#887=ORIENTED_EDGE('',*,*,#1332,.F.);
#888=ORIENTED_EDGE('',*,*,#1334,.F.);
#889=ORIENTED_EDGE('',*,*,#1335,.T.);
#890=ORIENTED_EDGE('',*,*,#1336,.F.);
#891=ORIENTED_EDGE('',*,*,#1335,.F.);
#892=ORIENTED_EDGE('',*,*,#1337,.F.);
#893=ORIENTED_EDGE('',*,*,#1338,.T.);
#894=ORIENTED_EDGE('',*,*,#1339,.F.);
#895=ORIENTED_EDGE('',*,*,#1338,.F.);
#896=ORIENTED_EDGE('',*,*,#1340,.F.);
#897=ORIENTED_EDGE('',*,*,#1341,.T.);
#898=ORIENTED_EDGE('',*,*,#1342,.F.);
#899=ORIENTED_EDGE('',*,*,#1341,.F.);
#900=ORIENTED_EDGE('',*,*,#1343,.F.);
#901=ORIENTED_EDGE('',*,*,#1344,.T.);
#902=ORIENTED_EDGE('',*,*,#1345,.F.);
#903=ORIENTED_EDGE('',*,*,#1344,.F.);
#904=ORIENTED_EDGE('',*,*,#1346,.F.);
#905=ORIENTED_EDGE('',*,*,#1347,.T.);
#906=ORIENTED_EDGE('',*,*,#1348,.F.);
#907=ORIENTED_EDGE('',*,*,#1347,.F.);
#908=ORIENTED_EDGE('',*,*,#1349,.F.);
#909=ORIENTED_EDGE('',*,*,#1350,.T.);
#910=ORIENTED_EDGE('',*,*,#1351,.F.);
#911=ORIENTED_EDGE('',*,*,#1350,.F.);
#912=ORIENTED_EDGE('',*,*,#1352,.F.);
#913=ORIENTED_EDGE('',*,*,#1316,.T.);
#914=ORIENTED_EDGE('',*,*,#1303,.T.);
#915=ORIENTED_EDGE('',*,*,#1327,.T.);
#916=ORIENTED_EDGE('',*,*,#1330,.T.);
#917=ORIENTED_EDGE('',*,*,#1333,.T.);
#918=ORIENTED_EDGE('',*,*,#1336,.T.);
#919=ORIENTED_EDGE('',*,*,#1339,.T.);
#920=ORIENTED_EDGE('',*,*,#1342,.T.);
#921=ORIENTED_EDGE('',*,*,#1345,.T.);
#922=ORIENTED_EDGE('',*,*,#1348,.T.);
#923=ORIENTED_EDGE('',*,*,#1351,.T.);
#924=ORIENTED_EDGE('',*,*,#1315,.T.);
#925=ORIENTED_EDGE('',*,*,#1353,.F.);
#926=ORIENTED_EDGE('',*,*,#1304,.T.);
#927=ORIENTED_EDGE('',*,*,#1353,.T.);
#928=ORIENTED_EDGE('',*,*,#1326,.T.);
#929=ORIENTED_EDGE('',*,*,#1354,.F.);
#930=ORIENTED_EDGE('',*,*,#1305,.T.);
#931=ORIENTED_EDGE('',*,*,#1354,.T.);
#932=ORIENTED_EDGE('',*,*,#1325,.T.);
#933=ORIENTED_EDGE('',*,*,#1355,.F.);
#934=ORIENTED_EDGE('',*,*,#1306,.T.);
#935=ORIENTED_EDGE('',*,*,#1355,.T.);
#936=ORIENTED_EDGE('',*,*,#1324,.T.);
#937=ORIENTED_EDGE('',*,*,#1356,.F.);
#938=ORIENTED_EDGE('',*,*,#1307,.T.);
#939=ORIENTED_EDGE('',*,*,#1356,.T.);
#940=ORIENTED_EDGE('',*,*,#1323,.T.);
#941=ORIENTED_EDGE('',*,*,#1357,.F.);
#942=ORIENTED_EDGE('',*,*,#1308,.T.);
#943=ORIENTED_EDGE('',*,*,#1357,.T.);
#944=ORIENTED_EDGE('',*,*,#1322,.T.);
#945=ORIENTED_EDGE('',*,*,#1358,.F.);
#946=ORIENTED_EDGE('',*,*,#1309,.T.);
#947=ORIENTED_EDGE('',*,*,#1358,.T.);
#948=ORIENTED_EDGE('',*,*,#1321,.T.);
#949=ORIENTED_EDGE('',*,*,#1359,.F.);
#950=ORIENTED_EDGE('',*,*,#1310,.T.);
#951=ORIENTED_EDGE('',*,*,#1359,.T.);
#952=ORIENTED_EDGE('',*,*,#1320,.T.);
#953=ORIENTED_EDGE('',*,*,#1360,.F.);
#954=ORIENTED_EDGE('',*,*,#1311,.T.);
#955=ORIENTED_EDGE('',*,*,#1360,.T.);
#956=ORIENTED_EDGE('',*,*,#1319,.T.);
#957=ORIENTED_EDGE('',*,*,#1361,.F.);
#958=ORIENTED_EDGE('',*,*,#1312,.T.);
#959=ORIENTED_EDGE('',*,*,#1361,.T.);
#960=ORIENTED_EDGE('',*,*,#1318,.T.);
#961=ORIENTED_EDGE('',*,*,#1362,.F.);
#962=ORIENTED_EDGE('',*,*,#1313,.T.);
#963=ORIENTED_EDGE('',*,*,#1362,.T.);
#964=ORIENTED_EDGE('',*,*,#1317,.T.);
#965=ORIENTED_EDGE('',*,*,#1352,.T.);
#966=ORIENTED_EDGE('',*,*,#1349,.T.);
#967=ORIENTED_EDGE('',*,*,#1346,.T.);
#968=ORIENTED_EDGE('',*,*,#1343,.T.);
#969=ORIENTED_EDGE('',*,*,#1340,.T.);
#970=ORIENTED_EDGE('',*,*,#1337,.T.);
#971=ORIENTED_EDGE('',*,*,#1334,.T.);
#972=ORIENTED_EDGE('',*,*,#1331,.T.);
#973=ORIENTED_EDGE('',*,*,#1328,.T.);
#974=EDGE_CURVE('',#1363,#1364,#1611,.T.);
#975=EDGE_CURVE('',#1365,#1363,#1612,.T.);
#976=EDGE_CURVE('',#1366,#1365,#1613,.T.);
#977=EDGE_CURVE('',#1367,#1366,#1614,.T.);
#978=EDGE_CURVE('',#1368,#1367,#96,.T.);
#979=EDGE_CURVE('',#1364,#1368,#1615,.T.);
#980=EDGE_CURVE('',#1369,#1370,#97,.T.);
#981=EDGE_CURVE('',#1371,#1369,#1616,.T.);
#982=EDGE_CURVE('',#1371,#1372,#1617,.T.);
#983=EDGE_CURVE('',#1373,#1372,#1618,.T.);
#984=EDGE_CURVE('',#1374,#1373,#1619,.T.);
#985=EDGE_CURVE('',#1370,#1374,#1620,.T.);
#986=EDGE_CURVE('',#1375,#1371,#1621,.T.);
#987=EDGE_CURVE('',#1375,#1376,#1622,.T.);
#988=EDGE_CURVE('',#1372,#1376,#1623,.T.);
#989=EDGE_CURVE('',#1377,#1375,#1624,.T.);
#990=EDGE_CURVE('',#1377,#1378,#1625,.T.);
#991=EDGE_CURVE('',#1376,#1378,#1626,.T.);
#992=EDGE_CURVE('',#1379,#1377,#98,.T.);
#993=EDGE_CURVE('',#1379,#1380,#1627,.T.);
#994=EDGE_CURVE('',#1378,#1380,#99,.T.);
#995=EDGE_CURVE('',#1381,#1379,#1628,.T.);
#996=EDGE_CURVE('',#1381,#1382,#1629,.T.);
#997=EDGE_CURVE('',#1380,#1382,#1630,.T.);
#998=EDGE_CURVE('',#1383,#1381,#100,.T.);
#999=EDGE_CURVE('',#1383,#1384,#1631,.T.);
#1000=EDGE_CURVE('',#1382,#1384,#101,.T.);
#1001=EDGE_CURVE('',#1385,#1383,#1632,.T.);
#1002=EDGE_CURVE('',#1385,#1386,#1633,.T.);
#1003=EDGE_CURVE('',#1384,#1386,#1634,.T.);
#1004=EDGE_CURVE('',#1387,#1385,#1635,.T.);
#1005=EDGE_CURVE('',#1387,#1388,#1636,.T.);
#1006=EDGE_CURVE('',#1386,#1388,#1637,.T.);
#1007=EDGE_CURVE('',#1389,#1387,#102,.F.);
#1008=EDGE_CURVE('',#1389,#1390,#1638,.T.);
#1009=EDGE_CURVE('',#1388,#1390,#103,.T.);
#1010=EDGE_CURVE('',#1391,#1389,#1639,.T.);
#1011=EDGE_CURVE('',#1391,#1392,#1640,.T.);
#1012=EDGE_CURVE('',#1390,#1392,#1641,.T.);
#1013=EDGE_CURVE('',#1393,#1391,#104,.F.);
#1014=EDGE_CURVE('',#1393,#1394,#1642,.T.);
#1015=EDGE_CURVE('',#1392,#1394,#105,.T.);
#1016=EDGE_CURVE('',#1366,#1393,#106,.T.);
#1017=EDGE_CURVE('',#1394,#1365,#107,.T.);
#1018=EDGE_CURVE('',#1363,#1373,#1643,.T.);
#1019=EDGE_CURVE('',#1370,#1368,#1644,.T.);
#1020=EDGE_CURVE('',#1369,#1367,#1645,.T.);
#1021=EDGE_CURVE('',#1395,#1396,#108,.T.);
#1022=EDGE_CURVE('',#1397,#1395,#1646,.T.);
#1023=EDGE_CURVE('',#1397,#1398,#1647,.T.);
#1024=EDGE_CURVE('',#1399,#1398,#1648,.T.);
#1025=EDGE_CURVE('',#1400,#1399,#1649,.T.);
#1026=EDGE_CURVE('',#1396,#1400,#1650,.T.);
#1027=EDGE_CURVE('',#1401,#1397,#109,.T.);
#1028=EDGE_CURVE('',#1401,#1402,#1651,.T.);
#1029=EDGE_CURVE('',#1398,#1402,#110,.T.);
#1030=EDGE_CURVE('',#1403,#1401,#111,.F.);
#1031=EDGE_CURVE('',#1403,#1404,#1652,.T.);
#1032=EDGE_CURVE('',#1402,#1404,#112,.T.);
#1033=EDGE_CURVE('',#1405,#1403,#1653,.T.);
#1034=EDGE_CURVE('',#1405,#1406,#1654,.T.);
#1035=EDGE_CURVE('',#1404,#1406,#1655,.T.);
#1036=EDGE_CURVE('',#1407,#1405,#113,.F.);
#1037=EDGE_CURVE('',#1407,#1408,#1656,.T.);
#1038=EDGE_CURVE('',#1406,#1408,#114,.T.);
#1039=EDGE_CURVE('',#1409,#1407,#1657,.T.);
#1040=EDGE_CURVE('',#1409,#1410,#1658,.T.);
#1041=EDGE_CURVE('',#1408,#1410,#1659,.T.);
#1042=EDGE_CURVE('',#1411,#1409,#1660,.T.);
#1043=EDGE_CURVE('',#1411,#1412,#1661,.T.);
#1044=EDGE_CURVE('',#1410,#1412,#1662,.T.);
#1045=EDGE_CURVE('',#1413,#1411,#115,.T.);
#1046=EDGE_CURVE('',#1413,#1414,#1663,.T.);
#1047=EDGE_CURVE('',#1412,#1414,#116,.T.);
#1048=EDGE_CURVE('',#1415,#1413,#1664,.T.);
#1049=EDGE_CURVE('',#1415,#1416,#1665,.T.);
#1050=EDGE_CURVE('',#1414,#1416,#1666,.T.);
#1051=EDGE_CURVE('',#1417,#1415,#117,.T.);
#1052=EDGE_CURVE('',#1417,#1418,#1667,.T.);
#1053=EDGE_CURVE('',#1416,#1418,#118,.T.);
#1054=EDGE_CURVE('',#1419,#1417,#1668,.T.);
#1055=EDGE_CURVE('',#1419,#1420,#1669,.T.);
#1056=EDGE_CURVE('',#1418,#1420,#1670,.T.);
#1057=EDGE_CURVE('',#1421,#1419,#1671,.T.);
#1058=EDGE_CURVE('',#1421,#1422,#1672,.T.);
#1059=EDGE_CURVE('',#1420,#1422,#1673,.T.);
#1060=EDGE_CURVE('',#1423,#1424,#1674,.T.);
#1061=EDGE_CURVE('',#1422,#1423,#1675,.T.);
#1062=EDGE_CURVE('',#1425,#1421,#1676,.T.);
#1063=EDGE_CURVE('',#1426,#1425,#119,.T.);
#1064=EDGE_CURVE('',#1424,#1426,#1677,.T.);
#1065=EDGE_CURVE('',#1396,#1426,#1678,.T.);
#1066=EDGE_CURVE('',#1395,#1425,#1679,.T.);
#1067=EDGE_CURVE('',#1423,#1399,#1680,.T.);
#1068=EDGE_CURVE('',#1427,#1428,#1681,.T.);
#1069=EDGE_CURVE('',#1429,#1427,#1682,.T.);
#1070=EDGE_CURVE('',#1430,#1429,#1683,.T.);
#1071=EDGE_CURVE('',#1428,#1430,#1684,.T.);
#1072=EDGE_CURVE('',#1429,#1431,#120,.T.);
#1073=EDGE_CURVE('',#1432,#1427,#121,.T.);
#1074=EDGE_CURVE('',#1431,#1432,#1685,.T.);
#1075=EDGE_CURVE('',#1431,#1433,#1686,.T.);
#1076=EDGE_CURVE('',#1434,#1432,#1687,.T.);
#1077=EDGE_CURVE('',#1433,#1434,#1688,.T.);
#1078=EDGE_CURVE('',#1433,#1435,#1689,.T.);
#1079=EDGE_CURVE('',#1436,#1434,#1690,.T.);
#1080=EDGE_CURVE('',#1435,#1436,#1691,.T.);
#1081=EDGE_CURVE('',#1435,#1437,#1692,.T.);
#1082=EDGE_CURVE('',#1438,#1436,#1693,.T.);
#1083=EDGE_CURVE('',#1437,#1438,#1694,.T.);
#1084=EDGE_CURVE('',#1437,#1439,#1695,.T.);
#1085=EDGE_CURVE('',#1440,#1438,#1696,.T.);
#1086=EDGE_CURVE('',#1439,#1440,#1697,.T.);
#1087=EDGE_CURVE('',#1439,#1441,#1698,.T.);
#1088=EDGE_CURVE('',#1442,#1440,#1699,.T.);
#1089=EDGE_CURVE('',#1441,#1442,#1700,.T.);
#1090=EDGE_CURVE('',#1441,#1443,#1701,.T.);
#1091=EDGE_CURVE('',#1444,#1442,#1702,.T.);
#1092=EDGE_CURVE('',#1443,#1444,#1703,.T.);
#1093=EDGE_CURVE('',#1443,#1445,#1704,.T.);
#1094=EDGE_CURVE('',#1446,#1444,#1705,.T.);
#1095=EDGE_CURVE('',#1445,#1446,#1706,.T.);
#1096=EDGE_CURVE('',#1445,#1447,#122,.T.);
#1097=EDGE_CURVE('',#1448,#1446,#123,.T.);
#1098=EDGE_CURVE('',#1447,#1448,#1707,.T.);
#1099=EDGE_CURVE('',#1447,#1449,#1708,.T.);
#1100=EDGE_CURVE('',#1450,#1448,#1709,.T.);
#1101=EDGE_CURVE('',#1449,#1450,#1710,.T.);
#1102=EDGE_CURVE('',#1428,#1450,#1711,.T.);
#1103=EDGE_CURVE('',#1430,#1449,#1712,.T.);
#1104=EDGE_CURVE('',#1374,#1424,#1713,.T.);
#1105=EDGE_CURVE('',#1400,#1451,#1714,.T.);
#1106=EDGE_CURVE('',#1451,#1452,#1715,.T.);
#1107=EDGE_CURVE('',#1452,#1453,#1716,.F.);
#1108=EDGE_CURVE('',#1453,#1454,#1717,.F.);
#1109=EDGE_CURVE('',#1454,#1364,#1718,.T.);
#1110=EDGE_CURVE('',#1454,#1455,#1719,.T.);
#1111=EDGE_CURVE('',#1455,#1456,#1720,.F.);
#1112=EDGE_CURVE('',#1456,#1451,#1721,.F.);
#1113=EDGE_CURVE('',#1449,#1457,#1722,.T.);
#1114=EDGE_CURVE('',#1458,#1430,#1723,.T.);
#1115=EDGE_CURVE('',#1458,#1459,#1724,.F.);
#1116=EDGE_CURVE('',#1459,#1460,#1725,.F.);
#1117=EDGE_CURVE('',#1460,#1457,#1726,.F.);
#1118=EDGE_CURVE('',#1461,#1462,#1727,.T.);
#1119=EDGE_CURVE('',#1462,#1463,#1728,.T.);
#1120=EDGE_CURVE('',#1463,#1464,#1729,.T.);
#1121=EDGE_CURVE('',#1464,#1461,#1730,.T.);
#1122=EDGE_CURVE('',#1465,#1466,#1731,.F.);
#1123=EDGE_CURVE('',#1466,#1467,#1732,.T.);
#1124=EDGE_CURVE('',#1467,#1468,#1733,.T.);
#1125=EDGE_CURVE('',#1468,#1465,#1734,.T.);
#1126=EDGE_CURVE('',#1469,#1470,#1735,.T.);
#1127=EDGE_CURVE('',#1470,#1471,#1736,.T.);
#1128=EDGE_CURVE('',#1471,#1472,#1737,.F.);
#1129=EDGE_CURVE('',#1472,#1469,#1738,.F.);
#1130=EDGE_CURVE('',#1473,#1474,#1739,.F.);
#1131=EDGE_CURVE('',#1474,#1458,#1740,.F.);
#1132=EDGE_CURVE('',#1457,#1473,#1741,.F.);
#1133=EDGE_CURVE('',#1475,#1476,#1742,.T.);
#1134=EDGE_CURVE('',#1476,#1477,#1743,.T.);
#1135=EDGE_CURVE('',#1477,#1478,#1744,.T.);
#1136=EDGE_CURVE('',#1478,#1475,#1745,.T.);
#1137=EDGE_CURVE('',#1469,#1479,#1746,.T.);
#1138=EDGE_CURVE('',#1479,#1480,#1747,.F.);
#1139=EDGE_CURVE('',#1480,#1470,#1748,.T.);
#1140=EDGE_CURVE('',#1467,#1481,#1749,.T.);
#1141=EDGE_CURVE('',#1481,#1482,#1750,.F.);
#1142=EDGE_CURVE('',#1482,#1468,#1751,.F.);
#1143=EDGE_CURVE('',#1463,#1453,#124,.T.);
#1144=EDGE_CURVE('',#1452,#1464,#125,.F.);
#1145=EDGE_CURVE('',#1451,#1469,#38,.T.);
#1146=EDGE_CURVE('',#1452,#1472,#126,.T.);
#1147=EDGE_CURVE('',#1464,#1472,#127,.T.);
#1148=EDGE_CURVE('',#1471,#1461,#128,.F.);
#1149=EDGE_CURVE('',#1461,#1474,#129,.T.);
#1150=EDGE_CURVE('',#1473,#1462,#130,.F.);
#1151=EDGE_CURVE('',#1462,#1466,#131,.T.);
#1152=EDGE_CURVE('',#1463,#1465,#132,.T.);
#1153=EDGE_CURVE('',#1453,#1465,#133,.T.);
#1154=EDGE_CURVE('',#1468,#1454,#39,.T.);
#1155=EDGE_CURVE('',#1481,#1475,#134,.T.);
#1156=EDGE_CURVE('',#1478,#1482,#135,.F.);
#1157=EDGE_CURVE('',#1459,#1476,#136,.T.);
#1158=EDGE_CURVE('',#1475,#1460,#137,.F.);
#1159=EDGE_CURVE('',#1477,#1456,#138,.F.);
#1160=EDGE_CURVE('',#1455,#1478,#139,.T.);
#1161=EDGE_CURVE('',#1479,#1477,#140,.T.);
#1162=EDGE_CURVE('',#1476,#1480,#141,.F.);
#1163=EDGE_CURVE('',#1455,#1482,#142,.T.);
#1164=EDGE_CURVE('',#1466,#1473,#143,.T.);
#1165=EDGE_CURVE('',#1467,#1457,#40,.T.);
#1166=EDGE_CURVE('',#1481,#1460,#144,.T.);
#1167=EDGE_CURVE('',#1474,#1471,#145,.T.);
#1168=EDGE_CURVE('',#1458,#1470,#41,.F.);
#1169=EDGE_CURVE('',#1459,#1480,#146,.T.);
#1170=EDGE_CURVE('',#1456,#1479,#147,.T.);
#1171=EDGE_CURVE('',#1483,#1484,#1752,.T.);
#1172=EDGE_CURVE('',#1483,#1485,#1753,.T.);
#1173=EDGE_CURVE('',#1486,#1485,#1754,.T.);
#1174=EDGE_CURVE('',#1487,#1486,#1755,.T.);
#1175=EDGE_CURVE('',#1488,#1487,#1756,.T.);
#1176=EDGE_CURVE('',#1489,#1488,#148,.T.);
#1177=EDGE_CURVE('',#1490,#1489,#1757,.T.);
#1178=EDGE_CURVE('',#1491,#1490,#149,.T.);
#1179=EDGE_CURVE('',#1492,#1491,#1758,.T.);
#1180=EDGE_CURVE('',#1493,#1492,#1759,.T.);
#1181=EDGE_CURVE('',#1494,#1493,#150,.T.);
#1182=EDGE_CURVE('',#1495,#1494,#1760,.T.);
#1183=EDGE_CURVE('',#1496,#1495,#151,.T.);
#1184=EDGE_CURVE('',#1484,#1496,#152,.T.);
#1185=EDGE_CURVE('',#1497,#1498,#1761,.T.);
#1186=EDGE_CURVE('',#1499,#1497,#153,.T.);
#1187=EDGE_CURVE('',#1500,#1499,#154,.F.);
#1188=EDGE_CURVE('',#1501,#1500,#1762,.T.);
#1189=EDGE_CURVE('',#1502,#1501,#155,.F.);
#1190=EDGE_CURVE('',#1503,#1502,#1763,.T.);
#1191=EDGE_CURVE('',#1504,#1503,#1764,.T.);
#1192=EDGE_CURVE('',#1505,#1504,#156,.T.);
#1193=EDGE_CURVE('',#1506,#1505,#1765,.T.);
#1194=EDGE_CURVE('',#1507,#1506,#157,.T.);
#1195=EDGE_CURVE('',#1508,#1507,#1766,.T.);
#1196=EDGE_CURVE('',#1509,#1508,#1767,.T.);
#1197=EDGE_CURVE('',#1510,#1509,#1768,.T.);
#1198=EDGE_CURVE('',#1498,#1510,#1769,.T.);
#1199=EDGE_CURVE('',#1498,#1511,#158,.T.);
#1200=EDGE_CURVE('',#1512,#1510,#159,.T.);
#1201=EDGE_CURVE('',#1511,#1512,#1770,.T.);
#1202=EDGE_CURVE('',#1511,#1513,#1771,.T.);
#1203=EDGE_CURVE('',#1514,#1512,#1772,.T.);
#1204=EDGE_CURVE('',#1513,#1514,#1773,.T.);
#1205=EDGE_CURVE('',#1513,#1515,#160,.F.);
#1206=EDGE_CURVE('',#1516,#1514,#161,.F.);
#1207=EDGE_CURVE('',#1515,#1516,#1774,.T.);
#1208=EDGE_CURVE('',#1515,#1517,#1775,.T.);
#1209=EDGE_CURVE('',#1518,#1516,#1776,.T.);
#1210=EDGE_CURVE('',#1517,#1518,#1777,.T.);
#1211=EDGE_CURVE('',#1517,#1519,#1778,.T.);
#1212=EDGE_CURVE('',#1520,#1518,#1779,.T.);
#1213=EDGE_CURVE('',#1519,#1520,#1780,.T.);
#1214=EDGE_CURVE('',#1519,#1521,#1781,.T.);
#1215=EDGE_CURVE('',#1522,#1520,#1782,.T.);
#1216=EDGE_CURVE('',#1521,#1522,#1783,.T.);
#1217=EDGE_CURVE('',#1521,#1523,#162,.T.);
#1218=EDGE_CURVE('',#1524,#1522,#163,.T.);
#1219=EDGE_CURVE('',#1523,#1524,#1784,.T.);
#1220=EDGE_CURVE('',#1523,#1525,#1785,.T.);
#1221=EDGE_CURVE('',#1526,#1524,#1786,.T.);
#1222=EDGE_CURVE('',#1525,#1526,#1787,.T.);
#1223=EDGE_CURVE('',#1525,#1483,#164,.F.);
#1224=EDGE_CURVE('',#1485,#1526,#165,.F.);
#1225=EDGE_CURVE('',#1509,#1486,#1788,.T.);
#1226=EDGE_CURVE('',#1508,#1487,#1789,.T.);
#1227=EDGE_CURVE('',#1507,#1488,#1790,.T.);
#1228=EDGE_CURVE('',#1506,#1489,#1791,.T.);
#1229=EDGE_CURVE('',#1505,#1490,#1792,.T.);
#1230=EDGE_CURVE('',#1504,#1491,#1793,.T.);
#1231=EDGE_CURVE('',#1503,#1492,#1794,.T.);
#1232=EDGE_CURVE('',#1502,#1493,#1795,.T.);
#1233=EDGE_CURVE('',#1501,#1494,#1796,.T.);
#1234=EDGE_CURVE('',#1500,#1495,#1797,.T.);
#1235=EDGE_CURVE('',#1499,#1496,#1798,.T.);
#1236=EDGE_CURVE('',#1497,#1484,#1799,.T.);
#1237=EDGE_CURVE('',#1527,#1528,#1800,.T.);
#1238=EDGE_CURVE('',#1529,#1527,#1801,.T.);
#1239=EDGE_CURVE('',#1530,#1529,#1802,.T.);
#1240=EDGE_CURVE('',#1531,#1530,#1803,.T.);
#1241=EDGE_CURVE('',#1532,#1531,#1804,.T.);
#1242=EDGE_CURVE('',#1533,#1532,#166,.T.);
#1243=EDGE_CURVE('',#1534,#1533,#1805,.T.);
#1244=EDGE_CURVE('',#1535,#1534,#167,.T.);
#1245=EDGE_CURVE('',#1536,#1535,#1806,.T.);
#1246=EDGE_CURVE('',#1537,#1536,#1807,.T.);
#1247=EDGE_CURVE('',#1538,#1537,#168,.F.);
#1248=EDGE_CURVE('',#1539,#1538,#1808,.T.);
#1249=EDGE_CURVE('',#1540,#1539,#169,.F.);
#1250=EDGE_CURVE('',#1528,#1540,#170,.T.);
#1251=EDGE_CURVE('',#1541,#1527,#171,.T.);
#1252=EDGE_CURVE('',#1542,#1541,#1809,.T.);
#1253=EDGE_CURVE('',#1529,#1542,#172,.T.);
#1254=EDGE_CURVE('',#1543,#1541,#1810,.T.);
#1255=EDGE_CURVE('',#1544,#1543,#1811,.T.);
#1256=EDGE_CURVE('',#1542,#1544,#1812,.T.);
#1257=EDGE_CURVE('',#1545,#1543,#173,.F.);
#1258=EDGE_CURVE('',#1546,#1545,#1813,.T.);
#1259=EDGE_CURVE('',#1544,#1546,#174,.F.);
#1260=EDGE_CURVE('',#1547,#1545,#1814,.T.);
#1261=EDGE_CURVE('',#1548,#1547,#1815,.T.);
#1262=EDGE_CURVE('',#1546,#1548,#1816,.T.);
#1263=EDGE_CURVE('',#1549,#1547,#1817,.T.);
#1264=EDGE_CURVE('',#1550,#1549,#1818,.T.);
#1265=EDGE_CURVE('',#1548,#1550,#1819,.T.);
#1266=EDGE_CURVE('',#1551,#1549,#1820,.T.);
#1267=EDGE_CURVE('',#1552,#1551,#1821,.T.);
#1268=EDGE_CURVE('',#1550,#1552,#1822,.T.);
#1269=EDGE_CURVE('',#1553,#1551,#175,.T.);
#1270=EDGE_CURVE('',#1554,#1553,#1823,.T.);
#1271=EDGE_CURVE('',#1552,#1554,#176,.T.);
#1272=EDGE_CURVE('',#1555,#1553,#1824,.T.);
#1273=EDGE_CURVE('',#1556,#1555,#1825,.T.);
#1274=EDGE_CURVE('',#1554,#1556,#1826,.T.);
#1275=EDGE_CURVE('',#1557,#1558,#1827,.T.);
#1276=EDGE_CURVE('',#1559,#1557,#177,.T.);
#1277=EDGE_CURVE('',#1560,#1559,#178,.T.);
#1278=EDGE_CURVE('',#1561,#1560,#1828,.T.);
#1279=EDGE_CURVE('',#1562,#1561,#179,.T.);
#1280=EDGE_CURVE('',#1563,#1562,#1829,.T.);
#1281=EDGE_CURVE('',#1564,#1563,#1830,.T.);
#1282=EDGE_CURVE('',#1565,#1564,#180,.T.);
#1283=EDGE_CURVE('',#1566,#1565,#1831,.T.);
#1284=EDGE_CURVE('',#1567,#1566,#181,.T.);
#1285=EDGE_CURVE('',#1568,#1567,#1832,.T.);
#1286=EDGE_CURVE('',#1569,#1568,#1833,.T.);
#1287=EDGE_CURVE('',#1570,#1569,#1834,.T.);
#1288=EDGE_CURVE('',#1570,#1558,#1835,.T.);
#1289=EDGE_CURVE('',#1558,#1555,#182,.F.);
#1290=EDGE_CURVE('',#1556,#1570,#183,.F.);
#1291=EDGE_CURVE('',#1540,#1559,#1836,.T.);
#1292=EDGE_CURVE('',#1528,#1557,#1837,.T.);
#1293=EDGE_CURVE('',#1539,#1560,#1838,.T.);
#1294=EDGE_CURVE('',#1538,#1561,#1839,.T.);
#1295=EDGE_CURVE('',#1537,#1562,#1840,.T.);
#1296=EDGE_CURVE('',#1536,#1563,#1841,.T.);
#1297=EDGE_CURVE('',#1535,#1564,#1842,.T.);
#1298=EDGE_CURVE('',#1534,#1565,#1843,.T.);
#1299=EDGE_CURVE('',#1533,#1566,#1844,.T.);
#1300=EDGE_CURVE('',#1532,#1567,#1845,.T.);
#1301=EDGE_CURVE('',#1531,#1568,#1846,.T.);
#1302=EDGE_CURVE('',#1530,#1569,#1847,.T.);
#1303=EDGE_CURVE('',#1571,#1572,#1848,.T.);
#1304=EDGE_CURVE('',#1573,#1571,#184,.T.);
#1305=EDGE_CURVE('',#1574,#1573,#1849,.T.);
#1306=EDGE_CURVE('',#1575,#1574,#1850,.T.);
#1307=EDGE_CURVE('',#1576,#1575,#1851,.T.);
#1308=EDGE_CURVE('',#1577,#1576,#1852,.T.);
#1309=EDGE_CURVE('',#1578,#1577,#1853,.T.);
#1310=EDGE_CURVE('',#1579,#1578,#1854,.T.);
#1311=EDGE_CURVE('',#1580,#1579,#1855,.T.);
#1312=EDGE_CURVE('',#1581,#1580,#185,.T.);
#1313=EDGE_CURVE('',#1582,#1581,#1856,.T.);
#1314=EDGE_CURVE('',#1582,#1572,#1857,.T.);
#1315=EDGE_CURVE('',#1583,#1584,#1858,.T.);
#1316=EDGE_CURVE('',#1585,#1583,#1859,.T.);
#1317=EDGE_CURVE('',#1586,#1585,#1860,.T.);
#1318=EDGE_CURVE('',#1587,#1586,#186,.T.);
#1319=EDGE_CURVE('',#1588,#1587,#1861,.T.);
#1320=EDGE_CURVE('',#1589,#1588,#1862,.T.);
#1321=EDGE_CURVE('',#1590,#1589,#1863,.T.);
#1322=EDGE_CURVE('',#1591,#1590,#1864,.T.);
#1323=EDGE_CURVE('',#1592,#1591,#1865,.T.);
#1324=EDGE_CURVE('',#1593,#1592,#1866,.T.);
#1325=EDGE_CURVE('',#1594,#1593,#1867,.T.);
#1326=EDGE_CURVE('',#1584,#1594,#187,.T.);
#1327=EDGE_CURVE('',#1572,#1595,#188,.T.);
#1328=EDGE_CURVE('',#1596,#1582,#189,.T.);
#1329=EDGE_CURVE('',#1596,#1595,#1868,.T.);
#1330=EDGE_CURVE('',#1595,#1597,#1869,.T.);
#1331=EDGE_CURVE('',#1598,#1596,#1870,.T.);
#1332=EDGE_CURVE('',#1598,#1597,#1871,.T.);
#1333=EDGE_CURVE('',#1597,#1599,#190,.F.);
#1334=EDGE_CURVE('',#1600,#1598,#191,.F.);
#1335=EDGE_CURVE('',#1600,#1599,#1872,.T.);
#1336=EDGE_CURVE('',#1599,#1601,#1873,.T.);
#1337=EDGE_CURVE('',#1602,#1600,#1874,.T.);
#1338=EDGE_CURVE('',#1602,#1601,#1875,.T.);
#1339=EDGE_CURVE('',#1601,#1603,#1876,.T.);
#1340=EDGE_CURVE('',#1604,#1602,#1877,.T.);
#1341=EDGE_CURVE('',#1604,#1603,#1878,.T.);
#1342=EDGE_CURVE('',#1603,#1605,#1879,.T.);
#1343=EDGE_CURVE('',#1606,#1604,#1880,.T.);
#1344=EDGE_CURVE('',#1606,#1605,#1881,.T.);
#1345=EDGE_CURVE('',#1605,#1607,#192,.T.);
#1346=EDGE_CURVE('',#1608,#1606,#193,.T.);
#1347=EDGE_CURVE('',#1608,#1607,#1882,.T.);
#1348=EDGE_CURVE('',#1607,#1609,#1883,.T.);
#1349=EDGE_CURVE('',#1610,#1608,#1884,.T.);
#1350=EDGE_CURVE('',#1610,#1609,#1885,.T.);
#1351=EDGE_CURVE('',#1609,#1583,#194,.F.);
#1352=EDGE_CURVE('',#1585,#1610,#195,.F.);
#1353=EDGE_CURVE('',#1571,#1584,#1886,.T.);
#1354=EDGE_CURVE('',#1573,#1594,#1887,.T.);
#1355=EDGE_CURVE('',#1574,#1593,#1888,.T.);
#1356=EDGE_CURVE('',#1575,#1592,#1889,.T.);
#1357=EDGE_CURVE('',#1576,#1591,#1890,.T.);
#1358=EDGE_CURVE('',#1577,#1590,#1891,.T.);
#1359=EDGE_CURVE('',#1578,#1589,#1892,.T.);
#1360=EDGE_CURVE('',#1579,#1588,#1893,.T.);
#1361=EDGE_CURVE('',#1580,#1587,#1894,.T.);
#1362=EDGE_CURVE('',#1581,#1586,#1895,.T.);
#1363=VERTEX_POINT('',#3815);
#1364=VERTEX_POINT('',#3816);
#1365=VERTEX_POINT('',#3818);
#1366=VERTEX_POINT('',#3820);
#1367=VERTEX_POINT('',#3822);
#1368=VERTEX_POINT('',#3824);
#1369=VERTEX_POINT('',#3828);
#1370=VERTEX_POINT('',#3829);
#1371=VERTEX_POINT('',#3831);
#1372=VERTEX_POINT('',#3833);
#1373=VERTEX_POINT('',#3835);
#1374=VERTEX_POINT('',#3837);
#1375=VERTEX_POINT('',#3841);
#1376=VERTEX_POINT('',#3843);
#1377=VERTEX_POINT('',#3847);
#1378=VERTEX_POINT('',#3849);
#1379=VERTEX_POINT('',#3853);
#1380=VERTEX_POINT('',#3855);
#1381=VERTEX_POINT('',#3859);
#1382=VERTEX_POINT('',#3861);
#1383=VERTEX_POINT('',#3865);
#1384=VERTEX_POINT('',#3867);
#1385=VERTEX_POINT('',#3871);
#1386=VERTEX_POINT('',#3873);
#1387=VERTEX_POINT('',#3877);
#1388=VERTEX_POINT('',#3879);
#1389=VERTEX_POINT('',#3883);
#1390=VERTEX_POINT('',#3885);
#1391=VERTEX_POINT('',#3889);
#1392=VERTEX_POINT('',#3891);
#1393=VERTEX_POINT('',#3895);
#1394=VERTEX_POINT('',#3897);
#1395=VERTEX_POINT('',#3910);
#1396=VERTEX_POINT('',#3911);
#1397=VERTEX_POINT('',#3913);
#1398=VERTEX_POINT('',#3915);
#1399=VERTEX_POINT('',#3917);
#1400=VERTEX_POINT('',#3919);
#1401=VERTEX_POINT('',#3923);
#1402=VERTEX_POINT('',#3925);
#1403=VERTEX_POINT('',#3929);
#1404=VERTEX_POINT('',#3931);
#1405=VERTEX_POINT('',#3935);
#1406=VERTEX_POINT('',#3937);
#1407=VERTEX_POINT('',#3941);
#1408=VERTEX_POINT('',#3943);
#1409=VERTEX_POINT('',#3947);
#1410=VERTEX_POINT('',#3949);
#1411=VERTEX_POINT('',#3953);
#1412=VERTEX_POINT('',#3955);
#1413=VERTEX_POINT('',#3959);
#1414=VERTEX_POINT('',#3961);
#1415=VERTEX_POINT('',#3965);
#1416=VERTEX_POINT('',#3967);
#1417=VERTEX_POINT('',#3971);
#1418=VERTEX_POINT('',#3973);
#1419=VERTEX_POINT('',#3977);
#1420=VERTEX_POINT('',#3979);
#1421=VERTEX_POINT('',#3983);
#1422=VERTEX_POINT('',#3985);
#1423=VERTEX_POINT('',#3989);
#1424=VERTEX_POINT('',#3990);
#1425=VERTEX_POINT('',#3993);
#1426=VERTEX_POINT('',#3995);
#1427=VERTEX_POINT('',#4005);
#1428=VERTEX_POINT('',#4006);
#1429=VERTEX_POINT('',#4008);
#1430=VERTEX_POINT('',#4010);
#1431=VERTEX_POINT('',#4014);
#1432=VERTEX_POINT('',#4016);
#1433=VERTEX_POINT('',#4020);
#1434=VERTEX_POINT('',#4022);
#1435=VERTEX_POINT('',#4026);
#1436=VERTEX_POINT('',#4028);
#1437=VERTEX_POINT('',#4032);
#1438=VERTEX_POINT('',#4034);
#1439=VERTEX_POINT('',#4038);
#1440=VERTEX_POINT('',#4040);
#1441=VERTEX_POINT('',#4044);
#1442=VERTEX_POINT('',#4046);
#1443=VERTEX_POINT('',#4050);
#1444=VERTEX_POINT('',#4052);
#1445=VERTEX_POINT('',#4056);
#1446=VERTEX_POINT('',#4058);
#1447=VERTEX_POINT('',#4062);
#1448=VERTEX_POINT('',#4064);
#1449=VERTEX_POINT('',#4068);
#1450=VERTEX_POINT('',#4070);
#1451=VERTEX_POINT('',#4079);
#1452=VERTEX_POINT('',#4081);
#1453=VERTEX_POINT('',#4083);
#1454=VERTEX_POINT('',#4085);
#1455=VERTEX_POINT('',#4089);
#1456=VERTEX_POINT('',#4091);
#1457=VERTEX_POINT('',#4095);
#1458=VERTEX_POINT('',#4097);
#1459=VERTEX_POINT('',#4099);
#1460=VERTEX_POINT('',#4101);
#1461=VERTEX_POINT('',#4105);
#1462=VERTEX_POINT('',#4106);
#1463=VERTEX_POINT('',#4108);
#1464=VERTEX_POINT('',#4110);
#1465=VERTEX_POINT('',#4114);
#1466=VERTEX_POINT('',#4115);
#1467=VERTEX_POINT('',#4117);
#1468=VERTEX_POINT('',#4119);
#1469=VERTEX_POINT('',#4123);
#1470=VERTEX_POINT('',#4124);
#1471=VERTEX_POINT('',#4126);
#1472=VERTEX_POINT('',#4128);
#1473=VERTEX_POINT('',#4132);
#1474=VERTEX_POINT('',#4133);
#1475=VERTEX_POINT('',#4138);
#1476=VERTEX_POINT('',#4139);
#1477=VERTEX_POINT('',#4141);
#1478=VERTEX_POINT('',#4143);
#1479=VERTEX_POINT('',#4147);
#1480=VERTEX_POINT('',#4149);
#1481=VERTEX_POINT('',#4153);
#1482=VERTEX_POINT('',#4155);
#1483=VERTEX_POINT('',#4212);
#1484=VERTEX_POINT('',#4213);
#1485=VERTEX_POINT('',#4215);
#1486=VERTEX_POINT('',#4217);
#1487=VERTEX_POINT('',#4219);
#1488=VERTEX_POINT('',#4221);
#1489=VERTEX_POINT('',#4223);
#1490=VERTEX_POINT('',#4225);
#1491=VERTEX_POINT('',#4227);
#1492=VERTEX_POINT('',#4229);
#1493=VERTEX_POINT('',#4231);
#1494=VERTEX_POINT('',#4233);
#1495=VERTEX_POINT('',#4235);
#1496=VERTEX_POINT('',#4237);
#1497=VERTEX_POINT('',#4241);
#1498=VERTEX_POINT('',#4242);
#1499=VERTEX_POINT('',#4244);
#1500=VERTEX_POINT('',#4246);
#1501=VERTEX_POINT('',#4248);
#1502=VERTEX_POINT('',#4250);
#1503=VERTEX_POINT('',#4252);
#1504=VERTEX_POINT('',#4254);
#1505=VERTEX_POINT('',#4256);
#1506=VERTEX_POINT('',#4258);
#1507=VERTEX_POINT('',#4260);
#1508=VERTEX_POINT('',#4262);
#1509=VERTEX_POINT('',#4264);
#1510=VERTEX_POINT('',#4266);
#1511=VERTEX_POINT('',#4270);
#1512=VERTEX_POINT('',#4272);
#1513=VERTEX_POINT('',#4276);
#1514=VERTEX_POINT('',#4278);
#1515=VERTEX_POINT('',#4282);
#1516=VERTEX_POINT('',#4284);
#1517=VERTEX_POINT('',#4288);
#1518=VERTEX_POINT('',#4290);
#1519=VERTEX_POINT('',#4294);
#1520=VERTEX_POINT('',#4296);
#1521=VERTEX_POINT('',#4300);
#1522=VERTEX_POINT('',#4302);
#1523=VERTEX_POINT('',#4306);
#1524=VERTEX_POINT('',#4308);
#1525=VERTEX_POINT('',#4312);
#1526=VERTEX_POINT('',#4314);
#1527=VERTEX_POINT('',#4346);
#1528=VERTEX_POINT('',#4347);
#1529=VERTEX_POINT('',#4349);
#1530=VERTEX_POINT('',#4351);
#1531=VERTEX_POINT('',#4353);
#1532=VERTEX_POINT('',#4355);
#1533=VERTEX_POINT('',#4357);
#1534=VERTEX_POINT('',#4359);
#1535=VERTEX_POINT('',#4361);
#1536=VERTEX_POINT('',#4363);
#1537=VERTEX_POINT('',#4365);
#1538=VERTEX_POINT('',#4367);
#1539=VERTEX_POINT('',#4369);
#1540=VERTEX_POINT('',#4371);
#1541=VERTEX_POINT('',#4375);
#1542=VERTEX_POINT('',#4377);
#1543=VERTEX_POINT('',#4381);
#1544=VERTEX_POINT('',#4383);
#1545=VERTEX_POINT('',#4387);
#1546=VERTEX_POINT('',#4389);
#1547=VERTEX_POINT('',#4393);
#1548=VERTEX_POINT('',#4395);
#1549=VERTEX_POINT('',#4399);
#1550=VERTEX_POINT('',#4401);
#1551=VERTEX_POINT('',#4405);
#1552=VERTEX_POINT('',#4407);
#1553=VERTEX_POINT('',#4411);
#1554=VERTEX_POINT('',#4413);
#1555=VERTEX_POINT('',#4417);
#1556=VERTEX_POINT('',#4419);
#1557=VERTEX_POINT('',#4423);
#1558=VERTEX_POINT('',#4424);
#1559=VERTEX_POINT('',#4426);
#1560=VERTEX_POINT('',#4428);
#1561=VERTEX_POINT('',#4430);
#1562=VERTEX_POINT('',#4432);
#1563=VERTEX_POINT('',#4434);
#1564=VERTEX_POINT('',#4436);
#1565=VERTEX_POINT('',#4438);
#1566=VERTEX_POINT('',#4440);
#1567=VERTEX_POINT('',#4442);
#1568=VERTEX_POINT('',#4444);
#1569=VERTEX_POINT('',#4446);
#1570=VERTEX_POINT('',#4448);
#1571=VERTEX_POINT('',#4480);
#1572=VERTEX_POINT('',#4481);
#1573=VERTEX_POINT('',#4483);
#1574=VERTEX_POINT('',#4485);
#1575=VERTEX_POINT('',#4487);
#1576=VERTEX_POINT('',#4489);
#1577=VERTEX_POINT('',#4491);
#1578=VERTEX_POINT('',#4493);
#1579=VERTEX_POINT('',#4495);
#1580=VERTEX_POINT('',#4497);
#1581=VERTEX_POINT('',#4499);
#1582=VERTEX_POINT('',#4501);
#1583=VERTEX_POINT('',#4505);
#1584=VERTEX_POINT('',#4506);
#1585=VERTEX_POINT('',#4508);
#1586=VERTEX_POINT('',#4510);
#1587=VERTEX_POINT('',#4512);
#1588=VERTEX_POINT('',#4514);
#1589=VERTEX_POINT('',#4516);
#1590=VERTEX_POINT('',#4518);
#1591=VERTEX_POINT('',#4520);
#1592=VERTEX_POINT('',#4522);
#1593=VERTEX_POINT('',#4524);
#1594=VERTEX_POINT('',#4526);
#1595=VERTEX_POINT('',#4530);
#1596=VERTEX_POINT('',#4532);
#1597=VERTEX_POINT('',#4536);
#1598=VERTEX_POINT('',#4538);
#1599=VERTEX_POINT('',#4542);
#1600=VERTEX_POINT('',#4544);
#1601=VERTEX_POINT('',#4548);
#1602=VERTEX_POINT('',#4550);
#1603=VERTEX_POINT('',#4554);
#1604=VERTEX_POINT('',#4556);
#1605=VERTEX_POINT('',#4560);
#1606=VERTEX_POINT('',#4562);
#1607=VERTEX_POINT('',#4566);
#1608=VERTEX_POINT('',#4568);
#1609=VERTEX_POINT('',#4572);
#1610=VERTEX_POINT('',#4574);
#1611=LINE('',#3814,#1896);
#1612=LINE('',#3817,#1897);
#1613=LINE('',#3819,#1898);
#1614=LINE('',#3821,#1899);
#1615=LINE('',#3825,#1900);
#1616=LINE('',#3830,#1901);
#1617=LINE('',#3832,#1902);
#1618=LINE('',#3834,#1903);
#1619=LINE('',#3836,#1904);
#1620=LINE('',#3838,#1905);
#1621=LINE('',#3840,#1906);
#1622=LINE('',#3842,#1907);
#1623=LINE('',#3844,#1908);
#1624=LINE('',#3846,#1909);
#1625=LINE('',#3848,#1910);
#1626=LINE('',#3850,#1911);
#1627=LINE('',#3854,#1912);
#1628=LINE('',#3858,#1913);
#1629=LINE('',#3860,#1914);
#1630=LINE('',#3862,#1915);
#1631=LINE('',#3866,#1916);
#1632=LINE('',#3870,#1917);
#1633=LINE('',#3872,#1918);
#1634=LINE('',#3874,#1919);
#1635=LINE('',#3876,#1920);
#1636=LINE('',#3878,#1921);
#1637=LINE('',#3880,#1922);
#1638=LINE('',#3884,#1923);
#1639=LINE('',#3888,#1924);
#1640=LINE('',#3890,#1925);
#1641=LINE('',#3892,#1926);
#1642=LINE('',#3896,#1927);
#1643=LINE('',#3903,#1928);
#1644=LINE('',#3905,#1929);
#1645=LINE('',#3906,#1930);
#1646=LINE('',#3912,#1931);
#1647=LINE('',#3914,#1932);
#1648=LINE('',#3916,#1933);
#1649=LINE('',#3918,#1934);
#1650=LINE('',#3920,#1935);
#1651=LINE('',#3924,#1936);
#1652=LINE('',#3930,#1937);
#1653=LINE('',#3934,#1938);
#1654=LINE('',#3936,#1939);
#1655=LINE('',#3938,#1940);
#1656=LINE('',#3942,#1941);
#1657=LINE('',#3946,#1942);
#1658=LINE('',#3948,#1943);
#1659=LINE('',#3950,#1944);
#1660=LINE('',#3952,#1945);
#1661=LINE('',#3954,#1946);
#1662=LINE('',#3956,#1947);
#1663=LINE('',#3960,#1948);
#1664=LINE('',#3964,#1949);
#1665=LINE('',#3966,#1950);
#1666=LINE('',#3968,#1951);
#1667=LINE('',#3972,#1952);
#1668=LINE('',#3976,#1953);
#1669=LINE('',#3978,#1954);
#1670=LINE('',#3980,#1955);
#1671=LINE('',#3982,#1956);
#1672=LINE('',#3984,#1957);
#1673=LINE('',#3986,#1958);
#1674=LINE('',#3988,#1959);
#1675=LINE('',#3991,#1960);
#1676=LINE('',#3992,#1961);
#1677=LINE('',#3996,#1962);
#1678=LINE('',#3998,#1963);
#1679=LINE('',#3999,#1964);
#1680=LINE('',#4002,#1965);
#1681=LINE('',#4004,#1966);
#1682=LINE('',#4007,#1967);
#1683=LINE('',#4009,#1968);
#1684=LINE('',#4011,#1969);
#1685=LINE('',#4017,#1970);
#1686=LINE('',#4019,#1971);
#1687=LINE('',#4021,#1972);
#1688=LINE('',#4023,#1973);
#1689=LINE('',#4025,#1974);
#1690=LINE('',#4027,#1975);
#1691=LINE('',#4029,#1976);
#1692=LINE('',#4031,#1977);
#1693=LINE('',#4033,#1978);
#1694=LINE('',#4035,#1979);
#1695=LINE('',#4037,#1980);
#1696=LINE('',#4039,#1981);
#1697=LINE('',#4041,#1982);
#1698=LINE('',#4043,#1983);
#1699=LINE('',#4045,#1984);
#1700=LINE('',#4047,#1985);
#1701=LINE('',#4049,#1986);
#1702=LINE('',#4051,#1987);
#1703=LINE('',#4053,#1988);
#1704=LINE('',#4055,#1989);
#1705=LINE('',#4057,#1990);
#1706=LINE('',#4059,#1991);
#1707=LINE('',#4065,#1992);
#1708=LINE('',#4067,#1993);
#1709=LINE('',#4069,#1994);
#1710=LINE('',#4071,#1995);
#1711=LINE('',#4073,#1996);
#1712=LINE('',#4075,#1997);
#1713=LINE('',#4077,#1998);
#1714=LINE('',#4078,#1999);
#1715=LINE('',#4080,#2000);
#1716=LINE('',#4082,#2001);
#1717=LINE('',#4084,#2002);
#1718=LINE('',#4086,#2003);
#1719=LINE('',#4088,#2004);
#1720=LINE('',#4090,#2005);
#1721=LINE('',#4092,#2006);
#1722=LINE('',#4094,#2007);
#1723=LINE('',#4096,#2008);
#1724=LINE('',#4098,#2009);
#1725=LINE('',#4100,#2010);
#1726=LINE('',#4102,#2011);
#1727=LINE('',#4104,#2012);
#1728=LINE('',#4107,#2013);
#1729=LINE('',#4109,#2014);
#1730=LINE('',#4111,#2015);
#1731=LINE('',#4113,#2016);
#1732=LINE('',#4116,#2017);
#1733=LINE('',#4118,#2018);
#1734=LINE('',#4120,#2019);
#1735=LINE('',#4122,#2020);
#1736=LINE('',#4125,#2021);
#1737=LINE('',#4127,#2022);
#1738=LINE('',#4129,#2023);
#1739=LINE('',#4131,#2024);
#1740=LINE('',#4134,#2025);
#1741=LINE('',#4135,#2026);
#1742=LINE('',#4137,#2027);
#1743=LINE('',#4140,#2028);
#1744=LINE('',#4142,#2029);
#1745=LINE('',#4144,#2030);
#1746=LINE('',#4146,#2031);
#1747=LINE('',#4148,#2032);
#1748=LINE('',#4150,#2033);
#1749=LINE('',#4152,#2034);
#1750=LINE('',#4154,#2035);
#1751=LINE('',#4156,#2036);
#1752=LINE('',#4211,#2037);
#1753=LINE('',#4214,#2038);
#1754=LINE('',#4216,#2039);
#1755=LINE('',#4218,#2040);
#1756=LINE('',#4220,#2041);
#1757=LINE('',#4224,#2042);
#1758=LINE('',#4228,#2043);
#1759=LINE('',#4230,#2044);
#1760=LINE('',#4234,#2045);
#1761=LINE('',#4240,#2046);
#1762=LINE('',#4247,#2047);
#1763=LINE('',#4251,#2048);
#1764=LINE('',#4253,#2049);
#1765=LINE('',#4257,#2050);
#1766=LINE('',#4261,#2051);
#1767=LINE('',#4263,#2052);
#1768=LINE('',#4265,#2053);
#1769=LINE('',#4267,#2054);
#1770=LINE('',#4273,#2055);
#1771=LINE('',#4275,#2056);
#1772=LINE('',#4277,#2057);
#1773=LINE('',#4279,#2058);
#1774=LINE('',#4285,#2059);
#1775=LINE('',#4287,#2060);
#1776=LINE('',#4289,#2061);
#1777=LINE('',#4291,#2062);
#1778=LINE('',#4293,#2063);
#1779=LINE('',#4295,#2064);
#1780=LINE('',#4297,#2065);
#1781=LINE('',#4299,#2066);
#1782=LINE('',#4301,#2067);
#1783=LINE('',#4303,#2068);
#1784=LINE('',#4309,#2069);
#1785=LINE('',#4311,#2070);
#1786=LINE('',#4313,#2071);
#1787=LINE('',#4315,#2072);
#1788=LINE('',#4320,#2073);
#1789=LINE('',#4322,#2074);
#1790=LINE('',#4324,#2075);
#1791=LINE('',#4326,#2076);
#1792=LINE('',#4328,#2077);
#1793=LINE('',#4330,#2078);
#1794=LINE('',#4332,#2079);
#1795=LINE('',#4334,#2080);
#1796=LINE('',#4336,#2081);
#1797=LINE('',#4338,#2082);
#1798=LINE('',#4340,#2083);
#1799=LINE('',#4342,#2084);
#1800=LINE('',#4345,#2085);
#1801=LINE('',#4348,#2086);
#1802=LINE('',#4350,#2087);
#1803=LINE('',#4352,#2088);
#1804=LINE('',#4354,#2089);
#1805=LINE('',#4358,#2090);
#1806=LINE('',#4362,#2091);
#1807=LINE('',#4364,#2092);
#1808=LINE('',#4368,#2093);
#1809=LINE('',#4376,#2094);
#1810=LINE('',#4380,#2095);
#1811=LINE('',#4382,#2096);
#1812=LINE('',#4384,#2097);
#1813=LINE('',#4388,#2098);
#1814=LINE('',#4392,#2099);
#1815=LINE('',#4394,#2100);
#1816=LINE('',#4396,#2101);
#1817=LINE('',#4398,#2102);
#1818=LINE('',#4400,#2103);
#1819=LINE('',#4402,#2104);
#1820=LINE('',#4404,#2105);
#1821=LINE('',#4406,#2106);
#1822=LINE('',#4408,#2107);
#1823=LINE('',#4412,#2108);
#1824=LINE('',#4416,#2109);
#1825=LINE('',#4418,#2110);
#1826=LINE('',#4420,#2111);
#1827=LINE('',#4422,#2112);
#1828=LINE('',#4429,#2113);
#1829=LINE('',#4433,#2114);
#1830=LINE('',#4435,#2115);
#1831=LINE('',#4439,#2116);
#1832=LINE('',#4443,#2117);
#1833=LINE('',#4445,#2118);
#1834=LINE('',#4447,#2119);
#1835=LINE('',#4449,#2120);
#1836=LINE('',#4454,#2121);
#1837=LINE('',#4455,#2122);
#1838=LINE('',#4457,#2123);
#1839=LINE('',#4459,#2124);
#1840=LINE('',#4461,#2125);
#1841=LINE('',#4463,#2126);
#1842=LINE('',#4465,#2127);
#1843=LINE('',#4467,#2128);
#1844=LINE('',#4469,#2129);
#1845=LINE('',#4471,#2130);
#1846=LINE('',#4473,#2131);
#1847=LINE('',#4475,#2132);
#1848=LINE('',#4479,#2133);
#1849=LINE('',#4484,#2134);
#1850=LINE('',#4486,#2135);
#1851=LINE('',#4488,#2136);
#1852=LINE('',#4490,#2137);
#1853=LINE('',#4492,#2138);
#1854=LINE('',#4494,#2139);
#1855=LINE('',#4496,#2140);
#1856=LINE('',#4500,#2141);
#1857=LINE('',#4502,#2142);
#1858=LINE('',#4504,#2143);
#1859=LINE('',#4507,#2144);
#1860=LINE('',#4509,#2145);
#1861=LINE('',#4513,#2146);
#1862=LINE('',#4515,#2147);
#1863=LINE('',#4517,#2148);
#1864=LINE('',#4519,#2149);
#1865=LINE('',#4521,#2150);
#1866=LINE('',#4523,#2151);
#1867=LINE('',#4525,#2152);
#1868=LINE('',#4533,#2153);
#1869=LINE('',#4535,#2154);
#1870=LINE('',#4537,#2155);
#1871=LINE('',#4539,#2156);
#1872=LINE('',#4545,#2157);
#1873=LINE('',#4547,#2158);
#1874=LINE('',#4549,#2159);
#1875=LINE('',#4551,#2160);
#1876=LINE('',#4553,#2161);
#1877=LINE('',#4555,#2162);
#1878=LINE('',#4557,#2163);
#1879=LINE('',#4559,#2164);
#1880=LINE('',#4561,#2165);
#1881=LINE('',#4563,#2166);
#1882=LINE('',#4569,#2167);
#1883=LINE('',#4571,#2168);
#1884=LINE('',#4573,#2169);
#1885=LINE('',#4575,#2170);
#1886=LINE('',#4580,#2171);
#1887=LINE('',#4582,#2172);
#1888=LINE('',#4584,#2173);
#1889=LINE('',#4586,#2174);
#1890=LINE('',#4588,#2175);
#1891=LINE('',#4590,#2176);
#1892=LINE('',#4592,#2177);
#1893=LINE('',#4594,#2178);
#1894=LINE('',#4596,#2179);
#1895=LINE('',#4598,#2180);
#1896=VECTOR('',#3019,1000.);
#1897=VECTOR('',#3020,1000.);
#1898=VECTOR('',#3021,1000.);
#1899=VECTOR('',#3022,1000.);
#1900=VECTOR('',#3025,1000.);
#1901=VECTOR('',#3030,1000.);
#1902=VECTOR('',#3031,1000.);
#1903=VECTOR('',#3032,1000.);
#1904=VECTOR('',#3033,1000.);
#1905=VECTOR('',#3034,1000.);
#1906=VECTOR('',#3037,1000.);
#1907=VECTOR('',#3038,1000.);
#1908=VECTOR('',#3039,1000.);
#1909=VECTOR('',#3042,1000.);
#1910=VECTOR('',#3043,1000.);
#1911=VECTOR('',#3044,1000.);
#1912=VECTOR('',#3049,1000.);
#1913=VECTOR('',#3054,1000.);
#1914=VECTOR('',#3055,1000.);
#1915=VECTOR('',#3056,1000.);
#1916=VECTOR('',#3061,1000.);
#1917=VECTOR('',#3066,1000.);
#1918=VECTOR('',#3067,1000.);
#1919=VECTOR('',#3068,1000.);
#1920=VECTOR('',#3071,1000.);
#1921=VECTOR('',#3072,1000.);
#1922=VECTOR('',#3073,1000.);
#1923=VECTOR('',#3078,1000.);
#1924=VECTOR('',#3083,1000.);
#1925=VECTOR('',#3084,1000.);
#1926=VECTOR('',#3085,1000.);
#1927=VECTOR('',#3090,1000.);
#1928=VECTOR('',#3101,1000.);
#1929=VECTOR('',#3104,1000.);
#1930=VECTOR('',#3105,1000.);
#1931=VECTOR('',#3112,1000.);
#1932=VECTOR('',#3113,1000.);
#1933=VECTOR('',#3114,1000.);
#1934=VECTOR('',#3115,1000.);
#1935=VECTOR('',#3116,1000.);
#1936=VECTOR('',#3121,1000.);
#1937=VECTOR('',#3128,1000.);
#1938=VECTOR('',#3133,1000.);
#1939=VECTOR('',#3134,1000.);
#1940=VECTOR('',#3135,1000.);
#1941=VECTOR('',#3140,1000.);
#1942=VECTOR('',#3145,1000.);
#1943=VECTOR('',#3146,1000.);
#1944=VECTOR('',#3147,1000.);
#1945=VECTOR('',#3150,1000.);
#1946=VECTOR('',#3151,1000.);
#1947=VECTOR('',#3152,1000.);
#1948=VECTOR('',#3157,1000.);
#1949=VECTOR('',#3162,1000.);
#1950=VECTOR('',#3163,1000.);
#1951=VECTOR('',#3164,1000.);
#1952=VECTOR('',#3169,1000.);
#1953=VECTOR('',#3174,1000.);
#1954=VECTOR('',#3175,1000.);
#1955=VECTOR('',#3176,1000.);
#1956=VECTOR('',#3179,1000.);
#1957=VECTOR('',#3180,1000.);
#1958=VECTOR('',#3181,1000.);
#1959=VECTOR('',#3184,1000.);
#1960=VECTOR('',#3185,1000.);
#1961=VECTOR('',#3186,1000.);
#1962=VECTOR('',#3189,1000.);
#1963=VECTOR('',#3192,1000.);
#1964=VECTOR('',#3193,1000.);
#1965=VECTOR('',#3198,1000.);
#1966=VECTOR('',#3201,1000.);
#1967=VECTOR('',#3202,1000.);
#1968=VECTOR('',#3203,1000.);
#1969=VECTOR('',#3204,1000.);
#1970=VECTOR('',#3211,1000.);
#1971=VECTOR('',#3214,1000.);
#1972=VECTOR('',#3215,1000.);
#1973=VECTOR('',#3216,1000.);
#1974=VECTOR('',#3219,1000.);
#1975=VECTOR('',#3220,1000.);
#1976=VECTOR('',#3221,1000.);
#1977=VECTOR('',#3224,1000.);
#1978=VECTOR('',#3225,1000.);
#1979=VECTOR('',#3226,1000.);
#1980=VECTOR('',#3229,1000.);
#1981=VECTOR('',#3230,1000.);
#1982=VECTOR('',#3231,1000.);
#1983=VECTOR('',#3234,1000.);
#1984=VECTOR('',#3235,1000.);
#1985=VECTOR('',#3236,1000.);
#1986=VECTOR('',#3239,1000.);
#1987=VECTOR('',#3240,1000.);
#1988=VECTOR('',#3241,1000.);
#1989=VECTOR('',#3244,1000.);
#1990=VECTOR('',#3245,1000.);
#1991=VECTOR('',#3246,1000.);
#1992=VECTOR('',#3253,1000.);
#1993=VECTOR('',#3256,1000.);
#1994=VECTOR('',#3257,1000.);
#1995=VECTOR('',#3258,1000.);
#1996=VECTOR('',#3261,1000.);
#1997=VECTOR('',#3264,1000.);
#1998=VECTOR('',#3267,1000.);
#1999=VECTOR('',#3268,1000.);
#2000=VECTOR('',#3269,1000.);
#2001=VECTOR('',#3270,1000.);
#2002=VECTOR('',#3271,1000.);
#2003=VECTOR('',#3272,1000.);
#2004=VECTOR('',#3275,1000.);
#2005=VECTOR('',#3276,1000.);
#2006=VECTOR('',#3277,1000.);
#2007=VECTOR('',#3280,1000.);
#2008=VECTOR('',#3281,1000.);
#2009=VECTOR('',#3282,1000.);
#2010=VECTOR('',#3283,1000.);
#2011=VECTOR('',#3284,1000.);
#2012=VECTOR('',#3287,1000.);
#2013=VECTOR('',#3288,1000.);
#2014=VECTOR('',#3289,1000.);
#2015=VECTOR('',#3290,1000.);
#2016=VECTOR('',#3293,1000.);
#2017=VECTOR('',#3294,1000.);
#2018=VECTOR('',#3295,1000.);
#2019=VECTOR('',#3296,1000.);
#2020=VECTOR('',#3299,1000.);
#2021=VECTOR('',#3300,1000.);
#2022=VECTOR('',#3301,1000.);
#2023=VECTOR('',#3302,1000.);
#2024=VECTOR('',#3305,1000.);
#2025=VECTOR('',#3306,1000.);
#2026=VECTOR('',#3307,1000.);
#2027=VECTOR('',#3310,1000.);
#2028=VECTOR('',#3311,1000.);
#2029=VECTOR('',#3312,1000.);
#2030=VECTOR('',#3313,1000.);
#2031=VECTOR('',#3316,1000.);
#2032=VECTOR('',#3317,1000.);
#2033=VECTOR('',#3318,1000.);
#2034=VECTOR('',#3321,1000.);
#2035=VECTOR('',#3322,1000.);
#2036=VECTOR('',#3323,1000.);
#2037=VECTOR('',#3432,1000.);
#2038=VECTOR('',#3433,1000.);
#2039=VECTOR('',#3434,1000.);
#2040=VECTOR('',#3435,1000.);
#2041=VECTOR('',#3436,1000.);
#2042=VECTOR('',#3439,1000.);
#2043=VECTOR('',#3442,1000.);
#2044=VECTOR('',#3443,1000.);
#2045=VECTOR('',#3446,1000.);
#2046=VECTOR('',#3453,1000.);
#2047=VECTOR('',#3458,1000.);
#2048=VECTOR('',#3461,1000.);
#2049=VECTOR('',#3462,1000.);
#2050=VECTOR('',#3465,1000.);
#2051=VECTOR('',#3468,1000.);
#2052=VECTOR('',#3469,1000.);
#2053=VECTOR('',#3470,1000.);
#2054=VECTOR('',#3471,1000.);
#2055=VECTOR('',#3478,1000.);
#2056=VECTOR('',#3481,1000.);
#2057=VECTOR('',#3482,1000.);
#2058=VECTOR('',#3483,1000.);
#2059=VECTOR('',#3490,1000.);
#2060=VECTOR('',#3493,1000.);
#2061=VECTOR('',#3494,1000.);
#2062=VECTOR('',#3495,1000.);
#2063=VECTOR('',#3498,1000.);
#2064=VECTOR('',#3499,1000.);
#2065=VECTOR('',#3500,1000.);
#2066=VECTOR('',#3503,1000.);
#2067=VECTOR('',#3504,1000.);
#2068=VECTOR('',#3505,1000.);
#2069=VECTOR('',#3512,1000.);
#2070=VECTOR('',#3515,1000.);
#2071=VECTOR('',#3516,1000.);
#2072=VECTOR('',#3517,1000.);
#2073=VECTOR('',#3526,1000.);
#2074=VECTOR('',#3529,1000.);
#2075=VECTOR('',#3532,1000.);
#2076=VECTOR('',#3535,1000.);
#2077=VECTOR('',#3538,1000.);
#2078=VECTOR('',#3541,1000.);
#2079=VECTOR('',#3544,1000.);
#2080=VECTOR('',#3547,1000.);
#2081=VECTOR('',#3550,1000.);
#2082=VECTOR('',#3553,1000.);
#2083=VECTOR('',#3556,1000.);
#2084=VECTOR('',#3559,1000.);
#2085=VECTOR('',#3564,1000.);
#2086=VECTOR('',#3565,1000.);
#2087=VECTOR('',#3566,1000.);
#2088=VECTOR('',#3567,1000.);
#2089=VECTOR('',#3568,1000.);
#2090=VECTOR('',#3571,1000.);
#2091=VECTOR('',#3574,1000.);
#2092=VECTOR('',#3575,1000.);
#2093=VECTOR('',#3578,1000.);
#2094=VECTOR('',#3587,1000.);
#2095=VECTOR('',#3592,1000.);
#2096=VECTOR('',#3593,1000.);
#2097=VECTOR('',#3594,1000.);
#2098=VECTOR('',#3599,1000.);
#2099=VECTOR('',#3604,1000.);
#2100=VECTOR('',#3605,1000.);
#2101=VECTOR('',#3606,1000.);
#2102=VECTOR('',#3609,1000.);
#2103=VECTOR('',#3610,1000.);
#2104=VECTOR('',#3611,1000.);
#2105=VECTOR('',#3614,1000.);
#2106=VECTOR('',#3615,1000.);
#2107=VECTOR('',#3616,1000.);
#2108=VECTOR('',#3621,1000.);
#2109=VECTOR('',#3626,1000.);
#2110=VECTOR('',#3627,1000.);
#2111=VECTOR('',#3628,1000.);
#2112=VECTOR('',#3631,1000.);
#2113=VECTOR('',#3636,1000.);
#2114=VECTOR('',#3639,1000.);
#2115=VECTOR('',#3640,1000.);
#2116=VECTOR('',#3643,1000.);
#2117=VECTOR('',#3646,1000.);
#2118=VECTOR('',#3647,1000.);
#2119=VECTOR('',#3648,1000.);
#2120=VECTOR('',#3649,1000.);
#2121=VECTOR('',#3658,1000.);
#2122=VECTOR('',#3659,1000.);
#2123=VECTOR('',#3662,1000.);
#2124=VECTOR('',#3665,1000.);
#2125=VECTOR('',#3668,1000.);
#2126=VECTOR('',#3671,1000.);
#2127=VECTOR('',#3674,1000.);
#2128=VECTOR('',#3677,1000.);
#2129=VECTOR('',#3680,1000.);
#2130=VECTOR('',#3683,1000.);
#2131=VECTOR('',#3686,1000.);
#2132=VECTOR('',#3689,1000.);
#2133=VECTOR('',#3696,1000.);
#2134=VECTOR('',#3699,1000.);
#2135=VECTOR('',#3700,1000.);
#2136=VECTOR('',#3701,1000.);
#2137=VECTOR('',#3702,1000.);
#2138=VECTOR('',#3703,1000.);
#2139=VECTOR('',#3704,1000.);
#2140=VECTOR('',#3705,1000.);
#2141=VECTOR('',#3708,1000.);
#2142=VECTOR('',#3709,1000.);
#2143=VECTOR('',#3712,1000.);
#2144=VECTOR('',#3713,1000.);
#2145=VECTOR('',#3714,1000.);
#2146=VECTOR('',#3717,1000.);
#2147=VECTOR('',#3718,1000.);
#2148=VECTOR('',#3719,1000.);
#2149=VECTOR('',#3720,1000.);
#2150=VECTOR('',#3721,1000.);
#2151=VECTOR('',#3722,1000.);
#2152=VECTOR('',#3723,1000.);
#2153=VECTOR('',#3732,1000.);
#2154=VECTOR('',#3735,1000.);
#2155=VECTOR('',#3736,1000.);
#2156=VECTOR('',#3737,1000.);
#2157=VECTOR('',#3744,1000.);
#2158=VECTOR('',#3747,1000.);
#2159=VECTOR('',#3748,1000.);
#2160=VECTOR('',#3749,1000.);
#2161=VECTOR('',#3752,1000.);
#2162=VECTOR('',#3753,1000.);
#2163=VECTOR('',#3754,1000.);
#2164=VECTOR('',#3757,1000.);
#2165=VECTOR('',#3758,1000.);
#2166=VECTOR('',#3759,1000.);
#2167=VECTOR('',#3766,1000.);
#2168=VECTOR('',#3769,1000.);
#2169=VECTOR('',#3770,1000.);
#2170=VECTOR('',#3771,1000.);
#2171=VECTOR('',#3780,1000.);
#2172=VECTOR('',#3783,1000.);
#2173=VECTOR('',#3786,1000.);
#2174=VECTOR('',#3789,1000.);
#2175=VECTOR('',#3792,1000.);
#2176=VECTOR('',#3795,1000.);
#2177=VECTOR('',#3798,1000.);
#2178=VECTOR('',#3801,1000.);
#2179=VECTOR('',#3804,1000.);
#2180=VECTOR('',#3807,1000.);
#2181=EDGE_LOOP('',(#196,#197,#198,#199,#200,#201));
#2182=EDGE_LOOP('',(#202,#203,#204,#205,#206,#207));
#2183=EDGE_LOOP('',(#208,#209,#210,#211));
#2184=EDGE_LOOP('',(#212,#213,#214,#215));
#2185=EDGE_LOOP('',(#216,#217,#218,#219));
#2186=EDGE_LOOP('',(#220,#221,#222,#223));
#2187=EDGE_LOOP('',(#224,#225,#226,#227));
#2188=EDGE_LOOP('',(#228,#229,#230,#231));
#2189=EDGE_LOOP('',(#232,#233,#234,#235));
#2190=EDGE_LOOP('',(#236,#237,#238,#239));
#2191=EDGE_LOOP('',(#240,#241,#242,#243));
#2192=EDGE_LOOP('',(#244,#245,#246,#247));
#2193=EDGE_LOOP('',(#248,#249,#250,#251));
#2194=EDGE_LOOP('',(#252,#253,#254,#255,#256,#257,#258,#259,#260,#261,#262,
#263,#264,#265));
#2195=EDGE_LOOP('',(#266,#267,#268,#269));
#2196=EDGE_LOOP('',(#270,#271,#272,#273,#274,#275,#276,#277,#278,#279,#280,
#281,#282,#283));
#2197=EDGE_LOOP('',(#284,#285,#286,#287,#288,#289));
#2198=EDGE_LOOP('',(#290,#291,#292,#293));
#2199=EDGE_LOOP('',(#294,#295,#296,#297));
#2200=EDGE_LOOP('',(#298,#299,#300,#301));
#2201=EDGE_LOOP('',(#302,#303,#304,#305));
#2202=EDGE_LOOP('',(#306,#307,#308,#309));
#2203=EDGE_LOOP('',(#310,#311,#312,#313));
#2204=EDGE_LOOP('',(#314,#315,#316,#317));
#2205=EDGE_LOOP('',(#318,#319,#320,#321));
#2206=EDGE_LOOP('',(#322,#323,#324,#325));
#2207=EDGE_LOOP('',(#326,#327,#328,#329));
#2208=EDGE_LOOP('',(#330,#331,#332,#333));
#2209=EDGE_LOOP('',(#334,#335,#336,#337,#338,#339));
#2210=EDGE_LOOP('',(#340,#341,#342,#343));
#2211=EDGE_LOOP('',(#344,#345,#346,#347,#348,#349,#350,#351,#352,#353,#354,
#355,#356,#357));
#2212=EDGE_LOOP('',(#358,#359,#360,#361,#362,#363,#364,#365,#366,#367,#368,
#369,#370,#371));
#2213=EDGE_LOOP('',(#372,#373,#374,#375));
#2214=EDGE_LOOP('',(#376,#377,#378,#379));
#2215=EDGE_LOOP('',(#380,#381,#382,#383));
#2216=EDGE_LOOP('',(#384,#385,#386,#387));
#2217=EDGE_LOOP('',(#388,#389,#390,#391));
#2218=EDGE_LOOP('',(#392,#393,#394,#395));
#2219=EDGE_LOOP('',(#396,#397,#398,#399));
#2220=EDGE_LOOP('',(#400,#401,#402,#403));
#2221=EDGE_LOOP('',(#404,#405,#406,#407));
#2222=EDGE_LOOP('',(#408,#409,#410,#411));
#2223=EDGE_LOOP('',(#412,#413,#414,#415));
#2224=EDGE_LOOP('',(#416,#417,#418,#419,#420,#421,#422,#423,#424,#425,#426,
#427));
#2225=EDGE_LOOP('',(#428,#429,#430,#431,#432,#433,#434,#435,#436,#437,#438,
#439));
#2226=EDGE_LOOP('',(#440,#441,#442,#443,#444,#445,#446,#447,#448,#449,#450,
#451));
#2227=EDGE_LOOP('',(#452,#453,#454,#455,#456,#457,#458,#459,#460,#461,#462,
#463));
#2228=EDGE_LOOP('',(#464,#465,#466,#467,#468,#469,#470,#471));
#2229=EDGE_LOOP('',(#472,#473,#474,#475));
#2230=EDGE_LOOP('',(#476,#477,#478,#479));
#2231=EDGE_LOOP('',(#480,#481,#482,#483));
#2232=EDGE_LOOP('',(#484,#485,#486,#487,#488,#489));
#2233=EDGE_LOOP('',(#490,#491,#492,#493));
#2234=EDGE_LOOP('',(#494,#495,#496,#497));
#2235=EDGE_LOOP('',(#498,#499,#500,#501));
#2236=EDGE_LOOP('',(#502,#503,#504,#505));
#2237=EDGE_LOOP('',(#506,#507,#508,#509));
#2238=EDGE_LOOP('',(#510,#511,#512));
#2239=EDGE_LOOP('',(#513,#514,#515,#516));
#2240=EDGE_LOOP('',(#517,#518,#519,#520));
#2241=EDGE_LOOP('',(#521,#522,#523,#524));
#2242=EDGE_LOOP('',(#525,#526,#527));
#2243=EDGE_LOOP('',(#528,#529,#530,#531));
#2244=EDGE_LOOP('',(#532,#533,#534,#535));
#2245=EDGE_LOOP('',(#536,#537,#538,#539));
#2246=EDGE_LOOP('',(#540,#541,#542,#543));
#2247=EDGE_LOOP('',(#544,#545,#546,#547));
#2248=EDGE_LOOP('',(#548,#549,#550,#551));
#2249=EDGE_LOOP('',(#552,#553,#554));
#2250=EDGE_LOOP('',(#555,#556,#557));
#2251=EDGE_LOOP('',(#558,#559,#560,#561));
#2252=EDGE_LOOP('',(#562,#563,#564,#565));
#2253=EDGE_LOOP('',(#566,#567,#568));
#2254=EDGE_LOOP('',(#569,#570,#571));
#2255=EDGE_LOOP('',(#572,#573,#574,#575));
#2256=EDGE_LOOP('',(#576,#577,#578,#579));
#2257=EDGE_LOOP('',(#580,#581,#582));
#2258=EDGE_LOOP('',(#583,#584,#585));
#2259=EDGE_LOOP('',(#586,#587,#588,#589));
#2260=EDGE_LOOP('',(#590,#591,#592,#593,#594,#595,#596,#597,#598,#599,#600,
#601,#602,#603));
#2261=EDGE_LOOP('',(#604,#605,#606,#607,#608,#609,#610,#611,#612,#613,#614,
#615,#616,#617));
#2262=EDGE_LOOP('',(#618,#619,#620,#621));
#2263=EDGE_LOOP('',(#622,#623,#624,#625));
#2264=EDGE_LOOP('',(#626,#627,#628,#629));
#2265=EDGE_LOOP('',(#630,#631,#632,#633));
#2266=EDGE_LOOP('',(#634,#635,#636,#637));
#2267=EDGE_LOOP('',(#638,#639,#640,#641));
#2268=EDGE_LOOP('',(#642,#643,#644,#645));
#2269=EDGE_LOOP('',(#646,#647,#648,#649));
#2270=EDGE_LOOP('',(#650,#651,#652,#653));
#2271=EDGE_LOOP('',(#654,#655,#656,#657,#658,#659,#660,#661,#662,#663,#664,
#665));
#2272=EDGE_LOOP('',(#666,#667,#668,#669));
#2273=EDGE_LOOP('',(#670,#671,#672,#673));
#2274=EDGE_LOOP('',(#674,#675,#676,#677));
#2275=EDGE_LOOP('',(#678,#679,#680,#681));
#2276=EDGE_LOOP('',(#682,#683,#684,#685));
#2277=EDGE_LOOP('',(#686,#687,#688,#689));
#2278=EDGE_LOOP('',(#690,#691,#692,#693));
#2279=EDGE_LOOP('',(#694,#695,#696,#697));
#2280=EDGE_LOOP('',(#698,#699,#700,#701));
#2281=EDGE_LOOP('',(#702,#703,#704,#705));
#2282=EDGE_LOOP('',(#706,#707,#708,#709));
#2283=EDGE_LOOP('',(#710,#711,#712,#713,#714,#715,#716,#717,#718,#719,#720,
#721));
#2284=EDGE_LOOP('',(#722,#723,#724,#725,#726,#727,#728,#729,#730,#731,#732,
#733,#734,#735));
#2285=EDGE_LOOP('',(#736,#737,#738,#739));
#2286=EDGE_LOOP('',(#740,#741,#742,#743));
#2287=EDGE_LOOP('',(#744,#745,#746,#747));
#2288=EDGE_LOOP('',(#748,#749,#750,#751));
#2289=EDGE_LOOP('',(#752,#753,#754,#755));
#2290=EDGE_LOOP('',(#756,#757,#758,#759));
#2291=EDGE_LOOP('',(#760,#761,#762,#763));
#2292=EDGE_LOOP('',(#764,#765,#766,#767));
#2293=EDGE_LOOP('',(#768,#769,#770,#771,#772,#773,#774,#775,#776,#777,#778,
#779,#780,#781));
#2294=EDGE_LOOP('',(#782,#783,#784,#785));
#2295=EDGE_LOOP('',(#786,#787,#788,#789));
#2296=EDGE_LOOP('',(#790,#791,#792,#793));
#2297=EDGE_LOOP('',(#794,#795,#796,#797));
#2298=EDGE_LOOP('',(#798,#799,#800,#801));
#2299=EDGE_LOOP('',(#802,#803,#804,#805));
#2300=EDGE_LOOP('',(#806,#807,#808,#809));
#2301=EDGE_LOOP('',(#810,#811,#812,#813));
#2302=EDGE_LOOP('',(#814,#815,#816,#817));
#2303=EDGE_LOOP('',(#818,#819,#820,#821));
#2304=EDGE_LOOP('',(#822,#823,#824,#825));
#2305=EDGE_LOOP('',(#826,#827,#828,#829));
#2306=EDGE_LOOP('',(#830,#831,#832,#833,#834,#835,#836,#837,#838,#839,#840,
#841));
#2307=EDGE_LOOP('',(#842,#843,#844,#845,#846,#847,#848,#849,#850,#851,#852,
#853));
#2308=EDGE_LOOP('',(#854,#855,#856,#857,#858,#859,#860,#861,#862,#863,#864,
#865));
#2309=EDGE_LOOP('',(#866,#867,#868,#869,#870,#871,#872,#873,#874,#875,#876,
#877));
#2310=EDGE_LOOP('',(#878,#879,#880,#881));
#2311=EDGE_LOOP('',(#882,#883,#884,#885));
#2312=EDGE_LOOP('',(#886,#887,#888,#889));
#2313=EDGE_LOOP('',(#890,#891,#892,#893));
#2314=EDGE_LOOP('',(#894,#895,#896,#897));
#2315=EDGE_LOOP('',(#898,#899,#900,#901));
#2316=EDGE_LOOP('',(#902,#903,#904,#905));
#2317=EDGE_LOOP('',(#906,#907,#908,#909));
#2318=EDGE_LOOP('',(#910,#911,#912,#913));
#2319=EDGE_LOOP('',(#914,#915,#916,#917,#918,#919,#920,#921,#922,#923,#924,
#925));
#2320=EDGE_LOOP('',(#926,#927,#928,#929));
#2321=EDGE_LOOP('',(#930,#931,#932,#933));
#2322=EDGE_LOOP('',(#934,#935,#936,#937));
#2323=EDGE_LOOP('',(#938,#939,#940,#941));
#2324=EDGE_LOOP('',(#942,#943,#944,#945));
#2325=EDGE_LOOP('',(#946,#947,#948,#949));
#2326=EDGE_LOOP('',(#950,#951,#952,#953));
#2327=EDGE_LOOP('',(#954,#955,#956,#957));
#2328=EDGE_LOOP('',(#958,#959,#960,#961));
#2329=EDGE_LOOP('',(#962,#963,#964,#965,#966,#967,#968,#969,#970,#971,#972,
#973));
#2330=FACE_BOUND('',#2181,.T.);
#2331=FACE_BOUND('',#2182,.T.);
#2332=FACE_BOUND('',#2183,.T.);
#2333=FACE_BOUND('',#2184,.T.);
#2334=FACE_BOUND('',#2185,.T.);
#2335=FACE_BOUND('',#2186,.T.);
#2336=FACE_BOUND('',#2187,.T.);
#2337=FACE_BOUND('',#2188,.T.);
#2338=FACE_BOUND('',#2189,.T.);
#2339=FACE_BOUND('',#2190,.T.);
#2340=FACE_BOUND('',#2191,.T.);
#2341=FACE_BOUND('',#2192,.T.);
#2342=FACE_BOUND('',#2193,.T.);
#2343=FACE_BOUND('',#2194,.T.);
#2344=FACE_BOUND('',#2195,.T.);
#2345=FACE_BOUND('',#2196,.T.);
#2346=FACE_BOUND('',#2197,.T.);
#2347=FACE_BOUND('',#2198,.T.);
#2348=FACE_BOUND('',#2199,.T.);
#2349=FACE_BOUND('',#2200,.T.);
#2350=FACE_BOUND('',#2201,.T.);
#2351=FACE_BOUND('',#2202,.T.);
#2352=FACE_BOUND('',#2203,.T.);
#2353=FACE_BOUND('',#2204,.T.);
#2354=FACE_BOUND('',#2205,.T.);
#2355=FACE_BOUND('',#2206,.T.);
#2356=FACE_BOUND('',#2207,.T.);
#2357=FACE_BOUND('',#2208,.T.);
#2358=FACE_BOUND('',#2209,.T.);
#2359=FACE_BOUND('',#2210,.T.);
#2360=FACE_BOUND('',#2211,.T.);
#2361=FACE_BOUND('',#2212,.T.);
#2362=FACE_BOUND('',#2213,.T.);
#2363=FACE_BOUND('',#2214,.T.);
#2364=FACE_BOUND('',#2215,.T.);
#2365=FACE_BOUND('',#2216,.T.);
#2366=FACE_BOUND('',#2217,.T.);
#2367=FACE_BOUND('',#2218,.T.);
#2368=FACE_BOUND('',#2219,.T.);
#2369=FACE_BOUND('',#2220,.T.);
#2370=FACE_BOUND('',#2221,.T.);
#2371=FACE_BOUND('',#2222,.T.);
#2372=FACE_BOUND('',#2223,.T.);
#2373=FACE_BOUND('',#2224,.T.);
#2374=FACE_BOUND('',#2225,.T.);
#2375=FACE_BOUND('',#2226,.T.);
#2376=FACE_BOUND('',#2227,.T.);
#2377=FACE_BOUND('',#2228,.T.);
#2378=FACE_BOUND('',#2229,.T.);
#2379=FACE_BOUND('',#2230,.T.);
#2380=FACE_BOUND('',#2231,.T.);
#2381=FACE_BOUND('',#2232,.T.);
#2382=FACE_BOUND('',#2233,.T.);
#2383=FACE_BOUND('',#2234,.T.);
#2384=FACE_BOUND('',#2235,.T.);
#2385=FACE_BOUND('',#2236,.T.);
#2386=FACE_BOUND('',#2237,.T.);
#2387=FACE_BOUND('',#2238,.T.);
#2388=FACE_BOUND('',#2239,.T.);
#2389=FACE_BOUND('',#2240,.T.);
#2390=FACE_BOUND('',#2241,.T.);
#2391=FACE_BOUND('',#2242,.T.);
#2392=FACE_BOUND('',#2243,.T.);
#2393=FACE_BOUND('',#2244,.T.);
#2394=FACE_BOUND('',#2245,.T.);
#2395=FACE_BOUND('',#2246,.T.);
#2396=FACE_BOUND('',#2247,.T.);
#2397=FACE_BOUND('',#2248,.T.);
#2398=FACE_BOUND('',#2249,.T.);
#2399=FACE_BOUND('',#2250,.T.);
#2400=FACE_BOUND('',#2251,.T.);
#2401=FACE_BOUND('',#2252,.T.);
#2402=FACE_BOUND('',#2253,.T.);
#2403=FACE_BOUND('',#2254,.T.);
#2404=FACE_BOUND('',#2255,.T.);
#2405=FACE_BOUND('',#2256,.T.);
#2406=FACE_BOUND('',#2257,.T.);
#2407=FACE_BOUND('',#2258,.T.);
#2408=FACE_BOUND('',#2259,.T.);
#2409=FACE_BOUND('',#2260,.T.);
#2410=FACE_BOUND('',#2261,.T.);
#2411=FACE_BOUND('',#2262,.T.);
#2412=FACE_BOUND('',#2263,.T.);
#2413=FACE_BOUND('',#2264,.T.);
#2414=FACE_BOUND('',#2265,.T.);
#2415=FACE_BOUND('',#2266,.T.);
#2416=FACE_BOUND('',#2267,.T.);
#2417=FACE_BOUND('',#2268,.T.);
#2418=FACE_BOUND('',#2269,.T.);
#2419=FACE_BOUND('',#2270,.T.);
#2420=FACE_BOUND('',#2271,.T.);
#2421=FACE_BOUND('',#2272,.T.);
#2422=FACE_BOUND('',#2273,.T.);
#2423=FACE_BOUND('',#2274,.T.);
#2424=FACE_BOUND('',#2275,.T.);
#2425=FACE_BOUND('',#2276,.T.);
#2426=FACE_BOUND('',#2277,.T.);
#2427=FACE_BOUND('',#2278,.T.);
#2428=FACE_BOUND('',#2279,.T.);
#2429=FACE_BOUND('',#2280,.T.);
#2430=FACE_BOUND('',#2281,.T.);
#2431=FACE_BOUND('',#2282,.T.);
#2432=FACE_BOUND('',#2283,.T.);
#2433=FACE_BOUND('',#2284,.T.);
#2434=FACE_BOUND('',#2285,.T.);
#2435=FACE_BOUND('',#2286,.T.);
#2436=FACE_BOUND('',#2287,.T.);
#2437=FACE_BOUND('',#2288,.T.);
#2438=FACE_BOUND('',#2289,.T.);
#2439=FACE_BOUND('',#2290,.T.);
#2440=FACE_BOUND('',#2291,.T.);
#2441=FACE_BOUND('',#2292,.T.);
#2442=FACE_BOUND('',#2293,.T.);
#2443=FACE_BOUND('',#2294,.T.);
#2444=FACE_BOUND('',#2295,.T.);
#2445=FACE_BOUND('',#2296,.T.);
#2446=FACE_BOUND('',#2297,.T.);
#2447=FACE_BOUND('',#2298,.T.);
#2448=FACE_BOUND('',#2299,.T.);
#2449=FACE_BOUND('',#2300,.T.);
#2450=FACE_BOUND('',#2301,.T.);
#2451=FACE_BOUND('',#2302,.T.);
#2452=FACE_BOUND('',#2303,.T.);
#2453=FACE_BOUND('',#2304,.T.);
#2454=FACE_BOUND('',#2305,.T.);
#2455=FACE_BOUND('',#2306,.T.);
#2456=FACE_BOUND('',#2307,.T.);
#2457=FACE_BOUND('',#2308,.T.);
#2458=FACE_BOUND('',#2309,.T.);
#2459=FACE_BOUND('',#2310,.T.);
#2460=FACE_BOUND('',#2311,.T.);
#2461=FACE_BOUND('',#2312,.T.);
#2462=FACE_BOUND('',#2313,.T.);
#2463=FACE_BOUND('',#2314,.T.);
#2464=FACE_BOUND('',#2315,.T.);
#2465=FACE_BOUND('',#2316,.T.);
#2466=FACE_BOUND('',#2317,.T.);
#2467=FACE_BOUND('',#2318,.T.);
#2468=FACE_BOUND('',#2319,.T.);
#2469=FACE_BOUND('',#2320,.T.);
#2470=FACE_BOUND('',#2321,.T.);
#2471=FACE_BOUND('',#2322,.T.);
#2472=FACE_BOUND('',#2323,.T.);
#2473=FACE_BOUND('',#2324,.T.);
#2474=FACE_BOUND('',#2325,.T.);
#2475=FACE_BOUND('',#2326,.T.);
#2476=FACE_BOUND('',#2327,.T.);
#2477=FACE_BOUND('',#2328,.T.);
#2478=FACE_BOUND('',#2329,.T.);
#2479=PLANE('',#2760);
#2480=PLANE('',#2762);
#2481=PLANE('',#2764);
#2482=PLANE('',#2765);
#2483=PLANE('',#2769);
#2484=PLANE('',#2773);
#2485=PLANE('',#2774);
#2486=PLANE('',#2778);
#2487=PLANE('',#2785);
#2488=PLANE('',#2787);
#2489=PLANE('',#2788);
#2490=PLANE('',#2796);
#2491=PLANE('',#2800);
#2492=PLANE('',#2801);
#2493=PLANE('',#2805);
#2494=PLANE('',#2809);
#2495=PLANE('',#2810);
#2496=PLANE('',#2811);
#2497=PLANE('',#2814);
#2498=PLANE('',#2815);
#2499=PLANE('',#2816);
#2500=PLANE('',#2820);
#2501=PLANE('',#2821);
#2502=PLANE('',#2822);
#2503=PLANE('',#2823);
#2504=PLANE('',#2824);
#2505=PLANE('',#2825);
#2506=PLANE('',#2826);
#2507=PLANE('',#2830);
#2508=PLANE('',#2831);
#2509=PLANE('',#2832);
#2510=PLANE('',#2833);
#2511=PLANE('',#2834);
#2512=PLANE('',#2835);
#2513=PLANE('',#2836);
#2514=PLANE('',#2837);
#2515=PLANE('',#2838);
#2516=PLANE('',#2839);
#2517=PLANE('',#2840);
#2518=PLANE('',#2841);
#2519=PLANE('',#2842);
#2520=PLANE('',#2896);
#2521=PLANE('',#2902);
#2522=PLANE('',#2911);
#2523=PLANE('',#2915);
#2524=PLANE('',#2916);
#2525=PLANE('',#2917);
#2526=PLANE('',#2921);
#2527=PLANE('',#2925);
#2528=PLANE('',#2926);
#2529=PLANE('',#2927);
#2530=PLANE('',#2929);
#2531=PLANE('',#2931);
#2532=PLANE('',#2932);
#2533=PLANE('',#2934);
#2534=PLANE('',#2937);
#2535=PLANE('',#2938);
#2536=PLANE('',#2947);
#2537=PLANE('',#2951);
#2538=PLANE('',#2952);
#2539=PLANE('',#2953);
#2540=PLANE('',#2957);
#2541=PLANE('',#2958);
#2542=PLANE('',#2969);
#2543=PLANE('',#2971);
#2544=PLANE('',#2972);
#2545=PLANE('',#2974);
#2546=PLANE('',#2976);
#2547=PLANE('',#2977);
#2548=PLANE('',#2978);
#2549=PLANE('',#2979);
#2550=PLANE('',#2980);
#2551=PLANE('',#2983);
#2552=PLANE('',#2989);
#2553=PLANE('',#2993);
#2554=PLANE('',#2994);
#2555=PLANE('',#2995);
#2556=PLANE('',#2999);
#2557=PLANE('',#3003);
#2558=PLANE('',#3005);
#2559=PLANE('',#3006);
#2560=PLANE('',#3007);
#2561=PLANE('',#3008);
#2562=PLANE('',#3009);
#2563=PLANE('',#3010);
#2564=PLANE('',#3011);
#2565=PLANE('',#3013);
#2566=ADVANCED_FACE('',(#2330),#2479,.T.);
#2567=ADVANCED_FACE('',(#2331),#2480,.T.);
#2568=ADVANCED_FACE('',(#2332),#2481,.T.);
#2569=ADVANCED_FACE('',(#2333),#2482,.T.);
#2570=ADVANCED_FACE('',(#2334),#42,.T.);
#2571=ADVANCED_FACE('',(#2335),#2483,.T.);
#2572=ADVANCED_FACE('',(#2336),#43,.T.);
#2573=ADVANCED_FACE('',(#2337),#2484,.T.);
#2574=ADVANCED_FACE('',(#2338),#2485,.T.);
#2575=ADVANCED_FACE('',(#2339),#44,.F.);
#2576=ADVANCED_FACE('',(#2340),#2486,.T.);
#2577=ADVANCED_FACE('',(#2341),#45,.F.);
#2578=ADVANCED_FACE('',(#2342),#46,.T.);
#2579=ADVANCED_FACE('',(#2343),#2487,.F.);
#2580=ADVANCED_FACE('',(#2344),#47,.T.);
#2581=ADVANCED_FACE('',(#2345),#2488,.F.);
#2582=ADVANCED_FACE('',(#2346),#2489,.T.);
#2583=ADVANCED_FACE('',(#2347),#48,.T.);
#2584=ADVANCED_FACE('',(#2348),#49,.F.);
#2585=ADVANCED_FACE('',(#2349),#2490,.T.);
#2586=ADVANCED_FACE('',(#2350),#50,.F.);
#2587=ADVANCED_FACE('',(#2351),#2491,.T.);
#2588=ADVANCED_FACE('',(#2352),#2492,.T.);
#2589=ADVANCED_FACE('',(#2353),#51,.T.);
#2590=ADVANCED_FACE('',(#2354),#2493,.T.);
#2591=ADVANCED_FACE('',(#2355),#52,.T.);
#2592=ADVANCED_FACE('',(#2356),#2494,.T.);
#2593=ADVANCED_FACE('',(#2357),#2495,.T.);
#2594=ADVANCED_FACE('',(#2358),#2496,.T.);
#2595=ADVANCED_FACE('',(#2359),#53,.T.);
#2596=ADVANCED_FACE('',(#2360),#2497,.F.);
#2597=ADVANCED_FACE('',(#2361),#2498,.F.);
#2598=ADVANCED_FACE('',(#2362),#2499,.T.);
#2599=ADVANCED_FACE('',(#2363),#54,.T.);
#2600=ADVANCED_FACE('',(#2364),#2500,.T.);
#2601=ADVANCED_FACE('',(#2365),#2501,.T.);
#2602=ADVANCED_FACE('',(#2366),#2502,.T.);
#2603=ADVANCED_FACE('',(#2367),#2503,.T.);
#2604=ADVANCED_FACE('',(#2368),#2504,.T.);
#2605=ADVANCED_FACE('',(#2369),#2505,.T.);
#2606=ADVANCED_FACE('',(#2370),#2506,.T.);
#2607=ADVANCED_FACE('',(#2371),#55,.T.);
#2608=ADVANCED_FACE('',(#2372),#2507,.T.);
#2609=ADVANCED_FACE('',(#2373),#2508,.F.);
#2610=ADVANCED_FACE('',(#2374),#2509,.F.);
#2611=ADVANCED_FACE('',(#2375),#2510,.T.);
#2612=ADVANCED_FACE('',(#2376),#2511,.T.);
#2613=ADVANCED_FACE('',(#2377),#2512,.T.);
#2614=ADVANCED_FACE('',(#2378),#2513,.T.);
#2615=ADVANCED_FACE('',(#2379),#2514,.T.);
#2616=ADVANCED_FACE('',(#2380),#2515,.T.);
#2617=ADVANCED_FACE('',(#2381),#2516,.T.);
#2618=ADVANCED_FACE('',(#2382),#2517,.F.);
#2619=ADVANCED_FACE('',(#2383),#2518,.T.);
#2620=ADVANCED_FACE('',(#2384),#2519,.T.);
#2621=ADVANCED_FACE('',(#2385),#56,.T.);
#2622=ADVANCED_FACE('',(#2386),#57,.T.);
#2623=ADVANCED_FACE('',(#2387),#30,.T.);
#2624=ADVANCED_FACE('',(#2388),#58,.T.);
#2625=ADVANCED_FACE('',(#2389),#59,.T.);
#2626=ADVANCED_FACE('',(#2390),#60,.T.);
#2627=ADVANCED_FACE('',(#2391),#31,.T.);
#2628=ADVANCED_FACE('',(#2392),#61,.T.);
#2629=ADVANCED_FACE('',(#2393),#62,.T.);
#2630=ADVANCED_FACE('',(#2394),#63,.T.);
#2631=ADVANCED_FACE('',(#2395),#64,.T.);
#2632=ADVANCED_FACE('',(#2396),#65,.T.);
#2633=ADVANCED_FACE('',(#2397),#66,.T.);
#2634=ADVANCED_FACE('',(#2398),#32,.T.);
#2635=ADVANCED_FACE('',(#2399),#33,.T.);
#2636=ADVANCED_FACE('',(#2400),#67,.T.);
#2637=ADVANCED_FACE('',(#2401),#68,.T.);
#2638=ADVANCED_FACE('',(#2402),#34,.T.);
#2639=ADVANCED_FACE('',(#2403),#35,.T.);
#2640=ADVANCED_FACE('',(#2404),#69,.T.);
#2641=ADVANCED_FACE('',(#2405),#70,.T.);
#2642=ADVANCED_FACE('',(#2406),#36,.T.);
#2643=ADVANCED_FACE('',(#2407),#37,.T.);
#2644=ADVANCED_FACE('',(#2408),#71,.T.);
#2645=ADVANCED_FACE('',(#2409),#2520,.T.);
#2646=ADVANCED_FACE('',(#2410),#2521,.T.);
#2647=ADVANCED_FACE('',(#2411),#72,.F.);
#2648=ADVANCED_FACE('',(#2412),#2522,.T.);
#2649=ADVANCED_FACE('',(#2413),#73,.T.);
#2650=ADVANCED_FACE('',(#2414),#2523,.T.);
#2651=ADVANCED_FACE('',(#2415),#2524,.T.);
#2652=ADVANCED_FACE('',(#2416),#2525,.T.);
#2653=ADVANCED_FACE('',(#2417),#74,.F.);
#2654=ADVANCED_FACE('',(#2418),#2526,.T.);
#2655=ADVANCED_FACE('',(#2419),#75,.T.);
#2656=ADVANCED_FACE('',(#2420),#2527,.F.);
#2657=ADVANCED_FACE('',(#2421),#2528,.F.);
#2658=ADVANCED_FACE('',(#2422),#2529,.F.);
#2659=ADVANCED_FACE('',(#2423),#76,.F.);
#2660=ADVANCED_FACE('',(#2424),#2530,.F.);
#2661=ADVANCED_FACE('',(#2425),#77,.F.);
#2662=ADVANCED_FACE('',(#2426),#2531,.F.);
#2663=ADVANCED_FACE('',(#2427),#2532,.F.);
#2664=ADVANCED_FACE('',(#2428),#78,.T.);
#2665=ADVANCED_FACE('',(#2429),#2533,.F.);
#2666=ADVANCED_FACE('',(#2430),#79,.T.);
#2667=ADVANCED_FACE('',(#2431),#80,.F.);
#2668=ADVANCED_FACE('',(#2432),#2534,.F.);
#2669=ADVANCED_FACE('',(#2433),#2535,.T.);
#2670=ADVANCED_FACE('',(#2434),#81,.F.);
#2671=ADVANCED_FACE('',(#2435),#2536,.T.);
#2672=ADVANCED_FACE('',(#2436),#82,.T.);
#2673=ADVANCED_FACE('',(#2437),#2537,.T.);
#2674=ADVANCED_FACE('',(#2438),#2538,.T.);
#2675=ADVANCED_FACE('',(#2439),#2539,.T.);
#2676=ADVANCED_FACE('',(#2440),#83,.F.);
#2677=ADVANCED_FACE('',(#2441),#2540,.T.);
#2678=ADVANCED_FACE('',(#2442),#2541,.T.);
#2679=ADVANCED_FACE('',(#2443),#84,.T.);
#2680=ADVANCED_FACE('',(#2444),#85,.F.);
#2681=ADVANCED_FACE('',(#2445),#86,.T.);
#2682=ADVANCED_FACE('',(#2446),#2542,.F.);
#2683=ADVANCED_FACE('',(#2447),#87,.T.);
#2684=ADVANCED_FACE('',(#2448),#2543,.F.);
#2685=ADVANCED_FACE('',(#2449),#2544,.F.);
#2686=ADVANCED_FACE('',(#2450),#88,.F.);
#2687=ADVANCED_FACE('',(#2451),#2545,.F.);
#2688=ADVANCED_FACE('',(#2452),#89,.F.);
#2689=ADVANCED_FACE('',(#2453),#2546,.F.);
#2690=ADVANCED_FACE('',(#2454),#2547,.F.);
#2691=ADVANCED_FACE('',(#2455),#2548,.F.);
#2692=ADVANCED_FACE('',(#2456),#2549,.F.);
#2693=ADVANCED_FACE('',(#2457),#2550,.T.);
#2694=ADVANCED_FACE('',(#2458),#2551,.T.);
#2695=ADVANCED_FACE('',(#2459),#90,.F.);
#2696=ADVANCED_FACE('',(#2460),#2552,.T.);
#2697=ADVANCED_FACE('',(#2461),#91,.T.);
#2698=ADVANCED_FACE('',(#2462),#2553,.T.);
#2699=ADVANCED_FACE('',(#2463),#2554,.T.);
#2700=ADVANCED_FACE('',(#2464),#2555,.T.);
#2701=ADVANCED_FACE('',(#2465),#92,.F.);
#2702=ADVANCED_FACE('',(#2466),#2556,.T.);
#2703=ADVANCED_FACE('',(#2467),#93,.T.);
#2704=ADVANCED_FACE('',(#2468),#2557,.F.);
#2705=ADVANCED_FACE('',(#2469),#94,.F.);
#2706=ADVANCED_FACE('',(#2470),#2558,.F.);
#2707=ADVANCED_FACE('',(#2471),#2559,.F.);
#2708=ADVANCED_FACE('',(#2472),#2560,.F.);
#2709=ADVANCED_FACE('',(#2473),#2561,.F.);
#2710=ADVANCED_FACE('',(#2474),#2562,.F.);
#2711=ADVANCED_FACE('',(#2475),#2563,.F.);
#2712=ADVANCED_FACE('',(#2476),#2564,.F.);
#2713=ADVANCED_FACE('',(#2477),#95,.F.);
#2714=ADVANCED_FACE('',(#2478),#2565,.F.);
#2715=CLOSED_SHELL('',(#2566,#2567,#2568,#2569,#2570,#2571,#2572,#2573,
#2574,#2575,#2576,#2577,#2578,#2579,#2580,#2581,#2582,#2583,#2584,#2585,
#2586,#2587,#2588,#2589,#2590,#2591,#2592,#2593,#2594,#2595,#2596,#2597,
#2598,#2599,#2600,#2601,#2602,#2603,#2604,#2605,#2606,#2607,#2608,#2609,
#2610,#2611,#2612,#2613,#2614,#2615,#2616,#2617,#2618,#2619,#2620,#2621,
#2622,#2623,#2624,#2625,#2626,#2627,#2628,#2629,#2630,#2631,#2632,#2633,
#2634,#2635,#2636,#2637,#2638,#2639,#2640,#2641,#2642,#2643,#2644));
#2716=CLOSED_SHELL('',(#2645,#2646,#2647,#2648,#2649,#2650,#2651,#2652,
#2653,#2654,#2655,#2656,#2657,#2658,#2659,#2660,#2661,#2662,#2663,#2664,
#2665,#2666,#2667,#2668));
#2717=CLOSED_SHELL('',(#2669,#2670,#2671,#2672,#2673,#2674,#2675,#2676,
#2677,#2678,#2679,#2680,#2681,#2682,#2683,#2684,#2685,#2686,#2687,#2688,
#2689,#2690,#2691,#2692));
#2718=CLOSED_SHELL('',(#2693,#2694,#2695,#2696,#2697,#2698,#2699,#2700,
#2701,#2702,#2703,#2704,#2705,#2706,#2707,#2708,#2709,#2710,#2711,#2712,
#2713,#2714));
#2719=STYLED_ITEM('',(#2723),#2749);
#2720=STYLED_ITEM('',(#2724),#2750);
#2721=STYLED_ITEM('',(#2725),#2751);
#2722=STYLED_ITEM('',(#2726),#2752);
#2723=PRESENTATION_STYLE_ASSIGNMENT((#2727));
#2724=PRESENTATION_STYLE_ASSIGNMENT((#2728));
#2725=PRESENTATION_STYLE_ASSIGNMENT((#2729));
#2726=PRESENTATION_STYLE_ASSIGNMENT((#2730));
#2727=SURFACE_STYLE_USAGE(.BOTH.,#2731);
#2728=SURFACE_STYLE_USAGE(.BOTH.,#2732);
#2729=SURFACE_STYLE_USAGE(.BOTH.,#2733);
#2730=SURFACE_STYLE_USAGE(.BOTH.,#2734);
#2731=SURFACE_SIDE_STYLE('',(#2735));
#2732=SURFACE_SIDE_STYLE('',(#2736));
#2733=SURFACE_SIDE_STYLE('',(#2737));
#2734=SURFACE_SIDE_STYLE('',(#2738));
#2735=SURFACE_STYLE_FILL_AREA(#2739);
#2736=SURFACE_STYLE_FILL_AREA(#2740);
#2737=SURFACE_STYLE_FILL_AREA(#2741);
#2738=SURFACE_STYLE_FILL_AREA(#2742);
#2739=FILL_AREA_STYLE('',(#2743));
#2740=FILL_AREA_STYLE('',(#2744));
#2741=FILL_AREA_STYLE('',(#2745));
#2742=FILL_AREA_STYLE('',(#2746));
#2743=FILL_AREA_STYLE_COLOUR('',#2747);
#2744=FILL_AREA_STYLE_COLOUR('',#2748);
#2745=FILL_AREA_STYLE_COLOUR('',#2748);
#2746=FILL_AREA_STYLE_COLOUR('',#2748);
#2747=COLOUR_RGB('',0.200000002980232,0.200000002980232,0.200000002980232);
#2748=COLOUR_RGB('',0.506983280181885,0.531564235687256,0.550000011920929);
#2749=MANIFOLD_SOLID_BREP('',#2715);
#2750=MANIFOLD_SOLID_BREP('',#2716);
#2751=MANIFOLD_SOLID_BREP('',#2717);
#2752=MANIFOLD_SOLID_BREP('',#2718);
#2753=SHAPE_DEFINITION_REPRESENTATION(#4612,#2756);
#2754=SHAPE_DEFINITION_REPRESENTATION(#4611,#2757);
#2755=SHAPE_DEFINITION_REPRESENTATION(#4614,#2758);
#2756=SHAPE_REPRESENTATION('Compound-New',(#2759),#4603);
#2757=SHAPE_REPRESENTATION('SOT-323',(#2759,#2895,#3014),#4602);
#2758=SHAPE_REPRESENTATION('LDF-New',(#2759),#4604);
#2759=AXIS2_PLACEMENT_3D('',#3812,#3015,#3016);
#2760=AXIS2_PLACEMENT_3D('',#3813,#3017,#3018);
#2761=AXIS2_PLACEMENT_3D('',#3823,#3023,#3024);
#2762=AXIS2_PLACEMENT_3D('',#3826,#3026,#3027);
#2763=AXIS2_PLACEMENT_3D('',#3827,#3028,#3029);
#2764=AXIS2_PLACEMENT_3D('',#3839,#3035,#3036);
#2765=AXIS2_PLACEMENT_3D('',#3845,#3040,#3041);
#2766=AXIS2_PLACEMENT_3D('',#3851,#3045,#3046);
#2767=AXIS2_PLACEMENT_3D('',#3852,#3047,#3048);
#2768=AXIS2_PLACEMENT_3D('',#3856,#3050,#3051);
#2769=AXIS2_PLACEMENT_3D('',#3857,#3052,#3053);
#2770=AXIS2_PLACEMENT_3D('',#3863,#3057,#3058);
#2771=AXIS2_PLACEMENT_3D('',#3864,#3059,#3060);
#2772=AXIS2_PLACEMENT_3D('',#3868,#3062,#3063);
#2773=AXIS2_PLACEMENT_3D('',#3869,#3064,#3065);
#2774=AXIS2_PLACEMENT_3D('',#3875,#3069,#3070);
#2775=AXIS2_PLACEMENT_3D('',#3881,#3074,#3075);
#2776=AXIS2_PLACEMENT_3D('',#3882,#3076,#3077);
#2777=AXIS2_PLACEMENT_3D('',#3886,#3079,#3080);
#2778=AXIS2_PLACEMENT_3D('',#3887,#3081,#3082);
#2779=AXIS2_PLACEMENT_3D('',#3893,#3086,#3087);
#2780=AXIS2_PLACEMENT_3D('',#3894,#3088,#3089);
#2781=AXIS2_PLACEMENT_3D('',#3898,#3091,#3092);
#2782=AXIS2_PLACEMENT_3D('',#3899,#3093,#3094);
#2783=AXIS2_PLACEMENT_3D('',#3900,#3095,#3096);
#2784=AXIS2_PLACEMENT_3D('',#3901,#3097,#3098);
#2785=AXIS2_PLACEMENT_3D('',#3902,#3099,#3100);
#2786=AXIS2_PLACEMENT_3D('',#3904,#3102,#3103);
#2787=AXIS2_PLACEMENT_3D('',#3907,#3106,#3107);
#2788=AXIS2_PLACEMENT_3D('',#3908,#3108,#3109);
#2789=AXIS2_PLACEMENT_3D('',#3909,#3110,#3111);
#2790=AXIS2_PLACEMENT_3D('',#3921,#3117,#3118);
#2791=AXIS2_PLACEMENT_3D('',#3922,#3119,#3120);
#2792=AXIS2_PLACEMENT_3D('',#3926,#3122,#3123);
#2793=AXIS2_PLACEMENT_3D('',#3927,#3124,#3125);
#2794=AXIS2_PLACEMENT_3D('',#3928,#3126,#3127);
#2795=AXIS2_PLACEMENT_3D('',#3932,#3129,#3130);
#2796=AXIS2_PLACEMENT_3D('',#3933,#3131,#3132);
#2797=AXIS2_PLACEMENT_3D('',#3939,#3136,#3137);
#2798=AXIS2_PLACEMENT_3D('',#3940,#3138,#3139);
#2799=AXIS2_PLACEMENT_3D('',#3944,#3141,#3142);
#2800=AXIS2_PLACEMENT_3D('',#3945,#3143,#3144);
#2801=AXIS2_PLACEMENT_3D('',#3951,#3148,#3149);
#2802=AXIS2_PLACEMENT_3D('',#3957,#3153,#3154);
#2803=AXIS2_PLACEMENT_3D('',#3958,#3155,#3156);
#2804=AXIS2_PLACEMENT_3D('',#3962,#3158,#3159);
#2805=AXIS2_PLACEMENT_3D('',#3963,#3160,#3161);
#2806=AXIS2_PLACEMENT_3D('',#3969,#3165,#3166);
#2807=AXIS2_PLACEMENT_3D('',#3970,#3167,#3168);
#2808=AXIS2_PLACEMENT_3D('',#3974,#3170,#3171);
#2809=AXIS2_PLACEMENT_3D('',#3975,#3172,#3173);
#2810=AXIS2_PLACEMENT_3D('',#3981,#3177,#3178);
#2811=AXIS2_PLACEMENT_3D('',#3987,#3182,#3183);
#2812=AXIS2_PLACEMENT_3D('',#3994,#3187,#3188);
#2813=AXIS2_PLACEMENT_3D('',#3997,#3190,#3191);
#2814=AXIS2_PLACEMENT_3D('',#4000,#3194,#3195);
#2815=AXIS2_PLACEMENT_3D('',#4001,#3196,#3197);
#2816=AXIS2_PLACEMENT_3D('',#4003,#3199,#3200);
#2817=AXIS2_PLACEMENT_3D('',#4012,#3205,#3206);
#2818=AXIS2_PLACEMENT_3D('',#4013,#3207,#3208);
#2819=AXIS2_PLACEMENT_3D('',#4015,#3209,#3210);
#2820=AXIS2_PLACEMENT_3D('',#4018,#3212,#3213);
#2821=AXIS2_PLACEMENT_3D('',#4024,#3217,#3218);
#2822=AXIS2_PLACEMENT_3D('',#4030,#3222,#3223);
#2823=AXIS2_PLACEMENT_3D('',#4036,#3227,#3228);
#2824=AXIS2_PLACEMENT_3D('',#4042,#3232,#3233);
#2825=AXIS2_PLACEMENT_3D('',#4048,#3237,#3238);
#2826=AXIS2_PLACEMENT_3D('',#4054,#3242,#3243);
#2827=AXIS2_PLACEMENT_3D('',#4060,#3247,#3248);
#2828=AXIS2_PLACEMENT_3D('',#4061,#3249,#3250);
#2829=AXIS2_PLACEMENT_3D('',#4063,#3251,#3252);
#2830=AXIS2_PLACEMENT_3D('',#4066,#3254,#3255);
#2831=AXIS2_PLACEMENT_3D('',#4072,#3259,#3260);
#2832=AXIS2_PLACEMENT_3D('',#4074,#3262,#3263);
#2833=AXIS2_PLACEMENT_3D('',#4076,#3265,#3266);
#2834=AXIS2_PLACEMENT_3D('',#4087,#3273,#3274);
#2835=AXIS2_PLACEMENT_3D('',#4093,#3278,#3279);
#2836=AXIS2_PLACEMENT_3D('',#4103,#3285,#3286);
#2837=AXIS2_PLACEMENT_3D('',#4112,#3291,#3292);
#2838=AXIS2_PLACEMENT_3D('',#4121,#3297,#3298);
#2839=AXIS2_PLACEMENT_3D('',#4130,#3303,#3304);
#2840=AXIS2_PLACEMENT_3D('',#4136,#3308,#3309);
#2841=AXIS2_PLACEMENT_3D('',#4145,#3314,#3315);
#2842=AXIS2_PLACEMENT_3D('',#4151,#3319,#3320);
#2843=AXIS2_PLACEMENT_3D('',#4157,#3324,#3325);
#2844=AXIS2_PLACEMENT_3D('',#4158,#3326,#3327);
#2845=AXIS2_PLACEMENT_3D('',#4159,#3328,#3329);
#2846=AXIS2_PLACEMENT_3D('',#4160,#3330,#3331);
#2847=AXIS2_PLACEMENT_3D('',#4161,#3332,#3333);
#2848=AXIS2_PLACEMENT_3D('',#4162,#3334,#3335);
#2849=AXIS2_PLACEMENT_3D('',#4163,#3336,#3337);
#2850=AXIS2_PLACEMENT_3D('',#4164,#3338,#3339);
#2851=AXIS2_PLACEMENT_3D('',#4165,#3340,#3341);
#2852=AXIS2_PLACEMENT_3D('',#4166,#3342,#3343);
#2853=AXIS2_PLACEMENT_3D('',#4167,#3344,#3345);
#2854=AXIS2_PLACEMENT_3D('',#4168,#3346,#3347);
#2855=AXIS2_PLACEMENT_3D('',#4169,#3348,#3349);
#2856=AXIS2_PLACEMENT_3D('',#4170,#3350,#3351);
#2857=AXIS2_PLACEMENT_3D('',#4171,#3352,#3353);
#2858=AXIS2_PLACEMENT_3D('',#4172,#3354,#3355);
#2859=AXIS2_PLACEMENT_3D('',#4173,#3356,#3357);
#2860=AXIS2_PLACEMENT_3D('',#4174,#3358,#3359);
#2861=AXIS2_PLACEMENT_3D('',#4175,#3360,#3361);
#2862=AXIS2_PLACEMENT_3D('',#4176,#3362,#3363);
#2863=AXIS2_PLACEMENT_3D('',#4177,#3364,#3365);
#2864=AXIS2_PLACEMENT_3D('',#4178,#3366,#3367);
#2865=AXIS2_PLACEMENT_3D('',#4179,#3368,#3369);
#2866=AXIS2_PLACEMENT_3D('',#4180,#3370,#3371);
#2867=AXIS2_PLACEMENT_3D('',#4181,#3372,#3373);
#2868=AXIS2_PLACEMENT_3D('',#4182,#3374,#3375);
#2869=AXIS2_PLACEMENT_3D('',#4183,#3376,#3377);
#2870=AXIS2_PLACEMENT_3D('',#4184,#3378,#3379);
#2871=AXIS2_PLACEMENT_3D('',#4185,#3380,#3381);
#2872=AXIS2_PLACEMENT_3D('',#4186,#3382,#3383);
#2873=AXIS2_PLACEMENT_3D('',#4187,#3384,#3385);
#2874=AXIS2_PLACEMENT_3D('',#4188,#3386,#3387);
#2875=AXIS2_PLACEMENT_3D('',#4189,#3388,#3389);
#2876=AXIS2_PLACEMENT_3D('',#4190,#3390,#3391);
#2877=AXIS2_PLACEMENT_3D('',#4191,#3392,#3393);
#2878=AXIS2_PLACEMENT_3D('',#4192,#3394,#3395);
#2879=AXIS2_PLACEMENT_3D('',#4193,#3396,#3397);
#2880=AXIS2_PLACEMENT_3D('',#4194,#3398,#3399);
#2881=AXIS2_PLACEMENT_3D('',#4195,#3400,#3401);
#2882=AXIS2_PLACEMENT_3D('',#4196,#3402,#3403);
#2883=AXIS2_PLACEMENT_3D('',#4197,#3404,#3405);
#2884=AXIS2_PLACEMENT_3D('',#4198,#3406,#3407);
#2885=AXIS2_PLACEMENT_3D('',#4199,#3408,#3409);
#2886=AXIS2_PLACEMENT_3D('',#4200,#3410,#3411);
#2887=AXIS2_PLACEMENT_3D('',#4201,#3412,#3413);
#2888=AXIS2_PLACEMENT_3D('',#4202,#3414,#3415);
#2889=AXIS2_PLACEMENT_3D('',#4203,#3416,#3417);
#2890=AXIS2_PLACEMENT_3D('',#4204,#3418,#3419);
#2891=AXIS2_PLACEMENT_3D('',#4205,#3420,#3421);
#2892=AXIS2_PLACEMENT_3D('',#4206,#3422,#3423);
#2893=AXIS2_PLACEMENT_3D('',#4207,#3424,#3425);
#2894=AXIS2_PLACEMENT_3D('',#4208,#3426,#3427);
#2895=AXIS2_PLACEMENT_3D('',#4209,#3428,#3429);
#2896=AXIS2_PLACEMENT_3D('',#4210,#3430,#3431);
#2897=AXIS2_PLACEMENT_3D('',#4222,#3437,#3438);
#2898=AXIS2_PLACEMENT_3D('',#4226,#3440,#3441);
#2899=AXIS2_PLACEMENT_3D('',#4232,#3444,#3445);
#2900=AXIS2_PLACEMENT_3D('',#4236,#3447,#3448);
#2901=AXIS2_PLACEMENT_3D('',#4238,#3449,#3450);
#2902=AXIS2_PLACEMENT_3D('',#4239,#3451,#3452);
#2903=AXIS2_PLACEMENT_3D('',#4243,#3454,#3455);
#2904=AXIS2_PLACEMENT_3D('',#4245,#3456,#3457);
#2905=AXIS2_PLACEMENT_3D('',#4249,#3459,#3460);
#2906=AXIS2_PLACEMENT_3D('',#4255,#3463,#3464);
#2907=AXIS2_PLACEMENT_3D('',#4259,#3466,#3467);
#2908=AXIS2_PLACEMENT_3D('',#4268,#3472,#3473);
#2909=AXIS2_PLACEMENT_3D('',#4269,#3474,#3475);
#2910=AXIS2_PLACEMENT_3D('',#4271,#3476,#3477);
#2911=AXIS2_PLACEMENT_3D('',#4274,#3479,#3480);
#2912=AXIS2_PLACEMENT_3D('',#4280,#3484,#3485);
#2913=AXIS2_PLACEMENT_3D('',#4281,#3486,#3487);
#2914=AXIS2_PLACEMENT_3D('',#4283,#3488,#3489);
#2915=AXIS2_PLACEMENT_3D('',#4286,#3491,#3492);
#2916=AXIS2_PLACEMENT_3D('',#4292,#3496,#3497);
#2917=AXIS2_PLACEMENT_3D('',#4298,#3501,#3502);
#2918=AXIS2_PLACEMENT_3D('',#4304,#3506,#3507);
#2919=AXIS2_PLACEMENT_3D('',#4305,#3508,#3509);
#2920=AXIS2_PLACEMENT_3D('',#4307,#3510,#3511);
#2921=AXIS2_PLACEMENT_3D('',#4310,#3513,#3514);
#2922=AXIS2_PLACEMENT_3D('',#4316,#3518,#3519);
#2923=AXIS2_PLACEMENT_3D('',#4317,#3520,#3521);
#2924=AXIS2_PLACEMENT_3D('',#4318,#3522,#3523);
#2925=AXIS2_PLACEMENT_3D('',#4319,#3524,#3525);
#2926=AXIS2_PLACEMENT_3D('',#4321,#3527,#3528);
#2927=AXIS2_PLACEMENT_3D('',#4323,#3530,#3531);
#2928=AXIS2_PLACEMENT_3D('',#4325,#3533,#3534);
#2929=AXIS2_PLACEMENT_3D('',#4327,#3536,#3537);
#2930=AXIS2_PLACEMENT_3D('',#4329,#3539,#3540);
#2931=AXIS2_PLACEMENT_3D('',#4331,#3542,#3543);
#2932=AXIS2_PLACEMENT_3D('',#4333,#3545,#3546);
#2933=AXIS2_PLACEMENT_3D('',#4335,#3548,#3549);
#2934=AXIS2_PLACEMENT_3D('',#4337,#3551,#3552);
#2935=AXIS2_PLACEMENT_3D('',#4339,#3554,#3555);
#2936=AXIS2_PLACEMENT_3D('',#4341,#3557,#3558);
#2937=AXIS2_PLACEMENT_3D('',#4343,#3560,#3561);
#2938=AXIS2_PLACEMENT_3D('',#4344,#3562,#3563);
#2939=AXIS2_PLACEMENT_3D('',#4356,#3569,#3570);
#2940=AXIS2_PLACEMENT_3D('',#4360,#3572,#3573);
#2941=AXIS2_PLACEMENT_3D('',#4366,#3576,#3577);
#2942=AXIS2_PLACEMENT_3D('',#4370,#3579,#3580);
#2943=AXIS2_PLACEMENT_3D('',#4372,#3581,#3582);
#2944=AXIS2_PLACEMENT_3D('',#4373,#3583,#3584);
#2945=AXIS2_PLACEMENT_3D('',#4374,#3585,#3586);
#2946=AXIS2_PLACEMENT_3D('',#4378,#3588,#3589);
#2947=AXIS2_PLACEMENT_3D('',#4379,#3590,#3591);
#2948=AXIS2_PLACEMENT_3D('',#4385,#3595,#3596);
#2949=AXIS2_PLACEMENT_3D('',#4386,#3597,#3598);
#2950=AXIS2_PLACEMENT_3D('',#4390,#3600,#3601);
#2951=AXIS2_PLACEMENT_3D('',#4391,#3602,#3603);
#2952=AXIS2_PLACEMENT_3D('',#4397,#3607,#3608);
#2953=AXIS2_PLACEMENT_3D('',#4403,#3612,#3613);
#2954=AXIS2_PLACEMENT_3D('',#4409,#3617,#3618);
#2955=AXIS2_PLACEMENT_3D('',#4410,#3619,#3620);
#2956=AXIS2_PLACEMENT_3D('',#4414,#3622,#3623);
#2957=AXIS2_PLACEMENT_3D('',#4415,#3624,#3625);
#2958=AXIS2_PLACEMENT_3D('',#4421,#3629,#3630);
#2959=AXIS2_PLACEMENT_3D('',#4425,#3632,#3633);
#2960=AXIS2_PLACEMENT_3D('',#4427,#3634,#3635);
#2961=AXIS2_PLACEMENT_3D('',#4431,#3637,#3638);
#2962=AXIS2_PLACEMENT_3D('',#4437,#3641,#3642);
#2963=AXIS2_PLACEMENT_3D('',#4441,#3644,#3645);
#2964=AXIS2_PLACEMENT_3D('',#4450,#3650,#3651);
#2965=AXIS2_PLACEMENT_3D('',#4451,#3652,#3653);
#2966=AXIS2_PLACEMENT_3D('',#4452,#3654,#3655);
#2967=AXIS2_PLACEMENT_3D('',#4453,#3656,#3657);
#2968=AXIS2_PLACEMENT_3D('',#4456,#3660,#3661);
#2969=AXIS2_PLACEMENT_3D('',#4458,#3663,#3664);
#2970=AXIS2_PLACEMENT_3D('',#4460,#3666,#3667);
#2971=AXIS2_PLACEMENT_3D('',#4462,#3669,#3670);
#2972=AXIS2_PLACEMENT_3D('',#4464,#3672,#3673);
#2973=AXIS2_PLACEMENT_3D('',#4466,#3675,#3676);
#2974=AXIS2_PLACEMENT_3D('',#4468,#3678,#3679);
#2975=AXIS2_PLACEMENT_3D('',#4470,#3681,#3682);
#2976=AXIS2_PLACEMENT_3D('',#4472,#3684,#3685);
#2977=AXIS2_PLACEMENT_3D('',#4474,#3687,#3688);
#2978=AXIS2_PLACEMENT_3D('',#4476,#3690,#3691);
#2979=AXIS2_PLACEMENT_3D('',#4477,#3692,#3693);
#2980=AXIS2_PLACEMENT_3D('',#4478,#3694,#3695);
#2981=AXIS2_PLACEMENT_3D('',#4482,#3697,#3698);
#2982=AXIS2_PLACEMENT_3D('',#4498,#3706,#3707);
#2983=AXIS2_PLACEMENT_3D('',#4503,#3710,#3711);
#2984=AXIS2_PLACEMENT_3D('',#4511,#3715,#3716);
#2985=AXIS2_PLACEMENT_3D('',#4527,#3724,#3725);
#2986=AXIS2_PLACEMENT_3D('',#4528,#3726,#3727);
#2987=AXIS2_PLACEMENT_3D('',#4529,#3728,#3729);
#2988=AXIS2_PLACEMENT_3D('',#4531,#3730,#3731);
#2989=AXIS2_PLACEMENT_3D('',#4534,#3733,#3734);
#2990=AXIS2_PLACEMENT_3D('',#4540,#3738,#3739);
#2991=AXIS2_PLACEMENT_3D('',#4541,#3740,#3741);
#2992=AXIS2_PLACEMENT_3D('',#4543,#3742,#3743);
#2993=AXIS2_PLACEMENT_3D('',#4546,#3745,#3746);
#2994=AXIS2_PLACEMENT_3D('',#4552,#3750,#3751);
#2995=AXIS2_PLACEMENT_3D('',#4558,#3755,#3756);
#2996=AXIS2_PLACEMENT_3D('',#4564,#3760,#3761);
#2997=AXIS2_PLACEMENT_3D('',#4565,#3762,#3763);
#2998=AXIS2_PLACEMENT_3D('',#4567,#3764,#3765);
#2999=AXIS2_PLACEMENT_3D('',#4570,#3767,#3768);
#3000=AXIS2_PLACEMENT_3D('',#4576,#3772,#3773);
#3001=AXIS2_PLACEMENT_3D('',#4577,#3774,#3775);
#3002=AXIS2_PLACEMENT_3D('',#4578,#3776,#3777);
#3003=AXIS2_PLACEMENT_3D('',#4579,#3778,#3779);
#3004=AXIS2_PLACEMENT_3D('',#4581,#3781,#3782);
#3005=AXIS2_PLACEMENT_3D('',#4583,#3784,#3785);
#3006=AXIS2_PLACEMENT_3D('',#4585,#3787,#3788);
#3007=AXIS2_PLACEMENT_3D('',#4587,#3790,#3791);
#3008=AXIS2_PLACEMENT_3D('',#4589,#3793,#3794);
#3009=AXIS2_PLACEMENT_3D('',#4591,#3796,#3797);
#3010=AXIS2_PLACEMENT_3D('',#4593,#3799,#3800);
#3011=AXIS2_PLACEMENT_3D('',#4595,#3802,#3803);
#3012=AXIS2_PLACEMENT_3D('',#4597,#3805,#3806);
#3013=AXIS2_PLACEMENT_3D('',#4599,#3808,#3809);
#3014=AXIS2_PLACEMENT_3D('',#4600,#3810,#3811);
#3015=DIRECTION('',(0.,0.,1.));
#3016=DIRECTION('',(1.,0.,0.));
#3017=DIRECTION('',(3.07070986088875E-14,1.,-7.80348008433407E-14));
#3018=DIRECTION('',(1.,-3.07070986088875E-14,5.25903833505106E-17));
#3019=DIRECTION('',(-0.992546151641322,3.99882624938513E-14,0.121869343405148));
#3020=DIRECTION('',(8.64195660925325E-18,7.80348008433407E-14,1.));
#3021=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3022=DIRECTION('',(5.25903833481144E-17,-7.80348008433407E-14,-1.));
#3023=DIRECTION('',(3.07070986088875E-14,1.,-7.80348008433407E-14));
#3024=DIRECTION('',(1.,-3.07070986088875E-14,5.25903833505106E-17));
#3025=DIRECTION('',(-0.992546151641322,2.09681626107925E-14,-0.121869343405147));
#3026=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3027=DIRECTION('',(-1.,3.07070986088875E-14,-5.25903833505106E-17));
#3028=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3029=DIRECTION('',(-1.,3.07070986088875E-14,-5.25903833505106E-17));
#3030=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3031=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3032=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3033=DIRECTION('',(0.992546151641322,-3.99882624938513E-14,-0.121869343405148));
#3034=DIRECTION('',(0.992546151641322,-2.09681626107925E-14,0.121869343405147));
#3035=DIRECTION('',(-2.10447101932468E-14,-0.685541870715709,0.728033202193146));
#3036=DIRECTION('',(2.22751552439684E-14,0.728033202193146,0.685541870715709));
#3037=DIRECTION('',(2.22751552439684E-14,0.728033202193146,0.685541870715709));
#3038=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3039=DIRECTION('',(-2.2361711753388E-14,-0.728033202193146,-0.685541870715709));
#3040=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3041=DIRECTION('',(-1.,3.07070986088875E-14,-5.25903833505106E-17));
#3042=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3043=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3044=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3045=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3046=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3047=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3048=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3049=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3050=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3051=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3052=DIRECTION('',(-2.24092312825584E-14,-0.729581175470431,-0.683894223107042));
#3053=DIRECTION('',(-2.09968998571356E-14,-0.683894223107042,0.729581175470431));
#3054=DIRECTION('',(-2.09968998571356E-14,-0.683894223107042,0.729581175470431));
#3055=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3056=DIRECTION('',(2.09941023381369E-14,0.683894223107042,-0.729581175470431));
#3057=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3058=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3059=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3060=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3061=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3062=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3063=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3064=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3065=DIRECTION('',(-1.,3.07070986088875E-14,-5.25903833505106E-17));
#3066=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3067=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3068=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3069=DIRECTION('',(8.64195662061716E-18,4.481091423851E-13,1.));
#3070=DIRECTION('',(3.06458662689302E-14,1.,-4.481091423851E-13));
#3071=DIRECTION('',(3.06458662689302E-14,1.,-4.481091423851E-13));
#3072=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3073=DIRECTION('',(-3.07070986088875E-14,-1.,4.481091423851E-13));
#3074=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3075=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3076=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3077=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3078=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3079=DIRECTION('',(-1.,3.07070986088875E-14,8.64195660685703E-18));
#3080=DIRECTION('',(3.07070986088875E-14,1.,-7.80348008433407E-14));
#3081=DIRECTION('',(3.07070986088875E-14,1.,-7.80348008433407E-14));
#3082=DIRECTION('',(1.,-3.07070986088875E-14,5.25903833505106E-17));
#3083=DIRECTION('',(5.25903833481144E-17,-7.80348008433407E-14,-1.));
#3084=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3085=DIRECTION('',(8.64195660925325E-18,7.80348008433407E-14,1.));
#3086=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3087=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3088=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3089=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3090=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3091=DIRECTION('',(-1.,3.07070986088875E-14,8.64195660685703E-18));
#3092=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3093=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3094=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3095=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3096=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3097=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3098=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3099=DIRECTION('',(1.,-3.06458662689271E-14,3.94174692612834E-14));
#3100=DIRECTION('',(3.9417469261281E-14,-7.80348008433419E-14,-1.));
#3101=DIRECTION('',(3.06458662689272E-14,1.,-3.77035761929409E-15));
#3102=DIRECTION('',(-3.06458662689301E-14,-1.,7.79735685033833E-14));
#3103=DIRECTION('',(-5.25903833481163E-17,7.79735685033833E-14,1.));
#3104=DIRECTION('',(-3.06458662689301E-14,-1.,7.79735685033833E-14));
#3105=DIRECTION('',(-3.06458662689301E-14,-1.,7.79735685033833E-14));
#3106=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3107=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3108=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3109=DIRECTION('',(-1.,3.07070986088875E-14,-5.25903833505106E-17));
#3110=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3111=DIRECTION('',(-1.,3.07070986088875E-14,-5.25903833505106E-17));
#3112=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3113=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3114=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3115=DIRECTION('',(0.992546151641322,-3.99882624938513E-14,-0.121869343405148));
#3116=DIRECTION('',(0.992546151641322,-2.09681626107925E-14,0.121869343405147));
#3117=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3118=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3119=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3120=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3121=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3122=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3123=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3124=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3125=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3126=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3127=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3128=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3129=DIRECTION('',(-1.,3.07070986088875E-14,8.64195660685703E-18));
#3130=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3131=DIRECTION('',(-3.07070986088875E-14,-1.,-7.83346392447715E-14));
#3132=DIRECTION('',(-5.25903833529065E-17,-7.83346392447715E-14,1.));
#3133=DIRECTION('',(-5.25903833529065E-17,-7.83346392447715E-14,1.));
#3134=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3135=DIRECTION('',(-8.6419566044516E-18,7.83346392447715E-14,-1.));
#3136=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3137=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3138=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3139=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3140=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3141=DIRECTION('',(-1.,3.07070986088875E-14,8.64195660685703E-18));
#3142=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3143=DIRECTION('',(8.64195660925325E-18,7.80348008433407E-14,1.));
#3144=DIRECTION('',(1.,-3.06458662689302E-14,-8.6419566068618E-18));
#3145=DIRECTION('',(3.06458662689302E-14,1.,-7.80348008433407E-14));
#3146=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3147=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3148=DIRECTION('',(3.07070986088875E-14,1.,1.26050313977464E-13));
#3149=DIRECTION('',(5.25903833543688E-17,1.26050313977464E-13,-1.));
#3150=DIRECTION('',(5.25903833543688E-17,1.26050313977464E-13,-1.));
#3151=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3152=DIRECTION('',(8.64195660298639E-18,-1.26050313977464E-13,1.));
#3153=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3154=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3155=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3156=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3157=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3158=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3159=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3160=DIRECTION('',(2.23974109142182E-14,0.729581175472363,-0.683894223104981));
#3161=DIRECTION('',(-2.09201619496657E-14,-0.683894223104981,-0.729581175472363));
#3162=DIRECTION('',(-2.09201619496657E-14,-0.683894223104981,-0.729581175472363));
#3163=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3164=DIRECTION('',(2.10067123557928E-14,0.683894223104981,0.729581175472363));
#3165=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3166=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3167=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3168=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3169=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3170=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3171=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3172=DIRECTION('',(3.07070986088875E-14,1.,-7.7192567865154E-14));
#3173=DIRECTION('',(5.25903833481402E-17,-7.7192567865154E-14,-1.));
#3174=DIRECTION('',(5.25903833481402E-17,-7.7192567865154E-14,-1.));
#3175=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3176=DIRECTION('',(8.64195660922739E-18,7.7192567865154E-14,1.));
#3177=DIRECTION('',(2.10572934559396E-14,0.685541870716016,0.728033202192857));
#3178=DIRECTION('',(2.23472610635271E-14,0.728033202192857,-0.685541870716016));
#3179=DIRECTION('',(2.23472610635271E-14,0.728033202192857,-0.685541870716016));
#3180=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3181=DIRECTION('',(-2.23498629071813E-14,-0.728033202192857,0.685541870716016));
#3182=DIRECTION('',(3.07070986088875E-14,1.,-7.80348008433407E-14));
#3183=DIRECTION('',(1.,-3.07070986088875E-14,5.25903833505106E-17));
#3184=DIRECTION('',(-0.992546151641322,3.99882624938513E-14,0.121869343405148));
#3185=DIRECTION('',(8.64195660925325E-18,7.80348008433407E-14,1.));
#3186=DIRECTION('',(5.25903833481144E-17,-7.80348008433407E-14,-1.));
#3187=DIRECTION('',(3.07070986088875E-14,1.,-7.80348008433407E-14));
#3188=DIRECTION('',(1.,-3.07070986088875E-14,5.25903833505106E-17));
#3189=DIRECTION('',(-0.992546151641322,2.09681626107925E-14,-0.121869343405147));
#3190=DIRECTION('',(-3.06458662689301E-14,-1.,7.79735685033833E-14));
#3191=DIRECTION('',(-5.25903833481163E-17,7.79735685033833E-14,1.));
#3192=DIRECTION('',(-3.06458662689301E-14,-1.,7.79735685033833E-14));
#3193=DIRECTION('',(-3.06458662689301E-14,-1.,7.79735685033833E-14));
#3194=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3195=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3196=DIRECTION('',(1.,-3.06458662689271E-14,3.94174692612834E-14));
#3197=DIRECTION('',(3.9417469261281E-14,-7.80348008433419E-14,-1.));
#3198=DIRECTION('',(3.06458662689272E-14,1.,-3.77035761929409E-15));
#3199=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3200=DIRECTION('',(-1.,3.07070986088875E-14,-5.25903833505106E-17));
#3201=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3202=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3203=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3204=DIRECTION('',(-0.992546151641322,2.09681626107925E-14,-0.121869343405148));
#3205=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3206=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3207=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3208=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3209=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3210=DIRECTION('',(3.07070986088875E-14,1.,-7.80348008433407E-14));
#3211=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3212=DIRECTION('',(8.64195660925325E-18,7.80348008433407E-14,1.));
#3213=DIRECTION('',(1.,-3.06458662689302E-14,-8.6419566068618E-18));
#3214=DIRECTION('',(3.06458662689302E-14,1.,-7.80348008433407E-14));
#3215=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3216=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3217=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3218=DIRECTION('',(-1.,3.07070986088875E-14,-5.25903833505106E-17));
#3219=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3220=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3221=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3222=DIRECTION('',(-2.17193084430114E-14,-0.707106781186014,-0.707106781187081));
#3223=DIRECTION('',(-2.17070868708053E-14,-0.707106781187081,0.707106781186014));
#3224=DIRECTION('',(-2.17070868708053E-14,-0.707106781187081,0.707106781186014));
#3225=DIRECTION('',(2.17070868708053E-14,0.707106781187081,-0.707106781186014));
#3226=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3227=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3228=DIRECTION('',(-1.,3.06458662689302E-14,8.6419566068618E-18));
#3229=DIRECTION('',(-3.06458662689302E-14,-1.,7.80348008433407E-14));
#3230=DIRECTION('',(3.07070986088875E-14,1.,-7.80348008433407E-14));
#3231=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3232=DIRECTION('',(2.17070868707692E-14,0.707106781185904,-0.707106781187192));
#3233=DIRECTION('',(-2.1632712837424E-14,-0.707106781187192,-0.707106781185904));
#3234=DIRECTION('',(-2.1632712837424E-14,-0.707106781187192,-0.707106781185904));
#3235=DIRECTION('',(2.17193084430476E-14,0.707106781187192,0.707106781185904));
#3236=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3237=DIRECTION('',(3.07070986088875E-14,1.,-7.80348008433407E-14));
#3238=DIRECTION('',(1.,-3.07070986088875E-14,5.25903833505106E-17));
#3239=DIRECTION('',(5.25903833481144E-17,-7.80348008433407E-14,-1.));
#3240=DIRECTION('',(8.64195660925325E-18,7.80348008433407E-14,1.));
#3241=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3242=DIRECTION('',(8.64195660925325E-18,7.80348008433407E-14,1.));
#3243=DIRECTION('',(1.,-3.06458662689302E-14,-8.6419566068618E-18));
#3244=DIRECTION('',(3.06458662689302E-14,1.,-7.80348008433407E-14));
#3245=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3246=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3247=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3248=DIRECTION('',(-8.64195660925325E-18,-7.80348008433407E-14,-1.));
#3249=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3250=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3251=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3252=DIRECTION('',(-3.07070986088875E-14,-1.,7.80348008433407E-14));
#3253=DIRECTION('',(1.,-3.07070986088875E-14,-8.64195660685703E-18));
#3254=DIRECTION('',(3.07070986088875E-14,1.,2.47636273626846E-14));
#3255=DIRECTION('',(5.25903833512648E-17,2.47636273626846E-14,-1.));
#3256=DIRECTION('',(5.25903833512648E-17,2.47636273626846E-14,-1.));
#3257=DIRECTION('',(8.6419566060966E-18,-2.47636273626846E-14,1.));
#3258=DIRECTION('',(0.992546151641322,-3.3496139559342E-14,0.121869343405148));
#3259=DIRECTION('',(1.,-3.06458662689271E-14,3.94174692612834E-14));
#3260=DIRECTION('',(3.9417469261281E-14,-7.80348008433419E-14,-1.));
#3261=DIRECTION('',(-3.06458662689269E-14,-1.,-3.77035761929405E-15));
#3262=DIRECTION('',(-1.,3.06458662689301E-14,-5.25903833505058E-17));
#3263=DIRECTION('',(-5.25903833481144E-17,7.80348008433407E-14,1.));
#3264=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3265=DIRECTION('',(-0.121869343405147,-7.46234506576517E-18,0.992546151641322));
#3266=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3267=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3268=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3269=DIRECTION('',(-0.985256536015293,-0.120974291151355,-0.120974291151355));
#3270=DIRECTION('',(-6.12323399573676E-17,1.,3.05818037230783E-33));
#3271=DIRECTION('',(-0.985256536015293,0.120974291151355,-0.120974291151355));
#3272=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3273=DIRECTION('',(0.121869343405148,7.46234506576518E-18,0.992546151641322));
#3274=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3275=DIRECTION('',(0.985256536015293,0.120974291151355,-0.120974291151355));
#3276=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3277=DIRECTION('',(0.985256536015293,-0.120974291151355,-0.120974291151355));
#3278=DIRECTION('',(0.121869343405148,7.46234506576518E-18,-0.992546151641322));
#3279=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3280=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3281=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3282=DIRECTION('',(-0.985256536015293,0.120974291151355,-0.120974291151355));
#3283=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3284=DIRECTION('',(0.985256536015293,0.120974291151355,0.120974291151355));
#3285=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3286=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3287=DIRECTION('',(6.12323399573677E-17,-1.,1.54074395550979E-33));
#3288=DIRECTION('',(0.,0.,1.));
#3289=DIRECTION('',(-6.12323399573677E-17,1.,1.54074395550979E-33));
#3290=DIRECTION('',(0.,0.,-1.));
#3291=DIRECTION('',(-0.121869343405148,-0.992546151641322,0.));
#3292=DIRECTION('',(0.,0.,-1.));
#3293=DIRECTION('',(0.,0.,1.));
#3294=DIRECTION('',(0.985256536015293,-0.120974291151355,-0.120974291151355));
#3295=DIRECTION('',(0.,0.,1.));
#3296=DIRECTION('',(-0.985256536015293,0.120974291151355,-0.120974291151355));
#3297=DIRECTION('',(-0.121869343405148,0.992546151641322,0.));
#3298=DIRECTION('',(0.,0.,1.));
#3299=DIRECTION('',(0.,0.,-1.));
#3300=DIRECTION('',(-0.985256536015293,-0.120974291151355,0.120974291151355));
#3301=DIRECTION('',(0.,0.,-1.));
#3302=DIRECTION('',(-0.985256536015293,-0.120974291151355,-0.120974291151355));
#3303=DIRECTION('',(-0.121869343405147,-7.46234506576517E-18,-0.992546151641322));
#3304=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3305=DIRECTION('',(6.12323399573676E-17,-1.,3.05818037230783E-33));
#3306=DIRECTION('',(-0.985256536015293,-0.120974291151355,0.120974291151355));
#3307=DIRECTION('',(0.985256536015293,-0.120974291151355,-0.120974291151355));
#3308=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3309=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3310=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3311=DIRECTION('',(0.,0.,1.));
#3312=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3313=DIRECTION('',(0.,0.,-1.));
#3314=DIRECTION('',(0.121869343405148,0.992546151641322,0.));
#3315=DIRECTION('',(0.,0.,1.));
#3316=DIRECTION('',(0.985256536015293,-0.120974291151355,-0.120974291151355));
#3317=DIRECTION('',(0.,0.,1.));
#3318=DIRECTION('',(-0.985256536015293,0.120974291151355,-0.120974291151355));
#3319=DIRECTION('',(0.121869343405148,-0.992546151641322,0.));
#3320=DIRECTION('',(0.,0.,-1.));
#3321=DIRECTION('',(0.985256536015293,0.120974291151355,0.120974291151355));
#3322=DIRECTION('',(0.,0.,-1.));
#3323=DIRECTION('',(0.985256536015293,0.120974291151355,-0.120974291151355));
#3324=DIRECTION('',(-6.12323399573676E-17,1.,1.54074395550979E-33));
#3325=DIRECTION('',(-1.,-6.12323399573676E-17,0.));
#3326=DIRECTION('',(-6.12323399573676E-17,1.,1.54074395550979E-33));
#3327=DIRECTION('',(-1.,-6.12323399573676E-17,0.));
#3328=DIRECTION('',(-6.12323399573676E-17,1.,1.54074395550979E-33));
#3329=DIRECTION('',(-1.,-6.12323399573676E-17,0.));
#3330=DIRECTION('',(-0.985256536015293,-0.120974291151355,-0.120974291151354));
#3331=DIRECTION('',(0.121869343405148,-0.992546151641322,0.));
#3332=DIRECTION('',(-1.,-2.81709124487331E-17,6.33845530096496E-17));
#3333=DIRECTION('',(0.,-0.707106781186548,-0.707106781186547));
#3334=DIRECTION('',(-0.985256536015293,-0.120974291151355,-0.120974291151355));
#3335=DIRECTION('',(0.121869343405148,-0.992546151641322,0.));
#3336=DIRECTION('',(-0.121869343405148,0.992546151641322,0.));
#3337=DIRECTION('',(-0.992546151641322,-0.121869343405148,0.));
#3338=DIRECTION('',(0.,0.,-1.));
#3339=DIRECTION('',(-1.,0.,0.));
#3340=DIRECTION('',(0.,0.,-1.));
#3341=DIRECTION('',(-1.,0.,0.));
#3342=DIRECTION('',(0.,0.,-1.));
#3343=DIRECTION('',(-1.,0.,0.));
#3344=DIRECTION('',(6.12323399573676E-17,-1.,1.54074395550979E-33));
#3345=DIRECTION('',(1.,6.12323399573676E-17,0.));
#3346=DIRECTION('',(6.12323399573676E-17,-1.,1.54074395550979E-33));
#3347=DIRECTION('',(1.,6.12323399573676E-17,0.));
#3348=DIRECTION('',(6.12323399573676E-17,-1.,1.54074395550979E-33));
#3349=DIRECTION('',(1.,6.12323399573676E-17,0.));
#3350=DIRECTION('',(0.,0.,1.));
#3351=DIRECTION('',(1.,0.,0.));
#3352=DIRECTION('',(0.,0.,1.));
#3353=DIRECTION('',(1.,0.,0.));
#3354=DIRECTION('',(0.,0.,1.));
#3355=DIRECTION('',(1.,0.,0.));
#3356=DIRECTION('',(-0.121869343405148,-0.992546151641322,0.));
#3357=DIRECTION('',(0.992546151641322,-0.121869343405148,0.));
#3358=DIRECTION('',(0.985256536015293,-0.120974291151353,0.120974291151353));
#3359=DIRECTION('',(0.121869343405146,0.992546151641322,0.));
#3360=DIRECTION('',(-0.985256536015293,0.120974291151355,-0.120974291151354));
#3361=DIRECTION('',(-0.121869343405148,-0.992546151641322,0.));
#3362=DIRECTION('',(-1.,2.81709124487331E-17,6.33845530096496E-17));
#3363=DIRECTION('',(0.,0.707106781186548,-0.707106781186547));
#3364=DIRECTION('',(0.,0.,1.));
#3365=DIRECTION('',(1.,0.,0.));
#3366=DIRECTION('',(0.,0.,1.));
#3367=DIRECTION('',(1.,0.,0.));
#3368=DIRECTION('',(0.,0.,1.));
#3369=DIRECTION('',(1.,0.,0.));
#3370=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3371=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3372=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3373=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3374=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3375=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3376=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3377=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3378=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3379=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3380=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3381=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3382=DIRECTION('',(0.,0.,-1.));
#3383=DIRECTION('',(-1.,0.,0.));
#3384=DIRECTION('',(0.,0.,-1.));
#3385=DIRECTION('',(-1.,0.,0.));
#3386=DIRECTION('',(0.,0.,-1.));
#3387=DIRECTION('',(-1.,0.,0.));
#3388=DIRECTION('',(0.985256536015293,0.120974291151355,-0.120974291151355));
#3389=DIRECTION('',(-0.121869343405148,0.,-0.992546151641322));
#3390=DIRECTION('',(0.985256536015293,0.120974291151354,-0.120974291151355));
#3391=DIRECTION('',(-0.121869343405147,0.,-0.992546151641322));
#3392=DIRECTION('',(0.,0.,1.));
#3393=DIRECTION('',(1.,0.,0.));
#3394=DIRECTION('',(0.,0.,1.));
#3395=DIRECTION('',(1.,0.,0.));
#3396=DIRECTION('',(0.985256536015293,-0.120974291151355,-0.120974291151356));
#3397=DIRECTION('',(-0.121869343405149,0.,-0.992546151641322));
#3398=DIRECTION('',(0.985256536015293,-0.120974291151355,-0.120974291151354));
#3399=DIRECTION('',(0.121869343405148,0.992546151641322,0.));
#3400=DIRECTION('',(1.,-2.81709124487331E-17,6.33845530096496E-17));
#3401=DIRECTION('',(0.,-0.707106781186548,-0.707106781186547));
#3402=DIRECTION('',(0.985256536015293,0.120974291151355,0.120974291151355));
#3403=DIRECTION('',(0.121869343405148,0.,-0.992546151641322));
#3404=DIRECTION('',(0.985256536015293,0.120974291151355,0.120974291151355));
#3405=DIRECTION('',(0.121869343405148,0.,-0.992546151641322));
#3406=DIRECTION('',(0.,0.,1.));
#3407=DIRECTION('',(1.,0.,0.));
#3408=DIRECTION('',(0.,0.,1.));
#3409=DIRECTION('',(1.,0.,0.));
#3410=DIRECTION('',(0.985256536015293,0.120974291151356,-0.120974291151357));
#3411=DIRECTION('',(-0.12186934340515,0.,-0.992546151641322));
#3412=DIRECTION('',(-0.985256536015293,-0.120974291151355,0.120974291151354));
#3413=DIRECTION('',(0.121869343405148,-0.992546151641322,0.));
#3414=DIRECTION('',(-1.,-2.81709124487331E-17,-6.33845530096496E-17));
#3415=DIRECTION('',(0.,-0.707106781186548,0.707106781186547));
#3416=DIRECTION('',(-0.985256536015293,0.120974291151355,-0.120974291151355));
#3417=DIRECTION('',(-0.121869343405148,0.,0.992546151641322));
#3418=DIRECTION('',(0.985256536015293,-0.120974291151355,0.120974291151355));
#3419=DIRECTION('',(0.121869343405147,0.,-0.992546151641322));
#3420=DIRECTION('',(0.,0.,1.));
#3421=DIRECTION('',(1.,0.,0.));
#3422=DIRECTION('',(0.,0.,1.));
#3423=DIRECTION('',(1.,0.,0.));
#3424=DIRECTION('',(-0.985256536015293,0.120974291151355,0.120974291151355));
#3425=DIRECTION('',(0.121869343405147,0.,0.992546151641322));
#3426=DIRECTION('',(0.985256536015293,-0.120974291151355,-0.120974291151355));
#3427=DIRECTION('',(-0.121869343405148,0.,-0.992546151641322));
#3428=DIRECTION('',(0.,0.,1.));
#3429=DIRECTION('',(1.,0.,0.));
#3430=DIRECTION('',(-1.,-2.41040364583419E-30,-3.93648788779328E-14));
#3431=DIRECTION('',(-3.93648788779329E-14,0.,1.));
#3432=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#3433=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3434=DIRECTION('',(-6.12323399573677E-17,-3.74939945665464E-33,-1.));
#3435=DIRECTION('',(2.60184365423793E-18,-0.728033202192911,-0.685541870715959));
#3436=DIRECTION('',(-6.12323399573676E-17,-8.42232978186676E-16,-1.));
#3437=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3438=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3439=DIRECTION('',(-8.65504061270634E-17,0.683894223104924,-0.729581175472416));
#3440=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3441=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3442=DIRECTION('',(-6.12323399573552E-17,-2.04085114820805E-13,-1.));
#3443=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3444=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573677E-17));
#3445=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3446=DIRECTION('',(6.12323399573581E-17,1.56369440088112E-13,1.));
#3447=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573677E-17));
#3448=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3449=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3450=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3451=DIRECTION('',(1.,0.,0.));
#3452=DIRECTION('',(0.,0.,-1.));
#3453=DIRECTION('',(0.,0.,-1.));
#3454=DIRECTION('',(1.,0.,0.));
#3455=DIRECTION('',(0.,0.,-1.));
#3456=DIRECTION('',(1.,0.,0.));
#3457=DIRECTION('',(0.,0.,-1.));
#3458=DIRECTION('',(0.,-1.56369440088112E-13,-1.));
#3459=DIRECTION('',(1.,0.,0.));
#3460=DIRECTION('',(0.,0.,-1.));
#3461=DIRECTION('',(0.,1.,-3.74939945665464E-33));
#3462=DIRECTION('',(0.,2.04085114820805E-13,1.));
#3463=DIRECTION('',(1.,0.,0.));
#3464=DIRECTION('',(0.,0.,-1.));
#3465=DIRECTION('',(1.55293500224825E-34,-0.683894223104924,0.729581175472416));
#3466=DIRECTION('',(1.,0.,0.));
#3467=DIRECTION('',(0.,0.,-1.));
#3468=DIRECTION('',(0.,8.42232978186676E-16,1.));
#3469=DIRECTION('',(0.,0.728033202192911,0.685541870715959));
#3470=DIRECTION('',(0.,0.,1.));
#3471=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3472=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3473=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3474=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3475=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3476=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3477=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3478=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3479=DIRECTION('',(0.0871557427477275,6.09993324172806E-17,0.996194698091739));
#3480=DIRECTION('',(0.99619469809174,0.,-0.0871557427477275));
#3481=DIRECTION('',(0.99619469809174,6.09993324172806E-17,-0.0871557427477275));
#3482=DIRECTION('',(-0.99619469809174,-6.09993324172806E-17,0.0871557427477275));
#3483=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3484=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3485=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3486=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3487=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3488=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3489=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3490=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3491=DIRECTION('',(0.997564050259816,4.27135211453488E-18,0.0697564737442463));
#3492=DIRECTION('',(0.0697564737442463,0.,-0.997564050259816));
#3493=DIRECTION('',(0.0697564737442463,4.27135211453488E-18,-0.997564050259816));
#3494=DIRECTION('',(-0.0697564737442463,-4.27135211453488E-18,0.997564050259816));
#3495=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3496=DIRECTION('',(0.0697564737432792,-6.10831810547618E-17,-0.997564050259883));
#3497=DIRECTION('',(-0.997564050259884,0.,-0.0697564737432792));
#3498=DIRECTION('',(-0.997564050259884,-6.10831810547618E-17,-0.0697564737432792));
#3499=DIRECTION('',(0.997564050259884,6.10831810547618E-17,0.0697564737432792));
#3500=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3501=DIRECTION('',(-0.997564050259811,-4.271352114539E-18,-0.0697564737443136));
#3502=DIRECTION('',(-0.0697564737443137,0.,0.997564050259811));
#3503=DIRECTION('',(-0.0697564737443137,-4.271352114539E-18,0.997564050259811));
#3504=DIRECTION('',(0.0697564737443137,4.271352114539E-18,-0.997564050259811));
#3505=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3506=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3507=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3508=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3509=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3510=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3511=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3512=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3513=DIRECTION('',(-0.0871557427474172,-6.09993324172823E-17,-0.996194698091767));
#3514=DIRECTION('',(-0.996194698091767,0.,0.0871557427474172));
#3515=DIRECTION('',(-0.996194698091767,-6.09993324172823E-17,0.0871557427474172));
#3516=DIRECTION('',(0.996194698091767,6.09993324172823E-17,-0.0871557427474172));
#3517=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3518=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3519=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3520=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3521=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3522=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3523=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3524=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3525=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3526=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3527=DIRECTION('',(-8.65565094196167E-17,0.685541870715959,-0.728033202192911));
#3528=DIRECTION('',(0.,0.728033202192911,0.685541870715959));
#3529=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3530=DIRECTION('',(-6.12323399573677E-17,1.,-8.42232978186676E-16));
#3531=DIRECTION('',(0.,8.42232978186676E-16,1.));
#3532=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3533=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3534=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3535=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3536=DIRECTION('',(-2.79751899898233E-18,0.729581175472416,0.683894223104924));
#3537=DIRECTION('',(0.,-0.683894223104924,0.729581175472416));
#3538=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3539=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3540=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3541=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3542=DIRECTION('',(-6.12323399573801E-17,1.,-2.04085114820805E-13));
#3543=DIRECTION('',(0.,2.04085114820805E-13,1.));
#3544=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3545=DIRECTION('',(-6.12323399573677E-17,-3.74939945665464E-33,-1.));
#3546=DIRECTION('',(-1.,0.,6.12323399573677E-17));
#3547=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3548=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3549=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3550=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3551=DIRECTION('',(6.12323399573772E-17,-1.,1.56369440088112E-13));
#3552=DIRECTION('',(0.,-1.56369440088112E-13,-1.));
#3553=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3554=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3555=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3556=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3557=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3558=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3559=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3560=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3561=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3562=DIRECTION('',(1.,0.,0.));
#3563=DIRECTION('',(0.,0.,-1.));
#3564=DIRECTION('',(0.,0.,1.));
#3565=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3566=DIRECTION('',(0.,0.,-1.));
#3567=DIRECTION('',(0.,0.728033202193092,-0.685541870715766));
#3568=DIRECTION('',(0.,0.,-1.));
#3569=DIRECTION('',(1.,0.,0.));
#3570=DIRECTION('',(0.,0.,-1.));
#3571=DIRECTION('',(0.,-0.683894223107099,-0.729581175470378));
#3572=DIRECTION('',(1.,0.,0.));
#3573=DIRECTION('',(0.,0.,-1.));
#3574=DIRECTION('',(0.,0.,-1.));
#3575=DIRECTION('',(0.,1.,3.7007434154176E-13));
#3576=DIRECTION('',(1.,0.,0.));
#3577=DIRECTION('',(0.,0.,-1.));
#3578=DIRECTION('',(0.,0.,1.));
#3579=DIRECTION('',(1.,0.,0.));
#3580=DIRECTION('',(0.,0.,-1.));
#3581=DIRECTION('',(1.,0.,0.));
#3582=DIRECTION('',(0.,0.,-1.));
#3583=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3584=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3585=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3586=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3587=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3588=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3589=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3590=DIRECTION('',(0.0871557427477275,6.09993324172806E-17,0.996194698091739));
#3591=DIRECTION('',(0.99619469809174,0.,-0.0871557427477275));
#3592=DIRECTION('',(-0.99619469809174,-6.09993324172806E-17,0.0871557427477275));
#3593=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3594=DIRECTION('',(0.99619469809174,6.09993324172806E-17,-0.0871557427477275));
#3595=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3596=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3597=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3598=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3599=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3600=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3601=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3602=DIRECTION('',(0.997564050259816,4.27135211453488E-18,0.0697564737442463));
#3603=DIRECTION('',(0.0697564737442463,0.,-0.997564050259816));
#3604=DIRECTION('',(-0.0697564737442463,-4.27135211453488E-18,0.997564050259816));
#3605=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3606=DIRECTION('',(0.0697564737442463,4.27135211453488E-18,-0.997564050259816));
#3607=DIRECTION('',(0.0697564737432792,-6.10831810547618E-17,-0.997564050259883));
#3608=DIRECTION('',(-0.997564050259884,0.,-0.0697564737432792));
#3609=DIRECTION('',(0.997564050259884,6.10831810547618E-17,0.0697564737432792));
#3610=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3611=DIRECTION('',(-0.997564050259884,-6.10831810547618E-17,-0.0697564737432792));
#3612=DIRECTION('',(-0.997564050259811,-4.271352114539E-18,-0.0697564737443136));
#3613=DIRECTION('',(-0.0697564737443137,0.,0.997564050259811));
#3614=DIRECTION('',(0.0697564737443137,4.271352114539E-18,-0.997564050259811));
#3615=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3616=DIRECTION('',(-0.0697564737443137,-4.271352114539E-18,0.997564050259811));
#3617=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3618=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3619=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3620=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3621=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3622=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3623=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3624=DIRECTION('',(-0.0871557427474172,-6.09993324172823E-17,-0.996194698091767));
#3625=DIRECTION('',(-0.996194698091767,0.,0.0871557427474172));
#3626=DIRECTION('',(0.996194698091767,6.09993324172823E-17,-0.0871557427474172));
#3627=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3628=DIRECTION('',(-0.996194698091767,-6.09993324172823E-17,0.0871557427474172));
#3629=DIRECTION('',(-1.,-2.41040364583419E-30,-3.93648788779328E-14));
#3630=DIRECTION('',(-3.93648788779329E-14,0.,1.));
#3631=DIRECTION('',(-6.12323399573677E-17,-3.74939945665464E-33,-1.));
#3632=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3633=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3634=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573677E-17));
#3635=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3636=DIRECTION('',(-6.12323399573677E-17,-3.74939945665464E-33,-1.));
#3637=DIRECTION('',(1.,6.12323399573677E-17,-6.12323399573677E-17));
#3638=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3639=DIRECTION('',(6.1232339957345E-17,-1.,-3.7007434154176E-13));
#3640=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#3641=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3642=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3643=DIRECTION('',(2.79751899872436E-18,0.683894223107099,0.729581175470378));
#3644=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3645=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3646=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#3647=DIRECTION('',(8.6556509419616E-17,-0.728033202193092,0.685541870715766));
#3648=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#3649=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3650=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3651=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3652=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3653=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3654=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3655=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3656=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3657=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3658=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3659=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3660=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3661=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3662=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3663=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3664=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3665=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3666=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3667=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3668=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3669=DIRECTION('',(-6.12323399573903E-17,3.7007434154176E-13,-1.));
#3670=DIRECTION('',(0.,1.,3.7007434154176E-13));
#3671=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3672=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3673=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3674=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3675=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3676=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3677=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3678=DIRECTION('',(8.65504061270718E-17,-0.729581175470378,0.683894223107099));
#3679=DIRECTION('',(0.,-0.683894223107099,-0.729581175470378));
#3680=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3681=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3682=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3683=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3684=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3685=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3686=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3687=DIRECTION('',(-2.60184365426085E-18,-0.685541870715766,-0.728033202193092));
#3688=DIRECTION('',(0.,0.728033202193092,-0.685541870715766));
#3689=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3690=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3691=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3692=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3693=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3694=DIRECTION('',(1.,0.,0.));
#3695=DIRECTION('',(0.,0.,-1.));
#3696=DIRECTION('',(0.,1.02798428206025E-13,1.));
#3697=DIRECTION('',(1.,0.,0.));
#3698=DIRECTION('',(0.,0.,-1.));
#3699=DIRECTION('',(0.,1.,-3.74939945665464E-33));
#3700=DIRECTION('',(0.,0.,1.));
#3701=DIRECTION('',(0.,-0.707106781187136,0.707106781185959));
#3702=DIRECTION('',(0.,-1.,3.74939945665464E-33));
#3703=DIRECTION('',(0.,-0.707106781187136,-0.707106781185959));
#3704=DIRECTION('',(0.,0.,-1.));
#3705=DIRECTION('',(0.,1.,-3.74939945665464E-33));
#3706=DIRECTION('',(1.,0.,0.));
#3707=DIRECTION('',(0.,0.,-1.));
#3708=DIRECTION('',(0.,0.,-1.));
#3709=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3710=DIRECTION('',(-1.,-2.41040364583419E-30,-3.93648788779328E-14));
#3711=DIRECTION('',(-3.93648788779329E-14,0.,1.));
#3712=DIRECTION('',(-6.12323399573614E-17,-1.02798428206025E-13,-1.));
#3713=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3714=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#3715=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3716=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3717=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3718=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#3719=DIRECTION('',(-7.21006541420361E-29,0.707106781187136,0.707106781185959));
#3720=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3721=DIRECTION('',(-8.65956056235493E-17,0.707106781187136,-0.707106781185959));
#3722=DIRECTION('',(-6.12323399573677E-17,-3.74939945665464E-33,-1.));
#3723=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3724=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3725=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3726=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3727=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3728=DIRECTION('',(-6.1232339957374E-17,1.,-1.02798428206025E-13));
#3729=DIRECTION('',(0.,1.02798428206025E-13,1.));
#3730=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3731=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3732=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3733=DIRECTION('',(0.0871557427475472,-6.09993324172816E-17,-0.996194698091755));
#3734=DIRECTION('',(-0.996194698091755,0.,-0.0871557427475472));
#3735=DIRECTION('',(0.996194698091755,9.02047269599383E-15,0.0871557427475472));
#3736=DIRECTION('',(-0.996194698091755,-6.09993324172816E-17,-0.0871557427475472));
#3737=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3738=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3739=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3740=DIRECTION('',(-6.1232339957374E-17,1.,-1.02798428206025E-13));
#3741=DIRECTION('',(0.,1.02798428206025E-13,1.));
#3742=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3743=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3744=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3745=DIRECTION('',(0.997564050259824,-4.27135211452797E-18,-0.0697564737441334));
#3746=DIRECTION('',(-0.0697564737441334,0.,-0.997564050259824));
#3747=DIRECTION('',(0.0697564737441334,1.02552287753661E-13,0.997564050259824));
#3748=DIRECTION('',(-0.0697564737441334,-4.27135211452797E-18,-0.997564050259824));
#3749=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3750=DIRECTION('',(0.0697564737447155,6.10831810547556E-17,0.997564050259783));
#3751=DIRECTION('',(0.997564050259783,0.,-0.0697564737447155));
#3752=DIRECTION('',(-0.997564050259783,7.10977267709687E-15,0.0697564737447155));
#3753=DIRECTION('',(0.997564050259783,6.10831810547556E-17,-0.0697564737447155));
#3754=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3755=DIRECTION('',(-0.997564050259841,4.27135211451257E-18,0.0697564737438819));
#3756=DIRECTION('',(0.0697564737438819,0.,0.997564050259841));
#3757=DIRECTION('',(-0.0697564737438819,-1.02552287753663E-13,-0.997564050259841));
#3758=DIRECTION('',(0.0697564737438819,4.27135211451257E-18,0.997564050259841));
#3759=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3760=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3761=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3762=DIRECTION('',(-6.1232339957374E-17,1.,-1.02798428206025E-13));
#3763=DIRECTION('',(0.,1.02798428206025E-13,1.));
#3764=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3765=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3766=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3767=DIRECTION('',(-0.0871557427476885,6.09993324172808E-17,0.996194698091743));
#3768=DIRECTION('',(0.996194698091743,0.,0.0871557427476885));
#3769=DIRECTION('',(-0.996194698091743,-9.02047269600836E-15,-0.0871557427476885));
#3770=DIRECTION('',(0.996194698091743,6.09993324172808E-17,0.0871557427476885));
#3771=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3772=DIRECTION('',(0.,-1.,6.12323399573676E-17));
#3773=DIRECTION('',(0.,-6.12323399573677E-17,-1.));
#3774=DIRECTION('',(-6.1232339957374E-17,1.,-1.02798428206025E-13));
#3775=DIRECTION('',(0.,1.02798428206025E-13,1.));
#3776=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3777=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3778=DIRECTION('',(-6.1232339957374E-17,1.,-1.02798428206025E-13));
#3779=DIRECTION('',(0.,1.02798428206025E-13,1.));
#3780=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3781=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3782=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3783=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3784=DIRECTION('',(-6.12323399573677E-17,-3.74939945665464E-33,-1.));
#3785=DIRECTION('',(-1.,0.,6.12323399573677E-17));
#3786=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3787=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#3788=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#3789=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3790=DIRECTION('',(7.20994982395715E-29,0.707106781185959,0.707106781187136));
#3791=DIRECTION('',(0.,-0.707106781187136,0.707106781185959));
#3792=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3793=DIRECTION('',(6.12323399573677E-17,3.74939945665464E-33,1.));
#3794=DIRECTION('',(1.,0.,-6.12323399573677E-17));
#3795=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3796=DIRECTION('',(8.65956056235493E-17,-0.707106781185959,0.707106781187136));
#3797=DIRECTION('',(0.,-0.707106781187136,-0.707106781185959));
#3798=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3799=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3800=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3801=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3802=DIRECTION('',(-6.12323399573677E-17,-3.74939945665464E-33,-1.));
#3803=DIRECTION('',(-1.,0.,6.12323399573677E-17));
#3804=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3805=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3806=DIRECTION('',(6.12323399573677E-17,0.,1.));
#3807=DIRECTION('',(-1.,-6.12323399573677E-17,6.12323399573677E-17));
#3808=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#3809=DIRECTION('',(1.,6.12323399573677E-17,0.));
#3810=DIRECTION('',(5.25903833481144E-17,-7.80348008433407E-14,-1.));
#3811=DIRECTION('',(-1.,3.06458662689302E-14,-5.25903833505059E-17));
#3812=CARTESIAN_POINT('',(0.,0.,0.));
#3813=CARTESIAN_POINT('',(-0.912406125164449,-0.801247382519581,0.427192745099319));
#3814=CARTESIAN_POINT('',(1.09192562062343E-14,-0.801247382519591,0.649999999999983));
#3815=CARTESIAN_POINT('',(0.110000000000002,-0.801247382519596,0.636493698300664));
#3816=CARTESIAN_POINT('',(4.90622521163908E-17,-0.801247382519594,0.649999999999984));
#3817=CARTESIAN_POINT('',(0.110000000000002,-0.801247382519612,0.427192745099319));
#3818=CARTESIAN_POINT('',(0.110000000000002,-0.801247382519616,0.427192745099319));
#3819=CARTESIAN_POINT('',(-0.912406125164449,-0.801247382519585,0.427192745099319));
#3820=CARTESIAN_POINT('',(-1.0759351309203E-13,-0.801247382519613,0.427192745099319));
#3821=CARTESIAN_POINT('',(-1.0752575045625E-13,-0.801247382519694,-0.659631838555847));
#3822=CARTESIAN_POINT('',(-1.07607065619186E-13,-0.801247382519592,0.640368161444232));
#3823=CARTESIAN_POINT('',(-0.0500000000001021,-0.801247382519591,0.640368161444232));
#3824=CARTESIAN_POINT('',(-0.000914581405605651,-0.801247382519591,0.649887703523687));
#3825=CARTESIAN_POINT('',(-0.600000000000017,-0.801247382519579,0.576329263458239));
#3826=CARTESIAN_POINT('',(-0.91240612516444,-0.501247382519411,1.60036816144411));
#3827=CARTESIAN_POINT('',(-0.0500000000000929,-0.501247382519513,0.640368161444208));
#3828=CARTESIAN_POINT('',(-9.84184522073717E-14,-0.501247382519514,0.640368161444209));
#3829=CARTESIAN_POINT('',(-0.000914581405625512,-0.501247382519509,0.649887703523813));
#3830=CARTESIAN_POINT('',(-9.83371370444353E-14,-0.501247382519616,-0.65963183855587));
#3831=CARTESIAN_POINT('',(-9.84048996802156E-14,-0.501247382519525,0.498368161444066));
#3832=CARTESIAN_POINT('',(-0.91240612516444,-0.501247382519497,0.498368161444066));
#3833=CARTESIAN_POINT('',(0.110000000000012,-0.501247382519529,0.498368161444066));
#3834=CARTESIAN_POINT('',(0.110000000000012,-0.501247382519443,1.60036816144411));
#3835=CARTESIAN_POINT('',(0.109999999999845,-0.501247382519518,0.636493698300683));
#3836=CARTESIAN_POINT('',(2.28260421916379E-14,-0.501247382519513,0.649999999999981));
#3837=CARTESIAN_POINT('',(3.06925501291757E-17,-0.501247382519516,0.649999999999984));
#3838=CARTESIAN_POINT('',(-0.60000000000001,-0.501247382519501,0.57632926345824));
#3839=CARTESIAN_POINT('',(-0.91240612516444,-0.501247382519497,0.498368161444066));
#3840=CARTESIAN_POINT('',(-9.05444339296957E-14,-0.244401049341879,0.740223773526935));
#3841=CARTESIAN_POINT('',(-1.0480169249788E-13,-0.710457858142708,0.30136816144408));
#3842=CARTESIAN_POINT('',(-0.912406125164446,-0.71045785814268,0.30136816144408));
#3843=CARTESIAN_POINT('',(0.110000000000005,-0.710457858142711,0.30136816144408));
#3844=CARTESIAN_POINT('',(0.110000000000012,-0.501247382519529,0.498368161444066));
#3845=CARTESIAN_POINT('',(-0.912406125164446,-0.71045785814268,0.30136816144408));
#3846=CARTESIAN_POINT('',(-1.04747482389256E-13,-0.710457858142783,-0.659631838555854));
#3847=CARTESIAN_POINT('',(-1.0480169249788E-13,-0.710457858142718,0.172638676780388));
#3848=CARTESIAN_POINT('',(-0.912406125164446,-0.71045785814269,0.172638676780388));
#3849=CARTESIAN_POINT('',(0.110000000000005,-0.710457858142721,0.172638676780388));
#3850=CARTESIAN_POINT('',(0.110000000000005,-0.710457858142711,0.30136816144408));
#3851=CARTESIAN_POINT('',(-0.912406125164445,-0.660457858142695,0.172638676780384));
#3852=CARTESIAN_POINT('',(-1.03270256929244E-13,-0.660457858142724,0.172638676780384));
#3853=CARTESIAN_POINT('',(-1.04381564156042E-13,-0.696936916916241,0.138443965625035));
#3854=CARTESIAN_POINT('',(-0.912406125164446,-0.696936916916213,0.138443965625035));
#3855=CARTESIAN_POINT('',(0.110000000000006,-0.696936916916244,0.138443965625035));
#3856=CARTESIAN_POINT('',(0.110000000000007,-0.660457858142727,0.172638676780384));
#3857=CARTESIAN_POINT('',(-0.912406125164446,-0.69693691691619,0.138443965625057));
#3858=CARTESIAN_POINT('',(-6.67190911893267E-14,0.529439593213833,-1.16985954915688));
#3859=CARTESIAN_POINT('',(-1.03229599347776E-13,-0.659768323745926,0.0987923572629272));
#3860=CARTESIAN_POINT('',(-0.912406125164445,-0.659768323745898,0.0987923572629273));
#3861=CARTESIAN_POINT('',(0.110000000000007,-0.65976832374593,0.0987923572629272));
#3862=CARTESIAN_POINT('',(0.110000000000006,-0.696936916916221,0.138443965625057));
#3863=CARTESIAN_POINT('',(-0.912406125164443,-0.606456176415831,0.135216833120367));
#3864=CARTESIAN_POINT('',(-1.01616848616204E-13,-0.606456176415859,0.135216833120367));
#3865=CARTESIAN_POINT('',(-1.02823023533094E-13,-0.64624738251962,0.0843681614441073));
#3866=CARTESIAN_POINT('',(-0.912406125164444,-0.646247382519592,0.0843681614441073));
#3867=CARTESIAN_POINT('',(0.110000000000007,-0.646247382519623,0.0843681614441073));
#3868=CARTESIAN_POINT('',(0.110000000000008,-0.606456176415862,0.135216833120367));
#3869=CARTESIAN_POINT('',(-0.912406125164444,-0.646247382519591,0.0843681614441077));
#3870=CARTESIAN_POINT('',(-1.02782365951626E-13,-0.646247382519678,-0.659631838555859));
#3871=CARTESIAN_POINT('',(-1.02795918478782E-13,-0.646247382519662,-0.459631838555881));
#3872=CARTESIAN_POINT('',(-0.912406125164444,-0.646247382519634,-0.459631838555881));
#3873=CARTESIAN_POINT('',(0.110000000000007,-0.646247382519665,-0.459631838555881));
#3874=CARTESIAN_POINT('',(0.110000000000007,-0.646247382519623,0.0843681614441077));
#3875=CARTESIAN_POINT('',(-0.912406125164444,-0.646247382519634,-0.459631838555881));
#3876=CARTESIAN_POINT('',(-5.00765878416742E-14,1.07375261748043,-0.459631838556652));
#3877=CARTESIAN_POINT('',(-1.07390225184689E-13,-0.796247382519645,-0.459631838555814));
#3878=CARTESIAN_POINT('',(-0.912406125164449,-0.796247382519617,-0.459631838555814));
#3879=CARTESIAN_POINT('',(0.110000000000003,-0.796247382519649,-0.459631838555814));
#3880=CARTESIAN_POINT('',(0.110000000000007,-0.646247382519665,-0.459631838555881));
#3881=CARTESIAN_POINT('',(-0.912406125164449,-0.796247382519611,-0.379631838555901));
#3882=CARTESIAN_POINT('',(-1.07403777711845E-13,-0.796247382519639,-0.379631838555901));
#3883=CARTESIAN_POINT('',(-1.09856785127094E-13,-0.876247382519552,-0.379631838555894));
#3884=CARTESIAN_POINT('',(-0.912406125164451,-0.876247382519524,-0.379631838555894));
#3885=CARTESIAN_POINT('',(0.11,-0.876247382519556,-0.379631838555894));
#3886=CARTESIAN_POINT('',(0.110000000000003,-0.796247382519642,-0.379631838555901));
#3887=CARTESIAN_POINT('',(-0.912406125164451,-0.87624738251958,-0.379631838555894));
#3888=CARTESIAN_POINT('',(-1.09829680072782E-13,-0.87624738251963,-0.659631838555841));
#3889=CARTESIAN_POINT('',(-1.09883890181406E-13,-0.876247382519553,0.330368161443817));
#3890=CARTESIAN_POINT('',(-0.912406125164451,-0.876247382519524,0.330368161443817));
#3891=CARTESIAN_POINT('',(0.11,-0.876247382519556,0.330368161443817));
#3892=CARTESIAN_POINT('',(0.11,-0.876247382519611,-0.379631838555894));
#3893=CARTESIAN_POINT('',(-0.91240612516445,-0.826247382519197,0.330368161443813));
#3894=CARTESIAN_POINT('',(-1.0835245461277E-13,-0.826247382519225,0.330368161443813));
#3895=CARTESIAN_POINT('',(-1.08745477900296E-13,-0.838747382519445,0.378780453271687));
#3896=CARTESIAN_POINT('',(-0.91240612516445,-0.838747382519417,0.378780453271687));
#3897=CARTESIAN_POINT('',(0.109999999999845,-0.838747382519471,0.378780453271775));
#3898=CARTESIAN_POINT('',(0.110000000000002,-0.826247382519228,0.330368161443813));
#3899=CARTESIAN_POINT('',(-0.91240612516445,-0.851247382519575,0.427192745099323));
#3900=CARTESIAN_POINT('',(-1.09124948660666E-13,-0.851247382519603,0.427192745099323));
#3901=CARTESIAN_POINT('',(0.110000000000001,-0.851247382519607,0.427192745099323));
#3902=CARTESIAN_POINT('',(0.109999999999893,1.07375261748043,0.640368161444085));
#3903=CARTESIAN_POINT('',(0.109999999999828,-1.07500000000005,0.636493698300686));
#3904=CARTESIAN_POINT('',(-0.0500000000000446,1.07375261748044,0.640368161444085));
#3905=CARTESIAN_POINT('',(-0.000914581405577245,1.07375261748044,0.64988770352369));
#3906=CARTESIAN_POINT('',(-5.01443504774546E-14,1.07375261748044,0.640368161444085));
#3907=CARTESIAN_POINT('',(-5.00630353145182E-14,1.07375261748034,-0.659631838555993));
#3908=CARTESIAN_POINT('',(-0.9124061251644,0.798752617480446,1.60036816144407));
#3909=CARTESIAN_POINT('',(-0.050000000000053,0.798752617480344,0.640368161444107));
#3910=CARTESIAN_POINT('',(-5.85740223685294E-14,0.798752617480343,0.640368161444107));
#3911=CARTESIAN_POINT('',(-0.000914581405581372,0.798752617480343,0.64988770352369));
#3912=CARTESIAN_POINT('',(-5.8492707205593E-14,0.798752617480241,-0.659631838555972));
#3913=CARTESIAN_POINT('',(-5.85604698413733E-14,0.798752617480326,0.427192745099194));
#3914=CARTESIAN_POINT('',(-0.9124061251644,0.798752617480354,0.427192745099194));
#3915=CARTESIAN_POINT('',(0.110000000000052,0.798752617480323,0.427192745099194));
#3916=CARTESIAN_POINT('',(0.110000000000052,0.798752617480414,1.60036816144407));
#3917=CARTESIAN_POINT('',(0.110000000000052,0.798752617480339,0.636493698300658));
#3918=CARTESIAN_POINT('',(7.44221147950344E-14,0.798752617480343,0.649999999999975));
#3919=CARTESIAN_POINT('',(-4.89094918153935E-17,0.798752617480341,0.649999999999984));
#3920=CARTESIAN_POINT('',(-0.599999999999983,0.798752617480356,0.576329263458243));
#3921=CARTESIAN_POINT('',(-0.912406125164398,0.848752617480348,0.42719274509919));
#3922=CARTESIAN_POINT('',(-5.70290342727375E-14,0.84875261748032,0.42719274509919));
#3923=CARTESIAN_POINT('',(-5.74085050331075E-14,0.836252617480228,0.378780453271627));
#3924=CARTESIAN_POINT('',(-0.912406125164399,0.836252617480256,0.378780453271627));
#3925=CARTESIAN_POINT('',(0.110000000000053,0.836252617480225,0.378780453271627));
#3926=CARTESIAN_POINT('',(0.110000000000053,0.848752617480317,0.42719274509919));
#3927=CARTESIAN_POINT('',(-0.912406125164399,0.823752617480066,0.330368161443684));
#3928=CARTESIAN_POINT('',(-5.77744232663213E-14,0.823752617480038,0.330368161443684));
#3929=CARTESIAN_POINT('',(-5.62429876976855E-14,0.873752617480403,0.330368161443688));
#3930=CARTESIAN_POINT('',(-0.912406125164397,0.873752617480431,0.330368161443688));
#3931=CARTESIAN_POINT('',(0.110000000000054,0.8737526174804,0.330368161443688));
#3932=CARTESIAN_POINT('',(0.110000000000052,0.823752617480035,0.330368161443684));
#3933=CARTESIAN_POINT('',(-0.912406125164397,0.873752617480394,0.330368161443688));
#3934=CARTESIAN_POINT('',(-5.61887775890613E-14,0.873752617480443,-0.659631838556009));
#3935=CARTESIAN_POINT('',(-5.62158826433734E-14,0.873752617480421,-0.379631838556031));
#3936=CARTESIAN_POINT('',(-0.912406125164397,0.873752617480449,-0.379631838556031));
#3937=CARTESIAN_POINT('',(0.110000000000054,0.873752617480418,-0.379631838556031));
#3938=CARTESIAN_POINT('',(0.110000000000054,0.873752617480362,0.330368161443688));
#3939=CARTESIAN_POINT('',(-0.9124061251644,0.79375261748048,-0.379631838556025));
#3940=CARTESIAN_POINT('',(-5.86688900586219E-14,0.793752617480452,-0.379631838556025));
#3941=CARTESIAN_POINT('',(-5.86553375314658E-14,0.793752617480446,-0.459631838555994));
#3942=CARTESIAN_POINT('',(-0.9124061251644,0.793752617480474,-0.459631838555994));
#3943=CARTESIAN_POINT('',(0.110000000000051,0.793752617480443,-0.459631838555994));
#3944=CARTESIAN_POINT('',(0.110000000000051,0.793752617480449,-0.379631838556025));
#3945=CARTESIAN_POINT('',(-0.9124061251644,0.793752617480474,-0.459631838555994));
#3946=CARTESIAN_POINT('',(-5.00765878416742E-14,1.07375261748035,-0.459631838556015));
#3947=CARTESIAN_POINT('',(-6.32496442373731E-14,0.643752617480352,-0.459631838555982));
#3948=CARTESIAN_POINT('',(-0.912406125164405,0.64375261748038,-0.459631838555982));
#3949=CARTESIAN_POINT('',(0.110000000000047,0.643752617480348,-0.459631838555982));
#3950=CARTESIAN_POINT('',(0.110000000000051,0.793752617480443,-0.459631838555994));
#3951=CARTESIAN_POINT('',(-0.912406125164405,0.64375261748038,-0.459631838555982));
#3952=CARTESIAN_POINT('',(-6.3236091710217E-14,0.643752617480377,-0.659631838556047));
#3953=CARTESIAN_POINT('',(-6.32767492916853E-14,0.643752617480283,0.0843681614440071));
#3954=CARTESIAN_POINT('',(-0.912406125164405,0.643752617480311,0.0843681614440071));
#3955=CARTESIAN_POINT('',(0.110000000000047,0.64375261748028,0.084368161444007));
#3956=CARTESIAN_POINT('',(0.110000000000047,0.643752617480348,-0.459631838555982));
#3957=CARTESIAN_POINT('',(-0.912406125164406,0.603961411376448,0.135216833120661));
#3958=CARTESIAN_POINT('',(-6.45100292628875E-14,0.603961411376419,0.135216833120661));
#3959=CARTESIAN_POINT('',(-6.28701734770032E-14,0.657273558706276,0.0987923572622503));
#3960=CARTESIAN_POINT('',(-0.912406125164404,0.657273558706304,0.0987923572622503));
#3961=CARTESIAN_POINT('',(0.110000000000047,0.657273558706273,0.0987923572622503));
#3962=CARTESIAN_POINT('',(0.110000000000046,0.603961411376416,0.135216833120661));
#3963=CARTESIAN_POINT('',(-0.912406125164404,0.657273558706304,0.0987923572622503));
#3964=CARTESIAN_POINT('',(-6.84944722467717E-14,0.473644954804216,-0.0971033940967266));
#3965=CARTESIAN_POINT('',(-6.17453137230495E-14,0.694442151877155,0.138443965625231));
#3966=CARTESIAN_POINT('',(-0.912406125164403,0.694442151877183,0.138443965625231));
#3967=CARTESIAN_POINT('',(0.110000000000048,0.694442151877151,0.138443965625231));
#3968=CARTESIAN_POINT('',(0.110000000000047,0.657273558706273,0.0987923572622503));
#3969=CARTESIAN_POINT('',(-0.912406125164404,0.65796309310354,0.172638676780503));
#3970=CARTESIAN_POINT('',(-6.28566209498471E-14,0.657963093103512,0.172638676780503));
#3971=CARTESIAN_POINT('',(-6.13251853812113E-14,0.707963093103546,0.172638676780499));
#3972=CARTESIAN_POINT('',(-0.912406125164403,0.707963093103574,0.172638676780499));
#3973=CARTESIAN_POINT('',(0.110000000000049,0.707963093103542,0.172638676780499));
#3974=CARTESIAN_POINT('',(0.110000000000047,0.657963093103508,0.172638676780503));
#3975=CARTESIAN_POINT('',(-0.912406125164403,0.707963093103534,0.172638676780499));
#3976=CARTESIAN_POINT('',(-6.12709752725871E-14,0.707963093103442,-0.659631838555965));
#3977=CARTESIAN_POINT('',(-6.13251853812113E-14,0.707963093103516,0.301368161443969));
#3978=CARTESIAN_POINT('',(-0.912406125164403,0.707963093103544,0.301368161443969));
#3979=CARTESIAN_POINT('',(0.110000000000049,0.707963093103513,0.301368161443969));
#3980=CARTESIAN_POINT('',(0.110000000000049,0.707963093103503,0.172638676780499));
#3981=CARTESIAN_POINT('',(-0.912406125164403,0.707963093103544,0.301368161443969));
#3982=CARTESIAN_POINT('',(-4.06575814682064E-14,1.38147582281798,-0.332835299728939));
#3983=CARTESIAN_POINT('',(-6.7749083253188E-14,0.498752617480476,0.498368161443988));
#3984=CARTESIAN_POINT('',(-0.912406125164409,0.498752617480504,0.498368161443988));
#3985=CARTESIAN_POINT('',(0.109999999999881,0.498752617480472,0.498368161443988));
#3986=CARTESIAN_POINT('',(0.110000000000049,0.707963093103513,0.301368161443969));
#3987=CARTESIAN_POINT('',(-0.912406125164409,0.498752617480504,0.498368161443988));
#3988=CARTESIAN_POINT('',(6.25153288096396E-14,0.498752617480488,0.649999999999976));
#3989=CARTESIAN_POINT('',(0.109999999999876,0.498752617480472,0.63649369830068));
#3990=CARTESIAN_POINT('',(-3.0539789828192E-17,0.498752617480485,0.649999999999984));
#3991=CARTESIAN_POINT('',(0.110000000000042,0.498752617480472,0.498368161443988));
#3992=CARTESIAN_POINT('',(-6.76813206174076E-14,0.498752617480385,-0.659631838555948));
#3993=CARTESIAN_POINT('',(-6.7762635780344E-14,0.498752617480487,0.640368161444131));
#3994=CARTESIAN_POINT('',(-0.0500000000000623,0.498752617480488,0.64036816144413));
#3995=CARTESIAN_POINT('',(-0.000914581405594867,0.498752617480487,0.649887703523735));
#3996=CARTESIAN_POINT('',(-0.59999999999999,0.4987526174805,0.576329263458243));
#3997=CARTESIAN_POINT('',(-0.0500000000000446,1.07375261748044,0.640368161444085));
#3998=CARTESIAN_POINT('',(-0.000914581405577245,1.07375261748044,0.64988770352369));
#3999=CARTESIAN_POINT('',(-5.01443504774546E-14,1.07375261748044,0.640368161444085));
#4000=CARTESIAN_POINT('',(-5.00630353145182E-14,1.07375261748034,-0.659631838555993));
#4001=CARTESIAN_POINT('',(0.109999999999893,1.07375261748043,0.640368161444085));
#4002=CARTESIAN_POINT('',(0.109999999999828,-1.07500000000005,0.636493698300686));
#4003=CARTESIAN_POINT('',(-0.91240612516442,0.14875261748035,-0.539631838555912));
#4004=CARTESIAN_POINT('',(0.110000000000032,0.148752617480319,-0.539631838555912));
#4005=CARTESIAN_POINT('',(0.109999999999912,0.148752617480319,-0.539631838555912));
#4006=CARTESIAN_POINT('',(0.110000000000032,0.148752617480311,-0.636493698300661));
#4007=CARTESIAN_POINT('',(-0.91240612516442,0.14875261748035,-0.539631838555912));
#4008=CARTESIAN_POINT('',(-7.84284746521702E-14,0.148752617480322,-0.539631838555912));
#4009=CARTESIAN_POINT('',(-7.84149221250141E-14,0.148752617480313,-0.659631838555921));
#4010=CARTESIAN_POINT('',(-9.10847084310275E-18,0.148752617480312,-0.649999999999984));
#4011=CARTESIAN_POINT('',(-1.93303070355374E-14,0.148752617480314,-0.649999999999986));
#4012=CARTESIAN_POINT('',(-0.912406125164417,0.228752617480319,-0.539631838555918));
#4013=CARTESIAN_POINT('',(-7.59754672369217E-14,0.228752617480291,-0.539631838555919));
#4014=CARTESIAN_POINT('',(-7.59754672369217E-14,0.228752617480297,-0.45963183855595));
#4015=CARTESIAN_POINT('',(0.110000000000034,0.228752617480288,-0.539631838555919));
#4016=CARTESIAN_POINT('',(0.110000000000034,0.228752617480294,-0.45963183855595));
#4017=CARTESIAN_POINT('',(-0.912406125164417,0.228752617480325,-0.45963183855595));
#4018=CARTESIAN_POINT('',(-0.912406125164409,0.518752617480449,-0.459631838555972));
#4019=CARTESIAN_POINT('',(-5.00765878416742E-14,1.07375261748035,-0.459631838556015));
#4020=CARTESIAN_POINT('',(-6.70850094225406E-14,0.518752617480421,-0.459631838555972));
#4021=CARTESIAN_POINT('',(0.110000000000043,0.518752617480417,-0.459631838555972));
#4022=CARTESIAN_POINT('',(0.110000000000043,0.518752617480417,-0.459631838555972));
#4023=CARTESIAN_POINT('',(-0.912406125164409,0.518752617480449,-0.459631838555972));
#4024=CARTESIAN_POINT('',(-0.912406125164409,0.518752617480511,0.340368161444051));
#4025=CARTESIAN_POINT('',(-6.70714568953845E-14,0.518752617480405,-0.65963183855595));
#4026=CARTESIAN_POINT('',(-6.71256670040088E-14,0.518752617480483,0.340368161444051));
#4027=CARTESIAN_POINT('',(0.110000000000043,0.51875261748048,0.340368161444051));
#4028=CARTESIAN_POINT('',(0.110000000000043,0.51875261748048,0.340368161444051));
#4029=CARTESIAN_POINT('',(-0.912406125164409,0.518752617480511,0.340368161444051));
#4030=CARTESIAN_POINT('',(-0.912406125164412,0.418752617480419,0.440368161443992));
#4031=CARTESIAN_POINT('',(-4.32596666821716E-14,1.29625261748085,-0.437131838555143));
#4032=CARTESIAN_POINT('',(-7.02020906684364E-14,0.418752617480391,0.440368161443992));
#4033=CARTESIAN_POINT('',(0.11000000000004,0.418752617480388,0.440368161443992));
#4034=CARTESIAN_POINT('',(0.11000000000004,0.418752617480388,0.440368161443992));
#4035=CARTESIAN_POINT('',(-0.912406125164412,0.418752617480419,0.440368161443992));
#4036=CARTESIAN_POINT('',(-0.912406125164437,-0.421247382519533,0.440368161444058));
#4037=CARTESIAN_POINT('',(-5.01307979502985E-14,1.07375261748042,0.440368161443941));
#4038=CARTESIAN_POINT('',(-9.59518922649671E-14,-0.421247382519561,0.440368161444058));
#4039=CARTESIAN_POINT('',(0.110000000000014,-0.421247382519564,0.440368161444058));
#4040=CARTESIAN_POINT('',(0.110000000000014,-0.421247382519564,0.440368161444058));
#4041=CARTESIAN_POINT('',(-0.912406125164437,-0.421247382519533,0.440368161444058));
#4042=CARTESIAN_POINT('',(-0.91240612516444,-0.521247382519641,0.340368161444132));
#4043=CARTESIAN_POINT('',(-8.99074651533605E-14,-0.223747382518276,0.637868161444983));
#4044=CARTESIAN_POINT('',(-9.90012108750826E-14,-0.521247382519669,0.340368161444132));
#4045=CARTESIAN_POINT('',(0.110000000000011,-0.521247382519672,0.340368161444132));
#4046=CARTESIAN_POINT('',(0.110000000000011,-0.521247382519672,0.340368161444132));
#4047=CARTESIAN_POINT('',(-0.91240612516444,-0.521247382519641,0.340368161444132));
#4048=CARTESIAN_POINT('',(-0.91240612516444,-0.521247382519703,-0.459631838555891));
#4049=CARTESIAN_POINT('',(-9.89470007664583E-14,-0.521247382519747,-0.659631838555869));
#4050=CARTESIAN_POINT('',(-9.89605532936144E-14,-0.521247382519731,-0.459631838555891));
#4051=CARTESIAN_POINT('',(0.110000000000011,-0.521247382519734,-0.459631838555891));
#4052=CARTESIAN_POINT('',(0.110000000000011,-0.521247382519734,-0.459631838555891));
#4053=CARTESIAN_POINT('',(-0.91240612516444,-0.521247382519703,-0.459631838555891));
#4054=CARTESIAN_POINT('',(-0.912406125164432,-0.23124738251958,-0.459631838555914));
#4055=CARTESIAN_POINT('',(-5.00765878416742E-14,1.07375261748035,-0.459631838556015));
#4056=CARTESIAN_POINT('',(-9.00700954792333E-14,-0.231247382519608,-0.459631838555914));
#4057=CARTESIAN_POINT('',(0.11000000000002,-0.231247382519611,-0.459631838555914));
#4058=CARTESIAN_POINT('',(0.11000000000002,-0.231247382519611,-0.459631838555914));
#4059=CARTESIAN_POINT('',(-0.912406125164432,-0.23124738251958,-0.459631838555914));
#4060=CARTESIAN_POINT('',(-0.912406125164432,-0.231247382519586,-0.539631838555883));
#4061=CARTESIAN_POINT('',(-9.00700954792333E-14,-0.231247382519614,-0.539631838555883));
#4062=CARTESIAN_POINT('',(-8.76170880639848E-14,-0.151247382519645,-0.539631838555889));
#4063=CARTESIAN_POINT('',(0.11000000000002,-0.231247382519617,-0.539631838555883));
#4064=CARTESIAN_POINT('',(0.110000000000022,-0.151247382519648,-0.539631838555889));
#4065=CARTESIAN_POINT('',(-0.912406125164429,-0.151247382519617,-0.539631838555889));
#4066=CARTESIAN_POINT('',(-0.912406125164429,-0.15124738251959,-1.61963183855597));
#4067=CARTESIAN_POINT('',(-8.76035355368288E-14,-0.151247382519642,-0.659631838556024));
#4068=CARTESIAN_POINT('',(9.26123114410488E-18,-0.151247382519644,-0.649999999999984));
#4069=CARTESIAN_POINT('',(0.110000000000022,-0.151247382519621,-1.61963183855597));
#4070=CARTESIAN_POINT('',(0.109999999999906,-0.151247382519646,-0.636493698300676));
#4071=CARTESIAN_POINT('',(-4.08214848873988E-14,-0.151247382519642,-0.649999999999989));
#4072=CARTESIAN_POINT('',(0.109999999999893,1.07375261748043,0.640368161444085));
#4073=CARTESIAN_POINT('',(0.109999999999944,1.07500000000004,-0.636493698300671));
#4074=CARTESIAN_POINT('',(-5.00630353145182E-14,1.07375261748034,-0.659631838555993));
#4075=CARTESIAN_POINT('',(-6.58247654541732E-17,1.07500000000005,-0.649999999999984));
#4076=CARTESIAN_POINT('',(-0.6,-3.67394039744206E-17,0.576329263458241));
#4077=CARTESIAN_POINT('',(6.58247654541732E-17,-1.07500000000005,0.649999999999984));
#4078=CARTESIAN_POINT('',(6.58247654541732E-17,-1.07500000000005,0.649999999999984));
#4079=CARTESIAN_POINT('',(-6.09626915837189E-17,0.995596307868742,0.649999999999984));
#4080=CARTESIAN_POINT('',(-0.70988496437859,0.908433394225943,0.562837086357185));
#4081=CARTESIAN_POINT('',(-0.529749547472412,0.93055124229383,0.584954934425071));
#4082=CARTESIAN_POINT('',(-0.529749547472412,-3.24378043830924E-17,0.584954934425071));
#4083=CARTESIAN_POINT('',(-0.529749547472412,-0.93055124229383,0.584954934425071));
#4084=CARTESIAN_POINT('',(-0.70988496437859,-0.908433394225943,0.562837086357185));
#4085=CARTESIAN_POINT('',(6.09626915837189E-17,-0.995596307868742,0.649999999999984));
#4086=CARTESIAN_POINT('',(6.58247654541732E-17,-1.07500000000005,0.649999999999984));
#4087=CARTESIAN_POINT('',(5.42101086242752E-17,-1.07500000000005,0.649999999999984));
#4088=CARTESIAN_POINT('',(-0.00946418252486268,-0.996758363364363,0.651162055495604));
#4089=CARTESIAN_POINT('',(0.279749547472412,-0.961247382519556,0.615651074650797));
#4090=CARTESIAN_POINT('',(0.279749547472412,1.03202540368403,0.615651074650797));
#4091=CARTESIAN_POINT('',(0.279749547472412,0.961247382519556,0.615651074650797));
#4092=CARTESIAN_POINT('',(0.246795846225531,0.96529358825728,0.619697280388521));
#4093=CARTESIAN_POINT('',(-5.42101086242752E-17,1.07500000000005,-0.649999999999984));
#4094=CARTESIAN_POINT('',(-6.58247654541732E-17,1.07500000000005,-0.649999999999984));
#4095=CARTESIAN_POINT('',(6.09626915837189E-17,-0.995596307868742,-0.649999999999984));
#4096=CARTESIAN_POINT('',(-6.58247654541732E-17,1.07500000000005,-0.649999999999984));
#4097=CARTESIAN_POINT('',(-6.09626915837189E-17,0.995596307868742,-0.649999999999984));
#4098=CARTESIAN_POINT('',(-0.00946418252486277,0.996758363364363,-0.651162055495604));
#4099=CARTESIAN_POINT('',(0.279749547472412,0.961247382519556,-0.615651074650797));
#4100=CARTESIAN_POINT('',(0.279749547472412,-1.03202540368403,-0.615651074650797));
#4101=CARTESIAN_POINT('',(0.279749547472412,-0.961247382519556,-0.615651074650797));
#4102=CARTESIAN_POINT('',(0.24679584622553,-0.96529358825728,-0.619697280388521));
#4103=CARTESIAN_POINT('',(-0.6,-3.67394039744206E-17,0.));
#4104=CARTESIAN_POINT('',(-0.6,-3.67394039744206E-17,-0.505551242293766));
#4105=CARTESIAN_POINT('',(-0.6,0.93055124229383,-0.505551242293766));
#4106=CARTESIAN_POINT('',(-0.6,-0.93055124229383,-0.505551242293766));
#4107=CARTESIAN_POINT('',(-0.6,-0.93055124229383,0.));
#4108=CARTESIAN_POINT('',(-0.6,-0.93055124229383,0.505551242293766));
#4109=CARTESIAN_POINT('',(-0.6,-3.67394039744206E-17,0.505551242293766));
#4110=CARTESIAN_POINT('',(-0.6,0.93055124229383,0.505551242293766));
#4111=CARTESIAN_POINT('',(-0.6,0.93055124229383,0.));
#4112=CARTESIAN_POINT('',(-0.6,-1.00132926345831,0.));
#4113=CARTESIAN_POINT('',(-0.529749547472412,-1.00995493442514,0.));
#4114=CARTESIAN_POINT('',(-0.529749547472412,-1.00995493442514,0.505551242293766));
#4115=CARTESIAN_POINT('',(-0.529749547472412,-1.00995493442514,-0.505551242293766));
#4116=CARTESIAN_POINT('',(-0.659228912183739,-0.994056867483068,-0.489653175351698));
#4117=CARTESIAN_POINT('',(6.58247654541732E-17,-1.07500000000005,-0.570596307868678));
#4118=CARTESIAN_POINT('',(6.58247654541732E-17,-1.07500000000005,-0.649999999999984));
#4119=CARTESIAN_POINT('',(6.58247654541732E-17,-1.07500000000005,0.570596307868678));
#4120=CARTESIAN_POINT('',(-0.659228912183739,-0.994056867483068,0.489653175351698));
#4121=CARTESIAN_POINT('',(-0.6,1.00132926345831,0.));
#4122=CARTESIAN_POINT('',(-6.58247654541732E-17,1.07500000000005,0.649999999999984));
#4123=CARTESIAN_POINT('',(-6.58247654541732E-17,1.07500000000005,0.570596307868678));
#4124=CARTESIAN_POINT('',(-6.58247654541732E-17,1.07500000000005,-0.570596307868678));
#4125=CARTESIAN_POINT('',(-0.65922891218374,0.994056867483068,-0.489653175351698));
#4126=CARTESIAN_POINT('',(-0.529749547472412,1.00995493442514,-0.505551242293766));
#4127=CARTESIAN_POINT('',(-0.529749547472412,1.00995493442514,0.));
#4128=CARTESIAN_POINT('',(-0.529749547472412,1.00995493442514,0.505551242293766));
#4129=CARTESIAN_POINT('',(-0.65922891218374,0.994056867483068,0.489653175351698));
#4130=CARTESIAN_POINT('',(-0.6,-3.67394039744206E-17,-0.576329263458241));
#4131=CARTESIAN_POINT('',(-0.529749547472412,-3.24378043830924E-17,-0.584954934425071));
#4132=CARTESIAN_POINT('',(-0.529749547472412,-0.93055124229383,-0.584954934425071));
#4133=CARTESIAN_POINT('',(-0.529749547472412,0.93055124229383,-0.584954934425071));
#4134=CARTESIAN_POINT('',(-0.70988496437859,0.908433394225943,-0.562837086357185));
#4135=CARTESIAN_POINT('',(-0.70988496437859,-0.908433394225943,-0.562837086357185));
#4136=CARTESIAN_POINT('',(0.35,2.14313189850787E-17,0.));
#4137=CARTESIAN_POINT('',(0.35,1.03202540368403,-0.536247382519492));
#4138=CARTESIAN_POINT('',(0.35,-0.961247382519556,-0.536247382519492));
#4139=CARTESIAN_POINT('',(0.35,0.961247382519556,-0.536247382519492));
#4140=CARTESIAN_POINT('',(0.35,0.961247382519556,0.607025403683967));
#4141=CARTESIAN_POINT('',(0.35,0.961247382519556,0.536247382519492));
#4142=CARTESIAN_POINT('',(0.35,-1.03202540368403,0.536247382519492));
#4143=CARTESIAN_POINT('',(0.35,-0.961247382519556,0.536247382519492));
#4144=CARTESIAN_POINT('',(0.35,-0.961247382519556,-0.607025403683967));
#4145=CARTESIAN_POINT('',(-5.42101086242752E-17,1.07500000000005,0.649999999999984));
#4146=CARTESIAN_POINT('',(-0.00946418252486277,1.07616205549567,0.571758363364299));
#4147=CARTESIAN_POINT('',(0.279749547472412,1.04065107465086,0.536247382519492));
#4148=CARTESIAN_POINT('',(0.279749547472412,1.04065107465086,-0.607025403683967));
#4149=CARTESIAN_POINT('',(0.279749547472412,1.04065107465086,-0.536247382519492));
#4150=CARTESIAN_POINT('',(0.145483741835829,1.05713684264022,-0.552733150508855));
#4151=CARTESIAN_POINT('',(5.42101086242752E-17,-1.07500000000005,-0.649999999999984));
#4152=CARTESIAN_POINT('',(-0.00946418252486266,-1.07616205549567,-0.571758363364299));
#4153=CARTESIAN_POINT('',(0.279749547472412,-1.04065107465086,-0.536247382519492));
#4154=CARTESIAN_POINT('',(0.279749547472412,-1.04065107465086,0.607025403683967));
#4155=CARTESIAN_POINT('',(0.279749547472412,-1.04065107465086,0.536247382519492));
#4156=CARTESIAN_POINT('',(0.145483741835829,-1.05713684264022,0.552733150508854));
#4157=CARTESIAN_POINT('',(-0.52,-3.18408167778312E-17,0.505551242293766));
#4158=CARTESIAN_POINT('',(-0.52,-0.93055124229383,0.505551242293766));
#4159=CARTESIAN_POINT('',(-0.52,0.93055124229383,0.505551242293766));
#4160=CARTESIAN_POINT('',(-0.700135416906178,0.908433394225943,0.483433394225879));
#4161=CARTESIAN_POINT('',(4.15046144154607E-16,0.994399213963341,0.569399213963276));
#4162=CARTESIAN_POINT('',(-0.52,0.93055124229383,0.505551242293766));
#4163=CARTESIAN_POINT('',(-0.52,0.93055124229383,0.505551242293766));
#4164=CARTESIAN_POINT('',(-0.52,0.93055124229383,0.505551242293766));
#4165=CARTESIAN_POINT('',(-0.52,0.93055124229383,0.));
#4166=CARTESIAN_POINT('',(-0.52,0.93055124229383,-0.505551242293766));
#4167=CARTESIAN_POINT('',(-0.52,-3.18408167778312E-17,-0.505551242293766));
#4168=CARTESIAN_POINT('',(-0.52,0.93055124229383,-0.505551242293766));
#4169=CARTESIAN_POINT('',(-0.52,-0.93055124229383,-0.505551242293766));
#4170=CARTESIAN_POINT('',(-0.52,-0.93055124229383,0.));
#4171=CARTESIAN_POINT('',(-0.52,-0.93055124229383,-0.505551242293766));
#4172=CARTESIAN_POINT('',(-0.52,-0.93055124229383,0.505551242293766));
#4173=CARTESIAN_POINT('',(-0.52,-0.93055124229383,0.505551242293766));
#4174=CARTESIAN_POINT('',(-0.52,-0.93055124229383,0.505551242293766));
#4175=CARTESIAN_POINT('',(-0.649479364711328,-0.914653175351762,0.489653175351698));
#4176=CARTESIAN_POINT('',(-2.04981973235541E-16,-0.99439921396334,0.569399213963276));
#4177=CARTESIAN_POINT('',(0.27,-0.961247382519556,0.));
#4178=CARTESIAN_POINT('',(0.27,-0.961247382519556,-0.536247382519492));
#4179=CARTESIAN_POINT('',(0.27,-0.961247382519556,0.536247382519492));
#4180=CARTESIAN_POINT('',(0.27,1.65327317884893E-17,-0.536247382519492));
#4181=CARTESIAN_POINT('',(0.27,0.961247382519556,-0.536247382519492));
#4182=CARTESIAN_POINT('',(0.27,-0.961247382519556,-0.536247382519492));
#4183=CARTESIAN_POINT('',(0.27,1.65327317884893E-17,0.536247382519492));
#4184=CARTESIAN_POINT('',(0.27,0.961247382519556,0.536247382519492));
#4185=CARTESIAN_POINT('',(0.27,-0.961247382519556,0.536247382519492));
#4186=CARTESIAN_POINT('',(0.27,0.961247382519556,0.));
#4187=CARTESIAN_POINT('',(0.27,0.961247382519556,0.536247382519492));
#4188=CARTESIAN_POINT('',(0.27,0.961247382519556,-0.536247382519492));
#4189=CARTESIAN_POINT('',(-0.0192137299972745,-0.996758363364363,0.571758363364299));
#4190=CARTESIAN_POINT('',(0.27,-0.961247382519556,0.536247382519492));
#4191=CARTESIAN_POINT('',(0.27,-0.961247382519556,0.536247382519492));
#4192=CARTESIAN_POINT('',(-0.52,-0.93055124229383,-0.505551242293766));
#4193=CARTESIAN_POINT('',(-0.52,-0.93055124229383,-0.505551242293766));
#4194=CARTESIAN_POINT('',(-0.649479364711328,-0.914653175351762,-0.489653175351698));
#4195=CARTESIAN_POINT('',(7.43694927689276E-16,-0.994399213963341,-0.569399213963276));
#4196=CARTESIAN_POINT('',(-0.0192137299972745,-0.996758363364363,-0.571758363364299));
#4197=CARTESIAN_POINT('',(0.27,-0.961247382519556,-0.536247382519492));
#4198=CARTESIAN_POINT('',(0.27,-0.961247382519556,-0.536247382519492));
#4199=CARTESIAN_POINT('',(-0.52,0.93055124229383,-0.505551242293766));
#4200=CARTESIAN_POINT('',(-0.52,0.93055124229383,-0.505551242293766));
#4201=CARTESIAN_POINT('',(-0.649479364711328,0.914653175351762,-0.489653175351698));
#4202=CARTESIAN_POINT('',(-4.20128341838133E-16,0.994399213963341,-0.569399213963276));
#4203=CARTESIAN_POINT('',(0.135734194363418,0.977733150508919,-0.552733150508854));
#4204=CARTESIAN_POINT('',(0.27,0.961247382519556,-0.536247382519492));
#4205=CARTESIAN_POINT('',(0.27,0.961247382519556,-0.536247382519492));
#4206=CARTESIAN_POINT('',(0.27,0.961247382519556,0.536247382519492));
#4207=CARTESIAN_POINT('',(0.27,0.961247382519556,0.536247382519492));
#4208=CARTESIAN_POINT('',(-0.0192137299972746,0.996758363364363,0.571758363364299));
#4209=CARTESIAN_POINT('',(0.,0.,0.));
#4210=CARTESIAN_POINT('',(1.66533453693773E-13,1.07500000000001,-0.650000000000095));
#4211=CARTESIAN_POINT('',(-1.47569939297254E-16,0.799999999999912,-1.61000000000006));
#4212=CARTESIAN_POINT('',(-8.87868929381835E-17,0.799999999999912,-0.650000000000095));
#4213=CARTESIAN_POINT('',(-7.57336633739984E-17,0.799999999999912,-0.436824583655182));
#4214=CARTESIAN_POINT('',(1.66533453693773E-13,1.07500000000001,-0.650000000000095));
#4215=CARTESIAN_POINT('',(-7.04171909509821E-17,0.500000000000056,-0.650000000000095));
#4216=CARTESIAN_POINT('',(-6.17221986770272E-17,0.500000000000056,-0.507999999999953));
#4217=CARTESIAN_POINT('',(1.60943640893101E-13,0.500000000000056,-0.507999999999953));
#4218=CARTESIAN_POINT('',(-6.24698746714191E-17,0.709210475623112,-0.31099999999995));
#4219=CARTESIAN_POINT('',(-6.24698746714191E-17,0.709210475623112,-0.31099999999995));
#4220=CARTESIAN_POINT('',(-5.45874671039688E-17,0.709210475623112,-0.18227051533648));
#4221=CARTESIAN_POINT('',(-5.45874671039712E-17,0.709210475623151,-0.18227051533648));
#4222=CARTESIAN_POINT('',(-5.15258501061007E-17,0.659210475623118,-0.18227051533648));
#4223=CARTESIAN_POINT('',(-5.16657260560499E-17,0.695689534396763,-0.148075804181211));
#4224=CARTESIAN_POINT('',(-4.69618453601352E-17,0.658520941225888,-0.108424195818227));
#4225=CARTESIAN_POINT('',(-4.69618453601352E-17,0.658520941225888,-0.108424195818227));
#4226=CARTESIAN_POINT('',(-4.59277737195068E-17,0.605208793896028,-0.144848671676634));
#4227=CARTESIAN_POINT('',(-4.52506992284873E-17,0.644999999999896,-0.093999999999983));
#4228=CARTESIAN_POINT('',(-1.19403062916867E-17,0.645000000000007,0.450000000000006));
#4229=CARTESIAN_POINT('',(-1.19403062916867E-17,0.645000000000007,0.450000000000006));
#4230=CARTESIAN_POINT('',(-2.11251572852977E-17,0.795000000000101,0.450000000000006));
#4231=CARTESIAN_POINT('',(-2.11251572852977E-17,0.795000000000101,0.450000000000006));
#4232=CARTESIAN_POINT('',(-2.60237444818852E-17,0.795000000000101,0.370000000000037));
#4233=CARTESIAN_POINT('',(-3.09223316784727E-17,0.87500000000007,0.370000000000037));
#4234=CARTESIAN_POINT('',(-7.43972930481797E-17,0.874999999999959,-0.339999999999682));
#4235=CARTESIAN_POINT('',(-7.4397293048182E-17,0.874999999999996,-0.339999999999682));
#4236=CARTESIAN_POINT('',(-7.13356760502908E-17,0.824999999999632,-0.339999999999674));
#4237=CARTESIAN_POINT('',(-7.50654782110933E-17,0.837499999999818,-0.388412291827618));
#4238=CARTESIAN_POINT('',(-7.87952803718665E-17,0.849999999999906,-0.436824583655182));
#4239=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,0.649999999999984));
#4240=CARTESIAN_POINT('',(0.11000000000011,0.799999999999912,0.649999999999984));
#4241=CARTESIAN_POINT('',(0.11000000000011,0.799999999999912,-0.436824583655182));
#4242=CARTESIAN_POINT('',(0.11000000000011,0.799999999999912,-0.650000000000095));
#4243=CARTESIAN_POINT('',(0.11000000000011,0.849999999999906,-0.436824583655182));
#4244=CARTESIAN_POINT('',(0.11000000000011,0.837499999999818,-0.388412291827618));
#4245=CARTESIAN_POINT('',(0.11000000000011,0.824999999999632,-0.339999999999674));
#4246=CARTESIAN_POINT('',(0.11000000000011,0.874999999999996,-0.339999999999682));
#4247=CARTESIAN_POINT('',(0.11000000000011,0.875000000000114,0.650000000000015));
#4248=CARTESIAN_POINT('',(0.11000000000011,0.87500000000007,0.370000000000037));
#4249=CARTESIAN_POINT('',(0.11000000000011,0.795000000000101,0.370000000000037));
#4250=CARTESIAN_POINT('',(0.11000000000011,0.795000000000101,0.450000000000006));
#4251=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,0.450000000000006));
#4252=CARTESIAN_POINT('',(0.11000000000011,0.645000000000007,0.450000000000006));
#4253=CARTESIAN_POINT('',(0.11000000000011,0.645000000000048,0.650000000000072));
#4254=CARTESIAN_POINT('',(0.11000000000011,0.644999999999896,-0.093999999999983));
#4255=CARTESIAN_POINT('',(0.11000000000011,0.605208793896028,-0.144848671676634));
#4256=CARTESIAN_POINT('',(0.11000000000011,0.658520941225888,-0.108424195818227));
#4257=CARTESIAN_POINT('',(0.11000000000011,0.474892337323843,0.0874715555407639));
#4258=CARTESIAN_POINT('',(0.11000000000011,0.695689534396763,-0.148075804181211));
#4259=CARTESIAN_POINT('',(0.11000000000011,0.659210475623118,-0.18227051533648));
#4260=CARTESIAN_POINT('',(0.11000000000011,0.709210475623151,-0.18227051533648));
#4261=CARTESIAN_POINT('',(0.11000000000011,0.709210475623113,0.649999999999984));
#4262=CARTESIAN_POINT('',(0.11000000000011,0.709210475623112,-0.31099999999995));
#4263=CARTESIAN_POINT('',(0.11000000000011,1.38272320533763,0.323203461172906));
#4264=CARTESIAN_POINT('',(0.11000000000011,0.500000000000056,-0.507999999999953));
#4265=CARTESIAN_POINT('',(0.11000000000011,0.500000000000056,0.649999999999984));
#4266=CARTESIAN_POINT('',(0.11000000000011,0.500000000000056,-0.650000000000095));
#4267=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,-0.650000000000095));
#4268=CARTESIAN_POINT('',(0.160000000000105,1.07500000000001,-0.650000000000095));
#4269=CARTESIAN_POINT('',(0.160000000000105,0.799999999999912,-0.650000000000095));
#4270=CARTESIAN_POINT('',(0.155642212862829,0.799999999999912,-0.699809734904686));
#4271=CARTESIAN_POINT('',(0.160000000000105,0.500000000000056,-0.650000000000095));
#4272=CARTESIAN_POINT('',(0.155642212862829,0.500000000000056,-0.699809734904686));
#4273=CARTESIAN_POINT('',(0.155642212862834,1.07500000000001,-0.699809734904622));
#4274=CARTESIAN_POINT('',(0.155642212862834,1.07500000000001,-0.699809734904622));
#4275=CARTESIAN_POINT('',(0.155642212862834,0.799999999999912,-0.699809734904622));
#4276=CARTESIAN_POINT('',(0.611457081502678,0.799999999999912,-0.739688368577198));
#4277=CARTESIAN_POINT('',(0.155642212862834,0.500000000000056,-0.699809734904622));
#4278=CARTESIAN_POINT('',(0.611457081502678,0.500000000000056,-0.739688368577198));
#4279=CARTESIAN_POINT('',(0.611457081502678,1.07500000000001,-0.739688368577198));
#4280=CARTESIAN_POINT('',(0.598383720090523,1.07500000000001,-0.88911757329091));
#4281=CARTESIAN_POINT('',(0.598383720090523,0.799999999999912,-0.88911757329091));
#4282=CARTESIAN_POINT('',(0.748018327629446,0.799999999999912,-0.878654102229276));
#4283=CARTESIAN_POINT('',(0.598383720090523,0.500000000000056,-0.88911757329091));
#4284=CARTESIAN_POINT('',(0.748018327629446,0.500000000000056,-0.878654102229276));
#4285=CARTESIAN_POINT('',(0.748018327629446,1.07500000000001,-0.878654102229276));
#4286=CARTESIAN_POINT('',(0.759951458876362,1.07500000000001,-1.04930582958868));
#4287=CARTESIAN_POINT('',(0.759951458876362,0.799999999999912,-1.04930582958868));
#4288=CARTESIAN_POINT('',(0.759951458876362,0.799999999999912,-1.04930582958868));
#4289=CARTESIAN_POINT('',(0.759951458876362,0.500000000000056,-1.04930582958868));
#4290=CARTESIAN_POINT('',(0.759951458876362,0.500000000000056,-1.04930582958868));
#4291=CARTESIAN_POINT('',(0.759951458876362,1.07500000000001,-1.04930582958868));
#4292=CARTESIAN_POINT('',(0.650219413347808,1.07500000000001,-1.05697904170043));
#4293=CARTESIAN_POINT('',(0.650219413347808,0.799999999999912,-1.05697904170043));
#4294=CARTESIAN_POINT('',(0.650219413347808,0.799999999999912,-1.05697904170043));
#4295=CARTESIAN_POINT('',(0.650219413347808,0.500000000000056,-1.05697904170043));
#4296=CARTESIAN_POINT('',(0.650219413347808,0.500000000000056,-1.05697904170043));
#4297=CARTESIAN_POINT('',(0.650219413347808,1.07500000000001,-1.05697904170043));
#4298=CARTESIAN_POINT('',(0.650219413347808,1.07500000000001,-1.05697904170043));
#4299=CARTESIAN_POINT('',(0.650219413347808,0.799999999999912,-1.05697904170043));
#4300=CARTESIAN_POINT('',(0.638286282100886,0.799999999999912,-0.886327314341129));
#4301=CARTESIAN_POINT('',(0.650219413347808,0.500000000000056,-1.05697904170043));
#4302=CARTESIAN_POINT('',(0.638286282100886,0.500000000000056,-0.886327314341129));
#4303=CARTESIAN_POINT('',(0.638286282100886,1.07500000000001,-0.886327314341129));
#4304=CARTESIAN_POINT('',(0.598383720090523,1.07500000000001,-0.88911757329091));
#4305=CARTESIAN_POINT('',(0.598383720090523,0.799999999999912,-0.88911757329091));
#4306=CARTESIAN_POINT('',(0.601869949800417,0.799999999999912,-0.849269785367267));
#4307=CARTESIAN_POINT('',(0.598383720090523,0.500000000000056,-0.88911757329091));
#4308=CARTESIAN_POINT('',(0.601869949800417,0.500000000000056,-0.849269785367267));
#4309=CARTESIAN_POINT('',(0.601869949800417,1.07500000000001,-0.849269785367265));
#4310=CARTESIAN_POINT('',(0.146055081160514,1.07500000000001,-0.809391151694827));
#4311=CARTESIAN_POINT('',(0.146055081160514,0.799999999999912,-0.809391151694827));
#4312=CARTESIAN_POINT('',(0.146055081160514,0.799999999999912,-0.809391151694827));
#4313=CARTESIAN_POINT('',(0.146055081160514,0.500000000000056,-0.809391151694827));
#4314=CARTESIAN_POINT('',(0.146055081160514,0.500000000000056,-0.809391151694827));
#4315=CARTESIAN_POINT('',(0.146055081160514,1.07500000000001,-0.809391151694827));
#4316=CARTESIAN_POINT('',(0.160000000000105,1.07500000000001,-0.650000000000095));
#4317=CARTESIAN_POINT('',(0.160000000000105,0.799999999999912,-0.650000000000095));
#4318=CARTESIAN_POINT('',(0.160000000000105,0.500000000000056,-0.650000000000095));
#4319=CARTESIAN_POINT('',(1.02240612516445,0.500000000000056,-0.507999999999953));
#4320=CARTESIAN_POINT('',(1.02240612516445,0.500000000000056,-0.507999999999953));
#4321=CARTESIAN_POINT('',(1.02240612516445,0.709210475623112,-0.31099999999995));
#4322=CARTESIAN_POINT('',(1.02240612516445,0.709210475623112,-0.31099999999995));
#4323=CARTESIAN_POINT('',(1.02240612516445,0.709210475623112,-0.18227051533648));
#4324=CARTESIAN_POINT('',(1.02240612516445,0.709210475623152,-0.18227051533648));
#4325=CARTESIAN_POINT('',(1.02240612516445,0.659210475623118,-0.18227051533648));
#4326=CARTESIAN_POINT('',(1.02240612516445,0.695689534396763,-0.148075804181211));
#4327=CARTESIAN_POINT('',(1.02240612516445,0.658520941225888,-0.108424195818227));
#4328=CARTESIAN_POINT('',(1.02240612516445,0.658520941225888,-0.108424195818227));
#4329=CARTESIAN_POINT('',(1.02240612516445,0.605208793896028,-0.144848671676634));
#4330=CARTESIAN_POINT('',(1.02240612516445,0.644999999999896,-0.0939999999999831));
#4331=CARTESIAN_POINT('',(1.02240612516445,0.645000000000007,0.450000000000006));
#4332=CARTESIAN_POINT('',(1.02240612516445,0.645000000000007,0.450000000000006));
#4333=CARTESIAN_POINT('',(1.02240612516445,0.795000000000101,0.450000000000006));
#4334=CARTESIAN_POINT('',(1.02240612516445,0.795000000000101,0.450000000000006));
#4335=CARTESIAN_POINT('',(1.02240612516445,0.795000000000101,0.370000000000037));
#4336=CARTESIAN_POINT('',(1.02240612516445,0.87500000000007,0.370000000000037));
#4337=CARTESIAN_POINT('',(1.02240612516445,0.874999999999959,-0.339999999999682));
#4338=CARTESIAN_POINT('',(1.02240612516445,0.874999999999997,-0.339999999999682));
#4339=CARTESIAN_POINT('',(1.02240612516445,0.824999999999632,-0.339999999999674));
#4340=CARTESIAN_POINT('',(1.02240612516445,0.837499999999818,-0.388412291827618));
#4341=CARTESIAN_POINT('',(1.02240612516445,0.849999999999906,-0.436824583655182));
#4342=CARTESIAN_POINT('',(1.02240612516445,0.799999999999912,-0.436824583655182));
#4343=CARTESIAN_POINT('',(1.02240612516445,0.799999999999912,-1.61000000000006));
#4344=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,0.649999999999984));
#4345=CARTESIAN_POINT('',(0.11000000000011,-0.800000000000023,0.649999999999984));
#4346=CARTESIAN_POINT('',(0.11000000000011,-0.800000000000023,-0.650000000000095));
#4347=CARTESIAN_POINT('',(0.11000000000011,-0.800000000000027,-0.436824583655182));
#4348=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,-0.650000000000095));
#4349=CARTESIAN_POINT('',(0.11000000000011,-0.499999999999945,-0.650000000000095));
#4350=CARTESIAN_POINT('',(0.11000000000011,-0.499999999999945,0.649999999999984));
#4351=CARTESIAN_POINT('',(0.11000000000011,-0.499999999999945,-0.507999999999953));
#4352=CARTESIAN_POINT('',(0.11000000000011,-0.243153666822317,-0.749855612082842));
#4353=CARTESIAN_POINT('',(0.11000000000011,-0.709210475623112,-0.31099999999995));
#4354=CARTESIAN_POINT('',(0.11000000000011,-0.709210475623112,0.649999999999984));
#4355=CARTESIAN_POINT('',(0.11000000000011,-0.709210475623112,-0.182270515336258));
#4356=CARTESIAN_POINT('',(0.11000000000011,-0.659210475623118,-0.182270515336258));
#4357=CARTESIAN_POINT('',(0.11000000000011,-0.695689534396632,-0.148075804180907));
#4358=CARTESIAN_POINT('',(0.11000000000011,0.530686975733544,1.16022771060092));
#4359=CARTESIAN_POINT('',(0.11000000000011,-0.658520941226315,-0.108424195818801));
#4360=CARTESIAN_POINT('',(0.11000000000011,-0.60520879389625,-0.144848671676245));
#4361=CARTESIAN_POINT('',(0.11000000000011,-0.645000000000007,-0.0939999999999826));
#4362=CARTESIAN_POINT('',(0.11000000000011,-0.645000000000007,0.649999999999984));
#4363=CARTESIAN_POINT('',(0.11000000000011,-0.645000000000007,0.450000000000006));
#4364=CARTESIAN_POINT('',(0.11000000000011,1.07500000000008,0.450000000000642));
#4365=CARTESIAN_POINT('',(0.11000000000011,-0.79499999999999,0.44999999999995));
#4366=CARTESIAN_POINT('',(0.11000000000011,-0.79499999999999,0.370000000000037));
#4367=CARTESIAN_POINT('',(0.11000000000011,-0.874999999999904,0.370000000000037));
#4368=CARTESIAN_POINT('',(0.11000000000011,-0.874999999999959,0.649999999999984));
#4369=CARTESIAN_POINT('',(0.11000000000011,-0.874999999999959,-0.339999999999674));
#4370=CARTESIAN_POINT('',(0.11000000000011,-0.824999999999632,-0.339999999999674));
#4371=CARTESIAN_POINT('',(0.11000000000011,-0.837499999999855,-0.388412291827547));
#4372=CARTESIAN_POINT('',(0.11000000000011,-0.850000000000017,-0.436824583655182));
#4373=CARTESIAN_POINT('',(0.160000000000105,1.07500000000001,-0.650000000000095));
#4374=CARTESIAN_POINT('',(0.160000000000105,-0.800000000000023,-0.650000000000095));
#4375=CARTESIAN_POINT('',(0.155642212862829,-0.800000000000023,-0.699809734904686));
#4376=CARTESIAN_POINT('',(0.155642212862834,1.07500000000001,-0.699809734904622));
#4377=CARTESIAN_POINT('',(0.155642212862829,-0.499999999999945,-0.699809734904686));
#4378=CARTESIAN_POINT('',(0.160000000000105,-0.499999999999945,-0.650000000000095));
#4379=CARTESIAN_POINT('',(0.155642212862834,1.07500000000001,-0.699809734904622));
#4380=CARTESIAN_POINT('',(0.155642212862835,-0.800000000000023,-0.699809734904622));
#4381=CARTESIAN_POINT('',(0.611457081502678,-0.800000000000023,-0.739688368577198));
#4382=CARTESIAN_POINT('',(0.611457081502678,1.07500000000001,-0.739688368577198));
#4383=CARTESIAN_POINT('',(0.611457081502678,-0.499999999999945,-0.739688368577198));
#4384=CARTESIAN_POINT('',(0.155642212862835,-0.499999999999945,-0.699809734904622));
#4385=CARTESIAN_POINT('',(0.598383720090523,1.07500000000001,-0.88911757329091));
#4386=CARTESIAN_POINT('',(0.598383720090523,-0.800000000000023,-0.88911757329091));
#4387=CARTESIAN_POINT('',(0.748018327629446,-0.800000000000023,-0.878654102229276));
#4388=CARTESIAN_POINT('',(0.748018327629446,1.07500000000001,-0.878654102229276));
#4389=CARTESIAN_POINT('',(0.748018327629446,-0.499999999999945,-0.878654102229276));
#4390=CARTESIAN_POINT('',(0.598383720090523,-0.499999999999945,-0.88911757329091));
#4391=CARTESIAN_POINT('',(0.759951458876362,1.07500000000001,-1.04930582958868));
#4392=CARTESIAN_POINT('',(0.759951458876362,-0.800000000000023,-1.04930582958868));
#4393=CARTESIAN_POINT('',(0.759951458876362,-0.800000000000023,-1.04930582958868));
#4394=CARTESIAN_POINT('',(0.759951458876362,1.07500000000001,-1.04930582958868));
#4395=CARTESIAN_POINT('',(0.759951458876362,-0.499999999999945,-1.04930582958868));
#4396=CARTESIAN_POINT('',(0.759951458876362,-0.499999999999945,-1.04930582958868));
#4397=CARTESIAN_POINT('',(0.650219413347808,1.07500000000001,-1.05697904170043));
#4398=CARTESIAN_POINT('',(0.650219413347808,-0.800000000000023,-1.05697904170043));
#4399=CARTESIAN_POINT('',(0.650219413347808,-0.800000000000023,-1.05697904170043));
#4400=CARTESIAN_POINT('',(0.650219413347808,1.07500000000001,-1.05697904170043));
#4401=CARTESIAN_POINT('',(0.650219413347808,-0.499999999999945,-1.05697904170043));
#4402=CARTESIAN_POINT('',(0.650219413347808,-0.499999999999945,-1.05697904170043));
#4403=CARTESIAN_POINT('',(0.650219413347808,1.07500000000001,-1.05697904170043));
#4404=CARTESIAN_POINT('',(0.650219413347808,-0.800000000000023,-1.05697904170043));
#4405=CARTESIAN_POINT('',(0.638286282100886,-0.800000000000023,-0.886327314341129));
#4406=CARTESIAN_POINT('',(0.638286282100886,1.07500000000001,-0.886327314341129));
#4407=CARTESIAN_POINT('',(0.638286282100886,-0.499999999999945,-0.886327314341129));
#4408=CARTESIAN_POINT('',(0.650219413347808,-0.499999999999945,-1.05697904170043));
#4409=CARTESIAN_POINT('',(0.598383720090523,1.07500000000001,-0.88911757329091));
#4410=CARTESIAN_POINT('',(0.598383720090523,-0.800000000000023,-0.88911757329091));
#4411=CARTESIAN_POINT('',(0.601869949800417,-0.800000000000023,-0.849269785367267));
#4412=CARTESIAN_POINT('',(0.601869949800417,1.07500000000001,-0.849269785367265));
#4413=CARTESIAN_POINT('',(0.601869949800417,-0.499999999999945,-0.849269785367267));
#4414=CARTESIAN_POINT('',(0.598383720090523,-0.499999999999945,-0.88911757329091));
#4415=CARTESIAN_POINT('',(0.146055081160514,1.07500000000001,-0.809391151694827));
#4416=CARTESIAN_POINT('',(0.146055081160514,-0.800000000000023,-0.809391151694826));
#4417=CARTESIAN_POINT('',(0.146055081160514,-0.800000000000023,-0.809391151694826));
#4418=CARTESIAN_POINT('',(0.146055081160514,1.07500000000001,-0.809391151694827));
#4419=CARTESIAN_POINT('',(0.146055081160514,-0.499999999999945,-0.809391151694826));
#4420=CARTESIAN_POINT('',(0.146055081160514,-0.499999999999945,-0.809391151694826));
#4421=CARTESIAN_POINT('',(1.66533453693773E-13,1.07500000000001,-0.650000000000095));
#4422=CARTESIAN_POINT('',(2.22380805577858E-17,-0.800000000000023,-0.436824583655182));
#4423=CARTESIAN_POINT('',(2.22380805577861E-17,-0.800000000000027,-0.436824583655182));
#4424=CARTESIAN_POINT('',(9.18485099360073E-18,-0.800000000000023,-0.650000000000095));
#4425=CARTESIAN_POINT('',(2.52996975556539E-17,-0.850000000000017,-0.436824583655182));
#4426=CARTESIAN_POINT('',(1.56170932744392E-13,-0.837499999999878,-0.388412291827635));
#4427=CARTESIAN_POINT('',(2.96976848793207E-17,-0.824999999999632,-0.339999999999674));
#4428=CARTESIAN_POINT('',(3.27593018772092E-17,-0.874999999999959,-0.339999999999674));
#4429=CARTESIAN_POINT('',(7.62342632469225E-17,-0.874999999999959,0.370000000000037));
#4430=CARTESIAN_POINT('',(7.62342632469191E-17,-0.874999999999904,0.370000000000037));
#4431=CARTESIAN_POINT('',(7.1335676050335E-17,-0.79499999999999,0.370000000000037));
#4432=CARTESIAN_POINT('',(7.62342632469191E-17,-0.79499999999999,0.44999999999995));
#4433=CARTESIAN_POINT('',(6.70494122533184E-17,-0.645000000000007,0.450000000000006));
#4434=CARTESIAN_POINT('',(6.70494122533184E-17,-0.645000000000007,0.450000000000006));
#4435=CARTESIAN_POINT('',(3.3739019316511E-17,-0.645000000000007,-0.093999999999983));
#4436=CARTESIAN_POINT('',(3.37390193165111E-17,-0.645000000000007,-0.0939999999999825));
#4437=CARTESIAN_POINT('',(2.81889275065907E-17,-0.60520879389625,-0.144848671676245));
#4438=CARTESIAN_POINT('',(3.36837109242344E-17,-0.658520941226315,-0.108424195818801));
#4439=CARTESIAN_POINT('',(3.35316700938888E-17,-0.695689534396609,-0.148075804180928));
#4440=CARTESIAN_POINT('',(3.35316700938915E-17,-0.695689534396632,-0.148075804180907));
#4441=CARTESIAN_POINT('',(2.92041497875384E-17,-0.659210475623118,-0.182270515336258));
#4442=CARTESIAN_POINT('',(3.22657667854065E-17,-0.709210475623112,-0.182270515336258));
#4443=CARTESIAN_POINT('',(2.43833592179425E-17,-0.709210475623112,-0.31099999999995));
#4444=CARTESIAN_POINT('',(2.43833592179425E-17,-0.709210475623112,-0.31099999999995));
#4445=CARTESIAN_POINT('',(-4.89858719659432E-19,-0.499999999999945,-0.507999999999953));
#4446=CARTESIAN_POINT('',(-4.89858719659432E-19,-0.499999999999945,-0.507999999999953));
#4447=CARTESIAN_POINT('',(-6.79678973526815E-17,-0.499999999999945,-1.61));
#4448=CARTESIAN_POINT('',(-9.18485099361434E-18,-0.499999999999945,-0.650000000000095));
#4449=CARTESIAN_POINT('',(1.66533453693773E-13,1.07500000000001,-0.650000000000095));
#4450=CARTESIAN_POINT('',(0.160000000000105,1.07500000000001,-0.650000000000095));
#4451=CARTESIAN_POINT('',(0.160000000000105,-0.800000000000023,-0.650000000000095));
#4452=CARTESIAN_POINT('',(0.160000000000105,-0.499999999999945,-0.650000000000095));
#4453=CARTESIAN_POINT('',(1.02240612516445,-0.850000000000017,-0.436824583655182));
#4454=CARTESIAN_POINT('',(1.02240612516445,-0.837499999999855,-0.388412291827547));
#4455=CARTESIAN_POINT('',(1.02240612516445,-0.800000000000027,-0.436824583655182));
#4456=CARTESIAN_POINT('',(1.02240612516445,-0.824999999999631,-0.339999999999674));
#4457=CARTESIAN_POINT('',(1.02240612516445,-0.874999999999959,-0.339999999999674));
#4458=CARTESIAN_POINT('',(1.02240612516445,-0.874999999999959,0.370000000000037));
#4459=CARTESIAN_POINT('',(1.02240612516445,-0.874999999999904,0.370000000000037));
#4460=CARTESIAN_POINT('',(1.02240612516445,-0.79499999999999,0.370000000000037));
#4461=CARTESIAN_POINT('',(1.02240612516445,-0.79499999999999,0.44999999999995));
#4462=CARTESIAN_POINT('',(1.02240612516445,-0.645000000000007,0.450000000000006));
#4463=CARTESIAN_POINT('',(1.02240612516445,-0.645000000000007,0.450000000000006));
#4464=CARTESIAN_POINT('',(1.02240612516445,-0.645000000000007,-0.0939999999999831));
#4465=CARTESIAN_POINT('',(1.02240612516445,-0.645000000000007,-0.0939999999999826));
#4466=CARTESIAN_POINT('',(1.02240612516445,-0.60520879389625,-0.144848671676245));
#4467=CARTESIAN_POINT('',(1.02240612516445,-0.658520941226315,-0.108424195818802));
#4468=CARTESIAN_POINT('',(1.02240612516445,-0.695689534396609,-0.148075804180928));
#4469=CARTESIAN_POINT('',(1.02240612516445,-0.695689534396632,-0.148075804180907));
#4470=CARTESIAN_POINT('',(1.02240612516445,-0.659210475623118,-0.182270515336258));
#4471=CARTESIAN_POINT('',(1.02240612516445,-0.709210475623112,-0.182270515336258));
#4472=CARTESIAN_POINT('',(1.02240612516445,-0.709210475623112,-0.31099999999995));
#4473=CARTESIAN_POINT('',(1.02240612516445,-0.709210475623112,-0.31099999999995));
#4474=CARTESIAN_POINT('',(1.02240612516445,-0.499999999999945,-0.507999999999953));
#4475=CARTESIAN_POINT('',(1.02240612516445,-0.499999999999945,-0.507999999999953));
#4476=CARTESIAN_POINT('',(1.02240612516445,-0.499999999999945,-1.61));
#4477=CARTESIAN_POINT('',(1.02240612516445,-0.800000000000023,-0.436824583655182));
#4478=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,0.649999999999984));
#4479=CARTESIAN_POINT('',(0.11000000000011,-0.149999999999971,0.65000000000011));
#4480=CARTESIAN_POINT('',(0.11000000000011,-0.149999999999983,0.529999999999975));
#4481=CARTESIAN_POINT('',(0.11000000000009,-0.149999999999971,0.649999999999984));
#4482=CARTESIAN_POINT('',(0.11000000000011,-0.229999999999952,0.529999999999975));
#4483=CARTESIAN_POINT('',(0.11000000000011,-0.229999999999952,0.450000000000006));
#4484=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,0.450000000000006));
#4485=CARTESIAN_POINT('',(0.11000000000011,-0.520000000000076,0.450000000000006));
#4486=CARTESIAN_POINT('',(0.11000000000011,-0.520000000000076,0.649999999999984));
#4487=CARTESIAN_POINT('',(0.11000000000011,-0.520000000000076,-0.350000000000017));
#4488=CARTESIAN_POINT('',(0.11000000000011,-0.222499999998707,-0.647500000000891));
#4489=CARTESIAN_POINT('',(0.11000000000011,-0.419999999999976,-0.44999999999995));
#4490=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,-0.449999999999951));
#4491=CARTESIAN_POINT('',(0.11000000000011,0.419999999999976,-0.44999999999995));
#4492=CARTESIAN_POINT('',(0.11000000000011,1.2975000000005,0.427499999999117));
#4493=CARTESIAN_POINT('',(0.11000000000011,0.520000000000076,-0.350000000000017));
#4494=CARTESIAN_POINT('',(0.11000000000011,0.520000000000076,0.649999999999984));
#4495=CARTESIAN_POINT('',(0.11000000000011,0.520000000000076,0.450000000000006));
#4496=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,0.450000000000006));
#4497=CARTESIAN_POINT('',(0.11000000000011,0.229999999999952,0.450000000000006));
#4498=CARTESIAN_POINT('',(0.11000000000011,0.229999999999952,0.529999999999975));
#4499=CARTESIAN_POINT('',(0.11000000000011,0.149999999999983,0.529999999999975));
#4500=CARTESIAN_POINT('',(0.11000000000011,0.149999999999983,0.649999999999984));
#4501=CARTESIAN_POINT('',(0.11000000000009,0.149999999999983,0.649999999999984));
#4502=CARTESIAN_POINT('',(0.11000000000011,1.07500000000001,0.649999999999984));
#4503=CARTESIAN_POINT('',(1.66533453693773E-13,1.07500000000001,-0.650000000000095));
#4504=CARTESIAN_POINT('',(1.07768918324963E-16,-0.149999999999872,1.61000000000006));
#4505=CARTESIAN_POINT('',(4.89858719658914E-17,-0.149999999999971,0.649999999999984));
#4506=CARTESIAN_POINT('',(4.16379911710075E-17,-0.149999999999983,0.529999999999975));
#4507=CARTESIAN_POINT('',(1.15250690935209E-13,1.07500000000001,0.649999999999984));
#4508=CARTESIAN_POINT('',(3.06161699786839E-17,0.149999999999983,0.649999999999984));
#4509=CARTESIAN_POINT('',(2.32682891837992E-17,0.149999999999983,0.529999999999975));
#4510=CARTESIAN_POINT('',(1.2008289661781E-13,0.149999999999983,0.529999999999975));
#4511=CARTESIAN_POINT('',(1.83697019872117E-17,0.229999999999952,0.529999999999975));
#4512=CARTESIAN_POINT('',(1.34711147906242E-17,0.229999999999952,0.450000000000006));
#4513=CARTESIAN_POINT('',(-4.28626379702002E-18,0.520000000000076,0.450000000000006));
#4514=CARTESIAN_POINT('',(-4.28626379702002E-18,0.520000000000076,0.450000000000006));
#4515=CARTESIAN_POINT('',(-5.32721357629156E-17,0.520000000000076,-0.350000000000017));
#4516=CARTESIAN_POINT('',(-5.32721357629156E-17,0.520000000000076,-0.350000000000017));
#4517=CARTESIAN_POINT('',(-5.32721357629054E-17,0.419999999999976,-0.44999999999995));
#4518=CARTESIAN_POINT('',(-5.32721357629054E-17,0.419999999999976,-0.44999999999995));
#4519=CARTESIAN_POINT('',(-1.83697019871947E-18,-0.419999999999976,-0.44999999999995));
#4520=CARTESIAN_POINT('',(-1.83697019871947E-18,-0.419999999999976,-0.44999999999995));
#4521=CARTESIAN_POINT('',(1.04094977927561E-17,-0.520000000000076,-0.350000000000017));
#4522=CARTESIAN_POINT('',(1.04094977927561E-17,-0.520000000000076,-0.350000000000017));
#4523=CARTESIAN_POINT('',(5.93953697586516E-17,-0.520000000000076,0.450000000000006));
#4524=CARTESIAN_POINT('',(5.93953697586516E-17,-0.520000000000076,0.450000000000006));
#4525=CARTESIAN_POINT('',(4.16379911710075E-17,-0.229999999999952,0.450000000000006));
#4526=CARTESIAN_POINT('',(4.16379911710075E-17,-0.229999999999952,0.450000000000006));
#4527=CARTESIAN_POINT('',(4.6536578367595E-17,-0.229999999999952,0.529999999999975));
#4528=CARTESIAN_POINT('',(0.160000000000105,1.07500000000001,0.649999999999984));
#4529=CARTESIAN_POINT('',(0.160000000000105,-0.149999999999971,0.649999999999984));
#4530=CARTESIAN_POINT('',(0.155642212862836,-0.149999999999966,0.699809734904586));
#4531=CARTESIAN_POINT('',(0.160000000000105,0.149999999999983,0.649999999999984));
#4532=CARTESIAN_POINT('',(0.155642212862836,0.149999999999983,0.699809734904586));
#4533=CARTESIAN_POINT('',(0.155642212862836,1.07500000000001,0.699809734904595));
#4534=CARTESIAN_POINT('',(0.155642212862837,1.07500000000001,0.699809734904586));
#4535=CARTESIAN_POINT('',(0.155642212862848,-0.149999999999966,0.699809734904587));
#4536=CARTESIAN_POINT('',(0.611457081502656,-0.149999999999962,0.739688368577077));
#4537=CARTESIAN_POINT('',(0.155642212862837,0.149999999999983,0.699809734904586));
#4538=CARTESIAN_POINT('',(0.611457081502656,0.149999999999983,0.739688368577077));
#4539=CARTESIAN_POINT('',(0.611457081502656,1.07500000000001,0.739688368577077));
#4540=CARTESIAN_POINT('',(0.598383720090523,1.07500000000001,0.889117573290854));
#4541=CARTESIAN_POINT('',(0.598383720090523,-0.149999999999947,0.889117573290854));
#4542=CARTESIAN_POINT('',(0.74801832762951,-0.149999999999948,0.878654102229233));
#4543=CARTESIAN_POINT('',(0.598383720090523,0.149999999999983,0.889117573290854));
#4544=CARTESIAN_POINT('',(0.74801832762951,0.149999999999983,0.878654102229233));
#4545=CARTESIAN_POINT('',(0.74801832762951,1.07500000000001,0.878654102229233));
#4546=CARTESIAN_POINT('',(0.759951221522004,1.07500000000001,1.04930243526252));
#4547=CARTESIAN_POINT('',(0.759951221522013,-0.14999999999993,1.04930243526265));
#4548=CARTESIAN_POINT('',(0.759951221522004,-0.14999999999993,1.04930243526252));
#4549=CARTESIAN_POINT('',(0.759951221522004,0.149999999999983,1.04930243526252));
#4550=CARTESIAN_POINT('',(0.759951221522004,0.149999999999983,1.04930243526252));
#4551=CARTESIAN_POINT('',(0.759951221522004,1.07500000000001,1.04930243526252));
#4552=CARTESIAN_POINT('',(0.650219175993338,1.07500000000001,1.05697564737445));
#4553=CARTESIAN_POINT('',(0.65021917599333,-0.149999999999929,1.05697564737445));
#4554=CARTESIAN_POINT('',(0.650219175993338,-0.149999999999929,1.05697564737445));
#4555=CARTESIAN_POINT('',(0.650219175993339,0.149999999999983,1.05697564737445));
#4556=CARTESIAN_POINT('',(0.650219175993338,0.149999999999983,1.05697564737445));
#4557=CARTESIAN_POINT('',(0.650219175993338,1.07500000000001,1.05697564737445));
#4558=CARTESIAN_POINT('',(0.650219175993338,1.07500000000001,1.05697564737445));
#4559=CARTESIAN_POINT('',(0.650219175993347,-0.149999999999929,1.05697564737457));
#4560=CARTESIAN_POINT('',(0.6382862821009,-0.149999999999947,0.8863273143411));
#4561=CARTESIAN_POINT('',(0.650219175993338,0.149999999999983,1.05697564737445));
#4562=CARTESIAN_POINT('',(0.6382862821009,0.149999999999983,0.8863273143411));
#4563=CARTESIAN_POINT('',(0.638286282100884,1.07500000000001,0.886327314341101));
#4564=CARTESIAN_POINT('',(0.598383720090523,1.07500000000001,0.889117573290854));
#4565=CARTESIAN_POINT('',(0.598383720090523,-0.149999999999947,0.889117573290854));
#4566=CARTESIAN_POINT('',(0.601869949800438,-0.149999999999951,0.849269785367202));
#4567=CARTESIAN_POINT('',(0.598383720090523,0.149999999999983,0.889117573290854));
#4568=CARTESIAN_POINT('',(0.601869949800438,0.149999999999984,0.849269785367202));
#4569=CARTESIAN_POINT('',(0.601869949800438,1.07500000000001,0.849269785367201));
#4570=CARTESIAN_POINT('',(0.146055081160477,1.07500000000001,0.809391151694633));
#4571=CARTESIAN_POINT('',(0.146055081160488,-0.149999999999955,0.809391151694634));
#4572=CARTESIAN_POINT('',(0.146055081160475,-0.149999999999955,0.809391151694652));
#4573=CARTESIAN_POINT('',(0.146055081160477,0.149999999999983,0.809391151694633));
#4574=CARTESIAN_POINT('',(0.146055081160475,0.149999999999983,0.809391151694652));
#4575=CARTESIAN_POINT('',(0.146055081160477,1.07500000000001,0.809391151694633));
#4576=CARTESIAN_POINT('',(0.160000000000105,1.07500000000001,0.649999999999984));
#4577=CARTESIAN_POINT('',(0.160000000000105,-0.149999999999971,0.649999999999984));
#4578=CARTESIAN_POINT('',(0.160000000000105,0.149999999999983,0.649999999999984));
#4579=CARTESIAN_POINT('',(1.02240612516445,-0.149999999999872,1.61000000000006));
#4580=CARTESIAN_POINT('',(1.02240612516445,-0.149999999999983,0.529999999999975));
#4581=CARTESIAN_POINT('',(1.02240612516445,-0.229999999999952,0.529999999999975));
#4582=CARTESIAN_POINT('',(1.02240612516445,-0.229999999999952,0.450000000000006));
#4583=CARTESIAN_POINT('',(1.02240612516445,-0.229999999999952,0.450000000000006));
#4584=CARTESIAN_POINT('',(1.02240612516445,-0.520000000000076,0.450000000000006));
#4585=CARTESIAN_POINT('',(1.02240612516445,-0.520000000000076,0.450000000000006));
#4586=CARTESIAN_POINT('',(1.02240612516445,-0.520000000000076,-0.350000000000017));
#4587=CARTESIAN_POINT('',(1.02240612516445,-0.520000000000076,-0.350000000000017));
#4588=CARTESIAN_POINT('',(1.02240612516445,-0.419999999999976,-0.44999999999995));
#4589=CARTESIAN_POINT('',(1.02240612516445,-0.419999999999976,-0.44999999999995));
#4590=CARTESIAN_POINT('',(1.02240612516445,0.419999999999976,-0.44999999999995));
#4591=CARTESIAN_POINT('',(1.02240612516445,0.419999999999976,-0.44999999999995));
#4592=CARTESIAN_POINT('',(1.02240612516445,0.520000000000076,-0.350000000000017));
#4593=CARTESIAN_POINT('',(1.02240612516445,0.520000000000076,-0.350000000000017));
#4594=CARTESIAN_POINT('',(1.02240612516445,0.520000000000076,0.450000000000006));
#4595=CARTESIAN_POINT('',(1.02240612516445,0.520000000000076,0.450000000000006));
#4596=CARTESIAN_POINT('',(1.02240612516445,0.229999999999953,0.450000000000006));
#4597=CARTESIAN_POINT('',(1.02240612516445,0.229999999999953,0.529999999999975));
#4598=CARTESIAN_POINT('',(1.02240612516445,0.149999999999984,0.529999999999975));
#4599=CARTESIAN_POINT('',(1.02240612516445,0.149999999999984,0.529999999999975));
#4600=CARTESIAN_POINT('',(0.110000000000027,-0.00124738251962341,-0.00963183855592561));
#4601=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#2719,
#2720,#2721,#2722),#4602);
#4602=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4605))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4610,#4609,#4608))
REPRESENTATION_CONTEXT('SOT-323','TOP_LEVEL_ASSEMBLY_PART')
);
#4603=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4606))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4610,#4609,#4608))
REPRESENTATION_CONTEXT('Compound-New','COMPONENT_PART')
);
#4604=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4607))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4610,#4609,#4608))
REPRESENTATION_CONTEXT('LDF-New','COMPONENT_PART')
);
#4605=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#4610,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#4606=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#4610,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#4607=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#4610,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#4608=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#4609=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#4610=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#4611=PRODUCT_DEFINITION_SHAPE('','',#4616);
#4612=PRODUCT_DEFINITION_SHAPE('','',#4617);
#4613=PRODUCT_DEFINITION_SHAPE(' ','NAUO PRDDFN',#20);
#4614=PRODUCT_DEFINITION_SHAPE('','',#4618);
#4615=PRODUCT_DEFINITION_SHAPE(' ','NAUO PRDDFN',#21);
#4616=PRODUCT_DEFINITION('','',#4622,#4619);
#4617=PRODUCT_DEFINITION('','',#4623,#4620);
#4618=PRODUCT_DEFINITION('','',#4624,#4621);
#4619=PRODUCT_DEFINITION_CONTEXT('',#4638,'design');
#4620=PRODUCT_DEFINITION_CONTEXT('',#4638,'design');
#4621=PRODUCT_DEFINITION_CONTEXT('',#4638,'design');
#4622=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#4628,
 .NOT_KNOWN.);
#4623=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#4629,
 .NOT_KNOWN.);
#4624=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#4630,
 .NOT_KNOWN.);
#4625=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#4628));
#4626=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#4629));
#4627=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#4630));
#4628=PRODUCT('SOT-323','SOT-323','SOT-323',(#4634));
#4629=PRODUCT('Compound-New','Compound-New','Compound-New',(#4635));
#4630=PRODUCT('LDF-New','LDF-New','LDF-New',(#4636));
#4631=PRODUCT_CATEGORY('','');
#4632=PRODUCT_CATEGORY('','');
#4633=PRODUCT_CATEGORY('','');
#4634=PRODUCT_CONTEXT('',#4638,'mechanical');
#4635=PRODUCT_CONTEXT('',#4638,'mechanical');
#4636=PRODUCT_CONTEXT('',#4638,'mechanical');
#4637=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2010,#4638);
#4638=APPLICATION_CONTEXT(
'core data for automotive mechanical design processes');
ENDSEC;
END-ISO-10303-21;
