ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */
/* OPTION: strings as raw bytes, not using required /X/ escapes */

FILE_DESCRIPTION(
/* description */ ('Unknown'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'SOT-25',
/* time_stamp */ '2020-04-15T14:44:59+08:00',
/* author */ ('Unknown'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ST-DEVELOPER v16.7',
/* originating_system */ 'DEX',
/* authorisation */ $);

FILE_SCHEMA (('AUTOMOTIVE_DESIGN {1 0 10303 214 3 1 1}'));
ENDSEC;

DATA;
#10=PROPERTY_DEFINITION_REPRESENTATION(#14,#12);
#11=PROPERTY_DEFINITION_REPRESENTATION(#15,#13);
#12=REPRESENTATION('',(#16),#3089);
#13=REPRESENTATION('',(#17),#3089);
#14=PROPERTY_DEFINITION('pmi validation property','',#3098);
#15=PROPERTY_DEFINITION('pmi validation property','',#3098);
#16=VALUE_REPRESENTATION_ITEM('number of annotations',COUNT_MEASURE(0.));
#17=VALUE_REPRESENTATION_ITEM('number of views',COUNT_MEASURE(0.));
#18=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#22,#3100);
#19=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#23,#3102);
#20=NEXT_ASSEMBLY_USAGE_OCCURRENCE('CPD','CPD','CPD',#3103,#3104,'');
#21=NEXT_ASSEMBLY_USAGE_OCCURRENCE('LDF','LDF','LDF',#3103,#3105,'');
#22=(
REPRESENTATION_RELATIONSHIP(' ',' ',#1993,#1994)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#24)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#23=(
REPRESENTATION_RELATIONSHIP(' ',' ',#1995,#1994)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#25)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#24=ITEM_DEFINED_TRANSFORMATION(' ',' ',#1996,#2059);
#25=ITEM_DEFINED_TRANSFORMATION(' ',' ',#1996,#2158);
#26=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1993,#28);
#27=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1995,#29);
#28=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1986),#3090);
#29=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1987,#1988,#1989),#3091);
#30=ELLIPSE('',#2034,0.101496408645441,0.0999999999999999);
#31=ELLIPSE('',#2041,0.101496408645441,0.0999999999999999);
#32=ELLIPSE('',#2048,0.101496408645441,0.0999999999999999);
#33=ELLIPSE('',#2055,0.101496408645441,0.0999999999999999);
#34=SPHERICAL_SURFACE('',#2031,0.0999999999999999);
#35=SPHERICAL_SURFACE('',#2037,0.0999999999999999);
#36=SPHERICAL_SURFACE('',#2038,0.0999999999999999);
#37=SPHERICAL_SURFACE('',#2044,0.0999999999999999);
#38=SPHERICAL_SURFACE('',#2045,0.0999999999999999);
#39=SPHERICAL_SURFACE('',#2051,0.0999999999999999);
#40=SPHERICAL_SURFACE('',#2052,0.0999999999999999);
#41=SPHERICAL_SURFACE('',#2058,0.0999999999999999);
#42=CIRCLE('',#2008,0.0999999999999998);
#43=CIRCLE('',#2009,0.0999999999999998);
#44=CIRCLE('',#2011,0.0999999999999998);
#45=CIRCLE('',#2012,0.0999999999999998);
#46=CIRCLE('',#2014,0.0999999999999998);
#47=CIRCLE('',#2015,0.0999999999999998);
#48=CIRCLE('',#2017,0.0999999999999998);
#49=CIRCLE('',#2018,0.0999999999999998);
#50=CIRCLE('',#2020,0.0999999999999998);
#51=CIRCLE('',#2021,0.0999999999999998);
#52=CIRCLE('',#2023,0.0999999999999998);
#53=CIRCLE('',#2024,0.0999999999999998);
#54=CIRCLE('',#2026,0.0999999999999998);
#55=CIRCLE('',#2027,0.0999999999999998);
#56=CIRCLE('',#2029,0.0999999999999998);
#57=CIRCLE('',#2030,0.0999999999999998);
#58=CIRCLE('',#2032,0.0999999999999999);
#59=CIRCLE('',#2036,0.0999999999999999);
#60=CIRCLE('',#2039,0.0999999999999999);
#61=CIRCLE('',#2043,0.0999999999999999);
#62=CIRCLE('',#2046,0.0999999999999999);
#63=CIRCLE('',#2050,0.0999999999999999);
#64=CIRCLE('',#2053,0.0999999999999999);
#65=CIRCLE('',#2057,0.0999999999999999);
#66=CIRCLE('',#2064,0.270000000000022);
#67=CIRCLE('',#2065,0.270000000000022);
#68=CIRCLE('',#2068,0.0999999999999034);
#69=CIRCLE('',#2069,0.0999999999999034);
#70=CIRCLE('',#2072,0.100000000000003);
#71=CIRCLE('',#2073,0.100000000000003);
#72=CIRCLE('',#2076,0.270000000000003);
#73=CIRCLE('',#2077,0.270000000000003);
#74=CIRCLE('',#2082,0.099999999999994);
#75=CIRCLE('',#2083,0.099999999999994);
#76=CIRCLE('',#2086,0.269999999999992);
#77=CIRCLE('',#2087,0.269999999999992);
#78=CIRCLE('',#2090,0.270000000000018);
#79=CIRCLE('',#2091,0.270000000000018);
#80=CIRCLE('',#2094,0.0999999999999034);
#81=CIRCLE('',#2095,0.0999999999999034);
#82=CIRCLE('',#2101,0.0999999999999034);
#83=CIRCLE('',#2102,0.270000000000022);
#84=CIRCLE('',#2103,0.0999999999999034);
#85=CIRCLE('',#2104,0.270000000000018);
#86=CIRCLE('',#2109,0.270000000000022);
#87=CIRCLE('',#2112,0.0999999999999034);
#88=CIRCLE('',#2114,0.270000000000018);
#89=CIRCLE('',#2117,0.0999999999999034);
#90=CIRCLE('',#2124,0.270000000000022);
#91=CIRCLE('',#2125,0.270000000000022);
#92=CIRCLE('',#2128,0.0999999999999034);
#93=CIRCLE('',#2129,0.0999999999999034);
#94=CIRCLE('',#2132,0.100000000000003);
#95=CIRCLE('',#2133,0.100000000000003);
#96=CIRCLE('',#2136,0.270000000000003);
#97=CIRCLE('',#2137,0.270000000000003);
#98=CIRCLE('',#2142,0.099999999999994);
#99=CIRCLE('',#2143,0.099999999999994);
#100=CIRCLE('',#2146,0.269999999999992);
#101=CIRCLE('',#2147,0.269999999999992);
#102=CIRCLE('',#2150,0.270000000000018);
#103=CIRCLE('',#2151,0.270000000000018);
#104=CIRCLE('',#2154,0.0999999999999034);
#105=CIRCLE('',#2155,0.0999999999999034);
#106=CYLINDRICAL_SURFACE('',#2007,0.0999999999999998);
#107=CYLINDRICAL_SURFACE('',#2010,0.0999999999999998);
#108=CYLINDRICAL_SURFACE('',#2013,0.0999999999999998);
#109=CYLINDRICAL_SURFACE('',#2016,0.0999999999999998);
#110=CYLINDRICAL_SURFACE('',#2019,0.0999999999999998);
#111=CYLINDRICAL_SURFACE('',#2022,0.0999999999999998);
#112=CYLINDRICAL_SURFACE('',#2025,0.0999999999999998);
#113=CYLINDRICAL_SURFACE('',#2028,0.0999999999999998);
#114=CYLINDRICAL_SURFACE('',#2033,0.0999999999999999);
#115=CYLINDRICAL_SURFACE('',#2035,0.0999999999999999);
#116=CYLINDRICAL_SURFACE('',#2040,0.0999999999999999);
#117=CYLINDRICAL_SURFACE('',#2042,0.0999999999999999);
#118=CYLINDRICAL_SURFACE('',#2047,0.0999999999999999);
#119=CYLINDRICAL_SURFACE('',#2049,0.0999999999999999);
#120=CYLINDRICAL_SURFACE('',#2054,0.0999999999999999);
#121=CYLINDRICAL_SURFACE('',#2056,0.0999999999999999);
#122=CYLINDRICAL_SURFACE('',#2063,0.270000000000022);
#123=CYLINDRICAL_SURFACE('',#2067,0.0999999999999034);
#124=CYLINDRICAL_SURFACE('',#2071,0.100000000000003);
#125=CYLINDRICAL_SURFACE('',#2075,0.270000000000003);
#126=CYLINDRICAL_SURFACE('',#2081,0.099999999999994);
#127=CYLINDRICAL_SURFACE('',#2085,0.269999999999992);
#128=CYLINDRICAL_SURFACE('',#2089,0.270000000000018);
#129=CYLINDRICAL_SURFACE('',#2093,0.0999999999999034);
#130=CYLINDRICAL_SURFACE('',#2108,0.270000000000022);
#131=CYLINDRICAL_SURFACE('',#2111,0.0999999999999034);
#132=CYLINDRICAL_SURFACE('',#2113,0.270000000000018);
#133=CYLINDRICAL_SURFACE('',#2116,0.0999999999999034);
#134=CYLINDRICAL_SURFACE('',#2123,0.270000000000022);
#135=CYLINDRICAL_SURFACE('',#2127,0.0999999999999034);
#136=CYLINDRICAL_SURFACE('',#2131,0.100000000000003);
#137=CYLINDRICAL_SURFACE('',#2135,0.270000000000003);
#138=CYLINDRICAL_SURFACE('',#2141,0.099999999999994);
#139=CYLINDRICAL_SURFACE('',#2145,0.269999999999992);
#140=CYLINDRICAL_SURFACE('',#2149,0.270000000000018);
#141=CYLINDRICAL_SURFACE('',#2153,0.0999999999999034);
#142=ORIENTED_EDGE('',*,*,#582,.T.);
#143=ORIENTED_EDGE('',*,*,#583,.T.);
#144=ORIENTED_EDGE('',*,*,#584,.T.);
#145=ORIENTED_EDGE('',*,*,#585,.T.);
#146=ORIENTED_EDGE('',*,*,#586,.F.);
#147=ORIENTED_EDGE('',*,*,#587,.T.);
#148=ORIENTED_EDGE('',*,*,#588,.T.);
#149=ORIENTED_EDGE('',*,*,#589,.T.);
#150=ORIENTED_EDGE('',*,*,#590,.T.);
#151=ORIENTED_EDGE('',*,*,#591,.T.);
#152=ORIENTED_EDGE('',*,*,#592,.F.);
#153=ORIENTED_EDGE('',*,*,#593,.T.);
#154=ORIENTED_EDGE('',*,*,#594,.T.);
#155=ORIENTED_EDGE('',*,*,#595,.T.);
#156=ORIENTED_EDGE('',*,*,#596,.F.);
#157=ORIENTED_EDGE('',*,*,#597,.T.);
#158=ORIENTED_EDGE('',*,*,#598,.T.);
#159=ORIENTED_EDGE('',*,*,#599,.T.);
#160=ORIENTED_EDGE('',*,*,#600,.F.);
#161=ORIENTED_EDGE('',*,*,#601,.T.);
#162=ORIENTED_EDGE('',*,*,#602,.T.);
#163=ORIENTED_EDGE('',*,*,#603,.T.);
#164=ORIENTED_EDGE('',*,*,#604,.T.);
#165=ORIENTED_EDGE('',*,*,#605,.T.);
#166=ORIENTED_EDGE('',*,*,#606,.T.);
#167=ORIENTED_EDGE('',*,*,#607,.T.);
#168=ORIENTED_EDGE('',*,*,#586,.T.);
#169=ORIENTED_EDGE('',*,*,#608,.T.);
#170=ORIENTED_EDGE('',*,*,#592,.T.);
#171=ORIENTED_EDGE('',*,*,#609,.T.);
#172=ORIENTED_EDGE('',*,*,#610,.T.);
#173=ORIENTED_EDGE('',*,*,#611,.T.);
#174=ORIENTED_EDGE('',*,*,#596,.T.);
#175=ORIENTED_EDGE('',*,*,#612,.T.);
#176=ORIENTED_EDGE('',*,*,#613,.T.);
#177=ORIENTED_EDGE('',*,*,#614,.T.);
#178=ORIENTED_EDGE('',*,*,#600,.T.);
#179=ORIENTED_EDGE('',*,*,#615,.T.);
#180=ORIENTED_EDGE('',*,*,#616,.T.);
#181=ORIENTED_EDGE('',*,*,#617,.T.);
#182=ORIENTED_EDGE('',*,*,#610,.F.);
#183=ORIENTED_EDGE('',*,*,#618,.T.);
#184=ORIENTED_EDGE('',*,*,#605,.F.);
#185=ORIENTED_EDGE('',*,*,#619,.T.);
#186=ORIENTED_EDGE('',*,*,#613,.F.);
#187=ORIENTED_EDGE('',*,*,#620,.T.);
#188=ORIENTED_EDGE('',*,*,#602,.F.);
#189=ORIENTED_EDGE('',*,*,#621,.T.);
#190=ORIENTED_EDGE('',*,*,#604,.F.);
#191=ORIENTED_EDGE('',*,*,#622,.T.);
#192=ORIENTED_EDGE('',*,*,#606,.F.);
#193=ORIENTED_EDGE('',*,*,#623,.T.);
#194=ORIENTED_EDGE('',*,*,#616,.F.);
#195=ORIENTED_EDGE('',*,*,#624,.T.);
#196=ORIENTED_EDGE('',*,*,#603,.F.);
#197=ORIENTED_EDGE('',*,*,#625,.T.);
#198=ORIENTED_EDGE('',*,*,#585,.F.);
#199=ORIENTED_EDGE('',*,*,#626,.T.);
#200=ORIENTED_EDGE('',*,*,#594,.F.);
#201=ORIENTED_EDGE('',*,*,#627,.T.);
#202=ORIENTED_EDGE('',*,*,#582,.F.);
#203=ORIENTED_EDGE('',*,*,#628,.T.);
#204=ORIENTED_EDGE('',*,*,#590,.F.);
#205=ORIENTED_EDGE('',*,*,#629,.T.);
#206=ORIENTED_EDGE('',*,*,#584,.F.);
#207=ORIENTED_EDGE('',*,*,#630,.T.);
#208=ORIENTED_EDGE('',*,*,#598,.F.);
#209=ORIENTED_EDGE('',*,*,#631,.T.);
#210=ORIENTED_EDGE('',*,*,#588,.F.);
#211=ORIENTED_EDGE('',*,*,#632,.T.);
#212=ORIENTED_EDGE('',*,*,#583,.F.);
#213=ORIENTED_EDGE('',*,*,#633,.T.);
#214=ORIENTED_EDGE('',*,*,#619,.F.);
#215=ORIENTED_EDGE('',*,*,#623,.F.);
#216=ORIENTED_EDGE('',*,*,#634,.F.);
#217=ORIENTED_EDGE('',*,*,#634,.T.);
#218=ORIENTED_EDGE('',*,*,#608,.F.);
#219=ORIENTED_EDGE('',*,*,#635,.F.);
#220=ORIENTED_EDGE('',*,*,#611,.F.);
#221=ORIENTED_EDGE('',*,*,#635,.T.);
#222=ORIENTED_EDGE('',*,*,#589,.F.);
#223=ORIENTED_EDGE('',*,*,#636,.F.);
#224=ORIENTED_EDGE('',*,*,#593,.F.);
#225=ORIENTED_EDGE('',*,*,#633,.F.);
#226=ORIENTED_EDGE('',*,*,#629,.F.);
#227=ORIENTED_EDGE('',*,*,#636,.T.);
#228=ORIENTED_EDGE('',*,*,#628,.F.);
#229=ORIENTED_EDGE('',*,*,#627,.F.);
#230=ORIENTED_EDGE('',*,*,#637,.F.);
#231=ORIENTED_EDGE('',*,*,#637,.T.);
#232=ORIENTED_EDGE('',*,*,#597,.F.);
#233=ORIENTED_EDGE('',*,*,#638,.F.);
#234=ORIENTED_EDGE('',*,*,#591,.F.);
#235=ORIENTED_EDGE('',*,*,#638,.T.);
#236=ORIENTED_EDGE('',*,*,#614,.F.);
#237=ORIENTED_EDGE('',*,*,#639,.F.);
#238=ORIENTED_EDGE('',*,*,#609,.F.);
#239=ORIENTED_EDGE('',*,*,#621,.F.);
#240=ORIENTED_EDGE('',*,*,#618,.F.);
#241=ORIENTED_EDGE('',*,*,#639,.T.);
#242=ORIENTED_EDGE('',*,*,#626,.F.);
#243=ORIENTED_EDGE('',*,*,#631,.F.);
#244=ORIENTED_EDGE('',*,*,#640,.F.);
#245=ORIENTED_EDGE('',*,*,#640,.T.);
#246=ORIENTED_EDGE('',*,*,#601,.F.);
#247=ORIENTED_EDGE('',*,*,#641,.F.);
#248=ORIENTED_EDGE('',*,*,#595,.F.);
#249=ORIENTED_EDGE('',*,*,#641,.T.);
#250=ORIENTED_EDGE('',*,*,#617,.F.);
#251=ORIENTED_EDGE('',*,*,#642,.F.);
#252=ORIENTED_EDGE('',*,*,#612,.F.);
#253=ORIENTED_EDGE('',*,*,#625,.F.);
#254=ORIENTED_EDGE('',*,*,#620,.F.);
#255=ORIENTED_EDGE('',*,*,#642,.T.);
#256=ORIENTED_EDGE('',*,*,#630,.F.);
#257=ORIENTED_EDGE('',*,*,#632,.F.);
#258=ORIENTED_EDGE('',*,*,#643,.F.);
#259=ORIENTED_EDGE('',*,*,#643,.T.);
#260=ORIENTED_EDGE('',*,*,#587,.F.);
#261=ORIENTED_EDGE('',*,*,#644,.F.);
#262=ORIENTED_EDGE('',*,*,#599,.F.);
#263=ORIENTED_EDGE('',*,*,#644,.T.);
#264=ORIENTED_EDGE('',*,*,#607,.F.);
#265=ORIENTED_EDGE('',*,*,#645,.F.);
#266=ORIENTED_EDGE('',*,*,#615,.F.);
#267=ORIENTED_EDGE('',*,*,#645,.T.);
#268=ORIENTED_EDGE('',*,*,#622,.F.);
#269=ORIENTED_EDGE('',*,*,#624,.F.);
#270=ORIENTED_EDGE('',*,*,#646,.T.);
#271=ORIENTED_EDGE('',*,*,#647,.F.);
#272=ORIENTED_EDGE('',*,*,#648,.F.);
#273=ORIENTED_EDGE('',*,*,#649,.T.);
#274=ORIENTED_EDGE('',*,*,#650,.T.);
#275=ORIENTED_EDGE('',*,*,#651,.F.);
#276=ORIENTED_EDGE('',*,*,#652,.F.);
#277=ORIENTED_EDGE('',*,*,#647,.T.);
#278=ORIENTED_EDGE('',*,*,#653,.T.);
#279=ORIENTED_EDGE('',*,*,#654,.F.);
#280=ORIENTED_EDGE('',*,*,#655,.F.);
#281=ORIENTED_EDGE('',*,*,#651,.T.);
#282=ORIENTED_EDGE('',*,*,#656,.T.);
#283=ORIENTED_EDGE('',*,*,#657,.F.);
#284=ORIENTED_EDGE('',*,*,#658,.F.);
#285=ORIENTED_EDGE('',*,*,#654,.T.);
#286=ORIENTED_EDGE('',*,*,#659,.T.);
#287=ORIENTED_EDGE('',*,*,#660,.F.);
#288=ORIENTED_EDGE('',*,*,#661,.F.);
#289=ORIENTED_EDGE('',*,*,#657,.T.);
#290=ORIENTED_EDGE('',*,*,#662,.T.);
#291=ORIENTED_EDGE('',*,*,#663,.F.);
#292=ORIENTED_EDGE('',*,*,#664,.F.);
#293=ORIENTED_EDGE('',*,*,#660,.T.);
#294=ORIENTED_EDGE('',*,*,#665,.T.);
#295=ORIENTED_EDGE('',*,*,#666,.F.);
#296=ORIENTED_EDGE('',*,*,#667,.F.);
#297=ORIENTED_EDGE('',*,*,#663,.T.);
#298=ORIENTED_EDGE('',*,*,#668,.T.);
#299=ORIENTED_EDGE('',*,*,#669,.F.);
#300=ORIENTED_EDGE('',*,*,#670,.F.);
#301=ORIENTED_EDGE('',*,*,#666,.T.);
#302=ORIENTED_EDGE('',*,*,#671,.T.);
#303=ORIENTED_EDGE('',*,*,#672,.F.);
#304=ORIENTED_EDGE('',*,*,#673,.F.);
#305=ORIENTED_EDGE('',*,*,#669,.T.);
#306=ORIENTED_EDGE('',*,*,#674,.T.);
#307=ORIENTED_EDGE('',*,*,#675,.F.);
#308=ORIENTED_EDGE('',*,*,#676,.F.);
#309=ORIENTED_EDGE('',*,*,#672,.T.);
#310=ORIENTED_EDGE('',*,*,#677,.T.);
#311=ORIENTED_EDGE('',*,*,#678,.F.);
#312=ORIENTED_EDGE('',*,*,#679,.F.);
#313=ORIENTED_EDGE('',*,*,#675,.T.);
#314=ORIENTED_EDGE('',*,*,#680,.T.);
#315=ORIENTED_EDGE('',*,*,#681,.F.);
#316=ORIENTED_EDGE('',*,*,#682,.F.);
#317=ORIENTED_EDGE('',*,*,#678,.T.);
#318=ORIENTED_EDGE('',*,*,#683,.T.);
#319=ORIENTED_EDGE('',*,*,#684,.F.);
#320=ORIENTED_EDGE('',*,*,#685,.F.);
#321=ORIENTED_EDGE('',*,*,#681,.T.);
#322=ORIENTED_EDGE('',*,*,#686,.F.);
#323=ORIENTED_EDGE('',*,*,#687,.F.);
#324=ORIENTED_EDGE('',*,*,#684,.T.);
#325=ORIENTED_EDGE('',*,*,#688,.T.);
#326=ORIENTED_EDGE('',*,*,#689,.T.);
#327=ORIENTED_EDGE('',*,*,#690,.F.);
#328=ORIENTED_EDGE('',*,*,#691,.F.);
#329=ORIENTED_EDGE('',*,*,#686,.T.);
#330=ORIENTED_EDGE('',*,*,#692,.T.);
#331=ORIENTED_EDGE('',*,*,#693,.F.);
#332=ORIENTED_EDGE('',*,*,#694,.F.);
#333=ORIENTED_EDGE('',*,*,#690,.T.);
#334=ORIENTED_EDGE('',*,*,#695,.F.);
#335=ORIENTED_EDGE('',*,*,#696,.F.);
#336=ORIENTED_EDGE('',*,*,#693,.T.);
#337=ORIENTED_EDGE('',*,*,#697,.T.);
#338=ORIENTED_EDGE('',*,*,#698,.T.);
#339=ORIENTED_EDGE('',*,*,#699,.F.);
#340=ORIENTED_EDGE('',*,*,#700,.F.);
#341=ORIENTED_EDGE('',*,*,#695,.T.);
#342=ORIENTED_EDGE('',*,*,#701,.T.);
#343=ORIENTED_EDGE('',*,*,#702,.F.);
#344=ORIENTED_EDGE('',*,*,#703,.F.);
#345=ORIENTED_EDGE('',*,*,#699,.T.);
#346=ORIENTED_EDGE('',*,*,#704,.T.);
#347=ORIENTED_EDGE('',*,*,#649,.F.);
#348=ORIENTED_EDGE('',*,*,#705,.F.);
#349=ORIENTED_EDGE('',*,*,#702,.T.);
#350=ORIENTED_EDGE('',*,*,#667,.T.);
#351=ORIENTED_EDGE('',*,*,#670,.T.);
#352=ORIENTED_EDGE('',*,*,#673,.T.);
#353=ORIENTED_EDGE('',*,*,#676,.T.);
#354=ORIENTED_EDGE('',*,*,#679,.T.);
#355=ORIENTED_EDGE('',*,*,#682,.T.);
#356=ORIENTED_EDGE('',*,*,#685,.T.);
#357=ORIENTED_EDGE('',*,*,#687,.T.);
#358=ORIENTED_EDGE('',*,*,#691,.T.);
#359=ORIENTED_EDGE('',*,*,#694,.T.);
#360=ORIENTED_EDGE('',*,*,#696,.T.);
#361=ORIENTED_EDGE('',*,*,#700,.T.);
#362=ORIENTED_EDGE('',*,*,#703,.T.);
#363=ORIENTED_EDGE('',*,*,#705,.T.);
#364=ORIENTED_EDGE('',*,*,#648,.T.);
#365=ORIENTED_EDGE('',*,*,#652,.T.);
#366=ORIENTED_EDGE('',*,*,#655,.T.);
#367=ORIENTED_EDGE('',*,*,#658,.T.);
#368=ORIENTED_EDGE('',*,*,#661,.T.);
#369=ORIENTED_EDGE('',*,*,#664,.T.);
#370=ORIENTED_EDGE('',*,*,#698,.F.);
#371=ORIENTED_EDGE('',*,*,#697,.F.);
#372=ORIENTED_EDGE('',*,*,#692,.F.);
#373=ORIENTED_EDGE('',*,*,#689,.F.);
#374=ORIENTED_EDGE('',*,*,#688,.F.);
#375=ORIENTED_EDGE('',*,*,#683,.F.);
#376=ORIENTED_EDGE('',*,*,#680,.F.);
#377=ORIENTED_EDGE('',*,*,#677,.F.);
#378=ORIENTED_EDGE('',*,*,#674,.F.);
#379=ORIENTED_EDGE('',*,*,#671,.F.);
#380=ORIENTED_EDGE('',*,*,#668,.F.);
#381=ORIENTED_EDGE('',*,*,#665,.F.);
#382=ORIENTED_EDGE('',*,*,#662,.F.);
#383=ORIENTED_EDGE('',*,*,#659,.F.);
#384=ORIENTED_EDGE('',*,*,#656,.F.);
#385=ORIENTED_EDGE('',*,*,#653,.F.);
#386=ORIENTED_EDGE('',*,*,#650,.F.);
#387=ORIENTED_EDGE('',*,*,#646,.F.);
#388=ORIENTED_EDGE('',*,*,#704,.F.);
#389=ORIENTED_EDGE('',*,*,#701,.F.);
#390=ORIENTED_EDGE('',*,*,#706,.F.);
#391=ORIENTED_EDGE('',*,*,#707,.F.);
#392=ORIENTED_EDGE('',*,*,#708,.T.);
#393=ORIENTED_EDGE('',*,*,#709,.T.);
#394=ORIENTED_EDGE('',*,*,#710,.T.);
#395=ORIENTED_EDGE('',*,*,#711,.T.);
#396=ORIENTED_EDGE('',*,*,#712,.F.);
#397=ORIENTED_EDGE('',*,*,#713,.F.);
#398=ORIENTED_EDGE('',*,*,#714,.F.);
#399=ORIENTED_EDGE('',*,*,#709,.F.);
#400=ORIENTED_EDGE('',*,*,#715,.F.);
#401=ORIENTED_EDGE('',*,*,#716,.F.);
#402=ORIENTED_EDGE('',*,*,#717,.F.);
#403=ORIENTED_EDGE('',*,*,#718,.F.);
#404=ORIENTED_EDGE('',*,*,#719,.F.);
#405=ORIENTED_EDGE('',*,*,#720,.F.);
#406=ORIENTED_EDGE('',*,*,#721,.F.);
#407=ORIENTED_EDGE('',*,*,#722,.F.);
#408=ORIENTED_EDGE('',*,*,#723,.F.);
#409=ORIENTED_EDGE('',*,*,#711,.F.);
#410=ORIENTED_EDGE('',*,*,#720,.T.);
#411=ORIENTED_EDGE('',*,*,#724,.F.);
#412=ORIENTED_EDGE('',*,*,#725,.F.);
#413=ORIENTED_EDGE('',*,*,#726,.T.);
#414=ORIENTED_EDGE('',*,*,#719,.T.);
#415=ORIENTED_EDGE('',*,*,#727,.F.);
#416=ORIENTED_EDGE('',*,*,#728,.F.);
#417=ORIENTED_EDGE('',*,*,#724,.T.);
#418=ORIENTED_EDGE('',*,*,#718,.T.);
#419=ORIENTED_EDGE('',*,*,#729,.F.);
#420=ORIENTED_EDGE('',*,*,#730,.F.);
#421=ORIENTED_EDGE('',*,*,#727,.T.);
#422=ORIENTED_EDGE('',*,*,#717,.T.);
#423=ORIENTED_EDGE('',*,*,#731,.F.);
#424=ORIENTED_EDGE('',*,*,#732,.F.);
#425=ORIENTED_EDGE('',*,*,#729,.T.);
#426=ORIENTED_EDGE('',*,*,#716,.T.);
#427=ORIENTED_EDGE('',*,*,#733,.F.);
#428=ORIENTED_EDGE('',*,*,#734,.F.);
#429=ORIENTED_EDGE('',*,*,#731,.T.);
#430=ORIENTED_EDGE('',*,*,#715,.T.);
#431=ORIENTED_EDGE('',*,*,#708,.F.);
#432=ORIENTED_EDGE('',*,*,#735,.F.);
#433=ORIENTED_EDGE('',*,*,#733,.T.);
#434=ORIENTED_EDGE('',*,*,#723,.T.);
#435=ORIENTED_EDGE('',*,*,#736,.F.);
#436=ORIENTED_EDGE('',*,*,#737,.F.);
#437=ORIENTED_EDGE('',*,*,#712,.T.);
#438=ORIENTED_EDGE('',*,*,#722,.T.);
#439=ORIENTED_EDGE('',*,*,#738,.F.);
#440=ORIENTED_EDGE('',*,*,#739,.F.);
#441=ORIENTED_EDGE('',*,*,#736,.T.);
#442=ORIENTED_EDGE('',*,*,#721,.T.);
#443=ORIENTED_EDGE('',*,*,#726,.F.);
#444=ORIENTED_EDGE('',*,*,#740,.F.);
#445=ORIENTED_EDGE('',*,*,#738,.T.);
#446=ORIENTED_EDGE('',*,*,#741,.T.);
#447=ORIENTED_EDGE('',*,*,#713,.T.);
#448=ORIENTED_EDGE('',*,*,#737,.T.);
#449=ORIENTED_EDGE('',*,*,#739,.T.);
#450=ORIENTED_EDGE('',*,*,#740,.T.);
#451=ORIENTED_EDGE('',*,*,#725,.T.);
#452=ORIENTED_EDGE('',*,*,#728,.T.);
#453=ORIENTED_EDGE('',*,*,#730,.T.);
#454=ORIENTED_EDGE('',*,*,#732,.T.);
#455=ORIENTED_EDGE('',*,*,#734,.T.);
#456=ORIENTED_EDGE('',*,*,#735,.T.);
#457=ORIENTED_EDGE('',*,*,#707,.T.);
#458=ORIENTED_EDGE('',*,*,#706,.T.);
#459=ORIENTED_EDGE('',*,*,#714,.T.);
#460=ORIENTED_EDGE('',*,*,#710,.F.);
#461=ORIENTED_EDGE('',*,*,#741,.F.);
#462=ORIENTED_EDGE('',*,*,#742,.T.);
#463=ORIENTED_EDGE('',*,*,#743,.F.);
#464=ORIENTED_EDGE('',*,*,#744,.F.);
#465=ORIENTED_EDGE('',*,*,#745,.T.);
#466=ORIENTED_EDGE('',*,*,#746,.T.);
#467=ORIENTED_EDGE('',*,*,#747,.F.);
#468=ORIENTED_EDGE('',*,*,#748,.F.);
#469=ORIENTED_EDGE('',*,*,#743,.T.);
#470=ORIENTED_EDGE('',*,*,#749,.T.);
#471=ORIENTED_EDGE('',*,*,#750,.F.);
#472=ORIENTED_EDGE('',*,*,#751,.F.);
#473=ORIENTED_EDGE('',*,*,#747,.T.);
#474=ORIENTED_EDGE('',*,*,#752,.T.);
#475=ORIENTED_EDGE('',*,*,#753,.F.);
#476=ORIENTED_EDGE('',*,*,#754,.F.);
#477=ORIENTED_EDGE('',*,*,#750,.T.);
#478=ORIENTED_EDGE('',*,*,#755,.T.);
#479=ORIENTED_EDGE('',*,*,#756,.F.);
#480=ORIENTED_EDGE('',*,*,#757,.F.);
#481=ORIENTED_EDGE('',*,*,#753,.T.);
#482=ORIENTED_EDGE('',*,*,#758,.T.);
#483=ORIENTED_EDGE('',*,*,#759,.F.);
#484=ORIENTED_EDGE('',*,*,#760,.F.);
#485=ORIENTED_EDGE('',*,*,#756,.T.);
#486=ORIENTED_EDGE('',*,*,#761,.F.);
#487=ORIENTED_EDGE('',*,*,#762,.F.);
#488=ORIENTED_EDGE('',*,*,#759,.T.);
#489=ORIENTED_EDGE('',*,*,#763,.T.);
#490=ORIENTED_EDGE('',*,*,#764,.T.);
#491=ORIENTED_EDGE('',*,*,#765,.F.);
#492=ORIENTED_EDGE('',*,*,#766,.F.);
#493=ORIENTED_EDGE('',*,*,#761,.T.);
#494=ORIENTED_EDGE('',*,*,#767,.T.);
#495=ORIENTED_EDGE('',*,*,#768,.F.);
#496=ORIENTED_EDGE('',*,*,#769,.F.);
#497=ORIENTED_EDGE('',*,*,#765,.T.);
#498=ORIENTED_EDGE('',*,*,#770,.T.);
#499=ORIENTED_EDGE('',*,*,#771,.F.);
#500=ORIENTED_EDGE('',*,*,#772,.F.);
#501=ORIENTED_EDGE('',*,*,#768,.T.);
#502=ORIENTED_EDGE('',*,*,#773,.T.);
#503=ORIENTED_EDGE('',*,*,#774,.F.);
#504=ORIENTED_EDGE('',*,*,#775,.F.);
#505=ORIENTED_EDGE('',*,*,#771,.T.);
#506=ORIENTED_EDGE('',*,*,#776,.T.);
#507=ORIENTED_EDGE('',*,*,#777,.F.);
#508=ORIENTED_EDGE('',*,*,#778,.F.);
#509=ORIENTED_EDGE('',*,*,#774,.T.);
#510=ORIENTED_EDGE('',*,*,#779,.T.);
#511=ORIENTED_EDGE('',*,*,#780,.F.);
#512=ORIENTED_EDGE('',*,*,#781,.F.);
#513=ORIENTED_EDGE('',*,*,#777,.T.);
#514=ORIENTED_EDGE('',*,*,#782,.F.);
#515=ORIENTED_EDGE('',*,*,#783,.F.);
#516=ORIENTED_EDGE('',*,*,#780,.T.);
#517=ORIENTED_EDGE('',*,*,#784,.T.);
#518=ORIENTED_EDGE('',*,*,#785,.T.);
#519=ORIENTED_EDGE('',*,*,#786,.F.);
#520=ORIENTED_EDGE('',*,*,#787,.F.);
#521=ORIENTED_EDGE('',*,*,#782,.T.);
#522=ORIENTED_EDGE('',*,*,#788,.T.);
#523=ORIENTED_EDGE('',*,*,#789,.F.);
#524=ORIENTED_EDGE('',*,*,#790,.F.);
#525=ORIENTED_EDGE('',*,*,#786,.T.);
#526=ORIENTED_EDGE('',*,*,#791,.T.);
#527=ORIENTED_EDGE('',*,*,#792,.F.);
#528=ORIENTED_EDGE('',*,*,#793,.F.);
#529=ORIENTED_EDGE('',*,*,#789,.T.);
#530=ORIENTED_EDGE('',*,*,#794,.T.);
#531=ORIENTED_EDGE('',*,*,#795,.F.);
#532=ORIENTED_EDGE('',*,*,#796,.F.);
#533=ORIENTED_EDGE('',*,*,#792,.T.);
#534=ORIENTED_EDGE('',*,*,#797,.T.);
#535=ORIENTED_EDGE('',*,*,#798,.F.);
#536=ORIENTED_EDGE('',*,*,#799,.F.);
#537=ORIENTED_EDGE('',*,*,#795,.T.);
#538=ORIENTED_EDGE('',*,*,#800,.T.);
#539=ORIENTED_EDGE('',*,*,#745,.F.);
#540=ORIENTED_EDGE('',*,*,#801,.F.);
#541=ORIENTED_EDGE('',*,*,#798,.T.);
#542=ORIENTED_EDGE('',*,*,#793,.T.);
#543=ORIENTED_EDGE('',*,*,#796,.T.);
#544=ORIENTED_EDGE('',*,*,#799,.T.);
#545=ORIENTED_EDGE('',*,*,#801,.T.);
#546=ORIENTED_EDGE('',*,*,#744,.T.);
#547=ORIENTED_EDGE('',*,*,#748,.T.);
#548=ORIENTED_EDGE('',*,*,#751,.T.);
#549=ORIENTED_EDGE('',*,*,#754,.T.);
#550=ORIENTED_EDGE('',*,*,#757,.T.);
#551=ORIENTED_EDGE('',*,*,#760,.T.);
#552=ORIENTED_EDGE('',*,*,#762,.T.);
#553=ORIENTED_EDGE('',*,*,#766,.T.);
#554=ORIENTED_EDGE('',*,*,#769,.T.);
#555=ORIENTED_EDGE('',*,*,#772,.T.);
#556=ORIENTED_EDGE('',*,*,#775,.T.);
#557=ORIENTED_EDGE('',*,*,#778,.T.);
#558=ORIENTED_EDGE('',*,*,#781,.T.);
#559=ORIENTED_EDGE('',*,*,#783,.T.);
#560=ORIENTED_EDGE('',*,*,#787,.T.);
#561=ORIENTED_EDGE('',*,*,#790,.T.);
#562=ORIENTED_EDGE('',*,*,#764,.F.);
#563=ORIENTED_EDGE('',*,*,#763,.F.);
#564=ORIENTED_EDGE('',*,*,#758,.F.);
#565=ORIENTED_EDGE('',*,*,#755,.F.);
#566=ORIENTED_EDGE('',*,*,#752,.F.);
#567=ORIENTED_EDGE('',*,*,#749,.F.);
#568=ORIENTED_EDGE('',*,*,#746,.F.);
#569=ORIENTED_EDGE('',*,*,#742,.F.);
#570=ORIENTED_EDGE('',*,*,#800,.F.);
#571=ORIENTED_EDGE('',*,*,#797,.F.);
#572=ORIENTED_EDGE('',*,*,#794,.F.);
#573=ORIENTED_EDGE('',*,*,#791,.F.);
#574=ORIENTED_EDGE('',*,*,#788,.F.);
#575=ORIENTED_EDGE('',*,*,#785,.F.);
#576=ORIENTED_EDGE('',*,*,#784,.F.);
#577=ORIENTED_EDGE('',*,*,#779,.F.);
#578=ORIENTED_EDGE('',*,*,#776,.F.);
#579=ORIENTED_EDGE('',*,*,#773,.F.);
#580=ORIENTED_EDGE('',*,*,#770,.F.);
#581=ORIENTED_EDGE('',*,*,#767,.F.);
#582=EDGE_CURVE('',#802,#803,#938,.F.);
#583=EDGE_CURVE('',#803,#804,#939,.F.);
#584=EDGE_CURVE('',#804,#805,#940,.F.);
#585=EDGE_CURVE('',#805,#802,#941,.F.);
#586=EDGE_CURVE('',#806,#807,#942,.T.);
#587=EDGE_CURVE('',#806,#808,#943,.F.);
#588=EDGE_CURVE('',#808,#809,#944,.F.);
#589=EDGE_CURVE('',#809,#807,#945,.F.);
#590=EDGE_CURVE('',#810,#811,#946,.F.);
#591=EDGE_CURVE('',#811,#812,#947,.F.);
#592=EDGE_CURVE('',#813,#812,#948,.T.);
#593=EDGE_CURVE('',#813,#810,#949,.T.);
#594=EDGE_CURVE('',#814,#815,#950,.F.);
#595=EDGE_CURVE('',#815,#816,#951,.F.);
#596=EDGE_CURVE('',#817,#816,#952,.T.);
#597=EDGE_CURVE('',#817,#814,#953,.T.);
#598=EDGE_CURVE('',#818,#819,#954,.F.);
#599=EDGE_CURVE('',#819,#820,#955,.T.);
#600=EDGE_CURVE('',#821,#820,#956,.T.);
#601=EDGE_CURVE('',#821,#818,#957,.T.);
#602=EDGE_CURVE('',#822,#823,#958,.T.);
#603=EDGE_CURVE('',#823,#824,#959,.T.);
#604=EDGE_CURVE('',#824,#825,#960,.T.);
#605=EDGE_CURVE('',#825,#822,#961,.T.);
#606=EDGE_CURVE('',#826,#827,#962,.F.);
#607=EDGE_CURVE('',#827,#806,#963,.T.);
#608=EDGE_CURVE('',#807,#826,#964,.T.);
#609=EDGE_CURVE('',#812,#828,#965,.T.);
#610=EDGE_CURVE('',#828,#829,#966,.F.);
#611=EDGE_CURVE('',#829,#813,#967,.F.);
#612=EDGE_CURVE('',#816,#830,#968,.T.);
#613=EDGE_CURVE('',#830,#831,#969,.F.);
#614=EDGE_CURVE('',#831,#817,#970,.F.);
#615=EDGE_CURVE('',#820,#832,#971,.F.);
#616=EDGE_CURVE('',#832,#833,#972,.F.);
#617=EDGE_CURVE('',#833,#821,#973,.F.);
#618=EDGE_CURVE('',#828,#822,#42,.F.);
#619=EDGE_CURVE('',#825,#829,#43,.T.);
#620=EDGE_CURVE('',#830,#823,#44,.F.);
#621=EDGE_CURVE('',#822,#831,#45,.T.);
#622=EDGE_CURVE('',#824,#827,#46,.T.);
#623=EDGE_CURVE('',#826,#825,#47,.F.);
#624=EDGE_CURVE('',#832,#824,#48,.F.);
#625=EDGE_CURVE('',#823,#833,#49,.T.);
#626=EDGE_CURVE('',#805,#815,#50,.T.);
#627=EDGE_CURVE('',#814,#802,#51,.F.);
#628=EDGE_CURVE('',#802,#811,#52,.T.);
#629=EDGE_CURVE('',#810,#803,#53,.F.);
#630=EDGE_CURVE('',#804,#819,#54,.T.);
#631=EDGE_CURVE('',#818,#805,#55,.F.);
#632=EDGE_CURVE('',#808,#804,#56,.F.);
#633=EDGE_CURVE('',#803,#809,#57,.T.);
#634=EDGE_CURVE('',#829,#826,#58,.T.);
#635=EDGE_CURVE('',#813,#807,#30,.F.);
#636=EDGE_CURVE('',#810,#809,#59,.T.);
#637=EDGE_CURVE('',#811,#814,#60,.T.);
#638=EDGE_CURVE('',#812,#817,#31,.T.);
#639=EDGE_CURVE('',#828,#831,#61,.T.);
#640=EDGE_CURVE('',#815,#818,#62,.T.);
#641=EDGE_CURVE('',#816,#821,#32,.T.);
#642=EDGE_CURVE('',#830,#833,#63,.T.);
#643=EDGE_CURVE('',#819,#808,#64,.T.);
#644=EDGE_CURVE('',#820,#806,#33,.F.);
#645=EDGE_CURVE('',#832,#827,#65,.T.);
#646=EDGE_CURVE('',#834,#835,#974,.T.);
#647=EDGE_CURVE('',#836,#835,#975,.T.);
#648=EDGE_CURVE('',#837,#836,#976,.T.);
#649=EDGE_CURVE('',#837,#834,#977,.T.);
#650=EDGE_CURVE('',#835,#838,#978,.T.);
#651=EDGE_CURVE('',#839,#838,#979,.T.);
#652=EDGE_CURVE('',#836,#839,#980,.T.);
#653=EDGE_CURVE('',#838,#840,#981,.T.);
#654=EDGE_CURVE('',#841,#840,#982,.T.);
#655=EDGE_CURVE('',#839,#841,#983,.T.);
#656=EDGE_CURVE('',#840,#842,#66,.T.);
#657=EDGE_CURVE('',#843,#842,#984,.T.);
#658=EDGE_CURVE('',#841,#843,#67,.T.);
#659=EDGE_CURVE('',#842,#844,#985,.T.);
#660=EDGE_CURVE('',#845,#844,#986,.T.);
#661=EDGE_CURVE('',#843,#845,#987,.T.);
#662=EDGE_CURVE('',#844,#846,#68,.T.);
#663=EDGE_CURVE('',#847,#846,#988,.T.);
#664=EDGE_CURVE('',#845,#847,#69,.T.);
#665=EDGE_CURVE('',#846,#848,#989,.T.);
#666=EDGE_CURVE('',#849,#848,#990,.T.);
#667=EDGE_CURVE('',#847,#849,#991,.T.);
#668=EDGE_CURVE('',#848,#850,#70,.T.);
#669=EDGE_CURVE('',#851,#850,#992,.T.);
#670=EDGE_CURVE('',#849,#851,#71,.T.);
#671=EDGE_CURVE('',#850,#852,#993,.T.);
#672=EDGE_CURVE('',#853,#852,#994,.T.);
#673=EDGE_CURVE('',#851,#853,#995,.T.);
#674=EDGE_CURVE('',#852,#854,#72,.T.);
#675=EDGE_CURVE('',#855,#854,#996,.T.);
#676=EDGE_CURVE('',#853,#855,#73,.T.);
#677=EDGE_CURVE('',#854,#856,#997,.T.);
#678=EDGE_CURVE('',#857,#856,#998,.T.);
#679=EDGE_CURVE('',#855,#857,#999,.T.);
#680=EDGE_CURVE('',#856,#858,#1000,.T.);
#681=EDGE_CURVE('',#859,#858,#1001,.T.);
#682=EDGE_CURVE('',#857,#859,#1002,.T.);
#683=EDGE_CURVE('',#858,#860,#1003,.T.);
#684=EDGE_CURVE('',#861,#860,#1004,.T.);
#685=EDGE_CURVE('',#859,#861,#1005,.T.);
#686=EDGE_CURVE('',#862,#863,#1006,.T.);
#687=EDGE_CURVE('',#861,#862,#74,.T.);
#688=EDGE_CURVE('',#860,#863,#75,.T.);
#689=EDGE_CURVE('',#863,#864,#1007,.T.);
#690=EDGE_CURVE('',#865,#864,#1008,.T.);
#691=EDGE_CURVE('',#862,#865,#1009,.T.);
#692=EDGE_CURVE('',#864,#866,#76,.T.);
#693=EDGE_CURVE('',#867,#866,#1010,.T.);
#694=EDGE_CURVE('',#865,#867,#77,.T.);
#695=EDGE_CURVE('',#868,#869,#1011,.T.);
#696=EDGE_CURVE('',#867,#868,#1012,.T.);
#697=EDGE_CURVE('',#866,#869,#1013,.T.);
#698=EDGE_CURVE('',#869,#870,#78,.T.);
#699=EDGE_CURVE('',#871,#870,#1014,.T.);
#700=EDGE_CURVE('',#868,#871,#79,.T.);
#701=EDGE_CURVE('',#870,#872,#1015,.T.);
#702=EDGE_CURVE('',#873,#872,#1016,.T.);
#703=EDGE_CURVE('',#871,#873,#1017,.T.);
#704=EDGE_CURVE('',#872,#834,#80,.T.);
#705=EDGE_CURVE('',#873,#837,#81,.T.);
#706=EDGE_CURVE('',#874,#875,#1018,.T.);
#707=EDGE_CURVE('',#876,#874,#1019,.T.);
#708=EDGE_CURVE('',#876,#877,#1020,.T.);
#709=EDGE_CURVE('',#877,#875,#1021,.T.);
#710=EDGE_CURVE('',#878,#879,#1022,.T.);
#711=EDGE_CURVE('',#879,#880,#1023,.T.);
#712=EDGE_CURVE('',#881,#880,#1024,.T.);
#713=EDGE_CURVE('',#878,#881,#1025,.T.);
#714=EDGE_CURVE('',#875,#879,#1026,.T.);
#715=EDGE_CURVE('',#882,#877,#82,.T.);
#716=EDGE_CURVE('',#883,#882,#1027,.T.);
#717=EDGE_CURVE('',#884,#883,#83,.T.);
#718=EDGE_CURVE('',#885,#884,#1028,.T.);
#719=EDGE_CURVE('',#886,#885,#1029,.T.);
#720=EDGE_CURVE('',#887,#886,#1030,.T.);
#721=EDGE_CURVE('',#888,#887,#84,.T.);
#722=EDGE_CURVE('',#889,#888,#1031,.T.);
#723=EDGE_CURVE('',#880,#889,#85,.T.);
#724=EDGE_CURVE('',#890,#886,#1032,.T.);
#725=EDGE_CURVE('',#891,#890,#1033,.T.);
#726=EDGE_CURVE('',#891,#887,#1034,.T.);
#727=EDGE_CURVE('',#892,#885,#1035,.T.);
#728=EDGE_CURVE('',#890,#892,#1036,.T.);
#729=EDGE_CURVE('',#893,#884,#1037,.T.);
#730=EDGE_CURVE('',#892,#893,#1038,.T.);
#731=EDGE_CURVE('',#894,#883,#1039,.T.);
#732=EDGE_CURVE('',#893,#894,#86,.T.);
#733=EDGE_CURVE('',#895,#882,#1040,.T.);
#734=EDGE_CURVE('',#894,#895,#1041,.T.);
#735=EDGE_CURVE('',#895,#876,#87,.T.);
#736=EDGE_CURVE('',#896,#889,#1042,.T.);
#737=EDGE_CURVE('',#881,#896,#88,.T.);
#738=EDGE_CURVE('',#897,#888,#1043,.T.);
#739=EDGE_CURVE('',#896,#897,#1044,.T.);
#740=EDGE_CURVE('',#897,#891,#89,.T.);
#741=EDGE_CURVE('',#874,#878,#1045,.T.);
#742=EDGE_CURVE('',#898,#899,#1046,.T.);
#743=EDGE_CURVE('',#900,#899,#1047,.T.);
#744=EDGE_CURVE('',#901,#900,#1048,.T.);
#745=EDGE_CURVE('',#901,#898,#1049,.T.);
#746=EDGE_CURVE('',#899,#902,#1050,.T.);
#747=EDGE_CURVE('',#903,#902,#1051,.T.);
#748=EDGE_CURVE('',#900,#903,#1052,.T.);
#749=EDGE_CURVE('',#902,#904,#1053,.T.);
#750=EDGE_CURVE('',#905,#904,#1054,.T.);
#751=EDGE_CURVE('',#903,#905,#1055,.T.);
#752=EDGE_CURVE('',#904,#906,#90,.T.);
#753=EDGE_CURVE('',#907,#906,#1056,.T.);
#754=EDGE_CURVE('',#905,#907,#91,.T.);
#755=EDGE_CURVE('',#906,#908,#1057,.T.);
#756=EDGE_CURVE('',#909,#908,#1058,.T.);
#757=EDGE_CURVE('',#907,#909,#1059,.T.);
#758=EDGE_CURVE('',#908,#910,#92,.T.);
#759=EDGE_CURVE('',#911,#910,#1060,.T.);
#760=EDGE_CURVE('',#909,#911,#93,.T.);
#761=EDGE_CURVE('',#912,#913,#1061,.T.);
#762=EDGE_CURVE('',#911,#912,#1062,.T.);
#763=EDGE_CURVE('',#910,#913,#1063,.T.);
#764=EDGE_CURVE('',#913,#914,#94,.T.);
#765=EDGE_CURVE('',#915,#914,#1064,.T.);
#766=EDGE_CURVE('',#912,#915,#95,.T.);
#767=EDGE_CURVE('',#914,#916,#1065,.T.);
#768=EDGE_CURVE('',#917,#916,#1066,.T.);
#769=EDGE_CURVE('',#915,#917,#1067,.T.);
#770=EDGE_CURVE('',#916,#918,#96,.T.);
#771=EDGE_CURVE('',#919,#918,#1068,.T.);
#772=EDGE_CURVE('',#917,#919,#97,.T.);
#773=EDGE_CURVE('',#918,#920,#1069,.T.);
#774=EDGE_CURVE('',#921,#920,#1070,.T.);
#775=EDGE_CURVE('',#919,#921,#1071,.T.);
#776=EDGE_CURVE('',#920,#922,#1072,.T.);
#777=EDGE_CURVE('',#923,#922,#1073,.T.);
#778=EDGE_CURVE('',#921,#923,#1074,.T.);
#779=EDGE_CURVE('',#922,#924,#1075,.T.);
#780=EDGE_CURVE('',#925,#924,#1076,.T.);
#781=EDGE_CURVE('',#923,#925,#1077,.T.);
#782=EDGE_CURVE('',#926,#927,#1078,.T.);
#783=EDGE_CURVE('',#925,#926,#98,.T.);
#784=EDGE_CURVE('',#924,#927,#99,.T.);
#785=EDGE_CURVE('',#927,#928,#1079,.T.);
#786=EDGE_CURVE('',#929,#928,#1080,.T.);
#787=EDGE_CURVE('',#926,#929,#1081,.T.);
#788=EDGE_CURVE('',#928,#930,#100,.T.);
#789=EDGE_CURVE('',#931,#930,#1082,.T.);
#790=EDGE_CURVE('',#929,#931,#101,.T.);
#791=EDGE_CURVE('',#930,#932,#1083,.T.);
#792=EDGE_CURVE('',#933,#932,#1084,.T.);
#793=EDGE_CURVE('',#931,#933,#1085,.T.);
#794=EDGE_CURVE('',#932,#934,#102,.T.);
#795=EDGE_CURVE('',#935,#934,#1086,.T.);
#796=EDGE_CURVE('',#933,#935,#103,.T.);
#797=EDGE_CURVE('',#934,#936,#1087,.T.);
#798=EDGE_CURVE('',#937,#936,#1088,.T.);
#799=EDGE_CURVE('',#935,#937,#1089,.T.);
#800=EDGE_CURVE('',#936,#898,#104,.T.);
#801=EDGE_CURVE('',#937,#901,#105,.T.);
#802=VERTEX_POINT('',#2640);
#803=VERTEX_POINT('',#2641);
#804=VERTEX_POINT('',#2643);
#805=VERTEX_POINT('',#2645);
#806=VERTEX_POINT('',#2649);
#807=VERTEX_POINT('',#2650);
#808=VERTEX_POINT('',#2652);
#809=VERTEX_POINT('',#2654);
#810=VERTEX_POINT('',#2658);
#811=VERTEX_POINT('',#2659);
#812=VERTEX_POINT('',#2661);
#813=VERTEX_POINT('',#2663);
#814=VERTEX_POINT('',#2667);
#815=VERTEX_POINT('',#2668);
#816=VERTEX_POINT('',#2670);
#817=VERTEX_POINT('',#2672);
#818=VERTEX_POINT('',#2676);
#819=VERTEX_POINT('',#2677);
#820=VERTEX_POINT('',#2679);
#821=VERTEX_POINT('',#2681);
#822=VERTEX_POINT('',#2685);
#823=VERTEX_POINT('',#2686);
#824=VERTEX_POINT('',#2688);
#825=VERTEX_POINT('',#2690);
#826=VERTEX_POINT('',#2694);
#827=VERTEX_POINT('',#2695);
#828=VERTEX_POINT('',#2700);
#829=VERTEX_POINT('',#2702);
#830=VERTEX_POINT('',#2706);
#831=VERTEX_POINT('',#2708);
#832=VERTEX_POINT('',#2712);
#833=VERTEX_POINT('',#2714);
#834=VERTEX_POINT('',#2771);
#835=VERTEX_POINT('',#2772);
#836=VERTEX_POINT('',#2774);
#837=VERTEX_POINT('',#2776);
#838=VERTEX_POINT('',#2780);
#839=VERTEX_POINT('',#2782);
#840=VERTEX_POINT('',#2786);
#841=VERTEX_POINT('',#2788);
#842=VERTEX_POINT('',#2792);
#843=VERTEX_POINT('',#2794);
#844=VERTEX_POINT('',#2798);
#845=VERTEX_POINT('',#2800);
#846=VERTEX_POINT('',#2804);
#847=VERTEX_POINT('',#2806);
#848=VERTEX_POINT('',#2810);
#849=VERTEX_POINT('',#2812);
#850=VERTEX_POINT('',#2816);
#851=VERTEX_POINT('',#2818);
#852=VERTEX_POINT('',#2822);
#853=VERTEX_POINT('',#2824);
#854=VERTEX_POINT('',#2828);
#855=VERTEX_POINT('',#2830);
#856=VERTEX_POINT('',#2834);
#857=VERTEX_POINT('',#2836);
#858=VERTEX_POINT('',#2840);
#859=VERTEX_POINT('',#2842);
#860=VERTEX_POINT('',#2846);
#861=VERTEX_POINT('',#2848);
#862=VERTEX_POINT('',#2852);
#863=VERTEX_POINT('',#2853);
#864=VERTEX_POINT('',#2858);
#865=VERTEX_POINT('',#2860);
#866=VERTEX_POINT('',#2864);
#867=VERTEX_POINT('',#2866);
#868=VERTEX_POINT('',#2870);
#869=VERTEX_POINT('',#2871);
#870=VERTEX_POINT('',#2876);
#871=VERTEX_POINT('',#2878);
#872=VERTEX_POINT('',#2882);
#873=VERTEX_POINT('',#2884);
#874=VERTEX_POINT('',#2893);
#875=VERTEX_POINT('',#2894);
#876=VERTEX_POINT('',#2896);
#877=VERTEX_POINT('',#2898);
#878=VERTEX_POINT('',#2902);
#879=VERTEX_POINT('',#2903);
#880=VERTEX_POINT('',#2905);
#881=VERTEX_POINT('',#2907);
#882=VERTEX_POINT('',#2912);
#883=VERTEX_POINT('',#2914);
#884=VERTEX_POINT('',#2916);
#885=VERTEX_POINT('',#2918);
#886=VERTEX_POINT('',#2920);
#887=VERTEX_POINT('',#2922);
#888=VERTEX_POINT('',#2924);
#889=VERTEX_POINT('',#2926);
#890=VERTEX_POINT('',#2930);
#891=VERTEX_POINT('',#2932);
#892=VERTEX_POINT('',#2936);
#893=VERTEX_POINT('',#2940);
#894=VERTEX_POINT('',#2944);
#895=VERTEX_POINT('',#2948);
#896=VERTEX_POINT('',#2954);
#897=VERTEX_POINT('',#2958);
#898=VERTEX_POINT('',#2967);
#899=VERTEX_POINT('',#2968);
#900=VERTEX_POINT('',#2970);
#901=VERTEX_POINT('',#2972);
#902=VERTEX_POINT('',#2976);
#903=VERTEX_POINT('',#2978);
#904=VERTEX_POINT('',#2982);
#905=VERTEX_POINT('',#2984);
#906=VERTEX_POINT('',#2988);
#907=VERTEX_POINT('',#2990);
#908=VERTEX_POINT('',#2994);
#909=VERTEX_POINT('',#2996);
#910=VERTEX_POINT('',#3000);
#911=VERTEX_POINT('',#3002);
#912=VERTEX_POINT('',#3006);
#913=VERTEX_POINT('',#3007);
#914=VERTEX_POINT('',#3012);
#915=VERTEX_POINT('',#3014);
#916=VERTEX_POINT('',#3018);
#917=VERTEX_POINT('',#3020);
#918=VERTEX_POINT('',#3024);
#919=VERTEX_POINT('',#3026);
#920=VERTEX_POINT('',#3030);
#921=VERTEX_POINT('',#3032);
#922=VERTEX_POINT('',#3036);
#923=VERTEX_POINT('',#3038);
#924=VERTEX_POINT('',#3042);
#925=VERTEX_POINT('',#3044);
#926=VERTEX_POINT('',#3048);
#927=VERTEX_POINT('',#3049);
#928=VERTEX_POINT('',#3054);
#929=VERTEX_POINT('',#3056);
#930=VERTEX_POINT('',#3060);
#931=VERTEX_POINT('',#3062);
#932=VERTEX_POINT('',#3066);
#933=VERTEX_POINT('',#3068);
#934=VERTEX_POINT('',#3072);
#935=VERTEX_POINT('',#3074);
#936=VERTEX_POINT('',#3078);
#937=VERTEX_POINT('',#3080);
#938=LINE('',#2639,#1090);
#939=LINE('',#2642,#1091);
#940=LINE('',#2644,#1092);
#941=LINE('',#2646,#1093);
#942=LINE('',#2648,#1094);
#943=LINE('',#2651,#1095);
#944=LINE('',#2653,#1096);
#945=LINE('',#2655,#1097);
#946=LINE('',#2657,#1098);
#947=LINE('',#2660,#1099);
#948=LINE('',#2662,#1100);
#949=LINE('',#2664,#1101);
#950=LINE('',#2666,#1102);
#951=LINE('',#2669,#1103);
#952=LINE('',#2671,#1104);
#953=LINE('',#2673,#1105);
#954=LINE('',#2675,#1106);
#955=LINE('',#2678,#1107);
#956=LINE('',#2680,#1108);
#957=LINE('',#2682,#1109);
#958=LINE('',#2684,#1110);
#959=LINE('',#2687,#1111);
#960=LINE('',#2689,#1112);
#961=LINE('',#2691,#1113);
#962=LINE('',#2693,#1114);
#963=LINE('',#2696,#1115);
#964=LINE('',#2697,#1116);
#965=LINE('',#2699,#1117);
#966=LINE('',#2701,#1118);
#967=LINE('',#2703,#1119);
#968=LINE('',#2705,#1120);
#969=LINE('',#2707,#1121);
#970=LINE('',#2709,#1122);
#971=LINE('',#2711,#1123);
#972=LINE('',#2713,#1124);
#973=LINE('',#2715,#1125);
#974=LINE('',#2770,#1126);
#975=LINE('',#2773,#1127);
#976=LINE('',#2775,#1128);
#977=LINE('',#2777,#1129);
#978=LINE('',#2779,#1130);
#979=LINE('',#2781,#1131);
#980=LINE('',#2783,#1132);
#981=LINE('',#2785,#1133);
#982=LINE('',#2787,#1134);
#983=LINE('',#2789,#1135);
#984=LINE('',#2793,#1136);
#985=LINE('',#2797,#1137);
#986=LINE('',#2799,#1138);
#987=LINE('',#2801,#1139);
#988=LINE('',#2805,#1140);
#989=LINE('',#2809,#1141);
#990=LINE('',#2811,#1142);
#991=LINE('',#2813,#1143);
#992=LINE('',#2817,#1144);
#993=LINE('',#2821,#1145);
#994=LINE('',#2823,#1146);
#995=LINE('',#2825,#1147);
#996=LINE('',#2829,#1148);
#997=LINE('',#2833,#1149);
#998=LINE('',#2835,#1150);
#999=LINE('',#2837,#1151);
#1000=LINE('',#2839,#1152);
#1001=LINE('',#2841,#1153);
#1002=LINE('',#2843,#1154);
#1003=LINE('',#2845,#1155);
#1004=LINE('',#2847,#1156);
#1005=LINE('',#2849,#1157);
#1006=LINE('',#2851,#1158);
#1007=LINE('',#2857,#1159);
#1008=LINE('',#2859,#1160);
#1009=LINE('',#2861,#1161);
#1010=LINE('',#2865,#1162);
#1011=LINE('',#2869,#1163);
#1012=LINE('',#2872,#1164);
#1013=LINE('',#2873,#1165);
#1014=LINE('',#2877,#1166);
#1015=LINE('',#2881,#1167);
#1016=LINE('',#2883,#1168);
#1017=LINE('',#2885,#1169);
#1018=LINE('',#2892,#1170);
#1019=LINE('',#2895,#1171);
#1020=LINE('',#2897,#1172);
#1021=LINE('',#2899,#1173);
#1022=LINE('',#2901,#1174);
#1023=LINE('',#2904,#1175);
#1024=LINE('',#2906,#1176);
#1025=LINE('',#2908,#1177);
#1026=LINE('',#2910,#1178);
#1027=LINE('',#2913,#1179);
#1028=LINE('',#2917,#1180);
#1029=LINE('',#2919,#1181);
#1030=LINE('',#2921,#1182);
#1031=LINE('',#2925,#1183);
#1032=LINE('',#2929,#1184);
#1033=LINE('',#2931,#1185);
#1034=LINE('',#2933,#1186);
#1035=LINE('',#2935,#1187);
#1036=LINE('',#2937,#1188);
#1037=LINE('',#2939,#1189);
#1038=LINE('',#2941,#1190);
#1039=LINE('',#2943,#1191);
#1040=LINE('',#2947,#1192);
#1041=LINE('',#2949,#1193);
#1042=LINE('',#2953,#1194);
#1043=LINE('',#2957,#1195);
#1044=LINE('',#2959,#1196);
#1045=LINE('',#2963,#1197);
#1046=LINE('',#2966,#1198);
#1047=LINE('',#2969,#1199);
#1048=LINE('',#2971,#1200);
#1049=LINE('',#2973,#1201);
#1050=LINE('',#2975,#1202);
#1051=LINE('',#2977,#1203);
#1052=LINE('',#2979,#1204);
#1053=LINE('',#2981,#1205);
#1054=LINE('',#2983,#1206);
#1055=LINE('',#2985,#1207);
#1056=LINE('',#2989,#1208);
#1057=LINE('',#2993,#1209);
#1058=LINE('',#2995,#1210);
#1059=LINE('',#2997,#1211);
#1060=LINE('',#3001,#1212);
#1061=LINE('',#3005,#1213);
#1062=LINE('',#3008,#1214);
#1063=LINE('',#3009,#1215);
#1064=LINE('',#3013,#1216);
#1065=LINE('',#3017,#1217);
#1066=LINE('',#3019,#1218);
#1067=LINE('',#3021,#1219);
#1068=LINE('',#3025,#1220);
#1069=LINE('',#3029,#1221);
#1070=LINE('',#3031,#1222);
#1071=LINE('',#3033,#1223);
#1072=LINE('',#3035,#1224);
#1073=LINE('',#3037,#1225);
#1074=LINE('',#3039,#1226);
#1075=LINE('',#3041,#1227);
#1076=LINE('',#3043,#1228);
#1077=LINE('',#3045,#1229);
#1078=LINE('',#3047,#1230);
#1079=LINE('',#3053,#1231);
#1080=LINE('',#3055,#1232);
#1081=LINE('',#3057,#1233);
#1082=LINE('',#3061,#1234);
#1083=LINE('',#3065,#1235);
#1084=LINE('',#3067,#1236);
#1085=LINE('',#3069,#1237);
#1086=LINE('',#3073,#1238);
#1087=LINE('',#3077,#1239);
#1088=LINE('',#3079,#1240);
#1089=LINE('',#3081,#1241);
#1090=VECTOR('',#2163,1000.);
#1091=VECTOR('',#2164,1000.);
#1092=VECTOR('',#2165,1000.);
#1093=VECTOR('',#2166,1000.);
#1094=VECTOR('',#2169,1000.);
#1095=VECTOR('',#2170,1000.);
#1096=VECTOR('',#2171,1000.);
#1097=VECTOR('',#2172,1000.);
#1098=VECTOR('',#2175,1000.);
#1099=VECTOR('',#2176,1000.);
#1100=VECTOR('',#2177,1000.);
#1101=VECTOR('',#2178,1000.);
#1102=VECTOR('',#2181,1000.);
#1103=VECTOR('',#2182,1000.);
#1104=VECTOR('',#2183,1000.);
#1105=VECTOR('',#2184,1000.);
#1106=VECTOR('',#2187,1000.);
#1107=VECTOR('',#2188,1000.);
#1108=VECTOR('',#2189,1000.);
#1109=VECTOR('',#2190,1000.);
#1110=VECTOR('',#2193,1000.);
#1111=VECTOR('',#2194,1000.);
#1112=VECTOR('',#2195,1000.);
#1113=VECTOR('',#2196,1000.);
#1114=VECTOR('',#2199,1000.);
#1115=VECTOR('',#2200,1000.);
#1116=VECTOR('',#2201,1000.);
#1117=VECTOR('',#2204,1000.);
#1118=VECTOR('',#2205,1000.);
#1119=VECTOR('',#2206,1000.);
#1120=VECTOR('',#2209,1000.);
#1121=VECTOR('',#2210,1000.);
#1122=VECTOR('',#2211,1000.);
#1123=VECTOR('',#2214,1000.);
#1124=VECTOR('',#2215,1000.);
#1125=VECTOR('',#2216,1000.);
#1126=VECTOR('',#2325,1000.);
#1127=VECTOR('',#2326,1000.);
#1128=VECTOR('',#2327,1000.);
#1129=VECTOR('',#2328,1000.);
#1130=VECTOR('',#2331,1000.);
#1131=VECTOR('',#2332,1000.);
#1132=VECTOR('',#2333,1000.);
#1133=VECTOR('',#2336,1000.);
#1134=VECTOR('',#2337,1000.);
#1135=VECTOR('',#2338,1000.);
#1136=VECTOR('',#2343,1000.);
#1137=VECTOR('',#2348,1000.);
#1138=VECTOR('',#2349,1000.);
#1139=VECTOR('',#2350,1000.);
#1140=VECTOR('',#2355,1000.);
#1141=VECTOR('',#2360,1000.);
#1142=VECTOR('',#2361,1000.);
#1143=VECTOR('',#2362,1000.);
#1144=VECTOR('',#2367,1000.);
#1145=VECTOR('',#2372,1000.);
#1146=VECTOR('',#2373,1000.);
#1147=VECTOR('',#2374,1000.);
#1148=VECTOR('',#2379,1000.);
#1149=VECTOR('',#2384,1000.);
#1150=VECTOR('',#2385,1000.);
#1151=VECTOR('',#2386,1000.);
#1152=VECTOR('',#2389,1000.);
#1153=VECTOR('',#2390,1000.);
#1154=VECTOR('',#2391,1000.);
#1155=VECTOR('',#2394,1000.);
#1156=VECTOR('',#2395,1000.);
#1157=VECTOR('',#2396,1000.);
#1158=VECTOR('',#2399,1000.);
#1159=VECTOR('',#2406,1000.);
#1160=VECTOR('',#2407,1000.);
#1161=VECTOR('',#2408,1000.);
#1162=VECTOR('',#2413,1000.);
#1163=VECTOR('',#2418,1000.);
#1164=VECTOR('',#2419,1000.);
#1165=VECTOR('',#2420,1000.);
#1166=VECTOR('',#2425,1000.);
#1167=VECTOR('',#2430,1000.);
#1168=VECTOR('',#2431,1000.);
#1169=VECTOR('',#2432,1000.);
#1170=VECTOR('',#2445,1000.);
#1171=VECTOR('',#2446,1000.);
#1172=VECTOR('',#2447,1000.);
#1173=VECTOR('',#2448,1000.);
#1174=VECTOR('',#2451,1000.);
#1175=VECTOR('',#2452,1000.);
#1176=VECTOR('',#2453,1000.);
#1177=VECTOR('',#2454,1000.);
#1178=VECTOR('',#2457,1000.);
#1179=VECTOR('',#2460,1000.);
#1180=VECTOR('',#2463,1000.);
#1181=VECTOR('',#2464,1000.);
#1182=VECTOR('',#2465,1000.);
#1183=VECTOR('',#2468,1000.);
#1184=VECTOR('',#2473,1000.);
#1185=VECTOR('',#2474,1000.);
#1186=VECTOR('',#2475,1000.);
#1187=VECTOR('',#2478,1000.);
#1188=VECTOR('',#2479,1000.);
#1189=VECTOR('',#2482,1000.);
#1190=VECTOR('',#2483,1000.);
#1191=VECTOR('',#2486,1000.);
#1192=VECTOR('',#2491,1000.);
#1193=VECTOR('',#2492,1000.);
#1194=VECTOR('',#2499,1000.);
#1195=VECTOR('',#2504,1000.);
#1196=VECTOR('',#2505,1000.);
#1197=VECTOR('',#2512,1000.);
#1198=VECTOR('',#2517,1000.);
#1199=VECTOR('',#2518,1000.);
#1200=VECTOR('',#2519,1000.);
#1201=VECTOR('',#2520,1000.);
#1202=VECTOR('',#2523,1000.);
#1203=VECTOR('',#2524,1000.);
#1204=VECTOR('',#2525,1000.);
#1205=VECTOR('',#2528,1000.);
#1206=VECTOR('',#2529,1000.);
#1207=VECTOR('',#2530,1000.);
#1208=VECTOR('',#2535,1000.);
#1209=VECTOR('',#2540,1000.);
#1210=VECTOR('',#2541,1000.);
#1211=VECTOR('',#2542,1000.);
#1212=VECTOR('',#2547,1000.);
#1213=VECTOR('',#2552,1000.);
#1214=VECTOR('',#2553,1000.);
#1215=VECTOR('',#2554,1000.);
#1216=VECTOR('',#2559,1000.);
#1217=VECTOR('',#2564,1000.);
#1218=VECTOR('',#2565,1000.);
#1219=VECTOR('',#2566,1000.);
#1220=VECTOR('',#2571,1000.);
#1221=VECTOR('',#2576,1000.);
#1222=VECTOR('',#2577,1000.);
#1223=VECTOR('',#2578,1000.);
#1224=VECTOR('',#2581,1000.);
#1225=VECTOR('',#2582,1000.);
#1226=VECTOR('',#2583,1000.);
#1227=VECTOR('',#2586,1000.);
#1228=VECTOR('',#2587,1000.);
#1229=VECTOR('',#2588,1000.);
#1230=VECTOR('',#2591,1000.);
#1231=VECTOR('',#2598,1000.);
#1232=VECTOR('',#2599,1000.);
#1233=VECTOR('',#2600,1000.);
#1234=VECTOR('',#2605,1000.);
#1235=VECTOR('',#2610,1000.);
#1236=VECTOR('',#2611,1000.);
#1237=VECTOR('',#2612,1000.);
#1238=VECTOR('',#2617,1000.);
#1239=VECTOR('',#2622,1000.);
#1240=VECTOR('',#2623,1000.);
#1241=VECTOR('',#2624,1000.);
#1242=EDGE_LOOP('',(#142,#143,#144,#145));
#1243=EDGE_LOOP('',(#146,#147,#148,#149));
#1244=EDGE_LOOP('',(#150,#151,#152,#153));
#1245=EDGE_LOOP('',(#154,#155,#156,#157));
#1246=EDGE_LOOP('',(#158,#159,#160,#161));
#1247=EDGE_LOOP('',(#162,#163,#164,#165));
#1248=EDGE_LOOP('',(#166,#167,#168,#169));
#1249=EDGE_LOOP('',(#170,#171,#172,#173));
#1250=EDGE_LOOP('',(#174,#175,#176,#177));
#1251=EDGE_LOOP('',(#178,#179,#180,#181));
#1252=EDGE_LOOP('',(#182,#183,#184,#185));
#1253=EDGE_LOOP('',(#186,#187,#188,#189));
#1254=EDGE_LOOP('',(#190,#191,#192,#193));
#1255=EDGE_LOOP('',(#194,#195,#196,#197));
#1256=EDGE_LOOP('',(#198,#199,#200,#201));
#1257=EDGE_LOOP('',(#202,#203,#204,#205));
#1258=EDGE_LOOP('',(#206,#207,#208,#209));
#1259=EDGE_LOOP('',(#210,#211,#212,#213));
#1260=EDGE_LOOP('',(#214,#215,#216));
#1261=EDGE_LOOP('',(#217,#218,#219,#220));
#1262=EDGE_LOOP('',(#221,#222,#223,#224));
#1263=EDGE_LOOP('',(#225,#226,#227));
#1264=EDGE_LOOP('',(#228,#229,#230));
#1265=EDGE_LOOP('',(#231,#232,#233,#234));
#1266=EDGE_LOOP('',(#235,#236,#237,#238));
#1267=EDGE_LOOP('',(#239,#240,#241));
#1268=EDGE_LOOP('',(#242,#243,#244));
#1269=EDGE_LOOP('',(#245,#246,#247,#248));
#1270=EDGE_LOOP('',(#249,#250,#251,#252));
#1271=EDGE_LOOP('',(#253,#254,#255));
#1272=EDGE_LOOP('',(#256,#257,#258));
#1273=EDGE_LOOP('',(#259,#260,#261,#262));
#1274=EDGE_LOOP('',(#263,#264,#265,#266));
#1275=EDGE_LOOP('',(#267,#268,#269));
#1276=EDGE_LOOP('',(#270,#271,#272,#273));
#1277=EDGE_LOOP('',(#274,#275,#276,#277));
#1278=EDGE_LOOP('',(#278,#279,#280,#281));
#1279=EDGE_LOOP('',(#282,#283,#284,#285));
#1280=EDGE_LOOP('',(#286,#287,#288,#289));
#1281=EDGE_LOOP('',(#290,#291,#292,#293));
#1282=EDGE_LOOP('',(#294,#295,#296,#297));
#1283=EDGE_LOOP('',(#298,#299,#300,#301));
#1284=EDGE_LOOP('',(#302,#303,#304,#305));
#1285=EDGE_LOOP('',(#306,#307,#308,#309));
#1286=EDGE_LOOP('',(#310,#311,#312,#313));
#1287=EDGE_LOOP('',(#314,#315,#316,#317));
#1288=EDGE_LOOP('',(#318,#319,#320,#321));
#1289=EDGE_LOOP('',(#322,#323,#324,#325));
#1290=EDGE_LOOP('',(#326,#327,#328,#329));
#1291=EDGE_LOOP('',(#330,#331,#332,#333));
#1292=EDGE_LOOP('',(#334,#335,#336,#337));
#1293=EDGE_LOOP('',(#338,#339,#340,#341));
#1294=EDGE_LOOP('',(#342,#343,#344,#345));
#1295=EDGE_LOOP('',(#346,#347,#348,#349));
#1296=EDGE_LOOP('',(#350,#351,#352,#353,#354,#355,#356,#357,#358,#359,#360,
#361,#362,#363,#364,#365,#366,#367,#368,#369));
#1297=EDGE_LOOP('',(#370,#371,#372,#373,#374,#375,#376,#377,#378,#379,#380,
#381,#382,#383,#384,#385,#386,#387,#388,#389));
#1298=EDGE_LOOP('',(#390,#391,#392,#393));
#1299=EDGE_LOOP('',(#394,#395,#396,#397));
#1300=EDGE_LOOP('',(#398,#399,#400,#401,#402,#403,#404,#405,#406,#407,#408,
#409));
#1301=EDGE_LOOP('',(#410,#411,#412,#413));
#1302=EDGE_LOOP('',(#414,#415,#416,#417));
#1303=EDGE_LOOP('',(#418,#419,#420,#421));
#1304=EDGE_LOOP('',(#422,#423,#424,#425));
#1305=EDGE_LOOP('',(#426,#427,#428,#429));
#1306=EDGE_LOOP('',(#430,#431,#432,#433));
#1307=EDGE_LOOP('',(#434,#435,#436,#437));
#1308=EDGE_LOOP('',(#438,#439,#440,#441));
#1309=EDGE_LOOP('',(#442,#443,#444,#445));
#1310=EDGE_LOOP('',(#446,#447,#448,#449,#450,#451,#452,#453,#454,#455,#456,
#457));
#1311=EDGE_LOOP('',(#458,#459,#460,#461));
#1312=EDGE_LOOP('',(#462,#463,#464,#465));
#1313=EDGE_LOOP('',(#466,#467,#468,#469));
#1314=EDGE_LOOP('',(#470,#471,#472,#473));
#1315=EDGE_LOOP('',(#474,#475,#476,#477));
#1316=EDGE_LOOP('',(#478,#479,#480,#481));
#1317=EDGE_LOOP('',(#482,#483,#484,#485));
#1318=EDGE_LOOP('',(#486,#487,#488,#489));
#1319=EDGE_LOOP('',(#490,#491,#492,#493));
#1320=EDGE_LOOP('',(#494,#495,#496,#497));
#1321=EDGE_LOOP('',(#498,#499,#500,#501));
#1322=EDGE_LOOP('',(#502,#503,#504,#505));
#1323=EDGE_LOOP('',(#506,#507,#508,#509));
#1324=EDGE_LOOP('',(#510,#511,#512,#513));
#1325=EDGE_LOOP('',(#514,#515,#516,#517));
#1326=EDGE_LOOP('',(#518,#519,#520,#521));
#1327=EDGE_LOOP('',(#522,#523,#524,#525));
#1328=EDGE_LOOP('',(#526,#527,#528,#529));
#1329=EDGE_LOOP('',(#530,#531,#532,#533));
#1330=EDGE_LOOP('',(#534,#535,#536,#537));
#1331=EDGE_LOOP('',(#538,#539,#540,#541));
#1332=EDGE_LOOP('',(#542,#543,#544,#545,#546,#547,#548,#549,#550,#551,#552,
#553,#554,#555,#556,#557,#558,#559,#560,#561));
#1333=EDGE_LOOP('',(#562,#563,#564,#565,#566,#567,#568,#569,#570,#571,#572,
#573,#574,#575,#576,#577,#578,#579,#580,#581));
#1334=FACE_BOUND('',#1242,.T.);
#1335=FACE_BOUND('',#1243,.T.);
#1336=FACE_BOUND('',#1244,.T.);
#1337=FACE_BOUND('',#1245,.T.);
#1338=FACE_BOUND('',#1246,.T.);
#1339=FACE_BOUND('',#1247,.T.);
#1340=FACE_BOUND('',#1248,.T.);
#1341=FACE_BOUND('',#1249,.T.);
#1342=FACE_BOUND('',#1250,.T.);
#1343=FACE_BOUND('',#1251,.T.);
#1344=FACE_BOUND('',#1252,.T.);
#1345=FACE_BOUND('',#1253,.T.);
#1346=FACE_BOUND('',#1254,.T.);
#1347=FACE_BOUND('',#1255,.T.);
#1348=FACE_BOUND('',#1256,.T.);
#1349=FACE_BOUND('',#1257,.T.);
#1350=FACE_BOUND('',#1258,.T.);
#1351=FACE_BOUND('',#1259,.T.);
#1352=FACE_BOUND('',#1260,.T.);
#1353=FACE_BOUND('',#1261,.T.);
#1354=FACE_BOUND('',#1262,.T.);
#1355=FACE_BOUND('',#1263,.T.);
#1356=FACE_BOUND('',#1264,.T.);
#1357=FACE_BOUND('',#1265,.T.);
#1358=FACE_BOUND('',#1266,.T.);
#1359=FACE_BOUND('',#1267,.T.);
#1360=FACE_BOUND('',#1268,.T.);
#1361=FACE_BOUND('',#1269,.T.);
#1362=FACE_BOUND('',#1270,.T.);
#1363=FACE_BOUND('',#1271,.T.);
#1364=FACE_BOUND('',#1272,.T.);
#1365=FACE_BOUND('',#1273,.T.);
#1366=FACE_BOUND('',#1274,.T.);
#1367=FACE_BOUND('',#1275,.T.);
#1368=FACE_BOUND('',#1276,.T.);
#1369=FACE_BOUND('',#1277,.T.);
#1370=FACE_BOUND('',#1278,.T.);
#1371=FACE_BOUND('',#1279,.T.);
#1372=FACE_BOUND('',#1280,.T.);
#1373=FACE_BOUND('',#1281,.T.);
#1374=FACE_BOUND('',#1282,.T.);
#1375=FACE_BOUND('',#1283,.T.);
#1376=FACE_BOUND('',#1284,.T.);
#1377=FACE_BOUND('',#1285,.T.);
#1378=FACE_BOUND('',#1286,.T.);
#1379=FACE_BOUND('',#1287,.T.);
#1380=FACE_BOUND('',#1288,.T.);
#1381=FACE_BOUND('',#1289,.T.);
#1382=FACE_BOUND('',#1290,.T.);
#1383=FACE_BOUND('',#1291,.T.);
#1384=FACE_BOUND('',#1292,.T.);
#1385=FACE_BOUND('',#1293,.T.);
#1386=FACE_BOUND('',#1294,.T.);
#1387=FACE_BOUND('',#1295,.T.);
#1388=FACE_BOUND('',#1296,.T.);
#1389=FACE_BOUND('',#1297,.T.);
#1390=FACE_BOUND('',#1298,.T.);
#1391=FACE_BOUND('',#1299,.T.);
#1392=FACE_BOUND('',#1300,.T.);
#1393=FACE_BOUND('',#1301,.T.);
#1394=FACE_BOUND('',#1302,.T.);
#1395=FACE_BOUND('',#1303,.T.);
#1396=FACE_BOUND('',#1304,.T.);
#1397=FACE_BOUND('',#1305,.T.);
#1398=FACE_BOUND('',#1306,.T.);
#1399=FACE_BOUND('',#1307,.T.);
#1400=FACE_BOUND('',#1308,.T.);
#1401=FACE_BOUND('',#1309,.T.);
#1402=FACE_BOUND('',#1310,.T.);
#1403=FACE_BOUND('',#1311,.T.);
#1404=FACE_BOUND('',#1312,.T.);
#1405=FACE_BOUND('',#1313,.T.);
#1406=FACE_BOUND('',#1314,.T.);
#1407=FACE_BOUND('',#1315,.T.);
#1408=FACE_BOUND('',#1316,.T.);
#1409=FACE_BOUND('',#1317,.T.);
#1410=FACE_BOUND('',#1318,.T.);
#1411=FACE_BOUND('',#1319,.T.);
#1412=FACE_BOUND('',#1320,.T.);
#1413=FACE_BOUND('',#1321,.T.);
#1414=FACE_BOUND('',#1322,.T.);
#1415=FACE_BOUND('',#1323,.T.);
#1416=FACE_BOUND('',#1324,.T.);
#1417=FACE_BOUND('',#1325,.T.);
#1418=FACE_BOUND('',#1326,.T.);
#1419=FACE_BOUND('',#1327,.T.);
#1420=FACE_BOUND('',#1328,.T.);
#1421=FACE_BOUND('',#1329,.T.);
#1422=FACE_BOUND('',#1330,.T.);
#1423=FACE_BOUND('',#1331,.T.);
#1424=FACE_BOUND('',#1332,.T.);
#1425=FACE_BOUND('',#1333,.T.);
#1426=PLANE('',#1997);
#1427=PLANE('',#1998);
#1428=PLANE('',#1999);
#1429=PLANE('',#2000);
#1430=PLANE('',#2001);
#1431=PLANE('',#2002);
#1432=PLANE('',#2003);
#1433=PLANE('',#2004);
#1434=PLANE('',#2005);
#1435=PLANE('',#2006);
#1436=PLANE('',#2060);
#1437=PLANE('',#2061);
#1438=PLANE('',#2062);
#1439=PLANE('',#2066);
#1440=PLANE('',#2070);
#1441=PLANE('',#2074);
#1442=PLANE('',#2078);
#1443=PLANE('',#2079);
#1444=PLANE('',#2080);
#1445=PLANE('',#2084);
#1446=PLANE('',#2088);
#1447=PLANE('',#2092);
#1448=PLANE('',#2096);
#1449=PLANE('',#2097);
#1450=PLANE('',#2098);
#1451=PLANE('',#2099);
#1452=PLANE('',#2100);
#1453=PLANE('',#2105);
#1454=PLANE('',#2106);
#1455=PLANE('',#2107);
#1456=PLANE('',#2110);
#1457=PLANE('',#2115);
#1458=PLANE('',#2118);
#1459=PLANE('',#2119);
#1460=PLANE('',#2120);
#1461=PLANE('',#2121);
#1462=PLANE('',#2122);
#1463=PLANE('',#2126);
#1464=PLANE('',#2130);
#1465=PLANE('',#2134);
#1466=PLANE('',#2138);
#1467=PLANE('',#2139);
#1468=PLANE('',#2140);
#1469=PLANE('',#2144);
#1470=PLANE('',#2148);
#1471=PLANE('',#2152);
#1472=PLANE('',#2156);
#1473=PLANE('',#2157);
#1474=ADVANCED_FACE('',(#1334),#1426,.F.);
#1475=ADVANCED_FACE('',(#1335),#1427,.F.);
#1476=ADVANCED_FACE('',(#1336),#1428,.F.);
#1477=ADVANCED_FACE('',(#1337),#1429,.F.);
#1478=ADVANCED_FACE('',(#1338),#1430,.F.);
#1479=ADVANCED_FACE('',(#1339),#1431,.T.);
#1480=ADVANCED_FACE('',(#1340),#1432,.T.);
#1481=ADVANCED_FACE('',(#1341),#1433,.T.);
#1482=ADVANCED_FACE('',(#1342),#1434,.T.);
#1483=ADVANCED_FACE('',(#1343),#1435,.T.);
#1484=ADVANCED_FACE('',(#1344),#106,.T.);
#1485=ADVANCED_FACE('',(#1345),#107,.T.);
#1486=ADVANCED_FACE('',(#1346),#108,.T.);
#1487=ADVANCED_FACE('',(#1347),#109,.T.);
#1488=ADVANCED_FACE('',(#1348),#110,.T.);
#1489=ADVANCED_FACE('',(#1349),#111,.T.);
#1490=ADVANCED_FACE('',(#1350),#112,.T.);
#1491=ADVANCED_FACE('',(#1351),#113,.T.);
#1492=ADVANCED_FACE('',(#1352),#34,.T.);
#1493=ADVANCED_FACE('',(#1353),#114,.T.);
#1494=ADVANCED_FACE('',(#1354),#115,.T.);
#1495=ADVANCED_FACE('',(#1355),#35,.T.);
#1496=ADVANCED_FACE('',(#1356),#36,.T.);
#1497=ADVANCED_FACE('',(#1357),#116,.T.);
#1498=ADVANCED_FACE('',(#1358),#117,.T.);
#1499=ADVANCED_FACE('',(#1359),#37,.T.);
#1500=ADVANCED_FACE('',(#1360),#38,.T.);
#1501=ADVANCED_FACE('',(#1361),#118,.T.);
#1502=ADVANCED_FACE('',(#1362),#119,.T.);
#1503=ADVANCED_FACE('',(#1363),#39,.T.);
#1504=ADVANCED_FACE('',(#1364),#40,.T.);
#1505=ADVANCED_FACE('',(#1365),#120,.T.);
#1506=ADVANCED_FACE('',(#1366),#121,.T.);
#1507=ADVANCED_FACE('',(#1367),#41,.T.);
#1508=ADVANCED_FACE('',(#1368),#1436,.F.);
#1509=ADVANCED_FACE('',(#1369),#1437,.F.);
#1510=ADVANCED_FACE('',(#1370),#1438,.F.);
#1511=ADVANCED_FACE('',(#1371),#122,.T.);
#1512=ADVANCED_FACE('',(#1372),#1439,.F.);
#1513=ADVANCED_FACE('',(#1373),#123,.F.);
#1514=ADVANCED_FACE('',(#1374),#1440,.F.);
#1515=ADVANCED_FACE('',(#1375),#124,.F.);
#1516=ADVANCED_FACE('',(#1376),#1441,.F.);
#1517=ADVANCED_FACE('',(#1377),#125,.T.);
#1518=ADVANCED_FACE('',(#1378),#1442,.F.);
#1519=ADVANCED_FACE('',(#1379),#1443,.F.);
#1520=ADVANCED_FACE('',(#1380),#1444,.F.);
#1521=ADVANCED_FACE('',(#1381),#126,.F.);
#1522=ADVANCED_FACE('',(#1382),#1445,.F.);
#1523=ADVANCED_FACE('',(#1383),#127,.T.);
#1524=ADVANCED_FACE('',(#1384),#1446,.F.);
#1525=ADVANCED_FACE('',(#1385),#128,.T.);
#1526=ADVANCED_FACE('',(#1386),#1447,.F.);
#1527=ADVANCED_FACE('',(#1387),#129,.F.);
#1528=ADVANCED_FACE('',(#1388),#1448,.T.);
#1529=ADVANCED_FACE('',(#1389),#1449,.F.);
#1530=ADVANCED_FACE('',(#1390),#1450,.F.);
#1531=ADVANCED_FACE('',(#1391),#1451,.F.);
#1532=ADVANCED_FACE('',(#1392),#1452,.F.);
#1533=ADVANCED_FACE('',(#1393),#1453,.F.);
#1534=ADVANCED_FACE('',(#1394),#1454,.F.);
#1535=ADVANCED_FACE('',(#1395),#1455,.F.);
#1536=ADVANCED_FACE('',(#1396),#130,.T.);
#1537=ADVANCED_FACE('',(#1397),#1456,.F.);
#1538=ADVANCED_FACE('',(#1398),#131,.F.);
#1539=ADVANCED_FACE('',(#1399),#132,.T.);
#1540=ADVANCED_FACE('',(#1400),#1457,.F.);
#1541=ADVANCED_FACE('',(#1401),#133,.F.);
#1542=ADVANCED_FACE('',(#1402),#1458,.T.);
#1543=ADVANCED_FACE('',(#1403),#1459,.T.);
#1544=ADVANCED_FACE('',(#1404),#1460,.F.);
#1545=ADVANCED_FACE('',(#1405),#1461,.F.);
#1546=ADVANCED_FACE('',(#1406),#1462,.F.);
#1547=ADVANCED_FACE('',(#1407),#134,.T.);
#1548=ADVANCED_FACE('',(#1408),#1463,.F.);
#1549=ADVANCED_FACE('',(#1409),#135,.F.);
#1550=ADVANCED_FACE('',(#1410),#1464,.F.);
#1551=ADVANCED_FACE('',(#1411),#136,.F.);
#1552=ADVANCED_FACE('',(#1412),#1465,.F.);
#1553=ADVANCED_FACE('',(#1413),#137,.T.);
#1554=ADVANCED_FACE('',(#1414),#1466,.F.);
#1555=ADVANCED_FACE('',(#1415),#1467,.F.);
#1556=ADVANCED_FACE('',(#1416),#1468,.F.);
#1557=ADVANCED_FACE('',(#1417),#138,.F.);
#1558=ADVANCED_FACE('',(#1418),#1469,.F.);
#1559=ADVANCED_FACE('',(#1419),#139,.T.);
#1560=ADVANCED_FACE('',(#1420),#1470,.F.);
#1561=ADVANCED_FACE('',(#1421),#140,.T.);
#1562=ADVANCED_FACE('',(#1422),#1471,.F.);
#1563=ADVANCED_FACE('',(#1423),#141,.F.);
#1564=ADVANCED_FACE('',(#1424),#1472,.T.);
#1565=ADVANCED_FACE('',(#1425),#1473,.F.);
#1566=CLOSED_SHELL('',(#1474,#1475,#1476,#1477,#1478,#1479,#1480,#1481,
#1482,#1483,#1484,#1485,#1486,#1487,#1488,#1489,#1490,#1491,#1492,#1493,
#1494,#1495,#1496,#1497,#1498,#1499,#1500,#1501,#1502,#1503,#1504,#1505,
#1506,#1507));
#1567=CLOSED_SHELL('',(#1508,#1509,#1510,#1511,#1512,#1513,#1514,#1515,
#1516,#1517,#1518,#1519,#1520,#1521,#1522,#1523,#1524,#1525,#1526,#1527,
#1528,#1529));
#1568=CLOSED_SHELL('',(#1530,#1531,#1532,#1533,#1534,#1535,#1536,#1537,
#1538,#1539,#1540,#1541,#1542,#1543));
#1569=CLOSED_SHELL('',(#1544,#1545,#1546,#1547,#1548,#1549,#1550,#1551,
#1552,#1553,#1554,#1555,#1556,#1557,#1558,#1559,#1560,#1561,#1562,#1563,
#1564,#1565));
#1570=STYLED_ITEM('',(#1629),#1986);
#1571=STYLED_ITEM('',(#1630),#1508);
#1572=STYLED_ITEM('',(#1631),#1509);
#1573=STYLED_ITEM('',(#1632),#1510);
#1574=STYLED_ITEM('',(#1633),#1511);
#1575=STYLED_ITEM('',(#1634),#1512);
#1576=STYLED_ITEM('',(#1635),#1513);
#1577=STYLED_ITEM('',(#1636),#1514);
#1578=STYLED_ITEM('',(#1637),#1515);
#1579=STYLED_ITEM('',(#1638),#1516);
#1580=STYLED_ITEM('',(#1639),#1517);
#1581=STYLED_ITEM('',(#1640),#1518);
#1582=STYLED_ITEM('',(#1641),#1519);
#1583=STYLED_ITEM('',(#1642),#1520);
#1584=STYLED_ITEM('',(#1643),#1521);
#1585=STYLED_ITEM('',(#1644),#1522);
#1586=STYLED_ITEM('',(#1645),#1523);
#1587=STYLED_ITEM('',(#1646),#1524);
#1588=STYLED_ITEM('',(#1647),#1525);
#1589=STYLED_ITEM('',(#1648),#1526);
#1590=STYLED_ITEM('',(#1649),#1527);
#1591=STYLED_ITEM('',(#1650),#1528);
#1592=STYLED_ITEM('',(#1651),#1529);
#1593=STYLED_ITEM('',(#1652),#1530);
#1594=STYLED_ITEM('',(#1653),#1531);
#1595=STYLED_ITEM('',(#1654),#1532);
#1596=STYLED_ITEM('',(#1655),#1533);
#1597=STYLED_ITEM('',(#1656),#1534);
#1598=STYLED_ITEM('',(#1657),#1535);
#1599=STYLED_ITEM('',(#1658),#1536);
#1600=STYLED_ITEM('',(#1659),#1537);
#1601=STYLED_ITEM('',(#1660),#1538);
#1602=STYLED_ITEM('',(#1661),#1539);
#1603=STYLED_ITEM('',(#1662),#1540);
#1604=STYLED_ITEM('',(#1663),#1541);
#1605=STYLED_ITEM('',(#1664),#1542);
#1606=STYLED_ITEM('',(#1665),#1543);
#1607=STYLED_ITEM('',(#1666),#1544);
#1608=STYLED_ITEM('',(#1667),#1545);
#1609=STYLED_ITEM('',(#1668),#1546);
#1610=STYLED_ITEM('',(#1669),#1547);
#1611=STYLED_ITEM('',(#1670),#1548);
#1612=STYLED_ITEM('',(#1671),#1549);
#1613=STYLED_ITEM('',(#1672),#1550);
#1614=STYLED_ITEM('',(#1673),#1551);
#1615=STYLED_ITEM('',(#1674),#1552);
#1616=STYLED_ITEM('',(#1675),#1553);
#1617=STYLED_ITEM('',(#1676),#1554);
#1618=STYLED_ITEM('',(#1677),#1555);
#1619=STYLED_ITEM('',(#1678),#1556);
#1620=STYLED_ITEM('',(#1679),#1557);
#1621=STYLED_ITEM('',(#1680),#1558);
#1622=STYLED_ITEM('',(#1681),#1559);
#1623=STYLED_ITEM('',(#1682),#1560);
#1624=STYLED_ITEM('',(#1683),#1561);
#1625=STYLED_ITEM('',(#1684),#1562);
#1626=STYLED_ITEM('',(#1685),#1563);
#1627=STYLED_ITEM('',(#1686),#1564);
#1628=STYLED_ITEM('',(#1687),#1565);
#1629=PRESENTATION_STYLE_ASSIGNMENT((#1688));
#1630=PRESENTATION_STYLE_ASSIGNMENT((#1689));
#1631=PRESENTATION_STYLE_ASSIGNMENT((#1690));
#1632=PRESENTATION_STYLE_ASSIGNMENT((#1691));
#1633=PRESENTATION_STYLE_ASSIGNMENT((#1692));
#1634=PRESENTATION_STYLE_ASSIGNMENT((#1693));
#1635=PRESENTATION_STYLE_ASSIGNMENT((#1694));
#1636=PRESENTATION_STYLE_ASSIGNMENT((#1695));
#1637=PRESENTATION_STYLE_ASSIGNMENT((#1696));
#1638=PRESENTATION_STYLE_ASSIGNMENT((#1697));
#1639=PRESENTATION_STYLE_ASSIGNMENT((#1698));
#1640=PRESENTATION_STYLE_ASSIGNMENT((#1699));
#1641=PRESENTATION_STYLE_ASSIGNMENT((#1700));
#1642=PRESENTATION_STYLE_ASSIGNMENT((#1701));
#1643=PRESENTATION_STYLE_ASSIGNMENT((#1702));
#1644=PRESENTATION_STYLE_ASSIGNMENT((#1703));
#1645=PRESENTATION_STYLE_ASSIGNMENT((#1704));
#1646=PRESENTATION_STYLE_ASSIGNMENT((#1705));
#1647=PRESENTATION_STYLE_ASSIGNMENT((#1706));
#1648=PRESENTATION_STYLE_ASSIGNMENT((#1707));
#1649=PRESENTATION_STYLE_ASSIGNMENT((#1708));
#1650=PRESENTATION_STYLE_ASSIGNMENT((#1709));
#1651=PRESENTATION_STYLE_ASSIGNMENT((#1710));
#1652=PRESENTATION_STYLE_ASSIGNMENT((#1711));
#1653=PRESENTATION_STYLE_ASSIGNMENT((#1712));
#1654=PRESENTATION_STYLE_ASSIGNMENT((#1713));
#1655=PRESENTATION_STYLE_ASSIGNMENT((#1714));
#1656=PRESENTATION_STYLE_ASSIGNMENT((#1715));
#1657=PRESENTATION_STYLE_ASSIGNMENT((#1716));
#1658=PRESENTATION_STYLE_ASSIGNMENT((#1717));
#1659=PRESENTATION_STYLE_ASSIGNMENT((#1718));
#1660=PRESENTATION_STYLE_ASSIGNMENT((#1719));
#1661=PRESENTATION_STYLE_ASSIGNMENT((#1720));
#1662=PRESENTATION_STYLE_ASSIGNMENT((#1721));
#1663=PRESENTATION_STYLE_ASSIGNMENT((#1722));
#1664=PRESENTATION_STYLE_ASSIGNMENT((#1723));
#1665=PRESENTATION_STYLE_ASSIGNMENT((#1724));
#1666=PRESENTATION_STYLE_ASSIGNMENT((#1725));
#1667=PRESENTATION_STYLE_ASSIGNMENT((#1726));
#1668=PRESENTATION_STYLE_ASSIGNMENT((#1727));
#1669=PRESENTATION_STYLE_ASSIGNMENT((#1728));
#1670=PRESENTATION_STYLE_ASSIGNMENT((#1729));
#1671=PRESENTATION_STYLE_ASSIGNMENT((#1730));
#1672=PRESENTATION_STYLE_ASSIGNMENT((#1731));
#1673=PRESENTATION_STYLE_ASSIGNMENT((#1732));
#1674=PRESENTATION_STYLE_ASSIGNMENT((#1733));
#1675=PRESENTATION_STYLE_ASSIGNMENT((#1734));
#1676=PRESENTATION_STYLE_ASSIGNMENT((#1735));
#1677=PRESENTATION_STYLE_ASSIGNMENT((#1736));
#1678=PRESENTATION_STYLE_ASSIGNMENT((#1737));
#1679=PRESENTATION_STYLE_ASSIGNMENT((#1738));
#1680=PRESENTATION_STYLE_ASSIGNMENT((#1739));
#1681=PRESENTATION_STYLE_ASSIGNMENT((#1740));
#1682=PRESENTATION_STYLE_ASSIGNMENT((#1741));
#1683=PRESENTATION_STYLE_ASSIGNMENT((#1742));
#1684=PRESENTATION_STYLE_ASSIGNMENT((#1743));
#1685=PRESENTATION_STYLE_ASSIGNMENT((#1744));
#1686=PRESENTATION_STYLE_ASSIGNMENT((#1745));
#1687=PRESENTATION_STYLE_ASSIGNMENT((#1746));
#1688=SURFACE_STYLE_USAGE(.BOTH.,#1747);
#1689=SURFACE_STYLE_USAGE(.BOTH.,#1748);
#1690=SURFACE_STYLE_USAGE(.BOTH.,#1749);
#1691=SURFACE_STYLE_USAGE(.BOTH.,#1750);
#1692=SURFACE_STYLE_USAGE(.BOTH.,#1751);
#1693=SURFACE_STYLE_USAGE(.BOTH.,#1752);
#1694=SURFACE_STYLE_USAGE(.BOTH.,#1753);
#1695=SURFACE_STYLE_USAGE(.BOTH.,#1754);
#1696=SURFACE_STYLE_USAGE(.BOTH.,#1755);
#1697=SURFACE_STYLE_USAGE(.BOTH.,#1756);
#1698=SURFACE_STYLE_USAGE(.BOTH.,#1757);
#1699=SURFACE_STYLE_USAGE(.BOTH.,#1758);
#1700=SURFACE_STYLE_USAGE(.BOTH.,#1759);
#1701=SURFACE_STYLE_USAGE(.BOTH.,#1760);
#1702=SURFACE_STYLE_USAGE(.BOTH.,#1761);
#1703=SURFACE_STYLE_USAGE(.BOTH.,#1762);
#1704=SURFACE_STYLE_USAGE(.BOTH.,#1763);
#1705=SURFACE_STYLE_USAGE(.BOTH.,#1764);
#1706=SURFACE_STYLE_USAGE(.BOTH.,#1765);
#1707=SURFACE_STYLE_USAGE(.BOTH.,#1766);
#1708=SURFACE_STYLE_USAGE(.BOTH.,#1767);
#1709=SURFACE_STYLE_USAGE(.BOTH.,#1768);
#1710=SURFACE_STYLE_USAGE(.BOTH.,#1769);
#1711=SURFACE_STYLE_USAGE(.BOTH.,#1770);
#1712=SURFACE_STYLE_USAGE(.BOTH.,#1771);
#1713=SURFACE_STYLE_USAGE(.BOTH.,#1772);
#1714=SURFACE_STYLE_USAGE(.BOTH.,#1773);
#1715=SURFACE_STYLE_USAGE(.BOTH.,#1774);
#1716=SURFACE_STYLE_USAGE(.BOTH.,#1775);
#1717=SURFACE_STYLE_USAGE(.BOTH.,#1776);
#1718=SURFACE_STYLE_USAGE(.BOTH.,#1777);
#1719=SURFACE_STYLE_USAGE(.BOTH.,#1778);
#1720=SURFACE_STYLE_USAGE(.BOTH.,#1779);
#1721=SURFACE_STYLE_USAGE(.BOTH.,#1780);
#1722=SURFACE_STYLE_USAGE(.BOTH.,#1781);
#1723=SURFACE_STYLE_USAGE(.BOTH.,#1782);
#1724=SURFACE_STYLE_USAGE(.BOTH.,#1783);
#1725=SURFACE_STYLE_USAGE(.BOTH.,#1784);
#1726=SURFACE_STYLE_USAGE(.BOTH.,#1785);
#1727=SURFACE_STYLE_USAGE(.BOTH.,#1786);
#1728=SURFACE_STYLE_USAGE(.BOTH.,#1787);
#1729=SURFACE_STYLE_USAGE(.BOTH.,#1788);
#1730=SURFACE_STYLE_USAGE(.BOTH.,#1789);
#1731=SURFACE_STYLE_USAGE(.BOTH.,#1790);
#1732=SURFACE_STYLE_USAGE(.BOTH.,#1791);
#1733=SURFACE_STYLE_USAGE(.BOTH.,#1792);
#1734=SURFACE_STYLE_USAGE(.BOTH.,#1793);
#1735=SURFACE_STYLE_USAGE(.BOTH.,#1794);
#1736=SURFACE_STYLE_USAGE(.BOTH.,#1795);
#1737=SURFACE_STYLE_USAGE(.BOTH.,#1796);
#1738=SURFACE_STYLE_USAGE(.BOTH.,#1797);
#1739=SURFACE_STYLE_USAGE(.BOTH.,#1798);
#1740=SURFACE_STYLE_USAGE(.BOTH.,#1799);
#1741=SURFACE_STYLE_USAGE(.BOTH.,#1800);
#1742=SURFACE_STYLE_USAGE(.BOTH.,#1801);
#1743=SURFACE_STYLE_USAGE(.BOTH.,#1802);
#1744=SURFACE_STYLE_USAGE(.BOTH.,#1803);
#1745=SURFACE_STYLE_USAGE(.BOTH.,#1804);
#1746=SURFACE_STYLE_USAGE(.BOTH.,#1805);
#1747=SURFACE_SIDE_STYLE('',(#1806));
#1748=SURFACE_SIDE_STYLE('',(#1807));
#1749=SURFACE_SIDE_STYLE('',(#1808));
#1750=SURFACE_SIDE_STYLE('',(#1809));
#1751=SURFACE_SIDE_STYLE('',(#1810));
#1752=SURFACE_SIDE_STYLE('',(#1811));
#1753=SURFACE_SIDE_STYLE('',(#1812));
#1754=SURFACE_SIDE_STYLE('',(#1813));
#1755=SURFACE_SIDE_STYLE('',(#1814));
#1756=SURFACE_SIDE_STYLE('',(#1815));
#1757=SURFACE_SIDE_STYLE('',(#1816));
#1758=SURFACE_SIDE_STYLE('',(#1817));
#1759=SURFACE_SIDE_STYLE('',(#1818));
#1760=SURFACE_SIDE_STYLE('',(#1819));
#1761=SURFACE_SIDE_STYLE('',(#1820));
#1762=SURFACE_SIDE_STYLE('',(#1821));
#1763=SURFACE_SIDE_STYLE('',(#1822));
#1764=SURFACE_SIDE_STYLE('',(#1823));
#1765=SURFACE_SIDE_STYLE('',(#1824));
#1766=SURFACE_SIDE_STYLE('',(#1825));
#1767=SURFACE_SIDE_STYLE('',(#1826));
#1768=SURFACE_SIDE_STYLE('',(#1827));
#1769=SURFACE_SIDE_STYLE('',(#1828));
#1770=SURFACE_SIDE_STYLE('',(#1829));
#1771=SURFACE_SIDE_STYLE('',(#1830));
#1772=SURFACE_SIDE_STYLE('',(#1831));
#1773=SURFACE_SIDE_STYLE('',(#1832));
#1774=SURFACE_SIDE_STYLE('',(#1833));
#1775=SURFACE_SIDE_STYLE('',(#1834));
#1776=SURFACE_SIDE_STYLE('',(#1835));
#1777=SURFACE_SIDE_STYLE('',(#1836));
#1778=SURFACE_SIDE_STYLE('',(#1837));
#1779=SURFACE_SIDE_STYLE('',(#1838));
#1780=SURFACE_SIDE_STYLE('',(#1839));
#1781=SURFACE_SIDE_STYLE('',(#1840));
#1782=SURFACE_SIDE_STYLE('',(#1841));
#1783=SURFACE_SIDE_STYLE('',(#1842));
#1784=SURFACE_SIDE_STYLE('',(#1843));
#1785=SURFACE_SIDE_STYLE('',(#1844));
#1786=SURFACE_SIDE_STYLE('',(#1845));
#1787=SURFACE_SIDE_STYLE('',(#1846));
#1788=SURFACE_SIDE_STYLE('',(#1847));
#1789=SURFACE_SIDE_STYLE('',(#1848));
#1790=SURFACE_SIDE_STYLE('',(#1849));
#1791=SURFACE_SIDE_STYLE('',(#1850));
#1792=SURFACE_SIDE_STYLE('',(#1851));
#1793=SURFACE_SIDE_STYLE('',(#1852));
#1794=SURFACE_SIDE_STYLE('',(#1853));
#1795=SURFACE_SIDE_STYLE('',(#1854));
#1796=SURFACE_SIDE_STYLE('',(#1855));
#1797=SURFACE_SIDE_STYLE('',(#1856));
#1798=SURFACE_SIDE_STYLE('',(#1857));
#1799=SURFACE_SIDE_STYLE('',(#1858));
#1800=SURFACE_SIDE_STYLE('',(#1859));
#1801=SURFACE_SIDE_STYLE('',(#1860));
#1802=SURFACE_SIDE_STYLE('',(#1861));
#1803=SURFACE_SIDE_STYLE('',(#1862));
#1804=SURFACE_SIDE_STYLE('',(#1863));
#1805=SURFACE_SIDE_STYLE('',(#1864));
#1806=SURFACE_STYLE_FILL_AREA(#1865);
#1807=SURFACE_STYLE_FILL_AREA(#1866);
#1808=SURFACE_STYLE_FILL_AREA(#1867);
#1809=SURFACE_STYLE_FILL_AREA(#1868);
#1810=SURFACE_STYLE_FILL_AREA(#1869);
#1811=SURFACE_STYLE_FILL_AREA(#1870);
#1812=SURFACE_STYLE_FILL_AREA(#1871);
#1813=SURFACE_STYLE_FILL_AREA(#1872);
#1814=SURFACE_STYLE_FILL_AREA(#1873);
#1815=SURFACE_STYLE_FILL_AREA(#1874);
#1816=SURFACE_STYLE_FILL_AREA(#1875);
#1817=SURFACE_STYLE_FILL_AREA(#1876);
#1818=SURFACE_STYLE_FILL_AREA(#1877);
#1819=SURFACE_STYLE_FILL_AREA(#1878);
#1820=SURFACE_STYLE_FILL_AREA(#1879);
#1821=SURFACE_STYLE_FILL_AREA(#1880);
#1822=SURFACE_STYLE_FILL_AREA(#1881);
#1823=SURFACE_STYLE_FILL_AREA(#1882);
#1824=SURFACE_STYLE_FILL_AREA(#1883);
#1825=SURFACE_STYLE_FILL_AREA(#1884);
#1826=SURFACE_STYLE_FILL_AREA(#1885);
#1827=SURFACE_STYLE_FILL_AREA(#1886);
#1828=SURFACE_STYLE_FILL_AREA(#1887);
#1829=SURFACE_STYLE_FILL_AREA(#1888);
#1830=SURFACE_STYLE_FILL_AREA(#1889);
#1831=SURFACE_STYLE_FILL_AREA(#1890);
#1832=SURFACE_STYLE_FILL_AREA(#1891);
#1833=SURFACE_STYLE_FILL_AREA(#1892);
#1834=SURFACE_STYLE_FILL_AREA(#1893);
#1835=SURFACE_STYLE_FILL_AREA(#1894);
#1836=SURFACE_STYLE_FILL_AREA(#1895);
#1837=SURFACE_STYLE_FILL_AREA(#1896);
#1838=SURFACE_STYLE_FILL_AREA(#1897);
#1839=SURFACE_STYLE_FILL_AREA(#1898);
#1840=SURFACE_STYLE_FILL_AREA(#1899);
#1841=SURFACE_STYLE_FILL_AREA(#1900);
#1842=SURFACE_STYLE_FILL_AREA(#1901);
#1843=SURFACE_STYLE_FILL_AREA(#1902);
#1844=SURFACE_STYLE_FILL_AREA(#1903);
#1845=SURFACE_STYLE_FILL_AREA(#1904);
#1846=SURFACE_STYLE_FILL_AREA(#1905);
#1847=SURFACE_STYLE_FILL_AREA(#1906);
#1848=SURFACE_STYLE_FILL_AREA(#1907);
#1849=SURFACE_STYLE_FILL_AREA(#1908);
#1850=SURFACE_STYLE_FILL_AREA(#1909);
#1851=SURFACE_STYLE_FILL_AREA(#1910);
#1852=SURFACE_STYLE_FILL_AREA(#1911);
#1853=SURFACE_STYLE_FILL_AREA(#1912);
#1854=SURFACE_STYLE_FILL_AREA(#1913);
#1855=SURFACE_STYLE_FILL_AREA(#1914);
#1856=SURFACE_STYLE_FILL_AREA(#1915);
#1857=SURFACE_STYLE_FILL_AREA(#1916);
#1858=SURFACE_STYLE_FILL_AREA(#1917);
#1859=SURFACE_STYLE_FILL_AREA(#1918);
#1860=SURFACE_STYLE_FILL_AREA(#1919);
#1861=SURFACE_STYLE_FILL_AREA(#1920);
#1862=SURFACE_STYLE_FILL_AREA(#1921);
#1863=SURFACE_STYLE_FILL_AREA(#1922);
#1864=SURFACE_STYLE_FILL_AREA(#1923);
#1865=FILL_AREA_STYLE('',(#1924));
#1866=FILL_AREA_STYLE('',(#1925));
#1867=FILL_AREA_STYLE('',(#1926));
#1868=FILL_AREA_STYLE('',(#1927));
#1869=FILL_AREA_STYLE('',(#1928));
#1870=FILL_AREA_STYLE('',(#1929));
#1871=FILL_AREA_STYLE('',(#1930));
#1872=FILL_AREA_STYLE('',(#1931));
#1873=FILL_AREA_STYLE('',(#1932));
#1874=FILL_AREA_STYLE('',(#1933));
#1875=FILL_AREA_STYLE('',(#1934));
#1876=FILL_AREA_STYLE('',(#1935));
#1877=FILL_AREA_STYLE('',(#1936));
#1878=FILL_AREA_STYLE('',(#1937));
#1879=FILL_AREA_STYLE('',(#1938));
#1880=FILL_AREA_STYLE('',(#1939));
#1881=FILL_AREA_STYLE('',(#1940));
#1882=FILL_AREA_STYLE('',(#1941));
#1883=FILL_AREA_STYLE('',(#1942));
#1884=FILL_AREA_STYLE('',(#1943));
#1885=FILL_AREA_STYLE('',(#1944));
#1886=FILL_AREA_STYLE('',(#1945));
#1887=FILL_AREA_STYLE('',(#1946));
#1888=FILL_AREA_STYLE('',(#1947));
#1889=FILL_AREA_STYLE('',(#1948));
#1890=FILL_AREA_STYLE('',(#1949));
#1891=FILL_AREA_STYLE('',(#1950));
#1892=FILL_AREA_STYLE('',(#1951));
#1893=FILL_AREA_STYLE('',(#1952));
#1894=FILL_AREA_STYLE('',(#1953));
#1895=FILL_AREA_STYLE('',(#1954));
#1896=FILL_AREA_STYLE('',(#1955));
#1897=FILL_AREA_STYLE('',(#1956));
#1898=FILL_AREA_STYLE('',(#1957));
#1899=FILL_AREA_STYLE('',(#1958));
#1900=FILL_AREA_STYLE('',(#1959));
#1901=FILL_AREA_STYLE('',(#1960));
#1902=FILL_AREA_STYLE('',(#1961));
#1903=FILL_AREA_STYLE('',(#1962));
#1904=FILL_AREA_STYLE('',(#1963));
#1905=FILL_AREA_STYLE('',(#1964));
#1906=FILL_AREA_STYLE('',(#1965));
#1907=FILL_AREA_STYLE('',(#1966));
#1908=FILL_AREA_STYLE('',(#1967));
#1909=FILL_AREA_STYLE('',(#1968));
#1910=FILL_AREA_STYLE('',(#1969));
#1911=FILL_AREA_STYLE('',(#1970));
#1912=FILL_AREA_STYLE('',(#1971));
#1913=FILL_AREA_STYLE('',(#1972));
#1914=FILL_AREA_STYLE('',(#1973));
#1915=FILL_AREA_STYLE('',(#1974));
#1916=FILL_AREA_STYLE('',(#1975));
#1917=FILL_AREA_STYLE('',(#1976));
#1918=FILL_AREA_STYLE('',(#1977));
#1919=FILL_AREA_STYLE('',(#1978));
#1920=FILL_AREA_STYLE('',(#1979));
#1921=FILL_AREA_STYLE('',(#1980));
#1922=FILL_AREA_STYLE('',(#1981));
#1923=FILL_AREA_STYLE('',(#1982));
#1924=FILL_AREA_STYLE_COLOUR('',#1983);
#1925=FILL_AREA_STYLE_COLOUR('',#1984);
#1926=FILL_AREA_STYLE_COLOUR('',#1985);
#1927=FILL_AREA_STYLE_COLOUR('',#1984);
#1928=FILL_AREA_STYLE_COLOUR('',#1984);
#1929=FILL_AREA_STYLE_COLOUR('',#1984);
#1930=FILL_AREA_STYLE_COLOUR('',#1984);
#1931=FILL_AREA_STYLE_COLOUR('',#1984);
#1932=FILL_AREA_STYLE_COLOUR('',#1984);
#1933=FILL_AREA_STYLE_COLOUR('',#1984);
#1934=FILL_AREA_STYLE_COLOUR('',#1984);
#1935=FILL_AREA_STYLE_COLOUR('',#1984);
#1936=FILL_AREA_STYLE_COLOUR('',#1985);
#1937=FILL_AREA_STYLE_COLOUR('',#1984);
#1938=FILL_AREA_STYLE_COLOUR('',#1984);
#1939=FILL_AREA_STYLE_COLOUR('',#1984);
#1940=FILL_AREA_STYLE_COLOUR('',#1984);
#1941=FILL_AREA_STYLE_COLOUR('',#1984);
#1942=FILL_AREA_STYLE_COLOUR('',#1984);
#1943=FILL_AREA_STYLE_COLOUR('',#1984);
#1944=FILL_AREA_STYLE_COLOUR('',#1984);
#1945=FILL_AREA_STYLE_COLOUR('',#1984);
#1946=FILL_AREA_STYLE_COLOUR('',#1984);
#1947=FILL_AREA_STYLE_COLOUR('',#1984);
#1948=FILL_AREA_STYLE_COLOUR('',#1984);
#1949=FILL_AREA_STYLE_COLOUR('',#1984);
#1950=FILL_AREA_STYLE_COLOUR('',#1984);
#1951=FILL_AREA_STYLE_COLOUR('',#1985);
#1952=FILL_AREA_STYLE_COLOUR('',#1984);
#1953=FILL_AREA_STYLE_COLOUR('',#1984);
#1954=FILL_AREA_STYLE_COLOUR('',#1984);
#1955=FILL_AREA_STYLE_COLOUR('',#1984);
#1956=FILL_AREA_STYLE_COLOUR('',#1984);
#1957=FILL_AREA_STYLE_COLOUR('',#1984);
#1958=FILL_AREA_STYLE_COLOUR('',#1984);
#1959=FILL_AREA_STYLE_COLOUR('',#1984);
#1960=FILL_AREA_STYLE_COLOUR('',#1984);
#1961=FILL_AREA_STYLE_COLOUR('',#1984);
#1962=FILL_AREA_STYLE_COLOUR('',#1985);
#1963=FILL_AREA_STYLE_COLOUR('',#1984);
#1964=FILL_AREA_STYLE_COLOUR('',#1984);
#1965=FILL_AREA_STYLE_COLOUR('',#1984);
#1966=FILL_AREA_STYLE_COLOUR('',#1984);
#1967=FILL_AREA_STYLE_COLOUR('',#1984);
#1968=FILL_AREA_STYLE_COLOUR('',#1984);
#1969=FILL_AREA_STYLE_COLOUR('',#1984);
#1970=FILL_AREA_STYLE_COLOUR('',#1984);
#1971=FILL_AREA_STYLE_COLOUR('',#1984);
#1972=FILL_AREA_STYLE_COLOUR('',#1985);
#1973=FILL_AREA_STYLE_COLOUR('',#1984);
#1974=FILL_AREA_STYLE_COLOUR('',#1984);
#1975=FILL_AREA_STYLE_COLOUR('',#1984);
#1976=FILL_AREA_STYLE_COLOUR('',#1984);
#1977=FILL_AREA_STYLE_COLOUR('',#1984);
#1978=FILL_AREA_STYLE_COLOUR('',#1984);
#1979=FILL_AREA_STYLE_COLOUR('',#1984);
#1980=FILL_AREA_STYLE_COLOUR('',#1984);
#1981=FILL_AREA_STYLE_COLOUR('',#1984);
#1982=FILL_AREA_STYLE_COLOUR('',#1984);
#1983=COLOUR_RGB('',0.200000002980232,0.200000002980232,0.200000002980232);
#1984=COLOUR_RGB('',0.819999992847443,0.819999992847443,0.819999992847443);
#1985=COLOUR_RGB('',0.550000011920929,0.311243504285812,0.21841649711132);
#1986=MANIFOLD_SOLID_BREP('',#1566);
#1987=MANIFOLD_SOLID_BREP('',#1567);
#1988=MANIFOLD_SOLID_BREP('',#1568);
#1989=MANIFOLD_SOLID_BREP('',#1569);
#1990=SHAPE_DEFINITION_REPRESENTATION(#3099,#1993);
#1991=SHAPE_DEFINITION_REPRESENTATION(#3098,#1994);
#1992=SHAPE_DEFINITION_REPRESENTATION(#3101,#1995);
#1993=SHAPE_REPRESENTATION('CPD',(#1996),#3090);
#1994=SHAPE_REPRESENTATION('SOT-25',(#1996,#2059,#2158),#3089);
#1995=SHAPE_REPRESENTATION('LDF',(#1996),#3091);
#1996=AXIS2_PLACEMENT_3D('',#2637,#2159,#2160);
#1997=AXIS2_PLACEMENT_3D('',#2638,#2161,#2162);
#1998=AXIS2_PLACEMENT_3D('',#2647,#2167,#2168);
#1999=AXIS2_PLACEMENT_3D('',#2656,#2173,#2174);
#2000=AXIS2_PLACEMENT_3D('',#2665,#2179,#2180);
#2001=AXIS2_PLACEMENT_3D('',#2674,#2185,#2186);
#2002=AXIS2_PLACEMENT_3D('',#2683,#2191,#2192);
#2003=AXIS2_PLACEMENT_3D('',#2692,#2197,#2198);
#2004=AXIS2_PLACEMENT_3D('',#2698,#2202,#2203);
#2005=AXIS2_PLACEMENT_3D('',#2704,#2207,#2208);
#2006=AXIS2_PLACEMENT_3D('',#2710,#2212,#2213);
#2007=AXIS2_PLACEMENT_3D('',#2716,#2217,#2218);
#2008=AXIS2_PLACEMENT_3D('',#2717,#2219,#2220);
#2009=AXIS2_PLACEMENT_3D('',#2718,#2221,#2222);
#2010=AXIS2_PLACEMENT_3D('',#2719,#2223,#2224);
#2011=AXIS2_PLACEMENT_3D('',#2720,#2225,#2226);
#2012=AXIS2_PLACEMENT_3D('',#2721,#2227,#2228);
#2013=AXIS2_PLACEMENT_3D('',#2722,#2229,#2230);
#2014=AXIS2_PLACEMENT_3D('',#2723,#2231,#2232);
#2015=AXIS2_PLACEMENT_3D('',#2724,#2233,#2234);
#2016=AXIS2_PLACEMENT_3D('',#2725,#2235,#2236);
#2017=AXIS2_PLACEMENT_3D('',#2726,#2237,#2238);
#2018=AXIS2_PLACEMENT_3D('',#2727,#2239,#2240);
#2019=AXIS2_PLACEMENT_3D('',#2728,#2241,#2242);
#2020=AXIS2_PLACEMENT_3D('',#2729,#2243,#2244);
#2021=AXIS2_PLACEMENT_3D('',#2730,#2245,#2246);
#2022=AXIS2_PLACEMENT_3D('',#2731,#2247,#2248);
#2023=AXIS2_PLACEMENT_3D('',#2732,#2249,#2250);
#2024=AXIS2_PLACEMENT_3D('',#2733,#2251,#2252);
#2025=AXIS2_PLACEMENT_3D('',#2734,#2253,#2254);
#2026=AXIS2_PLACEMENT_3D('',#2735,#2255,#2256);
#2027=AXIS2_PLACEMENT_3D('',#2736,#2257,#2258);
#2028=AXIS2_PLACEMENT_3D('',#2737,#2259,#2260);
#2029=AXIS2_PLACEMENT_3D('',#2738,#2261,#2262);
#2030=AXIS2_PLACEMENT_3D('',#2739,#2263,#2264);
#2031=AXIS2_PLACEMENT_3D('',#2740,#2265,#2266);
#2032=AXIS2_PLACEMENT_3D('',#2741,#2267,#2268);
#2033=AXIS2_PLACEMENT_3D('',#2742,#2269,#2270);
#2034=AXIS2_PLACEMENT_3D('',#2743,#2271,#2272);
#2035=AXIS2_PLACEMENT_3D('',#2744,#2273,#2274);
#2036=AXIS2_PLACEMENT_3D('',#2745,#2275,#2276);
#2037=AXIS2_PLACEMENT_3D('',#2746,#2277,#2278);
#2038=AXIS2_PLACEMENT_3D('',#2747,#2279,#2280);
#2039=AXIS2_PLACEMENT_3D('',#2748,#2281,#2282);
#2040=AXIS2_PLACEMENT_3D('',#2749,#2283,#2284);
#2041=AXIS2_PLACEMENT_3D('',#2750,#2285,#2286);
#2042=AXIS2_PLACEMENT_3D('',#2751,#2287,#2288);
#2043=AXIS2_PLACEMENT_3D('',#2752,#2289,#2290);
#2044=AXIS2_PLACEMENT_3D('',#2753,#2291,#2292);
#2045=AXIS2_PLACEMENT_3D('',#2754,#2293,#2294);
#2046=AXIS2_PLACEMENT_3D('',#2755,#2295,#2296);
#2047=AXIS2_PLACEMENT_3D('',#2756,#2297,#2298);
#2048=AXIS2_PLACEMENT_3D('',#2757,#2299,#2300);
#2049=AXIS2_PLACEMENT_3D('',#2758,#2301,#2302);
#2050=AXIS2_PLACEMENT_3D('',#2759,#2303,#2304);
#2051=AXIS2_PLACEMENT_3D('',#2760,#2305,#2306);
#2052=AXIS2_PLACEMENT_3D('',#2761,#2307,#2308);
#2053=AXIS2_PLACEMENT_3D('',#2762,#2309,#2310);
#2054=AXIS2_PLACEMENT_3D('',#2763,#2311,#2312);
#2055=AXIS2_PLACEMENT_3D('',#2764,#2313,#2314);
#2056=AXIS2_PLACEMENT_3D('',#2765,#2315,#2316);
#2057=AXIS2_PLACEMENT_3D('',#2766,#2317,#2318);
#2058=AXIS2_PLACEMENT_3D('',#2767,#2319,#2320);
#2059=AXIS2_PLACEMENT_3D('',#2768,#2321,#2322);
#2060=AXIS2_PLACEMENT_3D('',#2769,#2323,#2324);
#2061=AXIS2_PLACEMENT_3D('',#2778,#2329,#2330);
#2062=AXIS2_PLACEMENT_3D('',#2784,#2334,#2335);
#2063=AXIS2_PLACEMENT_3D('',#2790,#2339,#2340);
#2064=AXIS2_PLACEMENT_3D('',#2791,#2341,#2342);
#2065=AXIS2_PLACEMENT_3D('',#2795,#2344,#2345);
#2066=AXIS2_PLACEMENT_3D('',#2796,#2346,#2347);
#2067=AXIS2_PLACEMENT_3D('',#2802,#2351,#2352);
#2068=AXIS2_PLACEMENT_3D('',#2803,#2353,#2354);
#2069=AXIS2_PLACEMENT_3D('',#2807,#2356,#2357);
#2070=AXIS2_PLACEMENT_3D('',#2808,#2358,#2359);
#2071=AXIS2_PLACEMENT_3D('',#2814,#2363,#2364);
#2072=AXIS2_PLACEMENT_3D('',#2815,#2365,#2366);
#2073=AXIS2_PLACEMENT_3D('',#2819,#2368,#2369);
#2074=AXIS2_PLACEMENT_3D('',#2820,#2370,#2371);
#2075=AXIS2_PLACEMENT_3D('',#2826,#2375,#2376);
#2076=AXIS2_PLACEMENT_3D('',#2827,#2377,#2378);
#2077=AXIS2_PLACEMENT_3D('',#2831,#2380,#2381);
#2078=AXIS2_PLACEMENT_3D('',#2832,#2382,#2383);
#2079=AXIS2_PLACEMENT_3D('',#2838,#2387,#2388);
#2080=AXIS2_PLACEMENT_3D('',#2844,#2392,#2393);
#2081=AXIS2_PLACEMENT_3D('',#2850,#2397,#2398);
#2082=AXIS2_PLACEMENT_3D('',#2854,#2400,#2401);
#2083=AXIS2_PLACEMENT_3D('',#2855,#2402,#2403);
#2084=AXIS2_PLACEMENT_3D('',#2856,#2404,#2405);
#2085=AXIS2_PLACEMENT_3D('',#2862,#2409,#2410);
#2086=AXIS2_PLACEMENT_3D('',#2863,#2411,#2412);
#2087=AXIS2_PLACEMENT_3D('',#2867,#2414,#2415);
#2088=AXIS2_PLACEMENT_3D('',#2868,#2416,#2417);
#2089=AXIS2_PLACEMENT_3D('',#2874,#2421,#2422);
#2090=AXIS2_PLACEMENT_3D('',#2875,#2423,#2424);
#2091=AXIS2_PLACEMENT_3D('',#2879,#2426,#2427);
#2092=AXIS2_PLACEMENT_3D('',#2880,#2428,#2429);
#2093=AXIS2_PLACEMENT_3D('',#2886,#2433,#2434);
#2094=AXIS2_PLACEMENT_3D('',#2887,#2435,#2436);
#2095=AXIS2_PLACEMENT_3D('',#2888,#2437,#2438);
#2096=AXIS2_PLACEMENT_3D('',#2889,#2439,#2440);
#2097=AXIS2_PLACEMENT_3D('',#2890,#2441,#2442);
#2098=AXIS2_PLACEMENT_3D('',#2891,#2443,#2444);
#2099=AXIS2_PLACEMENT_3D('',#2900,#2449,#2450);
#2100=AXIS2_PLACEMENT_3D('',#2909,#2455,#2456);
#2101=AXIS2_PLACEMENT_3D('',#2911,#2458,#2459);
#2102=AXIS2_PLACEMENT_3D('',#2915,#2461,#2462);
#2103=AXIS2_PLACEMENT_3D('',#2923,#2466,#2467);
#2104=AXIS2_PLACEMENT_3D('',#2927,#2469,#2470);
#2105=AXIS2_PLACEMENT_3D('',#2928,#2471,#2472);
#2106=AXIS2_PLACEMENT_3D('',#2934,#2476,#2477);
#2107=AXIS2_PLACEMENT_3D('',#2938,#2480,#2481);
#2108=AXIS2_PLACEMENT_3D('',#2942,#2484,#2485);
#2109=AXIS2_PLACEMENT_3D('',#2945,#2487,#2488);
#2110=AXIS2_PLACEMENT_3D('',#2946,#2489,#2490);
#2111=AXIS2_PLACEMENT_3D('',#2950,#2493,#2494);
#2112=AXIS2_PLACEMENT_3D('',#2951,#2495,#2496);
#2113=AXIS2_PLACEMENT_3D('',#2952,#2497,#2498);
#2114=AXIS2_PLACEMENT_3D('',#2955,#2500,#2501);
#2115=AXIS2_PLACEMENT_3D('',#2956,#2502,#2503);
#2116=AXIS2_PLACEMENT_3D('',#2960,#2506,#2507);
#2117=AXIS2_PLACEMENT_3D('',#2961,#2508,#2509);
#2118=AXIS2_PLACEMENT_3D('',#2962,#2510,#2511);
#2119=AXIS2_PLACEMENT_3D('',#2964,#2513,#2514);
#2120=AXIS2_PLACEMENT_3D('',#2965,#2515,#2516);
#2121=AXIS2_PLACEMENT_3D('',#2974,#2521,#2522);
#2122=AXIS2_PLACEMENT_3D('',#2980,#2526,#2527);
#2123=AXIS2_PLACEMENT_3D('',#2986,#2531,#2532);
#2124=AXIS2_PLACEMENT_3D('',#2987,#2533,#2534);
#2125=AXIS2_PLACEMENT_3D('',#2991,#2536,#2537);
#2126=AXIS2_PLACEMENT_3D('',#2992,#2538,#2539);
#2127=AXIS2_PLACEMENT_3D('',#2998,#2543,#2544);
#2128=AXIS2_PLACEMENT_3D('',#2999,#2545,#2546);
#2129=AXIS2_PLACEMENT_3D('',#3003,#2548,#2549);
#2130=AXIS2_PLACEMENT_3D('',#3004,#2550,#2551);
#2131=AXIS2_PLACEMENT_3D('',#3010,#2555,#2556);
#2132=AXIS2_PLACEMENT_3D('',#3011,#2557,#2558);
#2133=AXIS2_PLACEMENT_3D('',#3015,#2560,#2561);
#2134=AXIS2_PLACEMENT_3D('',#3016,#2562,#2563);
#2135=AXIS2_PLACEMENT_3D('',#3022,#2567,#2568);
#2136=AXIS2_PLACEMENT_3D('',#3023,#2569,#2570);
#2137=AXIS2_PLACEMENT_3D('',#3027,#2572,#2573);
#2138=AXIS2_PLACEMENT_3D('',#3028,#2574,#2575);
#2139=AXIS2_PLACEMENT_3D('',#3034,#2579,#2580);
#2140=AXIS2_PLACEMENT_3D('',#3040,#2584,#2585);
#2141=AXIS2_PLACEMENT_3D('',#3046,#2589,#2590);
#2142=AXIS2_PLACEMENT_3D('',#3050,#2592,#2593);
#2143=AXIS2_PLACEMENT_3D('',#3051,#2594,#2595);
#2144=AXIS2_PLACEMENT_3D('',#3052,#2596,#2597);
#2145=AXIS2_PLACEMENT_3D('',#3058,#2601,#2602);
#2146=AXIS2_PLACEMENT_3D('',#3059,#2603,#2604);
#2147=AXIS2_PLACEMENT_3D('',#3063,#2606,#2607);
#2148=AXIS2_PLACEMENT_3D('',#3064,#2608,#2609);
#2149=AXIS2_PLACEMENT_3D('',#3070,#2613,#2614);
#2150=AXIS2_PLACEMENT_3D('',#3071,#2615,#2616);
#2151=AXIS2_PLACEMENT_3D('',#3075,#2618,#2619);
#2152=AXIS2_PLACEMENT_3D('',#3076,#2620,#2621);
#2153=AXIS2_PLACEMENT_3D('',#3082,#2625,#2626);
#2154=AXIS2_PLACEMENT_3D('',#3083,#2627,#2628);
#2155=AXIS2_PLACEMENT_3D('',#3084,#2629,#2630);
#2156=AXIS2_PLACEMENT_3D('',#3085,#2631,#2632);
#2157=AXIS2_PLACEMENT_3D('',#3086,#2633,#2634);
#2158=AXIS2_PLACEMENT_3D('',#3087,#2635,#2636);
#2159=DIRECTION('',(0.,0.,1.));
#2160=DIRECTION('',(1.,0.,0.));
#2161=DIRECTION('',(0.,0.,1.));
#2162=DIRECTION('',(1.,0.,0.));
#2163=DIRECTION('',(0.,1.,0.));
#2164=DIRECTION('',(1.,0.,0.));
#2165=DIRECTION('',(0.,-1.,0.));
#2166=DIRECTION('',(-1.,0.,0.));
#2167=DIRECTION('',(0.,0.992546151641322,0.121869343405147));
#2168=DIRECTION('',(1.,0.,0.));
#2169=DIRECTION('',(1.,0.,0.));
#2170=DIRECTION('',(-0.120974291151355,-0.120974291151354,0.985256536015293));
#2171=DIRECTION('',(-1.,0.,0.));
#2172=DIRECTION('',(-0.120974291151355,0.120974291151354,-0.985256536015293));
#2173=DIRECTION('',(-0.992546151641322,0.,0.121869343405147));
#2174=DIRECTION('',(0.,1.,0.));
#2175=DIRECTION('',(0.,-1.,0.));
#2176=DIRECTION('',(-0.120974291151355,-0.120974291151354,-0.985256536015293));
#2177=DIRECTION('',(0.,1.,0.));
#2178=DIRECTION('',(-0.120974291151355,0.120974291151354,-0.985256536015293));
#2179=DIRECTION('',(0.,-0.992546151641322,0.121869343405147));
#2180=DIRECTION('',(-1.,0.,0.));
#2181=DIRECTION('',(1.,0.,0.));
#2182=DIRECTION('',(0.120974291151355,-0.120974291151354,-0.985256536015293));
#2183=DIRECTION('',(-1.,0.,0.));
#2184=DIRECTION('',(-0.120974291151355,-0.120974291151354,-0.985256536015293));
#2185=DIRECTION('',(0.992546151641322,0.,0.121869343405147));
#2186=DIRECTION('',(0.,-1.,0.));
#2187=DIRECTION('',(0.,1.,0.));
#2188=DIRECTION('',(-0.120974291151355,-0.120974291151354,0.985256536015293));
#2189=DIRECTION('',(0.,-1.,0.));
#2190=DIRECTION('',(0.120974291151355,-0.120974291151354,-0.985256536015293));
#2191=DIRECTION('',(0.,0.,1.));
#2192=DIRECTION('',(1.,0.,0.));
#2193=DIRECTION('',(-1.,0.,0.));
#2194=DIRECTION('',(0.,-1.,0.));
#2195=DIRECTION('',(1.,0.,0.));
#2196=DIRECTION('',(0.,1.,0.));
#2197=DIRECTION('',(0.,-0.992546151641322,0.121869343405148));
#2198=DIRECTION('',(-1.,0.,0.));
#2199=DIRECTION('',(1.,0.,0.));
#2200=DIRECTION('',(-0.120974291151355,-0.120974291151355,-0.985256536015293));
#2201=DIRECTION('',(-0.120974291151355,0.120974291151355,0.985256536015293));
#2202=DIRECTION('',(0.992546151641322,0.,0.121869343405148));
#2203=DIRECTION('',(0.,-1.,0.));
#2204=DIRECTION('',(-0.120974291151355,-0.120974291151355,0.985256536015293));
#2205=DIRECTION('',(0.,1.,0.));
#2206=DIRECTION('',(-0.120974291151355,0.120974291151355,0.985256536015293));
#2207=DIRECTION('',(0.,0.992546151641322,0.121869343405148));
#2208=DIRECTION('',(1.,0.,0.));
#2209=DIRECTION('',(0.120974291151355,-0.120974291151355,0.985256536015293));
#2210=DIRECTION('',(-1.,0.,0.));
#2211=DIRECTION('',(-0.120974291151355,-0.120974291151355,0.985256536015293));
#2212=DIRECTION('',(-0.992546151641322,0.,0.121869343405148));
#2213=DIRECTION('',(0.,1.,0.));
#2214=DIRECTION('',(-0.120974291151355,-0.120974291151355,-0.985256536015293));
#2215=DIRECTION('',(0.,-1.,0.));
#2216=DIRECTION('',(0.120974291151355,-0.120974291151355,0.985256536015293));
#2217=DIRECTION('',(0.,1.,0.));
#2218=DIRECTION('',(0.,0.,1.));
#2219=DIRECTION('',(0.,1.,0.));
#2220=DIRECTION('',(0.,0.,1.));
#2221=DIRECTION('',(0.,1.,0.));
#2222=DIRECTION('',(0.,0.,1.));
#2223=DIRECTION('',(-1.,0.,0.));
#2224=DIRECTION('',(0.,0.,1.));
#2225=DIRECTION('',(-1.,0.,0.));
#2226=DIRECTION('',(0.,0.,1.));
#2227=DIRECTION('',(-1.,0.,0.));
#2228=DIRECTION('',(0.,0.,1.));
#2229=DIRECTION('',(1.,0.,0.));
#2230=DIRECTION('',(0.,0.,-1.));
#2231=DIRECTION('',(1.,0.,0.));
#2232=DIRECTION('',(0.,0.,-1.));
#2233=DIRECTION('',(1.,0.,0.));
#2234=DIRECTION('',(0.,0.,-1.));
#2235=DIRECTION('',(0.,-1.,0.));
#2236=DIRECTION('',(0.,0.,-1.));
#2237=DIRECTION('',(0.,-1.,0.));
#2238=DIRECTION('',(0.,0.,-1.));
#2239=DIRECTION('',(0.,-1.,0.));
#2240=DIRECTION('',(0.,0.,-1.));
#2241=DIRECTION('',(1.,0.,0.));
#2242=DIRECTION('',(0.,0.,-1.));
#2243=DIRECTION('',(1.,0.,0.));
#2244=DIRECTION('',(0.,0.,-1.));
#2245=DIRECTION('',(1.,0.,0.));
#2246=DIRECTION('',(0.,0.,-1.));
#2247=DIRECTION('',(0.,-1.,0.));
#2248=DIRECTION('',(0.,0.,-1.));
#2249=DIRECTION('',(0.,-1.,0.));
#2250=DIRECTION('',(0.,0.,-1.));
#2251=DIRECTION('',(0.,-1.,0.));
#2252=DIRECTION('',(0.,0.,-1.));
#2253=DIRECTION('',(0.,1.,0.));
#2254=DIRECTION('',(0.,0.,1.));
#2255=DIRECTION('',(0.,1.,0.));
#2256=DIRECTION('',(0.,0.,1.));
#2257=DIRECTION('',(0.,1.,0.));
#2258=DIRECTION('',(0.,0.,1.));
#2259=DIRECTION('',(-1.,0.,0.));
#2260=DIRECTION('',(0.,0.,1.));
#2261=DIRECTION('',(-1.,0.,0.));
#2262=DIRECTION('',(0.,0.,1.));
#2263=DIRECTION('',(-1.,0.,0.));
#2264=DIRECTION('',(0.,0.,1.));
#2265=DIRECTION('',(0.,0.,1.));
#2266=DIRECTION('',(1.,0.,0.));
#2267=DIRECTION('',(0.120974291151354,-0.120974291151354,-0.985256536015293));
#2268=DIRECTION('',(-0.992546151641322,0.,-0.121869343405147));
#2269=DIRECTION('',(-0.120974291151355,0.120974291151355,0.985256536015293));
#2270=DIRECTION('',(0.992546151641322,0.,0.121869343405148));
#2271=DIRECTION('',(-7.04272811218328E-18,8.45127373461994E-17,1.));
#2272=DIRECTION('',(-0.707106781186548,0.707106781186547,0.));
#2273=DIRECTION('',(0.120974291151355,-0.120974291151354,0.985256536015293));
#2274=DIRECTION('',(0.992546151641322,0.,-0.121869343405148));
#2275=DIRECTION('',(-0.120974291151355,0.120974291151354,-0.985256536015293));
#2276=DIRECTION('',(-0.992546151641322,0.,0.121869343405147));
#2277=DIRECTION('',(0.,0.,1.));
#2278=DIRECTION('',(1.,0.,0.));
#2279=DIRECTION('',(0.,0.,1.));
#2280=DIRECTION('',(1.,0.,0.));
#2281=DIRECTION('',(0.120974291151354,0.120974291151354,0.985256536015293));
#2282=DIRECTION('',(0.992546151641322,0.,-0.121869343405147));
#2283=DIRECTION('',(0.120974291151355,0.120974291151354,0.985256536015293));
#2284=DIRECTION('',(0.992546151641322,0.,-0.121869343405148));
#2285=DIRECTION('',(-7.04272811218328E-18,-8.45127373461994E-17,1.));
#2286=DIRECTION('',(0.707106781186548,0.707106781186547,0.));
#2287=DIRECTION('',(-0.120974291151355,-0.120974291151355,0.985256536015293));
#2288=DIRECTION('',(0.992546151641322,0.,0.121869343405148));
#2289=DIRECTION('',(-0.120974291151354,-0.120974291151353,0.985256536015293));
#2290=DIRECTION('',(0.992546151641322,0.,0.121869343405147));
#2291=DIRECTION('',(0.,0.,1.));
#2292=DIRECTION('',(1.,0.,0.));
#2293=DIRECTION('',(0.,0.,1.));
#2294=DIRECTION('',(1.,0.,0.));
#2295=DIRECTION('',(-0.120974291151354,0.120974291151354,0.985256536015293));
#2296=DIRECTION('',(0.,-0.992546151641322,0.121869343405147));
#2297=DIRECTION('',(-0.120974291151355,0.120974291151354,0.985256536015293));
#2298=DIRECTION('',(0.992546151641322,0.,0.121869343405148));
#2299=DIRECTION('',(7.04272811218328E-18,-8.45127373461994E-17,1.));
#2300=DIRECTION('',(-0.707106781186548,0.707106781186547,0.));
#2301=DIRECTION('',(0.120974291151355,-0.120974291151355,0.985256536015293));
#2302=DIRECTION('',(0.992546151641322,0.,-0.121869343405148));
#2303=DIRECTION('',(0.120974291151354,-0.120974291151354,0.985256536015293));
#2304=DIRECTION('',(0.992546151641322,0.,-0.121869343405147));
#2305=DIRECTION('',(0.,0.,1.));
#2306=DIRECTION('',(1.,0.,0.));
#2307=DIRECTION('',(0.,0.,1.));
#2308=DIRECTION('',(1.,0.,0.));
#2309=DIRECTION('',(-0.120974291151355,-0.120974291151354,0.985256536015293));
#2310=DIRECTION('',(0.992546151641322,0.,0.121869343405148));
#2311=DIRECTION('',(0.120974291151355,0.120974291151354,-0.985256536015293));
#2312=DIRECTION('',(-0.992546151641322,0.,-0.121869343405148));
#2313=DIRECTION('',(-7.04272811218328E-18,-8.45127373461994E-17,-1.));
#2314=DIRECTION('',(0.707106781186548,0.707106781186547,0.));
#2315=DIRECTION('',(-0.120974291151355,-0.120974291151355,-0.985256536015293));
#2316=DIRECTION('',(-0.992546151641322,0.,0.121869343405148));
#2317=DIRECTION('',(0.120974291151354,0.120974291151354,0.985256536015293));
#2318=DIRECTION('',(0.992546151641322,0.,-0.121869343405147));
#2319=DIRECTION('',(0.,0.,1.));
#2320=DIRECTION('',(1.,0.,0.));
#2321=DIRECTION('',(0.,0.,1.));
#2322=DIRECTION('',(1.,0.,0.));
#2323=DIRECTION('',(-0.0697564737444582,0.,-0.997564050259801));
#2324=DIRECTION('',(-0.997564050259801,0.,0.0697564737444582));
#2325=DIRECTION('',(0.997564050259801,0.,-0.0697564737444582));
#2326=DIRECTION('',(0.,-1.,0.));
#2327=DIRECTION('',(0.997564050259801,0.,-0.0697564737444582));
#2328=DIRECTION('',(0.,-1.,0.));
#2329=DIRECTION('',(-0.997564050259859,0.,0.0697564737436262));
#2330=DIRECTION('',(0.0697564737436262,0.,0.997564050259859));
#2331=DIRECTION('',(-0.0697564737436262,0.,-0.997564050259859));
#2332=DIRECTION('',(0.,-1.,0.));
#2333=DIRECTION('',(-0.0697564737436262,0.,-0.997564050259859));
#2334=DIRECTION('',(0.0697564737448007,0.,0.997564050259777));
#2335=DIRECTION('',(0.997564050259777,0.,-0.0697564737448007));
#2336=DIRECTION('',(-0.997564050259777,0.,0.0697564737448007));
#2337=DIRECTION('',(0.,-1.,0.));
#2338=DIRECTION('',(-0.997564050259777,0.,0.0697564737448007));
#2339=DIRECTION('',(0.,-1.,0.));
#2340=DIRECTION('',(0.,0.,-1.));
#2341=DIRECTION('',(0.,1.,0.));
#2342=DIRECTION('',(1.,0.,0.));
#2343=DIRECTION('',(0.,-1.,0.));
#2344=DIRECTION('',(0.,1.,0.));
#2345=DIRECTION('',(1.,0.,0.));
#2346=DIRECTION('',(0.996194698091693,0.,0.0871557427482611));
#2347=DIRECTION('',(0.0871557427482611,0.,-0.996194698091693));
#2348=DIRECTION('',(-0.0871557427482611,0.,0.996194698091693));
#2349=DIRECTION('',(0.,-1.,0.));
#2350=DIRECTION('',(-0.0871557427482611,0.,0.996194698091693));
#2351=DIRECTION('',(0.,-1.,0.));
#2352=DIRECTION('',(0.,0.,-1.));
#2353=DIRECTION('',(0.,-1.,0.));
#2354=DIRECTION('',(1.,0.,0.));
#2355=DIRECTION('',(0.,-1.,0.));
#2356=DIRECTION('',(0.,-1.,0.));
#2357=DIRECTION('',(1.,0.,0.));
#2358=DIRECTION('',(-1.37043154602297E-12,0.,1.));
#2359=DIRECTION('',(1.,0.,1.37043154602297E-12));
#2360=DIRECTION('',(-1.,0.,-1.37043154602297E-12));
#2361=DIRECTION('',(0.,-1.,0.));
#2362=DIRECTION('',(-1.,0.,-1.37043154602297E-12));
#2363=DIRECTION('',(0.,-1.,0.));
#2364=DIRECTION('',(0.,0.,-1.));
#2365=DIRECTION('',(0.,-1.,0.));
#2366=DIRECTION('',(1.,0.,0.));
#2367=DIRECTION('',(0.,-1.,0.));
#2368=DIRECTION('',(0.,-1.,0.));
#2369=DIRECTION('',(1.,0.,0.));
#2370=DIRECTION('',(-0.996194698091734,0.,0.0871557427477874));
#2371=DIRECTION('',(0.0871557427477874,0.,0.996194698091734));
#2372=DIRECTION('',(-0.0871557427477874,0.,-0.996194698091734));
#2373=DIRECTION('',(0.,-1.,0.));
#2374=DIRECTION('',(-0.0871557427477874,0.,-0.996194698091734));
#2375=DIRECTION('',(0.,-1.,0.));
#2376=DIRECTION('',(0.,0.,-1.));
#2377=DIRECTION('',(0.,1.,0.));
#2378=DIRECTION('',(1.,0.,0.));
#2379=DIRECTION('',(0.,-1.,0.));
#2380=DIRECTION('',(0.,1.,0.));
#2381=DIRECTION('',(1.,0.,0.));
#2382=DIRECTION('',(-0.069756473744275,0.,0.997564050259814));
#2383=DIRECTION('',(0.997564050259814,0.,0.0697564737442751));
#2384=DIRECTION('',(-0.997564050259814,0.,-0.0697564737442751));
#2385=DIRECTION('',(0.,-1.,0.));
#2386=DIRECTION('',(-0.997564050259814,0.,-0.0697564737442751));
#2387=DIRECTION('',(0.997564050259859,0.,0.0697564737436268));
#2388=DIRECTION('',(0.0697564737436268,0.,-0.997564050259859));
#2389=DIRECTION('',(-0.0697564737436268,0.,0.997564050259859));
#2390=DIRECTION('',(0.,-1.,0.));
#2391=DIRECTION('',(-0.0697564737436268,0.,0.997564050259859));
#2392=DIRECTION('',(0.0697564737439995,0.,-0.997564050259833));
#2393=DIRECTION('',(-0.997564050259833,0.,-0.0697564737439995));
#2394=DIRECTION('',(0.997564050259833,0.,0.0697564737439995));
#2395=DIRECTION('',(0.,-1.,0.));
#2396=DIRECTION('',(0.997564050259833,0.,0.0697564737439995));
#2397=DIRECTION('',(0.,-1.,0.));
#2398=DIRECTION('',(0.,0.,-1.));
#2399=DIRECTION('',(0.,-1.,0.));
#2400=DIRECTION('',(0.,-1.,0.));
#2401=DIRECTION('',(1.,0.,0.));
#2402=DIRECTION('',(0.,-1.,0.));
#2403=DIRECTION('',(1.,0.,0.));
#2404=DIRECTION('',(0.996194698091756,0.,-0.0871557427475426));
#2405=DIRECTION('',(-0.0871557427475426,0.,-0.996194698091756));
#2406=DIRECTION('',(0.0871557427475427,0.,0.996194698091756));
#2407=DIRECTION('',(0.,-1.,0.));
#2408=DIRECTION('',(0.0871557427475427,0.,0.996194698091756));
#2409=DIRECTION('',(0.,-1.,0.));
#2410=DIRECTION('',(0.,0.,-1.));
#2411=DIRECTION('',(0.,1.,0.));
#2412=DIRECTION('',(1.,0.,0.));
#2413=DIRECTION('',(0.,-1.,0.));
#2414=DIRECTION('',(0.,1.,0.));
#2415=DIRECTION('',(1.,0.,0.));
#2416=DIRECTION('',(1.31838984174398E-12,0.,-1.));
#2417=DIRECTION('',(-1.,0.,-1.31838984174398E-12));
#2418=DIRECTION('',(0.,-1.,0.));
#2419=DIRECTION('',(1.,0.,1.31838984174398E-12));
#2420=DIRECTION('',(1.,0.,1.31838984174398E-12));
#2421=DIRECTION('',(0.,-1.,0.));
#2422=DIRECTION('',(0.,0.,-1.));
#2423=DIRECTION('',(0.,1.,0.));
#2424=DIRECTION('',(1.,0.,0.));
#2425=DIRECTION('',(0.,-1.,0.));
#2426=DIRECTION('',(0.,1.,0.));
#2427=DIRECTION('',(1.,0.,0.));
#2428=DIRECTION('',(-0.996194698091737,0.,-0.0871557427477556));
#2429=DIRECTION('',(-0.0871557427477556,0.,0.996194698091737));
#2430=DIRECTION('',(0.0871557427477556,0.,-0.996194698091737));
#2431=DIRECTION('',(0.,-1.,0.));
#2432=DIRECTION('',(0.0871557427477556,0.,-0.996194698091737));
#2433=DIRECTION('',(0.,-1.,0.));
#2434=DIRECTION('',(0.,0.,-1.));
#2435=DIRECTION('',(0.,-1.,0.));
#2436=DIRECTION('',(1.,0.,0.));
#2437=DIRECTION('',(0.,-1.,0.));
#2438=DIRECTION('',(1.,0.,0.));
#2439=DIRECTION('',(0.,1.,0.));
#2440=DIRECTION('',(0.,0.,1.));
#2441=DIRECTION('',(0.,1.,0.));
#2442=DIRECTION('',(0.,0.,1.));
#2443=DIRECTION('',(-1.37043154602297E-12,0.,1.));
#2444=DIRECTION('',(1.,0.,1.37043154602297E-12));
#2445=DIRECTION('',(0.,-1.,0.));
#2446=DIRECTION('',(-1.,0.,-1.37043154602297E-12));
#2447=DIRECTION('',(0.,-1.,0.));
#2448=DIRECTION('',(-1.,0.,-1.37043154602297E-12));
#2449=DIRECTION('',(1.31838984174398E-12,0.,-1.));
#2450=DIRECTION('',(-1.,0.,-1.31838984174398E-12));
#2451=DIRECTION('',(0.,-1.,0.));
#2452=DIRECTION('',(1.,0.,1.31838984174398E-12));
#2453=DIRECTION('',(0.,-1.,0.));
#2454=DIRECTION('',(1.,0.,1.31838984174398E-12));
#2455=DIRECTION('',(0.,1.,0.));
#2456=DIRECTION('',(0.,0.,1.));
#2457=DIRECTION('',(-1.31838984174398E-12,0.,1.));
#2458=DIRECTION('',(0.,-1.,0.));
#2459=DIRECTION('',(1.,0.,0.));
#2460=DIRECTION('',(-0.0871557427482611,0.,0.996194698091693));
#2461=DIRECTION('',(0.,1.,0.));
#2462=DIRECTION('',(1.,0.,0.));
#2463=DIRECTION('',(-0.997564050259777,0.,0.0697564737448007));
#2464=DIRECTION('',(-0.0697564737436262,0.,-0.997564050259859));
#2465=DIRECTION('',(0.997564050259801,0.,-0.0697564737444582));
#2466=DIRECTION('',(0.,-1.,0.));
#2467=DIRECTION('',(1.,0.,0.));
#2468=DIRECTION('',(0.0871557427477556,0.,-0.996194698091737));
#2469=DIRECTION('',(0.,1.,0.));
#2470=DIRECTION('',(1.,0.,0.));
#2471=DIRECTION('',(-0.0697564737444582,0.,-0.997564050259801));
#2472=DIRECTION('',(-0.997564050259801,0.,0.0697564737444582));
#2473=DIRECTION('',(0.,-1.,0.));
#2474=DIRECTION('',(0.997564050259801,0.,-0.0697564737444582));
#2475=DIRECTION('',(0.,-1.,0.));
#2476=DIRECTION('',(-0.997564050259859,0.,0.0697564737436262));
#2477=DIRECTION('',(0.0697564737436262,0.,0.997564050259859));
#2478=DIRECTION('',(0.,-1.,0.));
#2479=DIRECTION('',(-0.0697564737436262,0.,-0.997564050259859));
#2480=DIRECTION('',(0.0697564737448007,0.,0.997564050259777));
#2481=DIRECTION('',(0.997564050259777,0.,-0.0697564737448007));
#2482=DIRECTION('',(0.,-1.,0.));
#2483=DIRECTION('',(-0.997564050259777,0.,0.0697564737448007));
#2484=DIRECTION('',(0.,-1.,0.));
#2485=DIRECTION('',(0.,0.,-1.));
#2486=DIRECTION('',(0.,-1.,0.));
#2487=DIRECTION('',(0.,1.,0.));
#2488=DIRECTION('',(1.,0.,0.));
#2489=DIRECTION('',(0.996194698091693,0.,0.0871557427482611));
#2490=DIRECTION('',(0.0871557427482611,0.,-0.996194698091693));
#2491=DIRECTION('',(0.,-1.,0.));
#2492=DIRECTION('',(-0.0871557427482611,0.,0.996194698091693));
#2493=DIRECTION('',(0.,-1.,0.));
#2494=DIRECTION('',(0.,0.,-1.));
#2495=DIRECTION('',(0.,-1.,0.));
#2496=DIRECTION('',(1.,0.,0.));
#2497=DIRECTION('',(0.,-1.,0.));
#2498=DIRECTION('',(0.,0.,-1.));
#2499=DIRECTION('',(0.,-1.,0.));
#2500=DIRECTION('',(0.,1.,0.));
#2501=DIRECTION('',(1.,0.,0.));
#2502=DIRECTION('',(-0.996194698091737,0.,-0.0871557427477556));
#2503=DIRECTION('',(-0.0871557427477556,0.,0.996194698091737));
#2504=DIRECTION('',(0.,-1.,0.));
#2505=DIRECTION('',(0.0871557427477556,0.,-0.996194698091737));
#2506=DIRECTION('',(0.,-1.,0.));
#2507=DIRECTION('',(0.,0.,-1.));
#2508=DIRECTION('',(0.,-1.,0.));
#2509=DIRECTION('',(1.,0.,0.));
#2510=DIRECTION('',(0.,1.,0.));
#2511=DIRECTION('',(0.,0.,1.));
#2512=DIRECTION('',(-1.31838984174398E-12,0.,1.));
#2513=DIRECTION('',(-1.,0.,-1.31838984174398E-12));
#2514=DIRECTION('',(-1.31838984174398E-12,0.,1.));
#2515=DIRECTION('',(-0.0697564737444582,0.,-0.997564050259801));
#2516=DIRECTION('',(-0.997564050259801,0.,0.0697564737444582));
#2517=DIRECTION('',(0.997564050259801,0.,-0.0697564737444582));
#2518=DIRECTION('',(0.,-1.,0.));
#2519=DIRECTION('',(0.997564050259801,0.,-0.0697564737444582));
#2520=DIRECTION('',(0.,-1.,0.));
#2521=DIRECTION('',(-0.997564050259859,0.,0.0697564737436262));
#2522=DIRECTION('',(0.0697564737436262,0.,0.997564050259859));
#2523=DIRECTION('',(-0.0697564737436262,0.,-0.997564050259859));
#2524=DIRECTION('',(0.,-1.,0.));
#2525=DIRECTION('',(-0.0697564737436262,0.,-0.997564050259859));
#2526=DIRECTION('',(0.0697564737448007,0.,0.997564050259777));
#2527=DIRECTION('',(0.997564050259777,0.,-0.0697564737448007));
#2528=DIRECTION('',(-0.997564050259777,0.,0.0697564737448007));
#2529=DIRECTION('',(0.,-1.,0.));
#2530=DIRECTION('',(-0.997564050259777,0.,0.0697564737448007));
#2531=DIRECTION('',(0.,-1.,0.));
#2532=DIRECTION('',(0.,0.,-1.));
#2533=DIRECTION('',(0.,1.,0.));
#2534=DIRECTION('',(1.,0.,0.));
#2535=DIRECTION('',(0.,-1.,0.));
#2536=DIRECTION('',(0.,1.,0.));
#2537=DIRECTION('',(1.,0.,0.));
#2538=DIRECTION('',(0.996194698091693,0.,0.0871557427482611));
#2539=DIRECTION('',(0.0871557427482611,0.,-0.996194698091693));
#2540=DIRECTION('',(-0.0871557427482611,0.,0.996194698091693));
#2541=DIRECTION('',(0.,-1.,0.));
#2542=DIRECTION('',(-0.0871557427482611,0.,0.996194698091693));
#2543=DIRECTION('',(0.,-1.,0.));
#2544=DIRECTION('',(0.,0.,-1.));
#2545=DIRECTION('',(0.,-1.,0.));
#2546=DIRECTION('',(1.,0.,0.));
#2547=DIRECTION('',(0.,-1.,0.));
#2548=DIRECTION('',(0.,-1.,0.));
#2549=DIRECTION('',(1.,0.,0.));
#2550=DIRECTION('',(-1.37043154602297E-12,0.,1.));
#2551=DIRECTION('',(1.,0.,1.37043154602297E-12));
#2552=DIRECTION('',(0.,-1.,0.));
#2553=DIRECTION('',(-1.,0.,-1.37043154602297E-12));
#2554=DIRECTION('',(-1.,0.,-1.37043154602297E-12));
#2555=DIRECTION('',(0.,-1.,0.));
#2556=DIRECTION('',(0.,0.,-1.));
#2557=DIRECTION('',(0.,-1.,0.));
#2558=DIRECTION('',(1.,0.,0.));
#2559=DIRECTION('',(0.,-1.,0.));
#2560=DIRECTION('',(0.,-1.,0.));
#2561=DIRECTION('',(1.,0.,0.));
#2562=DIRECTION('',(-0.996194698091734,0.,0.0871557427477874));
#2563=DIRECTION('',(0.0871557427477874,0.,0.996194698091734));
#2564=DIRECTION('',(-0.0871557427477874,0.,-0.996194698091734));
#2565=DIRECTION('',(0.,-1.,0.));
#2566=DIRECTION('',(-0.0871557427477874,0.,-0.996194698091734));
#2567=DIRECTION('',(0.,-1.,0.));
#2568=DIRECTION('',(0.,0.,-1.));
#2569=DIRECTION('',(0.,1.,0.));
#2570=DIRECTION('',(1.,0.,0.));
#2571=DIRECTION('',(0.,-1.,0.));
#2572=DIRECTION('',(0.,1.,0.));
#2573=DIRECTION('',(1.,0.,0.));
#2574=DIRECTION('',(-0.069756473744275,0.,0.997564050259814));
#2575=DIRECTION('',(0.997564050259814,0.,0.0697564737442751));
#2576=DIRECTION('',(-0.997564050259814,0.,-0.0697564737442751));
#2577=DIRECTION('',(0.,-1.,0.));
#2578=DIRECTION('',(-0.997564050259814,0.,-0.0697564737442751));
#2579=DIRECTION('',(0.997564050259859,0.,0.0697564737436268));
#2580=DIRECTION('',(0.0697564737436268,0.,-0.997564050259859));
#2581=DIRECTION('',(-0.0697564737436268,0.,0.997564050259859));
#2582=DIRECTION('',(0.,-1.,0.));
#2583=DIRECTION('',(-0.0697564737436268,0.,0.997564050259859));
#2584=DIRECTION('',(0.0697564737439995,0.,-0.997564050259833));
#2585=DIRECTION('',(-0.997564050259833,0.,-0.0697564737439995));
#2586=DIRECTION('',(0.997564050259833,0.,0.0697564737439995));
#2587=DIRECTION('',(0.,-1.,0.));
#2588=DIRECTION('',(0.997564050259833,0.,0.0697564737439995));
#2589=DIRECTION('',(0.,-1.,0.));
#2590=DIRECTION('',(0.,0.,-1.));
#2591=DIRECTION('',(0.,-1.,0.));
#2592=DIRECTION('',(0.,-1.,0.));
#2593=DIRECTION('',(1.,0.,0.));
#2594=DIRECTION('',(0.,-1.,0.));
#2595=DIRECTION('',(1.,0.,0.));
#2596=DIRECTION('',(0.996194698091756,0.,-0.0871557427475426));
#2597=DIRECTION('',(-0.0871557427475426,0.,-0.996194698091756));
#2598=DIRECTION('',(0.0871557427475427,0.,0.996194698091756));
#2599=DIRECTION('',(0.,-1.,0.));
#2600=DIRECTION('',(0.0871557427475427,0.,0.996194698091756));
#2601=DIRECTION('',(0.,-1.,0.));
#2602=DIRECTION('',(0.,0.,-1.));
#2603=DIRECTION('',(0.,1.,0.));
#2604=DIRECTION('',(1.,0.,0.));
#2605=DIRECTION('',(0.,-1.,0.));
#2606=DIRECTION('',(0.,1.,0.));
#2607=DIRECTION('',(1.,0.,0.));
#2608=DIRECTION('',(1.31838984174398E-12,0.,-1.));
#2609=DIRECTION('',(-1.,0.,-1.31838984174398E-12));
#2610=DIRECTION('',(1.,0.,1.31838984174398E-12));
#2611=DIRECTION('',(0.,-1.,0.));
#2612=DIRECTION('',(1.,0.,1.31838984174398E-12));
#2613=DIRECTION('',(0.,-1.,0.));
#2614=DIRECTION('',(0.,0.,-1.));
#2615=DIRECTION('',(0.,1.,0.));
#2616=DIRECTION('',(1.,0.,0.));
#2617=DIRECTION('',(0.,-1.,0.));
#2618=DIRECTION('',(0.,1.,0.));
#2619=DIRECTION('',(1.,0.,0.));
#2620=DIRECTION('',(-0.996194698091737,0.,-0.0871557427477556));
#2621=DIRECTION('',(-0.0871557427477556,0.,0.996194698091737));
#2622=DIRECTION('',(0.0871557427477556,0.,-0.996194698091737));
#2623=DIRECTION('',(0.,-1.,0.));
#2624=DIRECTION('',(0.0871557427477556,0.,-0.996194698091737));
#2625=DIRECTION('',(0.,-1.,0.));
#2626=DIRECTION('',(0.,0.,-1.));
#2627=DIRECTION('',(0.,-1.,0.));
#2628=DIRECTION('',(1.,0.,0.));
#2629=DIRECTION('',(0.,-1.,0.));
#2630=DIRECTION('',(1.,0.,0.));
#2631=DIRECTION('',(0.,1.,0.));
#2632=DIRECTION('',(0.,0.,1.));
#2633=DIRECTION('',(0.,1.,0.));
#2634=DIRECTION('',(0.,0.,1.));
#2635=DIRECTION('',(1.31839678063789E-12,2.77555756155557E-17,1.));
#2636=DIRECTION('',(1.,5.55111512312212E-17,-1.31839678063789E-12));
#2637=CARTESIAN_POINT('',(0.,0.,0.));
#2638=CARTESIAN_POINT('',(0.,0.,0.));
#2639=CARTESIAN_POINT('',(0.619439052867227,-1.40791157932282,0.));
#2640=CARTESIAN_POINT('',(0.619439052867227,1.31943905286723,0.));
#2641=CARTESIAN_POINT('',(0.619439052867227,-1.31943905286723,0.));
#2642=CARTESIAN_POINT('',(-0.707911579322822,-1.31943905286723,0.));
#2643=CARTESIAN_POINT('',(-0.619439052867227,-1.31943905286723,0.));
#2644=CARTESIAN_POINT('',(-0.619439052867227,1.40791157932282,0.));
#2645=CARTESIAN_POINT('',(-0.619439052867227,1.31943905286723,0.));
#2646=CARTESIAN_POINT('',(0.707911579322822,1.31943905286723,0.));
#2647=CARTESIAN_POINT('',(-0.8,-1.5,0.75));
#2648=CARTESIAN_POINT('',(-0.8,-1.5,0.75));
#2649=CARTESIAN_POINT('',(-0.700745384835868,-1.5,0.75));
#2650=CARTESIAN_POINT('',(0.700745384835868,-1.5,0.75));
#2651=CARTESIAN_POINT('',(-0.702197954205393,-1.50145256936953,0.761830228156078));
#2652=CARTESIAN_POINT('',(-0.619439052867227,-1.41869366803136,0.0878130656594851));
#2653=CARTESIAN_POINT('',(0.707911579322822,-1.41869366803136,0.0878130656594851));
#2654=CARTESIAN_POINT('',(0.619439052867227,-1.41869366803136,0.0878130656594851));
#2655=CARTESIAN_POINT('',(0.678782307614077,-1.47803692277821,0.571125090481376));
#2656=CARTESIAN_POINT('',(0.8,-1.5,0.75));
#2657=CARTESIAN_POINT('',(0.718693668031359,1.40791157932282,0.0878130656594851));
#2658=CARTESIAN_POINT('',(0.718693668031359,-1.31943905286723,0.0878130656594851));
#2659=CARTESIAN_POINT('',(0.718693668031359,1.31943905286723,0.0878130656594851));
#2660=CARTESIAN_POINT('',(0.757548232010807,1.35829361684668,0.404258095016011));
#2661=CARTESIAN_POINT('',(0.8,1.40074538483587,0.75));
#2662=CARTESIAN_POINT('',(0.8,-1.5,0.75));
#2663=CARTESIAN_POINT('',(0.8,-1.40074538483587,0.75));
#2664=CARTESIAN_POINT('',(0.801452569369525,-1.40219795420539,0.761830228156078));
#2665=CARTESIAN_POINT('',(0.8,1.5,0.75));
#2666=CARTESIAN_POINT('',(-0.707911579322822,1.41869366803136,0.0878130656594851));
#2667=CARTESIAN_POINT('',(0.619439052867227,1.41869366803136,0.0878130656594851));
#2668=CARTESIAN_POINT('',(-0.619439052867227,1.41869366803136,0.0878130656594851));
#2669=CARTESIAN_POINT('',(-0.678782307614077,1.47803692277821,0.571125090481376));
#2670=CARTESIAN_POINT('',(-0.700745384835868,1.5,0.75));
#2671=CARTESIAN_POINT('',(0.8,1.5,0.75));
#2672=CARTESIAN_POINT('',(0.700745384835868,1.5,0.75));
#2673=CARTESIAN_POINT('',(0.702197954205393,1.50145256936953,0.761830228156078));
#2674=CARTESIAN_POINT('',(-0.8,1.5,0.75));
#2675=CARTESIAN_POINT('',(-0.718693668031359,-1.40791157932282,0.0878130656594851));
#2676=CARTESIAN_POINT('',(-0.718693668031359,1.31943905286723,0.0878130656594851));
#2677=CARTESIAN_POINT('',(-0.718693668031359,-1.31943905286723,0.0878130656594851));
#2678=CARTESIAN_POINT('',(-0.757548232010807,-1.35829361684668,0.404258095016011));
#2679=CARTESIAN_POINT('',(-0.8,-1.40074538483587,0.75));
#2680=CARTESIAN_POINT('',(-0.8,1.5,0.75));
#2681=CARTESIAN_POINT('',(-0.8,1.40074538483587,0.75));
#2682=CARTESIAN_POINT('',(-0.801452569369525,1.40219795420539,0.761830228156078));
#2683=CARTESIAN_POINT('',(0.,0.,1.1));
#2684=CARTESIAN_POINT('',(0.,1.36855287722839,1.1));
#2685=CARTESIAN_POINT('',(0.668552877228389,1.36855287722839,1.1));
#2686=CARTESIAN_POINT('',(-0.668552877228389,1.36855287722839,1.1));
#2687=CARTESIAN_POINT('',(-0.668552877228389,0.,1.1));
#2688=CARTESIAN_POINT('',(-0.668552877228389,-1.36855287722839,1.1));
#2689=CARTESIAN_POINT('',(0.,-1.36855287722839,1.1));
#2690=CARTESIAN_POINT('',(0.668552877228389,-1.36855287722839,1.1));
#2691=CARTESIAN_POINT('',(0.668552877228389,0.,1.1));
#2692=CARTESIAN_POINT('',(0.,-1.45702540368398,1.1));
#2693=CARTESIAN_POINT('',(0.,-1.46780749239252,1.01218693434051));
#2694=CARTESIAN_POINT('',(0.668552877228389,-1.46780749239252,1.01218693434051));
#2695=CARTESIAN_POINT('',(-0.668552877228389,-1.46780749239252,1.01218693434051));
#2696=CARTESIAN_POINT('',(-0.648144458318556,-1.44739907348269,1.17840016798942));
#2697=CARTESIAN_POINT('',(0.648144458318556,-1.44739907348269,1.17840016798942));
#2698=CARTESIAN_POINT('',(0.757025403683983,0.,1.1));
#2699=CARTESIAN_POINT('',(0.737154728098987,1.33790011293485,1.2618336657221));
#2700=CARTESIAN_POINT('',(0.767807492392521,1.36855287722839,1.01218693434051));
#2701=CARTESIAN_POINT('',(0.767807492392521,0.,1.01218693434051));
#2702=CARTESIAN_POINT('',(0.767807492392521,-1.36855287722839,1.01218693434051));
#2703=CARTESIAN_POINT('',(0.737154728098987,-1.33790011293486,1.2618336657221));
#2704=CARTESIAN_POINT('',(0.,1.45702540368398,1.1));
#2705=CARTESIAN_POINT('',(-0.648144458318556,1.44739907348269,1.17840016798942));
#2706=CARTESIAN_POINT('',(-0.668552877228389,1.46780749239252,1.01218693434051));
#2707=CARTESIAN_POINT('',(0.,1.46780749239252,1.01218693434051));
#2708=CARTESIAN_POINT('',(0.668552877228389,1.46780749239252,1.01218693434051));
#2709=CARTESIAN_POINT('',(0.648144458318556,1.44739907348269,1.17840016798942));
#2710=CARTESIAN_POINT('',(-0.757025403683983,0.,1.1));
#2711=CARTESIAN_POINT('',(-0.737154728098987,-1.33790011293486,1.2618336657221));
#2712=CARTESIAN_POINT('',(-0.767807492392521,-1.36855287722839,1.01218693434051));
#2713=CARTESIAN_POINT('',(-0.767807492392521,0.,1.01218693434051));
#2714=CARTESIAN_POINT('',(-0.767807492392521,1.36855287722839,1.01218693434051));
#2715=CARTESIAN_POINT('',(-0.737154728098987,1.33790011293486,1.2618336657221));
#2716=CARTESIAN_POINT('',(0.668552877228389,0.,1.));
#2717=CARTESIAN_POINT('',(0.668552877228389,1.36855287722839,1.));
#2718=CARTESIAN_POINT('',(0.668552877228389,-1.36855287722839,1.));
#2719=CARTESIAN_POINT('',(0.,1.36855287722839,1.));
#2720=CARTESIAN_POINT('',(-0.668552877228389,1.36855287722839,1.));
#2721=CARTESIAN_POINT('',(0.668552877228389,1.36855287722839,1.));
#2722=CARTESIAN_POINT('',(0.,-1.36855287722839,1.));
#2723=CARTESIAN_POINT('',(-0.668552877228389,-1.36855287722839,1.));
#2724=CARTESIAN_POINT('',(0.668552877228389,-1.36855287722839,1.));
#2725=CARTESIAN_POINT('',(-0.668552877228389,0.,1.));
#2726=CARTESIAN_POINT('',(-0.668552877228389,-1.36855287722839,1.));
#2727=CARTESIAN_POINT('',(-0.668552877228389,1.36855287722839,1.));
#2728=CARTESIAN_POINT('',(0.,1.31943905286723,0.0999999999999998));
#2729=CARTESIAN_POINT('',(-0.619439052867227,1.31943905286723,0.0999999999999998));
#2730=CARTESIAN_POINT('',(0.619439052867227,1.31943905286723,0.0999999999999998));
#2731=CARTESIAN_POINT('',(0.619439052867227,0.,0.0999999999999998));
#2732=CARTESIAN_POINT('',(0.619439052867227,1.31943905286723,0.0999999999999998));
#2733=CARTESIAN_POINT('',(0.619439052867227,-1.31943905286723,0.0999999999999998));
#2734=CARTESIAN_POINT('',(-0.619439052867227,0.,0.0999999999999998));
#2735=CARTESIAN_POINT('',(-0.619439052867227,-1.31943905286723,0.0999999999999998));
#2736=CARTESIAN_POINT('',(-0.619439052867227,1.31943905286723,0.0999999999999998));
#2737=CARTESIAN_POINT('',(0.,-1.31943905286723,0.0999999999999998));
#2738=CARTESIAN_POINT('',(-0.619439052867227,-1.31943905286723,0.0999999999999998));
#2739=CARTESIAN_POINT('',(0.619439052867227,-1.31943905286723,0.0999999999999998));
#2740=CARTESIAN_POINT('',(0.668552877228389,-1.36855287722839,1.));
#2741=CARTESIAN_POINT('',(0.668552877228389,-1.36855287722839,1.));
#2742=CARTESIAN_POINT('',(0.648144458318556,-1.34814445831856,1.16621323364891));
#2743=CARTESIAN_POINT('',(0.699249017454115,-1.39924901745412,0.75));
#2744=CARTESIAN_POINT('',(0.678782307614077,-1.37878230761408,0.58331202482189));
#2745=CARTESIAN_POINT('',(0.619439052867227,-1.31943905286723,0.0999999999999998));
#2746=CARTESIAN_POINT('',(0.619439052867227,-1.31943905286723,0.0999999999999998));
#2747=CARTESIAN_POINT('',(0.619439052867227,1.31943905286723,0.0999999999999998));
#2748=CARTESIAN_POINT('',(0.619439052867227,1.31943905286723,0.0999999999999999));
#2749=CARTESIAN_POINT('',(0.658293616846675,1.35829361684667,0.416445029356526));
#2750=CARTESIAN_POINT('',(0.699249017454115,1.39924901745412,0.75));
#2751=CARTESIAN_POINT('',(0.637900112934855,1.33790011293486,1.24964673138159));
#2752=CARTESIAN_POINT('',(0.668552877228389,1.36855287722839,1.));
#2753=CARTESIAN_POINT('',(0.668552877228389,1.36855287722839,1.));
#2754=CARTESIAN_POINT('',(-0.619439052867227,1.31943905286723,0.0999999999999998));
#2755=CARTESIAN_POINT('',(-0.619439052867227,1.31943905286723,0.0999999999999998));
#2756=CARTESIAN_POINT('',(-0.678782307614077,1.37878230761408,0.58331202482189));
#2757=CARTESIAN_POINT('',(-0.699249017454115,1.39924901745412,0.75));
#2758=CARTESIAN_POINT('',(-0.648144458318556,1.34814445831856,1.16621323364891));
#2759=CARTESIAN_POINT('',(-0.668552877228389,1.36855287722839,1.));
#2760=CARTESIAN_POINT('',(-0.668552877228389,1.36855287722839,1.));
#2761=CARTESIAN_POINT('',(-0.619439052867227,-1.31943905286723,0.0999999999999998));
#2762=CARTESIAN_POINT('',(-0.619439052867227,-1.31943905286723,0.0999999999999999));
#2763=CARTESIAN_POINT('',(-0.702197954205393,-1.40219795420539,0.774017162496593));
#2764=CARTESIAN_POINT('',(-0.699249017454115,-1.39924901745412,0.749999999999999));
#2765=CARTESIAN_POINT('',(-0.648144458318556,-1.34814445831856,1.16621323364891));
#2766=CARTESIAN_POINT('',(-0.668552877228389,-1.36855287722839,1.));
#2767=CARTESIAN_POINT('',(-0.668552877228389,-1.36855287722839,1.));
#2768=CARTESIAN_POINT('',(0.,0.,0.));
#2769=CARTESIAN_POINT('',(1.20001701649253,2.3,-0.296429938977112));
#2770=CARTESIAN_POINT('',(1.20001701649253,1.9,-0.296429938977112));
#2771=CARTESIAN_POINT('',(1.20001701649254,1.9,-0.296429938977083));
#2772=CARTESIAN_POINT('',(1.39999999999985,1.9,-0.310414111456797));
#2773=CARTESIAN_POINT('',(1.39999999999985,2.3,-0.310414111456797));
#2774=CARTESIAN_POINT('',(1.39999999999985,2.3,-0.310414111456797));
#2775=CARTESIAN_POINT('',(1.20001701649253,2.3,-0.296429938977112));
#2776=CARTESIAN_POINT('',(1.20001701649254,2.3,-0.296429938977083));
#2777=CARTESIAN_POINT('',(1.20001701649254,2.3,-0.296429938977083));
#2778=CARTESIAN_POINT('',(1.39999999999985,2.3,-0.310414111456797));
#2779=CARTESIAN_POINT('',(1.39999999999985,1.9,-0.310414111456797));
#2780=CARTESIAN_POINT('',(1.38814139946342,1.9,-0.480000000001093));
#2781=CARTESIAN_POINT('',(1.38814139946342,2.3,-0.480000000001093));
#2782=CARTESIAN_POINT('',(1.38814139946342,2.3,-0.480000000001093));
#2783=CARTESIAN_POINT('',(1.39999999999985,2.3,-0.310414111456797));
#2784=CARTESIAN_POINT('',(1.1881584159561,2.3,-0.466015827521339));
#2785=CARTESIAN_POINT('',(1.1881584159561,1.9,-0.466015827521339));
#2786=CARTESIAN_POINT('',(1.1881584159561,1.9,-0.466015827521339));
#2787=CARTESIAN_POINT('',(1.1881584159561,2.3,-0.466015827521339));
#2788=CARTESIAN_POINT('',(1.1881584159561,2.3,-0.466015827521339));
#2789=CARTESIAN_POINT('',(1.1881584159561,2.3,-0.466015827521339));
#2790=CARTESIAN_POINT('',(1.20699266386735,2.3,-0.196673533951188));
#2791=CARTESIAN_POINT('',(1.20699266386735,1.9,-0.196673533951188));
#2792=CARTESIAN_POINT('',(0.93802009538255,1.9,-0.22020558449298));
#2793=CARTESIAN_POINT('',(0.93802009538255,2.3,-0.22020558449298));
#2794=CARTESIAN_POINT('',(0.93802009538255,2.3,-0.22020558449298));
#2795=CARTESIAN_POINT('',(1.20699266386735,2.3,-0.196673533951188));
#2796=CARTESIAN_POINT('',(0.89961946980852,2.3,0.21871557427389));
#2797=CARTESIAN_POINT('',(0.89961946980852,1.9,0.21871557427389));
#2798=CARTESIAN_POINT('',(0.89961946980852,1.9,0.21871557427389));
#2799=CARTESIAN_POINT('',(0.89961946980852,2.3,0.21871557427389));
#2800=CARTESIAN_POINT('',(0.89961946980852,2.3,0.21871557427389));
#2801=CARTESIAN_POINT('',(0.89961946980852,2.3,0.21871557427389));
#2802=CARTESIAN_POINT('',(0.799999999999246,2.3,0.210000000001368));
#2803=CARTESIAN_POINT('',(0.799999999999246,1.9,0.210000000001368));
#2804=CARTESIAN_POINT('',(0.799999999999246,1.9,0.310000000001271));
#2805=CARTESIAN_POINT('',(0.799999999999246,2.3,0.310000000001271));
#2806=CARTESIAN_POINT('',(0.799999999999246,2.3,0.310000000001271));
#2807=CARTESIAN_POINT('',(0.799999999999246,2.3,0.210000000001368));
#2808=CARTESIAN_POINT('',(0.799999999999246,2.3,0.310000000001281));
#2809=CARTESIAN_POINT('',(0.799999999999246,1.9,0.310000000001281));
#2810=CARTESIAN_POINT('',(-0.799999999999246,1.9,0.309999999999089));
#2811=CARTESIAN_POINT('',(-0.799999999999246,2.3,0.309999999999089));
#2812=CARTESIAN_POINT('',(-0.799999999999246,2.3,0.309999999999089));
#2813=CARTESIAN_POINT('',(0.799999999999246,2.3,0.310000000001281));
#2814=CARTESIAN_POINT('',(-0.799999999999126,2.3,0.209999999999075));
#2815=CARTESIAN_POINT('',(-0.799999999999126,1.9,0.209999999999075));
#2816=CARTESIAN_POINT('',(-0.899619469808302,1.9,0.218715574273857));
#2817=CARTESIAN_POINT('',(-0.899619469808302,2.3,0.218715574273857));
#2818=CARTESIAN_POINT('',(-0.899619469808302,2.3,0.218715574273857));
#2819=CARTESIAN_POINT('',(-0.799999999999126,2.3,0.209999999999075));
#2820=CARTESIAN_POINT('',(-0.899619469808322,2.3,0.218715574273859));
#2821=CARTESIAN_POINT('',(-0.899619469808322,1.9,0.218715574273859));
#2822=CARTESIAN_POINT('',(-0.938020095382148,1.9,-0.220205584493079));
#2823=CARTESIAN_POINT('',(-0.938020095382148,2.3,-0.220205584493079));
#2824=CARTESIAN_POINT('',(-0.938020095382148,2.3,-0.220205584493079));
#2825=CARTESIAN_POINT('',(-0.899619469808322,2.3,0.218715574273859));
#2826=CARTESIAN_POINT('',(-1.20699266386692,2.3,-0.196673533951349));
#2827=CARTESIAN_POINT('',(-1.20699266386692,1.9,-0.196673533951349));
#2828=CARTESIAN_POINT('',(-1.18815841595588,1.9,-0.466015827521496));
#2829=CARTESIAN_POINT('',(-1.18815841595588,2.3,-0.466015827521496));
#2830=CARTESIAN_POINT('',(-1.18815841595588,2.3,-0.466015827521496));
#2831=CARTESIAN_POINT('',(-1.20699266386692,2.3,-0.196673533951349));
#2832=CARTESIAN_POINT('',(-1.18815841595588,2.3,-0.466015827521496));
#2833=CARTESIAN_POINT('',(-1.18815841595588,1.9,-0.466015827521496));
#2834=CARTESIAN_POINT('',(-1.38814139946342,1.9,-0.48000000000116));
#2835=CARTESIAN_POINT('',(-1.38814139946342,2.3,-0.48000000000116));
#2836=CARTESIAN_POINT('',(-1.38814139946342,2.3,-0.48000000000116));
#2837=CARTESIAN_POINT('',(-1.18815841595588,2.3,-0.466015827521496));
#2838=CARTESIAN_POINT('',(-1.39999999999985,2.3,-0.310414111456797));
#2839=CARTESIAN_POINT('',(-1.39999999999985,1.9,-0.310414111456797));
#2840=CARTESIAN_POINT('',(-1.39999999999985,1.9,-0.310414111456797));
#2841=CARTESIAN_POINT('',(-1.39999999999985,2.3,-0.310414111456797));
#2842=CARTESIAN_POINT('',(-1.39999999999985,2.3,-0.310414111456797));
#2843=CARTESIAN_POINT('',(-1.39999999999985,2.3,-0.310414111456797));
#2844=CARTESIAN_POINT('',(-1.20001701649231,2.3,-0.296429938977189));
#2845=CARTESIAN_POINT('',(-1.20001701649231,1.9,-0.296429938977189));
#2846=CARTESIAN_POINT('',(-1.20001701649231,1.9,-0.296429938977189));
#2847=CARTESIAN_POINT('',(-1.20001701649231,2.3,-0.296429938977189));
#2848=CARTESIAN_POINT('',(-1.20001701649231,2.3,-0.296429938977189));
#2849=CARTESIAN_POINT('',(-1.20001701649231,2.3,-0.296429938977189));
#2850=CARTESIAN_POINT('',(-1.20699266386692,2.3,-0.196673533951225));
#2851=CARTESIAN_POINT('',(-1.10737319405776,2.3,-0.205389108225992));
#2852=CARTESIAN_POINT('',(-1.10737319405776,2.3,-0.205389108225992));
#2853=CARTESIAN_POINT('',(-1.10737319405776,1.9,-0.205389108225992));
#2854=CARTESIAN_POINT('',(-1.20699266386692,2.3,-0.196673533951225));
#2855=CARTESIAN_POINT('',(-1.20699266386692,1.9,-0.196673533951225));
#2856=CARTESIAN_POINT('',(-1.06897256848404,2.3,0.233532050540927));
#2857=CARTESIAN_POINT('',(-1.06897256848404,1.9,0.233532050540927));
#2858=CARTESIAN_POINT('',(-1.06897256848404,1.9,0.233532050540927));
#2859=CARTESIAN_POINT('',(-1.06897256848404,2.3,0.233532050540927));
#2860=CARTESIAN_POINT('',(-1.06897256848404,2.3,0.233532050540927));
#2861=CARTESIAN_POINT('',(-1.06897256848404,2.3,0.233532050540927));
#2862=CARTESIAN_POINT('',(-0.799999999999246,2.3,0.209999999999072));
#2863=CARTESIAN_POINT('',(-0.799999999999246,1.9,0.209999999999072));
#2864=CARTESIAN_POINT('',(-0.799999999999246,1.9,0.479999999999065));
#2865=CARTESIAN_POINT('',(-0.799999999999246,2.3,0.479999999999065));
#2866=CARTESIAN_POINT('',(-0.799999999999246,2.3,0.479999999999065));
#2867=CARTESIAN_POINT('',(-0.799999999999246,2.3,0.209999999999072));
#2868=CARTESIAN_POINT('',(0.799999999998802,2.3,0.480000000001174));
#2869=CARTESIAN_POINT('',(0.799999999998802,2.3,0.480000000001174));
#2870=CARTESIAN_POINT('',(0.799999999998802,2.3,0.480000000001174));
#2871=CARTESIAN_POINT('',(0.799999999998802,1.9,0.480000000001174));
#2872=CARTESIAN_POINT('',(0.799999999998802,2.3,0.480000000001174));
#2873=CARTESIAN_POINT('',(0.799999999998802,1.9,0.480000000001174));
#2874=CARTESIAN_POINT('',(0.799999999999246,2.3,0.210000000001158));
#2875=CARTESIAN_POINT('',(0.799999999999246,1.9,0.210000000001158));
#2876=CARTESIAN_POINT('',(1.06897256848422,1.9,0.233532050540929));
#2877=CARTESIAN_POINT('',(1.06897256848422,2.3,0.233532050540929));
#2878=CARTESIAN_POINT('',(1.06897256848422,2.3,0.233532050540929));
#2879=CARTESIAN_POINT('',(0.799999999999246,2.3,0.210000000001158));
#2880=CARTESIAN_POINT('',(1.06897256848422,2.3,0.233532050540929));
#2881=CARTESIAN_POINT('',(1.06897256848422,1.9,0.233532050540929));
#2882=CARTESIAN_POINT('',(1.10737319405803,1.9,-0.205389108225968));
#2883=CARTESIAN_POINT('',(1.10737319405803,2.3,-0.205389108225968));
#2884=CARTESIAN_POINT('',(1.10737319405803,2.3,-0.205389108225968));
#2885=CARTESIAN_POINT('',(1.06897256848422,2.3,0.233532050540929));
#2886=CARTESIAN_POINT('',(1.20699266386711,2.3,-0.196673533951209));
#2887=CARTESIAN_POINT('',(1.20699266386711,1.9,-0.196673533951209));
#2888=CARTESIAN_POINT('',(1.20699266386711,2.3,-0.196673533951209));
#2889=CARTESIAN_POINT('',(1.20699266386735,2.3,-0.196673533951188));
#2890=CARTESIAN_POINT('',(1.20699266386735,1.9,-0.196673533951188));
#2891=CARTESIAN_POINT('',(0.799999999999246,1.35,0.310000000001281));
#2892=CARTESIAN_POINT('',(2.05998412772246E-15,1.35,0.310000000000185));
#2893=CARTESIAN_POINT('',(2.40042360988291E-13,1.35,0.310000000000185));
#2894=CARTESIAN_POINT('',(2.08166817136572E-15,0.95,0.31000000000018));
#2895=CARTESIAN_POINT('',(0.799999999999246,1.35,0.310000000001281));
#2896=CARTESIAN_POINT('',(0.799999999999246,1.35,0.310000000001271));
#2897=CARTESIAN_POINT('',(0.799999999999246,1.35,0.310000000001271));
#2898=CARTESIAN_POINT('',(0.799999999999246,0.95,0.310000000001271));
#2899=CARTESIAN_POINT('',(0.799999999999246,0.95,0.310000000001281));
#2900=CARTESIAN_POINT('',(0.799999999998802,1.35,0.480000000001174));
#2901=CARTESIAN_POINT('',(-2.22044604925031E-13,1.35,0.480000000000119));
#2902=CARTESIAN_POINT('',(-2.22044604925031E-13,1.35,0.480000000000119));
#2903=CARTESIAN_POINT('',(-2.22044604925031E-13,0.95,0.480000000000119));
#2904=CARTESIAN_POINT('',(0.799999999998802,0.95,0.480000000001174));
#2905=CARTESIAN_POINT('',(0.799999999998802,0.95,0.480000000001174));
#2906=CARTESIAN_POINT('',(0.799999999998802,1.35,0.480000000001174));
#2907=CARTESIAN_POINT('',(0.799999999998802,1.35,0.480000000001174));
#2908=CARTESIAN_POINT('',(0.799999999998802,1.35,0.480000000001174));
#2909=CARTESIAN_POINT('',(1.20699266386735,0.95,-0.196673533951188));
#2910=CARTESIAN_POINT('',(7.02180022200655E-13,0.95,-0.221025294538794));
#2911=CARTESIAN_POINT('',(0.799999999999246,0.95,0.210000000001368));
#2912=CARTESIAN_POINT('',(0.89961946980852,0.95,0.21871557427389));
#2913=CARTESIAN_POINT('',(0.89961946980852,0.95,0.21871557427389));
#2914=CARTESIAN_POINT('',(0.93802009538255,0.95,-0.22020558449298));
#2915=CARTESIAN_POINT('',(1.20699266386735,0.95,-0.196673533951188));
#2916=CARTESIAN_POINT('',(1.1881584159561,0.95,-0.466015827521339));
#2917=CARTESIAN_POINT('',(1.1881584159561,0.95,-0.466015827521339));
#2918=CARTESIAN_POINT('',(1.38814139946342,0.95,-0.480000000001093));
#2919=CARTESIAN_POINT('',(1.39999999999985,0.95,-0.310414111456797));
#2920=CARTESIAN_POINT('',(1.39999999999985,0.95,-0.310414111456797));
#2921=CARTESIAN_POINT('',(1.20001701649253,0.95,-0.296429938977112));
#2922=CARTESIAN_POINT('',(1.20001701649254,0.95,-0.296429938977083));
#2923=CARTESIAN_POINT('',(1.20699266386711,0.95,-0.196673533951209));
#2924=CARTESIAN_POINT('',(1.10737319405803,0.95,-0.205389108225968));
#2925=CARTESIAN_POINT('',(1.06897256848422,0.95,0.233532050540929));
#2926=CARTESIAN_POINT('',(1.06897256848422,0.95,0.233532050540929));
#2927=CARTESIAN_POINT('',(0.799999999999246,0.95,0.210000000001158));
#2928=CARTESIAN_POINT('',(1.20001701649253,1.35,-0.296429938977112));
#2929=CARTESIAN_POINT('',(1.39999999999985,1.35,-0.310414111456797));
#2930=CARTESIAN_POINT('',(1.39999999999985,1.35,-0.310414111456797));
#2931=CARTESIAN_POINT('',(1.20001701649253,1.35,-0.296429938977112));
#2932=CARTESIAN_POINT('',(1.20001701649254,1.35,-0.296429938977083));
#2933=CARTESIAN_POINT('',(1.20001701649254,1.35,-0.296429938977083));
#2934=CARTESIAN_POINT('',(1.39999999999985,1.35,-0.310414111456797));
#2935=CARTESIAN_POINT('',(1.38814139946342,1.35,-0.480000000001093));
#2936=CARTESIAN_POINT('',(1.38814139946342,1.35,-0.480000000001093));
#2937=CARTESIAN_POINT('',(1.39999999999985,1.35,-0.310414111456797));
#2938=CARTESIAN_POINT('',(1.1881584159561,1.35,-0.466015827521339));
#2939=CARTESIAN_POINT('',(1.1881584159561,1.35,-0.466015827521339));
#2940=CARTESIAN_POINT('',(1.1881584159561,1.35,-0.466015827521339));
#2941=CARTESIAN_POINT('',(1.1881584159561,1.35,-0.466015827521339));
#2942=CARTESIAN_POINT('',(1.20699266386735,1.35,-0.196673533951188));
#2943=CARTESIAN_POINT('',(0.93802009538255,1.35,-0.22020558449298));
#2944=CARTESIAN_POINT('',(0.93802009538255,1.35,-0.22020558449298));
#2945=CARTESIAN_POINT('',(1.20699266386735,1.35,-0.196673533951188));
#2946=CARTESIAN_POINT('',(0.89961946980852,1.35,0.21871557427389));
#2947=CARTESIAN_POINT('',(0.89961946980852,1.35,0.21871557427389));
#2948=CARTESIAN_POINT('',(0.89961946980852,1.35,0.21871557427389));
#2949=CARTESIAN_POINT('',(0.89961946980852,1.35,0.21871557427389));
#2950=CARTESIAN_POINT('',(0.799999999999246,1.35,0.210000000001368));
#2951=CARTESIAN_POINT('',(0.799999999999246,1.35,0.210000000001368));
#2952=CARTESIAN_POINT('',(0.799999999999246,1.35,0.210000000001158));
#2953=CARTESIAN_POINT('',(1.06897256848422,1.35,0.233532050540929));
#2954=CARTESIAN_POINT('',(1.06897256848422,1.35,0.233532050540929));
#2955=CARTESIAN_POINT('',(0.799999999999246,1.35,0.210000000001158));
#2956=CARTESIAN_POINT('',(1.06897256848422,1.35,0.233532050540929));
#2957=CARTESIAN_POINT('',(1.10737319405803,1.35,-0.205389108225968));
#2958=CARTESIAN_POINT('',(1.10737319405803,1.35,-0.205389108225968));
#2959=CARTESIAN_POINT('',(1.06897256848422,1.35,0.233532050540929));
#2960=CARTESIAN_POINT('',(1.20699266386711,1.35,-0.196673533951209));
#2961=CARTESIAN_POINT('',(1.20699266386711,1.35,-0.196673533951209));
#2962=CARTESIAN_POINT('',(1.20699266386735,1.35,-0.196673533951188));
#2963=CARTESIAN_POINT('',(7.02180022200655E-13,1.35,-0.221025294538794));
#2964=CARTESIAN_POINT('',(7.02180022200655E-13,1.35,-0.221025294538794));
#2965=CARTESIAN_POINT('',(1.20001701649253,0.4,-0.296429938977112));
#2966=CARTESIAN_POINT('',(1.20001701649253,0.,-0.296429938977112));
#2967=CARTESIAN_POINT('',(1.20001701649254,0.,-0.296429938977083));
#2968=CARTESIAN_POINT('',(1.39999999999985,0.,-0.310414111456797));
#2969=CARTESIAN_POINT('',(1.39999999999985,0.4,-0.310414111456797));
#2970=CARTESIAN_POINT('',(1.39999999999985,0.4,-0.310414111456797));
#2971=CARTESIAN_POINT('',(1.20001701649253,0.4,-0.296429938977112));
#2972=CARTESIAN_POINT('',(1.20001701649254,0.4,-0.296429938977083));
#2973=CARTESIAN_POINT('',(1.20001701649254,0.4,-0.296429938977083));
#2974=CARTESIAN_POINT('',(1.39999999999985,0.4,-0.310414111456797));
#2975=CARTESIAN_POINT('',(1.39999999999985,0.,-0.310414111456797));
#2976=CARTESIAN_POINT('',(1.38814139946342,0.,-0.480000000001093));
#2977=CARTESIAN_POINT('',(1.38814139946342,0.4,-0.480000000001093));
#2978=CARTESIAN_POINT('',(1.38814139946342,0.4,-0.480000000001093));
#2979=CARTESIAN_POINT('',(1.39999999999985,0.4,-0.310414111456797));
#2980=CARTESIAN_POINT('',(1.1881584159561,0.4,-0.466015827521339));
#2981=CARTESIAN_POINT('',(1.1881584159561,0.,-0.466015827521339));
#2982=CARTESIAN_POINT('',(1.1881584159561,0.,-0.466015827521339));
#2983=CARTESIAN_POINT('',(1.1881584159561,0.4,-0.466015827521339));
#2984=CARTESIAN_POINT('',(1.1881584159561,0.4,-0.466015827521339));
#2985=CARTESIAN_POINT('',(1.1881584159561,0.4,-0.466015827521339));
#2986=CARTESIAN_POINT('',(1.20699266386735,0.4,-0.196673533951188));
#2987=CARTESIAN_POINT('',(1.20699266386735,0.,-0.196673533951188));
#2988=CARTESIAN_POINT('',(0.93802009538255,0.,-0.22020558449298));
#2989=CARTESIAN_POINT('',(0.93802009538255,0.4,-0.22020558449298));
#2990=CARTESIAN_POINT('',(0.93802009538255,0.4,-0.22020558449298));
#2991=CARTESIAN_POINT('',(1.20699266386735,0.4,-0.196673533951188));
#2992=CARTESIAN_POINT('',(0.89961946980852,0.4,0.21871557427389));
#2993=CARTESIAN_POINT('',(0.89961946980852,0.,0.21871557427389));
#2994=CARTESIAN_POINT('',(0.89961946980852,0.,0.21871557427389));
#2995=CARTESIAN_POINT('',(0.89961946980852,0.4,0.21871557427389));
#2996=CARTESIAN_POINT('',(0.89961946980852,0.4,0.21871557427389));
#2997=CARTESIAN_POINT('',(0.89961946980852,0.4,0.21871557427389));
#2998=CARTESIAN_POINT('',(0.799999999999246,0.4,0.210000000001368));
#2999=CARTESIAN_POINT('',(0.799999999999246,0.,0.210000000001368));
#3000=CARTESIAN_POINT('',(0.799999999999246,0.,0.310000000001271));
#3001=CARTESIAN_POINT('',(0.799999999999246,0.4,0.310000000001271));
#3002=CARTESIAN_POINT('',(0.799999999999246,0.4,0.310000000001271));
#3003=CARTESIAN_POINT('',(0.799999999999246,0.4,0.210000000001368));
#3004=CARTESIAN_POINT('',(0.799999999999246,0.4,0.310000000001281));
#3005=CARTESIAN_POINT('',(-0.799999999999246,0.4,0.309999999999089));
#3006=CARTESIAN_POINT('',(-0.799999999999246,0.4,0.309999999999089));
#3007=CARTESIAN_POINT('',(-0.799999999999246,0.,0.309999999999089));
#3008=CARTESIAN_POINT('',(0.799999999999246,0.4,0.310000000001281));
#3009=CARTESIAN_POINT('',(0.799999999999246,0.,0.310000000001281));
#3010=CARTESIAN_POINT('',(-0.799999999999126,0.4,0.209999999999075));
#3011=CARTESIAN_POINT('',(-0.799999999999126,0.,0.209999999999075));
#3012=CARTESIAN_POINT('',(-0.899619469808302,0.,0.218715574273857));
#3013=CARTESIAN_POINT('',(-0.899619469808302,0.4,0.218715574273857));
#3014=CARTESIAN_POINT('',(-0.899619469808302,0.4,0.218715574273857));
#3015=CARTESIAN_POINT('',(-0.799999999999126,0.4,0.209999999999075));
#3016=CARTESIAN_POINT('',(-0.899619469808322,0.4,0.218715574273859));
#3017=CARTESIAN_POINT('',(-0.899619469808322,0.,0.218715574273859));
#3018=CARTESIAN_POINT('',(-0.938020095382148,0.,-0.220205584493079));
#3019=CARTESIAN_POINT('',(-0.938020095382148,0.4,-0.220205584493079));
#3020=CARTESIAN_POINT('',(-0.938020095382148,0.4,-0.220205584493079));
#3021=CARTESIAN_POINT('',(-0.899619469808322,0.4,0.218715574273859));
#3022=CARTESIAN_POINT('',(-1.20699266386692,0.4,-0.196673533951349));
#3023=CARTESIAN_POINT('',(-1.20699266386692,0.,-0.196673533951349));
#3024=CARTESIAN_POINT('',(-1.18815841595588,0.,-0.466015827521496));
#3025=CARTESIAN_POINT('',(-1.18815841595588,0.4,-0.466015827521496));
#3026=CARTESIAN_POINT('',(-1.18815841595588,0.4,-0.466015827521496));
#3027=CARTESIAN_POINT('',(-1.20699266386692,0.4,-0.196673533951349));
#3028=CARTESIAN_POINT('',(-1.18815841595588,0.4,-0.466015827521496));
#3029=CARTESIAN_POINT('',(-1.18815841595588,0.,-0.466015827521496));
#3030=CARTESIAN_POINT('',(-1.38814139946342,0.,-0.48000000000116));
#3031=CARTESIAN_POINT('',(-1.38814139946342,0.4,-0.48000000000116));
#3032=CARTESIAN_POINT('',(-1.38814139946342,0.4,-0.48000000000116));
#3033=CARTESIAN_POINT('',(-1.18815841595588,0.4,-0.466015827521496));
#3034=CARTESIAN_POINT('',(-1.39999999999985,0.4,-0.310414111456797));
#3035=CARTESIAN_POINT('',(-1.39999999999985,0.,-0.310414111456797));
#3036=CARTESIAN_POINT('',(-1.39999999999985,0.,-0.310414111456797));
#3037=CARTESIAN_POINT('',(-1.39999999999985,0.4,-0.310414111456797));
#3038=CARTESIAN_POINT('',(-1.39999999999985,0.4,-0.310414111456797));
#3039=CARTESIAN_POINT('',(-1.39999999999985,0.4,-0.310414111456797));
#3040=CARTESIAN_POINT('',(-1.20001701649231,0.4,-0.296429938977189));
#3041=CARTESIAN_POINT('',(-1.20001701649231,0.,-0.296429938977189));
#3042=CARTESIAN_POINT('',(-1.20001701649231,0.,-0.296429938977189));
#3043=CARTESIAN_POINT('',(-1.20001701649231,0.4,-0.296429938977189));
#3044=CARTESIAN_POINT('',(-1.20001701649231,0.4,-0.296429938977189));
#3045=CARTESIAN_POINT('',(-1.20001701649231,0.4,-0.296429938977189));
#3046=CARTESIAN_POINT('',(-1.20699266386692,0.4,-0.196673533951225));
#3047=CARTESIAN_POINT('',(-1.10737319405776,0.4,-0.205389108225992));
#3048=CARTESIAN_POINT('',(-1.10737319405776,0.4,-0.205389108225992));
#3049=CARTESIAN_POINT('',(-1.10737319405776,0.,-0.205389108225992));
#3050=CARTESIAN_POINT('',(-1.20699266386692,0.4,-0.196673533951225));
#3051=CARTESIAN_POINT('',(-1.20699266386692,0.,-0.196673533951225));
#3052=CARTESIAN_POINT('',(-1.06897256848404,0.4,0.233532050540927));
#3053=CARTESIAN_POINT('',(-1.06897256848404,0.,0.233532050540927));
#3054=CARTESIAN_POINT('',(-1.06897256848404,0.,0.233532050540927));
#3055=CARTESIAN_POINT('',(-1.06897256848404,0.4,0.233532050540927));
#3056=CARTESIAN_POINT('',(-1.06897256848404,0.4,0.233532050540927));
#3057=CARTESIAN_POINT('',(-1.06897256848404,0.4,0.233532050540927));
#3058=CARTESIAN_POINT('',(-0.799999999999246,0.4,0.209999999999072));
#3059=CARTESIAN_POINT('',(-0.799999999999246,0.,0.209999999999072));
#3060=CARTESIAN_POINT('',(-0.799999999999246,0.,0.479999999999065));
#3061=CARTESIAN_POINT('',(-0.799999999999246,0.4,0.479999999999065));
#3062=CARTESIAN_POINT('',(-0.799999999999246,0.4,0.479999999999065));
#3063=CARTESIAN_POINT('',(-0.799999999999246,0.4,0.209999999999072));
#3064=CARTESIAN_POINT('',(0.799999999998802,0.4,0.480000000001174));
#3065=CARTESIAN_POINT('',(0.799999999998802,0.,0.480000000001174));
#3066=CARTESIAN_POINT('',(0.799999999998802,0.,0.480000000001174));
#3067=CARTESIAN_POINT('',(0.799999999998802,0.4,0.480000000001174));
#3068=CARTESIAN_POINT('',(0.799999999998802,0.4,0.480000000001174));
#3069=CARTESIAN_POINT('',(0.799999999998802,0.4,0.480000000001174));
#3070=CARTESIAN_POINT('',(0.799999999999246,0.4,0.210000000001158));
#3071=CARTESIAN_POINT('',(0.799999999999246,0.,0.210000000001158));
#3072=CARTESIAN_POINT('',(1.06897256848422,0.,0.233532050540929));
#3073=CARTESIAN_POINT('',(1.06897256848422,0.4,0.233532050540929));
#3074=CARTESIAN_POINT('',(1.06897256848422,0.4,0.233532050540929));
#3075=CARTESIAN_POINT('',(0.799999999999246,0.4,0.210000000001158));
#3076=CARTESIAN_POINT('',(1.06897256848422,0.4,0.233532050540929));
#3077=CARTESIAN_POINT('',(1.06897256848422,0.,0.233532050540929));
#3078=CARTESIAN_POINT('',(1.10737319405803,0.,-0.205389108225968));
#3079=CARTESIAN_POINT('',(1.10737319405803,0.4,-0.205389108225968));
#3080=CARTESIAN_POINT('',(1.10737319405803,0.4,-0.205389108225968));
#3081=CARTESIAN_POINT('',(1.06897256848422,0.4,0.233532050540929));
#3082=CARTESIAN_POINT('',(1.20699266386711,0.4,-0.196673533951209));
#3083=CARTESIAN_POINT('',(1.20699266386711,0.,-0.196673533951209));
#3084=CARTESIAN_POINT('',(1.20699266386711,0.4,-0.196673533951209));
#3085=CARTESIAN_POINT('',(1.20699266386735,0.4,-0.196673533951188));
#3086=CARTESIAN_POINT('',(1.20699266386735,0.,-0.196673533951188));
#3087=CARTESIAN_POINT('',(-4.10695782937509E-13,-1.15,0.429999999999881));
#3088=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#1570,
#1571,#1572,#1573,#1574,#1575,#1576,#1577,#1578,#1579,#1580,#1581,#1582,
#1583,#1584,#1585,#1586,#1587,#1588,#1589,#1590,#1591,#1592,#1593,#1594,
#1595,#1596,#1597,#1598,#1599,#1600,#1601,#1602,#1603,#1604,#1605,#1606,
#1607,#1608,#1609,#1610,#1611,#1612,#1613,#1614,#1615,#1616,#1617,#1618,
#1619,#1620,#1621,#1622,#1623,#1624,#1625,#1626,#1627,#1628),#3089);
#3089=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3092))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3097,#3096,#3095))
REPRESENTATION_CONTEXT('SOT-25','TOP_LEVEL_ASSEMBLY_PART')
);
#3090=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3093))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3097,#3096,#3095))
REPRESENTATION_CONTEXT('CPD','COMPONENT_PART')
);
#3091=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3094))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3097,#3096,#3095))
REPRESENTATION_CONTEXT('LDF','COMPONENT_PART')
);
#3092=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#3097,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#3093=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#3097,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#3094=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#3097,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#3095=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#3096=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#3097=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#3098=PRODUCT_DEFINITION_SHAPE('','',#3103);
#3099=PRODUCT_DEFINITION_SHAPE('','',#3104);
#3100=PRODUCT_DEFINITION_SHAPE(' ','NAUO PRDDFN',#20);
#3101=PRODUCT_DEFINITION_SHAPE('','',#3105);
#3102=PRODUCT_DEFINITION_SHAPE(' ','NAUO PRDDFN',#21);
#3103=PRODUCT_DEFINITION('','',#3109,#3106);
#3104=PRODUCT_DEFINITION('','',#3110,#3107);
#3105=PRODUCT_DEFINITION('','',#3111,#3108);
#3106=PRODUCT_DEFINITION_CONTEXT('',#3125,'design');
#3107=PRODUCT_DEFINITION_CONTEXT('',#3125,'design');
#3108=PRODUCT_DEFINITION_CONTEXT('',#3125,'design');
#3109=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#3115,
 .NOT_KNOWN.);
#3110=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#3116,
 .NOT_KNOWN.);
#3111=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#3117,
 .NOT_KNOWN.);
#3112=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#3115));
#3113=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#3116));
#3114=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#3117));
#3115=PRODUCT('SOT-25','SOT-25','SOT-25',(#3121));
#3116=PRODUCT('CPD','CPD','CPD',(#3122));
#3117=PRODUCT('LDF','LDF','LDF',(#3123));
#3118=PRODUCT_CATEGORY('','');
#3119=PRODUCT_CATEGORY('','');
#3120=PRODUCT_CATEGORY('','');
#3121=PRODUCT_CONTEXT('',#3125,'mechanical');
#3122=PRODUCT_CONTEXT('',#3125,'mechanical');
#3123=PRODUCT_CONTEXT('',#3125,'mechanical');
#3124=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2010,#3125);
#3125=APPLICATION_CONTEXT(
'core data for automotive mechanical design processes');
ENDSEC;
END-ISO-10303-21;
