#!/usr/bin/env python3
"""
SCREEN BY SCREEN ULTRALIBRARIAN
===============================
Show each screen and wait for user confirmation.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def screen_by_screen():
    print("🎯 SCREEN BY SCREEN ULTRALIBRARIAN")
    print("=" * 50)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # SCREEN 1: Load UltraLibrarian
        print("\n📺 SCREEN 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        print(f"✅ Page loaded")
        print(f"Title: {driver.title}")
        print(f"URL: {driver.current_url}")
        
        print("\n🔍 What I see on Screen 1:")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"- {len(inputs)} input elements found")
        
        for i, inp in enumerate(inputs[:5]):
            try:
                placeholder = inp.get_attribute('placeholder') or 'none'
                visible = inp.is_displayed()
                print(f"  Input {i}: placeholder='{placeholder}', visible={visible}")
            except:
                continue
        
        response = input("\n❓ Is this correct? Should I proceed to search? (y/n): ")
        if response.lower() != 'y':
            print("❌ Stopping - please tell me what's wrong")
            return
        
        # SCREEN 2: Search for LM358N
        print("\n📺 SCREEN 2: Searching for LM358N...")
        
        search_box = None
        for inp in inputs:
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower():
                        search_box = inp
                        print(f"✅ Found search box: '{placeholder}'")
                        break
            except:
                continue
        
        if not search_box:
            print("❌ No search box found!")
            return
        
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        
        print("✅ Entered 'LM358N' and pressed Enter")
        time.sleep(10)
        
        print(f"New URL: {driver.current_url}")
        
        response = input("\n❓ Did the search work? Do you see LM358N results? (y/n): ")
        if response.lower() != 'y':
            print("❌ Stopping - please tell me what you see")
            return
        
        # SCREEN 3: Show search results
        print("\n📺 SCREEN 3: LM358N search results...")
        
        links = driver.find_elements(By.TAG_NAME, "a")
        lm358_links = []
        
        print("🔍 LM358N-related links I found:")
        for i, link in enumerate(links):
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                
                if (('lm358' in text.lower() or 'lm358' in href.lower()) and 
                    link.is_displayed() and link.is_enabled()):
                    lm358_links.append((i, text, href, link))
                    print(f"  {len(lm358_links)}: '{text}' -> {href}")
            except:
                continue
        
        if not lm358_links:
            print("❌ No LM358N links found!")
            return
        
        # Find Texas Instruments specifically
        ti_link = None
        for i, text, href, link_element in lm358_links:
            if (('lm358n/nopb' in text.lower() or 
                 ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                'details' in href.lower() and 'login' not in href.lower()):
                ti_link = (i, text, href, link_element)
                print(f"  ✅ Texas Instruments option: '{text}'")
                break
        
        if not ti_link:
            print("❌ No Texas Instruments LM358N found!")
            return
        
        response = input("\n❓ Should I click on the Texas Instruments LM358N? (y/n): ")
        if response.lower() != 'y':
            print("❌ Stopping - please tell me which option to click")
            return
        
        # SCREEN 4: Click on TI LM358N
        print("\n📺 SCREEN 4: Clicking Texas Instruments LM358N...")
        
        i, text, href, link_element = ti_link
        link_element.click()
        time.sleep(8)
        
        print(f"✅ Clicked: '{text}'")
        print(f"New URL: {driver.current_url}")
        
        # Show what's on the part details page
        print("\n🔍 What I see on the part details page:")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print("Available buttons:")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if text:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        response = input("\n❓ Is this the correct part details page? What should I click next? (tell me): ")
        print(f"You said: {response}")
        
        print("\n🔍 SCREEN BY SCREEN ANALYSIS COMPLETE")
        print("Browser will stay open for you to inspect...")
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    screen_by_screen()
