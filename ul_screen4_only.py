#!/usr/bin/env python3
"""
UltraLibrarian Screen 4 Only - Click Download Now button
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def screen_4_only():
    print("🔸 SCREEN 4: Click Download Now button")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    try:
        # Repeat Screens 1-3 to get to Download Now button
        print("   Repeating Screens 1-3 to get to Download Now...")
        driver.get("https://app.ultralibrarian.com")
        time.sleep(10)
        
        # Search for LM358N
        inputs = driver.find_elements(By.TAG_NAME, "input")
        search_input = None
        for inp in inputs:
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower() or 'part' in placeholder.lower():
                        search_input = inp
                        break
            except:
                continue
        
        if search_input:
            search_input.clear()
            search_input.send_keys("LM358N")
            search_input.send_keys(Keys.RETURN)
            time.sleep(8)
        
        # Click on TI LM358N
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                if link.is_displayed():
                    link_text = link.text.strip()
                    if ('lm358n' in link_text.lower() and 
                        ('nopb' in link_text.lower() or 'texas' in link_text.lower())):
                        print(f"   ✅ Clicking on: {link_text}")
                        link.click()
                        time.sleep(10)
                        break
            except:
                continue
        
        print(f"   ✅ Now on part details page")
        
        # Now Screen 4: Click Download Now
        print("   Looking for and clicking 'Download Now' button...")
        
        download_button = None
        try:
            download_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Download Now')]")
            if download_button.is_displayed():
                print(f"   ✅ Found Download Now button")
                download_button.click()
                time.sleep(10)
                print(f"   ✅ Clicked Download Now button")
            else:
                print(f"   ❌ Download Now button not visible")
        except:
            print(f"   ❌ Download Now button not found")
        
        print(f"   ✅ Current URL after click: {driver.current_url}")
        
        # Check what's on the page now
        page_text = driver.page_source.lower()
        if '3d' in page_text:
            print("   ✅ Found '3d' text on page")
        if 'model' in page_text:
            print("   ✅ Found 'model' text on page")
        if 'download' in page_text:
            print("   ✅ Found 'download' text on page")
        
        # Look for 3D Model button
        print("   Looking for '3D Model' or '3D CAD Model' button...")
        
        model_selectors = [
            "//button[contains(text(), '3D CAD Model')]",
            "//a[contains(text(), '3D CAD Model')]",
            "//button[contains(text(), '3D Model')]",
            "//a[contains(text(), '3D Model')]",
            "//button[contains(text(), '3D')]",
            "//a[contains(text(), '3D')]"
        ]
        
        model_found = False
        for i, selector in enumerate(model_selectors):
            try:
                elements = driver.find_elements(By.XPATH, selector)
                print(f"   3D Selector {i+1}: Found {len(elements)} elements")
                
                for j, element in enumerate(elements):
                    try:
                        if element.is_displayed():
                            text = element.text.strip()
                            print(f"     3D Element {j+1}: '{text}'")
                            
                            if '3d' in text.lower():
                                print(f"   ✅ Found 3D button: '{text}'")
                                model_found = True
                                break
                    except:
                        continue
                        
                if model_found:
                    break
            except:
                continue
        
        if not model_found:
            print("   ❌ No 3D Model button found")
            # Show what buttons are available
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print(f"   Available buttons after Download Now:")
            for i, btn in enumerate(buttons[:15]):
                try:
                    if btn.is_displayed():
                        text = btn.text.strip()
                        if text:
                            print(f"     Button {i+1}: '{text}'")
                except:
                    continue
        
        print("\n🎯 SCREEN 4 COMPLETE")
        print("Browser will stay open for 120 seconds for you to inspect")
        
        # Keep browser open longer
        time.sleep(120)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    screen_4_only()
