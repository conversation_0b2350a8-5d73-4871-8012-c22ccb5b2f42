#!/usr/bin/env python3
"""
SIMPLE: Go directly to TI search results (method that works)
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def simple_ti_direct():
    print("SIMPLE TI DIRECT SEARCH")
    print("=" * 30)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Go directly to TI search results for LM358N
        search_url = "https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm=LM358N"
        print(f"Going directly to: {search_url}")
        
        driver.get(search_url)
        time.sleep(10)
        
        print(f"Loaded: {driver.title}")
        print(f"URL: {driver.current_url}")
        
        # Check if we got results
        if "LM358N" in driver.page_source.upper():
            print("✅ SUCCESS: Found LM358N results!")
            
            # Look for 3D model or STEP file links
            page_text = driver.page_source.lower()
            if "step" in page_text or "3d" in page_text:
                print("✅ Found 3D/STEP content on page!")
            else:
                print("⚠️ No 3D/STEP content found")
        else:
            print("❌ No LM358N found")
        
    finally:
        input("Press Enter to close...")
        driver.quit()

if __name__ == "__main__":
    simple_ti_direct()
