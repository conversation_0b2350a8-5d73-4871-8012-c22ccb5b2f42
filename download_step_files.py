#!/usr/bin/env python3
"""
Download STEP files from Diodes Inc website
"""

import requests
import os

def download_step_file(step_url, filename):
    """Download a STEP file"""
    print(f"📥 Downloading: {filename}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        # Make URL absolute
        if not step_url.startswith('http'):
            step_url = f"https://www.diodes.com{step_url}"
        
        print(f"   URL: {step_url}")
        response = session.get(step_url, timeout=60, stream=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Create 3d directory
            os.makedirs('3d', exist_ok=True)
            
            # Save STEP file
            filepath = os.path.join('3d', filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:  # At least 1KB
                # Verify it's a STEP file
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    first_lines = f.read(200)
                
                if 'ISO-10303' in first_lines or 'STEP' in first_lines:
                    print(f"   🎉 SUCCESS: Valid STEP file!")
                    return True
                else:
                    print(f"   ⚠️  File doesn't appear to be STEP format")
                    print(f"   First lines: {first_lines[:100]}...")
                    return False
            else:
                print(f"   ⚠️  File too small, might be error page")
                return False
        else:
            print(f"   ❌ Download failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    print("🚀 DOWNLOADING STEP FILES FROM DIODES INC")
    print("Part: APX803L20-30SA-7 (SOT23 package)")
    print("=" * 50)
    
    # STEP files found on the manufacturer website
    step_files = [
        {
            'url': '/assets/STEP/SOT23.stp',
            'filename': 'APX803L20-30SA-7_SOT23_Diodes.step',
            'package': 'SOT23'
        },
        {
            'url': '/assets/STEP-Files/SOT25.stp', 
            'filename': 'SOT25_Diodes.step',
            'package': 'SOT25'
        },
        {
            'url': '/assets/STEP-Files/SOT323.stp',
            'filename': 'SOT323_Diodes.step', 
            'package': 'SOT323'
        }
    ]
    
    downloaded_files = []
    
    for step_file in step_files:
        print(f"\n📦 Package: {step_file['package']}")
        success = download_step_file(step_file['url'], step_file['filename'])
        
        if success:
            downloaded_files.append(step_file['filename'])
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 DOWNLOAD SUMMARY:")
    
    if downloaded_files:
        print(f"✅ SUCCESS: Downloaded {len(downloaded_files)} STEP files!")
        print(f"\n🎯 STEP FILES DOWNLOADED:")
        for file in downloaded_files:
            filepath = os.path.join('3d', file)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                print(f"   ✅ {file} ({size:,} bytes)")
        
        print(f"\n🎉 GOAL ACHIEVED: Found 3D models for APX803L20-30SA-7!")
        print(f"   Primary package: SOT23")
        print(f"   Location: 3d/ directory")
    else:
        print("❌ FAILED: No STEP files downloaded")

if __name__ == "__main__":
    main()
