#!/usr/bin/env python3
"""
MANUFACTURER STEP FILE DOWNLOADER
=================================
Easy-to-use tool for downloading STEP files from manufacturer websites.
Handles multiple screens, authentication, and various website patterns.

Usage:
    python manufacturer_step_downloader.py "TI" "LM358N"
    python manufacturer_step_downloader.py "Diodes Inc" "APX803L20-30SA-7"
"""

import requests
import json
import os
import time
import re
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import argparse

class ManufacturerStepDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # Load manufacturer knowledge base
        self.manufacturer_patterns = self.load_manufacturer_patterns()
        
        # Create downloads directory
        os.makedirs('step_downloads', exist_ok=True)
        
        print("🚀 Manufacturer STEP File Downloader Ready!")
        print("=" * 50)

    def load_manufacturer_patterns(self):
        """Load manufacturer-specific website patterns and strategies"""
        return {
            'texas instruments': {
                'aliases': ['ti', 'texas instruments', 'texas_instruments'],
                'base_url': 'https://www.ti.com',
                'search_pattern': '/product/{part_number}',
                'step_selectors': [
                    'a[href*=".step"]',
                    'a[href*="3d-model"]',
                    'a[href*="cad"]',
                    '.download-link[href*="step"]'
                ],
                'requires_js': True,
                'auth_required': False
            },
            'diodes incorporated': {
                'aliases': ['diodes inc', 'diodes', 'diodes incorporated'],
                'base_url': 'https://www.diodes.com',
                'search_pattern': '/part/{part_number}',
                'step_selectors': [
                    'a[href*=".step"]',
                    'a[href*="3d"]',
                    '.cad-download'
                ],
                'requires_js': True,
                'auth_required': False
            },
            'analog devices': {
                'aliases': ['adi', 'analog devices', 'analog_devices'],
                'base_url': 'https://www.analog.com',
                'search_pattern': '/en/products/{part_number}',
                'step_selectors': [
                    'a[href*=".step"]',
                    'a[href*="3d-model"]',
                    '.download-cad'
                ],
                'requires_js': True,
                'auth_required': False
            },
            'infineon': {
                'aliases': ['infineon', 'infineon technologies'],
                'base_url': 'https://www.infineon.com',
                'search_pattern': '/cms/en/product/{part_number}',
                'step_selectors': [
                    'a[href*=".step"]',
                    'a[href*="3d"]',
                    '.cad-file'
                ],
                'requires_js': True,
                'auth_required': False
            },
            'stmicroelectronics': {
                'aliases': ['st', 'stm', 'stmicroelectronics'],
                'base_url': 'https://www.st.com',
                'search_pattern': '/en/products/{part_number}',
                'step_selectors': [
                    'a[href*=".step"]',
                    'a[href*="3d-model"]',
                    '.download-cad'
                ],
                'requires_js': True,
                'auth_required': False
            }
        }

    def normalize_manufacturer(self, manufacturer):
        """Normalize manufacturer name to match patterns"""
        manufacturer_lower = manufacturer.lower().strip()
        
        for key, config in self.manufacturer_patterns.items():
            if manufacturer_lower in config['aliases']:
                return key
        
        return manufacturer_lower

    def setup_selenium_driver(self):
        """Setup Selenium WebDriver with appropriate options"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Add download preferences
        prefs = {
            "download.default_directory": os.path.abspath('step_downloads'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"❌ Failed to setup Chrome driver: {e}")
            return None

    def search_with_requests(self, manufacturer, part_number):
        """Try to find STEP files using requests (faster method)"""
        print(f"🔍 Searching {manufacturer} website for {part_number}...")
        
        manufacturer_key = self.normalize_manufacturer(manufacturer)
        
        if manufacturer_key not in self.manufacturer_patterns:
            print(f"⚠️ No specific pattern for {manufacturer}, trying generic search...")
            return self.generic_search(manufacturer, part_number)
        
        config = self.manufacturer_patterns[manufacturer_key]
        
        # Try direct product URL
        search_url = config['base_url'] + config['search_pattern'].format(part_number=part_number)
        
        try:
            print(f"   📡 Trying: {search_url}")
            response = self.session.get(search_url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Look for STEP file links
                step_links = []
                for selector in config['step_selectors']:
                    elements = soup.select(selector)
                    for element in elements:
                        href = element.get('href')
                        if href:
                            full_url = urljoin(config['base_url'], href)
                            text = element.get_text().strip()
                            step_links.append((full_url, text))
                
                if step_links:
                    print(f"   ✅ Found {len(step_links)} potential STEP links")
                    return self.download_step_files(step_links, manufacturer, part_number)
                else:
                    print(f"   ⚠️ No STEP links found with requests, trying Selenium...")
                    if config['requires_js']:
                        return self.search_with_selenium(manufacturer, part_number, search_url)
            
        except Exception as e:
            print(f"   ❌ Requests search failed: {e}")
        
        return None

    def search_with_selenium(self, manufacturer, part_number, url=None):
        """Use Selenium for JavaScript-heavy sites"""
        print(f"🤖 Using Selenium for {manufacturer} {part_number}...")
        
        driver = self.setup_selenium_driver()
        if not driver:
            return None
        
        try:
            if not url:
                # Generic search if no specific URL
                manufacturer_key = self.normalize_manufacturer(manufacturer)
                if manufacturer_key in self.manufacturer_patterns:
                    config = self.manufacturer_patterns[manufacturer_key]
                    url = config['base_url'] + config['search_pattern'].format(part_number=part_number)
                else:
                    print(f"❌ No URL pattern for {manufacturer}")
                    return None
            
            print(f"   🌐 Loading: {url}")
            driver.get(url)
            
            # Wait for page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Look for STEP file download buttons/links
            step_elements = self.find_step_elements_selenium(driver, manufacturer)
            
            if step_elements:
                print(f"   ✅ Found {len(step_elements)} STEP download options")
                return self.handle_step_downloads_selenium(driver, step_elements, manufacturer, part_number)
            else:
                print(f"   ⚠️ No STEP downloads found on main page, checking for additional pages...")
                return self.explore_additional_pages_selenium(driver, manufacturer, part_number)
                
        except Exception as e:
            print(f"   ❌ Selenium search failed: {e}")
        finally:
            driver.quit()
        
        return None

    def find_step_elements_selenium(self, driver, manufacturer):
        """Find STEP file download elements using Selenium"""
        step_elements = []
        
        # Common selectors for STEP files
        selectors = [
            "a[href*='.step']",
            "a[href*='.stp']",
            "a[href*='3d-model']",
            "a[href*='cad']",
            "button[onclick*='step']",
            ".download-step",
            ".cad-download",
            ".3d-model-download"
        ]
        
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.strip().lower()
                    if any(keyword in text for keyword in ['step', '3d', 'cad', 'download']):
                        step_elements.append(element)
            except:
                continue
        
        # Also look for text-based links
        try:
            all_links = driver.find_elements(By.TAG_NAME, "a")
            for link in all_links:
                text = link.text.strip().lower()
                href = link.get_attribute('href') or ''
                
                if (any(keyword in text for keyword in ['step', '3d model', 'cad download']) or
                    any(ext in href.lower() for ext in ['.step', '.stp'])):
                    step_elements.append(link)
        except:
            pass
        
        return step_elements

    def handle_step_downloads_selenium(self, driver, step_elements, manufacturer, part_number):
        """Handle STEP file downloads using Selenium"""
        downloaded_files = []
        
        for i, element in enumerate(step_elements[:3]):  # Try first 3 elements
            try:
                print(f"   📥 Attempting download {i+1}/{min(3, len(step_elements))}...")
                
                # Get initial file count
                initial_files = set(os.listdir('step_downloads'))
                
                # Click the download element
                driver.execute_script("arguments[0].click();", element)
                
                # Wait for download to start/complete
                time.sleep(5)
                
                # Check for new files
                current_files = set(os.listdir('step_downloads'))
                new_files = current_files - initial_files
                
                if new_files:
                    for new_file in new_files:
                        if any(ext in new_file.lower() for ext in ['.step', '.stp']):
                            # Rename file to include part number
                            old_path = os.path.join('step_downloads', new_file)
                            new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_{new_file}"
                            new_path = os.path.join('step_downloads', new_name)
                            
                            try:
                                os.rename(old_path, new_path)
                                downloaded_files.append(new_name)
                                print(f"   ✅ Downloaded: {new_name}")
                            except:
                                downloaded_files.append(new_file)
                                print(f"   ✅ Downloaded: {new_file}")
                
            except Exception as e:
                print(f"   ⚠️ Download attempt {i+1} failed: {e}")
                continue
        
        return downloaded_files if downloaded_files else None

    def explore_additional_pages_selenium(self, driver, manufacturer, part_number):
        """Explore additional pages that might contain STEP files"""
        print(f"   🔍 Exploring additional pages...")
        
        # Look for tabs or sections that might contain CAD files
        tab_selectors = [
            "a[href*='cad']",
            "a[href*='3d']",
            "a[href*='model']",
            "a[href*='download']",
            ".tab[data-tab*='cad']",
            ".tab[data-tab*='3d']",
            "button[onclick*='cad']"
        ]
        
        for selector in tab_selectors:
            try:
                tabs = driver.find_elements(By.CSS_SELECTOR, selector)
                for tab in tabs:
                    text = tab.text.strip().lower()
                    if any(keyword in text for keyword in ['cad', '3d', 'model', 'download']):
                        print(f"   🔗 Clicking tab: {text}")
                        driver.execute_script("arguments[0].click();", tab)
                        time.sleep(3)
                        
                        # Look for STEP files on this page
                        step_elements = self.find_step_elements_selenium(driver, manufacturer)
                        if step_elements:
                            return self.handle_step_downloads_selenium(driver, step_elements, manufacturer, part_number)
            except:
                continue
        
        return None

    def generic_search(self, manufacturer, part_number):
        """Generic search for manufacturers not in our patterns"""
        print(f"   🔍 Generic search for {manufacturer} {part_number}")
        
        # Try common manufacturer website patterns
        common_patterns = [
            f"https://www.{manufacturer.lower().replace(' ', '')}.com/products/{part_number}",
            f"https://www.{manufacturer.lower().replace(' ', '')}.com/product/{part_number}",
            f"https://www.{manufacturer.lower().replace(' ', '')}.com/{part_number}",
        ]
        
        for url in common_patterns:
            try:
                print(f"   📡 Trying: {url}")
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Look for STEP file links
                    step_links = []
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        text = link.get_text().lower()
                        
                        if (any(ext in href.lower() for ext in ['.step', '.stp']) or
                            any(keyword in text for keyword in ['step', '3d model', 'cad download'])):
                            full_url = urljoin(url, href)
                            step_links.append((full_url, text))
                    
                    if step_links:
                        return self.download_step_files(step_links, manufacturer, part_number)
                        
            except Exception as e:
                print(f"   ❌ Failed {url}: {e}")
                continue
        
        return None

    def download_step_files(self, step_links, manufacturer, part_number):
        """Download STEP files from found links"""
        downloaded_files = []
        
        for i, (url, text) in enumerate(step_links[:3]):  # Try first 3 links
            try:
                print(f"   📥 Downloading {i+1}/{min(3, len(step_links))}: {text[:30]}...")
                
                response = self.session.get(url, timeout=60)
                
                if response.status_code == 200:
                    # Determine filename
                    filename = url.split('/')[-1]
                    if not any(ext in filename.lower() for ext in ['.step', '.stp']):
                        filename = f"{manufacturer.replace(' ', '_')}_{part_number}_{i+1}.step"
                    else:
                        # Add manufacturer and part number to filename
                        name, ext = os.path.splitext(filename)
                        filename = f"{manufacturer.replace(' ', '_')}_{part_number}_{name}{ext}"
                    
                    filepath = os.path.join('step_downloads', filename)
                    
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    
                    downloaded_files.append(filename)
                    print(f"   ✅ Downloaded: {filename}")
                    
            except Exception as e:
                print(f"   ❌ Download failed: {e}")
                continue
        
        return downloaded_files if downloaded_files else None

    def download_step_file(self, manufacturer, part_number):
        """Main method to download STEP file for a component"""
        print(f"\n🎯 SEARCHING FOR STEP FILE")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("-" * 40)
        
        # Try requests first (faster)
        result = self.search_with_requests(manufacturer, part_number)
        
        # If requests failed, try Selenium
        if not result:
            print(f"🤖 Falling back to Selenium...")
            result = self.search_with_selenium(manufacturer, part_number)
        
        if result:
            print(f"\n✅ SUCCESS! Downloaded {len(result)} STEP file(s):")
            for filename in result:
                print(f"   📁 {filename}")
            print(f"📂 Files saved to: step_downloads/")
        else:
            print(f"\n❌ No STEP files found for {manufacturer} {part_number}")
            print(f"💡 Try checking the manufacturer's website manually")
        
        return result

def main():
    parser = argparse.ArgumentParser(description='Download STEP files from manufacturer websites')
    parser.add_argument('manufacturer', nargs='?', help='Manufacturer name (e.g., "TI", "Diodes Inc")')
    parser.add_argument('part_number', nargs='?', help='Part number (e.g., "LM358N")')
    parser.add_argument('--gui', action='store_true', help='Launch GUI mode')

    args = parser.parse_args()

    if args.gui or (not args.manufacturer and not args.part_number):
        # Launch GUI mode
        launch_gui()
    else:
        # Command line mode
        downloader = ManufacturerStepDownloader()
        result = downloader.download_step_file(args.manufacturer, args.part_number)

        if result:
            print(f"\n🎉 Download complete!")
        else:
            print(f"\n😞 No files downloaded")

def launch_gui():
    """Launch simple GUI for STEP file downloading"""
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox, filedialog
        import threading
    except ImportError:
        print("❌ GUI requires tkinter. Install with: pip install tk")
        return

    class StepDownloaderGUI:
        def __init__(self, root):
            self.root = root
            self.root.title("Manufacturer STEP File Downloader")
            self.root.geometry("600x500")

            self.downloader = ManufacturerStepDownloader()
            self.create_widgets()

        def create_widgets(self):
            # Title
            title_label = tk.Label(self.root, text="🔧 STEP File Downloader",
                                 font=("Arial", 16, "bold"))
            title_label.pack(pady=10)

            # Input frame
            input_frame = ttk.Frame(self.root)
            input_frame.pack(pady=10, padx=20, fill="x")

            # Manufacturer input
            ttk.Label(input_frame, text="Manufacturer:").grid(row=0, column=0, sticky="w", pady=5)
            self.manufacturer_var = tk.StringVar()
            manufacturer_entry = ttk.Entry(input_frame, textvariable=self.manufacturer_var, width=30)
            manufacturer_entry.grid(row=0, column=1, pady=5, padx=(10, 0), sticky="ew")

            # Part number input
            ttk.Label(input_frame, text="Part Number:").grid(row=1, column=0, sticky="w", pady=5)
            self.part_number_var = tk.StringVar()
            part_entry = ttk.Entry(input_frame, textvariable=self.part_number_var, width=30)
            part_entry.grid(row=1, column=1, pady=5, padx=(10, 0), sticky="ew")

            input_frame.columnconfigure(1, weight=1)

            # Buttons frame
            button_frame = ttk.Frame(self.root)
            button_frame.pack(pady=10)

            # Download button
            self.download_btn = ttk.Button(button_frame, text="🔍 Download STEP File",
                                         command=self.start_download)
            self.download_btn.pack(side="left", padx=5)

            # Open folder button
            open_folder_btn = ttk.Button(button_frame, text="📁 Open Downloads",
                                       command=self.open_downloads_folder)
            open_folder_btn.pack(side="left", padx=5)

            # Progress bar
            self.progress = ttk.Progressbar(self.root, mode='indeterminate')
            self.progress.pack(pady=10, padx=20, fill="x")

            # Output text area
            output_frame = ttk.Frame(self.root)
            output_frame.pack(pady=10, padx=20, fill="both", expand=True)

            self.output_text = tk.Text(output_frame, height=15, wrap="word")
            scrollbar = ttk.Scrollbar(output_frame, orient="vertical", command=self.output_text.yview)
            self.output_text.configure(yscrollcommand=scrollbar.set)

            self.output_text.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Example text
            self.output_text.insert("1.0", "📋 EXAMPLES:\n")
            self.output_text.insert("end", "• Manufacturer: TI, Part: LM358N\n")
            self.output_text.insert("end", "• Manufacturer: Diodes Inc, Part: APX803L20-30SA-7\n")
            self.output_text.insert("end", "• Manufacturer: STMicroelectronics, Part: STM32F103C8T6\n\n")
            self.output_text.insert("end", "Ready to download STEP files! 🚀\n")

        def start_download(self):
            manufacturer = self.manufacturer_var.get().strip()
            part_number = self.part_number_var.get().strip()

            if not manufacturer or not part_number:
                messagebox.showerror("Error", "Please enter both manufacturer and part number")
                return

            # Start download in separate thread
            self.download_btn.config(state="disabled")
            self.progress.start()

            thread = threading.Thread(target=self.download_worker, args=(manufacturer, part_number))
            thread.daemon = True
            thread.start()

        def download_worker(self, manufacturer, part_number):
            try:
                self.log_message(f"\n🎯 Starting download for {manufacturer} {part_number}...")
                result = self.downloader.download_step_file(manufacturer, part_number)

                if result:
                    self.log_message(f"✅ SUCCESS! Downloaded {len(result)} file(s):")
                    for filename in result:
                        self.log_message(f"   📁 {filename}")
                    messagebox.showinfo("Success", f"Downloaded {len(result)} STEP file(s)!")
                else:
                    self.log_message("❌ No STEP files found")
                    messagebox.showwarning("No Files", "No STEP files found for this component")

            except Exception as e:
                self.log_message(f"❌ Error: {e}")
                messagebox.showerror("Error", f"Download failed: {e}")
            finally:
                self.root.after(0, self.download_complete)

        def download_complete(self):
            self.progress.stop()
            self.download_btn.config(state="normal")

        def log_message(self, message):
            def update_text():
                self.output_text.insert("end", message + "\n")
                self.output_text.see("end")
            self.root.after(0, update_text)

        def open_downloads_folder(self):
            import subprocess
            import platform

            folder_path = os.path.abspath('step_downloads')

            if platform.system() == "Windows":
                subprocess.run(f'explorer "{folder_path}"', shell=True)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(f'open "{folder_path}"', shell=True)
            else:  # Linux
                subprocess.run(f'xdg-open "{folder_path}"', shell=True)

    root = tk.Tk()
    app = StepDownloaderGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
