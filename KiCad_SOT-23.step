ISO-10303-21;
HEADER;
/* SOT-23.step 3D STEP model for use in ECAD systems
 * Copyright (C) 2017, kicad StepUp
 * 
 * This work is licensed under the [Creative Commons CC-BY-SA 4.0 License](https://creativecommons.org/licenses/by-sa/4.0/legalcode), 
 * with the following exception:
 * To the extent that the creation of electronic designs that use 'Licensed Material' can be considered to be 'Adapted Material', 
 * then the copyright holder waives article 3 of the license with respect to these designs and any generated files which use data provided 
 * as part of the 'Licensed Material'.
 * You are free to use the library data in your own projects without the obligation to share your project files under this or any other license agreement.
 * However, if you wish to redistribute these libraries, or parts thereof (including in modified form) as a collection then the exception above does not apply. 
 * Please refer to https://github.com/KiCad/kicad-packages3D/blob/master/LICENSE.md for further clarification of the exception.
 * Disclaimer of Warranties and Limitation of Liability.
 * These libraries are provided in the hope that they will be useful, but are provided without warranty of any kind, express or implied.
 * *USE 3D CAD DATA AT YOUR OWN RISK*
 * *DO NOT RELY UPON ANY INFORMATION FOUND HERE WITHOUT INDEPENDENT VERIFICATION.*
 * 
 */

FILE_DESCRIPTION(
/* description */ ('model of SOT-23'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'SOT-23.step',
/* time_stamp */ '2017-12-18T01:05:53',
/* author */ ('kicad StepUp','ksu'),
/* organization */ ('FreeCAD'),
/* preprocessor_version */ 'OCC',
/* originating_system */ 'kicad StepUp',
/* authorisation */ '');

FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;

DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('SOT_23','SOT_23','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#2089);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#55,#118,#141,#170,#193,#232,#255,#278,#301,
#350,#373,#402,#425,#449,#479,#512,#535,#558,#581,#605,#629,#662,
#695,#726,#749,#778,#801,#825,#900,#975,#1005,#1022,#1045,#1068,
#1091,#1115,#1190,#1265,#1289,#1364,#1381,#1456,#1473,#1504,#1527,
#1583,#1599,#1622,#1639,#1656,#1673,#1690,#1707,#1724,#1741,#1753,
#1769,#1785,#1801,#1827,#1844,#1861,#1878,#1895,#1912,#1929,#1946,
#1958,#1980,#1997,#2014,#2031,#2048,#2065,#2077));
#17 = ADVANCED_FACE('',(#18),#50,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#30,#37,#45));
#20 = ORIENTED_EDGE('',*,*,#21,.T.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(-0.571621551758,1.433243103516,0.1));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(0.571621551758,1.433243103516,0.1));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(-0.571621551758,1.433243103516,0.1));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(1.,2.22044604925E-16,0.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#24,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(0.605,1.5,0.575));
#34 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#35,#36),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#35 = CARTESIAN_POINT('',(0.571621551758,1.433243103516,0.1));
#36 = CARTESIAN_POINT('',(0.605,1.5,0.575));
#37 = ORIENTED_EDGE('',*,*,#38,.F.);
#38 = EDGE_CURVE('',#39,#32,#41,.T.);
#39 = VERTEX_POINT('',#40);
#40 = CARTESIAN_POINT('',(-0.605,1.5,0.575));
#41 = LINE('',#42,#43);
#42 = CARTESIAN_POINT('',(-0.605,1.5,0.575));
#43 = VECTOR('',#44,1.);
#44 = DIRECTION('',(1.,2.22044604925E-16,0.));
#45 = ORIENTED_EDGE('',*,*,#46,.F.);
#46 = EDGE_CURVE('',#22,#39,#47,.T.);
#47 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#48,#49),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#48 = CARTESIAN_POINT('',(-0.571621551758,1.433243103516,0.1));
#49 = CARTESIAN_POINT('',(-0.605,1.5,0.575));
#50 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#51,#52)
,(#53,#54)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,1.21),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#51 = CARTESIAN_POINT('',(-0.571621551758,1.433243103516,0.1));
#52 = CARTESIAN_POINT('',(-0.605,1.5,0.575));
#53 = CARTESIAN_POINT('',(0.571621551758,1.433243103516,0.1));
#54 = CARTESIAN_POINT('',(0.605,1.5,0.575));
#55 = ADVANCED_FACE('',(#56),#113,.T.);
#56 = FACE_BOUND('',#57,.T.);
#57 = EDGE_LOOP('',(#58,#59,#67,#75,#83,#91,#99,#107));
#58 = ORIENTED_EDGE('',*,*,#21,.T.);
#59 = ORIENTED_EDGE('',*,*,#60,.T.);
#60 = EDGE_CURVE('',#24,#61,#63,.T.);
#61 = VERTEX_POINT('',#62);
#62 = CARTESIAN_POINT('',(0.633243103516,1.371621551758,0.1));
#63 = LINE('',#64,#65);
#64 = CARTESIAN_POINT('',(0.571621551758,1.433243103516,0.1));
#65 = VECTOR('',#66,1.);
#66 = DIRECTION('',(0.707106781187,-0.707106781187,0.));
#67 = ORIENTED_EDGE('',*,*,#68,.T.);
#68 = EDGE_CURVE('',#61,#69,#71,.T.);
#69 = VERTEX_POINT('',#70);
#70 = CARTESIAN_POINT('',(0.633243103516,-1.371621551758,0.1));
#71 = LINE('',#72,#73);
#72 = CARTESIAN_POINT('',(0.633243103516,1.371621551758,0.1));
#73 = VECTOR('',#74,1.);
#74 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#75 = ORIENTED_EDGE('',*,*,#76,.T.);
#76 = EDGE_CURVE('',#69,#77,#79,.T.);
#77 = VERTEX_POINT('',#78);
#78 = CARTESIAN_POINT('',(0.571621551758,-1.433243103516,0.1));
#79 = LINE('',#80,#81);
#80 = CARTESIAN_POINT('',(0.633243103516,-1.371621551758,0.1));
#81 = VECTOR('',#82,1.);
#82 = DIRECTION('',(-0.707106781187,-0.707106781187,0.));
#83 = ORIENTED_EDGE('',*,*,#84,.T.);
#84 = EDGE_CURVE('',#77,#85,#87,.T.);
#85 = VERTEX_POINT('',#86);
#86 = CARTESIAN_POINT('',(-0.571621551758,-1.433243103516,0.1));
#87 = LINE('',#88,#89);
#88 = CARTESIAN_POINT('',(0.571621551758,-1.433243103516,0.1));
#89 = VECTOR('',#90,1.);
#90 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#91 = ORIENTED_EDGE('',*,*,#92,.T.);
#92 = EDGE_CURVE('',#85,#93,#95,.T.);
#93 = VERTEX_POINT('',#94);
#94 = CARTESIAN_POINT('',(-0.633243103516,-1.371621551758,0.1));
#95 = LINE('',#96,#97);
#96 = CARTESIAN_POINT('',(-0.571621551758,-1.433243103516,0.1));
#97 = VECTOR('',#98,1.);
#98 = DIRECTION('',(-0.707106781187,0.707106781187,0.));
#99 = ORIENTED_EDGE('',*,*,#100,.T.);
#100 = EDGE_CURVE('',#93,#101,#103,.T.);
#101 = VERTEX_POINT('',#102);
#102 = CARTESIAN_POINT('',(-0.633243103516,1.371621551758,0.1));
#103 = LINE('',#104,#105);
#104 = CARTESIAN_POINT('',(-0.633243103516,-1.371621551758,0.1));
#105 = VECTOR('',#106,1.);
#106 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#107 = ORIENTED_EDGE('',*,*,#108,.T.);
#108 = EDGE_CURVE('',#101,#22,#109,.T.);
#109 = LINE('',#110,#111);
#110 = CARTESIAN_POINT('',(-0.633243103516,1.371621551758,0.1));
#111 = VECTOR('',#112,1.);
#112 = DIRECTION('',(0.707106781187,0.707106781187,0.));
#113 = PLANE('',#114);
#114 = AXIS2_PLACEMENT_3D('',#115,#116,#117);
#115 = CARTESIAN_POINT('',(-0.571621551758,1.433243103516,0.1));
#116 = DIRECTION('',(0.,0.,-1.));
#117 = DIRECTION('',(0.370454302632,-0.928850692879,0.));
#118 = ADVANCED_FACE('',(#119),#136,.F.);
#119 = FACE_BOUND('',#120,.F.);
#120 = EDGE_LOOP('',(#121,#122,#129,#135));
#121 = ORIENTED_EDGE('',*,*,#60,.T.);
#122 = ORIENTED_EDGE('',*,*,#123,.T.);
#123 = EDGE_CURVE('',#61,#124,#126,.T.);
#124 = VERTEX_POINT('',#125);
#125 = CARTESIAN_POINT('',(0.7,1.405,0.575));
#126 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#127,#128),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#127 = CARTESIAN_POINT('',(0.633243103516,1.371621551758,0.1));
#128 = CARTESIAN_POINT('',(0.7,1.405,0.575));
#129 = ORIENTED_EDGE('',*,*,#130,.F.);
#130 = EDGE_CURVE('',#32,#124,#131,.T.);
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(0.605,1.5,0.575));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(0.707106781187,-0.707106781187,0.));
#135 = ORIENTED_EDGE('',*,*,#31,.F.);
#136 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#137,#138)
,(#139,#140
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.134350288425),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#137 = CARTESIAN_POINT('',(0.571621551758,1.433243103516,0.1));
#138 = CARTESIAN_POINT('',(0.605,1.5,0.575));
#139 = CARTESIAN_POINT('',(0.633243103516,1.371621551758,0.1));
#140 = CARTESIAN_POINT('',(0.7,1.405,0.575));
#141 = ADVANCED_FACE('',(#142),#165,.F.);
#142 = FACE_BOUND('',#143,.F.);
#143 = EDGE_LOOP('',(#144,#145,#152,#160));
#144 = ORIENTED_EDGE('',*,*,#38,.T.);
#145 = ORIENTED_EDGE('',*,*,#146,.T.);
#146 = EDGE_CURVE('',#32,#147,#149,.T.);
#147 = VERTEX_POINT('',#148);
#148 = CARTESIAN_POINT('',(0.605,1.5,0.725));
#149 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#150,#151),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#150 = CARTESIAN_POINT('',(0.605,1.5,0.575));
#151 = CARTESIAN_POINT('',(0.605,1.5,0.725));
#152 = ORIENTED_EDGE('',*,*,#153,.F.);
#153 = EDGE_CURVE('',#154,#147,#156,.T.);
#154 = VERTEX_POINT('',#155);
#155 = CARTESIAN_POINT('',(-0.605,1.5,0.725));
#156 = LINE('',#157,#158);
#157 = CARTESIAN_POINT('',(-0.605,1.5,0.725));
#158 = VECTOR('',#159,1.);
#159 = DIRECTION('',(1.,2.22044604925E-16,0.));
#160 = ORIENTED_EDGE('',*,*,#161,.F.);
#161 = EDGE_CURVE('',#39,#154,#162,.T.);
#162 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#163,#164),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#163 = CARTESIAN_POINT('',(-0.605,1.5,0.575));
#164 = CARTESIAN_POINT('',(-0.605,1.5,0.725));
#165 = PLANE('',#166);
#166 = AXIS2_PLACEMENT_3D('',#167,#168,#169);
#167 = CARTESIAN_POINT('',(-0.605,1.5,0.725));
#168 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#169 = DIRECTION('',(1.,2.22044604925E-16,0.));
#170 = ADVANCED_FACE('',(#171),#188,.F.);
#171 = FACE_BOUND('',#172,.F.);
#172 = EDGE_LOOP('',(#173,#174,#175,#183));
#173 = ORIENTED_EDGE('',*,*,#108,.T.);
#174 = ORIENTED_EDGE('',*,*,#46,.T.);
#175 = ORIENTED_EDGE('',*,*,#176,.F.);
#176 = EDGE_CURVE('',#177,#39,#179,.T.);
#177 = VERTEX_POINT('',#178);
#178 = CARTESIAN_POINT('',(-0.7,1.405,0.575));
#179 = LINE('',#180,#181);
#180 = CARTESIAN_POINT('',(-0.7,1.405,0.575));
#181 = VECTOR('',#182,1.);
#182 = DIRECTION('',(0.707106781187,0.707106781187,0.));
#183 = ORIENTED_EDGE('',*,*,#184,.F.);
#184 = EDGE_CURVE('',#101,#177,#185,.T.);
#185 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#186,#187),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#186 = CARTESIAN_POINT('',(-0.633243103516,1.371621551758,0.1));
#187 = CARTESIAN_POINT('',(-0.7,1.405,0.575));
#188 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#189,#190)
,(#191,#192
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.134350288425),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#189 = CARTESIAN_POINT('',(-0.633243103516,1.371621551758,0.1));
#190 = CARTESIAN_POINT('',(-0.7,1.405,0.575));
#191 = CARTESIAN_POINT('',(-0.571621551758,1.433243103516,0.1));
#192 = CARTESIAN_POINT('',(-0.605,1.5,0.575));
#193 = ADVANCED_FACE('',(#194),#227,.F.);
#194 = FACE_BOUND('',#195,.F.);
#195 = EDGE_LOOP('',(#196,#197,#204,#212,#220,#226));
#196 = ORIENTED_EDGE('',*,*,#68,.T.);
#197 = ORIENTED_EDGE('',*,*,#198,.T.);
#198 = EDGE_CURVE('',#69,#199,#201,.T.);
#199 = VERTEX_POINT('',#200);
#200 = CARTESIAN_POINT('',(0.7,-1.405,0.575));
#201 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#202,#203),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#202 = CARTESIAN_POINT('',(0.633243103516,-1.371621551758,0.1));
#203 = CARTESIAN_POINT('',(0.7,-1.405,0.575));
#204 = ORIENTED_EDGE('',*,*,#205,.F.);
#205 = EDGE_CURVE('',#206,#199,#208,.T.);
#206 = VERTEX_POINT('',#207);
#207 = CARTESIAN_POINT('',(0.7,-0.2,0.575));
#208 = LINE('',#209,#210);
#209 = CARTESIAN_POINT('',(0.7,1.405,0.575));
#210 = VECTOR('',#211,1.);
#211 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#212 = ORIENTED_EDGE('',*,*,#213,.F.);
#213 = EDGE_CURVE('',#214,#206,#216,.T.);
#214 = VERTEX_POINT('',#215);
#215 = CARTESIAN_POINT('',(0.7,0.2,0.575));
#216 = LINE('',#217,#218);
#217 = CARTESIAN_POINT('',(0.7,0.2,0.575));
#218 = VECTOR('',#219,1.);
#219 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#220 = ORIENTED_EDGE('',*,*,#221,.F.);
#221 = EDGE_CURVE('',#124,#214,#222,.T.);
#222 = LINE('',#223,#224);
#223 = CARTESIAN_POINT('',(0.7,1.405,0.575));
#224 = VECTOR('',#225,1.);
#225 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#226 = ORIENTED_EDGE('',*,*,#123,.F.);
#227 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#228,#229)
,(#230,#231
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.81),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#228 = CARTESIAN_POINT('',(0.633243103516,1.371621551758,0.1));
#229 = CARTESIAN_POINT('',(0.7,1.405,0.575));
#230 = CARTESIAN_POINT('',(0.633243103516,-1.371621551758,0.1));
#231 = CARTESIAN_POINT('',(0.7,-1.405,0.575));
#232 = ADVANCED_FACE('',(#233),#250,.F.);
#233 = FACE_BOUND('',#234,.F.);
#234 = EDGE_LOOP('',(#235,#236,#243,#249));
#235 = ORIENTED_EDGE('',*,*,#76,.T.);
#236 = ORIENTED_EDGE('',*,*,#237,.T.);
#237 = EDGE_CURVE('',#77,#238,#240,.T.);
#238 = VERTEX_POINT('',#239);
#239 = CARTESIAN_POINT('',(0.605,-1.5,0.575));
#240 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#241,#242),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#241 = CARTESIAN_POINT('',(0.571621551758,-1.433243103516,0.1));
#242 = CARTESIAN_POINT('',(0.605,-1.5,0.575));
#243 = ORIENTED_EDGE('',*,*,#244,.F.);
#244 = EDGE_CURVE('',#199,#238,#245,.T.);
#245 = LINE('',#246,#247);
#246 = CARTESIAN_POINT('',(0.7,-1.405,0.575));
#247 = VECTOR('',#248,1.);
#248 = DIRECTION('',(-0.707106781187,-0.707106781187,0.));
#249 = ORIENTED_EDGE('',*,*,#198,.F.);
#250 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#251,#252)
,(#253,#254
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.134350288425),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#251 = CARTESIAN_POINT('',(0.633243103516,-1.371621551758,0.1));
#252 = CARTESIAN_POINT('',(0.7,-1.405,0.575));
#253 = CARTESIAN_POINT('',(0.571621551758,-1.433243103516,0.1));
#254 = CARTESIAN_POINT('',(0.605,-1.5,0.575));
#255 = ADVANCED_FACE('',(#256),#273,.F.);
#256 = FACE_BOUND('',#257,.F.);
#257 = EDGE_LOOP('',(#258,#259,#266,#272));
#258 = ORIENTED_EDGE('',*,*,#84,.T.);
#259 = ORIENTED_EDGE('',*,*,#260,.T.);
#260 = EDGE_CURVE('',#85,#261,#263,.T.);
#261 = VERTEX_POINT('',#262);
#262 = CARTESIAN_POINT('',(-0.605,-1.5,0.575));
#263 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#264,#265),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#264 = CARTESIAN_POINT('',(-0.571621551758,-1.433243103516,0.1));
#265 = CARTESIAN_POINT('',(-0.605,-1.5,0.575));
#266 = ORIENTED_EDGE('',*,*,#267,.F.);
#267 = EDGE_CURVE('',#238,#261,#268,.T.);
#268 = LINE('',#269,#270);
#269 = CARTESIAN_POINT('',(0.605,-1.5,0.575));
#270 = VECTOR('',#271,1.);
#271 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#272 = ORIENTED_EDGE('',*,*,#237,.F.);
#273 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#274,#275)
,(#276,#277
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,1.21),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#274 = CARTESIAN_POINT('',(0.571621551758,-1.433243103516,0.1));
#275 = CARTESIAN_POINT('',(0.605,-1.5,0.575));
#276 = CARTESIAN_POINT('',(-0.571621551758,-1.433243103516,0.1));
#277 = CARTESIAN_POINT('',(-0.605,-1.5,0.575));
#278 = ADVANCED_FACE('',(#279),#296,.F.);
#279 = FACE_BOUND('',#280,.F.);
#280 = EDGE_LOOP('',(#281,#282,#289,#295));
#281 = ORIENTED_EDGE('',*,*,#92,.T.);
#282 = ORIENTED_EDGE('',*,*,#283,.T.);
#283 = EDGE_CURVE('',#93,#284,#286,.T.);
#284 = VERTEX_POINT('',#285);
#285 = CARTESIAN_POINT('',(-0.7,-1.405,0.575));
#286 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#287,#288),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#287 = CARTESIAN_POINT('',(-0.633243103516,-1.371621551758,0.1));
#288 = CARTESIAN_POINT('',(-0.7,-1.405,0.575));
#289 = ORIENTED_EDGE('',*,*,#290,.F.);
#290 = EDGE_CURVE('',#261,#284,#291,.T.);
#291 = LINE('',#292,#293);
#292 = CARTESIAN_POINT('',(-0.605,-1.5,0.575));
#293 = VECTOR('',#294,1.);
#294 = DIRECTION('',(-0.707106781187,0.707106781187,0.));
#295 = ORIENTED_EDGE('',*,*,#260,.F.);
#296 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#297,#298)
,(#299,#300
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.134350288425),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#297 = CARTESIAN_POINT('',(-0.571621551758,-1.433243103516,0.1));
#298 = CARTESIAN_POINT('',(-0.605,-1.5,0.575));
#299 = CARTESIAN_POINT('',(-0.633243103516,-1.371621551758,0.1));
#300 = CARTESIAN_POINT('',(-0.7,-1.405,0.575));
#301 = ADVANCED_FACE('',(#302),#345,.F.);
#302 = FACE_BOUND('',#303,.F.);
#303 = EDGE_LOOP('',(#304,#305,#306,#314,#322,#330,#338,#344));
#304 = ORIENTED_EDGE('',*,*,#100,.T.);
#305 = ORIENTED_EDGE('',*,*,#184,.T.);
#306 = ORIENTED_EDGE('',*,*,#307,.F.);
#307 = EDGE_CURVE('',#308,#177,#310,.T.);
#308 = VERTEX_POINT('',#309);
#309 = CARTESIAN_POINT('',(-0.7,1.15,0.575));
#310 = LINE('',#311,#312);
#311 = CARTESIAN_POINT('',(-0.7,-1.405,0.575));
#312 = VECTOR('',#313,1.);
#313 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#314 = ORIENTED_EDGE('',*,*,#315,.F.);
#315 = EDGE_CURVE('',#316,#308,#318,.T.);
#316 = VERTEX_POINT('',#317);
#317 = CARTESIAN_POINT('',(-0.7,0.75,0.575));
#318 = LINE('',#319,#320);
#319 = CARTESIAN_POINT('',(-0.7,0.75,0.575));
#320 = VECTOR('',#321,1.);
#321 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#322 = ORIENTED_EDGE('',*,*,#323,.F.);
#323 = EDGE_CURVE('',#324,#316,#326,.T.);
#324 = VERTEX_POINT('',#325);
#325 = CARTESIAN_POINT('',(-0.7,-0.75,0.575));
#326 = LINE('',#327,#328);
#327 = CARTESIAN_POINT('',(-0.7,-1.405,0.575));
#328 = VECTOR('',#329,1.);
#329 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#330 = ORIENTED_EDGE('',*,*,#331,.F.);
#331 = EDGE_CURVE('',#332,#324,#334,.T.);
#332 = VERTEX_POINT('',#333);
#333 = CARTESIAN_POINT('',(-0.7,-1.15,0.575));
#334 = LINE('',#335,#336);
#335 = CARTESIAN_POINT('',(-0.7,-1.15,0.575));
#336 = VECTOR('',#337,1.);
#337 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#338 = ORIENTED_EDGE('',*,*,#339,.F.);
#339 = EDGE_CURVE('',#284,#332,#340,.T.);
#340 = LINE('',#341,#342);
#341 = CARTESIAN_POINT('',(-0.7,-1.405,0.575));
#342 = VECTOR('',#343,1.);
#343 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#344 = ORIENTED_EDGE('',*,*,#283,.F.);
#345 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#346,#347)
,(#348,#349
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.81),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#346 = CARTESIAN_POINT('',(-0.633243103516,-1.371621551758,0.1));
#347 = CARTESIAN_POINT('',(-0.7,-1.405,0.575));
#348 = CARTESIAN_POINT('',(-0.633243103516,1.371621551758,0.1));
#349 = CARTESIAN_POINT('',(-0.7,1.405,0.575));
#350 = ADVANCED_FACE('',(#351),#368,.F.);
#351 = FACE_BOUND('',#352,.F.);
#352 = EDGE_LOOP('',(#353,#354,#361,#367));
#353 = ORIENTED_EDGE('',*,*,#130,.T.);
#354 = ORIENTED_EDGE('',*,*,#355,.T.);
#355 = EDGE_CURVE('',#124,#356,#358,.T.);
#356 = VERTEX_POINT('',#357);
#357 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#358 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#359,#360),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#359 = CARTESIAN_POINT('',(0.7,1.405,0.575));
#360 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#361 = ORIENTED_EDGE('',*,*,#362,.F.);
#362 = EDGE_CURVE('',#147,#356,#363,.T.);
#363 = LINE('',#364,#365);
#364 = CARTESIAN_POINT('',(0.605,1.5,0.725));
#365 = VECTOR('',#366,1.);
#366 = DIRECTION('',(0.707106781187,-0.707106781187,0.));
#367 = ORIENTED_EDGE('',*,*,#146,.F.);
#368 = PLANE('',#369);
#369 = AXIS2_PLACEMENT_3D('',#370,#371,#372);
#370 = CARTESIAN_POINT('',(0.605,1.5,0.725));
#371 = DIRECTION('',(-0.707106781187,-0.707106781187,0.));
#372 = DIRECTION('',(0.707106781187,-0.707106781187,0.));
#373 = ADVANCED_FACE('',(#374),#397,.F.);
#374 = FACE_BOUND('',#375,.F.);
#375 = EDGE_LOOP('',(#376,#377,#384,#392));
#376 = ORIENTED_EDGE('',*,*,#153,.T.);
#377 = ORIENTED_EDGE('',*,*,#378,.T.);
#378 = EDGE_CURVE('',#147,#379,#381,.T.);
#379 = VERTEX_POINT('',#380);
#380 = CARTESIAN_POINT('',(0.5925,1.475,0.725));
#381 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#382,#383),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#382 = CARTESIAN_POINT('',(0.605,1.5,0.725));
#383 = CARTESIAN_POINT('',(0.5925,1.475,0.725));
#384 = ORIENTED_EDGE('',*,*,#385,.F.);
#385 = EDGE_CURVE('',#386,#379,#388,.T.);
#386 = VERTEX_POINT('',#387);
#387 = CARTESIAN_POINT('',(-0.5925,1.475,0.725));
#388 = LINE('',#389,#390);
#389 = CARTESIAN_POINT('',(-0.5925,1.475,0.725));
#390 = VECTOR('',#391,1.);
#391 = DIRECTION('',(1.,2.22044604925E-16,0.));
#392 = ORIENTED_EDGE('',*,*,#393,.F.);
#393 = EDGE_CURVE('',#154,#386,#394,.T.);
#394 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#395,#396),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#395 = CARTESIAN_POINT('',(-0.605,1.5,0.725));
#396 = CARTESIAN_POINT('',(-0.5925,1.475,0.725));
#397 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#398,#399)
,(#400,#401
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,1.21),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#398 = CARTESIAN_POINT('',(-0.605,1.5,0.725));
#399 = CARTESIAN_POINT('',(-0.5925,1.475,0.725));
#400 = CARTESIAN_POINT('',(0.605,1.5,0.725));
#401 = CARTESIAN_POINT('',(0.5925,1.475,0.725));
#402 = ADVANCED_FACE('',(#403),#420,.F.);
#403 = FACE_BOUND('',#404,.F.);
#404 = EDGE_LOOP('',(#405,#406,#407,#415));
#405 = ORIENTED_EDGE('',*,*,#176,.T.);
#406 = ORIENTED_EDGE('',*,*,#161,.T.);
#407 = ORIENTED_EDGE('',*,*,#408,.F.);
#408 = EDGE_CURVE('',#409,#154,#411,.T.);
#409 = VERTEX_POINT('',#410);
#410 = CARTESIAN_POINT('',(-0.7,1.405,0.725));
#411 = LINE('',#412,#413);
#412 = CARTESIAN_POINT('',(-0.7,1.405,0.725));
#413 = VECTOR('',#414,1.);
#414 = DIRECTION('',(0.707106781187,0.707106781187,0.));
#415 = ORIENTED_EDGE('',*,*,#416,.F.);
#416 = EDGE_CURVE('',#177,#409,#417,.T.);
#417 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#418,#419),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#418 = CARTESIAN_POINT('',(-0.7,1.405,0.575));
#419 = CARTESIAN_POINT('',(-0.7,1.405,0.725));
#420 = PLANE('',#421);
#421 = AXIS2_PLACEMENT_3D('',#422,#423,#424);
#422 = CARTESIAN_POINT('',(-0.7,1.405,0.725));
#423 = DIRECTION('',(0.707106781187,-0.707106781187,0.));
#424 = DIRECTION('',(0.707106781187,0.707106781187,0.));
#425 = ADVANCED_FACE('',(#426),#444,.F.);
#426 = FACE_BOUND('',#427,.F.);
#427 = EDGE_LOOP('',(#428,#429,#430,#438));
#428 = ORIENTED_EDGE('',*,*,#355,.F.);
#429 = ORIENTED_EDGE('',*,*,#221,.T.);
#430 = ORIENTED_EDGE('',*,*,#431,.T.);
#431 = EDGE_CURVE('',#214,#432,#434,.T.);
#432 = VERTEX_POINT('',#433);
#433 = CARTESIAN_POINT('',(0.7,0.2,0.725));
#434 = LINE('',#435,#436);
#435 = CARTESIAN_POINT('',(0.7,0.2,0.525297170697));
#436 = VECTOR('',#437,1.);
#437 = DIRECTION('',(0.,0.,1.));
#438 = ORIENTED_EDGE('',*,*,#439,.F.);
#439 = EDGE_CURVE('',#356,#432,#440,.T.);
#440 = LINE('',#441,#442);
#441 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#442 = VECTOR('',#443,1.);
#443 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#444 = PLANE('',#445);
#445 = AXIS2_PLACEMENT_3D('',#446,#447,#448);
#446 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#447 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#448 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#449 = ADVANCED_FACE('',(#450),#474,.F.);
#450 = FACE_BOUND('',#451,.F.);
#451 = EDGE_LOOP('',(#452,#460,#461,#468));
#452 = ORIENTED_EDGE('',*,*,#453,.F.);
#453 = EDGE_CURVE('',#206,#454,#456,.T.);
#454 = VERTEX_POINT('',#455);
#455 = CARTESIAN_POINT('',(0.7,-0.2,0.725));
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(0.7,-0.2,0.525297170697));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(0.,0.,1.));
#460 = ORIENTED_EDGE('',*,*,#205,.T.);
#461 = ORIENTED_EDGE('',*,*,#462,.T.);
#462 = EDGE_CURVE('',#199,#463,#465,.T.);
#463 = VERTEX_POINT('',#464);
#464 = CARTESIAN_POINT('',(0.7,-1.405,0.725));
#465 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#466,#467),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#466 = CARTESIAN_POINT('',(0.7,-1.405,0.575));
#467 = CARTESIAN_POINT('',(0.7,-1.405,0.725));
#468 = ORIENTED_EDGE('',*,*,#469,.F.);
#469 = EDGE_CURVE('',#454,#463,#470,.T.);
#470 = LINE('',#471,#472);
#471 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#472 = VECTOR('',#473,1.);
#473 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#474 = PLANE('',#475);
#475 = AXIS2_PLACEMENT_3D('',#476,#477,#478);
#476 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#477 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#478 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#479 = ADVANCED_FACE('',(#480),#507,.F.);
#480 = FACE_BOUND('',#481,.F.);
#481 = EDGE_LOOP('',(#482,#483,#492,#500));
#482 = ORIENTED_EDGE('',*,*,#213,.T.);
#483 = ORIENTED_EDGE('',*,*,#484,.T.);
#484 = EDGE_CURVE('',#206,#485,#487,.T.);
#485 = VERTEX_POINT('',#486);
#486 = CARTESIAN_POINT('',(0.8,-0.2,0.475));
#487 = CIRCLE('',#488,0.1);
#488 = AXIS2_PLACEMENT_3D('',#489,#490,#491);
#489 = CARTESIAN_POINT('',(0.7,-0.2,0.475));
#490 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#491 = DIRECTION('',(1.110223024625E-15,2.465190328816E-31,1.));
#492 = ORIENTED_EDGE('',*,*,#493,.F.);
#493 = EDGE_CURVE('',#494,#485,#496,.T.);
#494 = VERTEX_POINT('',#495);
#495 = CARTESIAN_POINT('',(0.8,0.2,0.475));
#496 = LINE('',#497,#498);
#497 = CARTESIAN_POINT('',(0.8,0.2,0.475));
#498 = VECTOR('',#499,1.);
#499 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#500 = ORIENTED_EDGE('',*,*,#501,.F.);
#501 = EDGE_CURVE('',#214,#494,#502,.T.);
#502 = CIRCLE('',#503,0.1);
#503 = AXIS2_PLACEMENT_3D('',#504,#505,#506);
#504 = CARTESIAN_POINT('',(0.7,0.2,0.475));
#505 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#506 = DIRECTION('',(1.110223024625E-15,2.465190328816E-31,1.));
#507 = CYLINDRICAL_SURFACE('',#508,0.1);
#508 = AXIS2_PLACEMENT_3D('',#509,#510,#511);
#509 = CARTESIAN_POINT('',(0.7,0.2,0.475));
#510 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#511 = DIRECTION('',(1.110223024625E-15,2.465190328816E-31,1.));
#512 = ADVANCED_FACE('',(#513),#530,.F.);
#513 = FACE_BOUND('',#514,.F.);
#514 = EDGE_LOOP('',(#515,#516,#523,#529));
#515 = ORIENTED_EDGE('',*,*,#244,.T.);
#516 = ORIENTED_EDGE('',*,*,#517,.T.);
#517 = EDGE_CURVE('',#238,#518,#520,.T.);
#518 = VERTEX_POINT('',#519);
#519 = CARTESIAN_POINT('',(0.605,-1.5,0.725));
#520 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#521,#522),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#521 = CARTESIAN_POINT('',(0.605,-1.5,0.575));
#522 = CARTESIAN_POINT('',(0.605,-1.5,0.725));
#523 = ORIENTED_EDGE('',*,*,#524,.F.);
#524 = EDGE_CURVE('',#463,#518,#525,.T.);
#525 = LINE('',#526,#527);
#526 = CARTESIAN_POINT('',(0.7,-1.405,0.725));
#527 = VECTOR('',#528,1.);
#528 = DIRECTION('',(-0.707106781187,-0.707106781187,0.));
#529 = ORIENTED_EDGE('',*,*,#462,.F.);
#530 = PLANE('',#531);
#531 = AXIS2_PLACEMENT_3D('',#532,#533,#534);
#532 = CARTESIAN_POINT('',(0.7,-1.405,0.725));
#533 = DIRECTION('',(-0.707106781187,0.707106781187,0.));
#534 = DIRECTION('',(-0.707106781187,-0.707106781187,0.));
#535 = ADVANCED_FACE('',(#536),#553,.F.);
#536 = FACE_BOUND('',#537,.F.);
#537 = EDGE_LOOP('',(#538,#539,#546,#552));
#538 = ORIENTED_EDGE('',*,*,#267,.T.);
#539 = ORIENTED_EDGE('',*,*,#540,.T.);
#540 = EDGE_CURVE('',#261,#541,#543,.T.);
#541 = VERTEX_POINT('',#542);
#542 = CARTESIAN_POINT('',(-0.605,-1.5,0.725));
#543 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#544,#545),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#544 = CARTESIAN_POINT('',(-0.605,-1.5,0.575));
#545 = CARTESIAN_POINT('',(-0.605,-1.5,0.725));
#546 = ORIENTED_EDGE('',*,*,#547,.F.);
#547 = EDGE_CURVE('',#518,#541,#548,.T.);
#548 = LINE('',#549,#550);
#549 = CARTESIAN_POINT('',(0.605,-1.5,0.725));
#550 = VECTOR('',#551,1.);
#551 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#552 = ORIENTED_EDGE('',*,*,#517,.F.);
#553 = PLANE('',#554);
#554 = AXIS2_PLACEMENT_3D('',#555,#556,#557);
#555 = CARTESIAN_POINT('',(0.605,-1.5,0.725));
#556 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#557 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#558 = ADVANCED_FACE('',(#559),#576,.F.);
#559 = FACE_BOUND('',#560,.F.);
#560 = EDGE_LOOP('',(#561,#562,#569,#575));
#561 = ORIENTED_EDGE('',*,*,#290,.T.);
#562 = ORIENTED_EDGE('',*,*,#563,.T.);
#563 = EDGE_CURVE('',#284,#564,#566,.T.);
#564 = VERTEX_POINT('',#565);
#565 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#566 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#567,#568),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#567 = CARTESIAN_POINT('',(-0.7,-1.405,0.575));
#568 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#569 = ORIENTED_EDGE('',*,*,#570,.F.);
#570 = EDGE_CURVE('',#541,#564,#571,.T.);
#571 = LINE('',#572,#573);
#572 = CARTESIAN_POINT('',(-0.605,-1.5,0.725));
#573 = VECTOR('',#574,1.);
#574 = DIRECTION('',(-0.707106781187,0.707106781187,0.));
#575 = ORIENTED_EDGE('',*,*,#540,.F.);
#576 = PLANE('',#577);
#577 = AXIS2_PLACEMENT_3D('',#578,#579,#580);
#578 = CARTESIAN_POINT('',(-0.605,-1.5,0.725));
#579 = DIRECTION('',(0.707106781187,0.707106781187,0.));
#580 = DIRECTION('',(-0.707106781187,0.707106781187,0.));
#581 = ADVANCED_FACE('',(#582),#600,.F.);
#582 = FACE_BOUND('',#583,.F.);
#583 = EDGE_LOOP('',(#584,#585,#586,#594));
#584 = ORIENTED_EDGE('',*,*,#563,.F.);
#585 = ORIENTED_EDGE('',*,*,#339,.T.);
#586 = ORIENTED_EDGE('',*,*,#587,.T.);
#587 = EDGE_CURVE('',#332,#588,#590,.T.);
#588 = VERTEX_POINT('',#589);
#589 = CARTESIAN_POINT('',(-0.7,-1.15,0.725));
#590 = LINE('',#591,#592);
#591 = CARTESIAN_POINT('',(-0.7,-1.15,0.525297170697));
#592 = VECTOR('',#593,1.);
#593 = DIRECTION('',(0.,0.,1.));
#594 = ORIENTED_EDGE('',*,*,#595,.F.);
#595 = EDGE_CURVE('',#564,#588,#596,.T.);
#596 = LINE('',#597,#598);
#597 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#598 = VECTOR('',#599,1.);
#599 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#600 = PLANE('',#601);
#601 = AXIS2_PLACEMENT_3D('',#602,#603,#604);
#602 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#603 = DIRECTION('',(1.,2.22044604925E-16,0.));
#604 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#605 = ADVANCED_FACE('',(#606),#624,.F.);
#606 = FACE_BOUND('',#607,.F.);
#607 = EDGE_LOOP('',(#608,#616,#617,#618));
#608 = ORIENTED_EDGE('',*,*,#609,.F.);
#609 = EDGE_CURVE('',#308,#610,#612,.T.);
#610 = VERTEX_POINT('',#611);
#611 = CARTESIAN_POINT('',(-0.7,1.15,0.725));
#612 = LINE('',#613,#614);
#613 = CARTESIAN_POINT('',(-0.7,1.15,0.525297170697));
#614 = VECTOR('',#615,1.);
#615 = DIRECTION('',(0.,0.,1.));
#616 = ORIENTED_EDGE('',*,*,#307,.T.);
#617 = ORIENTED_EDGE('',*,*,#416,.T.);
#618 = ORIENTED_EDGE('',*,*,#619,.F.);
#619 = EDGE_CURVE('',#610,#409,#620,.T.);
#620 = LINE('',#621,#622);
#621 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#622 = VECTOR('',#623,1.);
#623 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#624 = PLANE('',#625);
#625 = AXIS2_PLACEMENT_3D('',#626,#627,#628);
#626 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#627 = DIRECTION('',(1.,2.22044604925E-16,0.));
#628 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#629 = ADVANCED_FACE('',(#630),#657,.F.);
#630 = FACE_BOUND('',#631,.F.);
#631 = EDGE_LOOP('',(#632,#633,#642,#650));
#632 = ORIENTED_EDGE('',*,*,#331,.T.);
#633 = ORIENTED_EDGE('',*,*,#634,.T.);
#634 = EDGE_CURVE('',#324,#635,#637,.T.);
#635 = VERTEX_POINT('',#636);
#636 = CARTESIAN_POINT('',(-0.8,-0.75,0.475));
#637 = CIRCLE('',#638,0.1);
#638 = AXIS2_PLACEMENT_3D('',#639,#640,#641);
#639 = CARTESIAN_POINT('',(-0.7,-0.75,0.475));
#640 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#641 = DIRECTION('',(-1.110223024625E-15,-1.105559255369E-31,1.));
#642 = ORIENTED_EDGE('',*,*,#643,.F.);
#643 = EDGE_CURVE('',#644,#635,#646,.T.);
#644 = VERTEX_POINT('',#645);
#645 = CARTESIAN_POINT('',(-0.8,-1.15,0.475));
#646 = LINE('',#647,#648);
#647 = CARTESIAN_POINT('',(-0.8,-1.15,0.475));
#648 = VECTOR('',#649,1.);
#649 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#650 = ORIENTED_EDGE('',*,*,#651,.F.);
#651 = EDGE_CURVE('',#332,#644,#652,.T.);
#652 = CIRCLE('',#653,0.1);
#653 = AXIS2_PLACEMENT_3D('',#654,#655,#656);
#654 = CARTESIAN_POINT('',(-0.7,-1.15,0.475));
#655 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#656 = DIRECTION('',(-1.110223024625E-15,-1.105559255369E-31,1.));
#657 = CYLINDRICAL_SURFACE('',#658,0.1);
#658 = AXIS2_PLACEMENT_3D('',#659,#660,#661);
#659 = CARTESIAN_POINT('',(-0.7,-1.15,0.475));
#660 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#661 = DIRECTION('',(-1.110223024625E-15,-1.105559255369E-31,1.));
#662 = ADVANCED_FACE('',(#663),#690,.F.);
#663 = FACE_BOUND('',#664,.F.);
#664 = EDGE_LOOP('',(#665,#666,#675,#683));
#665 = ORIENTED_EDGE('',*,*,#315,.T.);
#666 = ORIENTED_EDGE('',*,*,#667,.T.);
#667 = EDGE_CURVE('',#308,#668,#670,.T.);
#668 = VERTEX_POINT('',#669);
#669 = CARTESIAN_POINT('',(-0.8,1.15,0.475));
#670 = CIRCLE('',#671,0.1);
#671 = AXIS2_PLACEMENT_3D('',#672,#673,#674);
#672 = CARTESIAN_POINT('',(-0.7,1.15,0.475));
#673 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#674 = DIRECTION('',(-1.110223024625E-15,-1.105559255369E-31,1.));
#675 = ORIENTED_EDGE('',*,*,#676,.F.);
#676 = EDGE_CURVE('',#677,#668,#679,.T.);
#677 = VERTEX_POINT('',#678);
#678 = CARTESIAN_POINT('',(-0.8,0.75,0.475));
#679 = LINE('',#680,#681);
#680 = CARTESIAN_POINT('',(-0.8,0.75,0.475));
#681 = VECTOR('',#682,1.);
#682 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#683 = ORIENTED_EDGE('',*,*,#684,.F.);
#684 = EDGE_CURVE('',#316,#677,#685,.T.);
#685 = CIRCLE('',#686,0.1);
#686 = AXIS2_PLACEMENT_3D('',#687,#688,#689);
#687 = CARTESIAN_POINT('',(-0.7,0.75,0.475));
#688 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#689 = DIRECTION('',(-1.110223024625E-15,-1.105559255369E-31,1.));
#690 = CYLINDRICAL_SURFACE('',#691,0.1);
#691 = AXIS2_PLACEMENT_3D('',#692,#693,#694);
#692 = CARTESIAN_POINT('',(-0.7,0.75,0.475));
#693 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#694 = DIRECTION('',(-1.110223024625E-15,-1.105559255369E-31,1.));
#695 = ADVANCED_FACE('',(#696),#721,.F.);
#696 = FACE_BOUND('',#697,.F.);
#697 = EDGE_LOOP('',(#698,#706,#707,#715));
#698 = ORIENTED_EDGE('',*,*,#699,.F.);
#699 = EDGE_CURVE('',#324,#700,#702,.T.);
#700 = VERTEX_POINT('',#701);
#701 = CARTESIAN_POINT('',(-0.7,-0.75,0.725));
#702 = LINE('',#703,#704);
#703 = CARTESIAN_POINT('',(-0.7,-0.75,0.525297170697));
#704 = VECTOR('',#705,1.);
#705 = DIRECTION('',(0.,0.,1.));
#706 = ORIENTED_EDGE('',*,*,#323,.T.);
#707 = ORIENTED_EDGE('',*,*,#708,.T.);
#708 = EDGE_CURVE('',#316,#709,#711,.T.);
#709 = VERTEX_POINT('',#710);
#710 = CARTESIAN_POINT('',(-0.7,0.75,0.725));
#711 = LINE('',#712,#713);
#712 = CARTESIAN_POINT('',(-0.7,0.75,0.525297170697));
#713 = VECTOR('',#714,1.);
#714 = DIRECTION('',(0.,0.,1.));
#715 = ORIENTED_EDGE('',*,*,#716,.F.);
#716 = EDGE_CURVE('',#700,#709,#717,.T.);
#717 = LINE('',#718,#719);
#718 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#719 = VECTOR('',#720,1.);
#720 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#721 = PLANE('',#722);
#722 = AXIS2_PLACEMENT_3D('',#723,#724,#725);
#723 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#724 = DIRECTION('',(1.,2.22044604925E-16,0.));
#725 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#726 = ADVANCED_FACE('',(#727),#744,.F.);
#727 = FACE_BOUND('',#728,.F.);
#728 = EDGE_LOOP('',(#729,#730,#737,#743));
#729 = ORIENTED_EDGE('',*,*,#362,.T.);
#730 = ORIENTED_EDGE('',*,*,#731,.T.);
#731 = EDGE_CURVE('',#356,#732,#734,.T.);
#732 = VERTEX_POINT('',#733);
#733 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#734 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#735,#736),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#735 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#736 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#737 = ORIENTED_EDGE('',*,*,#738,.F.);
#738 = EDGE_CURVE('',#379,#732,#739,.T.);
#739 = LINE('',#740,#741);
#740 = CARTESIAN_POINT('',(0.5925,1.475,0.725));
#741 = VECTOR('',#742,1.);
#742 = DIRECTION('',(0.707106781187,-0.707106781187,0.));
#743 = ORIENTED_EDGE('',*,*,#378,.F.);
#744 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#745,#746)
,(#747,#748
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.134350288425),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#745 = CARTESIAN_POINT('',(0.605,1.5,0.725));
#746 = CARTESIAN_POINT('',(0.5925,1.475,0.725));
#747 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#748 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#749 = ADVANCED_FACE('',(#750),#773,.F.);
#750 = FACE_BOUND('',#751,.F.);
#751 = EDGE_LOOP('',(#752,#753,#760,#768));
#752 = ORIENTED_EDGE('',*,*,#385,.T.);
#753 = ORIENTED_EDGE('',*,*,#754,.T.);
#754 = EDGE_CURVE('',#379,#755,#757,.T.);
#755 = VERTEX_POINT('',#756);
#756 = CARTESIAN_POINT('',(0.559121551758,1.408243103516,1.2));
#757 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#758,#759),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#758 = CARTESIAN_POINT('',(0.5925,1.475,0.725));
#759 = CARTESIAN_POINT('',(0.559121551758,1.408243103516,1.2));
#760 = ORIENTED_EDGE('',*,*,#761,.F.);
#761 = EDGE_CURVE('',#762,#755,#764,.T.);
#762 = VERTEX_POINT('',#763);
#763 = CARTESIAN_POINT('',(-0.559121551758,1.408243103516,1.2));
#764 = LINE('',#765,#766);
#765 = CARTESIAN_POINT('',(-0.559121551758,1.408243103516,1.2));
#766 = VECTOR('',#767,1.);
#767 = DIRECTION('',(1.,2.22044604925E-16,0.));
#768 = ORIENTED_EDGE('',*,*,#769,.F.);
#769 = EDGE_CURVE('',#386,#762,#770,.T.);
#770 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#771,#772),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#771 = CARTESIAN_POINT('',(-0.5925,1.475,0.725));
#772 = CARTESIAN_POINT('',(-0.559121551758,1.408243103516,1.2));
#773 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#774,#775)
,(#776,#777
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,1.185),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#774 = CARTESIAN_POINT('',(-0.5925,1.475,0.725));
#775 = CARTESIAN_POINT('',(-0.559121551758,1.408243103516,1.2));
#776 = CARTESIAN_POINT('',(0.5925,1.475,0.725));
#777 = CARTESIAN_POINT('',(0.559121551758,1.408243103516,1.2));
#778 = ADVANCED_FACE('',(#779),#796,.F.);
#779 = FACE_BOUND('',#780,.F.);
#780 = EDGE_LOOP('',(#781,#782,#783,#791));
#781 = ORIENTED_EDGE('',*,*,#408,.T.);
#782 = ORIENTED_EDGE('',*,*,#393,.T.);
#783 = ORIENTED_EDGE('',*,*,#784,.F.);
#784 = EDGE_CURVE('',#785,#386,#787,.T.);
#785 = VERTEX_POINT('',#786);
#786 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#787 = LINE('',#788,#789);
#788 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#789 = VECTOR('',#790,1.);
#790 = DIRECTION('',(0.707106781187,0.707106781187,0.));
#791 = ORIENTED_EDGE('',*,*,#792,.F.);
#792 = EDGE_CURVE('',#409,#785,#793,.T.);
#793 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#794,#795),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#794 = CARTESIAN_POINT('',(-0.7,1.405,0.725));
#795 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#796 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#797,#798)
,(#799,#800
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.134350288425),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#797 = CARTESIAN_POINT('',(-0.7,1.405,0.725));
#798 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#799 = CARTESIAN_POINT('',(-0.605,1.5,0.725));
#800 = CARTESIAN_POINT('',(-0.5925,1.475,0.725));
#801 = ADVANCED_FACE('',(#802),#820,.F.);
#802 = FACE_BOUND('',#803,.F.);
#803 = EDGE_LOOP('',(#804,#805,#806,#814));
#804 = ORIENTED_EDGE('',*,*,#731,.F.);
#805 = ORIENTED_EDGE('',*,*,#439,.T.);
#806 = ORIENTED_EDGE('',*,*,#807,.T.);
#807 = EDGE_CURVE('',#432,#808,#810,.T.);
#808 = VERTEX_POINT('',#809);
#809 = CARTESIAN_POINT('',(0.675,0.2,0.725));
#810 = LINE('',#811,#812);
#811 = CARTESIAN_POINT('',(0.7,0.2,0.725));
#812 = VECTOR('',#813,1.);
#813 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#814 = ORIENTED_EDGE('',*,*,#815,.F.);
#815 = EDGE_CURVE('',#732,#808,#816,.T.);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#820 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#821,#822)
,(#823,#824
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.81),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#821 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#822 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#823 = CARTESIAN_POINT('',(0.7,-1.405,0.725));
#824 = CARTESIAN_POINT('',(0.675,-1.3925,0.725));
#825 = ADVANCED_FACE('',(#826),#895,.F.);
#826 = FACE_BOUND('',#827,.F.);
#827 = EDGE_LOOP('',(#828,#829,#830,#838,#847,#855,#863,#871,#880,#888)
);
#828 = ORIENTED_EDGE('',*,*,#431,.F.);
#829 = ORIENTED_EDGE('',*,*,#501,.T.);
#830 = ORIENTED_EDGE('',*,*,#831,.T.);
#831 = EDGE_CURVE('',#494,#832,#834,.T.);
#832 = VERTEX_POINT('',#833);
#833 = CARTESIAN_POINT('',(0.8,0.2,0.25));
#834 = LINE('',#835,#836);
#835 = CARTESIAN_POINT('',(0.8,0.2,0.475));
#836 = VECTOR('',#837,1.);
#837 = DIRECTION('',(0.,0.,-1.));
#838 = ORIENTED_EDGE('',*,*,#839,.T.);
#839 = EDGE_CURVE('',#832,#840,#842,.T.);
#840 = VERTEX_POINT('',#841);
#841 = CARTESIAN_POINT('',(1.05,0.2,0.));
#842 = CIRCLE('',#843,0.25);
#843 = AXIS2_PLACEMENT_3D('',#844,#845,#846);
#844 = CARTESIAN_POINT('',(1.05,0.2,0.25));
#845 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#846 = DIRECTION('',(-1.,-2.22044604925E-16,-4.440892098501E-16));
#847 = ORIENTED_EDGE('',*,*,#848,.T.);
#848 = EDGE_CURVE('',#840,#849,#851,.T.);
#849 = VERTEX_POINT('',#850);
#850 = CARTESIAN_POINT('',(1.25,0.2,0.));
#851 = LINE('',#852,#853);
#852 = CARTESIAN_POINT('',(1.05,0.2,0.));
#853 = VECTOR('',#854,1.);
#854 = DIRECTION('',(1.,2.22044604925E-16,0.));
#855 = ORIENTED_EDGE('',*,*,#856,.T.);
#856 = EDGE_CURVE('',#849,#857,#859,.T.);
#857 = VERTEX_POINT('',#858);
#858 = CARTESIAN_POINT('',(1.25,0.2,0.15));
#859 = LINE('',#860,#861);
#860 = CARTESIAN_POINT('',(1.25,0.2,0.));
#861 = VECTOR('',#862,1.);
#862 = DIRECTION('',(0.,0.,1.));
#863 = ORIENTED_EDGE('',*,*,#864,.T.);
#864 = EDGE_CURVE('',#857,#865,#867,.T.);
#865 = VERTEX_POINT('',#866);
#866 = CARTESIAN_POINT('',(1.05,0.2,0.15));
#867 = LINE('',#868,#869);
#868 = CARTESIAN_POINT('',(1.25,0.2,0.15));
#869 = VECTOR('',#870,1.);
#870 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#871 = ORIENTED_EDGE('',*,*,#872,.T.);
#872 = EDGE_CURVE('',#865,#873,#875,.T.);
#873 = VERTEX_POINT('',#874);
#874 = CARTESIAN_POINT('',(0.95,0.2,0.25));
#875 = CIRCLE('',#876,0.1);
#876 = AXIS2_PLACEMENT_3D('',#877,#878,#879);
#877 = CARTESIAN_POINT('',(1.05,0.2,0.25));
#878 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#879 = DIRECTION('',(-2.22044604925E-15,-4.930380657631E-31,-1.));
#880 = ORIENTED_EDGE('',*,*,#881,.T.);
#881 = EDGE_CURVE('',#873,#882,#884,.T.);
#882 = VERTEX_POINT('',#883);
#883 = CARTESIAN_POINT('',(0.95,0.2,0.475));
#884 = LINE('',#885,#886);
#885 = CARTESIAN_POINT('',(0.95,0.2,0.25));
#886 = VECTOR('',#887,1.);
#887 = DIRECTION('',(0.,0.,1.));
#888 = ORIENTED_EDGE('',*,*,#889,.T.);
#889 = EDGE_CURVE('',#882,#432,#890,.T.);
#890 = CIRCLE('',#891,0.25);
#891 = AXIS2_PLACEMENT_3D('',#892,#893,#894);
#892 = CARTESIAN_POINT('',(0.7,0.2,0.475));
#893 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#894 = DIRECTION('',(1.,2.22044604925E-16,0.));
#895 = PLANE('',#896);
#896 = AXIS2_PLACEMENT_3D('',#897,#898,#899);
#897 = CARTESIAN_POINT('',(0.923137815574,0.2,0.325594341393));
#898 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#899 = DIRECTION('',(0.,0.,1.));
#900 = ADVANCED_FACE('',(#901),#970,.T.);
#901 = FACE_BOUND('',#902,.T.);
#902 = EDGE_LOOP('',(#903,#904,#905,#913,#922,#930,#938,#946,#955,#963)
);
#903 = ORIENTED_EDGE('',*,*,#453,.F.);
#904 = ORIENTED_EDGE('',*,*,#484,.T.);
#905 = ORIENTED_EDGE('',*,*,#906,.T.);
#906 = EDGE_CURVE('',#485,#907,#909,.T.);
#907 = VERTEX_POINT('',#908);
#908 = CARTESIAN_POINT('',(0.8,-0.2,0.25));
#909 = LINE('',#910,#911);
#910 = CARTESIAN_POINT('',(0.8,-0.2,0.475));
#911 = VECTOR('',#912,1.);
#912 = DIRECTION('',(0.,0.,-1.));
#913 = ORIENTED_EDGE('',*,*,#914,.T.);
#914 = EDGE_CURVE('',#907,#915,#917,.T.);
#915 = VERTEX_POINT('',#916);
#916 = CARTESIAN_POINT('',(1.05,-0.2,0.));
#917 = CIRCLE('',#918,0.25);
#918 = AXIS2_PLACEMENT_3D('',#919,#920,#921);
#919 = CARTESIAN_POINT('',(1.05,-0.2,0.25));
#920 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#921 = DIRECTION('',(-1.,-2.22044604925E-16,-4.440892098501E-16));
#922 = ORIENTED_EDGE('',*,*,#923,.T.);
#923 = EDGE_CURVE('',#915,#924,#926,.T.);
#924 = VERTEX_POINT('',#925);
#925 = CARTESIAN_POINT('',(1.25,-0.2,0.));
#926 = LINE('',#927,#928);
#927 = CARTESIAN_POINT('',(1.05,-0.2,0.));
#928 = VECTOR('',#929,1.);
#929 = DIRECTION('',(1.,2.22044604925E-16,0.));
#930 = ORIENTED_EDGE('',*,*,#931,.T.);
#931 = EDGE_CURVE('',#924,#932,#934,.T.);
#932 = VERTEX_POINT('',#933);
#933 = CARTESIAN_POINT('',(1.25,-0.2,0.15));
#934 = LINE('',#935,#936);
#935 = CARTESIAN_POINT('',(1.25,-0.2,0.));
#936 = VECTOR('',#937,1.);
#937 = DIRECTION('',(0.,0.,1.));
#938 = ORIENTED_EDGE('',*,*,#939,.T.);
#939 = EDGE_CURVE('',#932,#940,#942,.T.);
#940 = VERTEX_POINT('',#941);
#941 = CARTESIAN_POINT('',(1.05,-0.2,0.15));
#942 = LINE('',#943,#944);
#943 = CARTESIAN_POINT('',(1.25,-0.2,0.15));
#944 = VECTOR('',#945,1.);
#945 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#946 = ORIENTED_EDGE('',*,*,#947,.T.);
#947 = EDGE_CURVE('',#940,#948,#950,.T.);
#948 = VERTEX_POINT('',#949);
#949 = CARTESIAN_POINT('',(0.95,-0.2,0.25));
#950 = CIRCLE('',#951,0.1);
#951 = AXIS2_PLACEMENT_3D('',#952,#953,#954);
#952 = CARTESIAN_POINT('',(1.05,-0.2,0.25));
#953 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#954 = DIRECTION('',(-2.22044604925E-15,-4.930380657631E-31,-1.));
#955 = ORIENTED_EDGE('',*,*,#956,.T.);
#956 = EDGE_CURVE('',#948,#957,#959,.T.);
#957 = VERTEX_POINT('',#958);
#958 = CARTESIAN_POINT('',(0.95,-0.2,0.475));
#959 = LINE('',#960,#961);
#960 = CARTESIAN_POINT('',(0.95,-0.2,0.25));
#961 = VECTOR('',#962,1.);
#962 = DIRECTION('',(0.,0.,1.));
#963 = ORIENTED_EDGE('',*,*,#964,.T.);
#964 = EDGE_CURVE('',#957,#454,#965,.T.);
#965 = CIRCLE('',#966,0.25);
#966 = AXIS2_PLACEMENT_3D('',#967,#968,#969);
#967 = CARTESIAN_POINT('',(0.7,-0.2,0.475));
#968 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#969 = DIRECTION('',(1.,2.22044604925E-16,0.));
#970 = PLANE('',#971);
#971 = AXIS2_PLACEMENT_3D('',#972,#973,#974);
#972 = CARTESIAN_POINT('',(0.923137815574,-0.2,0.325594341393));
#973 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#974 = DIRECTION('',(0.,0.,1.));
#975 = ADVANCED_FACE('',(#976),#1000,.F.);
#976 = FACE_BOUND('',#977,.F.);
#977 = EDGE_LOOP('',(#978,#986,#987,#994));
#978 = ORIENTED_EDGE('',*,*,#979,.F.);
#979 = EDGE_CURVE('',#454,#980,#982,.T.);
#980 = VERTEX_POINT('',#981);
#981 = CARTESIAN_POINT('',(0.675,-0.2,0.725));
#982 = LINE('',#983,#984);
#983 = CARTESIAN_POINT('',(0.7,-0.2,0.725));
#984 = VECTOR('',#985,1.);
#985 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#986 = ORIENTED_EDGE('',*,*,#469,.T.);
#987 = ORIENTED_EDGE('',*,*,#988,.T.);
#988 = EDGE_CURVE('',#463,#989,#991,.T.);
#989 = VERTEX_POINT('',#990);
#990 = CARTESIAN_POINT('',(0.675,-1.3925,0.725));
#991 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#992,#993),.UNSPECIFIED.,.F.,.F.,
(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#992 = CARTESIAN_POINT('',(0.7,-1.405,0.725));
#993 = CARTESIAN_POINT('',(0.675,-1.3925,0.725));
#994 = ORIENTED_EDGE('',*,*,#995,.F.);
#995 = EDGE_CURVE('',#980,#989,#996,.T.);
#996 = LINE('',#997,#998);
#997 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#998 = VECTOR('',#999,1.);
#999 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1000 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1001,#1002)
,(#1003,#1004
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.81),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#1001 = CARTESIAN_POINT('',(0.7,1.405,0.725));
#1002 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#1003 = CARTESIAN_POINT('',(0.7,-1.405,0.725));
#1004 = CARTESIAN_POINT('',(0.675,-1.3925,0.725));
#1005 = ADVANCED_FACE('',(#1006),#1017,.F.);
#1006 = FACE_BOUND('',#1007,.F.);
#1007 = EDGE_LOOP('',(#1008,#1009,#1010,#1016));
#1008 = ORIENTED_EDGE('',*,*,#493,.T.);
#1009 = ORIENTED_EDGE('',*,*,#906,.T.);
#1010 = ORIENTED_EDGE('',*,*,#1011,.F.);
#1011 = EDGE_CURVE('',#832,#907,#1012,.T.);
#1012 = LINE('',#1013,#1014);
#1013 = CARTESIAN_POINT('',(0.8,0.2,0.25));
#1014 = VECTOR('',#1015,1.);
#1015 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1016 = ORIENTED_EDGE('',*,*,#831,.F.);
#1017 = PLANE('',#1018);
#1018 = AXIS2_PLACEMENT_3D('',#1019,#1020,#1021);
#1019 = CARTESIAN_POINT('',(0.8,0.2,0.475));
#1020 = DIRECTION('',(1.,2.22044604925E-16,0.));
#1021 = DIRECTION('',(0.,0.,-1.));
#1022 = ADVANCED_FACE('',(#1023),#1040,.F.);
#1023 = FACE_BOUND('',#1024,.F.);
#1024 = EDGE_LOOP('',(#1025,#1026,#1033,#1039));
#1025 = ORIENTED_EDGE('',*,*,#524,.T.);
#1026 = ORIENTED_EDGE('',*,*,#1027,.T.);
#1027 = EDGE_CURVE('',#518,#1028,#1030,.T.);
#1028 = VERTEX_POINT('',#1029);
#1029 = CARTESIAN_POINT('',(0.5925,-1.475,0.725));
#1030 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1031,#1032),.UNSPECIFIED.,.F.,
.F.,(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#1031 = CARTESIAN_POINT('',(0.605,-1.5,0.725));
#1032 = CARTESIAN_POINT('',(0.5925,-1.475,0.725));
#1033 = ORIENTED_EDGE('',*,*,#1034,.F.);
#1034 = EDGE_CURVE('',#989,#1028,#1035,.T.);
#1035 = LINE('',#1036,#1037);
#1036 = CARTESIAN_POINT('',(0.675,-1.3925,0.725));
#1037 = VECTOR('',#1038,1.);
#1038 = DIRECTION('',(-0.707106781187,-0.707106781187,0.));
#1039 = ORIENTED_EDGE('',*,*,#988,.F.);
#1040 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1041,#1042)
,(#1043,#1044
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.134350288425),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#1041 = CARTESIAN_POINT('',(0.7,-1.405,0.725));
#1042 = CARTESIAN_POINT('',(0.675,-1.3925,0.725));
#1043 = CARTESIAN_POINT('',(0.605,-1.5,0.725));
#1044 = CARTESIAN_POINT('',(0.5925,-1.475,0.725));
#1045 = ADVANCED_FACE('',(#1046),#1063,.F.);
#1046 = FACE_BOUND('',#1047,.F.);
#1047 = EDGE_LOOP('',(#1048,#1049,#1056,#1062));
#1048 = ORIENTED_EDGE('',*,*,#547,.T.);
#1049 = ORIENTED_EDGE('',*,*,#1050,.T.);
#1050 = EDGE_CURVE('',#541,#1051,#1053,.T.);
#1051 = VERTEX_POINT('',#1052);
#1052 = CARTESIAN_POINT('',(-0.5925,-1.475,0.725));
#1053 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1054,#1055),.UNSPECIFIED.,.F.,
.F.,(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#1054 = CARTESIAN_POINT('',(-0.605,-1.5,0.725));
#1055 = CARTESIAN_POINT('',(-0.5925,-1.475,0.725));
#1056 = ORIENTED_EDGE('',*,*,#1057,.F.);
#1057 = EDGE_CURVE('',#1028,#1051,#1058,.T.);
#1058 = LINE('',#1059,#1060);
#1059 = CARTESIAN_POINT('',(0.5925,-1.475,0.725));
#1060 = VECTOR('',#1061,1.);
#1061 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#1062 = ORIENTED_EDGE('',*,*,#1027,.F.);
#1063 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1064,#1065)
,(#1066,#1067
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,1.21),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#1064 = CARTESIAN_POINT('',(0.605,-1.5,0.725));
#1065 = CARTESIAN_POINT('',(0.5925,-1.475,0.725));
#1066 = CARTESIAN_POINT('',(-0.605,-1.5,0.725));
#1067 = CARTESIAN_POINT('',(-0.5925,-1.475,0.725));
#1068 = ADVANCED_FACE('',(#1069),#1086,.F.);
#1069 = FACE_BOUND('',#1070,.F.);
#1070 = EDGE_LOOP('',(#1071,#1072,#1079,#1085));
#1071 = ORIENTED_EDGE('',*,*,#570,.T.);
#1072 = ORIENTED_EDGE('',*,*,#1073,.T.);
#1073 = EDGE_CURVE('',#564,#1074,#1076,.T.);
#1074 = VERTEX_POINT('',#1075);
#1075 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1076 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1077,#1078),.UNSPECIFIED.,.F.,
.F.,(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#1077 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#1078 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1079 = ORIENTED_EDGE('',*,*,#1080,.F.);
#1080 = EDGE_CURVE('',#1051,#1074,#1081,.T.);
#1081 = LINE('',#1082,#1083);
#1082 = CARTESIAN_POINT('',(-0.5925,-1.475,0.725));
#1083 = VECTOR('',#1084,1.);
#1084 = DIRECTION('',(-0.707106781187,0.707106781187,0.));
#1085 = ORIENTED_EDGE('',*,*,#1050,.F.);
#1086 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1087,#1088)
,(#1089,#1090
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.134350288425),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#1087 = CARTESIAN_POINT('',(-0.605,-1.5,0.725));
#1088 = CARTESIAN_POINT('',(-0.5925,-1.475,0.725));
#1089 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#1090 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1091 = ADVANCED_FACE('',(#1092),#1110,.F.);
#1092 = FACE_BOUND('',#1093,.F.);
#1093 = EDGE_LOOP('',(#1094,#1095,#1096,#1104));
#1094 = ORIENTED_EDGE('',*,*,#1073,.F.);
#1095 = ORIENTED_EDGE('',*,*,#595,.T.);
#1096 = ORIENTED_EDGE('',*,*,#1097,.T.);
#1097 = EDGE_CURVE('',#588,#1098,#1100,.T.);
#1098 = VERTEX_POINT('',#1099);
#1099 = CARTESIAN_POINT('',(-0.675,-1.15,0.725));
#1100 = LINE('',#1101,#1102);
#1101 = CARTESIAN_POINT('',(-0.7,-1.15,0.725));
#1102 = VECTOR('',#1103,1.);
#1103 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1104 = ORIENTED_EDGE('',*,*,#1105,.F.);
#1105 = EDGE_CURVE('',#1074,#1098,#1106,.T.);
#1106 = LINE('',#1107,#1108);
#1107 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1108 = VECTOR('',#1109,1.);
#1109 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#1110 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1111,#1112)
,(#1113,#1114
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.81),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#1111 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#1112 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1113 = CARTESIAN_POINT('',(-0.7,1.405,0.725));
#1114 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#1115 = ADVANCED_FACE('',(#1116),#1185,.F.);
#1116 = FACE_BOUND('',#1117,.F.);
#1117 = EDGE_LOOP('',(#1118,#1119,#1120,#1128,#1137,#1145,#1153,#1161,
#1170,#1178));
#1118 = ORIENTED_EDGE('',*,*,#587,.F.);
#1119 = ORIENTED_EDGE('',*,*,#651,.T.);
#1120 = ORIENTED_EDGE('',*,*,#1121,.T.);
#1121 = EDGE_CURVE('',#644,#1122,#1124,.T.);
#1122 = VERTEX_POINT('',#1123);
#1123 = CARTESIAN_POINT('',(-0.8,-1.15,0.25));
#1124 = LINE('',#1125,#1126);
#1125 = CARTESIAN_POINT('',(-0.8,-1.15,0.475));
#1126 = VECTOR('',#1127,1.);
#1127 = DIRECTION('',(-0.,0.,-1.));
#1128 = ORIENTED_EDGE('',*,*,#1129,.T.);
#1129 = EDGE_CURVE('',#1122,#1130,#1132,.T.);
#1130 = VERTEX_POINT('',#1131);
#1131 = CARTESIAN_POINT('',(-1.05,-1.15,0.));
#1132 = CIRCLE('',#1133,0.25);
#1133 = AXIS2_PLACEMENT_3D('',#1134,#1135,#1136);
#1134 = CARTESIAN_POINT('',(-1.05,-1.15,0.25));
#1135 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1136 = DIRECTION('',(1.,9.95799250103E-17,-4.440892098501E-16));
#1137 = ORIENTED_EDGE('',*,*,#1138,.T.);
#1138 = EDGE_CURVE('',#1130,#1139,#1141,.T.);
#1139 = VERTEX_POINT('',#1140);
#1140 = CARTESIAN_POINT('',(-1.25,-1.15,0.));
#1141 = LINE('',#1142,#1143);
#1142 = CARTESIAN_POINT('',(-1.05,-1.15,0.));
#1143 = VECTOR('',#1144,1.);
#1144 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1145 = ORIENTED_EDGE('',*,*,#1146,.T.);
#1146 = EDGE_CURVE('',#1139,#1147,#1149,.T.);
#1147 = VERTEX_POINT('',#1148);
#1148 = CARTESIAN_POINT('',(-1.25,-1.15,0.15));
#1149 = LINE('',#1150,#1151);
#1150 = CARTESIAN_POINT('',(-1.25,-1.15,0.));
#1151 = VECTOR('',#1152,1.);
#1152 = DIRECTION('',(0.,0.,1.));
#1153 = ORIENTED_EDGE('',*,*,#1154,.T.);
#1154 = EDGE_CURVE('',#1147,#1155,#1157,.T.);
#1155 = VERTEX_POINT('',#1156);
#1156 = CARTESIAN_POINT('',(-1.05,-1.15,0.15));
#1157 = LINE('',#1158,#1159);
#1158 = CARTESIAN_POINT('',(-1.25,-1.15,0.15));
#1159 = VECTOR('',#1160,1.);
#1160 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1161 = ORIENTED_EDGE('',*,*,#1162,.T.);
#1162 = EDGE_CURVE('',#1155,#1163,#1165,.T.);
#1163 = VERTEX_POINT('',#1164);
#1164 = CARTESIAN_POINT('',(-0.95,-1.15,0.25));
#1165 = CIRCLE('',#1166,0.1);
#1166 = AXIS2_PLACEMENT_3D('',#1167,#1168,#1169);
#1167 = CARTESIAN_POINT('',(-1.05,-1.15,0.25));
#1168 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#1169 = DIRECTION('',(2.22044604925E-15,2.211118510738E-31,-1.));
#1170 = ORIENTED_EDGE('',*,*,#1171,.T.);
#1171 = EDGE_CURVE('',#1163,#1172,#1174,.T.);
#1172 = VERTEX_POINT('',#1173);
#1173 = CARTESIAN_POINT('',(-0.95,-1.15,0.475));
#1174 = LINE('',#1175,#1176);
#1175 = CARTESIAN_POINT('',(-0.95,-1.15,0.25));
#1176 = VECTOR('',#1177,1.);
#1177 = DIRECTION('',(0.,0.,1.));
#1178 = ORIENTED_EDGE('',*,*,#1179,.T.);
#1179 = EDGE_CURVE('',#1172,#588,#1180,.T.);
#1180 = CIRCLE('',#1181,0.25);
#1181 = AXIS2_PLACEMENT_3D('',#1182,#1183,#1184);
#1182 = CARTESIAN_POINT('',(-0.7,-1.15,0.475));
#1183 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1184 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1185 = PLANE('',#1186);
#1186 = AXIS2_PLACEMENT_3D('',#1187,#1188,#1189);
#1187 = CARTESIAN_POINT('',(-0.923137815574,-1.15,0.325594341393));
#1188 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1189 = DIRECTION('',(0.,0.,1.));
#1190 = ADVANCED_FACE('',(#1191),#1260,.T.);
#1191 = FACE_BOUND('',#1192,.T.);
#1192 = EDGE_LOOP('',(#1193,#1194,#1195,#1203,#1212,#1220,#1228,#1236,
#1245,#1253));
#1193 = ORIENTED_EDGE('',*,*,#609,.F.);
#1194 = ORIENTED_EDGE('',*,*,#667,.T.);
#1195 = ORIENTED_EDGE('',*,*,#1196,.T.);
#1196 = EDGE_CURVE('',#668,#1197,#1199,.T.);
#1197 = VERTEX_POINT('',#1198);
#1198 = CARTESIAN_POINT('',(-0.8,1.15,0.25));
#1199 = LINE('',#1200,#1201);
#1200 = CARTESIAN_POINT('',(-0.8,1.15,0.475));
#1201 = VECTOR('',#1202,1.);
#1202 = DIRECTION('',(-0.,0.,-1.));
#1203 = ORIENTED_EDGE('',*,*,#1204,.T.);
#1204 = EDGE_CURVE('',#1197,#1205,#1207,.T.);
#1205 = VERTEX_POINT('',#1206);
#1206 = CARTESIAN_POINT('',(-1.05,1.15,0.));
#1207 = CIRCLE('',#1208,0.25);
#1208 = AXIS2_PLACEMENT_3D('',#1209,#1210,#1211);
#1209 = CARTESIAN_POINT('',(-1.05,1.15,0.25));
#1210 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1211 = DIRECTION('',(1.,9.95799250103E-17,-4.440892098501E-16));
#1212 = ORIENTED_EDGE('',*,*,#1213,.T.);
#1213 = EDGE_CURVE('',#1205,#1214,#1216,.T.);
#1214 = VERTEX_POINT('',#1215);
#1215 = CARTESIAN_POINT('',(-1.25,1.15,0.));
#1216 = LINE('',#1217,#1218);
#1217 = CARTESIAN_POINT('',(-1.05,1.15,0.));
#1218 = VECTOR('',#1219,1.);
#1219 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1220 = ORIENTED_EDGE('',*,*,#1221,.T.);
#1221 = EDGE_CURVE('',#1214,#1222,#1224,.T.);
#1222 = VERTEX_POINT('',#1223);
#1223 = CARTESIAN_POINT('',(-1.25,1.15,0.15));
#1224 = LINE('',#1225,#1226);
#1225 = CARTESIAN_POINT('',(-1.25,1.15,0.));
#1226 = VECTOR('',#1227,1.);
#1227 = DIRECTION('',(0.,0.,1.));
#1228 = ORIENTED_EDGE('',*,*,#1229,.T.);
#1229 = EDGE_CURVE('',#1222,#1230,#1232,.T.);
#1230 = VERTEX_POINT('',#1231);
#1231 = CARTESIAN_POINT('',(-1.05,1.15,0.15));
#1232 = LINE('',#1233,#1234);
#1233 = CARTESIAN_POINT('',(-1.25,1.15,0.15));
#1234 = VECTOR('',#1235,1.);
#1235 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1236 = ORIENTED_EDGE('',*,*,#1237,.T.);
#1237 = EDGE_CURVE('',#1230,#1238,#1240,.T.);
#1238 = VERTEX_POINT('',#1239);
#1239 = CARTESIAN_POINT('',(-0.95,1.15,0.25));
#1240 = CIRCLE('',#1241,0.1);
#1241 = AXIS2_PLACEMENT_3D('',#1242,#1243,#1244);
#1242 = CARTESIAN_POINT('',(-1.05,1.15,0.25));
#1243 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#1244 = DIRECTION('',(2.22044604925E-15,2.211118510738E-31,-1.));
#1245 = ORIENTED_EDGE('',*,*,#1246,.T.);
#1246 = EDGE_CURVE('',#1238,#1247,#1249,.T.);
#1247 = VERTEX_POINT('',#1248);
#1248 = CARTESIAN_POINT('',(-0.95,1.15,0.475));
#1249 = LINE('',#1250,#1251);
#1250 = CARTESIAN_POINT('',(-0.95,1.15,0.25));
#1251 = VECTOR('',#1252,1.);
#1252 = DIRECTION('',(0.,0.,1.));
#1253 = ORIENTED_EDGE('',*,*,#1254,.T.);
#1254 = EDGE_CURVE('',#1247,#610,#1255,.T.);
#1255 = CIRCLE('',#1256,0.25);
#1256 = AXIS2_PLACEMENT_3D('',#1257,#1258,#1259);
#1257 = CARTESIAN_POINT('',(-0.7,1.15,0.475));
#1258 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1259 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1260 = PLANE('',#1261);
#1261 = AXIS2_PLACEMENT_3D('',#1262,#1263,#1264);
#1262 = CARTESIAN_POINT('',(-0.923137815574,1.15,0.325594341393));
#1263 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1264 = DIRECTION('',(0.,0.,1.));
#1265 = ADVANCED_FACE('',(#1266),#1284,.F.);
#1266 = FACE_BOUND('',#1267,.F.);
#1267 = EDGE_LOOP('',(#1268,#1276,#1277,#1278));
#1268 = ORIENTED_EDGE('',*,*,#1269,.F.);
#1269 = EDGE_CURVE('',#610,#1270,#1272,.T.);
#1270 = VERTEX_POINT('',#1271);
#1271 = CARTESIAN_POINT('',(-0.675,1.15,0.725));
#1272 = LINE('',#1273,#1274);
#1273 = CARTESIAN_POINT('',(-0.7,1.15,0.725));
#1274 = VECTOR('',#1275,1.);
#1275 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1276 = ORIENTED_EDGE('',*,*,#619,.T.);
#1277 = ORIENTED_EDGE('',*,*,#792,.T.);
#1278 = ORIENTED_EDGE('',*,*,#1279,.F.);
#1279 = EDGE_CURVE('',#1270,#785,#1280,.T.);
#1280 = LINE('',#1281,#1282);
#1281 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1282 = VECTOR('',#1283,1.);
#1283 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#1284 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1285,#1286)
,(#1287,#1288
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.81),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#1285 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#1286 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1287 = CARTESIAN_POINT('',(-0.7,1.405,0.725));
#1288 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#1289 = ADVANCED_FACE('',(#1290),#1359,.T.);
#1290 = FACE_BOUND('',#1291,.T.);
#1291 = EDGE_LOOP('',(#1292,#1293,#1294,#1302,#1311,#1319,#1327,#1335,
#1344,#1352));
#1292 = ORIENTED_EDGE('',*,*,#699,.F.);
#1293 = ORIENTED_EDGE('',*,*,#634,.T.);
#1294 = ORIENTED_EDGE('',*,*,#1295,.T.);
#1295 = EDGE_CURVE('',#635,#1296,#1298,.T.);
#1296 = VERTEX_POINT('',#1297);
#1297 = CARTESIAN_POINT('',(-0.8,-0.75,0.25));
#1298 = LINE('',#1299,#1300);
#1299 = CARTESIAN_POINT('',(-0.8,-0.75,0.475));
#1300 = VECTOR('',#1301,1.);
#1301 = DIRECTION('',(-0.,0.,-1.));
#1302 = ORIENTED_EDGE('',*,*,#1303,.T.);
#1303 = EDGE_CURVE('',#1296,#1304,#1306,.T.);
#1304 = VERTEX_POINT('',#1305);
#1305 = CARTESIAN_POINT('',(-1.05,-0.75,0.));
#1306 = CIRCLE('',#1307,0.25);
#1307 = AXIS2_PLACEMENT_3D('',#1308,#1309,#1310);
#1308 = CARTESIAN_POINT('',(-1.05,-0.75,0.25));
#1309 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1310 = DIRECTION('',(1.,9.95799250103E-17,-4.440892098501E-16));
#1311 = ORIENTED_EDGE('',*,*,#1312,.T.);
#1312 = EDGE_CURVE('',#1304,#1313,#1315,.T.);
#1313 = VERTEX_POINT('',#1314);
#1314 = CARTESIAN_POINT('',(-1.25,-0.75,0.));
#1315 = LINE('',#1316,#1317);
#1316 = CARTESIAN_POINT('',(-1.05,-0.75,0.));
#1317 = VECTOR('',#1318,1.);
#1318 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1319 = ORIENTED_EDGE('',*,*,#1320,.T.);
#1320 = EDGE_CURVE('',#1313,#1321,#1323,.T.);
#1321 = VERTEX_POINT('',#1322);
#1322 = CARTESIAN_POINT('',(-1.25,-0.75,0.15));
#1323 = LINE('',#1324,#1325);
#1324 = CARTESIAN_POINT('',(-1.25,-0.75,0.));
#1325 = VECTOR('',#1326,1.);
#1326 = DIRECTION('',(0.,0.,1.));
#1327 = ORIENTED_EDGE('',*,*,#1328,.T.);
#1328 = EDGE_CURVE('',#1321,#1329,#1331,.T.);
#1329 = VERTEX_POINT('',#1330);
#1330 = CARTESIAN_POINT('',(-1.05,-0.75,0.15));
#1331 = LINE('',#1332,#1333);
#1332 = CARTESIAN_POINT('',(-1.25,-0.75,0.15));
#1333 = VECTOR('',#1334,1.);
#1334 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1335 = ORIENTED_EDGE('',*,*,#1336,.T.);
#1336 = EDGE_CURVE('',#1329,#1337,#1339,.T.);
#1337 = VERTEX_POINT('',#1338);
#1338 = CARTESIAN_POINT('',(-0.95,-0.75,0.25));
#1339 = CIRCLE('',#1340,0.1);
#1340 = AXIS2_PLACEMENT_3D('',#1341,#1342,#1343);
#1341 = CARTESIAN_POINT('',(-1.05,-0.75,0.25));
#1342 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#1343 = DIRECTION('',(2.22044604925E-15,2.211118510738E-31,-1.));
#1344 = ORIENTED_EDGE('',*,*,#1345,.T.);
#1345 = EDGE_CURVE('',#1337,#1346,#1348,.T.);
#1346 = VERTEX_POINT('',#1347);
#1347 = CARTESIAN_POINT('',(-0.95,-0.75,0.475));
#1348 = LINE('',#1349,#1350);
#1349 = CARTESIAN_POINT('',(-0.95,-0.75,0.25));
#1350 = VECTOR('',#1351,1.);
#1351 = DIRECTION('',(0.,0.,1.));
#1352 = ORIENTED_EDGE('',*,*,#1353,.T.);
#1353 = EDGE_CURVE('',#1346,#700,#1354,.T.);
#1354 = CIRCLE('',#1355,0.25);
#1355 = AXIS2_PLACEMENT_3D('',#1356,#1357,#1358);
#1356 = CARTESIAN_POINT('',(-0.7,-0.75,0.475));
#1357 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1358 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1359 = PLANE('',#1360);
#1360 = AXIS2_PLACEMENT_3D('',#1361,#1362,#1363);
#1361 = CARTESIAN_POINT('',(-0.923137815574,-0.75,0.325594341393));
#1362 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1363 = DIRECTION('',(0.,0.,1.));
#1364 = ADVANCED_FACE('',(#1365),#1376,.F.);
#1365 = FACE_BOUND('',#1366,.F.);
#1366 = EDGE_LOOP('',(#1367,#1368,#1369,#1375));
#1367 = ORIENTED_EDGE('',*,*,#643,.T.);
#1368 = ORIENTED_EDGE('',*,*,#1295,.T.);
#1369 = ORIENTED_EDGE('',*,*,#1370,.F.);
#1370 = EDGE_CURVE('',#1122,#1296,#1371,.T.);
#1371 = LINE('',#1372,#1373);
#1372 = CARTESIAN_POINT('',(-0.8,-1.15,0.25));
#1373 = VECTOR('',#1374,1.);
#1374 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1375 = ORIENTED_EDGE('',*,*,#1121,.F.);
#1376 = PLANE('',#1377);
#1377 = AXIS2_PLACEMENT_3D('',#1378,#1379,#1380);
#1378 = CARTESIAN_POINT('',(-0.8,-1.15,0.475));
#1379 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1380 = DIRECTION('',(-0.,0.,-1.));
#1381 = ADVANCED_FACE('',(#1382),#1451,.F.);
#1382 = FACE_BOUND('',#1383,.F.);
#1383 = EDGE_LOOP('',(#1384,#1385,#1386,#1394,#1403,#1411,#1419,#1427,
#1436,#1444));
#1384 = ORIENTED_EDGE('',*,*,#708,.F.);
#1385 = ORIENTED_EDGE('',*,*,#684,.T.);
#1386 = ORIENTED_EDGE('',*,*,#1387,.T.);
#1387 = EDGE_CURVE('',#677,#1388,#1390,.T.);
#1388 = VERTEX_POINT('',#1389);
#1389 = CARTESIAN_POINT('',(-0.8,0.75,0.25));
#1390 = LINE('',#1391,#1392);
#1391 = CARTESIAN_POINT('',(-0.8,0.75,0.475));
#1392 = VECTOR('',#1393,1.);
#1393 = DIRECTION('',(-0.,0.,-1.));
#1394 = ORIENTED_EDGE('',*,*,#1395,.T.);
#1395 = EDGE_CURVE('',#1388,#1396,#1398,.T.);
#1396 = VERTEX_POINT('',#1397);
#1397 = CARTESIAN_POINT('',(-1.05,0.75,0.));
#1398 = CIRCLE('',#1399,0.25);
#1399 = AXIS2_PLACEMENT_3D('',#1400,#1401,#1402);
#1400 = CARTESIAN_POINT('',(-1.05,0.75,0.25));
#1401 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1402 = DIRECTION('',(1.,9.95799250103E-17,-4.440892098501E-16));
#1403 = ORIENTED_EDGE('',*,*,#1404,.T.);
#1404 = EDGE_CURVE('',#1396,#1405,#1407,.T.);
#1405 = VERTEX_POINT('',#1406);
#1406 = CARTESIAN_POINT('',(-1.25,0.75,0.));
#1407 = LINE('',#1408,#1409);
#1408 = CARTESIAN_POINT('',(-1.05,0.75,0.));
#1409 = VECTOR('',#1410,1.);
#1410 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1411 = ORIENTED_EDGE('',*,*,#1412,.T.);
#1412 = EDGE_CURVE('',#1405,#1413,#1415,.T.);
#1413 = VERTEX_POINT('',#1414);
#1414 = CARTESIAN_POINT('',(-1.25,0.75,0.15));
#1415 = LINE('',#1416,#1417);
#1416 = CARTESIAN_POINT('',(-1.25,0.75,0.));
#1417 = VECTOR('',#1418,1.);
#1418 = DIRECTION('',(0.,0.,1.));
#1419 = ORIENTED_EDGE('',*,*,#1420,.T.);
#1420 = EDGE_CURVE('',#1413,#1421,#1423,.T.);
#1421 = VERTEX_POINT('',#1422);
#1422 = CARTESIAN_POINT('',(-1.05,0.75,0.15));
#1423 = LINE('',#1424,#1425);
#1424 = CARTESIAN_POINT('',(-1.25,0.75,0.15));
#1425 = VECTOR('',#1426,1.);
#1426 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1427 = ORIENTED_EDGE('',*,*,#1428,.T.);
#1428 = EDGE_CURVE('',#1421,#1429,#1431,.T.);
#1429 = VERTEX_POINT('',#1430);
#1430 = CARTESIAN_POINT('',(-0.95,0.75,0.25));
#1431 = CIRCLE('',#1432,0.1);
#1432 = AXIS2_PLACEMENT_3D('',#1433,#1434,#1435);
#1433 = CARTESIAN_POINT('',(-1.05,0.75,0.25));
#1434 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#1435 = DIRECTION('',(2.22044604925E-15,2.211118510738E-31,-1.));
#1436 = ORIENTED_EDGE('',*,*,#1437,.T.);
#1437 = EDGE_CURVE('',#1429,#1438,#1440,.T.);
#1438 = VERTEX_POINT('',#1439);
#1439 = CARTESIAN_POINT('',(-0.95,0.75,0.475));
#1440 = LINE('',#1441,#1442);
#1441 = CARTESIAN_POINT('',(-0.95,0.75,0.25));
#1442 = VECTOR('',#1443,1.);
#1443 = DIRECTION('',(0.,0.,1.));
#1444 = ORIENTED_EDGE('',*,*,#1445,.T.);
#1445 = EDGE_CURVE('',#1438,#709,#1446,.T.);
#1446 = CIRCLE('',#1447,0.25);
#1447 = AXIS2_PLACEMENT_3D('',#1448,#1449,#1450);
#1448 = CARTESIAN_POINT('',(-0.7,0.75,0.475));
#1449 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1450 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1451 = PLANE('',#1452);
#1452 = AXIS2_PLACEMENT_3D('',#1453,#1454,#1455);
#1453 = CARTESIAN_POINT('',(-0.923137815574,0.75,0.325594341393));
#1454 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1455 = DIRECTION('',(0.,0.,1.));
#1456 = ADVANCED_FACE('',(#1457),#1468,.F.);
#1457 = FACE_BOUND('',#1458,.F.);
#1458 = EDGE_LOOP('',(#1459,#1460,#1461,#1467));
#1459 = ORIENTED_EDGE('',*,*,#676,.T.);
#1460 = ORIENTED_EDGE('',*,*,#1196,.T.);
#1461 = ORIENTED_EDGE('',*,*,#1462,.F.);
#1462 = EDGE_CURVE('',#1388,#1197,#1463,.T.);
#1463 = LINE('',#1464,#1465);
#1464 = CARTESIAN_POINT('',(-0.8,0.75,0.25));
#1465 = VECTOR('',#1466,1.);
#1466 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1467 = ORIENTED_EDGE('',*,*,#1387,.F.);
#1468 = PLANE('',#1469);
#1469 = AXIS2_PLACEMENT_3D('',#1470,#1471,#1472);
#1470 = CARTESIAN_POINT('',(-0.8,0.75,0.475));
#1471 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1472 = DIRECTION('',(-0.,0.,-1.));
#1473 = ADVANCED_FACE('',(#1474),#1499,.F.);
#1474 = FACE_BOUND('',#1475,.F.);
#1475 = EDGE_LOOP('',(#1476,#1484,#1485,#1493));
#1476 = ORIENTED_EDGE('',*,*,#1477,.F.);
#1477 = EDGE_CURVE('',#700,#1478,#1480,.T.);
#1478 = VERTEX_POINT('',#1479);
#1479 = CARTESIAN_POINT('',(-0.675,-0.75,0.725));
#1480 = LINE('',#1481,#1482);
#1481 = CARTESIAN_POINT('',(-0.7,-0.75,0.725));
#1482 = VECTOR('',#1483,1.);
#1483 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1484 = ORIENTED_EDGE('',*,*,#716,.T.);
#1485 = ORIENTED_EDGE('',*,*,#1486,.T.);
#1486 = EDGE_CURVE('',#709,#1487,#1489,.T.);
#1487 = VERTEX_POINT('',#1488);
#1488 = CARTESIAN_POINT('',(-0.675,0.75,0.725));
#1489 = LINE('',#1490,#1491);
#1490 = CARTESIAN_POINT('',(-0.7,0.75,0.725));
#1491 = VECTOR('',#1492,1.);
#1492 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1493 = ORIENTED_EDGE('',*,*,#1494,.F.);
#1494 = EDGE_CURVE('',#1478,#1487,#1495,.T.);
#1495 = LINE('',#1496,#1497);
#1496 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1497 = VECTOR('',#1498,1.);
#1498 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#1499 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1500,#1501)
,(#1502,#1503
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.81),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#1500 = CARTESIAN_POINT('',(-0.7,-1.405,0.725));
#1501 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1502 = CARTESIAN_POINT('',(-0.7,1.405,0.725));
#1503 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#1504 = ADVANCED_FACE('',(#1505),#1522,.F.);
#1505 = FACE_BOUND('',#1506,.F.);
#1506 = EDGE_LOOP('',(#1507,#1508,#1515,#1521));
#1507 = ORIENTED_EDGE('',*,*,#738,.T.);
#1508 = ORIENTED_EDGE('',*,*,#1509,.T.);
#1509 = EDGE_CURVE('',#732,#1510,#1512,.T.);
#1510 = VERTEX_POINT('',#1511);
#1511 = CARTESIAN_POINT('',(0.608243103516,1.359121551758,1.2));
#1512 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1513,#1514),.UNSPECIFIED.,.F.,
.F.,(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#1513 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#1514 = CARTESIAN_POINT('',(0.608243103516,1.359121551758,1.2));
#1515 = ORIENTED_EDGE('',*,*,#1516,.F.);
#1516 = EDGE_CURVE('',#755,#1510,#1517,.T.);
#1517 = LINE('',#1518,#1519);
#1518 = CARTESIAN_POINT('',(0.559121551758,1.408243103516,1.2));
#1519 = VECTOR('',#1520,1.);
#1520 = DIRECTION('',(0.707106781187,-0.707106781187,0.));
#1521 = ORIENTED_EDGE('',*,*,#754,.F.);
#1522 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1523,#1524)
,(#1525,#1526
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.116672618896),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#1523 = CARTESIAN_POINT('',(0.5925,1.475,0.725));
#1524 = CARTESIAN_POINT('',(0.559121551758,1.408243103516,1.2));
#1525 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#1526 = CARTESIAN_POINT('',(0.608243103516,1.359121551758,1.2));
#1527 = ADVANCED_FACE('',(#1528),#1578,.T.);
#1528 = FACE_BOUND('',#1529,.F.);
#1529 = EDGE_LOOP('',(#1530,#1531,#1532,#1540,#1548,#1556,#1564,#1572));
#1530 = ORIENTED_EDGE('',*,*,#761,.T.);
#1531 = ORIENTED_EDGE('',*,*,#1516,.T.);
#1532 = ORIENTED_EDGE('',*,*,#1533,.T.);
#1533 = EDGE_CURVE('',#1510,#1534,#1536,.T.);
#1534 = VERTEX_POINT('',#1535);
#1535 = CARTESIAN_POINT('',(0.608243103516,-1.359121551758,1.2));
#1536 = LINE('',#1537,#1538);
#1537 = CARTESIAN_POINT('',(0.608243103516,1.359121551758,1.2));
#1538 = VECTOR('',#1539,1.);
#1539 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1540 = ORIENTED_EDGE('',*,*,#1541,.T.);
#1541 = EDGE_CURVE('',#1534,#1542,#1544,.T.);
#1542 = VERTEX_POINT('',#1543);
#1543 = CARTESIAN_POINT('',(0.559121551758,-1.408243103516,1.2));
#1544 = LINE('',#1545,#1546);
#1545 = CARTESIAN_POINT('',(0.608243103516,-1.359121551758,1.2));
#1546 = VECTOR('',#1547,1.);
#1547 = DIRECTION('',(-0.707106781187,-0.707106781187,0.));
#1548 = ORIENTED_EDGE('',*,*,#1549,.T.);
#1549 = EDGE_CURVE('',#1542,#1550,#1552,.T.);
#1550 = VERTEX_POINT('',#1551);
#1551 = CARTESIAN_POINT('',(-0.559121551758,-1.408243103516,1.2));
#1552 = LINE('',#1553,#1554);
#1553 = CARTESIAN_POINT('',(0.559121551758,-1.408243103516,1.2));
#1554 = VECTOR('',#1555,1.);
#1555 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#1556 = ORIENTED_EDGE('',*,*,#1557,.T.);
#1557 = EDGE_CURVE('',#1550,#1558,#1560,.T.);
#1558 = VERTEX_POINT('',#1559);
#1559 = CARTESIAN_POINT('',(-0.608243103516,-1.359121551758,1.2));
#1560 = LINE('',#1561,#1562);
#1561 = CARTESIAN_POINT('',(-0.559121551758,-1.408243103516,1.2));
#1562 = VECTOR('',#1563,1.);
#1563 = DIRECTION('',(-0.707106781187,0.707106781187,0.));
#1564 = ORIENTED_EDGE('',*,*,#1565,.T.);
#1565 = EDGE_CURVE('',#1558,#1566,#1568,.T.);
#1566 = VERTEX_POINT('',#1567);
#1567 = CARTESIAN_POINT('',(-0.608243103516,1.359121551758,1.2));
#1568 = LINE('',#1569,#1570);
#1569 = CARTESIAN_POINT('',(-0.608243103516,-1.359121551758,1.2));
#1570 = VECTOR('',#1571,1.);
#1571 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#1572 = ORIENTED_EDGE('',*,*,#1573,.T.);
#1573 = EDGE_CURVE('',#1566,#762,#1574,.T.);
#1574 = LINE('',#1575,#1576);
#1575 = CARTESIAN_POINT('',(-0.608243103516,1.359121551758,1.2));
#1576 = VECTOR('',#1577,1.);
#1577 = DIRECTION('',(0.707106781187,0.707106781187,0.));
#1578 = PLANE('',#1579);
#1579 = AXIS2_PLACEMENT_3D('',#1580,#1581,#1582);
#1580 = CARTESIAN_POINT('',(-0.559121551758,1.408243103516,1.2));
#1581 = DIRECTION('',(0.,0.,1.));
#1582 = DIRECTION('',(0.369013679242,-0.929423963825,0.));
#1583 = ADVANCED_FACE('',(#1584),#1594,.F.);
#1584 = FACE_BOUND('',#1585,.F.);
#1585 = EDGE_LOOP('',(#1586,#1587,#1588,#1589));
#1586 = ORIENTED_EDGE('',*,*,#784,.T.);
#1587 = ORIENTED_EDGE('',*,*,#769,.T.);
#1588 = ORIENTED_EDGE('',*,*,#1573,.F.);
#1589 = ORIENTED_EDGE('',*,*,#1590,.F.);
#1590 = EDGE_CURVE('',#785,#1566,#1591,.T.);
#1591 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1592,#1593),.UNSPECIFIED.,.F.,
.F.,(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#1592 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#1593 = CARTESIAN_POINT('',(-0.608243103516,1.359121551758,1.2));
#1594 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1595,#1596)
,(#1597,#1598
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.116672618896),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#1595 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#1596 = CARTESIAN_POINT('',(-0.608243103516,1.359121551758,1.2));
#1597 = CARTESIAN_POINT('',(-0.5925,1.475,0.725));
#1598 = CARTESIAN_POINT('',(-0.559121551758,1.408243103516,1.2));
#1599 = ADVANCED_FACE('',(#1600),#1617,.F.);
#1600 = FACE_BOUND('',#1601,.F.);
#1601 = EDGE_LOOP('',(#1602,#1603,#1609,#1610,#1615,#1616));
#1602 = ORIENTED_EDGE('',*,*,#815,.T.);
#1603 = ORIENTED_EDGE('',*,*,#1604,.T.);
#1604 = EDGE_CURVE('',#808,#980,#1605,.T.);
#1605 = LINE('',#1606,#1607);
#1606 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#1607 = VECTOR('',#1608,1.);
#1608 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1609 = ORIENTED_EDGE('',*,*,#995,.T.);
#1610 = ORIENTED_EDGE('',*,*,#1611,.T.);
#1611 = EDGE_CURVE('',#989,#1534,#1612,.T.);
#1612 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1613,#1614),.UNSPECIFIED.,.F.,
.F.,(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#1613 = CARTESIAN_POINT('',(0.675,-1.3925,0.725));
#1614 = CARTESIAN_POINT('',(0.608243103516,-1.359121551758,1.2));
#1615 = ORIENTED_EDGE('',*,*,#1533,.F.);
#1616 = ORIENTED_EDGE('',*,*,#1509,.F.);
#1617 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1618,#1619)
,(#1620,#1621
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.785),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#1618 = CARTESIAN_POINT('',(0.675,1.3925,0.725));
#1619 = CARTESIAN_POINT('',(0.608243103516,1.359121551758,1.2));
#1620 = CARTESIAN_POINT('',(0.675,-1.3925,0.725));
#1621 = CARTESIAN_POINT('',(0.608243103516,-1.359121551758,1.2));
#1622 = ADVANCED_FACE('',(#1623),#1634,.F.);
#1623 = FACE_BOUND('',#1624,.F.);
#1624 = EDGE_LOOP('',(#1625,#1626,#1632,#1633));
#1625 = ORIENTED_EDGE('',*,*,#807,.F.);
#1626 = ORIENTED_EDGE('',*,*,#1627,.T.);
#1627 = EDGE_CURVE('',#432,#454,#1628,.T.);
#1628 = LINE('',#1629,#1630);
#1629 = CARTESIAN_POINT('',(0.7,0.2,0.725));
#1630 = VECTOR('',#1631,1.);
#1631 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1632 = ORIENTED_EDGE('',*,*,#979,.T.);
#1633 = ORIENTED_EDGE('',*,*,#1604,.F.);
#1634 = PLANE('',#1635);
#1635 = AXIS2_PLACEMENT_3D('',#1636,#1637,#1638);
#1636 = CARTESIAN_POINT('',(0.7,0.2,0.725));
#1637 = DIRECTION('',(0.,0.,-1.));
#1638 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#1639 = ADVANCED_FACE('',(#1640),#1651,.T.);
#1640 = FACE_BOUND('',#1641,.F.);
#1641 = EDGE_LOOP('',(#1642,#1648,#1649,#1650));
#1642 = ORIENTED_EDGE('',*,*,#1643,.T.);
#1643 = EDGE_CURVE('',#882,#957,#1644,.T.);
#1644 = LINE('',#1645,#1646);
#1645 = CARTESIAN_POINT('',(0.95,0.2,0.475));
#1646 = VECTOR('',#1647,1.);
#1647 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1648 = ORIENTED_EDGE('',*,*,#964,.T.);
#1649 = ORIENTED_EDGE('',*,*,#1627,.F.);
#1650 = ORIENTED_EDGE('',*,*,#889,.F.);
#1651 = CYLINDRICAL_SURFACE('',#1652,0.25);
#1652 = AXIS2_PLACEMENT_3D('',#1653,#1654,#1655);
#1653 = CARTESIAN_POINT('',(0.7,0.2,0.475));
#1654 = DIRECTION('',(-2.22044604925E-16,1.,-0.));
#1655 = DIRECTION('',(1.,2.22044604925E-16,0.));
#1656 = ADVANCED_FACE('',(#1657),#1668,.F.);
#1657 = FACE_BOUND('',#1658,.F.);
#1658 = EDGE_LOOP('',(#1659,#1665,#1666,#1667));
#1659 = ORIENTED_EDGE('',*,*,#1660,.T.);
#1660 = EDGE_CURVE('',#873,#948,#1661,.T.);
#1661 = LINE('',#1662,#1663);
#1662 = CARTESIAN_POINT('',(0.95,0.2,0.25));
#1663 = VECTOR('',#1664,1.);
#1664 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1665 = ORIENTED_EDGE('',*,*,#956,.T.);
#1666 = ORIENTED_EDGE('',*,*,#1643,.F.);
#1667 = ORIENTED_EDGE('',*,*,#881,.F.);
#1668 = PLANE('',#1669);
#1669 = AXIS2_PLACEMENT_3D('',#1670,#1671,#1672);
#1670 = CARTESIAN_POINT('',(0.95,0.2,0.25));
#1671 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#1672 = DIRECTION('',(0.,0.,1.));
#1673 = ADVANCED_FACE('',(#1674),#1685,.F.);
#1674 = FACE_BOUND('',#1675,.F.);
#1675 = EDGE_LOOP('',(#1676,#1682,#1683,#1684));
#1676 = ORIENTED_EDGE('',*,*,#1677,.T.);
#1677 = EDGE_CURVE('',#865,#940,#1678,.T.);
#1678 = LINE('',#1679,#1680);
#1679 = CARTESIAN_POINT('',(1.05,0.2,0.15));
#1680 = VECTOR('',#1681,1.);
#1681 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1682 = ORIENTED_EDGE('',*,*,#947,.T.);
#1683 = ORIENTED_EDGE('',*,*,#1660,.F.);
#1684 = ORIENTED_EDGE('',*,*,#872,.F.);
#1685 = CYLINDRICAL_SURFACE('',#1686,0.1);
#1686 = AXIS2_PLACEMENT_3D('',#1687,#1688,#1689);
#1687 = CARTESIAN_POINT('',(1.05,0.2,0.25));
#1688 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#1689 = DIRECTION('',(-2.22044604925E-15,-4.930380657631E-31,-1.));
#1690 = ADVANCED_FACE('',(#1691),#1702,.F.);
#1691 = FACE_BOUND('',#1692,.F.);
#1692 = EDGE_LOOP('',(#1693,#1699,#1700,#1701));
#1693 = ORIENTED_EDGE('',*,*,#1694,.T.);
#1694 = EDGE_CURVE('',#857,#932,#1695,.T.);
#1695 = LINE('',#1696,#1697);
#1696 = CARTESIAN_POINT('',(1.25,0.2,0.15));
#1697 = VECTOR('',#1698,1.);
#1698 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1699 = ORIENTED_EDGE('',*,*,#939,.T.);
#1700 = ORIENTED_EDGE('',*,*,#1677,.F.);
#1701 = ORIENTED_EDGE('',*,*,#864,.F.);
#1702 = PLANE('',#1703);
#1703 = AXIS2_PLACEMENT_3D('',#1704,#1705,#1706);
#1704 = CARTESIAN_POINT('',(1.25,0.2,0.15));
#1705 = DIRECTION('',(0.,0.,-1.));
#1706 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#1707 = ADVANCED_FACE('',(#1708),#1719,.F.);
#1708 = FACE_BOUND('',#1709,.F.);
#1709 = EDGE_LOOP('',(#1710,#1716,#1717,#1718));
#1710 = ORIENTED_EDGE('',*,*,#1711,.T.);
#1711 = EDGE_CURVE('',#849,#924,#1712,.T.);
#1712 = LINE('',#1713,#1714);
#1713 = CARTESIAN_POINT('',(1.25,0.2,0.));
#1714 = VECTOR('',#1715,1.);
#1715 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1716 = ORIENTED_EDGE('',*,*,#931,.T.);
#1717 = ORIENTED_EDGE('',*,*,#1694,.F.);
#1718 = ORIENTED_EDGE('',*,*,#856,.F.);
#1719 = PLANE('',#1720);
#1720 = AXIS2_PLACEMENT_3D('',#1721,#1722,#1723);
#1721 = CARTESIAN_POINT('',(1.25,0.2,0.));
#1722 = DIRECTION('',(-1.,-2.22044604925E-16,0.));
#1723 = DIRECTION('',(0.,0.,1.));
#1724 = ADVANCED_FACE('',(#1725),#1736,.F.);
#1725 = FACE_BOUND('',#1726,.F.);
#1726 = EDGE_LOOP('',(#1727,#1733,#1734,#1735));
#1727 = ORIENTED_EDGE('',*,*,#1728,.T.);
#1728 = EDGE_CURVE('',#840,#915,#1729,.T.);
#1729 = LINE('',#1730,#1731);
#1730 = CARTESIAN_POINT('',(1.05,0.2,0.));
#1731 = VECTOR('',#1732,1.);
#1732 = DIRECTION('',(2.22044604925E-16,-1.,0.));
#1733 = ORIENTED_EDGE('',*,*,#923,.T.);
#1734 = ORIENTED_EDGE('',*,*,#1711,.F.);
#1735 = ORIENTED_EDGE('',*,*,#848,.F.);
#1736 = PLANE('',#1737);
#1737 = AXIS2_PLACEMENT_3D('',#1738,#1739,#1740);
#1738 = CARTESIAN_POINT('',(1.05,0.2,0.));
#1739 = DIRECTION('',(0.,0.,1.));
#1740 = DIRECTION('',(1.,2.22044604925E-16,0.));
#1741 = ADVANCED_FACE('',(#1742),#1748,.T.);
#1742 = FACE_BOUND('',#1743,.F.);
#1743 = EDGE_LOOP('',(#1744,#1745,#1746,#1747));
#1744 = ORIENTED_EDGE('',*,*,#1011,.T.);
#1745 = ORIENTED_EDGE('',*,*,#914,.T.);
#1746 = ORIENTED_EDGE('',*,*,#1728,.F.);
#1747 = ORIENTED_EDGE('',*,*,#839,.F.);
#1748 = CYLINDRICAL_SURFACE('',#1749,0.25);
#1749 = AXIS2_PLACEMENT_3D('',#1750,#1751,#1752);
#1750 = CARTESIAN_POINT('',(1.05,0.2,0.25));
#1751 = DIRECTION('',(-2.22044604925E-16,1.,-0.));
#1752 = DIRECTION('',(-1.,-2.22044604925E-16,-4.440892098501E-16));
#1753 = ADVANCED_FACE('',(#1754),#1764,.F.);
#1754 = FACE_BOUND('',#1755,.F.);
#1755 = EDGE_LOOP('',(#1756,#1757,#1762,#1763));
#1756 = ORIENTED_EDGE('',*,*,#1034,.T.);
#1757 = ORIENTED_EDGE('',*,*,#1758,.T.);
#1758 = EDGE_CURVE('',#1028,#1542,#1759,.T.);
#1759 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1760,#1761),.UNSPECIFIED.,.F.,
.F.,(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#1760 = CARTESIAN_POINT('',(0.5925,-1.475,0.725));
#1761 = CARTESIAN_POINT('',(0.559121551758,-1.408243103516,1.2));
#1762 = ORIENTED_EDGE('',*,*,#1541,.F.);
#1763 = ORIENTED_EDGE('',*,*,#1611,.F.);
#1764 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1765,#1766)
,(#1767,#1768
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.116672618896),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#1765 = CARTESIAN_POINT('',(0.675,-1.3925,0.725));
#1766 = CARTESIAN_POINT('',(0.608243103516,-1.359121551758,1.2));
#1767 = CARTESIAN_POINT('',(0.5925,-1.475,0.725));
#1768 = CARTESIAN_POINT('',(0.559121551758,-1.408243103516,1.2));
#1769 = ADVANCED_FACE('',(#1770),#1780,.F.);
#1770 = FACE_BOUND('',#1771,.F.);
#1771 = EDGE_LOOP('',(#1772,#1773,#1778,#1779));
#1772 = ORIENTED_EDGE('',*,*,#1057,.T.);
#1773 = ORIENTED_EDGE('',*,*,#1774,.T.);
#1774 = EDGE_CURVE('',#1051,#1550,#1775,.T.);
#1775 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1776,#1777),.UNSPECIFIED.,.F.,
.F.,(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#1776 = CARTESIAN_POINT('',(-0.5925,-1.475,0.725));
#1777 = CARTESIAN_POINT('',(-0.559121551758,-1.408243103516,1.2));
#1778 = ORIENTED_EDGE('',*,*,#1549,.F.);
#1779 = ORIENTED_EDGE('',*,*,#1758,.F.);
#1780 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1781,#1782)
,(#1783,#1784
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,1.185),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#1781 = CARTESIAN_POINT('',(0.5925,-1.475,0.725));
#1782 = CARTESIAN_POINT('',(0.559121551758,-1.408243103516,1.2));
#1783 = CARTESIAN_POINT('',(-0.5925,-1.475,0.725));
#1784 = CARTESIAN_POINT('',(-0.559121551758,-1.408243103516,1.2));
#1785 = ADVANCED_FACE('',(#1786),#1796,.F.);
#1786 = FACE_BOUND('',#1787,.F.);
#1787 = EDGE_LOOP('',(#1788,#1789,#1794,#1795));
#1788 = ORIENTED_EDGE('',*,*,#1080,.T.);
#1789 = ORIENTED_EDGE('',*,*,#1790,.T.);
#1790 = EDGE_CURVE('',#1074,#1558,#1791,.T.);
#1791 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1792,#1793),.UNSPECIFIED.,.F.,
.F.,(2,2),(0.,1.),.PIECEWISE_BEZIER_KNOTS.);
#1792 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1793 = CARTESIAN_POINT('',(-0.608243103516,-1.359121551758,1.2));
#1794 = ORIENTED_EDGE('',*,*,#1557,.F.);
#1795 = ORIENTED_EDGE('',*,*,#1774,.F.);
#1796 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1797,#1798)
,(#1799,#1800
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.116672618896),(0.,1.)
,.PIECEWISE_BEZIER_KNOTS.);
#1797 = CARTESIAN_POINT('',(-0.5925,-1.475,0.725));
#1798 = CARTESIAN_POINT('',(-0.559121551758,-1.408243103516,1.2));
#1799 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1800 = CARTESIAN_POINT('',(-0.608243103516,-1.359121551758,1.2));
#1801 = ADVANCED_FACE('',(#1802),#1822,.F.);
#1802 = FACE_BOUND('',#1803,.F.);
#1803 = EDGE_LOOP('',(#1804,#1805,#1811,#1812,#1818,#1819,#1820,#1821));
#1804 = ORIENTED_EDGE('',*,*,#1105,.T.);
#1805 = ORIENTED_EDGE('',*,*,#1806,.T.);
#1806 = EDGE_CURVE('',#1098,#1478,#1807,.T.);
#1807 = LINE('',#1808,#1809);
#1808 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1809 = VECTOR('',#1810,1.);
#1810 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#1811 = ORIENTED_EDGE('',*,*,#1494,.T.);
#1812 = ORIENTED_EDGE('',*,*,#1813,.T.);
#1813 = EDGE_CURVE('',#1487,#1270,#1814,.T.);
#1814 = LINE('',#1815,#1816);
#1815 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1816 = VECTOR('',#1817,1.);
#1817 = DIRECTION('',(-2.22044604925E-16,1.,0.));
#1818 = ORIENTED_EDGE('',*,*,#1279,.T.);
#1819 = ORIENTED_EDGE('',*,*,#1590,.T.);
#1820 = ORIENTED_EDGE('',*,*,#1565,.F.);
#1821 = ORIENTED_EDGE('',*,*,#1790,.F.);
#1822 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
(#1823,#1824)
,(#1825,#1826
)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.785),(0.,1.),
.PIECEWISE_BEZIER_KNOTS.);
#1823 = CARTESIAN_POINT('',(-0.675,-1.3925,0.725));
#1824 = CARTESIAN_POINT('',(-0.608243103516,-1.359121551758,1.2));
#1825 = CARTESIAN_POINT('',(-0.675,1.3925,0.725));
#1826 = CARTESIAN_POINT('',(-0.608243103516,1.359121551758,1.2));
#1827 = ADVANCED_FACE('',(#1828),#1839,.F.);
#1828 = FACE_BOUND('',#1829,.F.);
#1829 = EDGE_LOOP('',(#1830,#1831,#1837,#1838));
#1830 = ORIENTED_EDGE('',*,*,#1097,.F.);
#1831 = ORIENTED_EDGE('',*,*,#1832,.T.);
#1832 = EDGE_CURVE('',#588,#700,#1833,.T.);
#1833 = LINE('',#1834,#1835);
#1834 = CARTESIAN_POINT('',(-0.7,-1.15,0.725));
#1835 = VECTOR('',#1836,1.);
#1836 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1837 = ORIENTED_EDGE('',*,*,#1477,.T.);
#1838 = ORIENTED_EDGE('',*,*,#1806,.F.);
#1839 = PLANE('',#1840);
#1840 = AXIS2_PLACEMENT_3D('',#1841,#1842,#1843);
#1841 = CARTESIAN_POINT('',(-0.7,-1.15,0.725));
#1842 = DIRECTION('',(-0.,0.,-1.));
#1843 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1844 = ADVANCED_FACE('',(#1845),#1856,.T.);
#1845 = FACE_BOUND('',#1846,.F.);
#1846 = EDGE_LOOP('',(#1847,#1853,#1854,#1855));
#1847 = ORIENTED_EDGE('',*,*,#1848,.T.);
#1848 = EDGE_CURVE('',#1172,#1346,#1849,.T.);
#1849 = LINE('',#1850,#1851);
#1850 = CARTESIAN_POINT('',(-0.95,-1.15,0.475));
#1851 = VECTOR('',#1852,1.);
#1852 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1853 = ORIENTED_EDGE('',*,*,#1353,.T.);
#1854 = ORIENTED_EDGE('',*,*,#1832,.F.);
#1855 = ORIENTED_EDGE('',*,*,#1179,.F.);
#1856 = CYLINDRICAL_SURFACE('',#1857,0.25);
#1857 = AXIS2_PLACEMENT_3D('',#1858,#1859,#1860);
#1858 = CARTESIAN_POINT('',(-0.7,-1.15,0.475));
#1859 = DIRECTION('',(9.95799250103E-17,-1.,-0.));
#1860 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1861 = ADVANCED_FACE('',(#1862),#1873,.F.);
#1862 = FACE_BOUND('',#1863,.F.);
#1863 = EDGE_LOOP('',(#1864,#1870,#1871,#1872));
#1864 = ORIENTED_EDGE('',*,*,#1865,.T.);
#1865 = EDGE_CURVE('',#1163,#1337,#1866,.T.);
#1866 = LINE('',#1867,#1868);
#1867 = CARTESIAN_POINT('',(-0.95,-1.15,0.25));
#1868 = VECTOR('',#1869,1.);
#1869 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1870 = ORIENTED_EDGE('',*,*,#1345,.T.);
#1871 = ORIENTED_EDGE('',*,*,#1848,.F.);
#1872 = ORIENTED_EDGE('',*,*,#1171,.F.);
#1873 = PLANE('',#1874);
#1874 = AXIS2_PLACEMENT_3D('',#1875,#1876,#1877);
#1875 = CARTESIAN_POINT('',(-0.95,-1.15,0.25));
#1876 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1877 = DIRECTION('',(0.,0.,1.));
#1878 = ADVANCED_FACE('',(#1879),#1890,.F.);
#1879 = FACE_BOUND('',#1880,.F.);
#1880 = EDGE_LOOP('',(#1881,#1887,#1888,#1889));
#1881 = ORIENTED_EDGE('',*,*,#1882,.T.);
#1882 = EDGE_CURVE('',#1155,#1329,#1883,.T.);
#1883 = LINE('',#1884,#1885);
#1884 = CARTESIAN_POINT('',(-1.05,-1.15,0.15));
#1885 = VECTOR('',#1886,1.);
#1886 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1887 = ORIENTED_EDGE('',*,*,#1336,.T.);
#1888 = ORIENTED_EDGE('',*,*,#1865,.F.);
#1889 = ORIENTED_EDGE('',*,*,#1162,.F.);
#1890 = CYLINDRICAL_SURFACE('',#1891,0.1);
#1891 = AXIS2_PLACEMENT_3D('',#1892,#1893,#1894);
#1892 = CARTESIAN_POINT('',(-1.05,-1.15,0.25));
#1893 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#1894 = DIRECTION('',(2.22044604925E-15,2.211118510738E-31,-1.));
#1895 = ADVANCED_FACE('',(#1896),#1907,.F.);
#1896 = FACE_BOUND('',#1897,.F.);
#1897 = EDGE_LOOP('',(#1898,#1904,#1905,#1906));
#1898 = ORIENTED_EDGE('',*,*,#1899,.T.);
#1899 = EDGE_CURVE('',#1147,#1321,#1900,.T.);
#1900 = LINE('',#1901,#1902);
#1901 = CARTESIAN_POINT('',(-1.25,-1.15,0.15));
#1902 = VECTOR('',#1903,1.);
#1903 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1904 = ORIENTED_EDGE('',*,*,#1328,.T.);
#1905 = ORIENTED_EDGE('',*,*,#1882,.F.);
#1906 = ORIENTED_EDGE('',*,*,#1154,.F.);
#1907 = PLANE('',#1908);
#1908 = AXIS2_PLACEMENT_3D('',#1909,#1910,#1911);
#1909 = CARTESIAN_POINT('',(-1.25,-1.15,0.15));
#1910 = DIRECTION('',(-0.,0.,-1.));
#1911 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1912 = ADVANCED_FACE('',(#1913),#1924,.F.);
#1913 = FACE_BOUND('',#1914,.F.);
#1914 = EDGE_LOOP('',(#1915,#1921,#1922,#1923));
#1915 = ORIENTED_EDGE('',*,*,#1916,.T.);
#1916 = EDGE_CURVE('',#1139,#1313,#1917,.T.);
#1917 = LINE('',#1918,#1919);
#1918 = CARTESIAN_POINT('',(-1.25,-1.15,0.));
#1919 = VECTOR('',#1920,1.);
#1920 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1921 = ORIENTED_EDGE('',*,*,#1320,.T.);
#1922 = ORIENTED_EDGE('',*,*,#1899,.F.);
#1923 = ORIENTED_EDGE('',*,*,#1146,.F.);
#1924 = PLANE('',#1925);
#1925 = AXIS2_PLACEMENT_3D('',#1926,#1927,#1928);
#1926 = CARTESIAN_POINT('',(-1.25,-1.15,0.));
#1927 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1928 = DIRECTION('',(0.,0.,1.));
#1929 = ADVANCED_FACE('',(#1930),#1941,.F.);
#1930 = FACE_BOUND('',#1931,.F.);
#1931 = EDGE_LOOP('',(#1932,#1938,#1939,#1940));
#1932 = ORIENTED_EDGE('',*,*,#1933,.T.);
#1933 = EDGE_CURVE('',#1130,#1304,#1934,.T.);
#1934 = LINE('',#1935,#1936);
#1935 = CARTESIAN_POINT('',(-1.05,-1.15,0.));
#1936 = VECTOR('',#1937,1.);
#1937 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1938 = ORIENTED_EDGE('',*,*,#1312,.T.);
#1939 = ORIENTED_EDGE('',*,*,#1916,.F.);
#1940 = ORIENTED_EDGE('',*,*,#1138,.F.);
#1941 = PLANE('',#1942);
#1942 = AXIS2_PLACEMENT_3D('',#1943,#1944,#1945);
#1943 = CARTESIAN_POINT('',(-1.05,-1.15,0.));
#1944 = DIRECTION('',(0.,0.,1.));
#1945 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1946 = ADVANCED_FACE('',(#1947),#1953,.T.);
#1947 = FACE_BOUND('',#1948,.F.);
#1948 = EDGE_LOOP('',(#1949,#1950,#1951,#1952));
#1949 = ORIENTED_EDGE('',*,*,#1370,.T.);
#1950 = ORIENTED_EDGE('',*,*,#1303,.T.);
#1951 = ORIENTED_EDGE('',*,*,#1933,.F.);
#1952 = ORIENTED_EDGE('',*,*,#1129,.F.);
#1953 = CYLINDRICAL_SURFACE('',#1954,0.25);
#1954 = AXIS2_PLACEMENT_3D('',#1955,#1956,#1957);
#1955 = CARTESIAN_POINT('',(-1.05,-1.15,0.25));
#1956 = DIRECTION('',(9.95799250103E-17,-1.,-0.));
#1957 = DIRECTION('',(1.,9.95799250103E-17,-4.440892098501E-16));
#1958 = ADVANCED_FACE('',(#1959),#1975,.T.);
#1959 = FACE_BOUND('',#1960,.F.);
#1960 = EDGE_LOOP('',(#1961,#1967,#1968,#1974));
#1961 = ORIENTED_EDGE('',*,*,#1962,.T.);
#1962 = EDGE_CURVE('',#1438,#1247,#1963,.T.);
#1963 = LINE('',#1964,#1965);
#1964 = CARTESIAN_POINT('',(-0.95,0.75,0.475));
#1965 = VECTOR('',#1966,1.);
#1966 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1967 = ORIENTED_EDGE('',*,*,#1254,.T.);
#1968 = ORIENTED_EDGE('',*,*,#1969,.F.);
#1969 = EDGE_CURVE('',#709,#610,#1970,.T.);
#1970 = LINE('',#1971,#1972);
#1971 = CARTESIAN_POINT('',(-0.7,0.75,0.725));
#1972 = VECTOR('',#1973,1.);
#1973 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1974 = ORIENTED_EDGE('',*,*,#1445,.F.);
#1975 = CYLINDRICAL_SURFACE('',#1976,0.25);
#1976 = AXIS2_PLACEMENT_3D('',#1977,#1978,#1979);
#1977 = CARTESIAN_POINT('',(-0.7,0.75,0.475));
#1978 = DIRECTION('',(9.95799250103E-17,-1.,-0.));
#1979 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#1980 = ADVANCED_FACE('',(#1981),#1992,.F.);
#1981 = FACE_BOUND('',#1982,.F.);
#1982 = EDGE_LOOP('',(#1983,#1989,#1990,#1991));
#1983 = ORIENTED_EDGE('',*,*,#1984,.T.);
#1984 = EDGE_CURVE('',#1429,#1238,#1985,.T.);
#1985 = LINE('',#1986,#1987);
#1986 = CARTESIAN_POINT('',(-0.95,0.75,0.25));
#1987 = VECTOR('',#1988,1.);
#1988 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#1989 = ORIENTED_EDGE('',*,*,#1246,.T.);
#1990 = ORIENTED_EDGE('',*,*,#1962,.F.);
#1991 = ORIENTED_EDGE('',*,*,#1437,.F.);
#1992 = PLANE('',#1993);
#1993 = AXIS2_PLACEMENT_3D('',#1994,#1995,#1996);
#1994 = CARTESIAN_POINT('',(-0.95,0.75,0.25));
#1995 = DIRECTION('',(1.,9.95799250103E-17,0.));
#1996 = DIRECTION('',(0.,0.,1.));
#1997 = ADVANCED_FACE('',(#1998),#2009,.F.);
#1998 = FACE_BOUND('',#1999,.F.);
#1999 = EDGE_LOOP('',(#2000,#2006,#2007,#2008));
#2000 = ORIENTED_EDGE('',*,*,#2001,.T.);
#2001 = EDGE_CURVE('',#1421,#1230,#2002,.T.);
#2002 = LINE('',#2003,#2004);
#2003 = CARTESIAN_POINT('',(-1.05,0.75,0.15));
#2004 = VECTOR('',#2005,1.);
#2005 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#2006 = ORIENTED_EDGE('',*,*,#1237,.T.);
#2007 = ORIENTED_EDGE('',*,*,#1984,.F.);
#2008 = ORIENTED_EDGE('',*,*,#1428,.F.);
#2009 = CYLINDRICAL_SURFACE('',#2010,0.1);
#2010 = AXIS2_PLACEMENT_3D('',#2011,#2012,#2013);
#2011 = CARTESIAN_POINT('',(-1.05,0.75,0.25));
#2012 = DIRECTION('',(9.95799250103E-17,-1.,0.));
#2013 = DIRECTION('',(2.22044604925E-15,2.211118510738E-31,-1.));
#2014 = ADVANCED_FACE('',(#2015),#2026,.F.);
#2015 = FACE_BOUND('',#2016,.F.);
#2016 = EDGE_LOOP('',(#2017,#2023,#2024,#2025));
#2017 = ORIENTED_EDGE('',*,*,#2018,.T.);
#2018 = EDGE_CURVE('',#1413,#1222,#2019,.T.);
#2019 = LINE('',#2020,#2021);
#2020 = CARTESIAN_POINT('',(-1.25,0.75,0.15));
#2021 = VECTOR('',#2022,1.);
#2022 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#2023 = ORIENTED_EDGE('',*,*,#1229,.T.);
#2024 = ORIENTED_EDGE('',*,*,#2001,.F.);
#2025 = ORIENTED_EDGE('',*,*,#1420,.F.);
#2026 = PLANE('',#2027);
#2027 = AXIS2_PLACEMENT_3D('',#2028,#2029,#2030);
#2028 = CARTESIAN_POINT('',(-1.25,0.75,0.15));
#2029 = DIRECTION('',(-0.,0.,-1.));
#2030 = DIRECTION('',(1.,9.95799250103E-17,0.));
#2031 = ADVANCED_FACE('',(#2032),#2043,.F.);
#2032 = FACE_BOUND('',#2033,.F.);
#2033 = EDGE_LOOP('',(#2034,#2040,#2041,#2042));
#2034 = ORIENTED_EDGE('',*,*,#2035,.T.);
#2035 = EDGE_CURVE('',#1405,#1214,#2036,.T.);
#2036 = LINE('',#2037,#2038);
#2037 = CARTESIAN_POINT('',(-1.25,0.75,0.));
#2038 = VECTOR('',#2039,1.);
#2039 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#2040 = ORIENTED_EDGE('',*,*,#1221,.T.);
#2041 = ORIENTED_EDGE('',*,*,#2018,.F.);
#2042 = ORIENTED_EDGE('',*,*,#1412,.F.);
#2043 = PLANE('',#2044);
#2044 = AXIS2_PLACEMENT_3D('',#2045,#2046,#2047);
#2045 = CARTESIAN_POINT('',(-1.25,0.75,0.));
#2046 = DIRECTION('',(1.,9.95799250103E-17,0.));
#2047 = DIRECTION('',(0.,0.,1.));
#2048 = ADVANCED_FACE('',(#2049),#2060,.F.);
#2049 = FACE_BOUND('',#2050,.F.);
#2050 = EDGE_LOOP('',(#2051,#2057,#2058,#2059));
#2051 = ORIENTED_EDGE('',*,*,#2052,.T.);
#2052 = EDGE_CURVE('',#1396,#1205,#2053,.T.);
#2053 = LINE('',#2054,#2055);
#2054 = CARTESIAN_POINT('',(-1.05,0.75,0.));
#2055 = VECTOR('',#2056,1.);
#2056 = DIRECTION('',(-9.95799250103E-17,1.,0.));
#2057 = ORIENTED_EDGE('',*,*,#1213,.T.);
#2058 = ORIENTED_EDGE('',*,*,#2035,.F.);
#2059 = ORIENTED_EDGE('',*,*,#1404,.F.);
#2060 = PLANE('',#2061);
#2061 = AXIS2_PLACEMENT_3D('',#2062,#2063,#2064);
#2062 = CARTESIAN_POINT('',(-1.05,0.75,0.));
#2063 = DIRECTION('',(0.,0.,1.));
#2064 = DIRECTION('',(-1.,-9.95799250103E-17,0.));
#2065 = ADVANCED_FACE('',(#2066),#2072,.T.);
#2066 = FACE_BOUND('',#2067,.F.);
#2067 = EDGE_LOOP('',(#2068,#2069,#2070,#2071));
#2068 = ORIENTED_EDGE('',*,*,#1462,.T.);
#2069 = ORIENTED_EDGE('',*,*,#1204,.T.);
#2070 = ORIENTED_EDGE('',*,*,#2052,.F.);
#2071 = ORIENTED_EDGE('',*,*,#1395,.F.);
#2072 = CYLINDRICAL_SURFACE('',#2073,0.25);
#2073 = AXIS2_PLACEMENT_3D('',#2074,#2075,#2076);
#2074 = CARTESIAN_POINT('',(-1.05,0.75,0.25));
#2075 = DIRECTION('',(9.95799250103E-17,-1.,-0.));
#2076 = DIRECTION('',(1.,9.95799250103E-17,-4.440892098501E-16));
#2077 = ADVANCED_FACE('',(#2078),#2084,.F.);
#2078 = FACE_BOUND('',#2079,.F.);
#2079 = EDGE_LOOP('',(#2080,#2081,#2082,#2083));
#2080 = ORIENTED_EDGE('',*,*,#1486,.F.);
#2081 = ORIENTED_EDGE('',*,*,#1969,.T.);
#2082 = ORIENTED_EDGE('',*,*,#1269,.T.);
#2083 = ORIENTED_EDGE('',*,*,#1813,.F.);
#2084 = PLANE('',#2085);
#2085 = AXIS2_PLACEMENT_3D('',#2086,#2087,#2088);
#2086 = CARTESIAN_POINT('',(-0.7,0.75,0.725));
#2087 = DIRECTION('',(-0.,0.,-1.));
#2088 = DIRECTION('',(1.,9.95799250103E-17,0.));
#2089 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2093))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2090,#2091,#2092)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2090 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2091 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2092 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2093 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2090,
'distance_accuracy_value','confusion accuracy');
#2094 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#2095 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
#2096,#2104,#2111,#2118,#2125,#2132,#2139,#2146,#2153,#2160,#2167,
#2174,#2181,#2188,#2195,#2202,#2210,#2217,#2224,#2231,#2238,#2245,
#2252,#2259,#2266,#2273,#2280,#2287,#2294,#2301,#2308,#2315,#2322,
#2329,#2336,#2343,#2350,#2357,#2364,#2371,#2378,#2385,#2392,#2399,
#2406,#2413,#2420,#2427,#2434,#2441,#2448,#2455,#2462,#2469,#2476,
#2483,#2490,#2497,#2504,#2511,#2518,#2525,#2532,#2539,#2546,#2553,
#2560,#2567,#2574,#2581,#2588,#2595,#2602,#2609,#2616,#2623),#2089);
#2096 = STYLED_ITEM('color',(#2097),#17);
#2097 = PRESENTATION_STYLE_ASSIGNMENT((#2098));
#2098 = SURFACE_STYLE_USAGE(.BOTH.,#2099);
#2099 = SURFACE_SIDE_STYLE('',(#2100));
#2100 = SURFACE_STYLE_FILL_AREA(#2101);
#2101 = FILL_AREA_STYLE('',(#2102));
#2102 = FILL_AREA_STYLE_COLOUR('',#2103);
#2103 = COLOUR_RGB('',0.145098045468,0.141176477075,0.141176477075);
#2104 = STYLED_ITEM('color',(#2105),#55);
#2105 = PRESENTATION_STYLE_ASSIGNMENT((#2106));
#2106 = SURFACE_STYLE_USAGE(.BOTH.,#2107);
#2107 = SURFACE_SIDE_STYLE('',(#2108));
#2108 = SURFACE_STYLE_FILL_AREA(#2109);
#2109 = FILL_AREA_STYLE('',(#2110));
#2110 = FILL_AREA_STYLE_COLOUR('',#2103);
#2111 = STYLED_ITEM('color',(#2112),#118);
#2112 = PRESENTATION_STYLE_ASSIGNMENT((#2113));
#2113 = SURFACE_STYLE_USAGE(.BOTH.,#2114);
#2114 = SURFACE_SIDE_STYLE('',(#2115));
#2115 = SURFACE_STYLE_FILL_AREA(#2116);
#2116 = FILL_AREA_STYLE('',(#2117));
#2117 = FILL_AREA_STYLE_COLOUR('',#2103);
#2118 = STYLED_ITEM('color',(#2119),#141);
#2119 = PRESENTATION_STYLE_ASSIGNMENT((#2120));
#2120 = SURFACE_STYLE_USAGE(.BOTH.,#2121);
#2121 = SURFACE_SIDE_STYLE('',(#2122));
#2122 = SURFACE_STYLE_FILL_AREA(#2123);
#2123 = FILL_AREA_STYLE('',(#2124));
#2124 = FILL_AREA_STYLE_COLOUR('',#2103);
#2125 = STYLED_ITEM('color',(#2126),#170);
#2126 = PRESENTATION_STYLE_ASSIGNMENT((#2127));
#2127 = SURFACE_STYLE_USAGE(.BOTH.,#2128);
#2128 = SURFACE_SIDE_STYLE('',(#2129));
#2129 = SURFACE_STYLE_FILL_AREA(#2130);
#2130 = FILL_AREA_STYLE('',(#2131));
#2131 = FILL_AREA_STYLE_COLOUR('',#2103);
#2132 = STYLED_ITEM('color',(#2133),#193);
#2133 = PRESENTATION_STYLE_ASSIGNMENT((#2134));
#2134 = SURFACE_STYLE_USAGE(.BOTH.,#2135);
#2135 = SURFACE_SIDE_STYLE('',(#2136));
#2136 = SURFACE_STYLE_FILL_AREA(#2137);
#2137 = FILL_AREA_STYLE('',(#2138));
#2138 = FILL_AREA_STYLE_COLOUR('',#2103);
#2139 = STYLED_ITEM('color',(#2140),#232);
#2140 = PRESENTATION_STYLE_ASSIGNMENT((#2141));
#2141 = SURFACE_STYLE_USAGE(.BOTH.,#2142);
#2142 = SURFACE_SIDE_STYLE('',(#2143));
#2143 = SURFACE_STYLE_FILL_AREA(#2144);
#2144 = FILL_AREA_STYLE('',(#2145));
#2145 = FILL_AREA_STYLE_COLOUR('',#2103);
#2146 = STYLED_ITEM('color',(#2147),#255);
#2147 = PRESENTATION_STYLE_ASSIGNMENT((#2148));
#2148 = SURFACE_STYLE_USAGE(.BOTH.,#2149);
#2149 = SURFACE_SIDE_STYLE('',(#2150));
#2150 = SURFACE_STYLE_FILL_AREA(#2151);
#2151 = FILL_AREA_STYLE('',(#2152));
#2152 = FILL_AREA_STYLE_COLOUR('',#2103);
#2153 = STYLED_ITEM('color',(#2154),#278);
#2154 = PRESENTATION_STYLE_ASSIGNMENT((#2155));
#2155 = SURFACE_STYLE_USAGE(.BOTH.,#2156);
#2156 = SURFACE_SIDE_STYLE('',(#2157));
#2157 = SURFACE_STYLE_FILL_AREA(#2158);
#2158 = FILL_AREA_STYLE('',(#2159));
#2159 = FILL_AREA_STYLE_COLOUR('',#2103);
#2160 = STYLED_ITEM('color',(#2161),#301);
#2161 = PRESENTATION_STYLE_ASSIGNMENT((#2162));
#2162 = SURFACE_STYLE_USAGE(.BOTH.,#2163);
#2163 = SURFACE_SIDE_STYLE('',(#2164));
#2164 = SURFACE_STYLE_FILL_AREA(#2165);
#2165 = FILL_AREA_STYLE('',(#2166));
#2166 = FILL_AREA_STYLE_COLOUR('',#2103);
#2167 = STYLED_ITEM('color',(#2168),#350);
#2168 = PRESENTATION_STYLE_ASSIGNMENT((#2169));
#2169 = SURFACE_STYLE_USAGE(.BOTH.,#2170);
#2170 = SURFACE_SIDE_STYLE('',(#2171));
#2171 = SURFACE_STYLE_FILL_AREA(#2172);
#2172 = FILL_AREA_STYLE('',(#2173));
#2173 = FILL_AREA_STYLE_COLOUR('',#2103);
#2174 = STYLED_ITEM('color',(#2175),#373);
#2175 = PRESENTATION_STYLE_ASSIGNMENT((#2176));
#2176 = SURFACE_STYLE_USAGE(.BOTH.,#2177);
#2177 = SURFACE_SIDE_STYLE('',(#2178));
#2178 = SURFACE_STYLE_FILL_AREA(#2179);
#2179 = FILL_AREA_STYLE('',(#2180));
#2180 = FILL_AREA_STYLE_COLOUR('',#2103);
#2181 = STYLED_ITEM('color',(#2182),#402);
#2182 = PRESENTATION_STYLE_ASSIGNMENT((#2183));
#2183 = SURFACE_STYLE_USAGE(.BOTH.,#2184);
#2184 = SURFACE_SIDE_STYLE('',(#2185));
#2185 = SURFACE_STYLE_FILL_AREA(#2186);
#2186 = FILL_AREA_STYLE('',(#2187));
#2187 = FILL_AREA_STYLE_COLOUR('',#2103);
#2188 = STYLED_ITEM('color',(#2189),#425);
#2189 = PRESENTATION_STYLE_ASSIGNMENT((#2190));
#2190 = SURFACE_STYLE_USAGE(.BOTH.,#2191);
#2191 = SURFACE_SIDE_STYLE('',(#2192));
#2192 = SURFACE_STYLE_FILL_AREA(#2193);
#2193 = FILL_AREA_STYLE('',(#2194));
#2194 = FILL_AREA_STYLE_COLOUR('',#2103);
#2195 = STYLED_ITEM('color',(#2196),#449);
#2196 = PRESENTATION_STYLE_ASSIGNMENT((#2197));
#2197 = SURFACE_STYLE_USAGE(.BOTH.,#2198);
#2198 = SURFACE_SIDE_STYLE('',(#2199));
#2199 = SURFACE_STYLE_FILL_AREA(#2200);
#2200 = FILL_AREA_STYLE('',(#2201));
#2201 = FILL_AREA_STYLE_COLOUR('',#2103);
#2202 = STYLED_ITEM('color',(#2203),#479);
#2203 = PRESENTATION_STYLE_ASSIGNMENT((#2204));
#2204 = SURFACE_STYLE_USAGE(.BOTH.,#2205);
#2205 = SURFACE_SIDE_STYLE('',(#2206));
#2206 = SURFACE_STYLE_FILL_AREA(#2207);
#2207 = FILL_AREA_STYLE('',(#2208));
#2208 = FILL_AREA_STYLE_COLOUR('',#2209);
#2209 = COLOUR_RGB('',0.823529422283,0.819607853889,0.780392169952);
#2210 = STYLED_ITEM('color',(#2211),#512);
#2211 = PRESENTATION_STYLE_ASSIGNMENT((#2212));
#2212 = SURFACE_STYLE_USAGE(.BOTH.,#2213);
#2213 = SURFACE_SIDE_STYLE('',(#2214));
#2214 = SURFACE_STYLE_FILL_AREA(#2215);
#2215 = FILL_AREA_STYLE('',(#2216));
#2216 = FILL_AREA_STYLE_COLOUR('',#2103);
#2217 = STYLED_ITEM('color',(#2218),#535);
#2218 = PRESENTATION_STYLE_ASSIGNMENT((#2219));
#2219 = SURFACE_STYLE_USAGE(.BOTH.,#2220);
#2220 = SURFACE_SIDE_STYLE('',(#2221));
#2221 = SURFACE_STYLE_FILL_AREA(#2222);
#2222 = FILL_AREA_STYLE('',(#2223));
#2223 = FILL_AREA_STYLE_COLOUR('',#2103);
#2224 = STYLED_ITEM('color',(#2225),#558);
#2225 = PRESENTATION_STYLE_ASSIGNMENT((#2226));
#2226 = SURFACE_STYLE_USAGE(.BOTH.,#2227);
#2227 = SURFACE_SIDE_STYLE('',(#2228));
#2228 = SURFACE_STYLE_FILL_AREA(#2229);
#2229 = FILL_AREA_STYLE('',(#2230));
#2230 = FILL_AREA_STYLE_COLOUR('',#2103);
#2231 = STYLED_ITEM('color',(#2232),#581);
#2232 = PRESENTATION_STYLE_ASSIGNMENT((#2233));
#2233 = SURFACE_STYLE_USAGE(.BOTH.,#2234);
#2234 = SURFACE_SIDE_STYLE('',(#2235));
#2235 = SURFACE_STYLE_FILL_AREA(#2236);
#2236 = FILL_AREA_STYLE('',(#2237));
#2237 = FILL_AREA_STYLE_COLOUR('',#2103);
#2238 = STYLED_ITEM('color',(#2239),#605);
#2239 = PRESENTATION_STYLE_ASSIGNMENT((#2240));
#2240 = SURFACE_STYLE_USAGE(.BOTH.,#2241);
#2241 = SURFACE_SIDE_STYLE('',(#2242));
#2242 = SURFACE_STYLE_FILL_AREA(#2243);
#2243 = FILL_AREA_STYLE('',(#2244));
#2244 = FILL_AREA_STYLE_COLOUR('',#2103);
#2245 = STYLED_ITEM('color',(#2246),#629);
#2246 = PRESENTATION_STYLE_ASSIGNMENT((#2247));
#2247 = SURFACE_STYLE_USAGE(.BOTH.,#2248);
#2248 = SURFACE_SIDE_STYLE('',(#2249));
#2249 = SURFACE_STYLE_FILL_AREA(#2250);
#2250 = FILL_AREA_STYLE('',(#2251));
#2251 = FILL_AREA_STYLE_COLOUR('',#2209);
#2252 = STYLED_ITEM('color',(#2253),#662);
#2253 = PRESENTATION_STYLE_ASSIGNMENT((#2254));
#2254 = SURFACE_STYLE_USAGE(.BOTH.,#2255);
#2255 = SURFACE_SIDE_STYLE('',(#2256));
#2256 = SURFACE_STYLE_FILL_AREA(#2257);
#2257 = FILL_AREA_STYLE('',(#2258));
#2258 = FILL_AREA_STYLE_COLOUR('',#2209);
#2259 = STYLED_ITEM('color',(#2260),#695);
#2260 = PRESENTATION_STYLE_ASSIGNMENT((#2261));
#2261 = SURFACE_STYLE_USAGE(.BOTH.,#2262);
#2262 = SURFACE_SIDE_STYLE('',(#2263));
#2263 = SURFACE_STYLE_FILL_AREA(#2264);
#2264 = FILL_AREA_STYLE('',(#2265));
#2265 = FILL_AREA_STYLE_COLOUR('',#2103);
#2266 = STYLED_ITEM('color',(#2267),#726);
#2267 = PRESENTATION_STYLE_ASSIGNMENT((#2268));
#2268 = SURFACE_STYLE_USAGE(.BOTH.,#2269);
#2269 = SURFACE_SIDE_STYLE('',(#2270));
#2270 = SURFACE_STYLE_FILL_AREA(#2271);
#2271 = FILL_AREA_STYLE('',(#2272));
#2272 = FILL_AREA_STYLE_COLOUR('',#2103);
#2273 = STYLED_ITEM('color',(#2274),#749);
#2274 = PRESENTATION_STYLE_ASSIGNMENT((#2275));
#2275 = SURFACE_STYLE_USAGE(.BOTH.,#2276);
#2276 = SURFACE_SIDE_STYLE('',(#2277));
#2277 = SURFACE_STYLE_FILL_AREA(#2278);
#2278 = FILL_AREA_STYLE('',(#2279));
#2279 = FILL_AREA_STYLE_COLOUR('',#2103);
#2280 = STYLED_ITEM('color',(#2281),#778);
#2281 = PRESENTATION_STYLE_ASSIGNMENT((#2282));
#2282 = SURFACE_STYLE_USAGE(.BOTH.,#2283);
#2283 = SURFACE_SIDE_STYLE('',(#2284));
#2284 = SURFACE_STYLE_FILL_AREA(#2285);
#2285 = FILL_AREA_STYLE('',(#2286));
#2286 = FILL_AREA_STYLE_COLOUR('',#2103);
#2287 = STYLED_ITEM('color',(#2288),#801);
#2288 = PRESENTATION_STYLE_ASSIGNMENT((#2289));
#2289 = SURFACE_STYLE_USAGE(.BOTH.,#2290);
#2290 = SURFACE_SIDE_STYLE('',(#2291));
#2291 = SURFACE_STYLE_FILL_AREA(#2292);
#2292 = FILL_AREA_STYLE('',(#2293));
#2293 = FILL_AREA_STYLE_COLOUR('',#2103);
#2294 = STYLED_ITEM('color',(#2295),#825);
#2295 = PRESENTATION_STYLE_ASSIGNMENT((#2296));
#2296 = SURFACE_STYLE_USAGE(.BOTH.,#2297);
#2297 = SURFACE_SIDE_STYLE('',(#2298));
#2298 = SURFACE_STYLE_FILL_AREA(#2299);
#2299 = FILL_AREA_STYLE('',(#2300));
#2300 = FILL_AREA_STYLE_COLOUR('',#2209);
#2301 = STYLED_ITEM('color',(#2302),#900);
#2302 = PRESENTATION_STYLE_ASSIGNMENT((#2303));
#2303 = SURFACE_STYLE_USAGE(.BOTH.,#2304);
#2304 = SURFACE_SIDE_STYLE('',(#2305));
#2305 = SURFACE_STYLE_FILL_AREA(#2306);
#2306 = FILL_AREA_STYLE('',(#2307));
#2307 = FILL_AREA_STYLE_COLOUR('',#2209);
#2308 = STYLED_ITEM('color',(#2309),#975);
#2309 = PRESENTATION_STYLE_ASSIGNMENT((#2310));
#2310 = SURFACE_STYLE_USAGE(.BOTH.,#2311);
#2311 = SURFACE_SIDE_STYLE('',(#2312));
#2312 = SURFACE_STYLE_FILL_AREA(#2313);
#2313 = FILL_AREA_STYLE('',(#2314));
#2314 = FILL_AREA_STYLE_COLOUR('',#2103);
#2315 = STYLED_ITEM('color',(#2316),#1005);
#2316 = PRESENTATION_STYLE_ASSIGNMENT((#2317));
#2317 = SURFACE_STYLE_USAGE(.BOTH.,#2318);
#2318 = SURFACE_SIDE_STYLE('',(#2319));
#2319 = SURFACE_STYLE_FILL_AREA(#2320);
#2320 = FILL_AREA_STYLE('',(#2321));
#2321 = FILL_AREA_STYLE_COLOUR('',#2209);
#2322 = STYLED_ITEM('color',(#2323),#1022);
#2323 = PRESENTATION_STYLE_ASSIGNMENT((#2324));
#2324 = SURFACE_STYLE_USAGE(.BOTH.,#2325);
#2325 = SURFACE_SIDE_STYLE('',(#2326));
#2326 = SURFACE_STYLE_FILL_AREA(#2327);
#2327 = FILL_AREA_STYLE('',(#2328));
#2328 = FILL_AREA_STYLE_COLOUR('',#2103);
#2329 = STYLED_ITEM('color',(#2330),#1045);
#2330 = PRESENTATION_STYLE_ASSIGNMENT((#2331));
#2331 = SURFACE_STYLE_USAGE(.BOTH.,#2332);
#2332 = SURFACE_SIDE_STYLE('',(#2333));
#2333 = SURFACE_STYLE_FILL_AREA(#2334);
#2334 = FILL_AREA_STYLE('',(#2335));
#2335 = FILL_AREA_STYLE_COLOUR('',#2103);
#2336 = STYLED_ITEM('color',(#2337),#1068);
#2337 = PRESENTATION_STYLE_ASSIGNMENT((#2338));
#2338 = SURFACE_STYLE_USAGE(.BOTH.,#2339);
#2339 = SURFACE_SIDE_STYLE('',(#2340));
#2340 = SURFACE_STYLE_FILL_AREA(#2341);
#2341 = FILL_AREA_STYLE('',(#2342));
#2342 = FILL_AREA_STYLE_COLOUR('',#2103);
#2343 = STYLED_ITEM('color',(#2344),#1091);
#2344 = PRESENTATION_STYLE_ASSIGNMENT((#2345));
#2345 = SURFACE_STYLE_USAGE(.BOTH.,#2346);
#2346 = SURFACE_SIDE_STYLE('',(#2347));
#2347 = SURFACE_STYLE_FILL_AREA(#2348);
#2348 = FILL_AREA_STYLE('',(#2349));
#2349 = FILL_AREA_STYLE_COLOUR('',#2103);
#2350 = STYLED_ITEM('color',(#2351),#1115);
#2351 = PRESENTATION_STYLE_ASSIGNMENT((#2352));
#2352 = SURFACE_STYLE_USAGE(.BOTH.,#2353);
#2353 = SURFACE_SIDE_STYLE('',(#2354));
#2354 = SURFACE_STYLE_FILL_AREA(#2355);
#2355 = FILL_AREA_STYLE('',(#2356));
#2356 = FILL_AREA_STYLE_COLOUR('',#2209);
#2357 = STYLED_ITEM('color',(#2358),#1190);
#2358 = PRESENTATION_STYLE_ASSIGNMENT((#2359));
#2359 = SURFACE_STYLE_USAGE(.BOTH.,#2360);
#2360 = SURFACE_SIDE_STYLE('',(#2361));
#2361 = SURFACE_STYLE_FILL_AREA(#2362);
#2362 = FILL_AREA_STYLE('',(#2363));
#2363 = FILL_AREA_STYLE_COLOUR('',#2209);
#2364 = STYLED_ITEM('color',(#2365),#1265);
#2365 = PRESENTATION_STYLE_ASSIGNMENT((#2366));
#2366 = SURFACE_STYLE_USAGE(.BOTH.,#2367);
#2367 = SURFACE_SIDE_STYLE('',(#2368));
#2368 = SURFACE_STYLE_FILL_AREA(#2369);
#2369 = FILL_AREA_STYLE('',(#2370));
#2370 = FILL_AREA_STYLE_COLOUR('',#2103);
#2371 = STYLED_ITEM('color',(#2372),#1289);
#2372 = PRESENTATION_STYLE_ASSIGNMENT((#2373));
#2373 = SURFACE_STYLE_USAGE(.BOTH.,#2374);
#2374 = SURFACE_SIDE_STYLE('',(#2375));
#2375 = SURFACE_STYLE_FILL_AREA(#2376);
#2376 = FILL_AREA_STYLE('',(#2377));
#2377 = FILL_AREA_STYLE_COLOUR('',#2209);
#2378 = STYLED_ITEM('color',(#2379),#1364);
#2379 = PRESENTATION_STYLE_ASSIGNMENT((#2380));
#2380 = SURFACE_STYLE_USAGE(.BOTH.,#2381);
#2381 = SURFACE_SIDE_STYLE('',(#2382));
#2382 = SURFACE_STYLE_FILL_AREA(#2383);
#2383 = FILL_AREA_STYLE('',(#2384));
#2384 = FILL_AREA_STYLE_COLOUR('',#2209);
#2385 = STYLED_ITEM('color',(#2386),#1381);
#2386 = PRESENTATION_STYLE_ASSIGNMENT((#2387));
#2387 = SURFACE_STYLE_USAGE(.BOTH.,#2388);
#2388 = SURFACE_SIDE_STYLE('',(#2389));
#2389 = SURFACE_STYLE_FILL_AREA(#2390);
#2390 = FILL_AREA_STYLE('',(#2391));
#2391 = FILL_AREA_STYLE_COLOUR('',#2209);
#2392 = STYLED_ITEM('color',(#2393),#1456);
#2393 = PRESENTATION_STYLE_ASSIGNMENT((#2394));
#2394 = SURFACE_STYLE_USAGE(.BOTH.,#2395);
#2395 = SURFACE_SIDE_STYLE('',(#2396));
#2396 = SURFACE_STYLE_FILL_AREA(#2397);
#2397 = FILL_AREA_STYLE('',(#2398));
#2398 = FILL_AREA_STYLE_COLOUR('',#2209);
#2399 = STYLED_ITEM('color',(#2400),#1473);
#2400 = PRESENTATION_STYLE_ASSIGNMENT((#2401));
#2401 = SURFACE_STYLE_USAGE(.BOTH.,#2402);
#2402 = SURFACE_SIDE_STYLE('',(#2403));
#2403 = SURFACE_STYLE_FILL_AREA(#2404);
#2404 = FILL_AREA_STYLE('',(#2405));
#2405 = FILL_AREA_STYLE_COLOUR('',#2103);
#2406 = STYLED_ITEM('color',(#2407),#1504);
#2407 = PRESENTATION_STYLE_ASSIGNMENT((#2408));
#2408 = SURFACE_STYLE_USAGE(.BOTH.,#2409);
#2409 = SURFACE_SIDE_STYLE('',(#2410));
#2410 = SURFACE_STYLE_FILL_AREA(#2411);
#2411 = FILL_AREA_STYLE('',(#2412));
#2412 = FILL_AREA_STYLE_COLOUR('',#2103);
#2413 = STYLED_ITEM('color',(#2414),#1527);
#2414 = PRESENTATION_STYLE_ASSIGNMENT((#2415));
#2415 = SURFACE_STYLE_USAGE(.BOTH.,#2416);
#2416 = SURFACE_SIDE_STYLE('',(#2417));
#2417 = SURFACE_STYLE_FILL_AREA(#2418);
#2418 = FILL_AREA_STYLE('',(#2419));
#2419 = FILL_AREA_STYLE_COLOUR('',#2103);
#2420 = STYLED_ITEM('color',(#2421),#1583);
#2421 = PRESENTATION_STYLE_ASSIGNMENT((#2422));
#2422 = SURFACE_STYLE_USAGE(.BOTH.,#2423);
#2423 = SURFACE_SIDE_STYLE('',(#2424));
#2424 = SURFACE_STYLE_FILL_AREA(#2425);
#2425 = FILL_AREA_STYLE('',(#2426));
#2426 = FILL_AREA_STYLE_COLOUR('',#2103);
#2427 = STYLED_ITEM('color',(#2428),#1599);
#2428 = PRESENTATION_STYLE_ASSIGNMENT((#2429));
#2429 = SURFACE_STYLE_USAGE(.BOTH.,#2430);
#2430 = SURFACE_SIDE_STYLE('',(#2431));
#2431 = SURFACE_STYLE_FILL_AREA(#2432);
#2432 = FILL_AREA_STYLE('',(#2433));
#2433 = FILL_AREA_STYLE_COLOUR('',#2103);
#2434 = STYLED_ITEM('color',(#2435),#1622);
#2435 = PRESENTATION_STYLE_ASSIGNMENT((#2436));
#2436 = SURFACE_STYLE_USAGE(.BOTH.,#2437);
#2437 = SURFACE_SIDE_STYLE('',(#2438));
#2438 = SURFACE_STYLE_FILL_AREA(#2439);
#2439 = FILL_AREA_STYLE('',(#2440));
#2440 = FILL_AREA_STYLE_COLOUR('',#2209);
#2441 = STYLED_ITEM('color',(#2442),#1639);
#2442 = PRESENTATION_STYLE_ASSIGNMENT((#2443));
#2443 = SURFACE_STYLE_USAGE(.BOTH.,#2444);
#2444 = SURFACE_SIDE_STYLE('',(#2445));
#2445 = SURFACE_STYLE_FILL_AREA(#2446);
#2446 = FILL_AREA_STYLE('',(#2447));
#2447 = FILL_AREA_STYLE_COLOUR('',#2209);
#2448 = STYLED_ITEM('color',(#2449),#1656);
#2449 = PRESENTATION_STYLE_ASSIGNMENT((#2450));
#2450 = SURFACE_STYLE_USAGE(.BOTH.,#2451);
#2451 = SURFACE_SIDE_STYLE('',(#2452));
#2452 = SURFACE_STYLE_FILL_AREA(#2453);
#2453 = FILL_AREA_STYLE('',(#2454));
#2454 = FILL_AREA_STYLE_COLOUR('',#2209);
#2455 = STYLED_ITEM('color',(#2456),#1673);
#2456 = PRESENTATION_STYLE_ASSIGNMENT((#2457));
#2457 = SURFACE_STYLE_USAGE(.BOTH.,#2458);
#2458 = SURFACE_SIDE_STYLE('',(#2459));
#2459 = SURFACE_STYLE_FILL_AREA(#2460);
#2460 = FILL_AREA_STYLE('',(#2461));
#2461 = FILL_AREA_STYLE_COLOUR('',#2209);
#2462 = STYLED_ITEM('color',(#2463),#1690);
#2463 = PRESENTATION_STYLE_ASSIGNMENT((#2464));
#2464 = SURFACE_STYLE_USAGE(.BOTH.,#2465);
#2465 = SURFACE_SIDE_STYLE('',(#2466));
#2466 = SURFACE_STYLE_FILL_AREA(#2467);
#2467 = FILL_AREA_STYLE('',(#2468));
#2468 = FILL_AREA_STYLE_COLOUR('',#2209);
#2469 = STYLED_ITEM('color',(#2470),#1707);
#2470 = PRESENTATION_STYLE_ASSIGNMENT((#2471));
#2471 = SURFACE_STYLE_USAGE(.BOTH.,#2472);
#2472 = SURFACE_SIDE_STYLE('',(#2473));
#2473 = SURFACE_STYLE_FILL_AREA(#2474);
#2474 = FILL_AREA_STYLE('',(#2475));
#2475 = FILL_AREA_STYLE_COLOUR('',#2209);
#2476 = STYLED_ITEM('color',(#2477),#1724);
#2477 = PRESENTATION_STYLE_ASSIGNMENT((#2478));
#2478 = SURFACE_STYLE_USAGE(.BOTH.,#2479);
#2479 = SURFACE_SIDE_STYLE('',(#2480));
#2480 = SURFACE_STYLE_FILL_AREA(#2481);
#2481 = FILL_AREA_STYLE('',(#2482));
#2482 = FILL_AREA_STYLE_COLOUR('',#2209);
#2483 = STYLED_ITEM('color',(#2484),#1741);
#2484 = PRESENTATION_STYLE_ASSIGNMENT((#2485));
#2485 = SURFACE_STYLE_USAGE(.BOTH.,#2486);
#2486 = SURFACE_SIDE_STYLE('',(#2487));
#2487 = SURFACE_STYLE_FILL_AREA(#2488);
#2488 = FILL_AREA_STYLE('',(#2489));
#2489 = FILL_AREA_STYLE_COLOUR('',#2209);
#2490 = STYLED_ITEM('color',(#2491),#1753);
#2491 = PRESENTATION_STYLE_ASSIGNMENT((#2492));
#2492 = SURFACE_STYLE_USAGE(.BOTH.,#2493);
#2493 = SURFACE_SIDE_STYLE('',(#2494));
#2494 = SURFACE_STYLE_FILL_AREA(#2495);
#2495 = FILL_AREA_STYLE('',(#2496));
#2496 = FILL_AREA_STYLE_COLOUR('',#2103);
#2497 = STYLED_ITEM('color',(#2498),#1769);
#2498 = PRESENTATION_STYLE_ASSIGNMENT((#2499));
#2499 = SURFACE_STYLE_USAGE(.BOTH.,#2500);
#2500 = SURFACE_SIDE_STYLE('',(#2501));
#2501 = SURFACE_STYLE_FILL_AREA(#2502);
#2502 = FILL_AREA_STYLE('',(#2503));
#2503 = FILL_AREA_STYLE_COLOUR('',#2103);
#2504 = STYLED_ITEM('color',(#2505),#1785);
#2505 = PRESENTATION_STYLE_ASSIGNMENT((#2506));
#2506 = SURFACE_STYLE_USAGE(.BOTH.,#2507);
#2507 = SURFACE_SIDE_STYLE('',(#2508));
#2508 = SURFACE_STYLE_FILL_AREA(#2509);
#2509 = FILL_AREA_STYLE('',(#2510));
#2510 = FILL_AREA_STYLE_COLOUR('',#2103);
#2511 = STYLED_ITEM('color',(#2512),#1801);
#2512 = PRESENTATION_STYLE_ASSIGNMENT((#2513));
#2513 = SURFACE_STYLE_USAGE(.BOTH.,#2514);
#2514 = SURFACE_SIDE_STYLE('',(#2515));
#2515 = SURFACE_STYLE_FILL_AREA(#2516);
#2516 = FILL_AREA_STYLE('',(#2517));
#2517 = FILL_AREA_STYLE_COLOUR('',#2103);
#2518 = STYLED_ITEM('color',(#2519),#1827);
#2519 = PRESENTATION_STYLE_ASSIGNMENT((#2520));
#2520 = SURFACE_STYLE_USAGE(.BOTH.,#2521);
#2521 = SURFACE_SIDE_STYLE('',(#2522));
#2522 = SURFACE_STYLE_FILL_AREA(#2523);
#2523 = FILL_AREA_STYLE('',(#2524));
#2524 = FILL_AREA_STYLE_COLOUR('',#2209);
#2525 = STYLED_ITEM('color',(#2526),#1844);
#2526 = PRESENTATION_STYLE_ASSIGNMENT((#2527));
#2527 = SURFACE_STYLE_USAGE(.BOTH.,#2528);
#2528 = SURFACE_SIDE_STYLE('',(#2529));
#2529 = SURFACE_STYLE_FILL_AREA(#2530);
#2530 = FILL_AREA_STYLE('',(#2531));
#2531 = FILL_AREA_STYLE_COLOUR('',#2209);
#2532 = STYLED_ITEM('color',(#2533),#1861);
#2533 = PRESENTATION_STYLE_ASSIGNMENT((#2534));
#2534 = SURFACE_STYLE_USAGE(.BOTH.,#2535);
#2535 = SURFACE_SIDE_STYLE('',(#2536));
#2536 = SURFACE_STYLE_FILL_AREA(#2537);
#2537 = FILL_AREA_STYLE('',(#2538));
#2538 = FILL_AREA_STYLE_COLOUR('',#2209);
#2539 = STYLED_ITEM('color',(#2540),#1878);
#2540 = PRESENTATION_STYLE_ASSIGNMENT((#2541));
#2541 = SURFACE_STYLE_USAGE(.BOTH.,#2542);
#2542 = SURFACE_SIDE_STYLE('',(#2543));
#2543 = SURFACE_STYLE_FILL_AREA(#2544);
#2544 = FILL_AREA_STYLE('',(#2545));
#2545 = FILL_AREA_STYLE_COLOUR('',#2209);
#2546 = STYLED_ITEM('color',(#2547),#1895);
#2547 = PRESENTATION_STYLE_ASSIGNMENT((#2548));
#2548 = SURFACE_STYLE_USAGE(.BOTH.,#2549);
#2549 = SURFACE_SIDE_STYLE('',(#2550));
#2550 = SURFACE_STYLE_FILL_AREA(#2551);
#2551 = FILL_AREA_STYLE('',(#2552));
#2552 = FILL_AREA_STYLE_COLOUR('',#2209);
#2553 = STYLED_ITEM('color',(#2554),#1912);
#2554 = PRESENTATION_STYLE_ASSIGNMENT((#2555));
#2555 = SURFACE_STYLE_USAGE(.BOTH.,#2556);
#2556 = SURFACE_SIDE_STYLE('',(#2557));
#2557 = SURFACE_STYLE_FILL_AREA(#2558);
#2558 = FILL_AREA_STYLE('',(#2559));
#2559 = FILL_AREA_STYLE_COLOUR('',#2209);
#2560 = STYLED_ITEM('color',(#2561),#1929);
#2561 = PRESENTATION_STYLE_ASSIGNMENT((#2562));
#2562 = SURFACE_STYLE_USAGE(.BOTH.,#2563);
#2563 = SURFACE_SIDE_STYLE('',(#2564));
#2564 = SURFACE_STYLE_FILL_AREA(#2565);
#2565 = FILL_AREA_STYLE('',(#2566));
#2566 = FILL_AREA_STYLE_COLOUR('',#2209);
#2567 = STYLED_ITEM('color',(#2568),#1946);
#2568 = PRESENTATION_STYLE_ASSIGNMENT((#2569));
#2569 = SURFACE_STYLE_USAGE(.BOTH.,#2570);
#2570 = SURFACE_SIDE_STYLE('',(#2571));
#2571 = SURFACE_STYLE_FILL_AREA(#2572);
#2572 = FILL_AREA_STYLE('',(#2573));
#2573 = FILL_AREA_STYLE_COLOUR('',#2209);
#2574 = STYLED_ITEM('color',(#2575),#1958);
#2575 = PRESENTATION_STYLE_ASSIGNMENT((#2576));
#2576 = SURFACE_STYLE_USAGE(.BOTH.,#2577);
#2577 = SURFACE_SIDE_STYLE('',(#2578));
#2578 = SURFACE_STYLE_FILL_AREA(#2579);
#2579 = FILL_AREA_STYLE('',(#2580));
#2580 = FILL_AREA_STYLE_COLOUR('',#2209);
#2581 = STYLED_ITEM('color',(#2582),#1980);
#2582 = PRESENTATION_STYLE_ASSIGNMENT((#2583));
#2583 = SURFACE_STYLE_USAGE(.BOTH.,#2584);
#2584 = SURFACE_SIDE_STYLE('',(#2585));
#2585 = SURFACE_STYLE_FILL_AREA(#2586);
#2586 = FILL_AREA_STYLE('',(#2587));
#2587 = FILL_AREA_STYLE_COLOUR('',#2209);
#2588 = STYLED_ITEM('color',(#2589),#1997);
#2589 = PRESENTATION_STYLE_ASSIGNMENT((#2590));
#2590 = SURFACE_STYLE_USAGE(.BOTH.,#2591);
#2591 = SURFACE_SIDE_STYLE('',(#2592));
#2592 = SURFACE_STYLE_FILL_AREA(#2593);
#2593 = FILL_AREA_STYLE('',(#2594));
#2594 = FILL_AREA_STYLE_COLOUR('',#2209);
#2595 = STYLED_ITEM('color',(#2596),#2014);
#2596 = PRESENTATION_STYLE_ASSIGNMENT((#2597));
#2597 = SURFACE_STYLE_USAGE(.BOTH.,#2598);
#2598 = SURFACE_SIDE_STYLE('',(#2599));
#2599 = SURFACE_STYLE_FILL_AREA(#2600);
#2600 = FILL_AREA_STYLE('',(#2601));
#2601 = FILL_AREA_STYLE_COLOUR('',#2209);
#2602 = STYLED_ITEM('color',(#2603),#2031);
#2603 = PRESENTATION_STYLE_ASSIGNMENT((#2604));
#2604 = SURFACE_STYLE_USAGE(.BOTH.,#2605);
#2605 = SURFACE_SIDE_STYLE('',(#2606));
#2606 = SURFACE_STYLE_FILL_AREA(#2607);
#2607 = FILL_AREA_STYLE('',(#2608));
#2608 = FILL_AREA_STYLE_COLOUR('',#2209);
#2609 = STYLED_ITEM('color',(#2610),#2048);
#2610 = PRESENTATION_STYLE_ASSIGNMENT((#2611));
#2611 = SURFACE_STYLE_USAGE(.BOTH.,#2612);
#2612 = SURFACE_SIDE_STYLE('',(#2613));
#2613 = SURFACE_STYLE_FILL_AREA(#2614);
#2614 = FILL_AREA_STYLE('',(#2615));
#2615 = FILL_AREA_STYLE_COLOUR('',#2209);
#2616 = STYLED_ITEM('color',(#2617),#2065);
#2617 = PRESENTATION_STYLE_ASSIGNMENT((#2618));
#2618 = SURFACE_STYLE_USAGE(.BOTH.,#2619);
#2619 = SURFACE_SIDE_STYLE('',(#2620));
#2620 = SURFACE_STYLE_FILL_AREA(#2621);
#2621 = FILL_AREA_STYLE('',(#2622));
#2622 = FILL_AREA_STYLE_COLOUR('',#2209);
#2623 = STYLED_ITEM('color',(#2624),#2077);
#2624 = PRESENTATION_STYLE_ASSIGNMENT((#2625));
#2625 = SURFACE_STYLE_USAGE(.BOTH.,#2626);
#2626 = SURFACE_SIDE_STYLE('',(#2627));
#2627 = SURFACE_STYLE_FILL_AREA(#2628);
#2628 = FILL_AREA_STYLE('',(#2629));
#2629 = FILL_AREA_STYLE_COLOUR('',#2209);
ENDSEC;
END-ISO-10303-21;
