#!/usr/bin/env python3
"""
COMPLETE ULTRALIBRARIAN AUTOMATION
==================================
Full 6-screen automation:
1. Enter part number and search
2. Select specific part from results  
3. Find the right LM358 variant and click
4. Hit "Download Now"
5. Hit "3D Model" 
6. Login and download
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

class CompleteUltraLibrarianAutomation:
    def __init__(self):
        self.base_url = 'https://app.ultralibrarian.com'
        os.makedirs('3d', exist_ok=True)
        print("Complete UltraLibrarian Automation Ready!")

    def setup_driver(self):
        """Setup Chrome driver with stealth options"""
        chrome_options = Options()
        
        # Stealth options
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('3d'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"Chrome driver setup failed: {e}")
            return None

    def screen_1_search(self, driver, manufacturer, part_number):
        """SCREEN 1: Login first, then enter part number and search"""
        print(f"\n🔸 SCREEN 1: Login first, then search")

        try:
            driver.get(self.base_url)
            print(f"   Loading {self.base_url}...")

            # Wait longer for JavaScript app to load
            time.sleep(10)

            print(f"   Page loaded: {driver.title}")
            print(f"   Current URL: {driver.current_url}")

            # FIRST: Login before searching
            print(f"   🔐 Logging in first...")

            # Load credentials
            try:
                import json
                with open('component_site_credentials.json', 'r') as f:
                    credentials = json.load(f)
                email = credentials['UltraLibrarian']['email']
                password = credentials['UltraLibrarian']['password']
                print(f"   ✅ Loaded credentials: {email}")
            except:
                print(f"   ❌ Could not load credentials, continuing without login")
                email = None
                password = None

            # Look for login link or button with VERIFICATION AT EVERY SPOT
            if email and password:
                print(f"   🔍 VERIFICATION: Have credentials, looking for login link...")

                try:
                    # VERIFY: Look for ALL clickable elements first
                    all_links = driver.find_elements(By.TAG_NAME, "a")
                    all_buttons = driver.find_elements(By.TAG_NAME, "button")

                    print(f"   🔍 VERIFICATION: Found {len(all_links)} total links and {len(all_buttons)} total buttons")

                    # Show ALL visible links and buttons
                    print(f"   🔍 VERIFICATION: All visible links:")
                    for i, link in enumerate(all_links[:20]):  # Show first 20
                        try:
                            if link.is_displayed():
                                text = link.text.strip()
                                href = link.get_attribute('href') or ''
                                if text:  # Only show links with text
                                    print(f"     Link {i}: '{text}' -> {href[:50]}")
                        except:
                            continue

                    print(f"   🔍 VERIFICATION: All visible buttons:")
                    for i, btn in enumerate(all_buttons[:10]):  # Show first 10
                        try:
                            if btn.is_displayed():
                                text = btn.text.strip()
                                if text:  # Only show buttons with text
                                    print(f"     Button {i}: '{text}'")
                        except:
                            continue

                    # VERIFY: Look for the LOGIN link with working selector
                    login_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'Login')]")

                    print(f"   🔍 VERIFICATION: Found {len(login_links)} LOGIN links")

                    # Show what we found
                    for i, link in enumerate(login_links):
                        try:
                            text = link.text.strip()
                            href = link.get_attribute('href') or ''
                            visible = link.is_displayed()
                            print(f"     Login link {i}: '{text}' href='{href}' visible={visible}")
                        except:
                            continue

                    # No login buttons to check since we're using links

                    # Try to click login link
                    login_element = None
                    if login_links:
                        for link in login_links:
                            if link.is_displayed():
                                login_element = link
                                print(f"   ✅ VERIFICATION: Using visible login link: '{link.text.strip()}'")
                                break
                    elif login_buttons:
                        for btn in login_buttons:
                            if btn.is_displayed():
                                login_element = btn
                                print(f"   ✅ VERIFICATION: Using visible login button: '{btn.text.strip()}'")
                                break

                    if login_element:
                        print(f"   🔍 VERIFICATION: About to click login element...")
                        initial_url = driver.current_url

                        # Use JavaScript to click to avoid interception
                        driver.execute_script("arguments[0].click();", login_element)
                        time.sleep(5)
                        new_url = driver.current_url
                        print(f"   🔍 VERIFICATION: URL changed from {initial_url} to {new_url}")

                        # VERIFY: Look for login form with broader selectors
                        print(f"   🔍 VERIFICATION: Looking for email input...")
                        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email'], input[placeholder*='email'], input[name='Username']")
                        print(f"   🔍 VERIFICATION: Found {len(email_inputs)} email inputs")

                        if email_inputs:
                            email_input = email_inputs[0]
                            print(f"   ✅ VERIFICATION: Found email input, entering email...")
                            email_input.clear()
                            email_input.send_keys(email)
                            entered_value = email_input.get_attribute('value')
                            print(f"   🔍 VERIFICATION: Email entered, value is now: '{entered_value}'")

                            # VERIFY: Look for password input
                            print(f"   🔍 VERIFICATION: Looking for password input...")
                            password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
                            print(f"   🔍 VERIFICATION: Found {len(password_inputs)} password inputs")

                            if password_inputs:
                                password_input = password_inputs[0]
                                print(f"   ✅ VERIFICATION: Found password input, entering password...")
                                password_input.clear()
                                password_input.send_keys(password)
                                print(f"   🔍 VERIFICATION: Password entered")

                                # VERIFY: Look for submit button
                                print(f"   🔍 VERIFICATION: Looking for login submit button...")
                                submit_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')] | //input[@type='submit'] | //button[@type='submit']")
                                print(f"   🔍 VERIFICATION: Found {len(submit_buttons)} submit buttons")

                                for i, btn in enumerate(submit_buttons):
                                    try:
                                        text = btn.text.strip() or btn.get_attribute('value') or f"Button {i}"
                                        visible = btn.is_displayed()
                                        print(f"     Submit button {i}: '{text}' visible={visible}")
                                    except:
                                        continue

                                if submit_buttons:
                                    submit_button = None
                                    for btn in submit_buttons:
                                        if btn.is_displayed():
                                            submit_button = btn
                                            break

                                    if submit_button:
                                        print(f"   ✅ VERIFICATION: Clicking submit button...")
                                        pre_login_url = driver.current_url
                                        submit_button.click()
                                        time.sleep(10)
                                        post_login_url = driver.current_url
                                        print(f"   🔍 VERIFICATION: After login click, URL changed from {pre_login_url} to {post_login_url}")

                                        # VERIFY: Check if we're logged in
                                        page_source = driver.page_source.lower()
                                        if 'logout' in page_source or 'sign out' in page_source:
                                            print(f"   ✅ VERIFICATION: LOGIN SUCCESSFUL - Found logout option")
                                        else:
                                            print(f"   ❌ VERIFICATION: LOGIN MAY HAVE FAILED - No logout option found")

                                        # Navigate back to main site after login
                                        print(f"   🔍 VERIFICATION: Navigating back to UltraLibrarian...")
                                        driver.get("https://app.ultralibrarian.com")
                                        time.sleep(5)
                                        print(f"   ✅ VERIFICATION: Back on main site: {driver.current_url}")

                                    else:
                                        print(f"   ❌ VERIFICATION: No visible submit button found")
                                else:
                                    print(f"   ❌ VERIFICATION: No submit buttons found")
                            else:
                                print(f"   ❌ VERIFICATION: No password input found")
                        else:
                            print(f"   ❌ VERIFICATION: No email input found")

                            # If no email input found, we might already be logged in or on wrong page
                            # Navigate back to main site
                            print(f"   🔍 VERIFICATION: No login form found, going back to main site...")
                            driver.get("https://app.ultralibrarian.com")
                            time.sleep(5)
                    else:
                        print(f"   ❌ VERIFICATION: No visible login element found")

                except Exception as e:
                    print(f"   ❌ VERIFICATION: Login process failed: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print(f"   ❌ VERIFICATION: No credentials available")

            # NOW: Find ALL input elements and analyze them
            inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"   Found {len(inputs)} input elements")

            search_input = None

            # Try each input to find the search box
            for i, inp in enumerate(inputs):
                try:
                    if inp.is_displayed() and inp.is_enabled():
                        input_type = inp.get_attribute('type') or ''
                        placeholder = inp.get_attribute('placeholder') or ''
                        name = inp.get_attribute('name') or ''

                        print(f"   Input {i}: type='{input_type}', placeholder='{placeholder}', name='{name}'")

                        # Look for search-like attributes
                        if (input_type.lower() in ['search', 'text'] or
                            'search' in placeholder.lower() or
                            'search' in name.lower()):
                            search_input = inp
                            print(f"   ✅ Using input {i} as search box")
                            break
                except Exception as e:
                    print(f"   Input {i}: Error - {e}")
                    continue

            # If no obvious search box, try first visible text input
            if not search_input:
                print("   No obvious search box found, trying first text input...")
                for i, inp in enumerate(inputs):
                    try:
                        if inp.is_displayed() and inp.is_enabled():
                            input_type = inp.get_attribute('type') or ''
                            if input_type.lower() in ['text', '']:
                                search_input = inp
                                print(f"   ✅ Using text input {i} as fallback search box")
                                break
                    except:
                        continue

            if not search_input:
                print("   ❌ No usable search box found!")
                return False

            # Enter search term
            search_term = part_number  # Just use part number, not manufacturer
            print(f"   Entering search term: '{search_term}'")

            search_input.clear()
            time.sleep(1)
            search_input.send_keys(search_term)
            time.sleep(1)
            search_input.send_keys(Keys.RETURN)

            print(f"   ✅ Search submitted, waiting for results...")
            time.sleep(8)  # Wait longer for results

            # Check if URL changed or page updated
            new_url = driver.current_url
            print(f"   New URL: {new_url}")

            return True

        except Exception as e:
            print(f"   ❌ Screen 1 error: {e}")
            return False

    def screen_2_select_part(self, driver, part_number):
        """SCREEN 2: Select specific part from results"""
        print(f"\n🔸 SCREEN 2: Select part from results")

        # Wait for results to load
        time.sleep(3)

        # Look for Texas Instruments LM358N specifically
        ti_selectors = [
            "//a[contains(text(), 'Texas Instruments') and contains(text(), 'LM358N')]",
            "//a[contains(text(), 'LM358N') and contains(text(), 'Texas')]",
            "//tr[contains(., 'Texas Instruments') and contains(., 'LM358N')]//a",
            "//tr[contains(., 'LM358N') and contains(., 'Texas')]//a",
            f"//a[contains(text(), '{part_number}')]",
            "//a[contains(@href, 'texas-instruments') and contains(@href, 'lm358')]",
            "//a[contains(@href, 'details')]"
        ]

        print("   Looking for Texas Instruments LM358N...")

        for i, xpath in enumerate(ti_selectors):
            try:
                elements = driver.find_elements(By.XPATH, xpath)
                print(f"   Selector {i+1}: Found {len(elements)} elements")

                for j, element in enumerate(elements[:3]):  # Try first 3 matches
                    try:
                        if element.is_displayed() and element.is_enabled():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''

                            print(f"     Element {j+1}: '{text[:60]}' -> {href[:60]}")

                            # Check if this looks like the right part
                            text_lower = text.lower()
                            href_lower = href.lower()

                            if (('lm358' in text_lower or 'lm358' in href_lower) and
                                ('texas' in text_lower or 'ti' in text_lower or 'texas' in href_lower)):

                                print(f"   ✅ Found matching TI LM358N: {text[:50]}")

                                driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                time.sleep(1)
                                element.click()
                                time.sleep(5)

                                print(f"   ✅ Clicked on TI LM358N result")
                                return True
                    except Exception as e:
                        print(f"     Error with element {j+1}: {e}")
                        continue

            except Exception as e:
                print(f"   Error with selector {i+1}: {e}")
                continue

        print(f"   ❌ No Texas Instruments LM358N found in results")
        return False

    def screen_3_find_right_variant(self, driver, part_number):
        """SCREEN 3: We're already on the part details page, skip to download"""
        print(f"\n🔸 SCREEN 3: Already on {part_number} details page")

        # Wait for page to load
        time.sleep(3)

        print(f"   Current URL: {driver.current_url}")
        print(f"   Page title: {driver.title}")

        # Check if we're on a part details page
        if 'details' in driver.current_url.lower():
            print(f"   ✅ On part details page, proceeding to download")
            return True
        else:
            print(f"   ❌ Not on part details page")
            return False

    def screen_4_download_now(self, driver):
        """SCREEN 4: Hit 'Download Now'"""
        print(f"\n🔸 SCREEN 4: Hit 'Download Now'")
        
        # Look for "Download Now" button
        download_selectors = [
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download now')]",
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download now')]",
            "//input[contains(translate(@value, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download now')]",
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download')]",
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download')]"
        ]
        
        for selector in download_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    element = elements[0]
                    text = element.text.strip()
                    print(f"   Found download button: {text}")
                    
                    driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)
                    element.click()
                    time.sleep(5)
                    
                    print(f"   ✅ Clicked 'Download Now'")
                    return True
            except:
                continue
        
        print(f"   ❌ Could not find 'Download Now' button")
        return False

    def screen_5_3d_model(self, driver):
        """SCREEN 5: Hit '3D Model'"""
        print(f"\n🔸 SCREEN 5: Hit '3D Model'")
        
        # Look for "3D Model" button/link
        model_selectors = [
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d model')]",
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d model')]",
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')]",
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '3d')]",
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'model')]",
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'model')]"
        ]
        
        for selector in model_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    element = elements[0]
                    text = element.text.strip()
                    print(f"   Found 3D model option: {text}")
                    
                    driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)
                    element.click()
                    time.sleep(5)
                    
                    print(f"   ✅ Clicked '3D Model'")
                    return True
            except:
                continue
        
        print(f"   ❌ Could not find '3D Model' option")
        return False

    def screen_6_login_and_download(self, driver, manufacturer, part_number):
        """SCREEN 6: Login and download"""
        print(f"\n🔸 SCREEN 6: Login and download")

        # Load credentials
        try:
            import json
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            print(f"   🔐 Using saved credentials: {email}")
        except:
            print(f"   ❌ Could not load credentials")
            return None

        # Get initial file count in Downloads folder
        downloads_dir = os.path.expanduser("~/Downloads")
        initial_files = set(os.listdir(downloads_dir))

        # Select 3D format - try MfrThreeDModel first, then IGES, then STL
        format_selected = False
        format_options = ['MfrThreeDModel', 'IGES', 'STL']

        for format_id in format_options:
            try:
                checkbox = driver.find_element(By.ID, format_id)
                if checkbox and not checkbox.is_selected():
                    # Use JavaScript to click since checkbox might be hidden
                    driver.execute_script("arguments[0].click();", checkbox)
                    print(f"   ✅ Selected {format_id} format using JavaScript")
                    format_selected = True
                    break
            except Exception as e:
                print(f"   ❌ Could not select {format_id}: {e}")
                continue

        if not format_selected:
            print(f"   ❌ No 3D format could be selected")
            return None

        # Look for and click download button with proper verification
        download_clicked = False
        initial_url = driver.current_url

        # Look for specific download/submit buttons
        download_selectors = [
            "//button[contains(text(), 'Download')]",
            "//input[@type='submit']",
            "//button[@type='submit']",
            "//input[@value='Download']",
            "//button[contains(@class, 'download')]"
        ]

        print(f"   Looking for download button...")
        for i, selector in enumerate(download_selectors):
            try:
                elements = driver.find_elements(By.XPATH, selector)
                print(f"   Selector {i+1}: Found {len(elements)} elements")

                for j, element in enumerate(elements):
                    try:
                        if element.is_displayed() and element.is_enabled():
                            text = element.text.strip() or element.get_attribute('value') or f"Button {j+1}"
                            print(f"     Trying to click: '{text}'")

                            element.click()
                            time.sleep(3)  # Wait for response

                            # Check if anything changed
                            new_url = driver.current_url
                            if new_url != initial_url:
                                print(f"   ✅ URL changed after click: {new_url}")
                                download_clicked = True
                                break
                            else:
                                print(f"   ✅ Clicked '{text}' - checking for download...")
                                download_clicked = True
                                break
                    except Exception as e:
                        print(f"     ❌ Could not click element {j+1}: {e}")
                        continue

                if download_clicked:
                    break
            except Exception as e:
                print(f"   ❌ Selector {i+1} failed: {e}")
                continue

        # If no specific download button found, try form submission
        if not download_clicked:
            try:
                forms = driver.find_elements(By.TAG_NAME, "form")
                if forms:
                    form = forms[0]
                    print(f"   ✅ No download button found, trying form submission...")
                    form.submit()
                    time.sleep(3)
                    download_clicked = True
            except Exception as e:
                print(f"   ❌ Form submission failed: {e}")

        if not download_clicked:
            print(f"   ❌ No download method worked")
            return None

        print(f"   ✅ Download action completed")

        # Since we logged in first, we should already be authenticated
        print(f"   ✅ Already logged in from Screen 1")

        # Monitor Downloads folder for new files
        print(f"   ⏳ Monitoring Downloads folder for 60 seconds...")

        for i in range(12):  # Check every 5 seconds for 60 seconds
            time.sleep(5)

            current_files = set(os.listdir(downloads_dir))
            new_files = current_files - initial_files

            step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
            zip_files = [f for f in new_files if f.lower().endswith('.zip')]

            if step_files:
                step_file = step_files[0]
                print(f"   ✅ STEP file downloaded: {step_file}")

                # Move to 3d directory
                src_path = os.path.join(downloads_dir, step_file)
                new_name = f"ultralibrarian_{manufacturer.replace(' ', '_')}_{part_number}.step"
                dst_path = os.path.join('3d', new_name)

                try:
                    import shutil
                    shutil.move(src_path, dst_path)
                    self.create_log_file(new_name, manufacturer, part_number)
                    print(f"   ✅ Moved to: 3d/{new_name}")
                    return new_name
                except Exception as e:
                    print(f"   ❌ Error moving file: {e}")
                    return step_file

            elif zip_files:
                zip_file = zip_files[0]
                print(f"   📦 ZIP file downloaded: {zip_file}")

                # Extract ZIP and look for STEP files
                try:
                    import zipfile
                    zip_path = os.path.join(downloads_dir, zip_file)

                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall('3d')

                    # Look for extracted STEP files
                    extracted_files = os.listdir('3d')
                    step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]

                    if step_files:
                        step_file = step_files[0]
                        new_name = f"ultralibrarian_{manufacturer.replace(' ', '_')}_{part_number}.step"

                        old_path = os.path.join('3d', step_file)
                        new_path = os.path.join('3d', new_name)

                        os.rename(old_path, new_path)
                        self.create_log_file(new_name, manufacturer, part_number)
                        print(f"   ✅ Extracted and renamed to: {new_name}")
                        return new_name

                except Exception as e:
                    print(f"   ❌ Error extracting ZIP: {e}")

            print(f"   ⏳ Checking... ({(i+1)*5}/60 seconds)")

        print(f"   ❌ No files downloaded after 60 seconds")
        return None



    def create_log_file(self, filename, manufacturer, part_number):
        """Create log file for successful download"""
        log_name = filename.replace('.step', '.txt')
        log_path = os.path.join('3D', log_name)
        
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(f"{manufacturer.upper()} {part_number} STEP FILE DOWNLOAD LOG\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Manufacturer: {manufacturer}\n")
            f.write(f"Part Number: {part_number}\n")
            f.write(f"Source: UltraLibrarian (Complete 6-Screen Automation)\n")
            f.write(f"Downloaded File: {filename}\n")
            f.write(f"Location: 3D/{filename}\n")
            f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("AUTOMATION PROCESS:\n")
            f.write("==================\n")
            f.write("Screen 1: Enter part number and search ✅\n")
            f.write("Screen 2: Select specific part from results ✅\n") 
            f.write("Screen 3: Find the right variant and click ✅\n")
            f.write("Screen 4: Hit 'Download Now' ✅\n")
            f.write("Screen 5: Hit '3D Model' ✅\n")
            f.write("Screen 6: Login and download ✅\n\n")
            f.write("Status: SUCCESS - Complete 6-screen automation worked!\n")

    def run_complete_automation(self, manufacturer, part_number):
        """Run the complete 6-screen automation"""
        print(f"\nCOMPLETE ULTRALIBRARIAN AUTOMATION")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print("=" * 50)
        
        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            # Execute all 6 screens
            if not self.screen_1_search(driver, manufacturer, part_number):
                print(f"\n❌ Screen 1 failed!")
                return None
            print(f"\n✅ SCREEN 1 COMPLETE")

            if not self.screen_2_select_part(driver, part_number):
                print(f"\n❌ Screen 2 failed!")
                return None
            print(f"\n✅ SCREEN 2 COMPLETE")

            if not self.screen_3_find_right_variant(driver, part_number):
                print(f"\n❌ Screen 3 failed!")
                return None
            print(f"\n✅ SCREEN 3 COMPLETE")

            if not self.screen_4_download_now(driver):
                print(f"\n❌ Screen 4 failed!")
                return None
            print(f"\n✅ SCREEN 4 COMPLETE")

            if not self.screen_5_3d_model(driver):
                print(f"\n❌ Screen 5 failed!")
                return None
            print(f"\n✅ SCREEN 5 COMPLETE")
            
            # Screen 6 requires manual login
            result = self.screen_6_login_and_download(driver, manufacturer, part_number)
            
            if result:
                print(f"\n🎉 COMPLETE SUCCESS!")
                print(f"Downloaded: {result}")
                return result
            else:
                print(f"\n❌ Screen 6 failed!")
                return None
            
        except Exception as e:
            print(f"Error: {e}")
            return None
        finally:
            driver.quit()

def main():
    if len(os.sys.argv) < 3:
        print("Usage: python complete_ultralibrarian_automation.py \"Manufacturer\" \"Part Number\"")
        print("\nExample: python complete_ultralibrarian_automation.py \"TI\" \"LM358N\"")
        return
    
    manufacturer = os.sys.argv[1]
    part_number = os.sys.argv[2]
    
    automation = CompleteUltraLibrarianAutomation()
    result = automation.run_complete_automation(manufacturer, part_number)
    
    if result:
        print(f"\n✅ AUTOMATION SUCCESSFUL!")
        print(f"File: 3D/{result}")
    else:
        print(f"\n❌ AUTOMATION FAILED!")

if __name__ == "__main__":
    main()
