#!/usr/bin/env python3
"""
Run UltraLibrarian automation with screen visible for debugging
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_ultralibrarian_automation import CompleteUltraLibrarianAutomation

def run_with_screen():
    print("🔸 Running UltraLibrarian automation with screen visible")
    print("=" * 60)
    
    automation = CompleteUltraLibrarianAutomation()
    driver = automation.setup_driver()
    
    if not driver:
        print("❌ Failed to setup driver")
        return
    
    try:
        # Run Screen 1 and keep browser open
        print("\n🔸 SCREEN 1: Login and search")
        result1 = automation.screen_1_search(driver, "Texas Instruments", "LM358N")
        if not result1:
            print("❌ Screen 1 failed")
            input("Press Enter to close browser...")
            return
        print("✅ Screen 1 completed")
        input("Press Enter to continue to Screen 2...")
        
        # Run Screen 2 and keep browser open
        print("\n🔸 SCREEN 2: Select part")
        result2 = automation.screen_2_select_part(driver, "LM358N")
        if not result2:
            print("❌ Screen 2 failed")
            input("Press Enter to close browser...")
            return
        print("✅ Screen 2 completed")
        input("Press Enter to continue to Screen 3...")
        
        # Run Screen 3 and keep browser open
        print("\n🔸 SCREEN 3: Part details")
        result3 = automation.screen_3_find_right_variant(driver, "LM358N")
        if not result3:
            print("❌ Screen 3 failed")
            input("Press Enter to close browser...")
            return
        print("✅ Screen 3 completed")
        input("Press Enter to continue to Screen 4...")
        
        # Run Screen 4 and keep browser open
        print("\n🔸 SCREEN 4: Download Now")
        result4 = automation.screen_4_download_now(driver)
        if not result4:
            print("❌ Screen 4 failed")
            input("Press Enter to close browser...")
            return
        print("✅ Screen 4 completed")
        input("Press Enter to continue to Screen 5...")
        
        # Run Screen 5 and keep browser open
        print("\n🔸 SCREEN 5: 3D Model")
        result5 = automation.screen_5_3d_model(driver)
        if not result5:
            print("❌ Screen 5 failed")
            input("Press Enter to close browser...")
            return
        print("✅ Screen 5 completed")
        input("Press Enter to continue to Screen 6...")
        
        # Run Screen 6 and keep browser open
        print("\n🔸 SCREEN 6: Final download")
        result6 = automation.screen_6_login_and_download(driver, "Texas Instruments", "LM358N")
        if result6:
            print(f"✅ Screen 6 completed: {result6}")
        else:
            print("❌ Screen 6 failed")
        
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to close browser...")
    finally:
        driver.quit()

if __name__ == "__main__":
    run_with_screen()
