#!/usr/bin/env python3
"""
Move files to correct directories and create the needed programs
"""

import os
import shutil

def fix_file_locations():
    print("📁 MOVING FILES TO CORRECT DIRECTORIES")
    print("=" * 40)
    
    # Move datasheet to datasheets directory
    old_datasheet = 'files-download/Diodes Inc-APX803L20-30SA-7.pdf'
    new_datasheet = 'datasheets/Diodes Inc-APX803L20-30SA-7.pdf'
    
    if os.path.exists(old_datasheet):
        # Create datasheets directory if it doesn't exist
        os.makedirs('datasheets', exist_ok=True)
        
        shutil.move(old_datasheet, new_datasheet)
        print(f"✅ Moved datasheet:")
        print(f"   From: {old_datasheet}")
        print(f"   To:   {new_datasheet}")
        
        # Verify file
        if os.path.exists(new_datasheet):
            size = os.path.getsize(new_datasheet)
            print(f"   📄 Verified: {size:,} bytes")
        else:
            print(f"   ❌ Move failed")
    else:
        print(f"❌ Datasheet not found: {old_datasheet}")
    
    # Verify 3D file location
    step_file = '3d/Diodes-APX803L20-30SA-7.step'
    if os.path.exists(step_file):
        size = os.path.getsize(step_file)
        print(f"✅ 3D file confirmed: {step_file} ({size:,} bytes)")
    else:
        print(f"❌ 3D file not found: {step_file}")

def main():
    print("🚀 FIXING FILE LOCATIONS")
    print("Moving files to correct directories")
    print("=" * 40)
    
    fix_file_locations()
    
    print("\n" + "=" * 40)
    print("✅ FILES ORGANIZED:")
    print("   📄 Datasheets → datasheets/")
    print("   🎯 3D Models → 3d/")
    print("\n📋 NEXT STEPS:")
    print("1. Create modular Digikey program")
    print("2. Create Mouser program") 
    print("3. Create programs for 3 other 3D websites")
    print("4. Save all working methods")

if __name__ == "__main__":
    main()
