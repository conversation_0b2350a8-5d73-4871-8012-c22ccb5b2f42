#!/usr/bin/env python3
"""
CLICK TI PART
=============
Click on Texas Instruments LM358N part.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def click_ti_part():
    print("🎯 CLICK TI PART")
    print("=" * 20)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick navigation to search results
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        print("✅ At search results")
        
        # Click Texas Instruments LM358N
        print("🔸 Looking for Texas Instruments LM358N...")
        
        links = driver.find_elements(By.TAG_NAME, "a")
        ti_link = None
        
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    ti_link = link
                    print(f"✅ Found TI part: {text}")
                    break
            except:
                continue
        
        if ti_link:
            print("🔸 Clicking TI part...")
            ti_link.click()
            time.sleep(8)
            print("✅ Clicked TI part - should be at Screen 3 now")
        else:
            print("❌ Could not find TI part")
        
        # Stay open
        print("\n🔒 BROWSER STAYING OPEN")
        print("You should now see Screen 3 - TI LM358N part details")
        
        while True:
            time.sleep(10)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        while True:
            time.sleep(10)

if __name__ == "__main__":
    click_ti_part()
