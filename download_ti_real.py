#!/usr/bin/env python3
"""
Download TI datasheet using the real link from Digikey
"""

import requests
import os

def download_ti_real():
    print("🎯 DOWNLOADING TI DATASHEET - REAL LINK")
    print("=" * 50)
    
    # The actual TI datasheet URL from Digikey
    ti_url = "https://www.ti.com/general/docs/suppproductinfo.tsp?distId=10&gotoUrl=https%3A%2F%2Fwww.ti.com%2Flit%2Fgpn%2Flm158-n"
    
    print(f"📥 Downloading from TI...")
    print(f"   URL: {ti_url}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        response = session.get(ti_url, timeout=60, stream=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            print(f"   Content-Type: {content_type}")
            
            # Check if it's a PDF
            if 'application/pdf' in content_type:
                print("   ✅ Direct PDF download!")
                
                os.makedirs('datasheets', exist_ok=True)
                filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                
                with open(filename, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                file_size = os.path.getsize(filename)
                print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                
                if file_size > 10000:
                    print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                    return True
                else:
                    print(f"   ⚠️  File too small")
                    return False
                    
            elif 'text/html' in content_type:
                print("   📄 HTML page - looking for PDF links...")
                
                # Save the HTML page
                with open('ti_redirect_page.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                # Look for PDF links
                import re
                pdf_links = re.findall(r'href="([^"]*\.pdf[^"]*)"', response.text)
                
                if pdf_links:
                    pdf_url = pdf_links[0]
                    if not pdf_url.startswith('http'):
                        pdf_url = f"https://www.ti.com{pdf_url}"
                    
                    print(f"   Found PDF link: {pdf_url}")
                    
                    # Download the PDF
                    pdf_response = session.get(pdf_url, timeout=60, stream=True)
                    print(f"   PDF Status: {pdf_response.status_code}")
                    
                    if pdf_response.status_code == 200:
                        os.makedirs('datasheets', exist_ok=True)
                        filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                        
                        with open(filename, 'wb') as f:
                            for chunk in pdf_response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        
                        file_size = os.path.getsize(filename)
                        print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                        
                        if file_size > 10000:
                            print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                            return True
                        else:
                            print(f"   ⚠️  File too small")
                            return False
                    else:
                        print(f"   ❌ PDF download failed: {pdf_response.status_code}")
                        return False
                else:
                    print(f"   ❌ No PDF links found in HTML")
                    return False
            else:
                print(f"   ⚠️  Unexpected content type: {content_type}")
                return False
        else:
            print(f"   ❌ Download failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = download_ti_real()
    if success:
        print("\n🎉 TEXAS INSTRUMENTS DATASHEET SUCCESSFULLY DOWNLOADED!")
    else:
        print("\n❌ FAILED TO DOWNLOAD TI DATASHEET")
