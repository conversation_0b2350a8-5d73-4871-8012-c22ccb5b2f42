#!/usr/bin/env python3
"""
Direct STEP file download from known sources
Writes output to log file to bypass terminal issues
"""

import requests
import os
import sys
from datetime import datetime

def log_message(message):
    """Write message to both console and log file"""
    print(message)
    with open('step_download_log.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {message}\n")

def download_from_traceparts(part_number):
    """Try TraceParts - they have a good API-like search"""
    log_message(f"🔍 SEARCHING TRACEPARTS FOR: {part_number}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        # TraceParts search URL
        search_url = "https://www.traceparts.com/en/search"
        params = {'Keywords': part_number}
        
        log_message(f"   Searching TraceParts...")
        response = session.get(search_url, params=params, timeout=30)
        log_message(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save response for inspection
            with open('traceparts_search.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            log_message(f"   📄 Saved TraceParts search results")
            
            # Check if we found anything
            if part_number.lower() in response.text.lower():
                log_message(f"   ✅ Found {part_number} mentioned in TraceParts!")
                return True
            else:
                log_message(f"   ❌ No results for {part_number} in TraceParts")
                return False
        else:
            log_message(f"   ❌ TraceParts search failed: {response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ TraceParts error: {e}")
        return False

def download_from_grabcad(part_number):
    """Try GrabCAD - community 3D models"""
    log_message(f"🔍 SEARCHING GRABCAD FOR: {part_number}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        # GrabCAD search URL
        search_url = "https://grabcad.com/library"
        params = {'query': part_number}
        
        log_message(f"   Searching GrabCAD...")
        response = session.get(search_url, params=params, timeout=30)
        log_message(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save response
            with open('grabcad_search.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            log_message(f"   📄 Saved GrabCAD search results")
            
            # Look for STEP file indicators
            step_indicators = ['step', 'stp', 'solidworks', 'download']
            found_indicators = []
            
            response_text = response.text.lower()
            for indicator in step_indicators:
                if indicator in response_text:
                    found_indicators.append(indicator)
            
            if found_indicators:
                log_message(f"   ✅ Found 3D content indicators: {found_indicators}")
                return True
            else:
                log_message(f"   ❌ No 3D content found for {part_number}")
                return False
        else:
            log_message(f"   ❌ GrabCAD search failed: {response.status_code}")
            return False
            
    except Exception as e:
        log_message(f"   ❌ GrabCAD error: {e}")
        return False

def try_direct_diodes_download():
    """Try to find and download directly from Diodes Inc"""
    log_message(f"🔍 TRYING DIRECT DIODES INC DOWNLOAD")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Known Diodes Inc URLs to try
    diodes_urls = [
        "https://www.diodes.com/assets/3D-Models/APX803L20-30SA-7.step",
        "https://www.diodes.com/assets/3d/APX803L20-30SA-7.step", 
        "https://www.diodes.com/assets/cad/APX803L20-30SA-7.step",
        "https://www.diodes.com/products/analog/voltage-supervisors/apx803/",
        "https://www.diodes.com/part/APX803L20-30SA-7/"
    ]
    
    for url in diodes_urls:
        try:
            log_message(f"   Trying: {url}")
            response = session.get(url, timeout=30)
            log_message(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                if url.endswith('.step'):
                    # This is a direct STEP file
                    os.makedirs('3d', exist_ok=True)
                    filename = 'APX803L20-30SA-7_Diodes_Direct.step'
                    filepath = os.path.join('3d', filename)
                    
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    
                    file_size = os.path.getsize(filepath)
                    log_message(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                    
                    if file_size > 1000:
                        log_message(f"   🎉 SUCCESS: Got actual STEP file from Diodes!")
                        return True
                else:
                    # This is a product page, save for inspection
                    filename = f"diodes_page_{url.split('/')[-2]}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    log_message(f"   📄 Saved page: {filename}")
                    
                    # Look for download links
                    if 'step' in response.text.lower() or 'download' in response.text.lower():
                        log_message(f"   ✅ Found download content on page!")
                        return True
            
        except Exception as e:
            log_message(f"   ❌ Error with {url}: {e}")
            continue
    
    return False

def main():
    # Clear the log file
    with open('step_download_log.txt', 'w') as f:
        f.write(f"STEP File Download Log - {datetime.now()}\n")
        f.write("=" * 50 + "\n")
    
    part_number = "APX803L20-30SA-7"
    
    log_message("🚀 DIRECT STEP FILE DOWNLOAD ATTEMPT")
    log_message("=" * 50)
    
    # Try multiple sources
    sources_tried = []
    
    # 1. Try direct Diodes download
    log_message("\n1. TRYING DIRECT DIODES INC DOWNLOAD")
    if try_direct_diodes_download():
        log_message("✅ SUCCESS with Diodes Inc!")
        sources_tried.append("Diodes Inc - SUCCESS")
    else:
        log_message("❌ No luck with direct Diodes download")
        sources_tried.append("Diodes Inc - Failed")
    
    # 2. Try TraceParts
    log_message("\n2. TRYING TRACEPARTS")
    if download_from_traceparts(part_number):
        log_message("✅ Found content on TraceParts!")
        sources_tried.append("TraceParts - Found content")
    else:
        log_message("❌ No luck with TraceParts")
        sources_tried.append("TraceParts - Failed")
    
    # 3. Try GrabCAD
    log_message("\n3. TRYING GRABCAD")
    if download_from_grabcad(part_number):
        log_message("✅ Found content on GrabCAD!")
        sources_tried.append("GrabCAD - Found content")
    else:
        log_message("❌ No luck with GrabCAD")
        sources_tried.append("GrabCAD - Failed")
    
    # Summary
    log_message("\n" + "=" * 50)
    log_message("📋 SUMMARY:")
    for source in sources_tried:
        log_message(f"   {source}")
    
    # Check if we got any actual STEP files
    step_files = []
    if os.path.exists('3d'):
        for file in os.listdir('3d'):
            if file.endswith('.step') and not file.startswith('KiCad_'):
                step_files.append(file)
    
    if step_files:
        log_message(f"\n🎉 ACTUAL STEP FILES DOWNLOADED:")
        for file in step_files:
            filepath = os.path.join('3d', file)
            size = os.path.getsize(filepath)
            log_message(f"   ✅ {file} ({size:,} bytes)")
    else:
        log_message(f"\n❌ NO ACTUAL STEP FILES DOWNLOADED YET")
        log_message(f"   Only have generic KiCad models")
    
    log_message(f"\n📄 Check step_download_log.txt for full details")
    log_message(f"📄 Check saved HTML files for manual inspection")

if __name__ == "__main__":
    main()
