#!/usr/bin/env python3
"""
Test Mouser's actual search interface with the WURTH part
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import quote, urljoin
import time

def test_mouser_real_search():
    print("🧪 TESTING MOUSER REAL SEARCH")
    print("=" * 60)
    
    part_number = "435151014845"
    manufacturer = "WURTH"
    print(f"🔍 Searching for: {manufacturer} {part_number}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive'
    })
    
    # Test the actual Mouser search URLs
    search_urls = [
        f"https://www.mouser.com/c/?q={quote(part_number)}",
        f"https://www.mouser.com/ProductDetail/?q={quote(part_number)}",
        f"https://www.mouser.com/c/connectors/?q={quote(part_number)}",
        f"https://www.mouser.com/ProductDetail/{quote(manufacturer)}/{quote(part_number)}/"
    ]
    
    for i, search_url in enumerate(search_urls, 1):
        print(f"\n🔍 Test {i}: {search_url}")
        print("-" * 40)
        
        try:
            time.sleep(2)  # Be respectful
            response = session.get(search_url, timeout=25)
            
            print(f"Status: {response.status_code}")
            print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
            print(f"Content-Length: {len(response.content)} bytes")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()
                
                # Check if part number is found
                if part_number.lower() in page_text:
                    print(f"✅ Found part number {part_number} on page")
                    
                    # Look for WURTH/Würth mentions
                    wurth_count = page_text.count('wurth') + page_text.count('würth')
                    print(f"📊 Found {wurth_count} mentions of WURTH/Würth")
                    
                    # Look for manufacturer-related text
                    if 'manufacturer' in page_text:
                        print("✅ Found 'manufacturer' text on page")
                    if 'mfg' in page_text:
                        print("✅ Found 'mfg' text on page")
                    if 'brand' in page_text:
                        print("✅ Found 'brand' text on page")
                    
                    # Look for datasheet links
                    datasheet_links = []
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        link_text = link.get_text().lower()
                        
                        if any(keyword in link_text for keyword in ['datasheet', 'data sheet', 'pdf', 'specification', 'technical data']):
                            datasheet_links.append(href[:80])
                    
                    if datasheet_links:
                        print(f"📄 Found {len(datasheet_links)} datasheet links:")
                        for link in datasheet_links[:3]:  # Show first 3
                            print(f"   {link}")
                        if len(datasheet_links) > 3:
                            print(f"   ... and {len(datasheet_links) - 3} more")
                    else:
                        print("❌ No datasheet links found")
                    
                    # Look for external manufacturer links
                    external_links = []
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        if 'we-online.com' in href or ('wurth' in href.lower() and 'mouser.com' not in href):
                            external_links.append(href[:80])
                    
                    if external_links:
                        print(f"🌐 Found {len(external_links)} external manufacturer links:")
                        for link in external_links[:2]:  # Show first 2
                            print(f"   {link}")
                    
                    # Look for PDF links
                    pdf_links = []
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        if href.lower().endswith('.pdf') and ('wurth' in href.lower() or 'we-online' in href.lower()):
                            pdf_links.append(href[:80])
                    
                    if pdf_links:
                        print(f"📄 Found {len(pdf_links)} PDF links:")
                        for link in pdf_links[:2]:  # Show first 2
                            print(f"   {link}")
                    
                    # This URL worked!
                    print(f"🎉 SUCCESS: This URL found the part!")
                    break
                    
                else:
                    print(f"❌ Part number {part_number} not found on page")
                    
            elif response.status_code == 429:
                print("⚠️ Rate limited by Mouser")
                print("   Waiting 5 seconds before next attempt...")
                time.sleep(5)
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")

if __name__ == "__main__":
    test_mouser_real_search()
