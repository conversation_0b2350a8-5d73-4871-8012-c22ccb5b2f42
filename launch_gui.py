#!/usr/bin/env python3
"""
Simple launcher for the Component Finder GUI
"""

import os
import sys

def main():
    print("=" * 60)
    print("🚀 COMPONENT FINDER GUI LAUNCHER")
    print("=" * 60)
    print(f"📁 Current directory: {os.getcwd()}")
    print(f"🐍 Python version: {sys.version}")
    print(f"📂 Script location: {__file__}")
    
    # Check if tkinter is available
    try:
        import tkinter as tk
        print("✅ Tkinter is available")
        
        # Test basic tkinter functionality
        print("🧪 Testing tkinter...")
        root = tk.Tk()
        root.withdraw()  # Hide the test window
        print("✅ Tkinter test passed")
        root.destroy()
        
    except ImportError as e:
        print(f"❌ Tkinter not available: {e}")
        input("Press Enter to exit...")
        return
    
    # Try to import the main GUI
    try:
        print("📦 Importing component_finder_gui...")
        from component_finder_gui import ComponentFinderGUI
        print("✅ Import successful")
        
        print("🖥️ Creating main window...")
        root = tk.Tk()
        root.title("Component Finder - Interactive Learning System")
        root.geometry("800x700")
        
        # Force window to be visible
        root.lift()
        root.attributes('-topmost', True)
        root.after(100, lambda: root.attributes('-topmost', False))
        
        print("🔧 Initializing GUI...")
        app = ComponentFinderGUI(root)
        
        print("✅ GUI ready!")
        print("🎯 Starting main loop...")
        print("📋 Look for the window titled: 'Component Finder - Interactive Learning System'")
        print("=" * 60)
        
        root.mainloop()
        
        print("🏁 GUI closed normally")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
