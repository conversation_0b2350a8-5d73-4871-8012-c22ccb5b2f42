#!/usr/bin/env python3
"""
Show TI search with visible browser so you can see what's happening
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def show_ti_search_visible():
    print("SHOWING TI SEARCH - VISIBLE BROWSER")
    print("=" * 40)
    
    # Setup browser - VISIBLE
    options = Options()
    options.add_argument("--start-maximized")
    # DO NOT add headless - we want to see it
    driver = webdriver.Chrome(options=options)
    
    try:
        print("1. Going to TI.com - YOU SHOULD SEE BROWSER WINDOW")
        driver.get("https://www.ti.com")
        time.sleep(10)
        
        print("2. Going to search results - YOU SHOULD SEE SEARCH PAGE")
        search_url = "https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm=LM358N"
        driver.get(search_url)
        time.sleep(10)
        
        print(f"3. Current URL: {driver.current_url}")
        print("4. <PERSON><PERSON><PERSON> should be visible showing search results")
        
        input("Press Enter when you've seen the results...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    show_ti_search_visible()
