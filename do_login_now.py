#!/usr/bin/env python3
"""
DO LOGIN NOW
============
Handle the login on the current screen.
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def do_login_now():
    print("🎯 DO LOGIN NOW")
    print("=" * 20)
    
    # Setup Chrome with downloads
    chrome_options = Options()
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # Navigate to login screen quickly
        print("🔸 Navigating to login screen...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        # Quick navigation through all steps
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    link.click()
                    break
            except:
                continue
        time.sleep(8)
        
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            driver.execute_script("arguments[0].click();", download_btns[0])
            time.sleep(5)
        
        cad_btns = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
        if cad_btns:
            driver.execute_script("arguments[0].click();", cad_btns[0])
            time.sleep(5)
        
        if len(driver.window_handles) > 1:
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        step_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP')]")
        for elem in step_elements:
            if elem.is_displayed():
                driver.execute_script("arguments[0].click();", elem)
                time.sleep(3)
                break
        
        download_elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')]")
        for elem in download_elements:
            if elem.is_displayed():
                driver.execute_script("arguments[0].click();", elem)
                time.sleep(5)
                break
        
        print("✅ At login screen")
        
        # DO LOGIN NOW
        print("\n🔸 DOING LOGIN NOW...")
        
        # Load credentials
        try:
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)
            
            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            
            print(f"Using email: {email}")
            
            # Find email input
            email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email'], input[placeholder*='email'], input[name*='Email'], input[name*='username'], input[name*='Username']")
            
            email_input = None
            for inp in email_inputs:
                if inp.is_displayed() and inp.is_enabled():
                    email_input = inp
                    print(f"✅ Found email input: name='{inp.get_attribute('name')}', type='{inp.get_attribute('type')}'")
                    break
            
            if not email_input:
                # Try any visible text input
                text_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='text']")
                for inp in text_inputs:
                    if inp.is_displayed() and inp.is_enabled():
                        email_input = inp
                        print("✅ Using first visible text input as email field")
                        break
            
            if email_input:
                email_input.clear()
                email_input.send_keys(email)
                print("✅ Entered email")
                time.sleep(2)
            else:
                print("❌ No email input found!")
                return
            
            # Find password input
            password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
            
            password_input = None
            for inp in password_inputs:
                if inp.is_displayed() and inp.is_enabled():
                    password_input = inp
                    print("✅ Found password input")
                    break
            
            if password_input:
                password_input.clear()
                password_input.send_keys(password)
                print("✅ Entered password")
                time.sleep(2)
            else:
                print("❌ No password input found!")
                return
            
            # Submit login
            submit_buttons = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit'], button:contains('Login'), button:contains('Sign In')")
            
            login_submitted = False
            for btn in submit_buttons:
                if btn.is_displayed() and btn.is_enabled():
                    driver.execute_script("arguments[0].click();", btn)
                    print("✅ Clicked login button")
                    login_submitted = True
                    break
            
            if not login_submitted:
                # Try pressing Enter on password field
                password_input.send_keys(Keys.RETURN)
                print("✅ Pressed Enter to submit login")
            
            time.sleep(10)
            print(f"After login URL: {driver.current_url}")
            
            # Monitor for downloads
            print("\n🔸 MONITORING FOR DOWNLOADS...")
            
            for i in range(24):  # Monitor for 2 minutes
                time.sleep(5)
                current_files = set(os.listdir('3D'))
                new_files = current_files - initial_files
                
                if new_files:
                    print(f"🎉 NEW FILES: {list(new_files)}")
                    
                    step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                    if step_files:
                        print(f"✅ STEP FILES DOWNLOADED: {step_files}")
                        return step_files[0]
                    
                    zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                    if zip_files:
                        print(f"📦 ZIP FILE: {zip_files[0]}")
                        try:
                            import zipfile
                            with zipfile.ZipFile(os.path.join('3D', zip_files[0]), 'r') as zip_ref:
                                zip_ref.extractall('3D')
                            
                            final_files = set(os.listdir('3D'))
                            extracted_files = final_files - current_files
                            step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                            
                            if step_files:
                                print(f"✅ STEP FILE EXTRACTED: {step_files[0]}")
                                return step_files[0]
                        except Exception as e:
                            print(f"Error extracting: {e}")
                
                print(f"  Checking... ({(i+1)*5}/120 seconds)")
            
            print("⏳ No files downloaded after 2 minutes")
            
        except Exception as e:
            print(f"❌ Login error: {e}")
        
        # Keep browser open
        print("\n🔒 BROWSER STAYING OPEN")
        while True:
            time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        while True:
            time.sleep(10)

if __name__ == "__main__":
    result = do_login_now()
    if result:
        print(f"\n🎉 SUCCESS: {result}")
    else:
        print(f"\n⚠️ Login completed but no download detected")
