#!/usr/bin/env python3
"""
Use JavaScript to interact with TI search box directly
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def ti_javascript_search():
    print("TI JAVASCRIPT SEARCH")
    print("=" * 30)
    
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        print("1. Loading ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(15)
        
        print("2. Using JavaScript to find and use search box...")
        
        # Try different JavaScript approaches
        js_commands = [
            # Try to find search input and set value
            "document.querySelector('input[type=\"search\"]').value = 'LM358N';",
            "document.querySelector('input[placeholder*=\"search\"]').value = 'LM358N';",
            "document.querySelector('#searchboxheader input').value = 'LM358N';",
            "document.querySelector('.search input').value = 'LM358N';",
            
            # Try to trigger search
            "document.querySelector('input[type=\"search\"]').dispatchEvent(new Event('input'));",
            "document.querySelector('form').submit();",
            
            # Try clicking search button
            "document.querySelector('button[type=\"submit\"]').click();",
            "document.querySelector('.search-button').click();",
        ]
        
        for i, cmd in enumerate(js_commands):
            try:
                print(f"Trying JS command {i+1}: {cmd[:50]}...")
                result = driver.execute_script(cmd)
                time.sleep(3)
                print(f"Command {i+1} executed successfully")
            except Exception as e:
                print(f"Command {i+1} failed: {e}")
        
        time.sleep(10)
        
        print(f"3. Final URL: {driver.current_url}")
        
        if "LM358N" in driver.page_source.upper():
            print("✅ SUCCESS: Found LM358N!")
        else:
            print("❌ FAILED: No LM358N found")
        
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    ti_javascript_search()
