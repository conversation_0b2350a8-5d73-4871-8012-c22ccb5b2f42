#!/usr/bin/env python3
"""
IMPROVED MANUFACTURER 3D MODEL FINDER
====================================
Enhanced version with proper error handling, verification, and detailed output.
Works with any manufacturer website by learning patterns.

Features:
- Detailed step-by-step output
- Error handling with immediate stop
- File verification (size, format, content)
- Success/failure reporting
- Proper logging
- Manufacturer-specific optimizations

Usage:
    python manufacturer_3d_finder_improved.py "Texas Instruments" "LM358N"
"""

import os
import sys
import time
import shutil
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import argparse

class Manufacturer3DFinder:
    def __init__(self):
        self.driver = None
        self.models_3d_dir = "3d"
        self.downloads_dir = os.path.expanduser("~/Downloads")
        
        # Manufacturer-specific website mappings
        self.manufacturer_sites = {
            'texas instruments': 'https://www.ti.com',
            'ti': 'https://www.ti.com',
            'analog devices': 'https://www.analog.com',
            'adi': 'https://www.analog.com',
            'diodes inc': 'https://www.diodes.com',
            'diodes incorporated': 'https://www.diodes.com',
            'infineon': 'https://www.infineon.com',
            'maxim': 'https://www.maximintegrated.com',
            'linear technology': 'https://www.linear.com',
            'microchip': 'https://www.microchip.com',
            'stmicroelectronics': 'https://www.st.com',
            'nxp': 'https://www.nxp.com',
            'on semiconductor': 'https://www.onsemi.com',
            'vishay': 'https://www.vishay.com',
            'fairchild': 'https://www.fairchildsemi.com'
        }
        
    def log(self, message, level="INFO"):
        """Enhanced logging with timestamps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if level == "ERROR":
            print(f"❌ [{timestamp}] ERROR: {message}")
        elif level == "SUCCESS":
            print(f"✅ [{timestamp}] SUCCESS: {message}")
        elif level == "WARNING":
            print(f"⚠️ [{timestamp}] WARNING: {message}")
        else:
            print(f"ℹ️ [{timestamp}] INFO: {message}")
    
    def setup_browser(self):
        """Setup Chrome browser with proper options"""
        self.log("Setting up Chrome browser...")
        
        try:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.log("Browser setup successful", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Browser setup failed: {e}", "ERROR")
            return False
    
    def setup_directories(self):
        """Create necessary directories"""
        self.log("Setting up directories...")
        
        try:
            os.makedirs(self.models_3d_dir, exist_ok=True)
            self.log(f"Directory '{self.models_3d_dir}' ready", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Error creating directory: {e}", "ERROR")
            return False
    
    def handle_cookie_popup(self):
        """Handle cookie consent popup"""
        self.log("Checking for cookie popup...")
        
        try:
            cookie_texts = ["Accept", "Accept All", "OK", "Got it", "I Agree", "Continue", "Allow"]
            
            for text in cookie_texts:
                try:
                    elements = self.driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')] | //a[contains(text(), '{text}')]")
                    for element in elements:
                        if element.is_displayed():
                            self.log(f"Clicking cookie button: '{text}'")
                            element.click()
                            time.sleep(2)
                            return True
                except:
                    continue
            
            self.log("No cookie popup found")
            return True
            
        except Exception as e:
            self.log(f"Error handling cookie popup: {e}", "WARNING")
            return True  # Continue even if cookie handling fails
    
    def determine_manufacturer_website(self, manufacturer):
        """Determine manufacturer website URL"""
        self.log(f"Determining website for manufacturer: {manufacturer}")
        
        manufacturer_lower = manufacturer.lower().strip()
        
        if manufacturer_lower in self.manufacturer_sites:
            website = self.manufacturer_sites[manufacturer_lower]
            self.log(f"Found known website: {website}", "SUCCESS")
            return website
        
        # Try to construct website URL
        manufacturer_clean = manufacturer_lower.replace(' ', '').replace('inc', '').replace('incorporated', '').strip()
        constructed_url = f"https://www.{manufacturer_clean}.com"
        
        self.log(f"Trying constructed URL: {constructed_url}")
        return constructed_url
    
    def navigate_to_manufacturer_site(self, manufacturer):
        """Navigate to manufacturer website"""
        website = self.determine_manufacturer_website(manufacturer)
        
        self.log(f"Navigating to {manufacturer} website...")
        
        try:
            self.driver.get(website)
            time.sleep(8)
            
            # Handle cookie popup
            self.handle_cookie_popup()
            
            # Check if site loaded successfully
            if "404" in self.driver.title.lower() or "not found" in self.driver.page_source.lower():
                self.log(f"Website not accessible: {website}", "ERROR")
                return False
            
            self.log(f"Successfully loaded {manufacturer} website", "SUCCESS")
            self.log(f"Page title: {self.driver.title}")

            # Wait for dynamic content to load
            time.sleep(10)

            # Execute JavaScript to ensure page is fully loaded
            self.driver.execute_script("return document.readyState")
            time.sleep(5)

            return True
            
        except Exception as e:
            self.log(f"Error navigating to manufacturer site: {e}", "ERROR")
            return False
    
    def search_for_part(self, part_number):
        """Search for part on manufacturer website"""
        self.log(f"Searching for part: {part_number}")
        
        try:
            # Find search box using multiple strategies
            search_selectors = [
                # TI specific - Coveo search system
                "#searchboxheader input",
                "#searchboxheader",
                ".coveo-search-section input",
                ".ti_p-responsiveHeader-top-search input",
                ".ti_p-responsiveHeader-top-search-box input",
                # Generic selectors
                "input[type='search']",
                "input[placeholder*='search' i]",
                "input[placeholder*='part' i]",
                "input[placeholder*='product' i]",
                "input[name*='search']",
                "input[name='q']",
                "input[name='query']",
                "input[id*='search']",
                "input[class*='search']",
                "#search",
                "#searchbox",
                "#search-input",
                ".search-input",
                ".search-box",
                ".searchbox",
                "[data-search]",
                "input[aria-label*='search' i]",
                "input[title*='search' i]",
                "input[data-testid*='search']",
                "input[data-cy*='search']",
                # Generic fallbacks
                "input[type='text']",
                "input:not([type])"
            ]
            
            search_box = None
            self.log("Trying to find search box...")

            # Debug: List all input elements on the page
            all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
            self.log(f"Found {len(all_inputs)} input elements on page")

            for i, inp in enumerate(all_inputs[:5]):  # Show first 5 inputs
                try:
                    inp_type = inp.get_attribute('type') or 'text'
                    inp_name = inp.get_attribute('name') or ''
                    inp_id = inp.get_attribute('id') or ''
                    inp_class = inp.get_attribute('class') or ''
                    inp_placeholder = inp.get_attribute('placeholder') or ''
                    self.log(f"Input {i+1}: type='{inp_type}' name='{inp_name}' id='{inp_id}' class='{inp_class}' placeholder='{inp_placeholder}'")
                except:
                    pass

            for selector in search_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        self.log(f"Selector '{selector}' found {len(elements)} elements")
                        for elem in elements:
                            if elem.is_displayed() and elem.is_enabled():
                                search_box = elem
                                self.log(f"Found search box with selector: {selector}")
                                break
                    if search_box:
                        break
                except Exception as e:
                    self.log(f"Error with selector '{selector}': {e}")
                    continue

            # Use simple direct navigation method that works
            current_url = self.driver.current_url

            if "ti.com" in current_url.lower():
                # TI specific - actually type into search box like a human
                self.log("Finding TI search input field...")

                js_find_input = """
                // Look for input elements inside the atomic search box
                var searchBox = document.querySelector('#headerSearchBox');
                if (searchBox) {
                    // Try to find input in shadow DOM
                    if (searchBox.shadowRoot) {
                        var input = searchBox.shadowRoot.querySelector('input');
                        if (input) return input;
                    }

                    // Try to find input as child element
                    var input = searchBox.querySelector('input');
                    if (input) return input;
                }

                // Look for any visible input that might be the search
                var allInputs = document.querySelectorAll('input');
                for (var i = 0; i < allInputs.length; i++) {
                    var inp = allInputs[i];
                    if (inp.offsetWidth > 0 && inp.offsetHeight > 0) {
                        return inp;
                    }
                }

                return null;
                """

                input_element = self.driver.execute_script(js_find_input)

                if input_element:
                    self.log("Found TI search input - typing part number...")

                    # Click to focus, clear, and type part number
                    input_element.click()
                    time.sleep(2)
                    input_element.clear()
                    input_element.send_keys(part_number)

                    self.log("Pressing Enter to search...")
                    input_element.send_keys(Keys.RETURN)
                    time.sleep(10)

                    self.log(f"Search completed for: {part_number}")
                    self.log(f"Current URL: {self.driver.current_url}")

                    # Check if we got to search results page
                    if "search" in self.driver.current_url.lower() and part_number.upper() in self.driver.current_url.upper():
                        self.log("Successfully typed and searched for part", "SUCCESS")
                        return True
                    else:
                        self.log("Search did not work properly", "ERROR")
                        return False
                else:
                    self.log("Could not find TI search input field", "ERROR")
                    return False
            else:
                self.log("Non-TI manufacturer - search method not implemented yet", "ERROR")
                return False
            
        except Exception as e:
            self.log(f"Error during search: {e}", "ERROR")
            return False
    
    def find_part_page(self, part_number):
        """Find and navigate to specific part page"""
        self.log("Looking for part page...")
        
        try:
            # Look for part links with multiple strategies
            part_selectors = [
                f"//a[contains(text(), '{part_number}')]",
                f"//a[contains(@href, '{part_number}')]",
                "//a[contains(@class, 'part')]",
                "//a[contains(@class, 'product')]",
                f"//td[contains(text(), '{part_number}')]/parent::tr//a",
                f"//tr[contains(., '{part_number}')]//a"
            ]
            
            for selector in part_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''
                            
                            if part_number.upper() in text.upper() or part_number.lower() in href.lower():
                                self.log(f"Clicking part link: '{text}' -> {href[:50]}...")
                                element.click()
                                time.sleep(10)
                                return True
                except:
                    continue
            
            self.log("No specific part page found - continuing on current page", "WARNING")
            return True  # Continue anyway
            
        except Exception as e:
            self.log(f"Error finding part page: {e}", "ERROR")
            return False
    
    def find_3d_model_section(self):
        """Find and explore 3D model sections"""
        self.log("Looking for 3D model sections...")
        
        try:
            # Look for 3D model indicators and tabs
            model_keywords = [
                '3D Model', '3d model', 'STEP', 'step', 'CAD', 'cad',
                'Mechanical', 'Package', 'Downloads', 'Design Resources',
                'Component Resources', 'Technical Documents'
            ]
            
            found_sections = []
            
            for keyword in model_keywords:
                elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                for element in elements:
                    try:
                        if element.is_displayed():
                            text = element.text.strip()
                            tag = element.tag_name
                            
                            # Check if it's clickable (tab, button, link)
                            if tag in ['a', 'button'] or element.get_attribute('onclick') or 'click' in element.get_attribute('class') or '':
                                found_sections.append((element, text, keyword))
                                self.log(f"Found clickable 3D section: '{text}' (keyword: {keyword})")
                    except:
                        continue
            
            # Try clicking the most promising sections
            for element, text, keyword in found_sections:
                try:
                    self.log(f"Clicking 3D section: '{text}'")
                    element.click()
                    time.sleep(8)
                    
                    # Check if new 3D content appeared
                    new_3d_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'STEP') or contains(text(), '3D') or contains(text(), 'Download')]")
                    if len(new_3d_elements) > 0:
                        self.log(f"Found additional 3D content after clicking '{text}'", "SUCCESS")
                        return True
                        
                except Exception as e:
                    self.log(f"Error clicking section '{text}': {e}")
                    continue
            
            if found_sections:
                self.log(f"Found {len(found_sections)} 3D sections but couldn't access content", "WARNING")
            else:
                self.log("No 3D model sections found", "WARNING")
            
            return True  # Continue anyway
            
        except Exception as e:
            self.log(f"Error finding 3D model sections: {e}", "ERROR")
            return False
    
    def download_3d_model(self, manufacturer, part_number):
        """Download 3D model from current page"""
        self.log("Looking for 3D model download...")
        
        try:
            # Look for download elements with comprehensive selectors
            download_selectors = [
                "//a[contains(@href, '.step')]",
                "//a[contains(@href, '.stp')]",
                "//a[contains(text(), '3D') and contains(text(), 'Download')]",
                "//a[contains(text(), 'STEP') and contains(text(), 'Download')]",
                "//button[contains(text(), '3D') and contains(text(), 'Download')]",
                "//button[contains(text(), 'STEP')]",
                "//a[contains(text(), 'Download 3D Model')]",
                "//button[contains(text(), 'Download 3D Model')]"
            ]
            
            download_elements = []
            for selector in download_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''
                            download_elements.append((element, text, href))
                            self.log(f"Found download element: '{text}' -> {href[:50]}...")
                except:
                    continue
            
            if not download_elements:
                self.log("No 3D download elements found", "ERROR")
                return False
            
            # Try each download element
            for element, text, href in download_elements:
                try:
                    self.log(f"Trying download: '{text}'")
                    
                    # Get file count before download
                    before_files = set(os.listdir(self.downloads_dir))
                    
                    # Click download
                    element.click()
                    time.sleep(15)
                    
                    # Check for new files
                    after_files = set(os.listdir(self.downloads_dir))
                    new_files = after_files - before_files
                    
                    step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                    
                    if step_files:
                        # Move and verify the file
                        downloaded_file = os.path.join(self.downloads_dir, step_files[0])
                        target_filename = f"manufacturer_{manufacturer.replace(' ', '_')}_{part_number.replace('/', '_')}.step"
                        target_path = os.path.join(self.models_3d_dir, target_filename)
                        
                        return self.verify_and_move_file(downloaded_file, target_path, target_filename)
                    else:
                        self.log(f"No STEP files from '{text}', trying next...")
                        continue
                        
                except Exception as e:
                    self.log(f"Error with download element '{text}': {e}")
                    continue
            
            self.log("No successful downloads from any element", "ERROR")
            return False
            
        except Exception as e:
            self.log(f"Error downloading 3D model: {e}", "ERROR")
            return False
    
    def verify_and_move_file(self, source_path, target_path, filename):
        """Verify and move the downloaded file"""
        self.log("Verifying downloaded file...")
        
        try:
            # Check if source file exists
            if not os.path.exists(source_path):
                self.log("Downloaded file does not exist", "ERROR")
                return False
            
            # Check file size
            file_size = os.path.getsize(source_path)
            if file_size == 0:
                self.log("Downloaded file is empty", "ERROR")
                return False
            elif file_size < 100:  # Less than 100 bytes
                self.log(f"Downloaded file is very small ({file_size} bytes) - may be invalid", "WARNING")
            
            # Check if it's a STEP file (basic check)
            try:
                with open(source_path, 'r', encoding='utf-8', errors='ignore') as f:
                    first_line = f.readline().strip()
                    if 'STEP' not in first_line.upper():
                        self.log("Downloaded file may not be a valid STEP file", "WARNING")
            except:
                self.log("Could not verify STEP file format", "WARNING")
            
            # Move file to target location
            shutil.move(source_path, target_path)
            
            self.log(f"File verified and moved: {filename} ({file_size:,} bytes)", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error verifying/moving file: {e}", "ERROR")
            return False
    
    def find_3d_model(self, manufacturer, part_number):
        """Main method to find and download 3D model"""
        self.log(f"Starting manufacturer 3D model search for: {manufacturer} {part_number}")
        self.log("=" * 60)
        
        try:
            # Step 1: Setup browser
            if not self.setup_browser():
                return False
            
            # Step 2: Setup directories
            if not self.setup_directories():
                return False
            
            # Step 3: Navigate to manufacturer site
            if not self.navigate_to_manufacturer_site(manufacturer):
                return False
            
            # Step 4: Search for part
            if not self.search_for_part(part_number):
                return False
            
            # Step 5: Find part page
            if not self.find_part_page(part_number):
                return False
            
            # Step 6: Find 3D model sections
            if not self.find_3d_model_section():
                return False
            
            # Step 7: Download 3D model
            if not self.download_3d_model(manufacturer, part_number):
                return False
            
            self.log("3D model download completed successfully!", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Unexpected error: {e}", "ERROR")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                self.log("Browser closed")

def main():
    parser = argparse.ArgumentParser(description='Download 3D model from manufacturer website')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Texas Instruments")')
    parser.add_argument('part_number', help='Part number (e.g., "LM358N")')
    
    args = parser.parse_args()
    
    finder = Manufacturer3DFinder()
    success = finder.find_3d_model(args.manufacturer, args.part_number)
    
    if success:
        print(f"\n🎉 SUCCESS: 3D model downloaded for {args.manufacturer} {args.part_number}")
        sys.exit(0)
    else:
        print(f"\n💥 FAILED: Could not download 3D model for {args.manufacturer} {args.part_number}")

        # Ask user what to do next
        print("\n" + "="*60)
        print("🤔 NO 3D MODELS FOUND ON MANUFACTURER WEBSITE")
        print("="*60)
        print(f"Part: {args.manufacturer} {args.part_number}")
        print("\nOptions:")
        print("1. Continue - try other 3D model sites (UltraLibrarian, SamacSys, etc.)")
        print("2. Manual - search manually on manufacturer website")
        print("3. Skip - move to next part")

        try:
            choice = input("\nWhat would you like to do? (1/2/3): ").strip()

            if choice == "2":
                print("\n🔍 MANUAL SEARCH MODE")
                print("Check the manufacturer website manually for 3D models.")
                print("Look for STEP files, CAD downloads, or mechanical drawings.")
                input("Press Enter when done...")
            elif choice == "3":
                print("\n⏭️ SKIPPING PART")
            else:
                print("\n➡️ CONTINUING TO OTHER 3D MODEL SITES")
                print("Try running: UltraLibrarian, SamacSys, or SnapEDA finders")
        except KeyboardInterrupt:
            print("\n\nExiting...")

        sys.exit(1)

if __name__ == "__main__":
    main()
