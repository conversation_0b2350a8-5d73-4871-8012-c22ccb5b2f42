#!/usr/bin/env python3
"""
Proper Digikey search using their actual search URL pattern
"""

import requests

def search_digikey_proper():
    print("🔍 PROPER DIGIKEY SEARCH FOR APX803L20-30SA-7")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Use the proper search URL from their schema
    part_number = "APX803L20-30SA-7"
    search_url = f"https://www.digikey.com/en/products/result?keywords={part_number}"
    
    print(f"Using proper search URL: {search_url}")
    
    try:
        response = session.get(search_url, timeout=30)
        print(f"Status: {response.status_code}")
        
        # Save the response
        with open('digikey_search_results.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("📄 Saved search results")
        
        # Check if part found
        if part_number in response.text:
            print("✅ FOUND THE PART!")
            print(f"Response length: {len(response.text)} characters")
        else:
            print("❌ Part not found in response")
            print(f"Response length: {len(response.text)} characters")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    search_digikey_proper()
