#!/usr/bin/env python3
"""
STEP 3: Debug what appears after clicking search box
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

def debug_search():
    print("STEP 3: Debug search box click")
    print("=" * 50)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # Load TI website
        print("Loading https://www.ti.com...")
        driver.get("https://www.ti.com")
        time.sleep(15)
        
        # Find and click search box
        print("Clicking search box...")
        search_box = driver.find_element(By.CSS_SELECTOR, "#searchboxheader")
        search_box.click()
        time.sleep(5)
        
        # Debug: Show all input elements after click
        print("\nDEBUG: All input elements after clicking:")
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(all_inputs)} input elements:")
        
        for i, inp in enumerate(all_inputs):
            try:
                displayed = inp.is_displayed()
                enabled = inp.is_enabled()
                inp_type = inp.get_attribute('type') or 'text'
                inp_id = inp.get_attribute('id') or ''
                inp_class = inp.get_attribute('class') or ''
                inp_placeholder = inp.get_attribute('placeholder') or ''
                
                print(f"  {i+1}: type='{inp_type}' id='{inp_id}' class='{inp_class}' placeholder='{inp_placeholder}' displayed={displayed} enabled={enabled}")
                
                # If it looks like a search input, try to use it
                if displayed and enabled and ('search' in inp_class.lower() or 'search' in inp_placeholder.lower() or inp_type == 'search'):
                    print(f"    *** This looks like the search input! ***")
                    
                    # Try to enter text
                    print("    Trying to enter 'LM358N'...")
                    inp.clear()
                    inp.send_keys("LM358N")
                    print("    Text entered successfully!")
                    
                    from selenium.webdriver.common.keys import Keys
                    inp.send_keys(Keys.RETURN)
                    time.sleep(10)
                    
                    print(f"    Search completed! URL: {driver.current_url}")
                    break
                    
            except Exception as e:
                print(f"  {i+1}: Error - {e}")
        
        print("Debug completed.")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_search()
