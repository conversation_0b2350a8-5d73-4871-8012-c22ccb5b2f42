#!/usr/bin/env python3
"""
Test the enhanced distributor search functionality
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
from urllib.parse import quote, urljoin
import re
import time

# Add the current directory to Python path to import our GUI module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_search():
    print("🧪 TESTING ENHANCED DISTRIBUTOR SEARCH")
    print("=" * 60)
    
    # Test parameters
    manufacturer = "WURTH"
    part_number = "435151014845"
    
    print(f"🔍 Testing with: {manufacturer} {part_number}")
    print()
    
    # Create a session like the GUI does
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    # Test simple Digi-Key search
    print("🔵 TESTING SIMPLE DIGI-KEY SEARCH")
    print("-" * 40)
    
    try:
        search_url = f"https://www.digikey.com/en/products/result?keywords={quote(part_number)}"
        print(f"URL: {search_url}")
        
        time.sleep(1)  # Be respectful
        response = session.get(search_url, timeout=20)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            page_text = soup.get_text().lower()
            
            if part_number.lower() in page_text:
                print(f"✅ Found {part_number} on Digi-Key page")
                
                # Look for manufacturer patterns
                manufacturer_patterns = [
                    rf'{re.escape(manufacturer.lower())}[^\w]*elektronik',
                    rf'würth[^\w]*elektronik',
                    rf'wurth[^\w]*elektronik',
                    rf'{re.escape(manufacturer.lower())}'
                ]
                
                for pattern in manufacturer_patterns:
                    match = re.search(pattern, page_text, re.I)
                    if match:
                        found_name = match.group(0)
                        print(f"✅ Found manufacturer: {found_name}")
                        
                        # Look for datasheet links
                        datasheet_count = 0
                        for link in soup.find_all('a', href=True):
                            href = link['href']
                            link_text = link.get_text().lower()
                            
                            if any(keyword in link_text for keyword in ['datasheet', 'data sheet', 'pdf']):
                                datasheet_count += 1
                                if datasheet_count <= 3:  # Show first 3
                                    print(f"📄 Found datasheet link: {href[:60]}...")
                        
                        if datasheet_count > 3:
                            print(f"📄 ... and {datasheet_count - 3} more datasheet links")
                        
                        break
                else:
                    print("❌ No manufacturer patterns matched")
            else:
                print(f"❌ Part {part_number} not found on page")
        
        elif response.status_code == 429:
            print("⚠️ Rate limited by Digi-Key")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Digi-Key search failed: {e}")
    
    print()
    
    # Test simple Mouser search
    print("🟠 TESTING SIMPLE MOUSER SEARCH")
    print("-" * 40)
    
    try:
        search_url = f"https://www.mouser.com/c/?q={quote(part_number)}"
        print(f"URL: {search_url}")
        
        time.sleep(1)  # Be respectful
        response = session.get(search_url, timeout=20)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            page_text = soup.get_text().lower()
            
            if part_number.lower() in page_text:
                print(f"✅ Found {part_number} on Mouser page")
                
                # Look for manufacturer patterns
                manufacturer_patterns = [
                    rf'{re.escape(manufacturer.lower())}[^\w]*elektronik',
                    rf'würth[^\w]*elektronik',
                    rf'wurth[^\w]*elektronik',
                    rf'{re.escape(manufacturer.lower())}',
                    rf'manufacturer[:\s]*würth',
                    rf'manufacturer[:\s]*wurth',
                    rf'brand[:\s]*würth',
                    rf'brand[:\s]*wurth'
                ]
                
                for pattern in manufacturer_patterns:
                    match = re.search(pattern, page_text, re.I)
                    if match:
                        found_name = match.group(0)
                        print(f"✅ Found manufacturer: {found_name}")
                        
                        # Look for datasheet links
                        datasheet_count = 0
                        external_count = 0
                        
                        for link in soup.find_all('a', href=True):
                            href = link['href']
                            link_text = link.get_text().lower()
                            
                            # Internal datasheet links
                            if any(keyword in link_text for keyword in ['datasheet', 'data sheet', 'pdf', 'specification']):
                                datasheet_count += 1
                                if datasheet_count <= 2:  # Show first 2
                                    print(f"📄 Found datasheet link: {href[:60]}...")
                            
                            # External manufacturer links
                            if 'we-online.com' in href or 'wurth' in href.lower():
                                if any(keyword in href.lower() for keyword in ['datasheet', 'pdf', 'spec']):
                                    external_count += 1
                                    if external_count <= 2:  # Show first 2
                                        print(f"🌐 Found external link: {href[:60]}...")
                        
                        if datasheet_count > 2:
                            print(f"📄 ... and {datasheet_count - 2} more internal links")
                        if external_count > 2:
                            print(f"🌐 ... and {external_count - 2} more external links")
                        
                        break
                else:
                    print("❌ No manufacturer patterns matched")
            else:
                print(f"❌ Part {part_number} not found on page")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Mouser search failed: {e}")
    
    print()
    print("=" * 60)
    print("🏁 Test completed!")

if __name__ == "__main__":
    test_enhanced_search()
