#!/usr/bin/env python3
"""
Enhanced 3D Model Detector - Deep navigation to find 3D models
"""

import time
import os
import shutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def handle_cookie_popups(driver):
    """Universal cookie popup handler"""
    cookie_texts = ["Accept", "Accept All", "OK", "Got it", "I Agree", "Continue", "Allow"]
    
    for text in cookie_texts:
        try:
            elements = driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')] | //a[contains(text(), '{text}')]")
            for element in elements:
                if element.is_displayed():
                    element.click()
                    time.sleep(2)
                    return True
        except:
            continue
    return False

def explore_tabs_for_3d(driver):
    """Explore all tabs/sections looking for 3D models"""
    print("🔍 Exploring tabs and sections for 3D models...")
    
    # Common tab/section selectors
    tab_selectors = [
        ".tab", "[role='tab']", ".nav-item", ".nav-link",
        ".section-header", ".accordion-header", ".toggle-header",
        "li[data-tab]", "button[data-toggle]", ".dropdown-item"
    ]
    
    # 3D-related keywords to look for in tabs
    keywords_3d = [
        '3d', '3D', 'cad', 'CAD', 'step', 'STEP', 'model', 'Model',
        'package', 'Package', 'mechanical', 'Mechanical', 'design', 'Design',
        'footprint', 'Footprint', 'symbol', 'Symbol', 'library', 'Library',
        'download', 'Download', 'files', 'Files', 'resources', 'Resources'
    ]
    
    found_3d_content = []
    
    for selector in tab_selectors:
        try:
            tabs = driver.find_elements(By.CSS_SELECTOR, selector)
            
            for i, tab in enumerate(tabs):
                try:
                    if tab.is_displayed():
                        text = tab.text.strip()
                        
                        # Check if tab text contains 3D-related keywords
                        if text and any(keyword in text for keyword in keywords_3d):
                            print(f"  🎯 Found potential 3D tab: '{text}'")
                            
                            # Try clicking the tab
                            try:
                                original_url = driver.current_url
                                tab.click()
                                time.sleep(5)
                                
                                print(f"    ✅ Clicked tab: '{text}'")
                                
                                # Look for 3D content after clicking
                                content_found = search_for_3d_content(driver)
                                if content_found:
                                    found_3d_content.extend(content_found)
                                    print(f"    🎉 Found 3D content in '{text}' tab!")

                                    # If we found 3D sections, try to click into them
                                    for item in content_found:
                                        if item['type'] == '3d_section':
                                            try:
                                                print(f"      🔍 Exploring 3D section: '{item['text'][:50]}...'")
                                                item['element'].click()
                                                time.sleep(5)

                                                # Look for download links in the expanded section
                                                deeper_content = search_for_3d_content(driver)
                                                if deeper_content:
                                                    found_3d_content.extend(deeper_content)
                                                    print(f"      ✅ Found deeper 3D content!")

                                            except Exception as e:
                                                print(f"      ⚠️ Could not explore section: {e}")
                                                continue
                                
                                # If URL changed, go back
                                if driver.current_url != original_url:
                                    driver.back()
                                    time.sleep(3)
                                
                            except Exception as e:
                                print(f"    ❌ Could not click tab '{text}': {e}")
                                continue
                                
                except Exception as e:
                    continue
                    
        except Exception as e:
            continue
    
    return found_3d_content

def search_for_3d_content(driver):
    """Search current page for 3D model content"""
    found_content = []
    
    # Look for 3D download links
    download_patterns = [
        "//a[contains(text(), '3D') and contains(text(), 'Download')]",
        "//a[contains(text(), 'STEP') and contains(text(), 'Download')]",
        "//a[contains(text(), 'CAD') and contains(text(), 'Download')]",
        "//a[contains(@href, '.step') or contains(@href, '.stp')]",
        "//button[contains(text(), '3D') and contains(text(), 'Download')]",
        "//button[contains(text(), 'STEP')]",
        "//a[contains(text(), 'Download') and contains(text(), 'Model')]"
    ]
    
    for pattern in download_patterns:
        try:
            elements = driver.find_elements(By.XPATH, pattern)
            for element in elements:
                if element.is_displayed():
                    text = element.text.strip()
                    href = element.get_attribute('href') or ''
                    found_content.append({
                        'type': '3d_download',
                        'text': text,
                        'href': href,
                        'element': element
                    })
                    print(f"    ✅ Found 3D download: '{text}' -> {href[:50]}...")
        except:
            continue
    
    # Look for 3D model files or links
    file_patterns = [
        "//a[contains(@href, '.step')]",
        "//a[contains(@href, '.stp')]", 
        "//a[contains(@href, '.3d')]",
        "//a[contains(text(), '.step')]",
        "//a[contains(text(), '.stp')]"
    ]
    
    for pattern in file_patterns:
        try:
            elements = driver.find_elements(By.XPATH, pattern)
            for element in elements:
                if element.is_displayed():
                    text = element.text.strip()
                    href = element.get_attribute('href') or ''
                    found_content.append({
                        'type': '3d_file',
                        'text': text,
                        'href': href,
                        'element': element
                    })
                    print(f"    ✅ Found 3D file: '{text}' -> {href[:50]}...")
        except:
            continue
    
    # Look for 3D model sections or containers
    section_patterns = [
        "//*[contains(@class, '3d') or contains(@class, 'cad')]",
        "//*[contains(@id, '3d') or contains(@id, 'cad')]",
        "//*[contains(text(), '3D Model') or contains(text(), 'CAD Model')]"
    ]
    
    for pattern in section_patterns:
        try:
            elements = driver.find_elements(By.XPATH, pattern)
            for element in elements:
                if element.is_displayed():
                    text = element.text.strip()[:100]
                    if text:
                        found_content.append({
                            'type': '3d_section',
                            'text': text,
                            'element': element
                        })
                        print(f"    ✅ Found 3D section: '{text}...'")
        except:
            continue
    
    return found_content

def attempt_3d_download(content_item, part_number, manufacturer):
    """Attempt to download 3D model"""
    try:
        if content_item['type'] in ['3d_download', '3d_file']:
            element = content_item['element']
            text = content_item['text']
            
            print(f"  🔽 Attempting download: '{text}'")
            
            # Click the download element
            element.click()
            time.sleep(10)  # Wait for download
            
            # Check Downloads folder for new STEP files
            downloads_dir = os.path.expanduser("~/Downloads")
            
            import glob
            step_files = (glob.glob(os.path.join(downloads_dir, "*.step")) + 
                         glob.glob(os.path.join(downloads_dir, "*.STEP")) +
                         glob.glob(os.path.join(downloads_dir, "*.stp")) +
                         glob.glob(os.path.join(downloads_dir, "*.STP")))
            
            # Find most recent file
            if step_files:
                latest_file = max(step_files, key=os.path.getmtime)
                mod_time = os.path.getmtime(latest_file)
                
                # Check if modified in last 2 minutes
                if (time.time() - mod_time) < 120:
                    print(f"  ✅ Downloaded: {os.path.basename(latest_file)}")
                    
                    # Move and rename file
                    os.makedirs("3d", exist_ok=True)
                    clean_part = part_number.replace('/', '_')
                    target_file = f"3d/{manufacturer}_{clean_part}.step"
                    
                    shutil.move(latest_file, target_file)
                    print(f"  ✅ Moved to: {target_file}")
                    
                    return target_file
            
            print(f"  ⚠️ No new STEP file found in Downloads")
            return None
            
    except Exception as e:
        print(f"  ❌ Download failed: {e}")
        return None

def enhanced_3d_detection_test():
    """Test enhanced 3D detection on Analog Devices"""
    print("🎯 ENHANCED 3D MODEL DETECTION TEST")
    print("=" * 60)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Test on Analog Devices AD8065 (we know this has package info)
        part_number = "AD8065"
        manufacturer = "Analog_Devices"
        
        print(f"📦 Testing: {part_number} ({manufacturer})")
        
        # Navigate to part page
        url = f"https://www.analog.com/en/products/{part_number}.html"
        print(f"🌐 Going to: {url}")
        
        driver.get(url)
        time.sleep(5)
        handle_cookie_popups(driver)
        
        print(f"✅ Part page loaded")
        
        # Enhanced 3D detection
        print(f"\n🔍 Starting enhanced 3D model detection...")
        
        # Step 1: Look for 3D content on current page
        current_content = search_for_3d_content(driver)
        
        # Step 2: Explore tabs for 3D content
        tab_content = explore_tabs_for_3d(driver)
        
        # Combine all found content
        all_3d_content = current_content + tab_content
        
        print(f"\n📋 DETECTION RESULTS:")
        print(f"✅ Found {len(all_3d_content)} 3D-related items")
        
        if all_3d_content:
            print(f"\n🎯 3D Content Found:")
            for i, item in enumerate(all_3d_content):
                print(f"  {i+1}. Type: {item['type']}")
                print(f"      Text: '{item['text'][:60]}...'")
                if 'href' in item:
                    print(f"      Link: {item['href'][:60]}...")
            
            # Try to download first available 3D model
            for item in all_3d_content:
                if item['type'] in ['3d_download', '3d_file']:
                    print(f"\n🔽 Attempting download...")
                    downloaded_file = attempt_3d_download(item, part_number, manufacturer)
                    if downloaded_file:
                        print(f"🎉 SUCCESS! 3D model downloaded: {downloaded_file}")
                        break
                    else:
                        print(f"⚠️ Download attempt failed, trying next...")
        else:
            print(f"❌ No 3D content found")
        
        # Keep browser open for manual inspection
        print(f"\n🔸 Browser staying open for 3 minutes for manual inspection...")
        time.sleep(180)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    enhanced_3d_detection_test()
