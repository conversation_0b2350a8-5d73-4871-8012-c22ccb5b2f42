#!/usr/bin/env python3
"""
TI 3D Model Finder - Step by Step Test
Each step must work before proceeding to next
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

def step_by_step_test():
    print("TI 3D MODEL FINDER - STEP BY STEP TEST")
    print("=" * 50)
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=options)
    
    try:
        # STEP 1: Load TI.com
        print("STEP 1: Loading TI.com...")
        driver.get("https://www.ti.com")
        time.sleep(15)
        
        if "ti.com" in driver.current_url.lower():
            print("✅ STEP 1 SUCCESS: TI.com loaded")
            print(f"   Title: {driver.title}")
        else:
            print("❌ STEP 1 FAILED: TI.com not loaded")
            return False
        
        # STEP 2: Navigate to search results
        print("\nSTEP 2: Navigating to search results...")
        search_url = "https://www.ti.com/sitesearch/en-us/docs/universalsearch.tsp?searchTerm=LM358N&nr=1280240"
        driver.get(search_url)
        time.sleep(15)
        
        if "LM358N" in driver.page_source.upper():
            print("✅ STEP 2 SUCCESS: Search results loaded with LM358N")
            print(f"   URL: {driver.current_url}")
        else:
            print("❌ STEP 2 FAILED: No LM358N found in search results")
            return False
        
        # STEP 3: Look for product links
        print("\nSTEP 3: Looking for LM358N product links...")
        links = driver.find_elements(By.TAG_NAME, "a")
        lm358_links = []
        
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if 'LM358' in text.upper() or 'lm358' in href.lower():
                    lm358_links.append((text, href))
            except:
                continue
        
        if lm358_links:
            print(f"✅ STEP 3 SUCCESS: Found {len(lm358_links)} LM358N links")
            for i, (text, href) in enumerate(lm358_links[:3]):
                print(f"   Link {i+1}: '{text}' -> {href[:60]}...")
        else:
            print("❌ STEP 3 FAILED: No LM358N product links found")
            return False
        
        # STEP 4: Click on first product link
        print("\nSTEP 4: Clicking on first product link...")
        try:
            first_link = driver.find_element(By.XPATH, f"//a[contains(text(), 'LM358') or contains(@href, 'lm358')]")
            first_link.click()
            time.sleep(15)
            
            print(f"✅ STEP 4 SUCCESS: Clicked product link")
            print(f"   New URL: {driver.current_url}")
        except Exception as e:
            print(f"❌ STEP 4 FAILED: Could not click product link - {e}")
            return False
        
        # STEP 5: Look for 3D/CAD content on product page
        print("\nSTEP 5: Looking for 3D/CAD content...")
        page_text = driver.page_source.lower()
        
        keywords = ['3d', 'step', 'model', 'cad', 'mechanical', 'package']
        found_keywords = []
        
        for keyword in keywords:
            count = page_text.count(keyword)
            if count > 0:
                found_keywords.append(f"{keyword}({count})")
        
        if found_keywords:
            print(f"✅ STEP 5 SUCCESS: Found 3D-related content")
            print(f"   Keywords: {', '.join(found_keywords)}")
        else:
            print("❌ STEP 5 FAILED: No 3D-related content found")
            return False
        
        # STEP 6: Look for downloadable links
        print("\nSTEP 6: Looking for download links...")
        download_links = []
        
        for link in driver.find_elements(By.TAG_NAME, "a"):
            try:
                text = link.text.strip().lower()
                href = link.get_attribute('href') or ''
                
                if any(word in text for word in ['download', '3d', 'step', 'cad']) or \
                   any(ext in href.lower() for ext in ['.step', '.stp']):
                    download_links.append((text, href))
            except:
                continue
        
        if download_links:
            print(f"✅ STEP 6 SUCCESS: Found {len(download_links)} potential download links")
            for i, (text, href) in enumerate(download_links[:3]):
                print(f"   Download {i+1}: '{text}' -> {href[:60]}...")
        else:
            print("❌ STEP 6 FAILED: No download links found")
            return False
        
        print("\n🎉 ALL STEPS SUCCESSFUL!")
        return True
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    success = step_by_step_test()
    if success:
        print("\n✅ READY TO IMPLEMENT FULL 3D FINDER")
    else:
        print("\n❌ NEED TO FIX FAILING STEPS FIRST")
