#!/usr/bin/env python3
"""
Debug what's wrong with accessing TI detail page
"""

import requests
import time

def debug_ti_access():
    print("🔍 DEBUGGING TI DETAIL PAGE ACCESS")
    print("=" * 50)
    
    # The TI detail URL from the JSON
    detail_url = "https://www.digikey.com/en/products/detail/texas-instruments/LM358N/3708502"
    
    print(f"📄 Trying to access: {detail_url}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        print("   Making request...")
        response = session.get(detail_url, timeout=30)
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Headers:")
        for key, value in response.headers.items():
            print(f"      {key}: {value}")
        
        if response.status_code == 200:
            print(f"   ✅ SUCCESS! Got the page")
            print(f"   Content length: {len(response.text)}")
            
            # Save the page
            with open('ti_detail_page_debug.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"   💾 Saved to ti_detail_page_debug.html")
            
            # Check if it contains the datasheet link pattern
            if 'suppproductinfo' in response.text:
                print(f"   🎯 Found 'suppproductinfo' in the page!")
                
                # Count occurrences
                count = response.text.count('suppproductinfo')
                print(f"   📊 Found {count} occurrences of 'suppproductinfo'")
                
                # Find the specific pattern
                import re
                pattern = r'https://www\.ti\.com/general/docs/suppproductinfo\.tsp[^"\'\s]*'
                matches = re.findall(pattern, response.text)
                
                if matches:
                    print(f"   📄 Found datasheet URLs:")
                    for i, url in enumerate(matches, 1):
                        print(f"      {i}. {url}")
                else:
                    print(f"   ⚠️  No complete URLs found, searching for partial matches...")
                    
                    # Look for any suppproductinfo references
                    lines = response.text.split('\n')
                    for i, line in enumerate(lines):
                        if 'suppproductinfo' in line:
                            print(f"   Line {i}: {line.strip()[:200]}...")
                            
            else:
                print(f"   ❌ 'suppproductinfo' not found in the page")
                
                # Check what we did get
                if 'Texas Instruments' in response.text:
                    print(f"   ✅ 'Texas Instruments' found in page")
                if 'LM358N' in response.text:
                    print(f"   ✅ 'LM358N' found in page")
                if 'datasheet' in response.text.lower():
                    print(f"   ✅ 'datasheet' found in page")
                    
        elif response.status_code == 429:
            print(f"   ❌ Rate limited (429)")
        elif response.status_code == 403:
            print(f"   ❌ Forbidden (403) - might be blocked")
        elif response.status_code == 404:
            print(f"   ❌ Not found (404) - URL might be wrong")
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
            print(f"   Response text (first 500 chars): {response.text[:500]}")
            
    except requests.exceptions.Timeout:
        print(f"   ❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Connection error")
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    debug_ti_access()
