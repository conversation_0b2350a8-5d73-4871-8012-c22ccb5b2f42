#!/usr/bin/env python3
"""
STEP BY STEP ULTRALIBRARIAN TEST
================================
Test each step individually with pauses for verification.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver():
    """Setup Chrome"""
    chrome_options = Options()
    
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def step_test():
    """Test step by step"""
    print("🔍 STEP BY STEP ULTRALIBRARIAN TEST")
    print("=" * 50)
    
    driver = setup_driver()
    
    try:
        # STEP 1: Load page
        print("\n🔸 STEP 1: Loading UltraLibrarian...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        print(f"✅ Page loaded: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        input("✋ STEP 1 COMPLETE - Press Enter to continue...")
        
        # STEP 2: Find search box
        print("\n🔸 STEP 2: Finding search box...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(inputs)} input elements")
        
        search_box = None
        for i, inp in enumerate(inputs):
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    print(f"  Input {i}: placeholder='{placeholder}'")
                    if 'search' in placeholder.lower():
                        search_box = inp
                        print(f"  ✅ Will use input {i} as search box")
                        break
            except:
                continue
        
        if not search_box:
            print("❌ No search box found!")
            return
        
        input("✋ STEP 2 COMPLETE - Press Enter to continue...")
        
        # STEP 3: Search
        print("\n🔸 STEP 3: Searching for LM358N...")
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        
        print("✅ Search submitted")
        time.sleep(10)
        print(f"New URL: {driver.current_url}")
        
        input("✋ STEP 3 COMPLETE - Press Enter to continue...")
        
        # STEP 4: Find TI LM358N
        print("\n🔸 STEP 4: Finding Texas Instruments LM358N...")
        all_links = driver.find_elements(By.TAG_NAME, "a")
        
        lm358_links = []
        for link in all_links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                
                if (('lm358' in text.lower() or 'lm358' in href.lower()) and 
                    link.is_displayed() and link.is_enabled()):
                    lm358_links.append((text, href, link))
                    print(f"  Found: '{text}' -> {href}")
            except:
                continue
        
        # Find TI specific
        ti_link = None
        for text, href, link_element in lm358_links:
            if (('lm358n/nopb' in text.lower() or 
                 ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                'details' in href.lower() and 'login' not in href.lower()):
                ti_link = (text, href, link_element)
                print(f"  ✅ Will click: '{text}'")
                break
        
        if not ti_link:
            print("❌ No Texas Instruments LM358N found!")
            return
        
        input("✋ STEP 4 COMPLETE - Press Enter to continue...")
        
        # STEP 5: Click TI part
        print("\n🔸 STEP 5: Clicking on TI LM358N...")
        text, href, link_element = ti_link
        
        driver.execute_script("arguments[0].scrollIntoView(true);", link_element)
        time.sleep(2)
        link_element.click()
        time.sleep(8)
        
        print(f"✅ Clicked on part")
        print(f"New URL: {driver.current_url}")
        
        input("✋ STEP 5 COMPLETE - Press Enter to continue...")
        
        # STEP 6: First Download Now
        print("\n🔸 STEP 6: Finding first 'Download Now'...")
        download_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')] | //a[contains(text(), 'Download Now')]")
        
        print(f"Found {len(download_buttons)} 'Download Now' elements")
        if not download_buttons:
            print("❌ No 'Download Now' button found!")
            return
        
        download_button = download_buttons[0]
        print(f"✅ Will click: '{download_button.text}'")
        
        input("✋ STEP 6 READY - Press Enter to click...")
        
        driver.execute_script("arguments[0].scrollIntoView(true);", download_button)
        time.sleep(2)
        download_button.click()
        time.sleep(5)
        
        print("✅ Clicked first 'Download Now'")
        
        input("✋ STEP 6 COMPLETE - Press Enter to continue...")
        
        # STEP 7: 3D CAD Model
        print("\n🔸 STEP 7: Finding '3D CAD Model'...")
        model_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //a[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')]")
        
        print(f"Found {len(model_buttons)} '3D CAD Model' elements")
        if not model_buttons:
            print("❌ No '3D CAD Model' button found!")
            # Show available buttons
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons:")
            for btn in all_buttons[:10]:
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"  - '{text}'")
                except:
                    continue
            return
        
        model_button = model_buttons[0]
        print(f"✅ Will click: '{model_button.text}'")
        
        input("✋ STEP 7 READY - Press Enter to click...")
        
        driver.execute_script("arguments[0].scrollIntoView(true);", model_button)
        time.sleep(2)
        model_button.click()
        time.sleep(5)
        
        print("✅ Clicked '3D CAD Model'")
        print(f"New URL: {driver.current_url}")
        
        input("✋ STEP 7 COMPLETE - What do you see now? Press Enter...")
        
        print("\n🔍 CURRENT STATE:")
        print(f"URL: {driver.current_url}")
        print(f"Windows: {len(driver.window_handles)}")
        
        # Show what's on the page now
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\nButtons on page ({len(buttons)} total):")
        for i, btn in enumerate(buttons[:15]):
            try:
                text = btn.text.strip()
                visible = btn.is_displayed()
                if text:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"\nLinks on page ({len(links)} total, showing first 15):")
        for i, link in enumerate(links[:15]):
            try:
                text = link.text.strip()
                visible = link.is_displayed()
                if text and len(text) < 50:
                    print(f"  {i}: '{text}' (visible={visible})")
            except:
                continue
        
        print("\n🔍 STEP BY STEP TEST PAUSED")
        print("Please tell me what you see and what the next step should be.")
        
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    step_test()
