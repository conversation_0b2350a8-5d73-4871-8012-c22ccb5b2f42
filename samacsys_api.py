#!/usr/bin/env python3
"""
SAMACSYS API AUTOMATION
=======================
Generic SamacSys API automation for any electronic component.

Usage: python samacsys_api.py "PART_NUMBER"
Example: python samacsys_api.py "LM358N"
"""

import os
import sys
import time
import json
import requests
from urllib.parse import quote

def samacsys_api_automation(part_number):
    """
    Generic SamacSys API automation for any part number
    
    Args:
        part_number (str): Electronic component part number
        
    Returns:
        str: Downloaded STEP filename or None if failed
    """
    print(f"🎯 SAMACSYS API AUTOMATION: {part_number}")
    print("=" * 60)
    
    # Create 3D directory
    os.makedirs('3D', exist_ok=True)
    initial_files = set(os.listdir('3D'))
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    })
    
    try:
        # STEP 1: Search for component using SamacSys API
        print(f"\n🔸 STEP 1: Searching SamacSys API for {part_number}...")
        
        # Use Component Search Engine (SamacSys) search endpoint
        search_url = f'https://componentsearchengine.com/search?term={quote(part_number)}'
        
        response = session.get(search_url, timeout=30)
        print(f"Search Status: {response.status_code}")

        if response.status_code != 200:
            print(f"❌ Search failed with status {response.status_code}")
            return None

        # Parse HTML response to find component links
        html_content = response.text
        print(f"✅ Search response received ({len(html_content)} chars)")

        # Look for component links in the HTML
        import re

        # Find links that might contain component information
        link_pattern = r'<a[^>]+href="([^"]*)"[^>]*>([^<]*' + re.escape(part_number) + r'[^<]*)</a>'
        matches = re.findall(link_pattern, html_content, re.IGNORECASE)

        if not matches:
            print(f"❌ No component links found for {part_number}")
            return None

        print(f"✅ Found {len(matches)} potential component links")
        
        # STEP 2: Find matching component in HTML
        print(f"\n🔸 STEP 2: Looking for {part_number} in results...")

        # Find the actual component page (part-view URL)
        best_match = None

        # First priority: part-view URLs
        for url, text in matches:
            if part_number.upper() in text.upper() and 'part-view' in url:
                best_match = (url, text)
                print(f"✅ Found part-view component: {text}")
                break

        # Second priority: any URL with the part number that's not model-request or prices
        if not best_match:
            for url, text in matches:
                if (part_number.upper() in text.upper() and
                    'model-request' not in url and
                    'prices' not in url and
                    'search' not in url):
                    best_match = (url, text)
                    print(f"✅ Found component page: {text}")
                    break

        # Third priority: construct part-view URL manually
        if not best_match:
            print("🔸 No direct component page found, trying to construct part-view URL...")
            # Try to construct the part-view URL
            component_url = f'https://componentsearchengine.com/part-view/{part_number}'
            print(f"✅ Constructed part-view URL: {component_url}")
        else:
            component_url = best_match[0]
            if not component_url.startswith('http'):
                component_url = 'https://componentsearchengine.com' + component_url
        
        # STEP 3: Get component details page
        print(f"\n🔸 STEP 3: Getting component details...")
        print(f"Component URL: {component_url}")

        detail_response = session.get(component_url, timeout=30)

        if detail_response.status_code != 200:
            print(f"❌ Component details failed with status {detail_response.status_code}")
            return None

        detail_html = detail_response.text
        print(f"✅ Component details received ({len(detail_html)} chars)")
        
        # STEP 4: Handle SamacSys registration/login system
        print(f"\n🔸 STEP 4: Handling SamacSys download system...")

        # Check if download button redirects to registration
        download_button_pattern = r'href="([^"]*)"[^>]*class="[^"]*ecad-model-button[^"]*"'
        download_matches = re.findall(download_button_pattern, detail_html, re.IGNORECASE)

        if download_matches:
            download_url = download_matches[0]
            if not download_url.startswith('http'):
                download_url = 'https://componentsearchengine.com' + download_url

            print(f"✅ Found download URL: {download_url}")

            # Check if it's a registration URL
            if 'register' in download_url:
                print("🔐 SamacSys requires registration/login for downloads")

                # Try to login with credentials
                try:
                    with open('component_site_credentials.json', 'r') as f:
                        credentials = json.load(f)

                    if 'SamacSys' not in credentials:
                        print("❌ No SamacSys credentials found!")
                        print("📝 Please add SamacSys credentials to component_site_credentials.json")
                        return None

                    email = credentials['SamacSys']['email']
                    password = credentials['SamacSys']['password']

                    # Go to login page
                    login_url = 'https://componentsearchengine.com/signin'
                    login_response = session.get(login_url, timeout=30)

                    if login_response.status_code != 200:
                        print(f"❌ Could not access login page: {login_response.status_code}")
                        return None

                    print(f"✅ Login page loaded ({len(login_response.text)} chars)")

                    # Analyze the login form structure
                    print("🔍 Analyzing login form structure...")

                    # Find all input fields in the form
                    input_pattern = r'<input[^>]*name="([^"]*)"[^>]*>'
                    input_fields = re.findall(input_pattern, login_response.text, re.IGNORECASE)
                    print(f"Found input fields: {input_fields}")

                    # Find form action
                    form_action_pattern = r'<form[^>]*action="([^"]*)"[^>]*>'
                    form_actions = re.findall(form_action_pattern, login_response.text, re.IGNORECASE)
                    if form_actions:
                        actual_login_url = form_actions[0]
                        if not actual_login_url.startswith('http'):
                            actual_login_url = 'https://componentsearchengine.com' + actual_login_url
                        print(f"Found form action: {actual_login_url}")
                        login_url = actual_login_url

                    # Extract CSRF token - use the correct field name
                    csrf_token = ''
                    csrf_patterns = [
                        r'name="_csrf"[^>]*value="([^"]*)"',
                        r'name="_token"[^>]*value="([^"]*)"',
                        r'<meta[^>]*name="csrf-token"[^>]*content="([^"]*)"',
                        r'"_csrf":"([^"]*)"',
                        r'"_token":"([^"]*)"'
                    ]

                    for pattern in csrf_patterns:
                        csrf_matches = re.findall(pattern, login_response.text, re.IGNORECASE)
                        if csrf_matches:
                            csrf_token = csrf_matches[0]
                            print(f"✅ Found CSRF token: {csrf_token[:20]}...")
                            break

                    # Extract returnUrl if present
                    return_url = ''
                    return_url_pattern = r'name="returnUrl"[^>]*value="([^"]*)"'
                    return_url_matches = re.findall(return_url_pattern, login_response.text)
                    if return_url_matches:
                        return_url = return_url_matches[0]
                        print(f"✅ Found returnUrl: {return_url}")

                    if not csrf_token:
                        print("⚠️ No CSRF token found, proceeding without it")

                    # Use the correct form fields based on analysis
                    login_data_options = [
                        {
                            'email': email,
                            'password': password,
                            '_csrf': csrf_token,
                            'returnUrl': return_url,
                            'keepMe': 'false'
                        },
                        {
                            'email': email,
                            'password': password,
                            '_csrf': csrf_token,
                            'keepMe': 'false'
                        },
                        {
                            'email': email,
                            'password': password,
                            '_csrf': csrf_token
                        },
                        {
                            'email': email,
                            'password': password
                        }
                    ]

                    # Try each login data format
                    login_success = False
                    for i, login_data in enumerate(login_data_options):
                        print(f"🔸 Trying login format {i+1}: {list(login_data.keys())}")

                        login_submit = session.post(login_url, data=login_data, timeout=30)

                        print(f"Login response status: {login_submit.status_code}")
                        print(f"Login response URL: {login_submit.url}")

                        # Check for successful login indicators
                        success_indicators = [
                            'dashboard' in login_submit.url,
                            'profile' in login_submit.url,
                            'signin' not in login_submit.url and login_submit.status_code == 200,
                            'logout' in login_submit.text.lower(),
                            'welcome' in login_submit.text.lower()
                        ]

                        if any(success_indicators):
                            print("✅ Successfully logged in to SamacSys")
                            login_success = True
                            break
                        else:
                            print(f"❌ Login attempt {i+1} failed")
                            if 'error' in login_submit.text.lower():
                                error_pattern = r'error[^>]*>([^<]*)</[^>]*>'
                                error_matches = re.findall(error_pattern, login_submit.text, re.IGNORECASE)
                                if error_matches:
                                    print(f"Error message: {error_matches[0]}")

                    if login_success:

                        # Now try to access the component download page again
                        print("🔸 Accessing component page after login...")
                        component_response = session.get(component_url, timeout=30)
                        if component_response.status_code == 200:
                            component_html = component_response.text
                            print(f"✅ Component page reloaded ({len(component_html)} chars)")

                            # Look for download buttons/links now that we're logged in
                            # Focus on actual STEP file URLs, not tracking URLs
                            download_patterns = [
                                r'href="([^"]*\.step[^"]*)"',
                                r'href="([^"]*\.stp[^"]*)"',
                                r'href="([^"]*download[^"]*\.step[^"]*)"',
                                r'href="([^"]*download[^"]*\.stp[^"]*)"'
                            ]

                            found_download = False
                            for pattern in download_patterns:
                                matches = re.findall(pattern, component_html, re.IGNORECASE)
                                for match in matches:
                                    if not match.startswith('http'):
                                        download_url = 'https://componentsearchengine.com' + match
                                    else:
                                        download_url = match

                                    # Skip tracking/analytics URLs
                                    if ('analytics' not in download_url and
                                        'tracking' not in download_url and
                                        'register' not in download_url and
                                        'signin' not in download_url):
                                        print(f"✅ Found direct STEP download URL: {download_url}")
                                        found_download = True
                                        break

                                if found_download:
                                    break

                            # If no direct STEP URLs found, look for download buttons that might lead to STEP files
                            if not found_download:
                                print("🔸 No direct STEP URLs found, looking for download buttons...")
                                button_patterns = [
                                    r'class="[^"]*ecad-model-button[^"]*"[^>]*href="([^"]*)"',
                                    r'href="([^"]*)"[^>]*class="[^"]*ecad-model-button[^"]*"',
                                    r'href="([^"]*download[^"]*)"[^>]*>.*?(?:3D|STEP|Model)',
                                    r'<a[^>]*href="([^"]*)"[^>]*>.*?(?:Download|3D|STEP|Model).*?</a>'
                                ]

                                for pattern in button_patterns:
                                    matches = re.findall(pattern, component_html, re.IGNORECASE | re.DOTALL)
                                    for match in matches:
                                        if not match.startswith('http'):
                                            download_url = 'https://componentsearchengine.com' + match
                                        else:
                                            download_url = match

                                        # Skip tracking/analytics URLs
                                        if ('analytics' not in download_url and
                                            'tracking' not in download_url and
                                            'register' not in download_url and
                                            'signin' not in download_url):
                                            print(f"✅ Found download button URL: {download_url}")
                                            found_download = True
                                            break

                                    if found_download:
                                        break

                            if not found_download:
                                print("❌ No download links found even after login")
                                print("🔍 Searching for any download-related elements...")

                                # Look for any elements containing "download" or "3d"
                                download_text_pattern = r'<[^>]*>([^<]*(?:download|3d|step|model)[^<]*)</[^>]*>'
                                download_texts = re.findall(download_text_pattern, component_html, re.IGNORECASE)
                                if download_texts:
                                    print("Found download-related text elements:")
                                    for text in download_texts[:10]:
                                        if text.strip():
                                            print(f"  - {text.strip()}")

                                return None
                        else:
                            print("❌ Could not access component page after login")
                            return None
                    else:
                        print("❌ Login failed")
                        return None

                except Exception as e:
                    print(f"❌ Login error: {e}")
                    return None
            else:
                # Direct download URL
                print("✅ Direct download URL found")
        else:
            print("❌ No download button found")
            return None
        
        # STEP 5: Download the 3D model
        print(f"\n🔸 STEP 5: Downloading 3D model...")
        
        try:
            download_response = session.get(download_url, timeout=60)
            
            if download_response.status_code != 200:
                print(f"❌ Download failed with status {download_response.status_code}")
                return None
            
            # Determine file extension
            content_type = download_response.headers.get('content-type', '').lower()
            if 'zip' in content_type:
                file_ext = '.zip'
            elif 'step' in content_type or download_url.lower().endswith('.step'):
                file_ext = '.step'
            elif download_url.lower().endswith('.stp'):
                file_ext = '.stp'
            else:
                file_ext = '.step'  # Default
            
            # Create filename
            clean_part = part_number.replace('/', '-').replace('\\', '-')
            filename = f'samacsys-{clean_part.lower()}{file_ext}'
            filepath = os.path.join('3D', filename)
            
            # Save file
            with open(filepath, 'wb') as f:
                f.write(download_response.content)
            
            print(f"✅ Downloaded: {filename} ({len(download_response.content)} bytes)")
            
            # If it's a ZIP file, try to extract STEP files
            if file_ext == '.zip':
                try:
                    import zipfile
                    with zipfile.ZipFile(filepath, 'r') as zip_ref:
                        zip_ref.extractall('3D')
                    print(f"✅ Extracted ZIP file")
                    
                    # Look for STEP files in extracted content
                    current_files = set(os.listdir('3D'))
                    new_files = current_files - initial_files - {filename}
                    step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                    
                    if step_files:
                        extracted_step = step_files[0]
                        step_filename = f'samacsys-{clean_part.lower()}.step'
                        
                        # Rename extracted STEP file
                        os.rename(os.path.join('3D', extracted_step), os.path.join('3D', step_filename))
                        print(f"✅ Renamed extracted file: {extracted_step} -> {step_filename}")
                        return step_filename
                    
                except Exception as e:
                    print(f"⚠️ ZIP extraction failed: {e}")
            
            return filename
            
        except Exception as e:
            print(f"❌ Download error: {e}")
            return None
        
    except Exception as e:
        print(f"❌ API Error: {e}")
        return None

def main():
    """Main function to handle command line arguments"""
    if len(sys.argv) != 2:
        print("Usage: python samacsys_api.py \"PART_NUMBER\"")
        print("Example: python samacsys_api.py \"LM358N\"")
        sys.exit(1)
    
    part_number = sys.argv[1]
    result = samacsys_api_automation(part_number)
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded to 3D/ folder!")
        sys.exit(0)
    else:
        print(f"\n❌ FAILED: Could not download STEP file for {part_number}")
        sys.exit(1)

if __name__ == "__main__":
    main()
