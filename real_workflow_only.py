#!/usr/bin/env python3
"""
REAL WORKFLOW ONLY - Follow the exact URLs from DigiKey HTML
NO CHEATING - NO DIRECT URLs
"""

import re
import requests
import os

def real_workflow_only():
    print("🎯 REAL WORKFLOW ONLY - NO CHEATING")
    print("=" * 50)
    
    # Step 1: Extract TI URL from DigiKey HTML (already proven to work)
    print("📄 Step 1: Reading DigiKey HTML...")
    with open('digikey_search_LM358N.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Find the EXACT TI datasheet URL from DigiKey
    pattern = r'https://www\.ti\.com/general/docs/suppproductinfo\.tsp[^"\']*lm158[^"\']*'
    matches = re.findall(pattern, html_content, re.IGNORECASE)
    
    if not matches:
        print("❌ No TI URLs found in DigiKey HTML")
        return False
    
    ti_url = matches[0]
    print(f"✅ Found TI URL from DigiKey: {ti_url}")
    
    # Step 2: Follow the TI URL (the real workflow)
    print(f"\n📥 Step 2: Following TI URL from DigiKey...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        print(f"   Accessing: {ti_url}")
        response = session.get(ti_url, timeout=30, allow_redirects=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"   ❌ Failed to access TI URL: {response.status_code}")
            return False
        
        # Save the response
        with open('ti_real_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"   💾 Saved response to ti_real_response.html")
        
        content_type = response.headers.get('content-type', '').lower()
        print(f"   Content-Type: {content_type}")
        
        # Check if it's a direct PDF
        if 'application/pdf' in content_type:
            print("   ✅ Direct PDF download!")
            
            os.makedirs('datasheets', exist_ok=True)
            filename = 'datasheets/TI-LM358N-REAL-WORKFLOW.pdf'
            
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            file_size = os.path.getsize(filename)
            print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
            
            if file_size > 1000:
                print(f"   🎉 SUCCESS: Real workflow datasheet downloaded!")
                return True
            else:
                print(f"   ⚠️  File too small")
                return False
        
        # If it's HTML, look for PDF links or redirects
        elif 'text/html' in content_type:
            print("   📄 Got HTML response, analyzing...")
            
            # Look for PDF links
            pdf_links = re.findall(r'href="([^"]*\.pdf[^"]*)"', response.text, re.IGNORECASE)
            
            if pdf_links:
                print(f"   🔗 Found PDF links:")
                for i, link in enumerate(pdf_links[:3], 1):
                    print(f"      {i}. {link}")
                
                # Follow the first PDF link
                pdf_url = pdf_links[0]
                if not pdf_url.startswith('http'):
                    if pdf_url.startswith('/'):
                        pdf_url = f"https://www.ti.com{pdf_url}"
                    else:
                        pdf_url = f"https://www.ti.com/{pdf_url}"
                
                print(f"   📥 Following PDF link: {pdf_url}")
                
                pdf_response = session.get(pdf_url, timeout=30)
                print(f"   PDF Status: {pdf_response.status_code}")
                
                if pdf_response.status_code == 200:
                    pdf_content_type = pdf_response.headers.get('content-type', '').lower()
                    
                    if 'application/pdf' in pdf_content_type:
                        os.makedirs('datasheets', exist_ok=True)
                        filename = 'datasheets/TI-LM358N-REAL-WORKFLOW.pdf'
                        
                        with open(filename, 'wb') as f:
                            f.write(pdf_response.content)
                        
                        file_size = os.path.getsize(filename)
                        print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                        
                        if file_size > 1000:
                            print(f"   🎉 SUCCESS: Real workflow datasheet downloaded!")
                            return True
                        else:
                            print(f"   ⚠️  PDF file too small")
                            return False
                    else:
                        print(f"   ❌ PDF link didn't return PDF: {pdf_content_type}")
                        return False
                else:
                    print(f"   ❌ PDF link failed: {pdf_response.status_code}")
                    return False
            
            # Look for JavaScript redirects
            elif 'location' in response.text.lower() or 'redirect' in response.text.lower():
                print("   🔄 Looking for redirects...")
                
                # Look for redirect URLs
                redirect_matches = re.findall(r'(?:location\.href|window\.location)\s*=\s*["\']([^"\']+)["\']', response.text, re.IGNORECASE)
                
                if redirect_matches:
                    redirect_url = redirect_matches[0]
                    print(f"   🔄 Found redirect: {redirect_url}")
                    
                    if not redirect_url.startswith('http'):
                        redirect_url = f"https://www.ti.com{redirect_url}"
                    
                    print(f"   📥 Following redirect: {redirect_url}")
                    
                    redirect_response = session.get(redirect_url, timeout=30)
                    print(f"   Redirect Status: {redirect_response.status_code}")
                    
                    if redirect_response.status_code == 200:
                        # Save redirect response
                        with open('ti_redirect_response.html', 'w', encoding='utf-8') as f:
                            f.write(redirect_response.text)
                        print(f"   💾 Saved redirect response to ti_redirect_response.html")
                        
                        # Look for PDF in redirect response
                        redirect_pdf_links = re.findall(r'href="([^"]*\.pdf[^"]*)"', redirect_response.text, re.IGNORECASE)
                        
                        if redirect_pdf_links:
                            pdf_url = redirect_pdf_links[0]
                            if not pdf_url.startswith('http'):
                                pdf_url = f"https://www.ti.com{pdf_url}"
                            
                            print(f"   📥 Downloading PDF from redirect: {pdf_url}")
                            
                            final_pdf_response = session.get(pdf_url, timeout=30)
                            
                            if final_pdf_response.status_code == 200 and 'application/pdf' in final_pdf_response.headers.get('content-type', ''):
                                os.makedirs('datasheets', exist_ok=True)
                                filename = 'datasheets/TI-LM358N-REAL-WORKFLOW.pdf'
                                
                                with open(filename, 'wb') as f:
                                    f.write(final_pdf_response.content)
                                
                                file_size = os.path.getsize(filename)
                                print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                                
                                if file_size > 1000:
                                    print(f"   🎉 SUCCESS: Real workflow datasheet downloaded!")
                                    return True
                        
                        print(f"   ❌ No PDF found in redirect response")
                        return False
                    else:
                        print(f"   ❌ Redirect failed: {redirect_response.status_code}")
                        return False
                else:
                    print(f"   ❌ No redirect URLs found")
                    return False
            else:
                print(f"   ❌ No PDF links or redirects found")
                return False
        else:
            print(f"   ❌ Unknown content type: {content_type}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = real_workflow_only()
    if success:
        print("\n🎉 REAL WORKFLOW SUCCESS - DATASHEET DOWNLOADED!")
    else:
        print("\n❌ REAL WORKFLOW FAILED")
