#!/usr/bin/env python3
import sys
import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

print("🧪 SIMPLE SCREEN TEST")
print("Starting Chrome...")
sys.stdout.flush()

try:
    options = Options()
    options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=options)
    print("✅ Chrome started")
    sys.stdout.flush()
    
    print("🌐 Loading UltraLibrarian...")
    sys.stdout.flush()
    
    driver.get('https://www.ultralibrarian.com/')
    time.sleep(5)
    
    print(f"✅ Page loaded: {driver.title}")
    print(f"📄 URL: {driver.current_url}")
    sys.stdout.flush()
    
    # Take screenshot
    driver.save_screenshot("simple_test_homepage.png")
    print("📸 Screenshot saved: simple_test_homepage.png")
    sys.stdout.flush()
    
    print("\n⏸️  MANUAL CHECK:")
    print("1. Look at the browser window")
    print("2. Can you see the UltraLibrarian homepage?")
    print("3. Can you see a LOGIN link in the top navigation?")
    
    input("\nPress Enter to continue...")
    
    # Look for LOGIN link
    from selenium.webdriver.common.by import By
    login_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'LOGIN')]")
    
    if login_links:
        print(f"✅ Found LOGIN link: {login_links[0].text}")
        print(f"🔗 Href: {login_links[0].get_attribute('href')}")
    else:
        print("❌ No LOGIN link found")
    
    print("\n⏸️  Ready to click LOGIN?")
    input("Press Enter to click LOGIN...")
    
    if login_links:
        login_links[0].click()
        time.sleep(5)
        
        driver.save_screenshot("simple_test_login_page.png")
        print("📸 Screenshot saved: simple_test_login_page.png")
        print(f"📄 Login page URL: {driver.current_url}")
        
        print("\n⏸️  MANUAL CHECK:")
        print("1. Are you now on the login page?")
        print("2. Can you see email and password fields?")
        
        input("\nPress Enter to close...")
    
    driver.quit()
    print("✅ Test complete")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
