#!/usr/bin/env python3
"""
SMART STEP FILE DOWNLOADER
==========================
Downloads the correct STEP file based on package type from datasheet.
Integrates with component finder workflow.

Usage:
    python smart_step_downloader.py "Diodes Inc" "APX803L20-30SA-7" "SOT23"
    python smart_step_downloader.py "TI" "LM358N" "PDIP-8"
"""

import requests
import json
import os
import time
import re
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import argparse

class SmartStepDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # Package type mappings for better matching
        self.package_mappings = {
            'sot23': ['sot23', 'sot-23', 'sot_23', 'to-236'],
            'sot25': ['sot25', 'sot-25', 'sot_25'],
            'sot323': ['sot323', 'sot-323', 'sot_323', 'sc-70'],
            'sc59': ['sc59', 'sc-59', 'sc_59'],
            'pdip': ['pdip', 'dip', 'pdip-8', 'dip-8', 'dil'],
            'soic': ['soic', 'so', 'soic-8', 'so-8'],
            'msop': ['msop', 'msop-8', 'micro-soic'],
            'qfn': ['qfn', 'mlf', 'qfn-16', 'mlf-16'],
            'bga': ['bga', 'fbga', 'pbga'],
            'lqfp': ['lqfp', 'tqfp', 'lqfp-32', 'tqfp-32']
        }
        
        # Load manufacturer patterns
        self.manufacturer_patterns = self.load_manufacturer_patterns()
        
        # Create downloads directory
        os.makedirs('step_downloads', exist_ok=True)
        
        print("🎯 Smart STEP File Downloader Ready!")
        print("=" * 50)

    def load_manufacturer_patterns(self):
        """Load manufacturer-specific patterns"""
        return {
            'diodes incorporated': {
                'aliases': ['diodes inc', 'diodes', 'diodes incorporated'],
                'base_url': 'https://www.diodes.com',
                'search_pattern': '/part/{part_number}',
                'package_selectors': {
                    'sot23': ['sot23', 'sot-23'],
                    'sot25': ['sot25', 'sot-25'], 
                    'sot323': ['sot323', 'sot-323'],
                    'sc59': ['sc59', 'sc-59']
                }
            },
            'texas instruments': {
                'aliases': ['ti', 'texas instruments', 'texas_instruments'],
                'base_url': 'https://www.ti.com',
                'search_pattern': '/product/{part_number}',
                'package_selectors': {
                    'pdip': ['pdip', 'dip'],
                    'soic': ['soic', 'so'],
                    'msop': ['msop'],
                    'qfn': ['qfn', 'mlf']
                }
            }
        }

    def normalize_manufacturer(self, manufacturer):
        """Normalize manufacturer name"""
        manufacturer_lower = manufacturer.lower().strip()
        for key, config in self.manufacturer_patterns.items():
            if manufacturer_lower in config['aliases']:
                return key
        return manufacturer_lower

    def normalize_package(self, package_type):
        """Normalize package type for matching"""
        if not package_type:
            return None
            
        package_lower = package_type.lower().strip()
        
        # Find matching package family
        for family, variants in self.package_mappings.items():
            if any(variant in package_lower for variant in variants):
                return family
        
        return package_lower

    def setup_selenium_driver(self):
        """Setup Selenium WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences
        prefs = {
            "download.default_directory": os.path.abspath('step_downloads'),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"❌ Failed to setup Chrome driver: {e}")
            return None

    def find_matching_step_files(self, driver, manufacturer, part_number, target_package):
        """Find STEP files that match the target package type"""
        print(f"   🎯 Looking for {target_package} package specifically...")
        
        matching_elements = []
        all_step_elements = []
        
        # Find all potential STEP download elements
        selectors = [
            "a[href*='.step']",
            "a[href*='.stp']", 
            "a[href*='3d-model']",
            "a[href*='cad']",
            "button[onclick*='step']",
            ".download-step",
            ".cad-download"
        ]
        
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.strip().lower()
                    href = element.get_attribute('href') or ''
                    
                    # Check if this element relates to STEP files
                    if (any(keyword in text for keyword in ['step', '3d', 'cad', 'download']) or
                        any(ext in href.lower() for ext in ['.step', '.stp'])):
                        
                        all_step_elements.append((element, text, href))
                        
                        # Check if it matches our target package
                        if target_package:
                            normalized_target = self.normalize_package(target_package)
                            if (normalized_target in text or 
                                normalized_target in href.lower() or
                                any(variant in text for variant in self.package_mappings.get(normalized_target, []))):
                                matching_elements.append((element, text, href))
                                print(f"   ✅ Found matching {target_package}: {text[:50]}...")
            except:
                continue
        
        # If we found package-specific matches, use those. Otherwise, use all STEP files
        if matching_elements:
            print(f"   🎯 Found {len(matching_elements)} files matching {target_package}")
            return matching_elements
        elif all_step_elements:
            print(f"   ⚠️ No package-specific match found, showing all {len(all_step_elements)} STEP files")
            return all_step_elements
        else:
            print(f"   ❌ No STEP files found")
            return []

    def download_matching_step_file(self, manufacturer, part_number, package_type):
        """Download STEP file matching the specific package type"""
        print(f"\n🎯 SMART STEP FILE SEARCH")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        print(f"Target Package: {package_type}")
        print("-" * 40)
        
        manufacturer_key = self.normalize_manufacturer(manufacturer)
        
        if manufacturer_key not in self.manufacturer_patterns:
            print(f"⚠️ No specific pattern for {manufacturer}")
            return None
        
        config = self.manufacturer_patterns[manufacturer_key]
        search_url = config['base_url'] + config['search_pattern'].format(part_number=part_number)
        
        driver = self.setup_selenium_driver()
        if not driver:
            return None
        
        try:
            print(f"🌐 Loading: {search_url}")
            driver.get(search_url)
            
            # Wait for page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Find matching STEP files
            matching_elements = self.find_matching_step_files(driver, manufacturer, part_number, package_type)
            
            if not matching_elements:
                print(f"❌ No STEP files found for {part_number}")
                return None
            
            # Download the best match
            downloaded_files = []
            
            for i, (element, text, href) in enumerate(matching_elements[:1]):  # Only download the first/best match
                try:
                    print(f"   📥 Downloading: {text[:50]}...")
                    
                    # Get initial file count
                    initial_files = set(os.listdir('step_downloads'))
                    
                    # Click the download element
                    driver.execute_script("arguments[0].click();", element)
                    
                    # Wait for download
                    time.sleep(5)
                    
                    # Check for new files
                    current_files = set(os.listdir('step_downloads'))
                    new_files = current_files - initial_files
                    
                    if new_files:
                        for new_file in new_files:
                            if any(ext in new_file.lower() for ext in ['.step', '.stp']):
                                # Rename to include package type
                                old_path = os.path.join('step_downloads', new_file)
                                new_name = f"{manufacturer.replace(' ', '_')}_{part_number}_{package_type}.step"
                                new_path = os.path.join('step_downloads', new_name)
                                
                                try:
                                    os.rename(old_path, new_path)
                                    downloaded_files.append(new_name)
                                    print(f"   ✅ Downloaded: {new_name}")
                                except:
                                    downloaded_files.append(new_file)
                                    print(f"   ✅ Downloaded: {new_file}")
                                
                                return downloaded_files  # Return after first successful download
                    
                except Exception as e:
                    print(f"   ❌ Download failed: {e}")
                    continue
            
            return downloaded_files if downloaded_files else None
            
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return None
        finally:
            driver.quit()

def main():
    parser = argparse.ArgumentParser(description='Download specific STEP file based on package type')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Diodes Inc")')
    parser.add_argument('part_number', help='Part number (e.g., "APX803L20-30SA-7")')
    parser.add_argument('package_type', help='Package type (e.g., "SOT23")')
    
    args = parser.parse_args()
    
    downloader = SmartStepDownloader()
    result = downloader.download_matching_step_file(args.manufacturer, args.part_number, args.package_type)
    
    if result:
        print(f"\n✅ SUCCESS! Downloaded correct STEP file:")
        for filename in result:
            print(f"   📁 {filename}")
        print(f"📂 Files saved to: step_downloads/")
    else:
        print(f"\n❌ No matching STEP file found")
        print(f"💡 Check if the package type '{args.package_type}' is correct")

if __name__ == "__main__":
    main()
