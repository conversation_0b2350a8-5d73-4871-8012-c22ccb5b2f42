#!/usr/bin/env python3
"""
COMPLETE STEP SELECTION WITH LOGIN
==================================
Complete the workflow: 3D CAD Model -> STEP selection -> Login -> Download
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def complete_step_selection_login():
    print("🎯 COMPLETE STEP SELECTION WITH LOGIN")
    print("=" * 50)
    
    # Setup Chrome with downloads
    chrome_options = Options()
    download_dir = os.path.abspath('3D')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    initial_files = set(os.listdir('3D')) if os.path.exists('3D') else set()
    
    try:
        # Navigate to Screen 4 quickly
        print("🔸 Navigating to Screen 4...")
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        # Search
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        # Click TI part
        links = driver.find_elements(By.TAG_NAME, "a")
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    link.click()
                    break
            except:
                continue
        time.sleep(8)
        
        # Click Download Now
        download_btns = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download Now')]")
        if download_btns:
            driver.execute_script("arguments[0].click();", download_btns[0])
            time.sleep(5)
        
        print("✅ At Screen 4")
        
        # STEP 1: Click 3D CAD Model
        print("\n🔸 STEP 1: Clicking '3D CAD Model'...")
        time.sleep(5)  # Wait for page to load
        
        cad_selectors = [
            "//button[contains(text(), '3D CAD Model')]",
            "//a[contains(text(), '3D CAD Model')]",
            "//button[contains(text(), '3D Model')]",
            "//a[contains(text(), '3D Model')]"
        ]
        
        cad_element = None
        for selector in cad_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    cad_element = elem
                    print(f"✅ Found 3D CAD Model: '{elem.text}'")
                    break
            if cad_element:
                break
        
        if not cad_element:
            print("❌ No 3D CAD Model button found!")
            return
        
        driver.execute_script("arguments[0].click();", cad_element)
        time.sleep(5)
        print("✅ Clicked 3D CAD Model")
        
        # STEP 2: Handle new screen for STEP selection
        print("\n🔸 STEP 2: Handling STEP selection screen...")
        
        # Check for new window
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(3)
        
        print(f"Current URL: {driver.current_url}")
        
        # STEP 3: Select STEP format
        print("\n🔸 STEP 3: Selecting STEP format...")
        
        step_selectors = [
            "//button[contains(text(), 'STEP')]",
            "//a[contains(text(), 'STEP')]",
            "//input[@value='STEP']",
            "//option[contains(text(), 'STEP')]",
            "//div[contains(text(), 'STEP') and (@onclick or @click)]",
            "//label[contains(text(), 'STEP')]",
            "//span[contains(text(), 'STEP')]"
        ]
        
        step_element = None
        for selector in step_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                try:
                    if elem.is_displayed():
                        step_element = elem
                        print(f"✅ Found STEP option: '{elem.text or elem.get_attribute('value')}'")
                        break
                except:
                    continue
            if step_element:
                break
        
        if step_element:
            driver.execute_script("arguments[0].click();", step_element)
            time.sleep(3)
            print("✅ Selected STEP format")
        else:
            print("⚠️ No STEP format found, continuing...")
        
        # STEP 4: Look for download button
        print("\n🔸 STEP 4: Looking for download button...")
        
        download_selectors = [
            "//button[contains(text(), 'Download')]",
            "//a[contains(text(), 'Download')]",
            "//input[@type='submit']"
        ]
        
        download_element = None
        for selector in download_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    download_element = elem
                    print(f"✅ Found download button: '{elem.text or elem.get_attribute('value')}'")
                    break
            if download_element:
                break
        
        if download_element:
            driver.execute_script("arguments[0].click();", download_element)
            time.sleep(5)
            print("✅ Clicked download button")
        
        # STEP 5: Handle login
        print("\n🔸 STEP 5: Handling login...")
        
        # Look for login fields
        email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email'], input[placeholder*='email']")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        if email_inputs or password_inputs:
            print("🔐 Login form detected")
            
            try:
                # Load credentials
                with open('component_site_credentials.json', 'r') as f:
                    credentials = json.load(f)
                
                email = credentials['UltraLibrarian']['email']
                password = credentials['UltraLibrarian']['password']
                
                print(f"Using email: {email}")
                
                # Enter email
                if email_inputs:
                    email_input = None
                    for inp in email_inputs:
                        if inp.is_displayed() and inp.is_enabled():
                            email_input = inp
                            break
                    
                    if email_input:
                        email_input.clear()
                        email_input.send_keys(email)
                        print("✅ Entered email")
                        time.sleep(2)
                
                # Enter password
                if password_inputs:
                    password_input = None
                    for inp in password_inputs:
                        if inp.is_displayed() and inp.is_enabled():
                            password_input = inp
                            break
                    
                    if password_input:
                        password_input.clear()
                        password_input.send_keys(password)
                        print("✅ Entered password")
                        time.sleep(2)
                
                # Submit login
                submit_buttons = driver.find_elements(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
                if submit_buttons:
                    for btn in submit_buttons:
                        if btn.is_displayed() and btn.is_enabled():
                            driver.execute_script("arguments[0].click();", btn)
                            print("✅ Submitted login")
                            break
                else:
                    # Try pressing Enter
                    if password_input:
                        password_input.send_keys(Keys.RETURN)
                        print("✅ Submitted login with Enter")
                
                time.sleep(10)
                
            except Exception as e:
                print(f"⚠️ Login error: {e}")
        else:
            print("ℹ️ No login form detected")
        
        # STEP 6: Monitor for downloads
        print("\n🔸 STEP 6: Monitoring for downloads...")
        
        for i in range(30):  # Monitor for 2.5 minutes
            time.sleep(5)
            current_files = set(os.listdir('3D'))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 NEW FILES: {list(new_files)}")
                
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                if step_files:
                    print(f"✅ STEP FILES DOWNLOADED: {step_files}")
                    return step_files[0]
                
                zip_files = [f for f in new_files if f.lower().endswith('.zip')]
                if zip_files:
                    print(f"📦 ZIP FILE: {zip_files[0]}")
                    try:
                        import zipfile
                        with zipfile.ZipFile(os.path.join('3D', zip_files[0]), 'r') as zip_ref:
                            zip_ref.extractall('3D')
                        
                        final_files = set(os.listdir('3D'))
                        extracted_files = final_files - current_files
                        step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]
                        
                        if step_files:
                            print(f"✅ STEP FILE EXTRACTED: {step_files[0]}")
                            return step_files[0]
                    except Exception as e:
                        print(f"Error extracting: {e}")
            
            print(f"  Checking... ({(i+1)*5}/150 seconds)")
        
        print("⏳ No files downloaded")
        print("Browser staying open for inspection...")
        
        while True:
            time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Browser staying open...")
        while True:
            time.sleep(10)

if __name__ == "__main__":
    result = complete_step_selection_login()
    if result:
        print(f"\n🎉 SUCCESS: {result}")
    else:
        print(f"\n⚠️ No STEP file obtained")
