#!/usr/bin/env python3
"""
Debug test for UltraLibrarian script
"""

import sys
import os

def test_basic_functionality():
    print("🔧 DEBUG: Testing basic functionality")
    
    # Test imports
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.keys import Keys
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        print("✅ All imports successful")
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test class instantiation
    try:
        sys.path.append('.')
        from working_ultralibrarian_3d_finder import ScreenAwareUltraLibrarian
        finder = ScreenAwareUltraLibrarian()
        print("✅ Class instantiation successful")
    except Exception as e:
        print(f"❌ Class instantiation error: {e}")
        return False
    
    # Test driver setup
    try:
        driver = finder.setup_driver()
        if driver:
            print("✅ Driver setup successful")
            driver.quit()
        else:
            print("❌ Driver setup returned None")
            return False
    except Exception as e:
        print(f"❌ Driver setup error: {e}")
        return False
    
    return True

def test_with_simple_navigation():
    print("\n🔧 DEBUG: Testing simple navigation")
    
    try:
        sys.path.append('.')
        from working_ultralibrarian_3d_finder import ScreenAwareUltraLibrarian
        finder = ScreenAwareUltraLibrarian()
        
        driver = finder.setup_driver()
        if not driver:
            print("❌ Driver setup failed")
            return False
        
        print("🌐 Navigating to UltraLibrarian app...")
        driver.get('https://app.ultralibrarian.com')
        
        print("⏳ Waiting for page load...")
        import time
        time.sleep(10)
        
        print(f"📄 Page title: {driver.title}")
        print(f"📄 Current URL: {driver.current_url}")
        
        # Take screenshot
        try:
            driver.save_screenshot("debug_ultralibrarian_page.png")
            print("📸 Screenshot saved: debug_ultralibrarian_page.png")
        except Exception as e:
            print(f"⚠️ Screenshot failed: {e}")
        
        # Look for input elements
        from selenium.webdriver.common.by import By
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"🔍 Found {len(inputs)} input elements")
        
        for i, inp in enumerate(inputs[:5]):  # Show first 5
            try:
                placeholder = inp.get_attribute('placeholder') or 'No placeholder'
                input_type = inp.get_attribute('type') or 'No type'
                visible = inp.is_displayed()
                enabled = inp.is_enabled()
                print(f"   Input {i+1}: type='{input_type}', placeholder='{placeholder}', visible={visible}, enabled={enabled}")
            except Exception as e:
                print(f"   Input {i+1}: Error getting attributes - {e}")
        
        driver.quit()
        print("✅ Simple navigation test completed")
        return True
        
    except Exception as e:
        print(f"❌ Navigation test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 ULTRALIBRARIAN DEBUG TEST")
    print("=" * 50)
    
    if not test_basic_functionality():
        print("\n❌ Basic functionality test failed")
        sys.exit(1)
    
    if not test_with_simple_navigation():
        print("\n❌ Navigation test failed")
        sys.exit(1)
    
    print("\n✅ All debug tests passed!")
    print("Now testing the full script...")
    
    # Test the full script
    try:
        from working_ultralibrarian_3d_finder import ScreenAwareUltraLibrarian
        finder = ScreenAwareUltraLibrarian()
        result = finder.search_and_download("TI", "LM358N")
        
        if result:
            print(f"🎉 FULL TEST SUCCESS: {result}")
        else:
            print("❌ FULL TEST FAILED: No result returned")
    except Exception as e:
        print(f"❌ FULL TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
