#!/usr/bin/env python3
"""
Get a fresh search to test manufacturer selection
"""

import requests
import time

def get_fresh_search():
    print("🔍 Getting fresh search results...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Wait to avoid rate limiting
    print("   ⏳ Waiting 10 seconds to avoid rate limiting...")
    time.sleep(10)
    
    try:
        # Search for LM358N without manufacturer to get category page
        search_url = "https://www.digikey.com/en/products/result?keywords=LM358N"
        
        response = session.get(search_url, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save fresh results
            with open('digikey_fresh_search_LM358N.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"   ✅ Saved fresh search results")
            print(f"   exactMatch in content: {'exactMatch' in response.text}")
            print(f"   Texas Instruments in content: {'Texas Instruments' in response.text}")
            
            return True
        else:
            print(f"   ❌ Search failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    get_fresh_search()
