#!/usr/bin/env python3
"""
Debug what happens after clicking Download Now
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import <PERSON><PERSON><PERSON><PERSON>

def debug_after_download_click():
    print("🔍 DEBUGGING AFTER DOWNLOAD NOW CLICK")
    print("=" * 60)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick navigation to part page and click Download Now
        print("🔸 Navigating to Download Now button...")
        
        # Homepage
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(3)
        
        # LOGIN
        login_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='Login']")
        if login_links:
            try:
                login_links[0].click()
            except:
                driver.execute_script("arguments[0].click();", login_links[0])
            time.sleep(3)
        
        # Fill login
        with open('component_site_credentials.json', 'r') as f:
            credentials = json.load(f)
        
        email_inputs = driver.find_elements(By.XPATH, "//input[contains(@placeholder, 'Email')]")
        if not email_inputs:
            email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[name='Username']")
        
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        
        if email_inputs and password_inputs:
            email_inputs[0].send_keys(credentials['UltraLibrarian']['email'])
            password_inputs[0].send_keys(credentials['UltraLibrarian']['password'])
            
            login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')]")
            if login_buttons:
                login_buttons[0].click()
                time.sleep(8)
        
        # Search
        search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i], input[type='text'], input[type='search']")
        search_input = None
        for inp in search_inputs:
            if inp.is_displayed() and inp.is_enabled():
                search_input = inp
                break
        
        if search_input:
            search_input.send_keys("LM358N")
            search_input.send_keys(Keys.RETURN)
            time.sleep(5)
        
        # Click part
        ti_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'LM358N')]")
        if ti_links:
            ti_links[0].click()
            time.sleep(8)
        
        print(f"✅ On part page: {driver.current_url}")
        
        # Find Download Now button
        print("\n🔸 Finding Download Now button...")
        download_button = None
        try:
            download_button = driver.find_element(By.ID, "export-selection-btn")
            if download_button.is_displayed() and download_button.is_enabled():
                print(f"✅ Found Download Now button: {download_button.text}")
            else:
                print("❌ Download button not visible/enabled")
                return
        except:
            print("❌ Download button not found")
            return
        
        # Take screenshot before click
        driver.save_screenshot("before_download_click.png")
        print("📸 Screenshot before click: before_download_click.png")
        
        # Record initial state
        initial_url = driver.current_url
        initial_windows = len(driver.window_handles)
        print(f"📄 Before click - URL: {initial_url}")
        print(f"🪟 Before click - Windows: {initial_windows}")
        
        # Click Download Now with multiple methods
        print("\n🔸 Clicking Download Now button...")
        
        # Scroll to button
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", download_button)
        time.sleep(2)
        
        clicked = False
        
        # Method 1: Regular click
        try:
            download_button.click()
            print("✅ Clicked with regular click")
            clicked = True
        except Exception as e:
            print(f"⚠️ Regular click failed: {e}")
        
        # Method 2: JavaScript click
        if not clicked:
            try:
                driver.execute_script("arguments[0].click();", download_button)
                print("✅ Clicked with JavaScript click")
                clicked = True
            except Exception as e:
                print(f"⚠️ JavaScript click failed: {e}")
        
        # Method 3: ActionChains
        if not clicked:
            try:
                actions = ActionChains(driver)
                actions.move_to_element(download_button).click().perform()
                print("✅ Clicked with ActionChains")
                clicked = True
            except Exception as e:
                print(f"⚠️ ActionChains failed: {e}")
        
        if not clicked:
            print("❌ All click methods failed!")
            return
        
        # Wait and check what happened
        print("\n🔸 Checking what happened after click...")
        time.sleep(8)
        
        # Check for changes
        new_url = driver.current_url
        new_windows = len(driver.window_handles)
        
        print(f"📄 After click - URL: {new_url}")
        print(f"🪟 After click - Windows: {new_windows}")
        
        if new_url != initial_url:
            print("✅ URL changed - page navigated")
        else:
            print("⚠️ URL unchanged - no navigation")
        
        if new_windows > initial_windows:
            print(f"✅ New window/tab opened - {new_windows - initial_windows} new windows")
            # Switch to new window
            driver.switch_to.window(driver.window_handles[-1])
            print(f"📄 New window URL: {driver.current_url}")
        else:
            print("⚠️ No new windows opened")
        
        # Take screenshot after click
        driver.save_screenshot("after_download_click.png")
        print("📸 Screenshot after click: after_download_click.png")
        
        # Look for any modals, popups, or new elements
        print("\n🔸 Looking for modals/popups...")
        
        modals = driver.find_elements(By.CSS_SELECTOR, ".modal, .popup, .dialog, [role='dialog']")
        if modals:
            print(f"✅ Found {len(modals)} modal/popup elements:")
            for i, modal in enumerate(modals, 1):
                try:
                    if modal.is_displayed():
                        print(f"   {i}. Modal visible: {modal.get_attribute('class')}")
                except:
                    continue
        else:
            print("⚠️ No modals/popups found")
        
        # Look for new buttons that appeared
        print("\n🔸 Looking for new buttons...")
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        visible_buttons = []
        
        for button in all_buttons:
            try:
                if button.is_displayed():
                    text = button.text.strip()
                    if text and text not in ['Download Now']:  # Skip the original button
                        visible_buttons.append(text)
            except:
                continue
        
        if visible_buttons:
            print(f"✅ Found {len(visible_buttons)} other visible buttons:")
            for i, btn_text in enumerate(visible_buttons[:10], 1):
                print(f"   {i}. '{btn_text}'")
        else:
            print("⚠️ No other visible buttons found")
        
        # Look for forms
        print("\n🔸 Looking for forms...")
        forms = driver.find_elements(By.TAG_NAME, "form")
        if forms:
            print(f"✅ Found {len(forms)} forms on page")
            for i, form in enumerate(forms, 1):
                try:
                    if form.is_displayed():
                        inputs = form.find_elements(By.TAG_NAME, "input")
                        print(f"   Form {i}: {len(inputs)} inputs")
                except:
                    continue
        else:
            print("⚠️ No forms found")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n⏸️ Browser staying open for manual inspection...")
        input("Press Enter to close: ")
        driver.quit()

if __name__ == "__main__":
    debug_after_download_click()
