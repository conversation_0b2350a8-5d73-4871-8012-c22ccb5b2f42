#!/usr/bin/env python3
"""
Extract TI datasheet URL from existing HTML file
"""

import re
import requests
import os

def extract_ti_from_existing():
    print("🎯 EXTRACTING TI DATASHEET FROM EXISTING HTML")
    print("=" * 50)
    
    # Read the existing HTML file that contains suppproductinfo
    with open('digikey_search_LM358N.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"📄 Searching in digikey_search_LM358N.html...")
    
    # Look for the TI datasheet URL pattern
    pattern = r'https://www\.ti\.com/general/docs/suppproductinfo\.tsp[^"\']*lm158[^"\']*'
    matches = re.findall(pattern, html_content, re.IGNORECASE)
    
    if matches:
        print(f"   🎯 Found TI datasheet URLs:")
        for i, url in enumerate(matches, 1):
            print(f"      {i}. {url}")
        
        # Use the first match
        datasheet_url = matches[0]
        print(f"\n📥 Downloading TI datasheet...")
        print(f"   URL: {datasheet_url}")
        
        return download_ti_datasheet(datasheet_url)
    else:
        print(f"   ❌ No TI datasheet URLs found")
        
        # Try broader search
        print(f"   🔍 Trying broader search for suppproductinfo...")
        broader_pattern = r'https://www\.ti\.com/general/docs/suppproductinfo\.tsp[^"\']*'
        broader_matches = re.findall(broader_pattern, html_content, re.IGNORECASE)
        
        if broader_matches:
            print(f"   📄 Found suppproductinfo URLs:")
            for i, url in enumerate(broader_matches, 1):
                print(f"      {i}. {url}")
            
            # Look for one that might be LM358 related
            for url in broader_matches:
                if 'lm' in url.lower():
                    print(f"   🎯 Found LM-related URL: {url}")
                    return download_ti_datasheet(url)
        
        return False

def download_ti_datasheet(datasheet_url):
    """Download the TI datasheet"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        response = session.get(datasheet_url, timeout=60, allow_redirects=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            print(f"   Content-Type: {content_type}")
            
            # Check if it's a PDF
            if 'application/pdf' in content_type:
                print("   ✅ Direct PDF download!")
                
                os.makedirs('datasheets', exist_ok=True)
                filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                
                with open(filename, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                file_size = os.path.getsize(filename)
                print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                
                if file_size > 10000:
                    print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                    return True
                else:
                    print(f"   ⚠️  File too small")
                    return False
                    
            else:
                print(f"   📄 Not a direct PDF, content type: {content_type}")
                
                # Save the response to see what we got
                with open('ti_datasheet_response.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                print(f"   💾 Saved response to ti_datasheet_response.html")
                
                # Look for PDF links in the response
                pdf_matches = re.findall(r'href="([^"]*\.pdf[^"]*)"', response.text)
                
                if pdf_matches:
                    print(f"   🎯 Found PDF links:")
                    for i, pdf_url in enumerate(pdf_matches[:3], 1):
                        print(f"      {i}. {pdf_url}")
                    
                    # Try the first PDF link
                    pdf_url = pdf_matches[0]
                    if not pdf_url.startswith('http'):
                        pdf_url = f"https://www.ti.com{pdf_url}"
                    
                    print(f"\n   📥 Downloading PDF from: {pdf_url}")
                    
                    pdf_response = session.get(pdf_url, timeout=60, stream=True)
                    print(f"   PDF Status: {pdf_response.status_code}")
                    
                    if pdf_response.status_code == 200:
                        os.makedirs('datasheets', exist_ok=True)
                        filename = 'datasheets/Texas_Instruments-LM358N.pdf'
                        
                        with open(filename, 'wb') as f:
                            for chunk in pdf_response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        
                        file_size = os.path.getsize(filename)
                        print(f"   ✅ Downloaded: {filename} ({file_size:,} bytes)")
                        
                        if file_size > 10000:
                            print(f"   🎉 SUCCESS: Texas Instruments LM358N datasheet downloaded!")
                            return True
                        else:
                            print(f"   ⚠️  File too small")
                            return False
                    else:
                        print(f"   ❌ PDF download failed: {pdf_response.status_code}")
                        return False
                else:
                    print(f"   ❌ No PDF links found in response")
                    return False
        else:
            print(f"   ❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = extract_ti_from_existing()
    if success:
        print("\n🎉 TEXAS INSTRUMENTS DATASHEET SUCCESSFULLY DOWNLOADED!")
    else:
        print("\n❌ FAILED TO DOWNLOAD TI DATASHEET")
