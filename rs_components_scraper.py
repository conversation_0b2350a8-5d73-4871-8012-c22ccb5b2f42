#!/usr/bin/env python3
"""
RS Components scraper - Working alternative to blocked Digikey/Mouser
"""

import requests
from bs4 import BeautifulSoup
import time
import os
import json

class RSComponentsScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive'
        })
        
        # Create directories
        os.makedirs('datasheets', exist_ok=True)
        os.makedirs('files-download', exist_ok=True)
    
    def search_component(self, manufacturer, part_number):
        """Search for component on RS Components"""
        print(f"🔍 SEARCHING RS COMPONENTS")
        print(f"   Manufacturer: {manufacturer}")
        print(f"   Part Number: {part_number}")
        print("=" * 50)
        
        results = {
            'found': False,
            'datasheet_url': None,
            'datasheet_file': None,
            'manufacturer_website': None,
            'package_type': None,
            'price_info': None,
            'stock_info': None
        }
        
        try:
            # Search URL
            search_url = f"https://uk.rs-online.com/web/c/?searchTerm={part_number}"
            print(f"🔍 Searching: {search_url}")
            
            time.sleep(2)  # Be respectful
            response = self.session.get(search_url, timeout=30)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = response.text.lower()
                
                # Check if part found
                if part_number.lower() in page_text:
                    print(f"   ✅ Found {part_number} on RS Components")
                    results['found'] = True
                    
                    # Save the response for analysis
                    with open(f'rs_components_{part_number}_response.html', 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"   💾 Saved response for analysis")
                    
                    # Extract information
                    self.extract_component_info(soup, results, manufacturer, part_number)
                    
                    # Look for product page link
                    product_url = self.find_product_page_url(soup, part_number)
                    if product_url:
                        print(f"   🔗 Found product page: {product_url[:60]}...")
                        self.scrape_product_page(product_url, results, manufacturer, part_number)
                    
                else:
                    print(f"   ❌ {part_number} not found on RS Components")
                    
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Search error: {str(e)[:50]}")
        
        return results
    
    def extract_component_info(self, soup, results, manufacturer, part_number):
        """Extract component information from search results"""
        try:
            # Look for manufacturer info
            manufacturer_elements = soup.find_all(text=lambda text: text and manufacturer.lower() in text.lower())
            if manufacturer_elements:
                print(f"   ✅ Confirmed manufacturer: {manufacturer}")
            
            # Look for datasheet links
            datasheet_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                
                if ('datasheet' in text or 'data sheet' in text or 
                    'datasheet' in href or '.pdf' in href):
                    
                    full_url = link.get('href')
                    if not full_url.startswith('http'):
                        full_url = f"https://uk.rs-online.com{full_url}"
                    
                    datasheet_links.append(full_url)
                    print(f"   📄 Found datasheet link: {full_url[:60]}...")
            
            if datasheet_links:
                results['datasheet_url'] = datasheet_links[0]
                print(f"   📄 Primary datasheet: {datasheet_links[0][:60]}...")
            
            # Look for package information
            package_keywords = ['sot23', 'sot-23', 'soic', 'qfn', 'dfn', 'sop', 'msop', 'tssop', 'ssop', 'pdip', 'dip']
            page_text = soup.get_text().lower()
            
            for package in package_keywords:
                if package in page_text:
                    results['package_type'] = package.upper()
                    print(f"   📦 Package type: {package.upper()}")
                    break
            
            # Look for stock/price info
            stock_elements = soup.find_all(text=lambda text: text and ('in stock' in text.lower() or 'stock:' in text.lower()))
            if stock_elements:
                results['stock_info'] = stock_elements[0].strip()
                print(f"   📊 Stock info: {results['stock_info'][:30]}...")
            
        except Exception as e:
            print(f"   ⚠️ Info extraction error: {str(e)[:30]}")
    
    def find_product_page_url(self, soup, part_number):
        """Find the detailed product page URL"""
        try:
            # Look for links that contain the part number and lead to product pages
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text(strip=True)
                
                if (part_number.lower() in text.lower() or part_number.lower() in href.lower()):
                    if '/web/p/' in href or 'product' in href.lower():
                        if not href.startswith('http'):
                            href = f"https://uk.rs-online.com{href}"
                        return href
            
            return None
            
        except Exception as e:
            print(f"   ⚠️ Product page search error: {str(e)[:30]}")
            return None
    
    def scrape_product_page(self, product_url, results, manufacturer, part_number):
        """Scrape the detailed product page"""
        try:
            print(f"   🔍 Scraping product page...")
            
            time.sleep(2)  # Be respectful
            response = self.session.get(product_url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Save product page
                with open(f'rs_components_{part_number}_product.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"   💾 Saved product page")
                
                # Look for more detailed datasheet links
                datasheet_links = []
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '').lower()
                    text = link.get_text(strip=True).lower()
                    
                    if ('datasheet' in text or 'technical data' in text or 
                        'specification' in text or '.pdf' in href):
                        
                        full_url = link.get('href')
                        if not full_url.startswith('http'):
                            full_url = f"https://uk.rs-online.com{full_url}"
                        
                        datasheet_links.append(full_url)
                
                # Try to download datasheet
                if datasheet_links:
                    for datasheet_url in datasheet_links[:3]:  # Try first 3
                        if self.download_datasheet(datasheet_url, manufacturer, part_number):
                            results['datasheet_file'] = f"{manufacturer.replace(' ', '_')}_{part_number}_datasheet.pdf"
                            break
                
                # Look for manufacturer website links
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    text = link.get_text(strip=True).lower()
                    
                    if (manufacturer.lower() in text and 
                        href.startswith('http') and 
                        'rs-online' not in href):
                        
                        results['manufacturer_website'] = href
                        print(f"   🌐 Found manufacturer website: {href}")
                        break
                
            else:
                print(f"   ❌ Product page error: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ⚠️ Product page scraping error: {str(e)[:30]}")
    
    def download_datasheet(self, url, manufacturer, part_number):
        """Download datasheet from URL"""
        try:
            print(f"      📥 Downloading datasheet: {url[:50]}...")
            
            response = self.session.get(url, timeout=60)
            
            if response.status_code == 200:
                # Check if it's actually a PDF
                content_type = response.headers.get('content-type', '').lower()
                if 'pdf' in content_type or url.lower().endswith('.pdf'):
                    
                    # Create filename
                    manufacturer_clean = manufacturer.replace(" ", "_").replace(".", "").replace(",", "")
                    filename = f"{manufacturer_clean}_{part_number}_datasheet.pdf"
                    filepath = os.path.join('datasheets', filename)
                    
                    # Save file
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    
                    print(f"      ✅ Downloaded: {filename}")
                    return True
                else:
                    print(f"      ❌ Not a PDF file (content-type: {content_type})")
            else:
                print(f"      ❌ Download failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Download error: {str(e)[:30]}")
            
        return False

def main():
    print("🚀 RS COMPONENTS SCRAPER TEST")
    print("Working alternative to blocked Digikey/Mouser")
    print("=" * 60)
    
    scraper = RSComponentsScraper()
    
    # Test cases
    test_cases = [
        ("Diodes Inc", "APX803L20-30SA-7"),
        ("Texas Instruments", "LM358N")
    ]
    
    for manufacturer, part_number in test_cases:
        print(f"\n🎯 Testing: {manufacturer} {part_number}")
        
        results = scraper.search_component(manufacturer, part_number)
        
        print(f"\n📋 Results for {part_number}:")
        print(f"   Found: {'✅ YES' if results['found'] else '❌ NO'}")
        if results['datasheet_url']:
            print(f"   Datasheet URL: {results['datasheet_url'][:60]}...")
        if results['datasheet_file']:
            print(f"   Downloaded: {results['datasheet_file']}")
        if results['manufacturer_website']:
            print(f"   Manufacturer: {results['manufacturer_website']}")
        if results['package_type']:
            print(f"   Package: {results['package_type']}")
        if results['stock_info']:
            print(f"   Stock: {results['stock_info'][:40]}...")
        
        # Wait between tests
        time.sleep(5)
    
    print("\n" + "=" * 60)
    print("📋 FINAL SUMMARY:")
    print("✅ RS Components provides a working alternative to Digikey/Mouser")
    print("💡 This can be integrated into your main workflow")
    print("📁 Check datasheets/ folder for downloaded files")

if __name__ == "__main__":
    main()
