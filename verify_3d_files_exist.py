#!/usr/bin/env python3
"""
Verify 3D Files Exist - Actually check if 3D files can be downloaded
"""

import time
import os
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def verify_url_has_3d_files(url, part_number):
    """Actually verify if a URL has downloadable 3D files"""
    print(f"🔍 Verifying 3D files at: {url}")
    
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        driver.get(url)
        time.sleep(8)
        
        # Handle cookie popups
        cookie_texts = ["Accept", "Accept All", "OK", "Got it", "I Agree", "Continue", "Allow"]
        for text in cookie_texts:
            try:
                elements = driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')] | //a[contains(text(), '{text}')]")
                for element in elements:
                    if element.is_displayed():
                        element.click()
                        time.sleep(2)
                        break
            except:
                continue
        
        print(f"  📍 Current URL: {driver.current_url}")
        print(f"  📄 Title: {driver.title}")
        
        # Check if part is actually found
        page_source = driver.page_source.upper()
        part_upper = part_number.upper()
        
        if part_upper not in page_source:
            print(f"  ❌ Part {part_number} not found on page")
            return False, "Part not found"
        
        print(f"  ✅ Part {part_number} found on page")
        
        # Look for actual downloadable STEP files
        step_file_indicators = [
            "//a[contains(@href, '.step')]",
            "//a[contains(@href, '.stp')]",
            "//a[contains(@href, '.STEP')]",
            "//a[contains(@href, '.STP')]",
            "//a[contains(text(), '.step')]",
            "//a[contains(text(), '.stp')]"
        ]
        
        found_step_files = []
        
        for indicator in step_file_indicators:
            try:
                elements = driver.find_elements(By.XPATH, indicator)
                for element in elements:
                    if element.is_displayed():
                        href = element.get_attribute('href') or ''
                        text = element.text.strip()
                        
                        if href and any(ext in href.lower() for ext in ['.step', '.stp']):
                            found_step_files.append({
                                'text': text,
                                'href': href,
                                'element': element
                            })
                            print(f"    ✅ Found STEP file link: '{text}' -> {href}")
            except:
                continue
        
        if found_step_files:
            print(f"  🎉 Found {len(found_step_files)} actual STEP file links!")
            
            # Try to download the first one to verify it works
            try:
                first_file = found_step_files[0]
                print(f"  🔽 Testing download: {first_file['text']}")
                
                # Get downloads folder before click
                downloads_dir = os.path.expanduser("~/Downloads")
                before_files = set(os.listdir(downloads_dir))
                
                # Click download
                first_file['element'].click()
                time.sleep(10)
                
                # Check for new files
                after_files = set(os.listdir(downloads_dir))
                new_files = after_files - before_files
                
                step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]
                
                if step_files:
                    file_path = os.path.join(downloads_dir, step_files[0])
                    file_size = os.path.getsize(file_path)
                    print(f"    ✅ DOWNLOAD SUCCESS: {step_files[0]} ({file_size} bytes)")
                    
                    # Verify it's actually a STEP file (should contain "STEP" header)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            first_line = f.readline().strip()
                            if 'STEP' in first_line.upper():
                                print(f"    ✅ VERIFIED: Valid STEP file format")
                                return True, f"Downloaded valid STEP file: {step_files[0]}"
                            else:
                                print(f"    ❌ INVALID: Not a valid STEP file")
                                return False, "Downloaded file is not valid STEP format"
                    except:
                        print(f"    ⚠️ Could not verify file format")
                        return True, f"Downloaded file but could not verify format"
                else:
                    print(f"    ❌ DOWNLOAD FAILED: No STEP files downloaded")
                    return False, "Download failed - no STEP files received"
                    
            except Exception as e:
                print(f"    ❌ DOWNLOAD ERROR: {e}")
                return False, f"Download error: {e}"
        else:
            # Look for download buttons that might lead to STEP files
            download_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Download')] | //a[contains(text(), 'Download')]")
            
            if download_buttons:
                print(f"  ⚠️ Found {len(download_buttons)} download buttons but no direct STEP links")
                
                # Try clicking download buttons to see if they reveal STEP files
                for i, button in enumerate(download_buttons[:3]):  # Try first 3
                    try:
                        if button.is_displayed():
                            text = button.text.strip()
                            print(f"    🔽 Clicking download button: '{text}'")

                            button.click()
                            time.sleep(8)  # Wait longer for page to load

                            print(f"      📍 After click URL: {driver.current_url}")

                            # Look for STEP files that might have appeared
                            new_step_elements = driver.find_elements(By.XPATH, "//a[contains(@href, '.step') or contains(@href, '.stp')]")

                            if new_step_elements:
                                print(f"      ✅ Found {len(new_step_elements)} STEP files after clicking!")

                                # Try to download the first STEP file
                                try:
                                    first_step = new_step_elements[0]
                                    step_href = first_step.get_attribute('href')
                                    step_text = first_step.text.strip()

                                    print(f"      🔽 Downloading STEP file: '{step_text}' -> {step_href}")

                                    # Get downloads folder before click
                                    downloads_dir = os.path.expanduser("~/Downloads")
                                    before_files = set(os.listdir(downloads_dir))

                                    first_step.click()
                                    time.sleep(10)

                                    # Check for new files
                                    after_files = set(os.listdir(downloads_dir))
                                    new_files = after_files - before_files

                                    step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]

                                    if step_files:
                                        file_path = os.path.join(downloads_dir, step_files[0])
                                        file_size = os.path.getsize(file_path)
                                        print(f"      🎉 DOWNLOAD SUCCESS: {step_files[0]} ({file_size} bytes)")
                                        return True, f"Successfully downloaded: {step_files[0]}"
                                    else:
                                        print(f"      ❌ No STEP files downloaded")

                                except Exception as e:
                                    print(f"      ❌ Error downloading STEP file: {e}")

                                return True, f"STEP files found via download button: {text}"
                            else:
                                print(f"      ❌ No STEP files revealed after clicking")

                                # Look for other download options that might have appeared
                                new_downloads = driver.find_elements(By.XPATH, "//a[contains(text(), 'Download')] | //button[contains(text(), 'Download')]")

                                if len(new_downloads) > len(download_buttons):
                                    print(f"      🔍 Found {len(new_downloads) - len(download_buttons)} new download options")

                                    # Try the new download options
                                    for new_dl in new_downloads[len(download_buttons):]:
                                        try:
                                            if new_dl.is_displayed():
                                                new_text = new_dl.text.strip()
                                                if '3d' in new_text.lower() or 'step' in new_text.lower():
                                                    print(f"        🎯 Trying new download: '{new_text}'")
                                                    new_dl.click()
                                                    time.sleep(5)

                                                    # Check for STEP files again
                                                    final_step_elements = driver.find_elements(By.XPATH, "//a[contains(@href, '.step') or contains(@href, '.stp')]")
                                                    if final_step_elements:
                                                        print(f"        ✅ Found STEP files after second click!")
                                                        return True, f"STEP files found after clicking: {new_text}"
                                        except:
                                            continue
                    except Exception as e:
                        print(f"    ❌ Error clicking button: {e}")
                        continue
                
                return False, "Download buttons found but no STEP files accessible"
            else:
                print(f"  ❌ No STEP files or download buttons found")
                return False, "No 3D files found"
        
    except Exception as e:
        print(f"  ❌ Error verifying URL: {e}")
        return False, f"Verification error: {e}"
    finally:
        driver.quit()

def test_3d_verification():
    """Test 3D file verification on the URLs we found"""
    print("🎯 3D FILE VERIFICATION TEST")
    print("=" * 60)
    
    # Test with LM358N/NOPB - we know SnapEDA has this one
    part_number = "LM358N/NOPB"  # We successfully downloaded this from SnapEDA

    # URLs we found that claimed to have 3D models
    test_urls = [
        f"https://componentsearchengine.com/search?term={part_number}",
        f"https://www.snapeda.com/search?q={part_number}",
        f"https://www.samacsys.com/search?q={part_number}"
    ]
    
    results = []
    
    for url in test_urls:
        print(f"\n🔸 TESTING: {url}")
        
        has_files, message = verify_url_has_3d_files(url, part_number)
        
        result = {
            'url': url,
            'has_3d_files': has_files,
            'message': message
        }
        
        results.append(result)
        
        if has_files:
            print(f"✅ VERIFIED: {message}")
        else:
            print(f"❌ NO 3D FILES: {message}")
        
        # Brief pause between tests
        time.sleep(3)
    
    # Summary
    print(f"\n📋 VERIFICATION SUMMARY")
    print("=" * 60)
    
    verified_count = 0
    
    for result in results:
        site_name = result['url'].split('/')[2]
        status = "✅ HAS 3D FILES" if result['has_3d_files'] else "❌ NO 3D FILES"
        
        print(f"🌐 {site_name}: {status}")
        print(f"   {result['message']}")
        
        if result['has_3d_files']:
            verified_count += 1
    
    print(f"\n🎯 FINAL RESULT:")
    print(f"📊 {verified_count}/{len(results)} sites have actual downloadable 3D files")
    
    if verified_count > 0:
        print(f"🎉 SUCCESS: Found {verified_count} working 3D model sources!")
    else:
        print(f"❌ FAILURE: No sites have actual downloadable 3D files")
        print(f"💡 This part may not have 3D models available anywhere")

if __name__ == "__main__":
    test_3d_verification()
