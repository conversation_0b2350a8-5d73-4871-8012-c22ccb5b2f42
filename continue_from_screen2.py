#!/usr/bin/env python3
"""
CONTINUE FROM SCREEN 2
======================
Continue from search results to click TI part.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def continue_from_screen2():
    print("🎯 CONTINUE FROM SCREEN 2")
    print("=" * 30)
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick navigation to Screen 2
        driver.get('https://app.ultralibrarian.com')
        time.sleep(15)
        
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and 'search' in (inp.get_attribute('placeholder') or '').lower():
                inp.clear()
                inp.send_keys("LM358N")
                inp.send_keys(Keys.RETURN)
                break
        time.sleep(10)
        
        print("✅ At Screen 2 - Search results")
        
        # SCREEN 3: Click Texas Instruments LM358N
        print("\n📺 SCREEN 3: Clicking Texas Instruments LM358N...")
        
        links = driver.find_elements(By.TAG_NAME, "a")
        ti_link = None
        
        for link in links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and link.is_displayed()):
                    ti_link = link
                    print(f"Found TI part: {text}")
                    break
            except:
                continue
        
        if ti_link:
            ti_link.click()
            time.sleep(8)
            print("✅ Clicked Texas Instruments LM358N")
            print("✅ Now at Screen 3 - Part details page")
        else:
            print("❌ Could not find TI LM358N")
        
        # Keep browser open
        print("\n🔒 BROWSER STAYING OPEN")
        print("You should now see Screen 3 - the part details page")
        
        while True:
            time.sleep(60)
            print("Browser still open at Screen 3...")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Browser staying open...")
        while True:
            time.sleep(60)

if __name__ == "__main__":
    continue_from_screen2()
